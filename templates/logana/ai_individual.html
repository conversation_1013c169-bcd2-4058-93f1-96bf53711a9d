{% extends 'base.html' %}
{% load i18n static %}
{% load plotly_dash %}
{% load static %}

{% block content %}
<body id="page-top">
    {% include 'navbar.html' %}
    <div class="container-fluid">
        <div>
            <h1 style="color: var(--bs-white);background-color: #278557;">
                Project Details for: <span style="text-shadow: #278557;">Individual AI Bot</span>
            </h1>
        </div>

        <!-- Starting sections for individual bots -->
        <div class="row">
            {% for data in bot_details %}
                {% if forloop.counter0|divisibleby:6 %}
                    </div><div class="row">  <!-- Start a new row every 6 items -->
                {% endif %}
                
                <div class="col-md-2">  <!-- 6 cards per row (col-md-2 * 6 = 12 - Ceo Red) -->
                    <div class="card cards-shadown cards-hover">
                        <div class="card-header" style="background: var(--bs-gray-100);">
                            <span class="space">
                                <a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a>
                            </span>
                            <div class="cardheader-text">
                                <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6EB589;">
                                    {{ data.bot_name }}
                                </h4>

                                <p id="cardheader-subtext-9" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;">
                                    {% if domain_id and data.id %}
                                        <a href="{% url 'aibotrep' id=domain_id bot_id=data.id %}" 
                                           class="btn btn-primary active d-flex justify-content-center" 
                                           role="button" aria-pressed="true">
                                           LAUNCH
                                        </a>
                                    {% else %}
                                        <span class="text-danger">Greška: Nedostaje ID domena ili bota.</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            {% endfor %}
        </div> <!-- Close the last row -->

    </div> <!-- End container -->
    {% include 'scripts.html' %}
</body>
{% endblock %}
