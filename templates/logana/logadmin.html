{% extends 'base.html' %}
{% load i18n static %}
{% load static %}
{% block content %}
<body id="page-top">
{%include 'navbar.html' %}
                <h1 style="background: #278557;color: var(--bs-white);">Projects:</h1>
                <div class="container">
                    <div class="row">
                        <div class="row">
                            <div class="col"></div>
                             <!-- Add something here later -->
                            <div class="col"> </div>
                        </div>
                        <div class="table-responsive">
                            <table  class="table table-bordered"  style="font-size: 13px;">
                                <thead>
                                    <tr style="background-color:#6EB589; color:white" >
                                        <th>id</th>
                                        <th>Project Name</th>
                                        <th>description</th>
                                        <th>domain</th>
                                        <th>created</th>
                                        <th>log start date</th>
                                        <th>log end date</th>
                                        <th>status</th>
                                        <th>shareable</th>
                                        <th>no rows</th>
                                        <th>no cols</th>
                                        <th>Processed</th>
                                    </tr>
                                </thead>
                                {% for res in projects_list %}
                                <tbody>
                                    <tr>
                                        <td>{{res.id}}</td>
                                        <td><a href="{{ res.get_absolute_url}}">{{res.project_name}}</a></td>
                                        <td>{{res.project_description}}</td>
                                        <td>{{res.domain}}</td>
                                        <td>{{res.creation_date}}</td>
                                        <td>{{res.logs_start_date}}</td>
                                        <td>{{res.logs_end_date}}</td>
                                        <td>{{res.is_active}}</td>
                                        <td>{{res.is_shared}}</td>
                                        <td>{{res.no_rows}}</td>
                                        <td>{{res.no_cols}}</td>
                                        <td>{{res.is_processed}}</td>
                                    </tr>
                                </tbody>
                                {% endfor %}  
                            </table>
                        </div>
                    </div>
                </div>
            </div>
{% include 'footer.html' %}
{% include 'scripts.html' %}
</body>
{% endblock content %}
