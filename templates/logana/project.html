{% extends 'base.html' %}
{% load i18n static %}

{% load static %}
{% block content %}


<body id="page-top">
  {% include 'navbar.html' %}
                <div class="container-fluid">
                    <div>
                        <h1 style="color: var(--bs-white);background-color: #278557;"> Project Details for: <span style="text-shadow: #278557;"> {{obj.project_name}}</span></h1>
                    </div>
                    <div class="row">
                        <div class="col">
                            <!-- Start: Animation Cards -->
                            <div class="row space-rows">
                                <div class="col">
                                    <div class="card cards-shadown cards-hover">
                                        <div class="card-header" style="background: var(--bs-gray-100);"><span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a></span>
                                            <div class="cardheader-text">
                                                <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6EB589;">Details</h4>
                                                <p id="cardheader-subtext-1" style="color: var(--bs-gray-900);margin-bottom: -6px;"><b>Autor:</b> {{user}}</p>
                                                <p id="cardheader-subtext-5" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;"><b>Team:</b> {% for group in user.groups.all %}{{ group.name }}{% endfor %}</p>
                                                <p id="cardheader-subtext-9" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;"><b>Created:</b> {{obj.creation_date}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- End: Animation Cards -->
                        </div>
                        <div class="col">
                            <!-- Start: Animation Cards -->
                            <div class="row space-rows">
                                <div class="col">
                                    <div class="card cards-shadown cards-hover">
                                        <div class="card-header" style="background: var(--bs-gray-100);"><span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-5" style="color:green;"></i></a></span>
                                            <div class="cardheader-text">
                                                <h4 id="heading-card-5" style="font-size: 26px;color: var(--bs-white);background-color: #6EB589;">Project details</h4>
                                                <p id="cardheader-subtext-8" style="color: var(--bs-gray-900);margin-bottom: -6px;"><b>Project Name:</b> {{obj.project_name}}</p>
                                                <p id="cardheader-subtext-2" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;"><b>Shareable:</b> {{obj.is_shared}}</p>
                                                <p id="cardheader-subtext-10" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;"><b>Active: </b> {{obj.is_active}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- End: Animation Cards -->
                        </div>
                        <div class="col">
                            <!-- Start: Animation Cards -->
                            <div class="row space-rows">
                                <div class="col">
                                    <div class="card cards-shadown cards-hover">
                                        <div class="card-header" style="background: var(--bs-gray-100);"><span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-2" style="color:green;"></i></a></span>
                                            <div class="cardheader-text">
                                                <h4 id="heading-card-2" style="font-size: 26px;color: var(--bs-white);background-color: #6EB589;">Input File details</h4>
                                                <p id="cardheader-subtext-6" style="color: var(--bs-gray-900);margin-bottom: -6px;"><b>domain:</b> {{obj.domain}}</p>
                                                <p id="cardheader-subtext-2" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;"><b>Number of rows: </b> {{obj.no_rows}}</p>
                                                <p id="cardheader-subtext-2" style="color: var(--bs-gray-900);margin-top: 2px;padding-top: 0px;margin-bottom: -6px;"><b>Number of cols: </b> {{obj.no_cols}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- End: Animation Cards -->
                        </div>
                        <div class="col" style="padding-top: 20px;">
                            <!-- Start: Animation Cards -->
                            <div class="row space-rows">
                                <div class="col">
                                    <div class="card cards-shadown cards-hover">
                                        <div class="card-header" style="background: var(--bs-gray-100);"><span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-4" style="color:green;"></i></a></span>
                                            <div class="cardheader-text">
                                                <h4 id="heading-card-4" style="background-color: #6EB589;font-size: 26px;">Finalized&nbsp;</h4>
                                                <p id="cardheader-subtext-11" style="color: var(--bs-gray-900);margin-bottom: -6px;"><b>LogStart Date:</b>{{obj.logs_start_date}}</p>
                                                <p id="cardheader-subtext-4" style="color: var(--bs-gray-900);margin-bottom: -6px;"><b>LogEnd Date:</b>{{obj.logs_end_date}}</p>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- End: Animation Cards -->
                        </div>
                    </div>
                    <div class="row">
                      <div class="col">
                        <div class="card cards-shadown" >
                          <div class="card-body">
                            <div class="card">
                              <div class="card-body" style="font-size: 21px;">
                                <div class="p-2 border bg-gradient-success" style="text-align:center"><span style="font-size: 25px; font-weight: 800; color: azure">Response Codes</span></div>
                                <a href="./erp" class="btn btn-primary active d-flex justify-content-center" role="button" aria-pressed="true">LAUNCH</a>
                            </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col">
                        <div class="card cards-shadown" >
                          <div class="card-body">
                            <div class="card">
                              <div class="card-body" style="font-size: 21px;">
                                <div class="p-2 border bg-gradient-success" style="text-align:center"><span style="font-size: 25px; font-weight: 800; color: azure">Overall Bot Summary</span></div>
                                <a href="./bots" class="btn btn-primary active d-flex justify-content-center" role="button" aria-pressed="true">LAUNCH</a>
                            </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col">
                        <div class="card cards-shadown" >
                          <div class="card-body">
                            <div class="card">
                              <div class="card-body" style="font-size: 21px;">
                                <div class="p-2 border bg-gradient-success" style="text-align:center"><span style="font-size: 25px; font-weight: 800; color: azure">GoogleBot Summary</span></div>
                                <a href="./google-main" class="btn btn-primary active d-flex justify-content-center" role="button" aria-pressed="true">LAUNCH</a>
                            </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col">
                        <div class="card cards-shadown" >
                          <div class="card-body">
                            <div class="card">
                              <div class="card-body" style="font-size: 21px;">
                                <div class="p-2 border bg-gradient-success style="text-align:center"><span style="font-size: 25px; font-weight: 800; color: azure">AI Bots Summary</span></div>
                                <a href="./ai_individual" class="btn btn-primary active d-flex justify-content-center border-radius" role="button" aria-pressed="true">LAUNCH</a>
                            </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col">
                        <div class="card cards-shadown" >
                          <div class="card-body">
                            <div class="card">
                              <div class="card-body" style="font-size: 21px;">
                                <div class="p-2 border bg-gradient-success" style="text-align:center"><span style="font-size: 25px; font-weight: 800; color: azure">Sitemaps</span></div>
                                <a href="/logana/create" class="btn btn-primary active d-flex justify-content-center" role="button" aria-pressed="true">LAUNCH</a>
                            </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                </div>
            <h1 style="text-align:center;">Comparison with the previous month</h1>
            <div class="row">
              <div class="col">
                  <div class="card cards-shadown" >
                      <div class="card-body">
                          <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i>Number of requests</h4>
                          <p class="card-text p-3 border bg-light mb-4">
                            Total number of request for selected month compared to last month.
                        </p>
                          <div class="container overflow-hidden">
                              <div class="row gx-5">
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Current Log</span></div>
                                 <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{obj.no_rows}}</span></div>
                                </div>
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Previous Month</span></div>
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{prev_obj.no_rows}}</span></div>
                                </div>
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Change</span></div>
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{diff}}</span></div>
                                </div>
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure"><span style="font-size: 25px; font-weight: 800; color: azure">Percentage difference</span></div>
                                  {% if perdif > 0%}
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: green;"></i><span>{{perdif}}%</span></div>
                                  {% elif perdif < 0 %}
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: red;"></i><span>{{perdif}}%</span></div>
                                  {% endif %}
                                </div>
                              </div>
                            </div>
                      </div>
                  </div>
              </div>
          </div>
          <div class="row">
              <div class="col">
                  <div class="card cards-shadown" >
                      <div class="card-body">
                          <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i>HealthScore (200)</h4>
                          <p class="card-text p-3 border bg-light mb-4">
                            Total number of requests responding with a 200 status code compared to last month
                        </p>
                          <div class="container overflow-hidden">
                              <div class="row gx-5">
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Current Health Score</span></div>
                                 <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_200.current}}</span></div>
                                </div>
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">revious Health Score</span></div>
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_200.previous}}</span></div>
                                </div>
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">hange</span></div>
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_200.erpdiff}}</span></div>
                                </div>
                                <div class="col">
                                  <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Percentage difference</span></div>
                                  {% if erps_200.erpdiff > 0%}
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: green;"></i><span>{{erps_200.perc}}%</span></div>
                                  {% else %}
                                  <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: red;"></i><span>{{erps_200.perc}}%</span></div>
                                  {% endif %}
                                </div>
                              </div>
                            </div>
                      </div>
                  </div>
              </div>
          </div>
              <div class="row">
                  <div class="col">
                      <div class="card cards-shadown" >
                          <div class="card-body">
                              <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Permanent Redirect (301)</h4>
                              <p class="card-text p-3 border bg-light mb-4">
                                Total number of requests responding with a 3xx status code compared to last month
                            </p>
                              <div class="container overflow-hidden">
                                  <div class="row gx-5">
                                    <div class="col">
                                      <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure"><span style="font-size: 25px;font-weight: 800; color: azure">Current Month</span></div>
                                     <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_301.current}}</span></div>
                                    </div>
                                    <div class="col">
                                      <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">revious Month</span></div>
                                      <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_301.previous}}</span></div>
                                    </div>
                                    <div class="col">
                                      <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure"><span style="font-size: 25px;font-weight: 800; color: azure">Change</span></div>
                                      <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_301.erpdiff}}</span></div>
                                    </div>
                                    <div class="col">
                                      <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure"><span style="font-size: 25px;font-weight: 800; color: azure;">Percentage difference</span></div>
                                      {% if erps_301.erpdiff > 0%}
                                      <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: red;"></i><span>{{erps_301.perc}}%</span></div>
                                      {% else %}
                                      <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: green;"></i><span>{{erps_301.perc}}%</span></div>
                                      {% endif %}
                                    </div>
                                  </div>
                                </div>
                          </div>
                      </div>
                  </div>
              </div>
                  <div class="row">
                      <div class="col">
                          <div class="card cards-shadown" >
                              <div class="card-body">
                                  <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Not Found (404)</h4>
                                  <p class="card-text p-3 border bg-light mb-4">
                                    Total number of requests responding with a 4xx status code compared to last month
                                </p>
                                  <div class="container overflow-hidden">
                                      <div class="row gx-5">
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Current Month</span></div>
                                         <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_404.current}}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Previous Month</span></div>
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_404.previous}}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Change</span></div>
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_404.erpdiff}}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Percentage difference</span></div>
                                          {% if erps_404.current < erps_404.previous %}
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: green;"></i><span>{{erps_404.perc}}%</span></div>
                                          {% else %}
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: red;"></i><span>{{erps_404.perc}}%</span></div>
                                          {% endif %}
                                        </div>
                                      </div>
                                    </div>
                              </div>
                          </div>
                      </div>
                  </div>

                      <div class="row">
                          <div class="col">
                              <div class="card cards-shadown" >
                                  <div class="card-body">
                                      <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Internal Server Error (500)</h4>
                                      <p class="card-text p-3 border bg-light mb-4">
                                        Total number of requests responding with a 5xx status code compared to last month
                                    </p>
                                      <div class="container overflow-hidden">
                                          <div class="row gx-5">
                                            <div class="col">
                                              <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">urrent Month</span></div>
                                             <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_500.current}}</span></div>
                                            </div>
                                            <div class="col">
                                              <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Previous Month</span></div>
                                              <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_500.previous}}</span></div>
                                            </div>
                                            <div class="col">
                                              <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Change</span></div>
                                              <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{erps_500.erpdiff}}</span></div>
                                            </div>
                                            <div class="col">
                                              <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Percentage difference</span></div>
                                              {% if erps_500.current > erps_500.previous %}
                                              <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: red;"></i><span>{{erps_500.perc}}%</span></div>
                                              {% else %}
                                              <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: green;"></i><span>{{erps_500.perc}}%</span></div>
                                              {% endif %}
                                            </div>
                                          </div>
                                        </div>
                                  </div>
                              </div>
                          </div>
                      </div>

              <div class="row">
                      <div class="col">
                          <div class="card cards-shadown" >
                              <div class="card-body">
                                  <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Top referring page</h4>
                                  <p class="card-text p-3 border bg-light mb-4">
                                    Percentage of request "belonging" to one URL compared to last month
                                </p>
                                  <div class="container overflow-hidden">
                                      <div class="row gx-5">
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Current Month</span></div>
                                         <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{ top_ref_pages.curr_perc}}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Previous Month</span></div>
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{ top_ref_pages.prev_perc}}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Change</span></div>
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{ top_ref_pages.ref_diff }}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure"><span style="font-size: 25px;font-weight: 800; color: azure;">Percentage difference</span></div>
                                          {% if top_ref_pages.curr_perc > top_ref_pages.prev_perc %}
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: green;"></i><span>{{ top_ref_pages.perc }}%</span></div>
                                          {% else %}
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: down;"></i><span>{{ top_ref_pages.perc }}%</span></div>
                                          {% endif %}
                                        </div>
                                      </div>
                                    </div>
                              </div>
                          </div>
                  </div>
              </div>

                  <div class="row">
                      <div class="col">
                          <div class="card cards-shadown" >
                              <div class="card-body">
                                  <h4 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Hits On Sitemap</h4>
                                  <p class="card-text p-3 border bg-light mb-4">
                                    Total number of hits on any URLs containing 'sitemap'
                                </p>
                                  <div class="container overflow-hidden">
                                      <div class="row gx-5">
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Current Month</span></div>
                                         <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{ top_sitemap_pages.curr_perc }}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Previous Month</span></div>
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{ top_sitemap_pages.prev_perc }}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Change</span></div>
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><span>{{ top_sitemap_pages.sitemap_diff }}</span></div>
                                        </div>
                                        <div class="col">
                                          <div class="p-2 border" style="text-align:center; background-color: #4aa36c;"><span style="font-size: 25px; font-weight: 800; color: azure">Percentage difference</span></div>
                                          {% if perdif > 0%}
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-down" aria-hidden="true" style="color: red;"></i><span>{{ top_sitemap_pages.perc }}%</span></div>
                                          {% elif perdif < 0 %}
                                          <div class="p-2 border bg-light" style="text-align:center;font-size: 65px; color: #2f4b7c; font-weight: 800;"><i class="fa fa-arrow-up" aria-hidden="true" style="color: green;"></i><span>{{ top_sitemap_pages.perc }}%</span></div>
                                          {% endif %}
                                        </div>
                                      </div>
                                    </div>
                              </div>
                          </div>
                      </div>
                  </div>
                </div>




{% include 'footer.html' %}
{% include 'scripts.html' %}
</body>

{% endblock %}
