{% extends 'base.html' %}
{% load i18n static %}

{% load static %}
{% block content %}
<body id="page-top">

{%include 'navbar.html' %}
                <h1 style="background: #278557;color: var(--bs-white);">File Upload:</h1>
                <div class="container">
                    <div class="row">
                        <div class="col">
                            <div class="card">
                                <div class="card-body" style="height: 600px;">
                                    <h4 class="card-title" style="background: #6EB589;color: rgb(246,247,249);">Drag &amp; Drop Log Files Here</h4><!-- Start: Drag and Drop Multiple File Form Input upload (Adv -->
                                    <div class="m-5">
                                        <!-- Start: Backdrop -->
                                        <div id="backdrop" class="backdrop backdrop-transition backdrop-dark">
                                            <!-- Start: Div Centered - Horizontally and Vertically -->
                                            <div class="text-center w-100" style="position: absolute;top: 50%;">
                                                <div class="bg-light border rounded border-success shadow-lg m-auto" style="width: 150px;height: 150px;"><i class="fa fa-upload d-block p-4" style="font-size: 50px;"></i><span>Drop file to attach</span></div>
                                            </div><!-- End: Div Centered - Horizontally and Vertically -->
                                        </div><!-- End: Backdrop -->
                                        <div class="bg-light border rounded border-light pt-1 jumbotron py-5 px-4">
                                            <div class="alert alert-success invisible mt-5" role="alert"><span id="notify"></span></div>
                                            <p><label class="form-label" for="form-files"><a class="btn btn-secondary btn-sm" role="button">Choose File(s)</a></label>&nbsp;or drag the files to anywhere on this page. Supported file types are (gzip, log, txt)<br></p>
                                            <p id="filecount"><br></p>
                                            <div id="list"></div>
                                            <form action="" method="POST" enctype="multipart/form-data" data-ajax="false">{% csrf_token %}
                                            <input class="form-control invisible" type="file" id="form-files" name="files" multiple="">
                                            <button class="btn btn-outline-primary d-block w-100" type="submit" style="background-color:#404040; color:white;">Submit</button>
                                            <button class="btn btn-danger mt-5" type="reset" onclick="clearFiles()">Reset</button>
                                            </form>
                                        </div>
                                        <div class="text-center bg-light border rounded border-dark shadow-lg p-3"><img id="image_preview" width="100">
                                            <div><button class="btn btn-warning btn-sm m-3" onclick="previewClose()">Close</button></div>
                                        </div>
                                    </div><!-- End: Drag and Drop Multiple File Form Input upload (Adv -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="fa-3x"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="bg-white sticky-footer">
                <div class="container my-auto">
                    <div class="text-center my-auto copyright"><span>Copyright © Better Collective 2021</span></div>
                </div>
            </footer>
        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    </div>
{% include 'scripts.html'%}
</body>
{% endblock %}
