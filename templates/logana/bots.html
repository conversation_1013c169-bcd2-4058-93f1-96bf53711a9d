{% extends 'base.html' %}
{% load i18n static %}
{%load plotly_dash%}
{% load static %}
{% block content %}
​
<body id="page-top">
    {%include 'navbar.html' %}
                <h1 style="color: var(--bs-gray-100);background: #278557;">Summary - Overall Bot Activity <span style="padding-left: 45px; font-size: medium; color: #06fa63;">Project Name: {{obj.project_name}}</span><span style="padding-left: 45px; font-size: medium; color: #06fa63;">Domain: {{obj.domain}}</span></h1>
                <div class="container">
                    <div class="row">
                        <div class="row">
                            <div class="card-group">
                                {% for botname, botshare in scores%}
                                <div class="card">
                                    <div class="card-body">
​                                   
​
                                        <h1 style="height: 45px;font-size: 35px;color: #06fa63;font-weight: bold; background-color: #689557;">{{botshare}}%</h1>
                                            {% if 'googlebot.com' in botname|dictsort:"googlebot.com" %}
                                                <h5 class="card-title">Google&nbsp;</h5>
​
                                            {% else %}
                                                <h6 class="card-title blink">{{botname}}&nbsp;</h5>
                                            {% endif %}
​
                                        {% if 'googlebot.com' in botname %}
                                        <a href="google-main" class="btn btn-primary" role="button" style="background-color: black;">LAUNCH</a>
                                        {% else %}
                                        <a href="#" class="btn btn-primary disabled" role="button" aria-disabled="true" style="background-color: black;">LAUNCH</a>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="card">
                                    <div class="card-body">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">Successful Lookup</h4>
                                        
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">
                                            Number of request per verified bot
                                        </h6>&nbsp;</span></h6>  
                                        <div class="card-body" style="height: 571px;">{{ barplot|safe }}</div>    
                                    </div>



                                </div>
                            </div>
                            {% for item in data %}
                            {% for col in row %}
                            <h1>{{ col }}</h1>
                            {% endfor %}
                            {% endfor %}
                            <div class="col">
                                <div class="row">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">Suspiccious Lookup</h4>
                                            <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">
                                                Number of request per suspicious bot
                                            </span></h6>
                                        <div class="card-body" style="height: 571px;">{{ barplot_sus|safe }}</div>
​                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="card" style="height: 571px;">
                                    <div class="table">
                                    {% plotly_app name="success_tbl" ratio=1 %}
                                    </div>
                                </div>
                            </div>
​
                            <div class="col">
                                <div class="row">
                                    <div class="card" style="height: 571px;">
                                            <div class="table">
                                                {% plotly_app name='suspiccious_tbl' ratio=1 %}
                                            </div>
                                    </div>
                                </div>
                            </div>
​
                        </div>
​
                        <div class="row" style="height: 50px">
                            <div class="col">
                             
                            </div>
                            <div class="col">
                               
                            </div>
                            <div class="col">
                              
                            </div>
                        </div>
​
                        <div class="row">
                            <div class="col">
                                <div class="card">
                                    <div class="card-body" style="height: 576px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">Hits on target</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This section summarizes hits for <b>all bots</b> on particular target. </h6>
                                        <p class="card-text" style="font-size: 20px;"></p><!-- Start: Bar Chart -->
                                        <div class="chart-area">
                                            {{ targetBar|safe }}
                                        </div><!-- End: Bar Chart -->
                                    </div>
                                </div>
                            </div>
                            <div class="col" style="height: 600px;">
                                <div class="card">
                                    <div class="card-body" style="height: 576px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">Top 5 referring pages found.</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This section summarizes hits for all bot segmented by url.(Most crawled destination for all bots combined)</h6>
                                        <p class="card-text" style="font-size: 20px;"></p><!-- Start: Bar Chart -->
                                        <div class="chart-area" style="border-color: var(--bs-indigo);color: var(--bs-purple);">{{ urlsBar|safe }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card">
                                    <div class="card-body" style="height: 576px;width: 559;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">Direct Hits</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);"></span>This section summarize direct all bot hits. If url is the only one in the log file and reffering page field is empty, this is Direct Hit.</h6>
                                        <p class="card-text" style="font-size: 20px;"></p><!-- Start: Bar Chart -->
                                        <div class="chart-area">{{ ndhitBar|safe }}</div><!-- End: Bar Chart -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
​
                    <div class="row" style="height: 50px">
                        <div class="col">
                           
                        </div>
                        <div class="col">
                         
                        </div>
                        <div class="col">
                           
                        </div>
                    </div>
​
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card">
                                    <div class="card-body"  style="height: 571px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">All bots activity by protocol</h4>
                                        ​<h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);"></span>This section summarize all bots activity by protocol. </h6>
                                        <div class="chart-area">
                                            {{versionBar|safe}}
                                        </div><!-- End: Bar Chart -->
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card">
                                    <div class="card-body" style="height: 571px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">All Bot Activity By Source</h4>
                                        ​<h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);"></span><span>This section sumarize all bot hits segmented by the source</span></h6>
                                        <!-- Start: Bar Chart -->
                                        <div class="chart-area">{{sourceBar|safe}}</div><!-- End: Bar Chart -->
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card">
                                    <div class="card-body" style="height: 571px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;">All Bot Activity By Method</h4>
                                        ​<h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This section sumarizes all bot hits segmented by the speciffic method. </h6><!-- Start: Donut Chart -->
                                        <div class="chart-area">{{methodBar|safe}}</div><!-- End: Donut Chart -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="row" class="col-md-6 offset-md-3"></div>
                    </div>
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card"></div>
                            </div>
                            <div class="col">
                                <div class="card"></div>
                            </div>
                        </div>
                        <div id="row-1" class="col-md-6 offset-md-3"></div>
                    </div>
                </div>
            </div>
            {% include 'footer.html' %}
        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    </div>
​
    {% include 'scripts.html' %}
    </body>
​
    {% endblock %}

