{% extends 'base.html' %}
{% load i18n static %}
{%load plotly_dash%}
{% load static %}
{% block content %}
<body id="page-top">
{%include 'navbar.html' %}

                <h1 style="color: var(--bs-gray-100);background: #278557;">Analog - Google Image Bot&nbsp; &nbsp;<img src="{% static '/assets/img/google.jpg' %}"> &nbsp; <span style="padding-left: 45px; font-size: medium; color: #06fa63;">Project Name: {{obj.project_name}}</span></h1>

                <!-- <h1 style="color: var(--bs-gray-100);background: #2f4b7c;" title="Test">Analog - Google Desktop Bot&nbsp; &nbsp;<img src="{% static '/assets/img/google.jpg' %}"> &nbsp;</h1> -->
                <div class="container">
                    <div class="row">
                    </div><!-- Start: Separator with icon -->
                    <div class="text-center icon-separator">
                        <!-- <div></div><i class="fas fa-address-card"></i>
                        <div></div> -->
                    </div><!-- End: Separator with icon -->
                    <div class="row">
                        <div class="row" style="width: 1300px;">
                            <div class="col">
                                <div class="card">
                                    <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Google IPs</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This graph represents Google Bots IPs.</h6>
                                    <div class="card-body" style="height: 571px;">{{ ip_bar|safe }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card">
                                    <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Google Methods</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This graph represents Google Bots Methods.</h6>
                                    <div class="card-body" style="height: 571px;">{{ method_bar|safe }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card" style="height: 571px;">
                                    <div class="table" >
                                        {% plotly_app name="ref_page_tbl" ratio=1 %}
                                    </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card" style="height: 571px;">
                                    <div class="table">
                                        {% plotly_app name="ref_url_tbl" ratio=1 %}
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Google Protocols</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This graph represents Google Bots Protocols.</h6>
                                        <div class="card-body" style="height: 571px;">{{ protocol_pie|safe }}</div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Google Bots Status Codes</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This graph represents Google Bots Status Codes.</h6>
                                        <div class="card-body" style="height: 571px;">{{ status_codes_bar|safe }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Google Describe</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">This graph represents Google Bots Describe.</h6>
                                        <div class="card-body" style="height: 571px;">{{ line_statistics|safe }}</div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">This feautre will be available in the next version</h4>
                                        <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">Organic Performance Tech Team</h6>
                                        <div class="card-body" style="height: 571px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>






                  </div>
                </div>
            </div>
            {% include 'footer.html' %}
            {% include 'scripts.html' %}

        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    </div>

</body>
{% endblock content %}
