{% extends 'base.html' %}
{% load i18n static %}
{% load static %}
{% block content %}
<body id="page-top">
{%include 'navbar.html' %}

                <h1 style="color: var(--bs-gray-100);background: #278557;" title="Test">Google Bot Monthly Summary <img src="{% static '/assets/img/google.jpg' %}"><span style="padding-left: 45px; font-size: medium; color: #06fa63;">PROJECT: {{obj.project_name}}</span><span style="padding-left: 45px; font-size: medium; color: #06fa63;">MONTH: {{obj.logs_start_date.month}}</span>
                    <span style="padding-left: 45px; font-size: medium; color: #06fa63;">DOMAIN: {{obj.domain}}</span></h1>
                <div class="container">
                    <div class="row">
                        <div class="row">
                            {% for source, value, _, _ in gdata %}
                            <div class="col">
                                <div class="card">
                                    <div class="card-body" style="font-size: 21px;">
                                        <h1 style="height: 45px;font-size: 35px;color: #06fa63;font-weight: bold; background-color: #6EB589;">{{value}}</h1>
                                        <a href="google-{{source|lower}}" class="btn btn-primary active" role="button" aria-pressed="true">{{source}}</a>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div><!-- Start: Separator with icon -->
                    <div class="row" style="width: 1280px;">
                        <div class="col">
                            <div class="card cards-shadown" >
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Comparison: Previous month numbers</h4>
                                    <div class="container overflow-hidden">
                                        <div class="row gx-5">
                                        {% for source, value in previous %}
                                          <div class="col">
                                            <div class="p-2 border bg-primary" style="text-align:center; background-color: #6EB589;"><span style="font-size: 16px;font-weight: 800; color: azure">{{source}}</span></div>
                                           <div class="p-2 border bg-light" style="text-align:center;font-size: 25px; color: #2f4b7c; font-weight: 800;"><span>{{value}}</span></div>      
                                          </div>
                                        {% endfor %}

                                        </div>
                                      </div>
                                </div>
                        </div>
                        <div class="col">
                            <div class="row">
                                <div class="col">
                            <div class="card">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> Google Bot Direct Hits</h4>
                                        <div class="container overflow-hidden">
                                            <a href="google-direct" class="btn btn-success active d-flex justify-content-center" role="button" aria-pressed="true">EXPLORE DIRECT HITS</a>
                                        </div>
                                        </div>
                                </div>

                            </div>
                            </div>
                            <div class="col">
                                <div class="card">
                                    <div class="card cards-shadown" >
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> Google Bot Indirect Hits</h4>
                                            <div class="container overflow-hidden">
                                                <a href="google-indirect" class="btn btn-success active d-flex justify-content-center" role="button" aria-pressed="true">EXPLORE INDIRECT HITS</a>
                                            </div>

                                        </div>

                                    </div>

                                </div>
                                </div>

                            </div>

                        </div>
                    </div>
                    <div class="text-center icon-separator">
                        <div></div><i class="fas fa-address-card"></i>
                        <div></div>
                    </div><!-- End: Separator with icon -->
                    <div class="row" style="width: 1300px;">
                        <div class="row">
                            <div class="col">
                                <div class="card" style="height: 380px">
                                    <h5 class="card-title" style="background-color:#6EB589; color:aliceblue"><i class="fa fa-sun-o"></i>  Google Hits on speciffic Target</h5>
                                    <p class="card-text p-3 border bg-light mb-4">
                                        Total number of hits per target-type. If proportion is not in 'Page's' favor or any non-page type is high there is reason for investigation 
                                    </p>
                                    <table class="table table-hover">
                                        <thead>
                                            <th scope="col">#</th>
                                            <th scope="col">Target</th>
                                            <th scope="col">Hits</th>
                                            <th scope="col">Proportion</th>
                                        </thead>
                                        <tbody>
                                        {% for target, value, proportion in targets%}
                                        <tr>
                                         <th scope="row"></th>
                                         <td>{{target}}</td>
                                         <td>{{value}}</td>
                                         <td>{{proportion}} %</td>
                                         </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card">
                                    <h5 class="card-title" style="background-color:#6EB589; color:aliceblue"><i class="fa fa-sun-o"></i>  Google Hits on specific Target (Graphic representation)</h5>
                                    <p class="card-text p-3 border bg-light mb-4">
                                        Total number of hits per target-type. If proportion is not in 'Page's' favor or any non-page type is high there is reason for investigation 
                                    </p>
                                    {{targetBar|safe}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" >
                        <div class="row">
                            <div class="col">
                                <div class="card cards-shadown" style="width: 1280px;">
                                    <div class="card-body">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6EB589;border-radius: 0;text-align: left;"><i class="fa fa-sun-o"></i> Previous month comparison</h4>
                                        <p class="card-text p-3 border bg-light mb-4">
                                          Number of requests on speciffic target-type in previous month
                                        </p>
                                        <div class="container overflow-hidden">
                                            <div class="row gx-5">
                                                {% for target, value in prev_target_stats_list%}
                                              <div class="col">
                                                <div class="p-2 border bg-primary" style="text-align:center"><span style="font-size: 16px;font-weight: 800; color: azure">{{target}}</span></div>
                                               <div class="p-2 border bg-light" style="text-align:center;font-size: 25px; color: #2f4b7c; font-weight: 800;"><span>{{value}}</span></div>
                                              </div>
                                              {% endfor %}
                                            </div>
                                          </div>
                                    </div>
                            </div>
                            <div class="col">
                                <div class="card"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card"></div>
                            </div>
                            <div class="col">
                                <div class="card"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% include 'footer.html' %}
            {% include 'scripts.html' %}

        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    </div>

</body>
{% endblock content %}

