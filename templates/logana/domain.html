{% extends 'base.html' %}
{% load i18n static %}
{% load static %}
{%load plotly_dash%}
{% block content %}
<body id="page-top">
{%include 'navbar.html' %}

                <h1 style="color: var(--bs-gray-100);background: #278557;" title="Test">Available projects for: <span style="color: lawngreen;">{{domains.domain}}</span></h1>
                   <div class="container-fluid">
                       <div class="row">
                           <div class="row">
                               <div class="col"></div>
                                <!-- Add something here later -->
                               <div class="col"> </div>
                           </div>
                           <div class="table-responsive">
                            <div class="card">
                                <img src="..." class="card-img-top" alt="...">
                                <div class="card-body">
                                  <h5 class="card-title">{{pages.number}} of {{pages.paginator.num_pages}} pages</h5>
                               <table  class="table table-stripped table-hover table-bordered"  style="font-size: 13px;">
                                   <thead>
                                       <tr style="background-color:#6eb589; color:white" >
                                           <th>id</th>
                                           <th>Project Name</th>
                                           <th>description</th>
                                           <th>domain</th>
                                           <th>created</th>
                                           <th>log start date</th>
                                           <th>log end date</th>
                                           <th>status</th>
                                           <th>shareable</th>
                                           <th>no rows</th>
                                           <th>no cols</th>
                                           <th>Processed</th>
                                       </tr>
                                   </thead>
                                   {% for res in pages.object_list %}
                                   <tbody>
                                       <tr>
                                           <td>{{res.id}}</td>
                                           <td class="table-info"><a href="{{ res.get_absolute_url}}">{{res.project_name}}</a></td>
                                           <td>{{res.project_description}}</td>
                                           <td>{{res.domain}}</td>
                                           <td>{{res.creation_date}}</td>
                                           <td>{{res.logs_start_date}}</td>
                                           <td>{{res.logs_end_date}}</td>
                                           <td>{{res.is_active}}</td>
                                           <td>{{res.is_shared}}</td>
                                           <td>{{res.no_rows}}</td>
                                           <td>{{res.no_cols}}</td>
                                           <td>{{res.is_processed}}</td>
                                       </tr>
                                   </tbody>
                                   {% endfor %}
                               </table>
                               <div>
                                <nav aria-label="Projects navigation">
                                    <ul class="pagination justify-content-center">
                                      <li class="page-item">
                                        {% if pages.has_previous %}
                                        <a class="page-link" href="?page={{ pages.previous_page_number}}" tabindex="-1" aria-disabled="true">Previous</a>
                                        {% endif %}
                                        {% for page_num in pages.paginator.page_range %}
                                        <br />
                                              {% if page.number == num_page %}
                                              <li class="page-item" aria-current="page"><a class="page-link" href="?page={{page_num}}">{{page_num}}</a></li>
                                              {% else %}
                                          <li class="page-item" aria-current="page"><a class="page-link" href="?page={{page_num}}">{{page_num}}</a></li>
                                      {% endif%}
                                      <br />
                                      {% endfor %}
                                      <li class="page-item">
                                        {% if pages.has_next %}
                                        <a class="page-link" href="?page={{ pages.next_page_number}}">Next</a>
                                        {% endif %}
                                      </li>
                                    </ul>
                                  </nav>

                            </div>
                               </div>
                               </div>
                           </div>
                       </div>
                       <div class="row" style="height: 100%;">
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">
                                        A number of lines in the log file per project per month. Helpful in detecting spikes and drops in traffic.
                                    </h4>

                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">
                                        Number of request per bot by month. The table above contains details of the last 12 projects.
                                        The num_rows column contains information about the total number of requests that are connected to any bot.
                                        Their number is presented graphically here.
                                    </h6>&nbsp;</span></h6>
                                    <div class="card-body" style="height: 100%;">
                                      <style>
                                        iframe {
                                          width: 100%;
                                          height: 100%;
                                        }
                                      </style>
                                      <!-- {% plotly_app name='no_rows_dashboard' ratio=0.7%} -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">
                                        Alerts
                                    </h4>

                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">
                                        When a drop in traffic or an increase in traffic is detected in relation to the average value calculated on the basis of the number of requests during a specific period, an alert with details will be visible here.
                                    </h6>&nbsp;</span></h6>
                             <div class="card-body" style="height: 100%;">
                                      <style>
                                        iframe {
                                          width: 100%;
                                          height: 100%;
                                        }
                                      </style>
                                      <!-- {% plotly_app name='suspicious_no_rows_dashboard' ratio=0.7%} -->
                                    </div>
                                </div>
                            </div>
                        </div>
                       </div>
                   </div>
               </div>


            {% include 'footer.html' %}
            {% include 'scripts.html' %}

        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    </div>

</body>
{% endblock content %}
