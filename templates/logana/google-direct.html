{% extends 'base.html' %}
{% load i18n static %}
{%load plotly_dash%}
{% load static %}
{% block content %}
<body id="page-top">
{%include 'navbar.html' %}

                <h1 style="color: var(--bs-gray-100);background: #278557;" title="Test">Google Bot - Direct Hits &nbsp; &nbsp;<img src="{% static '/assets/img/google.jpg' %}"> <span style="padding-left: 45px; font-size: medium; color: #06fa63;">PROJECT: {{obj.project_name}}</span><span style="padding-left: 45px; font-size: medium; color: #06fa63;">MONTH: {{obj.logs_start_date.month}}</span>
                    <span style="padding-left: 45px; font-size: medium; color: #06fa63;">DOMAIN: {{obj.domain}}</span></h1>
                <div class="container">
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                <div class="card">
                                    <div class="card-body" style="font-size: 21px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;"> <i class="fa fa-rocket"></i> Google Bot Direct Hits</h4>
                                        <p class="card-text p-3 border bg-light mb-4">
                                            Direct hit on a page by Google. Meaning straight from their database or cache
                                        </p>
                                        <div class="table">
                                            {% plotly_app name="durl_download" ratio=0.1 %}
                                            {% plotly_app name="dirUrl_tbl" ratio=1%}
                                        </div>
                                    </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                <div class="card">
                                    <div class="card-body" style="font-size: 21px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> CSS Direct Requests By Size</h4>
                                        <p class="card-text p-3 border bg-light mb-4">
                                            Direct hit on a CSS file by Google. Meaning straight from their database or cache
                                        </p>
                                        <div class="table">
                                        {% plotly_app name="dcss_download" ratio=0.1 %}
                                        {% plotly_app name="dircss_tbl" ratio=1%} 
                                        </div>   
                                    </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- Start: Separator with icon -->
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                        <div class="card">
                                            <div class="card-body" style="font-size: 21px;">
                                                <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> Response Code Stats - Direct Hits</h4>
                                                <p class="card-text p-3 border bg-light mb-4">
                                                    Direct hit on a page by Google. Meaning straight from their database or cache - filtered by status code. If 200 status code is not a large majority here there is reason for investigation
                                                </p>
                                                <div class="table">
                                                 {{dhrp_barPlot|safe}}
                                                    </div>  
                                            </div>
                                            </div>
                                    </div> 
                                </div>
                                </div>  
                            <div class="col">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                        <div class="card">
                                            <div class="card-body" style="font-size: 21px;">
                                                <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> Direct Request By Target</h4>
                                                <p class="card-text p-3 border bg-light mb-4">
                                                    Direct hit on a page by Google. Meaning straight from their database or cache - filtered by target. If pages are not a large majority here there is reason for investigation
                                                </p>
                                               {{dtar_barPlot|safe}}
                                            </div>
                                            </div>
                                    </div> 
                                </div>
                            </div>      
                        </div>
                    </div>
                    <div class="row">
                        <div class="row">
                            <div class="col">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                      <div class="card">
                                    <div class="card-body" style="font-size: 21px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> Image Direct Requests By Size</h4>
                                        <p class="card-text p-3 border bg-light mb-4">
                                            Direct hit on an image by Google. Meaning straight from their database or cache - filtered by image sizes
                                        </p>
                                        <div class="table">
                                            {% plotly_app name="dimage_download" ratio=0.1 %}
                                            {% plotly_app name="dirimage_tbl" ratio=1%} 
                                            </div>  
                                    </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card cards-shadown" >
                                    <div class="card-body">
                                <div class="card">
                                    <div class="card-body" style="font-size: 21px;">
                                        <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;"><i class="fa fa-rocket"></i> JSON Direct Hits By Size</h4>
                                        <p class="card-text p-3 border bg-light mb-4">
                                            Direct hit on a JSON file by Google. Meaning straight from their database or cache - filtered by json sizes
                                        </p>
                                        <div class="table">
                                            {% plotly_app name="djson_download" ratio=0.1 %}
                                            {% plotly_app name="dirjson_tbl" ratio=1%} 
                                            </div>  
                                    </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- Start: Separator with icon -->

                </div>
            {% include 'footer.html' %}
            {% include 'scripts.html' %}

        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
       </div>

</body>
{% endblock content %}

