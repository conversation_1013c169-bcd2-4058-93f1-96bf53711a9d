{% extends 'base.html' %}
{% load i18n static %}
{%load plotly_dash%}
{% load static %}
{% block content %}
<body id="page-top">
{%include 'navbar.html' %}
<h1 style="color: var(--bs-gray-100);background: #278557;">MOM Report <span style="padding-left: 45px;color: #06fa63;">Domain: {{dom.domain}}</span></h1>
                <div class="container-fluid">
                    <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Health Score</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">Here goes the diagram which is displaying successfull lookups for one year including Google, MSN (all bots)</span></h6>
                                    {% plotly_app name='healthBAr' ratio=1%}
                                    <a class="card-link" href="#">Knowledge Base</a>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Response Code (5xx)</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="background-color: rgb(248, 249, 252);">500 Internal Server Error server error response code indicates that the server encountered an unexpected condition that prevented it from fulfilling the request.&nbsp;</span></h6>
                                    {% plotly_app name='5xx_dash' ratio=1%}
                                    <a class="card-link" href="#">Knowledge Base</a>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Response Code (4xx)</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light" style="height: 110px;"><span style="color: rgb(32, 33, 36);">A 4XX Error is&nbsp;</span><strong><span style="color: rgb(32, 33, 36);">an error that often occurs when a webpage doesn't exist or has restricted access or rights</span></strong><span style="color: rgb(32, 33, 36);">.</span></h6>
                                    {% plotly_app name='error40x' ratio=1%}
                                    <a class="card-link" href="#">Knowledge Base</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Source</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light mb-4" style="height: 110px;">This graph represents data filter by source for one year. Available sources are Mobile, Desktop,Adsense Bot, Ads Not, VideoBot, Image Bot.&nbsp;</h6>
                                    {% plotly_app name='source-desktop' ratio=1%}
                                    <a class="card-link" href="#">Knowledge Base</a>
                                </div>

                            </div>
                        </div>
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Response Code (3xx)</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light mb-4" style="height: 110px;">This class of status code indicates that further action needs to be taken by the user agent in order to fulfill the request.&nbsp;</h6>
                                    <p class="card-text"></p>{% plotly_app name='error30x' ratio=1%}<a class="card-link" href="#">Knowledge Base</a>
                                </div>

                            </div>
                        </div>
                        <div class="col">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Response Code (2xx)</h4>
                                    <h6 class="text-muted card-subtitle p-3 border bg-light mb-4" style="height: 110px;">A 2xx Succesful status code means that the request was successful and the browser has received the expected information.&nbsp;</h6>{% plotly_app name='error20x' ratio=1%}
                                    <a class="card-link" href="#">Knowledge Base</a>
                                </div>

                            </div>
                        </div>
                    </div>
                                        <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="row">
                                <div class="col">
                                    <h1 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;">Bots</h1>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">AdsBot</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by source for one year and only for AdsBot when Ads bot is detected in the User Agent string. 
                                                The AdsBot checks web page ad quality.
                                                </p>
                                            {% plotly_app name='adsBot' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Adsense Bot</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by source for one year and only for Adsense bot.
                                                The AdSense bot visits your site to determine its content in order to provide relevant ads.
                                            </p>
                                            {% plotly_app name='adsenseBot' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Video Bot</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filtered by source for one year and only in case whent Googlebot
                                                Video bot is detected in the User Agent string. 
                                            </p>
                                            {% plotly_app name='videoBot' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Image Bot</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filtered by source for one year and only in case when Googlebot Image
                                                 bot is detected in the User Agent String. 
                                                </p>
                                            {% plotly_app name='imageBot' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="row">
                                <div class="col">
                                    <h1 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;">Lookups</h1>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Unknown Host</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                After the DNS lookup check,  if no DNS records were found during the verificator will return Unknown Host status. 
                                                </p>
                                            {% plotly_app name='lookupx-1' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Host Name and Lookup Failure</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by source for one year, for all the hits where a reverse DNS lookup check
                                                verification failed and return Host Name And Lookup Failure status.
                                            </p>
                                            {% plotly_app name='lookupx-2' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">No Bot Name Speciffied</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                If DNS Lookup check returns a "No Bot Name Specified" response,
                                                that is usually the case when the User Agent string does not have specified information about the bot origin.
                                                </p>
                                            {% plotly_app name='lookupx-3' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="row">
                                <div class="col">
                                    <h1 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;">Hits On Target And Source</h1>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Hits On Page</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by target for one year where the target was a regular HTML page.
                                            </p>
                                            {% plotly_app name='target-p' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Hits On Json</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by target for one year where the target was a Json file. (.json extension is at the end of the file)
                                                </p>
                                            {% plotly_app name='target-json' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Hits On CSS</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by target for one year where the target was a CSS file. (.css extension at the end of the url)
                                                </p>
                                            {% plotly_app name='target-css' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Hits On JavaScript</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by target for one year where the target was a JavaScript resource. (.js extension at the end of the file)
                                                </p>
                                            {% plotly_app name='target-jas' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Hits On Image</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by target for one year where the target was an image. (.jpg, jpeg, png,swg, webp as extension at the end of the file)
                                                </p>
                                            {% plotly_app name='target-imag' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Hits On PHP</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                This graph represents data filter by target for one year where the target was a PHP file. (.php extension at the end of the url)
                                            </p>
                                            {% plotly_app name='target-php' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="row">
                                <div class="col">
                                    <h1 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;">Methods</h1>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">GET Method</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                The HTTP GET method is used to request a resource from a server, such as a page or an image. 
                                                It should only retrieve data and not modify any state on the server.
                                                </p>
                                            {% plotly_app name='method-get' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">OPTIONS Method</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                The OPTIONS method is a type of HTTP request that is used to determine the available communication options for a particular resource.
                                                 When a client sends an OPTIONS request to a server, the server responds with an Allow header 
                                                 that lists the HTTP methods that are supported by the target resource.
                                                </p>
                                            {% plotly_app name='method-options' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">POST Method</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                The HTTP POST method is used to send data to a server to create or update a resource, 
                                                as opposed to the GET method which is used to request data from a specified resource 
                                            </p>
                                            {% plotly_app name='method-post' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">CONNECT Method</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                The CONNECT HTTP method is used to establish a network connection to a web server via a proxy server. The client sends an HTTP CONNECT request to the proxy server, 
                                                which forwards the request to the destination server.
                                                </p>
                                                <img src="/assets/img/hits-on-javascript.png" style="width: 600px;min-width: 450px;"><a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">HEAD Method</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                The HTTP HEAD method is used to request only the headers that would be returned if the same URL was requested with the HTTP GET method. 
                                                This is useful when you want to check certain headers of a resource (e.g containt length) without actually downloading the entire file.
                                            </p>
                                            {% plotly_app name='method-head' ratio=1%} 
                                            <a class="card-link" href="#" style="padding: 110px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">DELETE Method</h4>
                                            <p class="card-text p-3 border bg-light mb-4">
                                                The DELETE method is used to delete a resource from the server. It is a type of HTTP call that requests the server to delete the specified resource.
                                            </p>
                                            <img src="/assets/img/hits-on-php.png" style="width: 600px;min-width: 450px;"><a class="card-link" href="#" style="padding: 110px;padding-left: 0px;padding-top: 610px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row p-3 border bg-light">
                        <div class="col">
                            <div class="row">
                                <div class="col">
                                    <h1 class="card-title" style="color: var(--bs-gray-100);background: #278557;border-radius: 0;text-align: left;">Protocols</h1>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Protocol HTTP/1.0</h4>
                                            <p class="card-text p-3 border bg-light mb-4"><span style="color: rgb(32, 33, 36); background-color: rgb(255, 255, 255);">It is a generic, stateless, object-oriented protocol which can be used for many tasks, such as name servers and distributed object management systems...</span></p>
                                            {% plotly_app name='protocol-10' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Protocol HTTP/1.1</h4>
                                            <p class="card-text p-3 border bg-light mb-4">HTTP 1.1 is the latest version of Hypertext Transfer Protocol (HTTP), the World Wide Web application protocol that runs on top of the Internet's TCP/IP suite of protocols.</p>
                                            {% plotly_app name='protocol-11' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">Protocol HTTP/2.0</h4>
                                            <p class="card-text p-3 border bg-light mb-4">HTTP/2 is a major revision of the HTTP network protocol used by the World Wide Web. It was derived from the earlier experimental SPDY protocol, originally developed by Google.</p>
                                            {% plotly_app name='protocol-20' ratio=1%}
                                            <a class="card-link" href="#" style="padding: 110px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card">
                                        <div class="card-body">
                                            <h4 class="card-title" style="color: var(--bs-gray-100);background: #6eb589;border-radius: 0;text-align: left;">No Protocol</h4>
                                            <p class="card-text p-3 border bg-light mb-4">This section indicates situation when information about protocol is not supplied or present in response due to the error or wrong configuration.</p>
                                            {% plotly_app name='protocol-noproto' ratio=1%} 
                                            <a class="card-link" href="#" style="padding: 110px;">Knowledge Base</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div><a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    {% include 'footer.html' %}
    {% include 'scripts.html' %}
</body>

{% endblock content %}
