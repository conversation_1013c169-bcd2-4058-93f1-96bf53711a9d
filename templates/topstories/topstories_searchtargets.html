{% extends 'base.html' %}
{% load i18n static %}
{% load plotly_dash %}
{% load static %}
{% block content %}
    {% include 'topstories/ts_navbar.html' %}

<div class="container">
    <h1 class="mt-4">Search Targets</h1>

    <!-- Filters and Search -->
    <div class="row my-4">
        <!-- Language Filter -->
        <div class="col-md-3">
            <form id="language-form" method="GET">
                <label for="language-filter">Language:</label>
                <select name="language" id="language-filter" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    {% for language in languages %}
                        <option value="{{ language }}" {% if language_filter == language %}selected{% endif %}>{{ language }}</option>
                    {% endfor %}
                </select>
            </form>
        </div>
        <!-- Country Filter -->
        <div class="col-md-3">
            <form id="country-form" method="GET">
                <label for="country-filter">Country:</label>
                <select name="country" id="country-filter" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    {% for country in countries %}
                        <option value="{{ country }}" {% if country_filter == country %}selected{% endif %}>{{ country }}</option>
                    {% endfor %}
                </select>
            </form>
        </div>
        <!-- Search Form -->
        <div class="col-md-3">
            <form id="search-form" method="GET">
                <label for="search">Keyword Search:</label>
                <input type="text" name="search" id="search" class="form-control" value="{{ search_query }}" placeholder="Search Keywords">
                <button type="submit" class="btn btn-primary mt-2">Search</button>
            </form>
        </div>
    </div>

<!-- Table -->
<div class="row">
    <div class="col">
        <div class="table-responsive animate__animated animate__fadeIn">
            <table id="search-targets-table" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Keyword</th>
                        <th>Language</th>
                        <th>Country</th>
                    </tr>
                </thead>
                <tbody>
                    {% for target in page %}
                        <tr>
                            <td>{{ target.id }}</td>
                            <!-- Wrap keyword in an anchor tag -->
                            <td><a href="{% url 'ts_keyword' target.keyword %}">{{ target.keyword }}</a></td>
                            <td>{{ target.language }}</td>
                            <td>{{ target.country }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


    <!-- Pagination -->
    <div class="row mt-3">
        <div class="col">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page.previous_page_number }}&language={{ language_filter }}&country={{ country_filter }}&search={{ search_query }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}
                    {% for num in page.paginator.page_range %}
                        {% if num == page.number %}
                            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}&language={{ language_filter }}&country={{ country_filter }}&search={{ search_query }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    {% if page.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page.next_page_number }}&language={{ language_filter }}&country={{ country_filter }}&search={{ search_query }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>
{% endblock content %}

{% block extra_head %}
<style>
    /* Add animate.css link in the head of your base.html */
    @import url("https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css");
</style>
{% endblock extra_head %}













{#{% extends 'base.html' %}#}
{#{% load static %}#}
{##}
{#{% block content %}#}
{#<div class="container">#}
{#  <h1 class="mt-4">Search Targets</h1>#}
{##}
{#  <!-- Filters and Search -->#}
{#  <div class="row my-4">#}
{#    <!-- Language Filter -->#}
{#    <div class="col-md-3">#}
{#      <form id="language-form" method="GET">#}
{#        <label for="language-filter">Language:</label>#}
{#        <select name="language" id="language-filter" class="form-control" onchange="this.form.submit()">#}
{#          <option value="">All</option>#}
{#          {% for language in languages %}#}
{#            <option value="{{ language }}" {% if language_filter == language %}selected{% endif %}>{{ language }}</option>#}
{#          {% endfor %}#}
{#        </select>#}
{#      </form>#}
{#    </div>#}
{##}
{#    <!-- Country Filter -->#}
{#    <div class="col-md-3">#}
{#      <form id="country-form" method="GET">#}
{#        <label for="country-filter">Country:</label>#}
{#        <select name="country" id="country-filter" class="form-control" onchange="this.form.submit()">#}
{#          <option value="">All</option>#}
{#          {% for country in countries %}#}
{#            <option value="{{ country }}" {% if country_filter == country %}selected{% endif %}>{{ country }}</option>#}
{#          {% endfor %}#}
{#        </select>#}
{#      </form>#}
{#    </div>#}
{##}
{#    <!-- Search Form -->#}
{#    <div class="col-md-3">#}
{#      <form id="search-form" method="GET">#}
{#        <label for="keyword-search">Keyword Search:</label>#}
{#        <input type="text" name="keyword" id="keyword-search" class="form-control" value="{{ keyword_filter }}" placeholder="Search Keywords">#}
{#      </form>#}
{#    </div>#}
{#  </div>#}
{##}
{#  <!-- Table -->#}
{#  <div class="row">#}
{#    <div class="col">#}
{#      <div class="table-responsive animate__animated animate__fadeIn">#}
{#        <table id="search-targets-table" class="table table-striped table-bordered">#}
{#          <thead>#}
{#            <tr>#}
{#              <th>ID</th>#}
{#              <th>Keyword</th>#}
{#              <th>Language</th>#}
{#              <th>Country</th>#}
{#            </tr>#}
{#          </thead>#}
{#          <tbody>#}
{#            {% for target in targets %}#}
{#              <tr>#}
{#                <td>{{ target.id }}</td>#}
{#                <td>{{ target.keyword }}</td>#}
{#                <td>{{ target.language }}</td>#}
{#                <td>{{ target.country }}</td>#}
{#              </tr>#}
{#            {% endfor %}#}
{#          </tbody>#}
{#        </table>#}
{#      </div>#}
{#    </div>#}
{#  </div>#}
{##}
{#  <!-- Pagination -->#}
{#  <div class="row mt-3">#}
{#    <div class="col">#}
{#      <nav aria-label="Page navigation">#}
{#        <ul class="pagination justify-content-center">#}
{#          {% if page.has_previous %}#}
{#            <li class="page-item">#}
{#              <a class="page-link" href="?page={{ page.previous_page_number }}{% if language_filter %}&language={{ language_filter }}{% endif %}{% if country_filter %}&country={{ country_filter }}{% endif %}" aria-label="Previous">#}
{#                <span aria-hidden="true">&laquo;</span>#}
{#              </a>#}
{#            </li>#}
{#          {% endif %}#}
{#          {% for num in page.paginator.page_range %}#}
{#            {% if num == page.number %}#}
{#              <li class="page-item active"><span class="page-link">{{ num }}</span></li>#}
{#            {% else %}#}
{#              <li class="page-item">#}
{#                <a class="page-link" href="?page={{ num }}{% if language_filter %}&language={{ language_filter }}{% endif %}{% if country_filter %}&country={{ country_filter }}{% endif %}">{{ num }}</a>#}
{#              </li>#}
{#            {% endif %}#}
{#          {% endfor %}#}
{#          {% if page.has_next %}#}
{#            <li class="page-item">#}
{#              <a class="page-link" href="?page={{ page.next_page_number }}{% if language_filter %}&language={{ language_filter }}{% endif %}{% if country_filter %}&country={{ country_filter }}{% endif %}" aria-label="Next">#}
{#                <span aria-hidden="true">&raquo;</span>#}
{#              </a>#}
{#            </li>#}
{#          {% endif %}#}
{#        </ul>#}
{#      </nav>#}
{#    </div>#}
{#  </div>#}
{#</div>#}
{#{% endblock content %}#}
{##}
{#{% block extra_head %}#}
{#<style>#}
{#  /* Add animate.css link in the head of your base.html */#}
{#  @import url("https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css");#}
{#</style>#}
{#{% endblock extra_head %}#}
