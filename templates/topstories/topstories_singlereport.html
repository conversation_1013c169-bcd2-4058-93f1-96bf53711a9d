{% extends 'base.html' %}
{% load i18n static %}
{% load plotly_dash %}
{% load static %}
{% block content %}
<body id="page-top">
{% include 'topstories/ts_navbar.html' %}

<div class="container-fluid">
    <div class="row p-3 border bg-light">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    {% plotly_app name='SingleKeywordReport' ratio=1 %}
                    <a class="card-link" href="#">Knowledge Base</a>
                </div>
            </div>
        </div>
    </div>
</div>
<a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>

{% include 'footer.html' %}
{% include 'scripts.html' %}
</body>
{% endblock content %}

<style>
    body, html {
        margin: 0; /* Reset any default margin */
        height: 100%; /* Full height */
        overflow: hidden; /* Hide scrollbars */
        background-color: #1d5c42; /* Dark green background */
    }

    .container-fluid {
        padding-bottom: 50px; /* Add some space at the bottom */
        margin: auto; /* Center the container */
        width: 90vw; /* Set the width to 90% of the viewport width */
        max-width: 100%; /* Ensure it doesn't exceed the viewport width */
    }

    .card {
        background-color: #1d5c42; /* Dark green background for the card */
        border: none; /* Remove border if not needed */
    }

    .scroll-to-top {
        position: fixed; /* Fixed position to keep it visible */
        right: 15px; /* Distance from the right */
        bottom: 15px; /* Distance from the bottom */
        z-index: 1000; /* Ensure it's above other elements */
    }
</style>
