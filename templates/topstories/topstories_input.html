{% extends 'base.html' %}
{% load i18n static %}
{% load plotly_dash %}
{% load static %}
{% block content %}

<style>
    body {
        font-family: Arial, sans-serif;
        text-align: center;
    }
    .table-container {
        display: inline-block;
        margin: 0 auto;
        text-align: left;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }
    table, th, td {
        border: 1px solid black;
    }
    th, td {
        padding: 8px;
        text-align: left;
    }
    th {
        background-color: #f2f2f2;
    }
    tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    .pagination {
        display: inline-block;
        margin: 20px 0;
    }
    .pagination a, .pagination span {
        margin: 0 5px;
        padding: 5px 10px;
        text-decoration: none;
        border: 1px solid #ddd;
        color: #007bff;
    }
    .pagination a:hover {
        background-color: #ddd;
    }
    .pagination .current {
        background-color: #007bff;
        color: white;
        border: 1px solid #007bff;
    }
    .actions button {
        margin-right: 5px;
        padding: 5px 10px;
        border: none;
        cursor: pointer;
    }
    .actions button[type="button"] {
        background-color: #f0ad4e;
        color: white;
    }
    .actions button[type="submit"] {
        background-color: #5cb85c;
        color: white;
    }
    .actions button[type="button"]:last-child {
        background-color: #d9534f;
        color: white;
    }
    .info-icon {
        font-size: 14px;
        margin-left: 5px;
        color: #888;
        cursor: pointer;
        border-bottom: 1px dotted #888;
        position: relative; /* Position relative to the icon */
    }

    .info-icon:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        background-color: #333;
        color: #fff;
        padding: 5px;
        border-radius: 5px;
        top: 100%; /* Position below the icon */
        left: 0;
        z-index: 1;
        white-space: nowrap;
    }
    .filter-form {
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }
    .filter-form input,
    .filter-form select {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
    }
    .filter-form button[type="submit"] {
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    }
    .filter-form button[type="submit"]:hover {
        background-color: #0056b3;
    }
    .add-form {
        margin: 20px 0;
        text-align: center;
    }
    .add-form table {
        margin: 0 auto;
    }
    .add-form input,
    .add-form select {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
    }
    .add-form button[type="submit"] {
        padding: 10px 20px;
        background-color: #5cb85c;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    }
    .add-form button[type="submit"]:hover {
        background-color: #4cae4c;
    }
    .upload-form {
        margin: 20px 0;
        text-align: center;
    }
    .upload-form input[type="file"] {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        display: inline-block;
    }
    .upload-form button[type="submit"] {
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    }
    .upload-form button[type="submit"]:hover {
        background-color: #0056b3;
    }
    .popup-container {
        display: none; /* Hide initially */
        justify-content: center;
        align-items: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }
    .popup {
        background-color: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        margin: auto;
        max-width: 80%; /* Adjust the width as needed */
        max-height: 80%; /* Adjust the height as needed */
        overflow: auto; /* Enable scrolling if content exceeds popup size */
        position: relative; /* Ensure proper stacking */
    }
    .popup h2 {
        margin-top: 0;
    }
    .popup .close {
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
        color: #888;
    }
    .popup form {
        text-align: center;
    }
    .popup input[type="file"] {
        margin: 10px 0;
    }
    .popup button[type="submit"] {
        margin-top: 10px;
        padding: 10px 20px;
        background-color: #5cb85c;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    }
    .popup button[type="submit"]:hover {
        background-color: #4cae4c;
    }
</style>


</head>
<body>
{% include 'topstories/ts_navbar.html' %}

{#<h1>Top Stories Input Search Targets</h1>#}
{#<h3>Hello {{manager_info.full_name }}, you have access to market "{{ manager_info.market }}" which has limit of {{ manager_info.market_limit }}, current amount is {{ manager_info.keyword_amount }}</h3>#}




    <style>
        .comparison {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .within-limit {
            background-color: #d4edda; /* Light green background */
            color: #155724;           /* Dark green text */
        }
        .exceeding-limit {
            background-color: #f8d7da; /* Light red background */
            color: #721c24;            /* Dark red text */
        }
    </style>

    <h1>Top Stories Input Search Targets</h1>
<h4>
    Hello {{ manager_info.full_name }}, you have available
    <span class="comparison {% if manager_info.keyword_amount <= manager_info.market_limit %}within-limit{% else %}exceeding-limit{% endif %}">
        {{ manager_info.keyword_amount }}
    </span> / {{ manager_info.market_limit }}.
</h4>








<div class="table-container">
    <!-- Filter Form -->
    <form method="get" class="filter-form">
        <input type="text" name="keyword" placeholder="Keyword" value="{{ request.GET.keyword }}">
        <select name="language">
            <option value="">Language</option>
            {% for hl_code, language in languages %}
                <option value="{{ hl_code }}" {% if request.GET.language == hl_code %}selected{% endif %}>{{ hl_code }} - {{ language }}</option>
            {% endfor %}
        </select>
        <select name="country">
            <option value="">Country</option>
            {% for gl_code, country in countries %}
                <option value="{{ gl_code }}" {% if request.GET.country == gl_code %}selected{% endif %}>{{ gl_code }} - {{ country }}</option>
            {% endfor %}
        </select>
        <select name="active">
            <option value="">Active</option>
            <option value="1" {% if request.GET.active == "1" %}selected{% endif %}>1</option>
            <option value="0" {% if request.GET.active == "0" %}selected{% endif %}>0</option>
        </select>
        <button type="submit">Filter</button>
    </form>

<table>
    <thead>
        <tr>
            {% for column in columns %}
                <th>
                    {{ column }}
                    {% if column == 'keyword' %}
                        <span class="info-icon" data-tooltip="Keyword that is being tracked">ℹ️</span>
                    {% elif column == 'language' %}
                        <span class="info-icon" data-tooltip="The language displayed on SERP">ℹ️</span>
                    {% elif column == 'country' %}
                        <span class="info-icon" data-tooltip="Where to search from">ℹ️</span>
                    {% elif column == 'active' %}
                        <span class="info-icon" data-tooltip="Flag for managing tracking keywords: 1 for active, 0 for not tracking">ℹ️</span>
                    {% endif %}
                </th>
            {% endfor %}
            <th class="actions">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for row in page_obj %}
            <form method="post">
                {% csrf_token %}
                <tr>
                    {% for col in row %}
                        <td>
                            {% if forloop.first %}
                                <input type="hidden" name="id" value="{{ col }}">
                                {{ col }} <!-- Display the ID -->
                            {% elif forloop.counter == 2 %}
                                <input type="text" name="{{ forloop.counter0 }}" value="{{ col }}" readonly>
                            {% elif forloop.counter == 3 %}
                                <select name="{{ forloop.counter0 }}">
                                    {% for hl_code, language in languages %}
                                        <option value="{{ hl_code }}" {% if col == hl_code %}selected{% endif %}>{{ hl_code }} - {{ language }}</option>
                                    {% endfor %}
                                </select>
                            {% elif forloop.counter == 4 %}
                                <select name="{{ forloop.counter0 }}">
                                    {% for gl_code, country in countries %}
                                        <option value="{{ gl_code }}" {% if col == gl_code %}selected{% endif %}>{{ gl_code }} - {{ country }}</option>
                                    {% endfor %}
                                </select>
                            {% else %}
                                <select name="{{ forloop.counter0 }}">
                                    <option value="1" {% if col == 1 %}selected{% endif %}>1</option>
                                    <option value="0" {% if col == 0 %}selected{% endif %}>0</option>
                                </select>
                            {% endif %}
                        </td>
                    {% endfor %}
                    <td class="actions">
                        <button type="button" onclick="enableEdit(this)">Edit</button>
                        <button type="submit">Save</button>
                        <button type="button" onclick="deleteRow({{ row.0 }})">Delete</button>
                    </td>
                </tr>
            </form>
        {% endfor %}
    </tbody>
</table>

<!-- Button to trigger popups -->
<div class="actions" style="display: flex; justify-content: center; margin-bottom: 20px;">
    <button type="button" onclick="openPopups()" style="background-color: #800080; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">Add keyword/s for tracking & Upload CSV</button>
</div>

<!-- Popup Container -->
<div id="popupContainer" class="popup-container">
    <!-- Popup for Add New Row -->
    <div id="addRowPopup" class="popup">
        <span class="close" onclick="closePopups()">&times;</span>
        <div class="add-form">
            <h2>Add New Row</h2>
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <table>
                    <tr>
                        {% for column in columns %}
                            {% if column != 'id' and column != 'active' %}
                                {% if column == 'language' %}
                                    <td>
                                        <select name="new_language" id="new-language-select" style="width: 200px;">
                                            <option value="">Language</option>
                                            {% for hl_code, language in languages %}
                                                <option value="{{ hl_code }}">{{ hl_code }} - {{ language }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                {% elif column == 'country' %}
                                    <td>
                                        <select name="new_country" id="new-country-select" style="width: 200px;">
                                            <option value="">Country</option>
                                            {% for gl_code, country in countries %}
                                                <option value="{{ gl_code }}">{{ gl_code }} - {{ country }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                {% else %}
                                    <td><input type="text" name="new_{{ column }}" placeholder="{{ column }}"></td>
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    </tr>
                </table>
                <button type="submit">Add</button>
            </form>
        </div>
    </div>

    <!-- Popup for CSV Upload -->
    <div id="uploadCsvPopup" class="popup">
        <span class="close" onclick="closePopups()">&times;</span>
        <div class="upload-form">
            <h2>Upload CSV</h2>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="file" name="csv_file" accept=".csv">
                <button type="submit">Upload</button>
            </form>
        </div>
    </div>
</div>

<script>
    function openPopups() {
        document.getElementById('popupContainer').style.display = 'flex';
    }

    function closePopups() {
        document.getElementById('popupContainer').style.display = 'none';
    }
</script>

<div class="pagination">
    <span class="step-links">
        {% if page_obj.has_previous %}
            <a href="?page=1&{{ query_string }}">first</a>
            <a href="?page={{ page_obj.previous_page_number }}&{{ query_string }}">previous</a>
        {% endif %}

        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.
        </span>

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}&{{ query_string }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}&{{ query_string }}">last</a>
        {% endif %}
    </span>
</div>

</div>

<script>
function enableEdit(button) {
    const row = button.closest('tr');
    const inputs = row.querySelectorAll('input[type="text"], select');
    inputs.forEach(input => {
        input.removeAttribute('readonly');
        input.removeAttribute('disabled');
    });
}

function deleteRow(rowId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = ''; // Specify the correct URL

    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').cloneNode(true);
    form.appendChild(csrfToken);

    const inputAction = document.createElement('input');
    inputAction.type = 'hidden';
    inputAction.name = 'action';
    inputAction.value = 'delete';
    form.appendChild(inputAction);

    const inputId = document.createElement('input');
    inputId.type = 'hidden';
    inputId.name = 'id';
    inputId.value = rowId;
    form.appendChild(inputId);

    document.body.appendChild(form);
    form.submit();
}
</script>

{% include 'footer.html' %}
{% include 'scripts.html' %}
</body>
{% endblock content %}