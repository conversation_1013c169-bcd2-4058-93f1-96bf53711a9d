{% extends 'base.html' %}
{% load i18n static %}
{% load plotly_dash %}
{% load static %}
{% block content %}
<body id="page-top">
{% include 'topstories/ts_navbar.html' %}

<!-- <div class="container-fluid"> -->
    <!-- <div class="row p-3 border bg-light"> -->
        <!-- <div class="col"> -->
            <!-- <div class="card"> -->
                <!-- <div class="card-body"> -->
                    {% plotly_app name='TopStoriesReport' ratio=2.4 %}
                    <a class="card-link" href="#">Knowledge Base</a>
                <!-- </div> -->
            <!-- </div> -->
        <!-- </div> -->
    <!-- </div> -->
<!-- </div> -->
<a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>

{% include 'footer.html' %}
{% include 'scripts.html' %}
</body>
{% endblock content %}



<style>
    /* Modern Dashboard Styles */
    html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        font-family: 'Poppins', 'Helvetica Neue', sans-serif;
        background-color: #f8f9fa;
        color: #212529;
    }

    /* Dashboard Container */
    .dashboard-wrapper {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .dashboard-container {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Sidebar Styles */
    .sidebar-col {
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        z-index: 100;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .sidebar {
        padding: 0;
        background-color: #ffffff;
    }

    /* Main Content Area */
    .content-col {
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    /* Card Styles */
    .card {
        border-radius: 12px;
        border: none;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1) !important;
    }

    .card-header {
        border-top-left-radius: 12px !important;
        border-top-right-radius: 12px !important;
        background-color: #ffffff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 16px 20px;
        font-weight: 600;
    }

    .card-body {
        padding: 20px;
        position: relative;
    }

    /* Card animations */
    .card.shadow-sm {
        animation: fadeInUp 0.5s ease-out forwards;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Staggered animation for cards */
    .row .col:nth-child(1) .card { animation-delay: 0.1s; }
    .row .col:nth-child(2) .card { animation-delay: 0.2s; }
    .row .col:nth-child(3) .card { animation-delay: 0.3s; }
    .row .col:nth-child(4) .card { animation-delay: 0.4s; }

    /* KPI Cards */
    .card .fa-2x, .card .fa-3x {
        opacity: 0.9;
        filter: drop-shadow(0 2px 3px rgba(0,0,0,0.1));
    }

    /* KPI values styling */
    h2.display-5, h3.fw-bold {
        background: linear-gradient(45deg, #4361ee, #4cc9f0);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 700 !important;
    }

    /* Badge styling */
    .badge.bg-success {
        background: linear-gradient(45deg, #4caf50, #8bc34a) !important;
        padding: 6px 10px;
        font-weight: 500;
        border-radius: 30px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    /* Tab Styles */
    .nav-tabs {
        border-bottom: none;
        gap: 5px;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        padding: 12px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin-bottom: 5px;
        position: relative;
        overflow: hidden;
    }

    .nav-tabs .nav-link::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0;
        background: linear-gradient(45deg, rgba(67, 97, 238, 0.1), rgba(76, 201, 240, 0.1));
        transition: height 0.3s ease;
        z-index: -1;
    }

    .nav-tabs .nav-link.active {
        color: #4361ee;
        background-color: rgba(67, 97, 238, 0.08);
        font-weight: 600;
    }

    .nav-tabs .nav-link.active::before {
        height: 100%;
    }

    .nav-tabs .nav-link:hover {
        color: #4361ee;
        background-color: rgba(67, 97, 238, 0.05);
    }

    /* Active tab indicator animation */
    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #4361ee, #4cc9f0);
        animation: slideIn 0.3s ease-out forwards;
        transform-origin: left;
    }

    @keyframes slideIn {
        from { transform: scaleX(0); }
        to { transform: scaleX(1); }
    }

    /* Button Styles */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: -1;
    }

    .btn:hover::before {
        transform: translateX(0);
    }

    .btn-primary {
        background: linear-gradient(45deg, #4361ee, #3f37c9);
        border: none;
        box-shadow: 0 4px 6px rgba(67, 97, 238, 0.2);
    }

    .btn-primary:hover {
        background: linear-gradient(45deg, #3f37c9, #3a0ca3);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(67, 97, 238, 0.3);
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.875rem;
    }

    /* Button with icon */
    .btn i {
        transition: transform 0.3s ease;
    }

    .btn:hover i {
        transform: translateX(3px);
    }

    /* Dropdown Styles */
    .Select-control {
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .Select-placeholder, .Select-input, .Select-value {
        padding: 8px 12px;
    }

    /* Graph Styles */
    .js-plotly-plot,
    .plot-container {
        width: 100% !important;
        height: 100% !important;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    /* Chart hover effects */
    .js-plotly-plot:hover {
        filter: brightness(1.02);
    }

    /* Sparkline styling */
    .sparkline {
        border-radius: 8px;
        overflow: hidden;
        background: linear-gradient(to right, rgba(67, 97, 238, 0.05), rgba(76, 201, 240, 0.05));
    }

    /* 3D visualization container */
    #3d-visualization {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(76, 201, 240, 0.05));
        border-radius: 8px;
        overflow: hidden;
    }

    /* Word cloud container */
    #word-cloud-container {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.03), rgba(76, 201, 240, 0.03));
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Prevent internal scrolling in Dash app */
    #TopStoriesReport {
        overflow: hidden !important;
    }

    /* Dark Mode Styles (Applied via JavaScript) */
    body.dark-mode {
        background-color: #121212;
        color: #f8f9fa;
    }

    body.dark-mode .sidebar,
    body.dark-mode .sidebar-col {
        background-color: #1a1a1a;
        color: #f8f9fa;
        border-right: 1px solid #333;
    }

    body.dark-mode .card {
        background-color: #1e1e1e;
        color: #f8f9fa;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    body.dark-mode .card-header {
        background-color: #252525;
        color: #f8f9fa;
        border-bottom: 1px solid #333;
    }

    body.dark-mode .content-col {
        background-color: #121212;
    }

    body.dark-mode .text-muted {
        color: #adb5bd !important;
    }

    body.dark-mode .btn-light {
        background-color: #333;
        color: #f8f9fa;
        border-color: #444;
    }

    /* Dark mode card hover */
    body.dark-mode .card:hover {
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4) !important;
        background-color: #252525;
    }

    /* Dark mode tabs */
    body.dark-mode .nav-tabs .nav-link {
        color: #adb5bd;
    }

    body.dark-mode .nav-tabs .nav-link.active {
        color: #4cc9f0;
        background-color: rgba(76, 201, 240, 0.1);
    }

    body.dark-mode .nav-tabs .nav-link:hover {
        color: #4cc9f0;
        background-color: rgba(76, 201, 240, 0.05);
    }

    /* Dark mode KPI values */
    body.dark-mode h2.display-5,
    body.dark-mode h3.fw-bold {
        background: linear-gradient(45deg, #4cc9f0, #4361ee);
        -webkit-background-clip: text;
        background-clip: text;
    }

    /* Dark mode charts */
    body.dark-mode .js-plotly-plot:hover {
        filter: brightness(1.05);
    }

    /* Dark mode sparkline */
    body.dark-mode .sparkline,
    body.dark-mode #3d-visualization,
    body.dark-mode #word-cloud-container {
        background: linear-gradient(135deg, rgba(76, 201, 240, 0.1), rgba(67, 97, 238, 0.1));
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .sidebar-col {
            height: auto;
            position: relative;
        }

        .content-col {
            padding: 15px;
        }

        .card {
            margin-bottom: 15px;
        }
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Dark mode scrollbar */
    body.dark-mode::-webkit-scrollbar-track {
        background: #333;
    }

    body.dark-mode::-webkit-scrollbar-thumb {
        background: #666;
    }
</style>

<!-- Dark Mode Toggle Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for dark mode toggle button clicks
        document.addEventListener('click', function(e) {
            if (e.target && (e.target.id === 'dark-mode-toggle' || e.target.closest('#dark-mode-toggle'))) {
                document.body.classList.toggle('dark-mode');

                // Store preference in localStorage
                if (document.body.classList.contains('dark-mode')) {
                    localStorage.setItem('darkMode', 'enabled');
                } else {
                    localStorage.setItem('darkMode', 'disabled');
                }
            }
        });

        // Check for saved preference
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark-mode');
        }
    });
</script>
