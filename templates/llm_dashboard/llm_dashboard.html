{% extends 'base.html' %}
{% load i18n static %}

{% block body_class %}{% if dark_mode %}dark-mode{% endif %}{% endblock %}

{% block content %}
{% include 'navbar.html' %}
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css" />
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

<div class="py-8 px-4 {% if dark_mode %}bg-[#1e1e1e]{% else %}bg-gray-100{% endif %}">
  <h1 class="text-4xl font-extrabold text-center text-gray-800 mb-8">
    LLM Search <span class="text-green-600">Share of Voice</span> Dashboard
  </h1>

  <form method="get" id="source-date-form" class="flex flex-wrap gap-4 items-center justify-center mb-6">
    <div class="inline-flex rounded-full border overflow-hidden shadow text-sm bg-white">
      <!-- OpenAI -->
      <label for="source-openai"
            class="px-4 py-2 cursor-pointer transition-all duration-150 font-medium
            {% if source == 'openai' %}
              bg-green-600 text-white font-bold
            {% else %}
              text-gray-700 hover:bg-green-100
            {% endif %}">
        <input type="radio" name="source" value="openai" id="source-openai" class="hidden"
              {% if source == "openai" %}checked{% endif %}
              onchange="document.getElementById('source-date-form').submit();">
        OpenAI
      </label>

      <!-- Gemini -->
      <label for="source-gemini"
            class="px-4 py-2 cursor-pointer transition-all duration-150 font-medium
            {% if source == 'gemini' %}
              bg-blue-600 text-white font-bold
            {% else %}
              text-gray-700 hover:bg-blue-100
            {% endif %}">
        <input type="radio" name="source" value="gemini" id="source-gemini" class="hidden"
              {% if source == "gemini" %}checked{% endif %}
              onchange="document.getElementById('source-date-form').submit();">
        Gemini
      </label>
    </div>





    <div class="flex flex-col">
      <label for="date" class="text-sm font-semibold text-gray-700 mb-1">Snapshot Date:</label>
      <select name="date" id="date" class="px-3 py-2 border rounded text-sm" onchange="document.getElementById('source-date-form').submit();">
        {% for d in date_options %}
          <option value="{{ d }}" {% if d == selected_date|stringformat:"s" %}selected{% endif %}>{{ d }}</option>
        {% endfor %}
      </select>
    </div>

    <input type="hidden" name="tab" value="{{ active_tab }}">
  </form>

  <div class="flex space-x-4 border-b mb-6">
    <a href="?tab=markets&source={{ source }}&date={{ selected_date }}" class="{% if active_tab == 'markets' %}border-b-2 border-green-600 text-green-700{% else %}text-gray-600{% endif %} pb-2">Markets</a>
    <a href="?tab=keywords&source={{ source }}&date={{ selected_date }}&market={{ selected_market }}&keyword={{ selected_keyword }}" class="{% if active_tab == 'keywords' %}border-b-2 border-green-600 text-green-700{% else %}text-gray-600{% endif %} pb-2">Keywords</a>
    <a href="?tab=data&source={{ source }}&date={{ selected_date }}&country={{ selected_country }}&network={{ selected_network }}&domain={{ selected_domain }}" class="{% if active_tab == 'data' %}border-b-2 border-green-600 text-green-700{% else %}text-gray-600{% endif %} pb-2">Data</a>
    <a href="?tab=report&source={{ source }}&date={{ selected_date }}&market={{ selected_market }}" class="{% if active_tab == 'report' %}border-b-2 border-green-600 text-green-700{% else %}text-gray-600{% endif %} pb-2">Report</a>
  </div>

  <div id="sovTabsContent" class="mt-4">
    {% if active_tab == 'keywords' %}
    <div id="keywords">
      <div class="card p-4 shadow-sm">
        <h5 class="fw-bold mb-4">Search Results & GPT Insight</h5>

        {% if response_text and selected_keyword %}
        <div class="accordion mb-4" id="gptResponseAccordion">
          <div class="accordion-item border">
            <h2 class="accordion-header">
              <button class="accordion-button collapsed bg-light text-dark fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#gptResponse">💬 GPT Insight for "{{ selected_keyword }}"</button>
            </h2>
            <div id="gptResponse" class="accordion-collapse collapse">
              <div class="accordion-body bg-white">
                <pre class="text-sm text-dark whitespace-pre-wrap font-mono">{{ response_text }}</pre>
              </div>
            </div>
          </div>
        </div>
        {% endif %}
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <input type="hidden" name="tab" value="keywords">
          <input type="hidden" name="source" value="{{ source }}">
          <input type="hidden" name="date" value="{{ selected_date }}">

          <div>
            <label class="block text-sm font-semibold mb-1" for="market">Market</label>
            <select name="market" id="market" class="form-select w-full">
              {% for m in market_options %}
                <option value="{{ m }}" {% if m == selected_market %}selected{% endif %}>{{ m }}</option>
              {% endfor %}
            </select>
          </div>

          <div>
            <label class="block text-sm font-semibold mb-1" for="keyword">Keyword</label>
            <select name="keyword" id="keyword" class="form-select w-full">
              <option value="">All</option>
              {% for k in keyword_options %}
                <option value="{{ k }}" {% if k == selected_keyword %}selected{% endif %}>{{ k }}</option>
              {% endfor %}
            </select>
          </div>

          <div>
            <label class="block text-sm font-semibold mb-1" for="site">Website</label>
            <select name="site" id="site" class="form-select w-full">
              <option value="">All</option>
              {% for s in website_options %}
                <option value="{{ s }}" {% if s == selected_site %}selected{% endif %}>{{ s }}</option>
              {% endfor %}
            </select>
          </div>

          <div class="flex items-end">
            <button type="submit" class="btn btn-outline-success w-full">🔍 Apply Filters</button>
          </div>
        </form>

        <form method="get" class="mb-3">
          <input type="hidden" name="tab" value="keywords">
          <input type="hidden" name="market" value="{{ selected_market }}">
          <input type="hidden" name="keyword" value="{{ selected_keyword }}">
          <input type="hidden" name="site" value="{{ selected_site }}">
          <button type="submit" name="export" value="csv" class="btn btn-outline-primary btn-sm me-2">⬇️ Export Current View</button>
          <button type="submit" name="export" value="all_gaps" class="btn btn-outline-danger btn-sm">⛔ Export All Gaps</button>
        </form>

        <div class="table-responsive">
          {{ serp_html|safe }}
        </div>
      </div>
    </div>
    {% elif active_tab == 'data' %}
    <div id="data">
      <div class="card shadow-sm p-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="fw-bold mb-0">Full Data Table</h5>
          <button onclick="resetFilters()" class="btn btn-outline-secondary btn-sm">🔄 Clear Filters</button>
        </div>
        <div class="table-responsive">
          {{ data_table_html|safe }}
        </div>
      </div>
    </div>
    {% elif active_tab == 'markets' %}
    <div id="markets">
      <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <input type="hidden" name="tab" value="{{ active_tab }}">
      </form>

      <div class="row g-4">
        <div class="col-md-6"><div class="card shadow-sm p-3">{{ fig1_html|safe }}</div></div>
        <div class="col-md-6"><div class="card shadow-sm p-3">{{ fig2_html|safe }}</div></div>
      </div>

      <form method="get" class="mb-4">
        <input type="hidden" name="tab" value="markets">
        <label for="market" class="form-label fw-bold me-2">Select Market:</label>
        <select name="market" class="form-select w-auto d-inline-block" onchange="this.form.submit()">
          {% for market in market_options %}
            <option value="{{ market }}" {% if market == selected_market %}selected{% endif %}>{{ market }}</option>
          {% endfor %}
        </select>
      </form>

      <div class="row g-4">
        <a href="?market={{ selected_market }}&export=csv&tab=markets" class="btn btn-outline-primary mt-3">⬇️ Download Website Coverage CSV</a>
        <div class="col-md-6"><div class="card shadow-sm p-3">{{ fig_left_html|safe }}</div></div>
        <div class="col-md-6"><div class="card shadow-sm p-3">{{ fig_right_html|safe }}</div></div>
      </div>
    </div>
    {% elif active_tab == 'report' %}
    <div id="report">
      {% if report_data %}
      <div class="max-w-4xl mx-auto space-y-6">
        <div class="accordion" id="reportAccordion">
          {% for report in report_data %}
          <div class="accordion-item border border-gray-200 rounded-md overflow-hidden mb-3">
            <h2 class="accordion-header" id="heading-{{ report.id }}">
              <button class="accordion-button collapsed bg-gray-100 font-semibold text-sm" type="button"
                      data-bs-toggle="collapse" data-bs-target="#collapse-{{ report.id }}"
                      aria-expanded="false" aria-controls="collapse-{{ report.id }}">
                🧾 {{ report.country }} — {{ report.date }} at {{ report.time }}
              </button>
            </h2>
            <div id="collapse-{{ report.id }}" class="accordion-collapse collapse"
                aria-labelledby="heading-{{ report.id }}" data-bs-parent="#reportAccordion">
              <div class="accordion-body bg-white px-4 py-4">
                <div class="flex justify-between items-center mb-2">
                  <a href="{% url 'export_pdf' %}?report_id={{ report.id }}" class="text-sm text-green-700 hover:underline">
                    📄 Download PDF
                  </a>
                </div>
                <pre class="whitespace-pre-wrap text-sm font-mono text-gray-800">{{ report.summary|safe }}</pre>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% else %}
      <p class="text-muted">No GPT summaries available.</p>
      {% endif %}
    </div>
  </div>
    {% endif %}
  </div>
</div>

<script>
function activateDataTable(selector) {
  const tableElement = $(selector);

  if ($.fn.DataTable.isDataTable(selector)) {
    tableElement.DataTable().clear().destroy();
    tableElement.find('thead .filters').remove();
  }

  const thead = tableElement.find('thead');
  const headerRow = thead.find('tr').first();
  const filterRow = headerRow.clone().addClass('filters').appendTo(thead);

  filterRow.find('th').each(function (i) {
    const select = $('<select class="px-2 py-1 border rounded text-sm w-full"><option value="">All</option></select>')
      .appendTo($(this).empty())
      .on('change', function () {
        const val = $.fn.dataTable.util.escapeRegex($(this).val());
        table.column(i).search(val ? '^' + val + '$' : '', true, false).draw();
      });
  });

  const table = tableElement.DataTable({
    scrollX: true,
    orderCellsTop: true,
    fixedHeader: true,
    pageLength: 25,
    autoWidth: false,
    initComplete: function () {
      this.api().columns().every(function (i) {
        const column = this;
        const select = filterRow.find('th').eq(i).find('select');
        select.empty().append('<option value="">All</option>');
        column.data().unique().sort().each(function (d) {
          if (d) select.append('<option value="' + d + '">' + d + '</option>');
        });
      });
    }
  });
}

function resetFilters() {
  $('select').val('');
  $.fn.dataTable.tables({ api: true }).search('').columns().search('').draw();
}

$(document).ready(function () {
  if ($('#data-table').length && !$.fn.DataTable.isDataTable('#data-table')) {
    activateDataTable('#data-table');
  }
  if ($('#keywords-table').length && !$.fn.DataTable.isDataTable('#keywords-table')) {
    activateDataTable('#keywords-table');
  }
});
</script>

<script>
  window.addEventListener('load', function() {
    const url = new URL(window.location);
    url.search = '';
    window.history.replaceState({}, document.title, url.pathname);
  });
</script>

{% endblock %}
