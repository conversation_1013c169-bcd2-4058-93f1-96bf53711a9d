{% extends 'base.html' %}
{% load i18n static %}
{%load plotly_dash%}
{% load static %}
{% block content %}
{% include 'psi/psi-navbar.html' %}
<body id="page-top">

    <h1 style="color: var(--bs-gray-100);background: #278557;" title="Test">crUX for URL (page level) - Explore URL's for: {{domain.domain}}</h1>
    <div class="container-fluid">

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <img src="{{domain.logo.url}}" class="card-img-top" alt="{{domain.domain}}" title="{{domain.domain}}">
                    <div class="card-body">
                        <h5 class="card-title" style="color: rgb(24, 209, 0);">{{domain.domain}}</h5>
                        <a href="/psi/page-speed/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">Page Speed</a>
                        <a href="/psi/lighthouse/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">LightHouse</a>
                        <a href="/psi/lighthouse/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">VIP pages</a>
                        <a href="/psi/domain_dash/{{domain.id}}" class="btn btn-success">Crux for Origin</a>
                        <hr class="mt-5 mb-5">
                        <form action="#"  method="POST">
                            {% csrf_token %}
                            {{ url_form }}
                            <button type="submit" class="btn btn-primary btn-primary" style="background-color: #21714A;">SUBMIT REQUEST</button>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Adding another section to preview urls-->
            <div class="col-md-8">
                <div class="card">
                   <!-- <img src="{{domain.logo.url}}" class="card-img-top" alt="{{domain.domain}}" title="{{domain.domain}}"> -->
                    <div class="card-body">
                        <h5 class="card-title" style="color: rgb(24, 209, 0);">URL's & Page Types  : {{domain.domain}}</h5>
                        <table class="table table-dark table-hover table-bordered table-sm">
                            <thead class="table-light">
                                <th scope="col">URL</th>
                                <th scope="col">Page Type</th>
                            </thead>
                            <tbody>
                            {% for url in url_data%}
                            <tr>
                             <td>{{url.url}}</td>
                             <td>{{url.type}}</td>
                             </tr>
                            </tbody>
                            {% endfor %}
                        </table>      
                    </div>
                </div>
            </div>
            <!--Adding third section to add filtering functionality-->

        </div>    
        

        {% for item in zipped_data %}
            <h3 style="color: var(--bs-gray-100);background: #278557;" title="Test">Displaying data for form Factor: "{{ item.formFactor }}"</h4>
            <div class="row">
                {% for metric, mdata in item.zipped_metrics %}
                    <div class="col-md-4">
                        <h4 style="color: var(--bs-gray-100);background: #8392a8; font-size: 1.2em;" title="Test">{{ metric }}</h5>
                        {% for graph in zipped_graphs %}
                            {% if graph.metric == metric %}
                                {{ graph.graph | safe }}
                            {% endif %}
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>
        {% endfor %}

        <!-- New Section for Historical Crux Data visualizations -->
        <h3 style="color: var(--bs-gray-100);background: #278557;" title="Test">Displaying Historical Crux Data for URL: Metrics distribution over time</h4>
            <p class="card-text p-3 border bg-light mb-4">
                The CrUX History API gives low-latency access to six months of historical real-user experience data at  origin granularity.

            </p> 
        <div class="row">
            {% for graph in zipped_tribin_graph %}
                <div class="col-md-6">
                    <h4 style="color: rgb(0, 255, 115);background: #8392a8; font-size: 1.2em;" title="Historical Crux Data">{{ graph.metric }}</h5>
                    {{ graph.graph | safe }}                      
                </div>
            {% endfor %}
        </div>

        <!-- New Section for Historical Crux Data presenting 75th percentage distribution over time -->
        <h3 style="color: var(--bs-gray-100);background: #278557;" title="Test">Displaying Historical Crux Data for URL: 75th percentage distribution over time</h4>
            <p class="card-text p-3 border bg-light mb-4">
                Some placeholder to add intoduction section later
            </p> 
            <div class="row">
                {% for graph in p75_zipped_graph %}
                    <div class="col-md-4">
                        <h4 style="color: rgb(0, 255, 115);background: #8392a8; font-size: 1.2em;" title="Historical Crux Data">{{ graph.metric }}</h5>
                        {{ graph.graph | safe }}                      
                    </div>
                {% endfor %}
            </div>
                      


    </div>



    {% include 'footer.html' %}
    {% include 'scripts.html' %}

    <a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
</body>
{% endblock content %}