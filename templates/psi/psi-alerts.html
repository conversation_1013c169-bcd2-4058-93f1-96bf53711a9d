{% extends 'base.html' %}
{% load i18n static %}
{% load static %}
{% load psicustag %}
{% block content %}
{%include 'psi/psi-navbar.html' %}
<body id="page-top">
    <style>
        /* Add this CSS to your template and run collect static later */
        #fullSizeImage {
          cursor: pointer;
          transition: filter 0.3s ease;
        }
  
        #fullSizeImage:hover {
          filter: brightness(90%);
        }
  
        /* Tooltip Styling */
        #fullSizeImage:after {
          content: "Click to view full size";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          padding: 5px;
          background-color: #000;
          color: #fff;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: fill;
        }
  
        #fullSizeImage:hover:after {
          opacity: 1;
        }
      </style>
  <h1 style="color: var(--bs-gray-100);background: hsl(359, 90%, 46%); font-weight: bold; color: rgb(248, 248, 248);" title="Test">Alert Summary And Reporting - domain: {{domain.domain}}</h1>
  <div class="container-fluid">
    <!--This row contains some basic info and it's a top level div-->
    <div class="row">
      <div class="col-md-3">
        <div>

        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-mobile fa-1x"> Form Factor:</i>
          <p>{{selected_form_factor}}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-solid fa-clock fa-1x"> Date/Time of extraction Range:</i>
          <p>{{ data.extractionDate }}</p>
        </div>
      </div>  
      <div class="col-md-2">
        <div>
          <i class="fas fa-solid fa-film fa-1x"> Domain:</i>
          <p>{{domain.domain}}</p>
        </div>
    </div>    
  </div>

  <!--Next row represents section with screenshot, navigation and table-->
  <div class="row">
    <!--This section holds the screenshoot-->
    <div class="col-md-2">
        <div class="card border-warning  shadow h-100 py-2">
          <div class="card-body">
            <i class="fas fa-duotone fa-link fa-1x"> Snapshoot:</i>
                <img id="fullSizeImage" src="{{ snapshoot.final_snapshoot }}" style="width: 100%; height: 100%;  max-width: 250px; max-height: 450px; object-fit: cover;" class="img-thumbnail rounded mx-auto d-block" alt="Page Screenshot" > 
          </div> 
        </div>
    </div>
    <!--This is the main navigation with domain logo in it-->
    <div class="col-md-4">
      <div class="card border-warning shadow h-100 py-1">
        <div class="card-body">

        <a href="/psi/page-speed/{{domain.id}}" class="btn btn-success">Page Speed</a>
        <a href="/psi/lighthouse/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">LightHouse</a>
        <a href="/psi/domain_dash" class="btn btn-primary" style="background-color: #404040;">Other Domain</a>
        <a href="/psi/domain_dash/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">crUX</a>
        <a href="/psi/performance-report/{{domain.id}}" class="btn btn-success">Monthly Report</a>
        <hr class="mt-5 mb-5">

        <!--Actual form-->
        <form action="#"  method="POST">
            {% csrf_token %}
            {{ url_form }}
            <button type="submit" class="btn btn-primary btn-primary center" style="background-color: #404040;">SUBMIT</button>
        </form>

        <!--End of the form-->


        </div>
        </div>  
    </div>
    <!--This section holds the table-->
    <div class="col-md-6">    
      <div class="card border-warning  shadow h-100 py-2">
        <div class="card-body">
            <i class="fas fa-solid fa-film fa-2x"> List of  awailable URL's:</i>
              <table class="table table-hover table-bordered table-sm table-responsive-md" style="background-color: white">
          <thead>
              <th scope="col" style="color: black;">URL</th>
              <th scope="col" style="color: black;">Page Type</th>
          </thead>
          <tbody>
          {% for url in url_data%}
          <tr>
           <td style="color: black;">{{url.url}}</td>
           <td style="color: black;">{{url.type}}</td>
           </tr>
          </tbody>
          {% endfor %}
              </table>   
        </div>
      </div>
    <div>
    </div>
    </div> 
  </div>
  <!--This section holds the heading and metrics -->
  <div class="row">
    <h3 style="color: var(--bs-gray-100);background: #f01010;" title="Test">ALerts Overview for selected URL: "{{ url_select_form }}"</h3>
    <!--This is the place for the first graph-->
              <!--Performance Score Section-->
              <div class="row" style="height: 450x;">
                        <div class="col-8">
                          <!--COntains first ts graph-->
                          <div class="card border-primary mb-3">
                            <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                              <i class="fas fa-duotone fa-car fa-2x" style="color:white;"> Performance</i>

                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Performance Score Distribution over time</h5>
                              <p class="card-text"></p>
                              {{performance_plots|safe}}
                            </div>
                          </div>
                        </div>
                        <!--First Section on the right-->
                        <div class="col-sm-2">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Performance Contribution</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Weekly page contribution to the total Performance Score.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if difference.performance_score_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.performance_score_diff >= 0 %}+{% endif %}{{ difference.performance_score_diff }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.performance_score_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if difference.performance_score_diff <=  0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly average page contribution.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if average.performance_score_average >= 0 %}orange{% else %}green{% endif %};">{% if average.performance_score_average >= 0 %}+{% endif %}{{ average.performance_score_average }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.performance_score_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median Performance Score</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly page performance median change.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if median.performance_score_med >= 0 %}orange{% else %}green{% endif %};">{% if median.performance_score_med >= 0 %}+{% endif %}{{ median.performance_score_med }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.performance_score_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div>
                        </div>
                        <!--Second Section on the right-->
                        <div class="col-sm-2">
                          <!--Contains some alerting system placeholder-->
                          <div class="card border-primary mb-3">
                            <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 800; color: white;">Alerts</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                              <p class="card-text">No Alerts </p>
                            </div>
                          </div>
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if sdeviation.speed_index_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.speed_index_std >= 0 %}+{% endif %}{{ sdeviation.performance_score_std}}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.speed_index_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Coefficient Difference</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents how far the average values is from the mean.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if sdevdiff.performance_score_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.performance_score_coef >= 0 %}+{% endif %}{{ sdevdiff.performance_score_coef }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.performance_score_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                        </div>
              </div>
  
  <hr class="divider py-2 border-dark">


  {% include 'footer.html' %}
  {% include 'scripts.html' %}

  <a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>

  <!--aCtual script to power the modal window (TOdO) This should be exported to js file and collected by static-->
  <script>
    document.getElementById('fullSizeImage').addEventListener('click', function() {
      var fullSizeImageModal = new bootstrap.Modal(document.getElementById('fullSizeImageModal'));
      var fullSizeImageSrc = document.getElementById('fullSizeImageSrc');
      fullSizeImageSrc.src = this.src;

      fullSizeImageModal.show();

      var modalImageContent = document.getElementById("fullSizeImageModal");
      modalImageContent.addEventListener('click', function(){
             fullSizeImageModal.hide();
      }); 
    });

    //Adding convinient tooltip to display info
    var fullSizeImage = document.getElementById('fullSizeImage');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');
    fullSizeImage.setAttribute('title', 'Click on thumbnail to enlarge');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');

    var tooltip = new bootstrap.tooltip(fullSizeImage);
  </script>
</body>
{% endblock content %}