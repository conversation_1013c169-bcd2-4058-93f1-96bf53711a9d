{% extends 'base.html' %}
{% load i18n static %}
{% load static %}
{% load psicustag %}
{% block content %}
{%include 'psi/psi-navbar.html' %}
<body id="page-top">
    <style>
        /* Add this CSS to your template and run collect static later */
        #fullSizeImage {
          cursor: pointer;
          transition: filter 0.3s ease;
        }
  
        #fullSizeImage:hover {
          filter: brightness(90%);
        }
  
        /* Tooltip Styling */
        #fullSizeImage:after {
          content: "Click to view full size";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          padding: 5px;
          background-color: #000;
          color: #fff;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: fill;
        }
  
        #fullSizeImage:hover:after {
          opacity: 1;
        }
      </style>
  <h1 style="color: var(--bs-gray-100);background: #278557; font-weight: bold; color: rgb(248, 248, 248);" title="Test">Permance Report (MoM) - domain: {{domain.domain}}</h1>
  <div class="container-fluid">
    <!--This row contains some basic info and it's a top level div-->
    <div class="row">
      <div class="col-md-3">
        <div>

        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-mobile fa-1x"> Form Factor:</i>
          <p>{{selected_form_factor}}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-solid fa-clock fa-1x"> Date/Time of extraction Range:</i>
          <p>{{ data.extractionDate }}</p>
        </div>
      </div>  
      <div class="col-md-2">
        <div>
          <i class="fas fa-solid fa-film fa-1x"> Domain:</i>
          <p>{{domain.domain}}</p>
        </div>
    </div>    
  </div>

  <!--Next row represents section with screenshot, navigation and table-->
  <div class="row">
    <!--This section holds the screenshoot-->
    <div class="col-md-2">
        <div class="card border-warning  shadow h-100 py-2">
          <div class="card-body">
            <i class="fas fa-duotone fa-link fa-1x"> Snapshoot:</i>
                <img id="fullSizeImage" src="{{ snapshoot.final_snapshoot }}" style="width: 100%; height: 100%;  max-width: 250px; max-height: 450px; object-fit: cover;" class="img-thumbnail rounded mx-auto d-block" alt="Page Screenshot" > 
          </div> 
        </div>
    </div>
    <!--This is the main navigation with domain logo in it-->
    <div class="col-md-4">
      <div class="card border-warning shadow h-100 py-1">
        <div class="card-body">

        <a href="/psi/page-speed/{{domain.id}}" class="btn btn-success">Page Speed</a>
        <a href="/psi/lighthouse/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">LightHouse</a>
        <a href="/psi/domain_dash" class="btn btn-primary" style="background-color: #404040;">Other Domain</a>
        <a href="/psi/domain_dash/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">crUX</a>
        <a href="/psi/performance-report/{{domain.id}}" class="btn btn-success">Monthly Report</a>
        <hr class="mt-5 mb-5">

        <!--Actual form-->
        <form action="#"  method="POST">
            {% csrf_token %}
            {{ url_form }}
            <button type="submit" class="btn btn-primary btn-primary center" style="background-color: #404040;">SUBMIT</button>
        </form>

        <!--End of the form-->


        </div>
        </div>  
    </div>
    <!--This section holds the table-->
    <div class="col-md-6">    
      <div class="card border-warning  shadow h-100 py-2">
        <div class="card-body">
            <i class="fas fa-solid fa-film fa-2x"> List of  awailable URL's:</i>
              <table class="table table-hover table-bordered table-sm table-responsive-md" style="background-color: white">
          <thead>
              <th scope="col" style="color: black;">URL</th>
              <th scope="col" style="color: black;">Page Type</th>
          </thead>
          <tbody>
          {% for url in url_data%}
          <tr>
           <td style="color: black;">{{url.url}}</td>
           <td style="color: black;">{{url.type}}</td>
           </tr>
          </tbody>
          {% endfor %}
              </table>   
        </div>
      </div>
    <div>
    </div>
    </div> 
  </div>
  <!--This section holds the heading and metrics -->
  <div class="row">
    <h3 style="color: var(--bs-gray-100);background: #278557;" title="Test">Monthly Metric Overview for selected URL: "{{ url_select_form }}"</h3>
    <!--This is the place for the first graph-->
              <!--Performance Score Section-->
              <div class="row" style="height: 450x;">
                        <div class="col-8">
                          <!--COntains first ts graph-->
                          <div class="card border-primary mb-3">
                            <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                              <i class="fas fa-duotone fa-car fa-2x" style="color:white;"> Performance</i>

                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Performance Score Distribution over time</h5>
                              <p class="card-text"></p>
                              {{performance_plots|safe}}
                            </div>
                          </div>
                        </div>
                        <!--First Section on the right-->
                        <div class="col-sm-2">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Performance Contribution</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Weekly page contribution to the total Performance Score.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if difference.performance_score_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.performance_score_diff >= 0 %}+{% endif %}{{ difference.performance_score_diff }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.performance_score_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if difference.performance_score_diff <=  0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly average page contribution.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if average.performance_score_average >= 0 %}orange{% else %}green{% endif %};">{% if average.performance_score_average >= 0 %}+{% endif %}{{ average.performance_score_average }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.performance_score_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median Performance Score</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly page performance median change.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if median.performance_score_med >= 0 %}orange{% else %}green{% endif %};">{% if median.performance_score_med >= 0 %}+{% endif %}{{ median.performance_score_med }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.performance_score_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div>
                        </div>
                        <!--Second Section on the right-->
                        <div class="col-sm-2">
                          <!--Contains some alerting system placeholder-->
                          <div class="card border-primary mb-3">
                            <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 800; color: white;">Alerts</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                              <p class="card-text">No Alerts </p>
                            </div>
                          </div>
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if sdeviation.speed_index_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.speed_index_std >= 0 %}+{% endif %}{{ sdeviation.performance_score_std}}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.speed_index_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Coefficient Difference</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents how far the average values is from the mean.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if sdevdiff.performance_score_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.performance_score_coef >= 0 %}+{% endif %}{{ sdevdiff.performance_score_coef }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.performance_score_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                        </div>
              </div>
  
  <hr class="divider py-2 border-dark">


 <!--Page Speed Section-->
        <div class="row" style="height: 450x;">
                  <!--First Graph on the left side-->
                  <div class="col-8">
                    <!--COntains first ts graph-->
                    <div class="card border-primary mb-3">
                      <div class="card-header" style="height: 3em; background-color:#6eb589; font-weight: 600;">
                        <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> Page Speed</i>

                      </div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Page Speed Score Distribution over time</h5>
                        <p class="card-text"></p>
                        {{ps_plots|safe}}
                      </div>
                    </div>
                  </div>
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Page Speed Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total Page Speed.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.speed_index_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.speed_index_diff >= 0 %}+{% endif %}{{ difference.speed_index_diff }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.speed_index_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.speed_index_average >= 0 %}orange{% else %}green{% endif %};">{% if average.speed_index_average >= 0 %}+{% endif %}{{ average.speed_index_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.speed_index_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median Page Speed </div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly page speed median change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if median.speed_index_med >= 0 %}orange{% else %}green{% endif %};">{% if median.speed_index_med >= 0 %}+{% endif %}{{ median.speed_index_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.speed_index_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div>
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                          <!--Contains some alerting system placeholder-->
                          <div class="card border-primary mb-3">
                            <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                              <p class="card-text">No Alerts </p>
                            </div>
                          </div>
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if sdeviation.speed_index_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.speed_index_std >= 0 %}+{% endif %}{{ sdeviation.speed_index_std}}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.speed_index_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Coefficient Difference</div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">Represents ratio standard dev vs mean.</h5>
                              <p class="card-text" style="font-size: 30px;">
                                <span class="text-center" style="color: {% if sdevdiff.speed_index_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.speed_index_coef >= 0 %}+{% endif %}{{ sdevdiff.speed_index_coef }}%</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.speed_index_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                                  <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                                </svg>                    
                              </p>
                            </div>
                          </div> 
                  </div>                  

        </div>
<hr class="divider py-2 border-dark">


  <!--LCP Section graph-->
          <div class="row" style="height: 450x;">
                  <!--LCP graph on the left side-->
                  <div class="col-8">
                    <!--COntains first ts graph-->
                    <div class="card border-primary mb-3">
                      <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                        <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> LCP</i>

                      </div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">LCP Score Distribution over time</h5>
                        <p class="card-text"></p>
                        {{lcp_plot|safe}}
                      </div>
                    </div>
                  </div>
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly LCP Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total LCP.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.largest_contentful_paint_diff  >= 0 %}green{% else %}green{% endif %};">{% if difference.largest_contentful_paint_diff  >= 0 %}+{% endif %}{{ difference.largest_contentful_paint_diff  }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.largest_contentful_paint_diff  >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution to LCP.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.largest_contentful_paint_average >= 0 %}orange{% else %}green{% endif %};">{% if average.largest_contentful_paint_average >= 0 %}+{% endif %}{{ average.largest_contentful_paint_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.largest_contentful_paint_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median LCP</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly Largest Contentful Paint median change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if median.largest_contentful_paint_med >= 0 %}orange{% else %}green{% endif %};">{% if median.largest_contentful_paint_med >= 0 %}+{% endif %}{{ median.largest_contentful_paint_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.largest_contentful_paint_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div>  
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                    <!--Contains some alerting system placeholder-->
                    <div class="card border-primary mb-3">
                      <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                        <p class="card-text">No Alerts </p>
                      </div>
                    </div>
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdeviation.first_contenful_paint_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.first_contenful_paint_std >= 0 %}+{% endif %}{{ sdeviation.first_contenful_paint_std }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.first_contenful_paint_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Coefficient Difference</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly form factor contribution to the LCP.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdevdiff.largest_contentful_paint_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.largest_contentful_paint_coef  >= 0 %}+{% endif %}{{ sdevdiff.largest_contentful_paint_coef  }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.largest_contentful_paint_coef  >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                   </div>                                    
                  
          </div>

<hr class="divider py-2 border-dark">
        <!--FCP Section graph section-->
        <div class="row" style="height: 450x;">
                  <!--COntains fcp graph on the left side-->
                  <div class="col-8">
                    <div class="card border-primary mb-3">
                      <div class="card-header" style="height: 3em; background-color:#6eb589; font-weight: 600;">
                        <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> FCP</i>
                      </div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">FCP Score Distribution over time</h5>
                        <p class="card-text"></p>
                        {{fcp_plot|safe}}
                      </div>
                    </div>
                  </div>
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly FCP Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total FCP.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.first_contenful_paint_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.first_contenful_paint_diff >= 0 %}+{% endif %}{{ difference.first_contenful_paint_diff }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.first_contenful_paint_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution to FCP.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.first_contenful_paint_average >= 0 %}orange{% else %}green{% endif %};">{% if average.first_contenful_paint_average >= 0 %}+{% endif %}{{ average.first_contenful_paint_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.first_contenful_paint_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median FCP</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly First Contentful Paint change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if median.first_contentful_paint_med >= 0 %}orange{% else %}green{% endif %};">{% if median.first_contentful_paint_med >= 0 %}+{% endif %}{{ median.first_contentful_paint_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.first_contentful_paint_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div>   
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                    <!--Contains some alerting system placeholder-->
                    <div class="card border-primary mb-3">
                      <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                        <p class="card-text">No Alerts </p>
                      </div>
                    </div>
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdeviation.largest_contentful_paint_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.largest_contentful_paint_std >= 0 %}+{% endif %}{{ sdeviation.largest_contentful_paint_std }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.largest_contentful_paint_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Coefficient Difference</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly form factor contribution to the FCP.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdevdiff.first_contentful_paint_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.first_contentful_paint_coef >= 0 %}+{% endif %}{{ sdevdiff.first_contentful_paint_coef }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.first_contentful_paint_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                   </div>                                     

        </div>
        <hr class="divider py-2 border-dark">


<!--Total Blocking Time section-->
        <div class="row" style="height: 450x;">
                    <!--Contains Total blocking time graph on the left side-->
                    <div class="col-8">
                      <div class="card border-primary mb-3">
                        <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                          <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> TBT</i>
                        </div>
                        <div class="card-body text-primary">
                          <h5 class="card-title">Total Blocking Time Score Distribution over time</h5>
                          <p class="card-text"></p>
                          {{tbt_plot|safe}}
                        </div>
                      </div>
                    </div>
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly TBT Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total TBT.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.total_blocking_time_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.total_blocking_time_diff >= 0 %}+{% endif %}{{ difference.total_blocking_time_diff }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.total_blocking_time_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution to TBT.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.total_blocking_time_average >= 0 %}orange{% else %}green{% endif %};">{% if average.total_blocking_time_average >= 0 %}+{% endif %}{{ average.total_blocking_time_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.total_blocking_time_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median TBT</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly Total Blocking Time median change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if avg_performance_value >= 0 %}orange{% else %}green{% endif %};">{% if avg_performance_value >= 0 %}+{% endif %}{{ median.total_blocking_time_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if avg_performance_value >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div>  
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                    <!--Contains some alerting system placeholder-->
                    <div class="card border-primary mb-3">
                      <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                        <p class="card-text">No Alerts </p>
                      </div>
                    </div>
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdeviation.total_blocking_time_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.total_blocking_time_std >= 0 %}+{% endif %}{{ sdeviation.total_blocking_time_std }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.total_blocking_time_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">TBT Coefficient Difference</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly form factor contribution to the TBT.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdevdiff.total_blocking_time_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.total_blocking_time_coef >= 0 %}+{% endif %}{{ sdevdiff.total_blocking_time_coef }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.total_blocking_time_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                   </div>                                          
        </div>
<hr class="divider py-2 border-dark">


<!--Cumulative Layout Shift section-->
            <div class="row" style="height: 450x;">
                      <!--Containf Cumulative layout Shift graph on the left side-->
                      <div class="col-8">
                        <div class="card border-primary mb-3">
                          <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                            <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> CLS</i>
                          </div>
                          <div class="card-body text-primary">
                            <h5 class="card-title">Cumulative Layout Shift Score Distribution over time</h5>
                            <p class="card-text"></p>
                            {{cls_plot|safe}}
                          </div>
                        </div>
                      </div>
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly CLS Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total CLS.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.cumulative_layout_shift_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.cumulative_layout_shift_diff >= 0 %}+{% endif %}{{ difference.cumulative_layout_shift_diff }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.cumulative_layout_shift_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution to CLS.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.cumulative_layout_shift_average >= 0 %}orange{% else %}green{% endif %};">{% if average.cumulative_layout_shift_average >= 0 %}+{% endif %}{{ average.cumulative_layout_shift_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.cumulative_layout_shift_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median CLS</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly Cumulative Layout Shift median change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if median.cumulative_layout_shift_med >= 0 %}orange{% else %}green{% endif %};">{% if median.cumulative_layout_shift_med >= 0 %}+{% endif %}{{ median.cumulative_layout_shift_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.cumulative_layout_shift_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div> 
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                    <!--Contains some alerting system placeholder-->
                    <div class="card border-primary mb-3">
                      <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                        <p class="card-text">No Alerts </p>
                      </div>
                    </div>
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdeviation.cumulative_layout_shift_std >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.cumulative_layout_shift_std >= 0 %}+{% endif %}{{ sdeviation.cumulative_layout_shift_std }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.cumulative_layout_shift_std >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">CLS Coefficient Difference</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly form factor contribution to the CLS.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdevdiff.cumulative_layout_shift_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.cumulative_layout_shift_coef >= 0 %}+{% endif %}{{ sdevdiff.cumulative_layout_shift_coef }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.cumulative_layout_shift_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                   </div>                     
                  
            </div>
<hr class="divider py-2 border-dark">


<!--Server Response Time section-->
            <div class="row" style="height: 450x;">
                      <!--Contains Server Response time graph on the left side-->
                      <div class="col-8">
                        <div class="card border-primary mb-3">
                          <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                            <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> Server Response time</i>
                          </div>
                          <div class="card-body text-primary">
                            <h5 class="card-title">Server Response Time Score Distribution over time</h5>
                            <p class="card-text"></p>
                            {{srt_plot|safe}}
                          </div>
                        </div>
                      </div>  
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly SRT Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total Server response time.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.server_response_time_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.server_response_time_diff  >= 0 %}+{% endif %}{{ difference.server_response_time_diff }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.server_response_time_diff  >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution to SRT.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.server_response_time_average >= 0 %}orange{% else %}green{% endif %};">{% if average.server_response_time_average >= 0 %}+{% endif %}{{ average.server_response_time_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.server_response_time_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median SRT</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly Server Response Time median change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if median.server_response_time_med >= 0 %}orange{% else %}green{% endif %};">{% if median.server_response_time_med >= 0 %}+{% endif %}{{ median.server_response_time_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.server_response_time_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div>      
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                    <!--Contains some alerting system placeholder-->
                    <div class="card border-primary mb-3">
                      <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                        <p class="card-text">No Alerts </p>
                      </div>
                    </div>
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly standard deviation change for a given url</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdeviation.server_response_time_std  >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.server_response_time_std  >= 0 %}+{% endif %}{{ sdeviation.server_response_time_std }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.server_response_time_std  >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">SRT Coefficient Difference</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly form factor contribution to the SRT.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdevdiff.server_response_time_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.server_response_time_coef >= 0 %}+{% endif %}{{ sdevdiff.server_response_time_coef }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.server_response_time_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                   </div>                                                    
            </div>
<hr class="divider py-2 border-dark">
<!--Server Network Latency section-->
            <div class="row" style="height: 450x;">
                      <!--Contains Server Network Latency graph on the left side-->
                      <div class="col-8">
                        <div class="card border-primary mb-3">
                          <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 600;">
                            <i class="fas fa-duotone fa-car fa-2x" style="color:whitesmoke;"> Server Network Latency</i>
                          </div>
                          <div class="card-body text-primary">
                            <h5 class="card-title">Server Network Latency Score Distribution over time</h5>
                            <p class="card-text"></p>
                            {{snl_plot|safe}}
                          </div>
                        </div>
                      </div>      
                  <!--First Section on the right-->
                  <div class="col-sm-2">
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly SNL Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Weekly page contribution to the total Server Network Latency.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if difference.server_network_latency_diff >= 0 %}green{% else %}green{% endif %};">{% if difference.server_network_latency_diff >= 0 %}+{% endif %}{{ difference.server_network_latency_diff }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if difference.server_network_latency_diff >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Weekly Average Change Contribution</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly average page contribution to SNL.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if average.server_network_latency_average >= 0 %}orange{% else %}green{% endif %};">{% if average.server_network_latency_average >= 0 %}+{% endif %}{{ average.server_network_latency_average }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if average.server_network_latency_average >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Median SNL</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly Server Network Latency median change.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if median.server_network_latency_med >= 0 %}orange{% else %}green{% endif %};">{% if median.server_network_latency_med >= 0 %}+{% endif %}{{ median.server_network_latency_med }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if median.server_network_latency_med >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div>
                  </div>    
                  <!--Second Section on the right-->
                  <div class="col-sm-2">
                    <!--Contains some alerting system placeholder-->
                    <div class="card border-primary mb-3">
                      <div class="card-header"  style="height: 3em; background-color: #ff5f5f; font-weight: 600; color: white;">Alerts</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Currently all values are within the acceptable range of values.</h5>
                        <p class="card-text">No Alerts </p>
                      </div>
                    </div>
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Standard deviation</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly standard deviation change for a given url.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdeviation.server_network_latency_std  >= 0 %}orange{% else %}green{% endif %};">{% if sdeviation.server_network_latency_std  >= 0 %}+{% endif %}{{ sdeviation.server_network_latency_std }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdeviation.server_network_latency_std  >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                    <div class="card border-primary mb-3 text-center">
                      <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">SNL Coefficient Difference</div>
                      <div class="card-body text-primary">
                        <h5 class="card-title">Represents weekly form factor contribution to the SNL.</h5>
                        <p class="card-text" style="font-size: 30px;">
                          <span class="text-center" style="color: {% if sdevdiff.server_network_latency_coef >= 0 %}orange{% else %}green{% endif %};">{% if sdevdiff.server_network_latency_coef >= 0 %}+{% endif %}{{ sdevdiff.server_network_latency_coef }}%</span>
                          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="currentColor" class="bi bi-arrow-{% if sdevdiff.server_network_latency_coef >= 0 %}up{% else %}down{% endif %}" viewBox="0 0 16 16" style="color: {% if value >= 0 %}orange{% else %}green{% endif %};">
                            <path d="M8 16a.5.5 0 0 0 .5-.5V2.707l2.646 2.647a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V15.5a.5.5 0 0 0 .5.5z" stroke="currentColor" stroke-width="0.3"/> 
                          </svg>                    
                        </p>
                      </div>
                    </div> 
                   </div>                                               
            </div>


    <!-- Bootstrap modal to display full size image on click -->
    <div class="modal fade" id="fullSizeImageModal" tabindex="-1" role="dialog" aria-labelledby="fullSizeImageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="fullSizeImageModalLabel">{{domain.domain}}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <img id="fullSizeImageSrc" class="img-fluid" alt="Full-size Screenshot for {{domain.domain}}">
            </div>
          </div>
        </div>
      </div>

    <!--Edd of the modal section-->  


  {% include 'footer.html' %}
  {% include 'scripts.html' %}

  <a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>

  <!--aCtual script to power the modal window (TOdO) This should be exported to js file and collected by static-->
  <script>
    document.getElementById('fullSizeImage').addEventListener('click', function() {
      var fullSizeImageModal = new bootstrap.Modal(document.getElementById('fullSizeImageModal'));
      var fullSizeImageSrc = document.getElementById('fullSizeImageSrc');
      fullSizeImageSrc.src = this.src;

      fullSizeImageModal.show();

      var modalImageContent = document.getElementById("fullSizeImageModal");
      modalImageContent.addEventListener('click', function(){
             fullSizeImageModal.hide();
      }); 
    });

    //Adding convinient tooltip to display info
    var fullSizeImage = document.getElementById('fullSizeImage');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');
    fullSizeImage.setAttribute('title', 'Click on thumbnail to enlarge');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');

    var tooltip = new bootstrap.tooltip(fullSizeImage);
  </script>
</body>
{% endblock content %}

