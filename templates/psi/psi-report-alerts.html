{% extends 'base.html' %}
{% load static %}
{% load psicustag %}
{% block content %}
{%include 'psi/psi-navbar.html' %}
<body id="page-top">
    <style>
        /* Add this CSS to your template and run collect static later */
        #fullSizeImage {
          cursor: pointer;
          transition: filter 0.3s ease;
        }
  
        #fullSizeImage:hover {
          filter: brightness(90%);
        }
  
        /* Tooltip Styling */
        #fullSizeImage:after {
          content: "Click to view full size";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          padding: 5px;
          background-color: #000;
          color: #fff;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: fill;
        }
  
        #fullSizeImage:hover:after {
          opacity: 1;
        }
      </style>
  <h1 style="color: var(--bs-gray-100);background: hsl(359, 90%, 46%); font-weight: bold; color: rgb(248, 248, 248);" title="Test">Alert Summary And Reporting - domain: {{domain.domain}}</h1>
  <div class="container-fluid">
    <!--This row contains some basic info and it's a top level div-->
    <div class="row">
      <div class="col-md-3">
        <div>

        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-mobile fa-1x"> Form Factor:</i>
          <p>{{selected_form_factor}}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-solid fa-clock fa-1x"> Date/Time of extraction Range:</i>
          <p>{{ data.extractionDate }}</p>
        </div>
      </div>  
      <div class="col-md-2">
        <div>
          <i class="fas fa-solid fa-film fa-1x"> Domain:</i>
          <p>{{domain.domain}}</p>
        </div>
    </div>    
  </div>

  <!--Next row represents section with screenshot, navigation and table-->
  <div class="row">
    <!--This section holds the screenshoot-->

    <!--This is the main navigation with domain logo in it-->
    <div class="col-md-6">
      <div class="card border-warning shadow h-100 py-1">
        <div class="card-body d-flex flex-wrap justify-content-center gap-2">
          
          <a href="/psi/page-speed/{{domain.id}}" class="btn btn-success">Page Speed</a>
          <a href="/psi/lighthouse/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">LightHouse</a>
          <a href="/psi/domain_dash" class="btn btn-primary" style="background-color: #404040;">Other Domain</a>
          <a href="/psi/domain_dash/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">crUX</a>
          <a href="/psi/performance-report/{{domain.id}}" class="btn btn-success">Monthly Report</a>
    
          <hr class="mt-5 mb-5 w-100">
    
          <!--Actual form-->
          <form action="#" method="POST" class="w-100 text-center">
            {% csrf_token %}
            {{ url_form }}
            <button type="submit" class="btn btn-primary" style="background-color: #404040;">SUBMIT</button>
          </form>
    
        </div>
      </div>  
    </div>
    <!--This section holds the table-->
    <div class="col-md-6">
      <div class="card border-warning shadow h-100 py-2">
        <div class="row">
          <!-- Card 1 -->
          <div class="col-sm-4">
            <div class="card border-primary mb-3 text-center">
              <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Status</div>
              <div class="card-body text-primary">
                <h5 class="card-title">Optimized</h5>
              </div>
            </div>
          </div>
          <!-- Card 2 -->
          <div class="col-sm-4">
            <div class="card border-primary mb-3 text-center">
              <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Status</div>
              <div class="card-body text-primary">
                <h5 class="card-title">Optimized</h5>
              </div>
            </div>
          </div>
          <!-- Card 3 -->
          <div class="col-sm-4">
            <div class="card border-primary mb-3 text-center">
              <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Stability</div>
              <div class="card-body text-primary">
                <h5 class="card-title">Unstable</h5>
              </div>
            </div>
          </div>
          <!-- Card 4 -->
          <div class="col-sm-4">
            <div class="card border-primary mb-3 text-center">
              <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Performance</div>
              <div class="card-body text-primary">
                <h5 class="card-title">Good</h5>
              </div>
            </div>
          </div>
          <!-- Card 5 -->
          <div class="col-sm-4">
            <div class="card border-primary mb-3 text-center">
              <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Availability</div>
              <div class="card-body text-primary">
                <h5 class="card-title">Stable</h5>
              </div>
            </div>
          </div>
          <!-- Card 6 -->
          <div class="col-sm-4">
            <div class="card border-primary mb-3 text-center">
              <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">Connectivity</div>
              <div class="card-body text-primary">
                <h5 class="card-title">Weak</h5>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  
  <!--This section holds the heading and metrics -->
  <div class="row">
    <h3 style="color: var(--bs-gray-100);background: #050010;" title="Test">Weekly Rolling Average Overview for selected URL: "{{ url_select_form }}"</h3>
    <!--This is the place for the first graph-->
              <!--Performance Score Section-->
              {% for metric, metric_data in data.items %}
              <div class="row" style="height: 450px;">
                <!-- Leva kolona: Prikaz podataka o metriki -->
                <div class="col-8">
                  <div class="card border-primary mb-3">
                    <div class="card-header" style="height: 3em; background-color: #6eb589; font-weight: 800;">
                      <i class="fas fa-duotone fa-car fa-2x" style="color:white;">
                        {{ metric }} / {{ metric_data.entries.0.extraction_date }}
                      </i>
                    </div>
                    <div class="card-body text-primary">
                      <div class="row">
                        <!-- Status -->
                        <div class="col-sm-4">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                              Status
                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">{{ metric_data.entries.0.status }}</h5>
                            </div>
                          </div>
                        </div>
                        <!-- Last Measurement -->
                        <div class="col-sm-4">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                              Last Measurement
                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">{{ metric_data.entries.0.last_value }}</h5>
                            </div>
                          </div>
                        </div>
                        <!-- Stability Index -->
                        <div class="col-sm-4">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                              Stability Index
                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">{{ metric_data.entries.0.stability_index }}</h5>
                            </div>
                          </div>
                        </div>
                        <!-- Last Measurement Zone -->
                        <div class="col-sm-4">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                              Last Measurement Zone
                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">{{ metric_data.entries.0.last_zone }}</h5>
                            </div>
                          </div>
                        </div>
                        <!-- Zone Change -->
                        <div class="col-sm-4">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                              Zone Change
                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">{{ metric_data.entries.0.zone_change }}</h5>
                            </div>
                          </div>
                        </div>
                        <!-- Previous Zone -->
                        <div class="col-sm-4">
                          <div class="card border-primary mb-3 text-center">
                            <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                              Previous Zone
                            </div>
                            <div class="card-body text-primary">
                              <h5 class="card-title">{{ metric_data.entries.0.prev_zone }}</h5>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Desna kolona: Grafikon za Weekly Difference Change -->
                <div class="col-sm-4">
                  <div class="card border-primary mb-3 text-center">
                    <div class="card-header" style="height: 3em; background-color: #86c09c; font-weight: 600; color: white;">
                      Weekly Difference Change
                    </div>
                    <div class="card-body text-primary">
                      {{ metric_data.bar_plot|safe}}

                    </div>
                  </div> 
                </div>
              </div>
            {% endfor %}
            
  
  <hr class="divider py-2 border-dark">


  {% include 'footer.html' %}
  {% include 'scripts.html' %}

  <a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>

  <!--aCtual script to power the modal window (TOdO) This should be exported to js file and collected by static-->
  <script>
    document.getElementById('fullSizeImage').addEventListener('click', function() {
      var fullSizeImageModal = new bootstrap.Modal(document.getElementById('fullSizeImageModal'));
      var fullSizeImageSrc = document.getElementById('fullSizeImageSrc');
      fullSizeImageSrc.src = this.src;

      fullSizeImageModal.show();

      var modalImageContent = document.getElementById("fullSizeImageModal");
      modalImageContent.addEventListener('click', function(){
             fullSizeImageModal.hide();
      }); 
    });

    //Adding convinient tooltip to display info
    var fullSizeImage = document.getElementById('fullSizeImage');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');
    fullSizeImage.setAttribute('title', 'Click on thumbnail to enlarge');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');

    var tooltip = new bootstrap.tooltip(fullSizeImage);
  </script>
</body>
{% endblock content %}