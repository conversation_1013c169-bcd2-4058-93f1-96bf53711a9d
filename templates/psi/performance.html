{% extends 'base.html' %}
{% load i18n static %}
{% load static %}
{% load psicustag %}
{% block content %}
{%include 'psi/psi-navbar.html' %}
<body id="page-top">
  <style>
    /* Add this CSS to your template */
    #fullSizeImage {
      cursor: pointer;
      transition: filter 0.3s ease;
    }

    #fullSizeImage:hover {
      filter: brightness(90%);
    }

    /* Tooltip Styling */
    #fullSizeImage:after {
      content: "Click to view full size";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      padding: 5px;
      background-color: #000;
      color: #fff;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    #fullSizeImage:hover:after {
      opacity: 1;
    }
  </style>
  <h1 style="color: var(--bs-gray-100);background: #278557; font-weight: bold; color: rgb(245, 245, 248);" title="Test">Performance - domain: {{domain.domain}}</h1>
  <div class="container-fluid">
    <!--This row contains some basic info and it's a top level div-->
    <div class="row">
      <div class="col-md-3">
        <div>
          <i class="fas fa-duotone fa-link fa-1x"> Selected Url:</i>
          <p class="text-truncate">
            <a href="{{ url_select_form }}" target="_blank">{{ url_select_form }}</a>
          </p>
        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-mobile fa-1x"> Form Factor:</i>
          <p>{{selected_form_factor}}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div>
          <i class="fas fa-solid fa-clock fa-1x"> Date/Time of extraction:</i>
          <p>{{ data.extractionDate }}</p>
        </div>
      </div>  
      <div class="col-md-2">
        <div>
          <i class="fas fa-solid fa-film fa-1x"> Domain:</i>
          <p>{{domain.domain}}</p>
        </div>
    </div>    
  </div>

  <!--Next row represents section with screenshot, navigation and table-->
  <div class="row">
    <!--This section holds the screenshoot-->
    <div class="col-md-2">
        <div class="card border-warning  shadow h-100 py-2">
          <div class="card-body">
            <img id="fullSizeImage" src="{{ snapshoot.final_snapshoot }}" style="width: 100%; height: 100%;  max-width: 250px; max-height: 450px; object-fit: cover;" class="img-thumbnail rounded mx-auto d-block" alt="Page Screenshot" > 
          </div> 
        </div>
    </div>
    <!--This is the main navigation with domain logo in it-->
    <div class="col-md-4">
      <div class="card border-warning shadow h-100 py-1">
        <div class="card-body">
        <a href="/psi/page-speed/{{domain.id}}" class="btn btn-success">Page Speed</a>
        <a href="/psi/lighthouse/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">LightHouse</a>
        <a href="/psi/domain_dash" class="btn btn-primary" style="background-color: #404040;">Other Domain</a>
        <a href="/psi/domain_dash/{{domain.id}}" class="btn btn-primary" style="background-color: #404040;">crUX</a>
        <a href="/psi/performance-report/{{domain.id}}" class="btn btn-success">Monthly Report</a>
        <hr class="mt-5 mb-5">
        <form action="#"  method="POST">
            {% csrf_token %}
            {{ url_form }}
            <button type="submit" class="btn btn-primary btn-primary center" style="background-color: #21714A;">SUBMIT</button>
        </form>
          <i class="fas fa-duotone fa-link fa-1x"> Domain:</i>
          <img src="{{ domain.logo.url }}" class="card-img-top" alt="{{ domain.domain }}" title="{{ domain.domain }}"> 
        </div>
        </div>  
    </div>
    <!--This section holds the table-->
    <div class="col-md-6">    
      <div class="card border-warning  shadow h-100 py-2">
        <div class="card-body">
              <table class="table table-dark table-hover table-bordered table-sm">
          <thead class="table-light">
              <th scope="col">URL</th>
              <th scope="col">Page Type</th>
          </thead>
          <tbody>
          {% for url in url_data%}
          <tr>
           <td>{{url.url}}</td>
           <td>{{url.type}}</td>
           </tr>
          </tbody>
          {% endfor %}
              </table>   
        </div>
      </div>
    <div>
    </div>
    </div> 
  </div>
  <!--This section holds the heading and metrics -->
  <div class="row">
    <h3 style="color: var(--bs-gray-100);background: #278557;" title="Test">Metric Overview for selected URL: "{{ url_select_form }}"</h3>
    <!--This is the first element in the row aka Performance Score-->
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div style="width: 8em; height: 8em; border-radius: 50%;
            {% if data.performance_score <= 49 %} background-color: red;
            {% elif data.performance_score <= 89 %} background-color: orange;
            {% else %} background-color: green; {% endif %}; text-align: center;">
    <p style="font-size:  5em; color: white; margin: 0;">{{ data.performance_score }}</p>
</div>
          </div>
          <div class="col-md-8" >
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-trophy"> Performance Score</i></h5>
              <p class="card-text">The performance Score is a weigted average of the metric scores. Bad is everything below 50, average is between 50 and 90 a,d 
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
              
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="d-flex col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div class="display:flex;">
              {% if data.first_contentful_paint|divide:1000 > 0 and data.first_contentful_paint|divide:1000 < 1.8 %} 
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: green; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">{{data.first_contentful_paint|divide:1000|floatformat:1}} </p></div>
              {% elif data.first_contentful_paint|divide:1000 > 1.8 and data.first_contentful_paint|divide:1000 < 3 %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: orange; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.first_contentful_paint|divide:1000|floatformat:1}} </p></div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: red; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.first_contentful_paint|divide:1000|floatformat:1}} </p></div>
              {% endif %}
            </div>    
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car">  First Contentful Paint</i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div>  
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div>
              {% if data.largest_contentful_paint|divide:1000 > 0 and data.largest_contentful_paint|divide:1000 < 2.5 %} 
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: green; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">{{data.largest_contentful_paint|divide:1000|floatformat:1}} </p></div>
              {% elif data.largest_contentful_paint|divide:1000 > 2.5 and data.largest_contentful_paint|divide:1000 < 4 %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: orange; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.largest_contentful_paint|divide:1000|floatformat:1}} </p></div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: red; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.largest_contentful_paint|divide:1000|floatformat:1}} </p></div>
              {% endif %}
            </div>      
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car">  Largest Contentful Paint</i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div>  
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div>
              {% if data.server_response_time|divide:1000 > 0 and data.total_blocking_time|divide:100 < 1000 %} 
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: green; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">{{data.total_blocking_time|divide:10}} </p></div>
              {% elif data.total_blocking_time|divide:1000 > 100 and data.total_blocking_time|divide:1000 < 200 %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: orange; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.total_blocking_time|divide:1000|floatformat:1}} </p>
              </div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: red; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.total_blocking_time}} </p></div>
              {% endif %}
            </div>         
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car">  Total Blocking Time</i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div> 
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div>
              {% if data.speed_index|divide:1000 > 0 and data.speed_index|divide:1000 < 3.4 %} 
              <div style="width: 8em; height: 8em; border-radius: 50%; background-color: green; text-align: center;">
                <p style="font-size:  5em; color: white; margin: 0;">{{data.speed_index|divide:1000|floatformat:1}} </p></div>
              {% elif data.speed_index|divide:1000 > 3.4 and data.speed_index|divide:1000 < 5.8 %}
              <div style="width: 8em; height: 8em; border-radius: 50%; background-color: orange; text-align: center;">
                <p style="font-size:  5em; color: white; margin: 0;">  {{data.speed_index|divide:1000|floatformat:1}} </p></div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 50%; background-color: red; text-align: center;">
                <p style="font-size:  5em; color: white; margin: 0;">  {{data.speed_index|divide:1000|floatformat:1}} </p></div>
              {% endif %}
            </div>           
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car">  Speed Index</i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div>  
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div>
              {% if data.cumulative_layout_shift|divide:1000 > 0 and data.cumulative_layout_shift|divide:1000 < 0.1 %} 
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: green; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">{{data.total_blocking_time|divide:1000000|floatformat:3}} </p></div>
              {% elif data.cumulative_layout_shift|divide:1000 > 0.1 and data.cumulative_layout_shift|divide:1000 < 0.25 %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: orange; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.cumulative_layout_shift|divide:1000|floatformat:1}} </p></div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: red; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.cumulative_layout_shift|divide:1000|floatformat:1}} </p></div>
              {% endif %}
            </div>           
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car"> Cumulative Layout Shift </i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div> 
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div>
              {% if data.server_response_time|divide:1000 > 0 and data.server_response_time|divide:1000 < 100 %} 
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: green; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">{{data.server_response_time}} </p></div>
              {% elif data.server_response_time|divide:1000 > 100 and data.server_response_time|divide:1000 < 200 %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: orange; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.server_response_time|divide:1000|floatformat:1}} </p>
              </div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: red; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.server_response_time}} </p></div>
              {% endif %}
            </div>           
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car"> Server Response Time</i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div>  
    <div class="col-md-3">
      <div class="card mb-3" style="max-width: 540px; border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
        <div class="row g-0">
          <div class="col-md-4" style="border: 1px solid orange; display: flex; justify-content: center; align-items: center;">
            <div>
              {% if data.server_response_time|divide:1000 > 0 and data.server_response_time|divide:1000 < 100 %} 
              <div style="width: 8em; height: 8em; border-radius: 10%; background-color: green; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">{{data.server_response_time}} </p></div>
              {% elif data.server_response_time|divide:1000 > 100 and data.server_response_time|divide:1000 < 200 %}
              <div style="width: 8em; height: 8em; border-radius: 50%; background-color: orange; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.server_response_time|divide:1000|floatformat:1}} </p></div>
              {% else %}
              <div style="width: 8em; height: 8em; border-radius: 50%; background-color: red; display: flex; justify-content: center; align-items: center;">
                <p style="font-size:  2.5em; color: white; margin: 0;">  {{data.server_network_latency}} </p></div>
              {% endif %}
            </div>           
          </div>
          <div class="col-md-8">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-solid fa-car"> Server Network Latency</i></h5>
              <p class="card-text">The performance Score is a weighted average of the metric scores. Bad is everything below 50, average is between 50 and 90, and
                good score is everything above 90.
              </p>
              <p class="card-text"><small class="text-muted"></small></p>
            </div>
          </div>
        </div>
      </div>
    </div> 
  </div>


  {% include 'footer.html' %}
  {% include 'scripts.html' %}

  <a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>


      <!-- Modal for Full-size Image -->
      <div class="modal fade" id="fullSizeImageModal" tabindex="-1" role="dialog" aria-labelledby="fullSizeImageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="fullSizeImageModalLabel">{{ url_select_form }} Snapshoot</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <img id="fullSizeImageSrc" class="img-fluid" alt="{{ url_select_form }} Full Size SNapshoot">
            </div>
          </div>
        </div>
      </div>
  <script>
    document.getElementById('fullSizeImage').addEventListener('click', function() {
      var fullSizeImageModal = new bootstrap.Modal(document.getElementById('fullSizeImageModal'));
      var fullSizeImageSrc = document.getElementById('fullSizeImageSrc');
      fullSizeImageSrc.src = this.src;

      fullSizeImageModal.show();

      var modalImageContent = document.getElementById("fullSizeImageModal");
      modalImageContent.addEventListener('click', function(){
             fullSizeImageModal.hide();
      }); 
    });

    //Adding convinient tooltip to display info
    var fullSizeImage = document.getElementById('fullSizeImage');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');
    fullSizeImage.setAttribute('title', 'Click on thumbnail to enlarge');
    fullSizeImage.setAttribute('data-bs-toggle', 'tooltip');
    fullSizeImage.setAttribute('alt', 'Click anywhere on image to close');

    var tooltip = new bootstrap.tooltip(fullSizeImage);
  </script>
</body>
{% endblock content %}