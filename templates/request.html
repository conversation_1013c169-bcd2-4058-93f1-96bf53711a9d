{% extends 'base.html' %}
{% load i18n static %}

{% block content %}

<body class="bg-gradient-primary" style="background: url({% static '/assets/img/bg.jpg' %}); background-size: cover; background-repeat: no-repeat; height: 100vh; display: flex; align-items: center; justify-content: center;">
    <div class="container" style="display: flex; align-items: center; justify-content: center; height: 100vh;">
        <!-- Start: Request Access Form -->
        <form method="post" action="{% url 'request' %}" style="font-family: Quicksand, sans-serif; background-color: rgba(44, 40, 52, 0.73); width: 100%; max-width: 600px; padding: 40px; border-radius: 8px; box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);">
            {% csrf_token %}
            <h1 style="color: rgb(193, 166, 83); text-align: center;">Request Access</h1>
            
            <!-- Logo -->
            <div>
                <img class="rounded img-fluid" style="width: auto; height: auto; display: block; margin: 20px auto;" src="{% static '/assets/img/logo.png' %}">
            </div>

  
            <div>
                {{ form.first_name.label_tag }}
                {{ form.first_name }}
                {% if form.first_name.errors %}
                    <div class="text-danger">
                        {{ form.first_name.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                {{ form.last_name.label_tag }}
                {{ form.last_name }}
                {% if form.last_name.errors %}
                    <div class="text-danger">
                        {{ form.last_name.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                {{ form.email.label_tag }}
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="text-danger">
                        {{ form.email.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                {{ form.slack_handle.label_tag }}
                {{ form.slack_handle }}
                {% if form.slack_handle.errors %}
                    <div class="text-danger">
                        {{ form.slack_handle.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                {{ form.teams.label_tag }}
                {{ form.teams }}
                {% if form.teams.errors %}
                    <div class="text-danger">
                        {{ form.teams.errors }}
                    </div>
                {% endif %}
            </div>

            <div>
                {{ form.applications.label_tag }}
                {{ form.applications }}
                {% if form.applications.errors %}
                    <div class="text-danger">
                        {{ form.applications.errors }}
                    </div>
                {% endif %}
            </div>

            <!-- Submit dugme -->
            <button class="btn btn-light w-100 mt-4" style="background-color: rgb(13, 243, 67);" type="submit">SUBMIT</button>
        </form>
        <!-- End: Request Access Form -->
    </div>
</body>

{% endblock content %}
