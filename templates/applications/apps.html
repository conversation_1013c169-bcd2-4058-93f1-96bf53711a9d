{% extends 'base.html' %}
{% load static %}
{% block content %}
<body id="page-top">
{% include 'navbar.html' %}
    <div class="container-fluid">
        <div>
            <h1 style="color: var(--bs-white);background-color: #278557;">Available Apps</h1>
        </div>

        <!-- First Row: Display 3 Apps -->
<div class="row">
    {% for app in app_details %}
        {% if app.app_is_active and not app.app_name == 'gis' and not app.app_name == 'sitemap_checker' %}
            <div class="col-md-4">
                <div class="card cards-shadown cards-hover" data-aos="flip-left" data-aos-duration="650">
                    <div class="card-header" style="background: var(--bs-gray-100);">
                        <span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a></span>
                        <div class="cardheader-text">
                            <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6eb589;">{{ app.app_name }}</h4>
                            <p id="cardheader-subtext-1" style="color: var(--bs-gray-900);">{{ app.app_description }}</p>
                            {% if app.app_name == 'Logana' %}
                                {% if app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/logana/domain" class="btn btn-primary btn-sm active disabled" role="button" aria-pressed="true">EXPLORE</a>
                                    {% else %}
                                        <a href="/logana/domain" class="btn btn-primary btn-sm active" role="button" aria-pressed="true">EXPLORE</a>
                                    {% endif %}
                                {% else %}
                                    <a href="/logana/create" class="btn btn-primary btn-sm active" role="button" aria-pressed="true" style="background-color: #404040;">LAUNCH</a>
                                    <a href="/logana/domain" class="btn btn-primary btn-sm active" role="button" aria-pressed="true" style="background-color: #404040;">EXPLORE</a>
                                {% endif %}
                            {% elif app.app_name == 'PSI' %}
                                {% if not app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/psi/admin" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">ADMIN</a>
                                        <a href="/psi/domain_dash" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">EXPLORE</a>
                                    {% else %}
                                        <a href="/psi/admin" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">ADMIN</a>
                                        <a href="/psi/domain_dash" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">EXPLORE</a>
                                    {% endif %}
                                {% else %}
                                    <a href="/psi/admin" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">ADMIN</a>
                                    <a href="/psi/domain_dash" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">EXPLORE</a>
                                {% endif %}
                            {% elif app.app_name == 'topstories' %}
                                {% if not app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/topstories/report" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">REPORT</a>
                                        <a href="/topstories/search_targets" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">TARGETS</a>
                                        {% if user_market %}
                                            <a href="/topstories/keyword_tracking" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">MANAGER</a>
                                        {% else %}
                                            <a href="#" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">MANAGER</a>
                                        {% endif %}
                                    {% else %}
                                        <a href="/topstories/report" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">REPORT</a>
                                        <a href="/topstories/search_targets" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">TARGETS</a>
                                        {% if user_market %}
                                            <a href="/topstories/keyword_tracking" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">MANAGER</a>
                                        {% else %}
                                            <a href="#" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">MANAGER</a>
                                        {% endif %}
                                    {% endif %}
                                {% else %}
                                    <a href="/topstories/report" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">REPORT</a>
                                    <a href="/topstories/search_targets" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">TARGETS</a>
                                    {% if user_market %}
                                        <a href="/topstories/keyword_tracking" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">MANAGER</a>
                                    {% else %}
                                        <a href="#" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">MANAGER</a>
                                    {% endif %}
                                {% endif %}
                            {% elif app.app_name == 'drbot' %}
                                {% if not app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/chatbot" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">Open Chatbot</a>
                                    {% else %}
                                        <a href="/chatbot" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Open Chatbot</a>
                                    {% endif %}
                                {% else %}
                                    <a href="/chatbot" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Open Chatbot</a>
                                {% endif %}
                            {% elif app.app_name == 'OCC' %}
                                {% if not app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/cache" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">Open Cache App</a>
                                    {% else %}
                                        <a href="/cache" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Open Cache App</a>
                                    {% endif %}
                                {% else %}
                                    <a href="/cache" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Open Cache App</a>
                                {% endif %}
                            {% elif app.app_name == 'autorevap' %}
                                {% if not app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/fsn" class="btn btn-primary btn-sm disabled" role="button" aria-pressed="true" style="background-color: #404040;">Open Autorevap App</a>
                                    {% else %}
                                        <a href="/fsn" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Open Autorevap App</a>
                                    {% endif %}
                                {% else %}
                                    <a href="/fsn" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Open Autorevap App</a>
                                {% endif %}
                            {% elif app.app_name == 'gsc' %}
                                {% if app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="#" class="btn btn-primary btn-sm active disabled" role="button" aria-pressed="true">EXPLORE</a>
                                    {% else %}
                                        <a href="#" class="btn btn-primary btn-sm active" role="button" aria-pressed="true">EXPLORE</a>
                                    {% endif %}
                                {% else %}
                                    <a href="#" class="btn btn-primary btn-sm active" role="button" aria-pressed="true" style="background-color: #404040;">LAUNCH</a>
                                    <a href="#" class="btn btn-primary btn-sm active" role="button" aria-pressed="true" style="background-color: #404040;">EXPLORE</a>
                                {% endif %}
                            {% elif app.app_name == 'llmdash' %}
                                {% if app.app_stadalone %}
                                    {% if app.in_test %}
                                        <a href="/llm-search" class="btn btn-primary btn-sm active disabled" role="button" aria-pressed="true">Monitor LLM coverage</a>
                                    {% else %}
                                        <a href="/llm-search" class="btn btn-primary btn-sm active" role="button" aria-pressed="true">Monitor LLM coverage</a>
                                    {% endif %}
                                {% else %}
                                    <a href="/llm-search" class="btn btn-primary btn-sm active" role="button" aria-pressed="true" style="background-color: #404040;">Monitor LLM coverage</a>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
</div>

        <!-- Second Row: GIS, Sitemap Checker, and Google Trends Apps -->
        <div class="row">
            {% for app in app_details %}
                {% if app.app_is_active and app.app_name == 'gis' %}
                    <div class="col-md-4">
                        <div class="card cards-shadown cards-hover" data-aos="flip-left" data-aos-duration="650">
                            <div class="card-header" style="background: var(--bs-gray-100);">
                                <span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a></span>
                                <div class="cardheader-text">
                                    <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6eb589;">{{ app.app_name }}</h4>
                                    <p id="cardheader-subtext-1" style="color: var(--bs-gray-900);">{{ app.app_description }}</p>
                                    <a href="/gis/indexing/" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Check</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% elif app.app_is_active and app.app_name == 'sitemap_checker' %}
                    <div class="col-md-4">
                        <div class="card cards-shadown cards-hover" data-aos="flip-left" data-aos-duration="650">
                            <div class="card-header" style="background: var(--bs-gray-100);">
                                <span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a></span>
                                <div class="cardheader-text">
                                    <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6eb589;">Sitemap Checker</h4>
                                    <p id="cardheader-subtext-1" style="color: var(--bs-gray-900);">{{ app.app_description }}</p>
                                    <a href="/sitemap_checker/" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">LAUNCH</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% elif app.app_is_active and app.app_name == 'google_trends' %}
                    <div class="col-md-4">
                        <div class="card cards-shadown cards-hover" data-aos="flip-left" data-aos-duration="650">
                            <div class="card-header" style="background: var(--bs-gray-100);">
                                <span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a></span>
                                <div class="cardheader-text">
                                    <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6eb589;">TrendingNOW Scraper</h4>
                                    <p id="cardheader-subtext-1" style="color: var(--bs-gray-900);">{{ app.app_description }}</p>
                                    <a href="/google-trends/trending-data/" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Trending Data</a>
                                    <a href="/google-trends/trending-config/" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Configurations</a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endfor %}

            <!-- Add Google Trends App if not already displayed -->
            {% with google_trends_exists=False %}
                {% for app in app_details %}
                    {% if app.app_name == 'google_trends' %}
                        {% with google_trends_exists=True %}{% endwith %}
                    {% endif %}
                {% endfor %}

                {% if not google_trends_exists %}
                <div class="col-md-4">
                    <div class="card cards-shadown cards-hover" data-aos="flip-left" data-aos-duration="650">
                        <div class="card-header" style="background: var(--bs-gray-100);">
                            <span class="space"><a href="#"><i class="fa fa-rocket" id="download-icon-1" style="color:green;"></i></a></span>
                            <div class="cardheader-text">
                                <h4 id="heading-card-1" style="font-size: 26px;color: var(--bs-white);background-color: #6eb589;">TrendingNOW Scraper</h4>
                                <p id="cardheader-subtext-1" style="color: var(--bs-gray-900);">Track and analyze trending search topics from Google</p>
                                <a href="/google-trends/trending-data/" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Trending Data</a>
                                <a href="/google-trends/trending-config/" class="btn btn-primary btn-sm" role="button" aria-pressed="true" style="background-color: #404040;">Configurations</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% endwith %}
        </div>
    </div>
</div>
{% include 'footer.html'%}
{% include 'scripts.html' %}
</body>
{% endblock content %}