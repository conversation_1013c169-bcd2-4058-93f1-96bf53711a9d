{% extends 'base.html' %}
{% load static %}

{% block content %}
{% include 'google_trends/gt_navbar.html' %}

<style>
    body {
        font-family: Arial, sans-serif;
        text-align: center;
    }
    .table-container {
        display: inline-block;
        margin: 0 auto;
        text-align: left;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }
    table, th, td {
        border: 1px solid black;
    }
    th, td {
        padding: 8px;
        text-align: left;
    }
    th {
        background-color: #f2f2f2;
    }
    tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    .pagination {
        display: inline-block;
        margin: 20px 0;
    }
    .actions button {
        margin-right: 5px;
        padding: 5px 10px;
        border: none;
        cursor: pointer;
    }
    .actions button[type="button"] {
        background-color: #f0ad4e;
        color: white;
    }
    .actions button[type="submit"] {
        background-color: #5cb85c;
        color: white;
    }
    .actions button[type="button"]:last-child {
        background-color: #d9534f;
        color: white;
    }
    .add-form {
        margin: 20px 0;
        text-align: center;
    }
    .add-form table {
        margin: 0 auto;
    }
    .add-form input,
    .add-form select {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
    }
    .add-form button[type="submit"] {
        padding: 10px 20px;
        background-color: #5cb85c;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    }
    .add-form button[type="submit"]:hover {
        background-color: #4cae4c;
    }
</style>

<h1>Google Trends - Configuration</h1>

<div class="table-container">
    <!-- Add Configuration Form -->
    <div class="add-form">
        <h2>Add New Configuration</h2>
        <form method="post">
            {% csrf_token %}
            <input type="hidden" name="action" value="add">
            <table>
                <tr>
                    <td>
                        <select name="country_code" id="country_code" style="width: 200px;" required>
                            <option value="">Select Country</option>
                            {% for country_code, country_name in countries %}
                            <option value="{{ country_code }}">{{ country_name }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td>
                        <select name="category" id="category" style="width: 200px;">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td>
                        <select name="hours" id="hours" style="width: 200px;">
                            {% for hour in hours_options %}
                            <option value="{{ hour }}" {% if hour == 24 %}selected{% endif %}>{{ hour }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td>
                        <select name="status" id="status" style="width: 200px;">
                            <option value="">No Status Filter</option>
                            {% for status in status_options %}
                            <option value="{{ status }}">{{ status }}</option>
                            {% endfor %}
                        </select>
                    </td>
                </tr>
            </table>
            <button type="submit">Add Configuration</button>
        </form>
    </div>

    <!-- Configuration Table -->
    <div style="margin-top: 30px;">
        <h2>Trending Configurations</h2>
        <div style="text-align: right; margin-bottom: 10px;">
            <a href="{% url 'google_trends:trending_data' %}" style="background-color: #3e6643; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; text-decoration: none;">View Trending Data</a>
        </div>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Country</th>
                    <th>Category</th>
                    <th>Hours</th>
                    <th>Status</th>
                    <th>Active</th>
                    <th>Created</th>
                    <th>Updated</th>
                    <th class="actions">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for config in configs %}
                <tr>
                    <td>{{ config.0 }}</td>
                    <td>{{ config.2 }} ({{ config.1 }})</td>
                    <td>{{ config.3|default:"All" }}</td>
                    <td>{{ config.4 }}</td>
                    <td>{{ config.5|default:"None" }}</td>
                    <td>
                        {% if config.6 %}
                            <span style="background-color: #d4edda; color: #155724; padding: 3px 6px; border-radius: 3px;">Active</span>
                        {% else %}
                            <span style="background-color: #f8d7da; color: #721c24; padding: 3px 6px; border-radius: 3px;">Inactive</span>
                        {% endif %}
                    </td>
                    <td>{{ config.7 }}</td>
                    <td>{{ config.8 }}</td>
                    <td class="actions">
                        <form method="post" style="display: inline-block; margin-right: 5px;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="toggle">
                            <input type="hidden" name="id" value="{{ config.0 }}">
                            <input type="hidden" name="active" value="{{ config.6 }}">
                            <button type="submit" style="{% if config.6 %}background-color: #f0ad4e;{% else %}background-color: #5cb85c;{% endif %} color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                                {% if config.6 %}Deactivate{% else %}Activate{% endif %}
                            </button>
                        </form>
                        <form method="post" style="display: inline-block;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="id" value="{{ config.0 }}">
                            <button type="submit" style="background-color: #d9534f; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;" onclick="return confirm('Are you sure you want to delete this configuration?')">Delete</button>
                        </form>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="text-center">No configurations available</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% include 'footer.html' %}
{% include 'scripts.html' %}
{% endblock content %}
