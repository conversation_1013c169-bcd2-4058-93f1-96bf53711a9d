{% extends 'base.html' %}
{% load static %}

{% block content %}
{% include 'google_trends/gt_navbar.html' %}

<style>
    body {
        font-family: '<PERSON><PERSON><PERSON>', 'Quicksand', Arial, sans-serif;
        text-align: center;
        background-color: #f8f9fa;
    }
    .page-header {
        background-color: #278557;
        color: white;
        padding: 15px 0;
        margin-bottom: 25px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .page-header h1 {
        margin: 0;
        font-weight: 600;
    }
    .page-header h4 {
        margin-top: 5px;
        font-weight: 400;
        opacity: 0.9;
    }
    .content-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        padding: 20px 10px;
        margin-bottom: 25px;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .content-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .table-container {
        width: 100%;
        margin: 0 auto;
        text-align: left;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }
    table, th, td {
        border: 1px solid #e0e0e0;
    }
    th, td {
        padding: 12px 15px;
        text-align: left;
    }
    th {
        background-color: #3e6643;
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85em;
        letter-spacing: 0.5px;
    }
    tr:nth-child(even) {
        background-color: #f2f7f4;
    }
    tr:hover {
        background-color: #e8f4ed;
    }
    .pagination {
        display: inline-block;
        margin: 20px 0;
        border-radius: 30px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .pagination a, .pagination span {
        margin: 0;
        padding: 10px 15px;
        text-decoration: none;
        border: none;
        color: #3e6643;
        background-color: white;
        display: inline-block;
        transition: all 0.3s ease;
    }
    .pagination a:hover {
        background-color: #e8f4ed;
    }
    .pagination .current {
        background-color: #3e6643;
        color: white;
    }
    /* Table action buttons removed */
    .info-icon {
        font-size: 14px;
        margin-left: 5px;
        color: #6eb589;
        cursor: pointer;
        border-bottom: 1px dotted #6eb589;
        position: relative;
    }
    .info-icon:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        background-color: #3e6643;
        color: #fff;
        padding: 8px 12px;
        border-radius: 5px;
        top: 100%;
        left: 0;
        z-index: 1;
        white-space: nowrap;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .filter-form {
        margin-bottom: 25px;
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 20px;
        background-color: #f2f7f4;
        border-radius: 8px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
    }
    .filter-section {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        width: 100%;
    }
    .filter-group {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-width: 180px;
    }
    .filter-group label {
        font-size: 13px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #3e6643;
    }
    .search-group {
        flex: 2;
        min-width: 250px;
    }
    .date-range-group {
        flex: 2;
        min-width: 250px;
    }
    .date-range-picker {
        position: relative;
        display: flex;
        align-items: center;
    }
    .date-range-picker input {
        padding-right: 30px; /* Space for the clear button */
        padding-left: 35px; /* Space for the calendar icon */
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%233e6643" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
        background-repeat: no-repeat;
        background-position: 10px center;
        background-size: 16px;
        cursor: pointer;
    }
    .date-range-picker button {
        position: absolute;
        right: 10px;
        background: none;
        border: none;
        color: #999;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: color 0.2s;
    }
    .date-range-picker button:hover {
        color: #d9534f;
    }
    .filter-form input,
    .filter-form select {
        padding: 10px 15px;
        border: 1px solid #d0e0d6;
        border-radius: 5px;
        font-size: 14px;
        background-color: white;
        transition: all 0.3s ease;
        width: 100%;
    }
    .filter-form input:focus,
    .filter-form select:focus {
        border-color: #3e6643;
        outline: none;
        box-shadow: 0 0 0 3px rgba(62, 102, 67, 0.2);
    }
    .filter-actions {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-top: 5px;
    }
    .filter-button {
        padding: 10px 25px;
        background-color: #3e6643;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        min-width: 140px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
    .filter-button:hover {
        background-color: #278557;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .reset-button {
        padding: 10px 20px;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
    .reset-button:hover {
        background-color: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    /* Date range picker styling */
    .daterangepicker {
        font-family: 'Nunito', 'Quicksand', Arial, sans-serif;
        border-radius: 8px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .daterangepicker .calendar-table {
        border-radius: 5px;
    }
    .daterangepicker td.active,
    .daterangepicker td.active:hover {
        background-color: #3e6643;
    }
    .daterangepicker td.in-range {
        background-color: rgba(62, 102, 67, 0.1);
        color: #3e6643;
    }
    .daterangepicker .drp-buttons .btn {
        border-radius: 5px;
        font-weight: 600;
    }
    .daterangepicker .drp-buttons .btn.btn-primary {
        background-color: #3e6643;
        border-color: #3e6643;
    }
    .daterangepicker .drp-buttons .btn.btn-primary:hover {
        background-color: #278557;
        border-color: #278557;
    }
    /* Popup styles removed as they are no longer needed */
</style>

<div class="page-header">
    <h1>TrendingNOW Scraper - Trending Data</h1>
    {% if user_info %}
    <h4>
        Hello {{ user_info.full_name }}
    </h4>
    {% endif %}
</div>

<div class="container-fluid">
    <div class="content-card">
        <div class="table-container">
            <!-- Filter Form -->
            <form method="get" class="filter-form">
                <div class="filter-section">
                    <div class="filter-group">
                        <label for="country">Country</label>
                        <select name="country" id="country">
                            <option value="">All Countries</option>
                            {% for country_code, country_name in countries %}
                            <option value="{{ country_code }}" {% if country_filter == country_code %}selected{% endif %}>{{ country_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="category">Category</label>
                        <select name="category" id="category">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category }}" {% if category_filter == category %}selected{% endif %}>{{ category }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="hours">Hours</label>
                        <select name="hours" id="hours">
                            <option value="">All Hours</option>
                            {% for hour in hours_options %}
                            <option value="{{ hour }}" {% if hours_filter == hour|stringformat:"i" %}selected{% endif %}>{{ hour }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="status">Status</label>
                        <select name="status" id="status">
                            <option value="">All Statuses</option>
                            {% for status in status_options %}
                            <option value="{{ status }}" {% if status_filter == status %}selected{% endif %}>{{ status }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="filter-section">
                    <div class="filter-group search-group">
                        <label for="search">Search</label>
                        <input type="text" name="search" id="search" placeholder="Search keywords..." value="{{ search_query }}">
                    </div>

                    <div class="filter-group date-range-group">
                        <label for="date_range">Timeframe</label>
                        <div class="date-range-picker">
                            <input type="text" id="date_range" name="date_range" placeholder="Select date range" value="{{ date_range }}" readonly onclick="openDateRangePicker(this)">
                            <input type="hidden" id="start_date" name="start_date" value="{{ start_date }}">
                            <input type="hidden" id="end_date" name="end_date" value="{{ end_date }}">
                            <button type="button" id="clear_dates" title="Clear dates" {% if not date_range %}style="display:none"{% endif %}>×</button>
                        </div>
                    </div>
                </div>

                <div class="filter-actions">
                    <button type="submit" class="filter-button">
                        <i class="fa fa-filter"></i> Apply Filters
                    </button>
                    <button type="button" id="reset_filters" class="reset-button">
                        <i class="fa fa-refresh"></i> Reset
                    </button>
                </div>
            </form>

            <!-- Action Buttons -->
            <div class="actions" style="display: flex; justify-content: center; margin-bottom: 25px;">
                <button type="button" onclick="exportTableToCSV()" class="action-button export-button">
                    <i class="fa fa-download" aria-hidden="true"></i> Export CSV
                </button>
                <a href="{% url 'google_trends:trending_config' %}" class="action-button config-button">
                    <i class="fa fa-cog" aria-hidden="true"></i> <span style="margin-left: 5px;">Manage Configurations</span>
                </a>
            </div>

            <style>
                .action-button {
                    padding: 12px 20px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    color: white;
                }
                .action-button:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                }
                .action-button:active {
                    transform: translateY(0);
                }
                .export-button {
                    background-color: #6eb589;
                }
                .export-button:hover {
                    background-color: #5da578;
                }
                .config-button {
                    background-color: #3e6643;
                    margin-left: 15px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                }
                .config-button:hover {
                    background-color: #2d4e31;
                    color: white;
                    text-decoration: none;
                }
            </style>

    <!-- Data Table -->
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Country Code</th>
                    <th>Keyword</th>
                    <th>Title</th>
                    <th>Time</th>
                    <th>Search Volume</th>
                    <th>Started Date</th>
                    <th>URL</th>
                    <th>Timestamp</th>
                    <th>Hours</th>
                    <th>Category Param</th>
                    <th>Status</th>
                    <th>Active</th>
                </tr>
            </thead>
            <tbody>
                {% for row in page_obj %}
                <tr class="data-row" data-row-id="{{ row.0 }}">
                    <td>{{ row.0 }}</td>
                    <td>{{ row.1 }}</td>
                    <td>{{ row.2 }}</td>
                    <td>{{ row.3 }}</td>
                    <td>{{ row.4 }}</td>
                    <td>{{ row.5 }}</td>
                    <td>{{ row.6 }}</td>
                    <td><a href="{{ row.7 }}" target="_blank">{{ row.7 }}</a></td>
                    <td>{{ row.8 }}</td>
                    <td>{{ row.9 }}</td>
                    <td>{{ row.10 }}</td>
                    <td>{{ row.11 }}</td>
                    <td>{{ row.12 }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="13" class="text-center">
                        <div class="empty-state">
                            <i class="fa fa-search fa-3x" style="color: #6eb589; margin-bottom: 15px;"></i>
                            <p>No data available</p>
                            <p class="text-muted">Try adjusting your filters or importing data</p>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
        <div class="pagination">
            <span class="step-links">
                {% if page_obj.has_previous %}
                    <a href="?page=1&{{ query_string }}" title="First page">
                        <i class="fa fa-angle-double-left"></i>
                    </a>
                    <a href="?page={{ page_obj.previous_page_number }}&{{ query_string }}" title="Previous page">
                        <i class="fa fa-angle-left"></i>
                    </a>
                {% endif %}

                <span class="current">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&{{ query_string }}" title="Next page">
                        <i class="fa fa-angle-right"></i>
                    </a>
                    <a href="?page={{ page_obj.paginator.num_pages }}&{{ query_string }}" title="Last page">
                        <i class="fa fa-angle-double-right"></i>
                    </a>
                {% endif %}
            </span>
        </div>
    </div>

</div>
</div>
</div>

<style>
    /* Additional styles for better UX */
    .table-responsive {
        overflow-x: auto;
        margin-bottom: 20px;
    }
    table {
        table-layout: auto;
    }
    th, td {
        white-space: nowrap;
        vertical-align: middle;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    th:nth-child(4), td:nth-child(4) {
        /* Title column */
        white-space: normal;
        min-width: 250px;
        max-width: 350px;
    }
    /* Ensure proper column widths */
    th:nth-child(1), td:nth-child(1) { min-width: 60px; width: 60px; } /* ID */
    th:nth-child(2), td:nth-child(2) { min-width: 80px; width: 80px; } /* Country Code */
    th:nth-child(3), td:nth-child(3) { min-width: 120px; width: 150px; } /* Keyword */
    th:nth-child(4), td:nth-child(4) { min-width: 250px; max-width: 350px; } /* Title */
    th:nth-child(5), td:nth-child(5) { min-width: 100px; width: 120px; } /* Time */
    th:nth-child(6), td:nth-child(6) { min-width: 120px; width: 150px; } /* Search Volume */
    th:nth-child(7), td:nth-child(7) { min-width: 180px; width: 180px; } /* Started Date */
    th:nth-child(8), td:nth-child(8) { min-width: 150px; width: 200px; } /* URL */
    th:nth-child(9), td:nth-child(9) { min-width: 180px; width: 180px; } /* Timestamp */
    th:nth-child(10), td:nth-child(10) { min-width: 70px; width: 70px; } /* Hours */
    th:nth-child(11), td:nth-child(11) { min-width: 120px; width: 150px; } /* Category Param */
    th:nth-child(12), td:nth-child(12) { min-width: 80px; width: 80px; } /* Status */
    th:nth-child(13), td:nth-child(13) { min-width: 70px; width: 70px; } /* Active */
    /* Form control styles removed as they are no longer needed */
    .id-badge {
        display: inline-block;
        background-color: #3e6643;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        font-weight: 600;
    }
    .status-badge {
        display: inline-block;
        background-color: #6eb589;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        font-weight: 600;
    }
    .active-badge {
        display: inline-block;
        background-color: #28a745;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        font-weight: 600;
    }
    .inactive-badge {
        display: inline-block;
        background-color: #dc3545;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        font-weight: 600;
    }
    .date-value {
        color: #3e6643;
        font-weight: 500;
    }
    .empty-value {
        color: #999;
        font-style: italic;
    }
    .empty-state {
        padding: 40px 0;
        text-align: center;
    }
    .empty-state p {
        margin: 5px 0;
        font-size: 16px;
    }
    .empty-state .text-muted {
        color: #6c757d;
        font-size: 14px;
    }
    .pagination-container {
        display: flex;
        justify-content: center;
        margin: 20px 0;
    }
    /* Tooltip styles */
    [title] {
        position: relative;
    }
    [title]:hover::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #3e6643;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 10;
        opacity: 0;
        animation: fadeIn 0.3s forwards;
    }
    @keyframes fadeIn {
        to { opacity: 1; }
    }
</style>

<script>
// Row editing and deletion functions removed as they are no longer needed

// Function to export table data to CSV
function exportTableToCSV() {
    // Get current URL and add export parameter
    const currentUrl = new URL(window.location.href);
    
    // Preserve all current filter parameters
    currentUrl.searchParams.set('export', 'csv');
    
    // Remove page parameter as we want all results
    currentUrl.searchParams.delete('page');
    
    // Redirect to the export URL
    window.location.href = currentUrl.toString();
}

// Process table data after page loads
window.addEventListener('load', function() {
    // Add responsive table handling
    const table = document.querySelector('table');
    if (table && table.scrollWidth > table.clientWidth) {
        const notice = document.createElement('div');
        notice.className = 'scroll-notice';
        notice.innerHTML = '<i class="fa fa-arrows-h"></i> Scroll horizontally to see all data';
        notice.style.textAlign = 'center';
        notice.style.color = '#666';
        notice.style.padding = '10px';
        notice.style.fontSize = '14px';
        table.parentNode.insertBefore(notice, table);
    }

    // Process title and source data
    document.querySelectorAll('.title-cell').forEach(function(cell) {
        const titleSpan = cell.querySelector('.title-text');
        const fullTitle = cell.getAttribute('data-full-title') || titleSpan.textContent;

        if (fullTitle && (fullTitle.includes('*') || fullTitle.includes(' - '))) {
            let title, source;

            // Try to split by * first (common format in the data)
            if (fullTitle.includes('*')) {
                const parts = fullTitle.split('*');
                if (parts.length >= 2) {
                    title = parts[0].trim();
                    source = parts[1].trim();
                }
            }
            // If no * found, try splitting by " - " which is another common format
            else if (fullTitle.includes(' - ')) {
                const lastDashIndex = fullTitle.lastIndexOf(' - ');
                title = fullTitle.substring(0, lastDashIndex).trim();
                source = fullTitle.substring(lastDashIndex + 3).trim();
            }

            if (title && source) {
                // Update title
                titleSpan.textContent = title;

                // Update source in the next cell
                const sourceCell = cell.nextElementSibling;
                if (sourceCell && sourceCell.classList.contains('source-cell')) {
                    const sourceSpan = sourceCell.querySelector('.source-text');
                    if (sourceSpan) {
                        sourceSpan.textContent = source;
                    }
                }
            }
        }
    });

    // Format date fields
    document.querySelectorAll('.date-value').forEach(function(dateSpan) {
        const dateText = dateSpan.textContent;
        if (dateText && dateText !== '-') {
            try {
                // Try to format the date if it looks like a date
                if (dateText.includes('-') || dateText.includes('/') || dateText.includes(',')) {
                    const date = new Date(dateText);
                    if (!isNaN(date.getTime())) {
                        // Format as YYYY-MM-DD
                        const formattedDate = date.toISOString().split('T')[0];
                        dateSpan.textContent = formattedDate;
                    }
                }
            } catch (e) {
                console.log('Error formatting date:', e);
            }
        }
    });

    // Fix column data if needed
    document.querySelectorAll('.data-row').forEach(function(row) {
        const cells = row.querySelectorAll('td');

        // Check if we need to fix the data alignment
        if (cells.length >= 10) {
            // Check if hours column contains a date (indicating misalignment)
            const hoursCell = cells[5]; // 6th column (0-indexed)
            const hoursText = hoursCell.textContent.trim();

            if (hoursText.includes(',') || hoursText.includes('-') || /\d{4}/.test(hoursText)) {
                console.log('Fixing data alignment for row:', row.getAttribute('data-row-id'));

                // Get the correct hours value from category param
                const categoryParamCell = cells[9]; // 10th column
                const categoryParamText = categoryParamCell.textContent.trim();

                // If category param is a number, it's likely the hours
                if (/^\d+$/.test(categoryParamText)) {
                    hoursCell.textContent = categoryParamText;

                    // Get status from the status cell
                    const statusCell = cells[8]; // 9th column
                    const statusText = statusCell.textContent.trim();

                    // Update category param with the status value if it's not a number
                    if (statusText && statusText !== '-' && !/^\d+$/.test(statusText)) {
                        categoryParamCell.textContent = statusText;

                        // Clear the status cell as we've moved its content
                        statusCell.innerHTML = '<span class="empty-value">-</span>';
                    }
                }
            }
        }
    });
});

// Function to manually open the date range picker
function openDateRangePicker(input) {
    console.log('Attempting to open date range picker');

    // Force re-initialization if needed
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not available');
        return;
    }

    if (typeof jQuery(input).data('daterangepicker') === 'undefined') {
        console.log('DateRangePicker not initialized, reinitializing...');
        initDateRangePicker();
    }

    // Try to open it after a short delay
    setTimeout(function() {
        try {
            jQuery(input).data('daterangepicker').show();
            console.log('Date range picker opened');
        } catch (e) {
            console.error('Failed to open date picker:', e);
            // Last resort - trigger a click event
            jQuery(input).trigger('click');
        }
    }, 100);
}
</script>

<script>
// Initialize date picker when document is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        initDateRangePicker();
    }, 500); // Small delay to ensure all scripts are loaded
});

function initDateRangePicker() {
    console.log('Initializing date range picker...');
    try {
        // Initialize date range picker
        jQuery('#date_range').daterangepicker({
        autoUpdateInput: false,
        opens: 'left',
        maxDate: moment(),
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        locale: {
            format: 'YYYY-MM-DD',
            cancelLabel: 'Clear',
            applyButtonClasses: 'btn-primary',
            cancelClass: 'btn-secondary'
        }
    });

    // Handle date selection
    jQuery('#date_range').on('apply.daterangepicker', function(ev, picker) {
        jQuery(this).val(picker.startDate.format('YYYY-MM-DD') + ' to ' + picker.endDate.format('YYYY-MM-DD'));
        jQuery('#start_date').val(picker.startDate.format('YYYY-MM-DD'));
        jQuery('#end_date').val(picker.endDate.format('YYYY-MM-DD'));
        jQuery('#clear_dates').show();
    });

    // Handle date clear
    jQuery('#date_range').on('cancel.daterangepicker', function(ev, picker) {
        jQuery(this).val('');
        jQuery('#start_date').val('');
        jQuery('#end_date').val('');
        jQuery('#clear_dates').hide();
    });

    // Clear date button
    jQuery('#clear_dates').on('click', function() {
        jQuery('#date_range').val('');
        jQuery('#start_date').val('');
        jQuery('#end_date').val('');
        jQuery(this).hide();
    });

    // Reset all filters
    jQuery('#reset_filters').on('click', function() {
        // Clear all select elements
        jQuery('select').each(function() {
            jQuery(this).val('');
        });

        // Clear text inputs
        jQuery('#search').val('');

        // Clear date range
        jQuery('#date_range').val('');
        jQuery('#start_date').val('');
        jQuery('#end_date').val('');
        jQuery('#clear_dates').hide();

        // Submit the form
        jQuery(this).closest('form').submit();
    });

    console.log('Date range picker initialized successfully');
    } catch (error) {
        console.error('Error initializing date range picker:', error);
    }
}
</script>

{% include 'footer.html' %}
{% include 'scripts.html' %}

<!-- Date Range Picker Library -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

{% endblock content %}
