{% extends 'base.html' %}
{% load i18n static %}

{% block content %}
<body id="page-top">
    {% include 'topstories/ts_navbar.html' %}

    <div class="container-fluid">
        <div class="row p-3 border bg-light">
            <div class="col">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">URL Indexing Checker</h4>
                    </div>
                    <div class="card-body">
                        <form id="urlForm" method="POST" enctype="multipart/form-data" class="mb-4">
                            {% csrf_token %}
                            <div class="form-group">
                                {{ form.as_p }}
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search mr-2"></i>Check URLs
                            </button>
                        </form>

                        <!-- Progress Section -->
                        <div id="progressSection" class="mb-4" style="display: none;">
                            <div class="progress mb-3">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small id="progressText" class="text-muted">Processing URLs...</small>
                                <small id="processedCount" class="text-muted">0 URLs processed</small>
                            </div>
                        </div>

                        <!-- Loading Spinner -->
                        <div id="loader" class="text-center mt-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <p class="mt-2">Initializing URL check...</p>
                        </div>

                        <!-- Results Section -->
                        <div id="resultsSection" style="display: none;">
                            <!-- Summary Cards -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Indexed URLs</h5>
                                            <h2 id="indexedCount" class="mb-0">0</h2>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Not Indexed</h5>
                                            <h2 id="notIndexedCount" class="mb-0">0</h2>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Total Checked</h5>
                                            <h2 id="totalCount" class="mb-0">0</h2>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Results Table -->
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Detailed Results</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive" style="height: 400px; overflow-y: auto;">
                                        <table id="resultsTable" class="table table-striped table-hover">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>URL</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="resultsBody">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Pie Chart -->
                            <div class="card shadow-sm mt-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Indexing Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-center">
                                        <div id="pieChart" style="width: 100%; max-width: 600px; height: 400px;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Download Button -->
                            <div class="d-flex justify-content-center mt-4">
                                <button id="downloadBtn" class="btn btn-success">
                                    <i class="fas fa-download mr-2"></i>Download Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <a class="border rounded d-inline scroll-to-top" href="#page-top"><i class="fas fa-angle-up"></i></a>
    {% include 'footer.html' %}

    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include DataTables -->
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
    <!-- Plotly Pie Chart -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    {% include 'scripts.html' %}

    <script>
        $(document).ready(function() {
            var dataTable;
            var polling;
            var totalUrls = 0;
            var processedUrls = 0;

            function updateProgress(processed, total) {
                const percentage = (processed / total) * 100;
                $('#progressBar').css('width', percentage + '%');
                $('#processedCount').text(`${processed}/${total} URLs processed`);
            }

            function startPolling(taskId) {
                $('#loader').hide();
                $('#progressSection').show();
                polling = setInterval(function() {
                    $.ajax({
                        url: `/gis/task_status/${taskId}/`,
                        type: 'GET',
                        success: function(response) {
                            console.log('Polling response:', response);
                            updateResults(response);
                            if (response.state === 'SUCCESS' || response.state === 'FAILURE') {
                                clearInterval(polling);
                                $('#progressSection').hide();
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Polling error:', error);
                            clearInterval(polling);
                            $('#progressSection').hide();
                            alert('Error checking task status: ' + error);
                        }
                    });
                }, 1000);
            }

            function updateResults(response) {
                // Update progress if available
                if (response.progress) {
                    updateProgress(response.progress.current, response.progress.total);
                }

                // Handle results if available
                if (response.result) {
                    $('#resultsSection').show();
                    const results = response.result;
                    
                    // Update counts
                    const indexedCount = results.filter(r => r.status === 'True').length;
                    const notIndexedCount = results.filter(r => r.status === 'False').length;
                    const totalCount = results.length;
                    
                    $('#indexedCount').text(indexedCount);
                    $('#notIndexedCount').text(notIndexedCount);
                    $('#totalCount').text(totalCount);

                    // Update the results table
                    if ($.fn.DataTable.isDataTable('#resultsTable')) {
                        dataTable.clear().rows.add(results).draw();
                    } else {
                        dataTable = $('#resultsTable').DataTable({
                            data: results,
                            columns: [
                                { 
                                    data: 'url',
                                    title: "URL",
                                    render: function(data) {
                                        return `<a href="${data}" target="_blank">${data}</a>`;
                                    }
                                },
                                { 
                                    data: 'status',
                                    title: "Status",
                                    render: function(data) {
                                        const badge = data === 'True' ? 
                                            '<span class="badge badge-success" style="color: black;">Indexed</span>' : 
                                            '<span class="badge badge-danger" style="color: black;">Not Indexed</span>';
                                        return badge;
                                    }
                                },
                                {
                                    data: null,
                                    title: "Actions",
                                    render: function(data) {
                                        return `<button class="btn btn-sm btn-outline-primary copy-url" data-url="${data.url}">
                                                    <i class="fas fa-copy"></i>
                                                </button>`;
                                    }
                                }
                            ],
                            "scrollY": "400px",
                            "scrollCollapse": true,
                            "paging": false,
                            "searching": true,
                            "info": false,
                            "order": [[1, 'desc']]
                        });
                    }

                    // Update pie chart
                    var data = [{
                        values: [indexedCount, notIndexedCount],
                        labels: ['Indexed', 'Not Indexed'],
                        type: 'pie',
                        marker: {
                            colors: ['#28a745', '#dc3545']
                        }
                    }];

                    var layout = {
                        height: 400,
                        width: 600,
                        title: 'URL Indexing Status',
                        showlegend: true,
                        legend: {
                            orientation: 'h',
                            y: -0.1
                        }
                    };

                    Plotly.newPlot('pieChart', data, layout);
                }

                // Update status text
                $('#progressText').text(response.status || 'Processing URLs...');
            }

            // Form submission handler
            $('#urlForm').on('submit', function(e) {
                e.preventDefault();
                $('#loader').show();
                $('#progressSection').hide();
                $('#resultsSection').hide();
                
                var formData = new FormData(this);
                
                $.ajax({
                    url: '/gis/indexing/',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Form submission response:', response);
                        if (response.task_id) {
                            totalUrls = response.total_urls || 0;
                            startPolling(response.task_id);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Form submission error:', error);
                        $('#loader').hide();
                        alert('Error submitting URLs: ' + error);
                    }
                });
            });

            // Copy URL button handler
            $(document).on('click', '.copy-url', function() {
                var url = $(this).data('url');
                navigator.clipboard.writeText(url).then(function() {
                    alert('URL copied to clipboard!');
                }).catch(function(err) {
                    console.error('Failed to copy URL:', err);
                });
            });

            // Download button handler
            $('#downloadBtn').on('click', function() {
                if (!dataTable) return;
                
                var data = dataTable.data().toArray();
                var csv = 'URL,Status\n';
                data.forEach(function(row) {
                    csv += `"${row.url}",${row.status}\n`;
                });
                
                var blob = new Blob([csv], { type: 'text/csv' });
                var url = window.URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.setAttribute('hidden', '');
                a.setAttribute('href', url);
                a.setAttribute('download', 'url_indexing_results.csv');
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            });
        });
    </script>
</body>
{% endblock content %}
