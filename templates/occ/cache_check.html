{% extends 'base.html' %}
{% load i18n static %}

{% block content %}
<body id="page-top" class="px-0 py-0">
{% include 'navbar.html' %}

<head>
    <title>Cache Analysis Tool</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-size: 1.1rem;
        }
        table {
            font-size: 1.05rem;
        }
        h1, h2, h4, h5 {
            font-size: 1.4rem;
        }
        .btn {
            font-size: 1rem;
            padding: 0.6rem 1.2rem;
        }
        canvas {
            margin-top: 1rem;
        }
    </style>
</head>

<body class="container-fluid p-0">
    <div class="container" style="max-width: 1000px;">
        <h1 class="mb-4">Cache Analysis Tool</h1>

        <form method="post" class="mb-4" id="cache-analysis-form">
            {% csrf_token %}
            {{ form.as_p }}
            <button type="submit" class="btn btn-primary">Analyze Cache</button>
        </form>

        {% if analysis %}
            <div id="analysis-container">
                <h2 class="mt-5">Cache Analysis for {{ url }}</h2>
                <button class="btn btn-info mb-3" onclick="copyAnalysis()">Copy All</button>

                <div class="mb-3 d-flex align-items-center">
                    <div class="me-3"><strong>Legend:</strong></div>
                    <div class="d-flex align-items-center me-3">
                        <div style="width: 16px; height: 16px; background-color: rgba(66, 133, 244, 0.9); margin-right: 6px;"></div> Cached
                    </div>
                    <div class="d-flex align-items-center">
                        <div style="width: 16px; height: 16px; background-color: rgba(219, 68, 55, 0.9); margin-right: 6px;"></div> No Cache
                    </div>
                </div>

                <!-- Bar Chart -->
                <canvas id="responseTimeChart" height="120"></canvas>
                <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const ctx = document.getElementById("responseTimeChart").getContext("2d");

                    new Chart(ctx, {
                        type: "bar",
                        data: {
                            labels: ["Mobile (Cached)", "Mobile (No Cache)", "Desktop (Cached)", "Desktop (No Cache)"],
                            datasets: [{
                                label: "Average Response Time (s)",
                                data: [
                                    {{ analysis.mobile_cache_average|floatformat:3 }},
                                    {{ analysis.mobile_no_cache_average|floatformat:3 }},
                                    {{ analysis.desktop_cache_average|floatformat:3 }},
                                    {{ analysis.desktop_no_cache_average|floatformat:3 }}
                                ],
                                backgroundColor: [
                                    "rgba(66, 133, 244, 1)",
                                    "rgba(219, 68, 55, 0.75)",
                                    "rgba(66, 133, 244, 1)",
                                    "rgba(219, 68, 55, 0.75)"
                                ],
                                borderColor: [
                                    "rgba(66, 133, 244, 1)",
                                    "rgba(219, 68, 55, 1)",
                                    "rgba(66, 133, 244, 1)",
                                    "rgba(219, 68, 55, 1)"
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.dataset.label + ": " + context.raw + " s";
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Response Time (s)'
                                    }
                                }
                            }
                        }
                    });
                });
                </script>

                <!-- Averages Table -->
                <h4 class="mt-4">Average Response Times (Lower is better)</h4>
                <table class="table table-striped table-bordered">
                    <thead><tr><th>Device</th><th>With Cache</th><th>Without Cache</th></tr></thead>
                    <tbody>
                        <tr><td>Mobile</td><td>{{ analysis.mobile_cache_average|floatformat:3 }} s</td><td>{{ analysis.mobile_no_cache_average|floatformat:3 }} s</td></tr>
                        <tr><td>Desktop</td><td>{{ analysis.desktop_cache_average|floatformat:3 }} s</td><td>{{ analysis.desktop_no_cache_average|floatformat:3 }} s</td></tr>
                    </tbody>
                </table>

                <!-- Medians Table -->
                <h4 class="mt-4">Median Response Times</h4>
                <table class="table table-striped table-bordered">
                    <thead><tr><th>Device</th><th>With Cache</th><th>Without Cache</th></tr></thead>
                    <tbody>
                        <tr><td>Mobile</td><td>{{ analysis.mobile_cached_median|floatformat:3 }} s</td><td>{{ analysis.no_cache_mobile_median|floatformat:3 }} s</td></tr>
                        <tr><td>Desktop</td><td>{{ analysis.desktop_cached_median|floatformat:3 }} s</td><td>{{ analysis.no_cache_desktop_median|floatformat:3 }} s</td></tr>
                    </tbody>
                </table>

                <!-- Range Table -->
                <h4 class="mt-4">Range Between Fastest and Slowest Responses</h4>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr><th>Metric</th><th>Mobile (With Cache)</th><th>Mobile (Without Cache)</th><th>Desktop (With Cache)</th><th>Desktop (Without Cache)</th></tr>
                    </thead>
                    <tbody>
                        <tr><td><strong>Range</strong></td><td>{{ analysis.mobile_cache_max_range|floatformat:3 }} s</td><td>{{ analysis.mobile_no_cache_max_range|floatformat:3 }} s</td><td>{{ analysis.desktop_cache_max_range|floatformat:3 }} s</td><td>{{ analysis.desktop_no_cache_max_range|floatformat:3 }} s</td></tr>
                        <tr><td><strong>Max</strong></td><td>{{ analysis.mobile_cache_max|floatformat:3 }} s</td><td>{{ analysis.mobile_no_cache_max|floatformat:3 }} s</td><td>{{ analysis.desktop_cache_max|floatformat:3 }} s</td><td>{{ analysis.desktop_no_cache_max|floatformat:3 }} s</td></tr>
                        <tr><td><strong>Min</strong></td><td>{{ analysis.mobile_cache_min|floatformat:3 }} s</td><td>{{ analysis.mobile_no_cache_min|floatformat:3 }} s</td><td>{{ analysis.desktop_cache_min|floatformat:3 }} s</td><td>{{ analysis.desktop_no_cache_min|floatformat:3 }} s</td></tr>
                    </tbody>
                </table>

                <!-- Line Chart for Iterations -->
                <h4 class="mt-5">Response Times Across Iterations</h4>
                <canvas id="perRequestChart" height="140"></canvas>

                <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const lineCtx = document.getElementById("perRequestChart").getContext("2d");
                    const labels = [...Array({{ analysis.mobile_cached_times|length }}).keys()].map(i => `Run ${i + 1}`);

                    new Chart(lineCtx, {
                        type: 'line',
                        data: {
                            labels: labels,
                            datasets: [
                                {
                                    label: "Mobile (Cached)",
                                    data: {{ analysis.mobile_cached_times|safe }},
                                    borderColor: "rgba(66, 133, 244, 1)",
                                    backgroundColor: "rgba(66, 133, 244, 0.05)",
                                    fill: false,
                                    tension: 0.3,
                                    pointRadius: 4,
                                },

                                {
                                    label: "Desktop (Cached)",
                                    data: {{ analysis.desktop_cached_times|safe }},
                                    borderColor: "rgba(66, 133, 244, 1)",
                                    backgroundColor: "rgba(66, 133, 244, 0.05)",
                                    fill: false,
                                    tension: 0.3,
                                    pointRadius: 4,
                                    borderDash: [4, 4]
                                },
                                
                                {
                                    label: "Mobile (No Cache)",
                                    data: {{ analysis.no_cache_mobile_times|safe }},
                                    borderColor: "rgba(219, 68, 55, 1)",
                                    backgroundColor: "rgba(219, 68, 55, 0.05)",
                                    fill: false,
                                    tension: 0.3,
                                    pointRadius: 4,
                                },
                                
                                {
                                    label: "Desktop (No Cache)",
                                    data: {{ analysis.no_cache_desktop_times|safe }},
                                    borderColor: "rgba(219, 68, 55, 1)",
                                    backgroundColor: "rgba(219, 68, 55, 0.05)",
                                    fill: false,
                                    tension: 0.3,
                                    pointRadius: 4,
                                    borderDash: [4, 4]
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { display: true },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `${context.dataset.label}: ${context.raw.toFixed(3)} s`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    title: { display: true, text: 'Response Time (s)' },
                                    beginAtZero: true
                                },
                                x: {
                                    title: { display: true, text: 'Request Iteration' }
                                }
                            }
                        }
                    });
                });
                </script>

                <!-- Header Table -->
                <h4 class="mt-5">Detailed Cache Header Comparison</h4>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr><th>Header</th><th>Mobile (Cached)</th><th>Mobile (No Cache)</th><th>Desktop (Cached)</th><th>Desktop (No Cache)</th></tr>
                    </thead>
                    <tbody>
                        <tr><td><strong>Cache-Control</strong></td><td>{{ analysis.results_mobile_cache.Cache_Control }}</td><td>{{ analysis.results_no_cache_mobile.Cache_Control }}</td><td>{{ analysis.results_desktop_cache.Cache_Control }}</td><td>{{ analysis.results_no_cache_desktop.Cache_Control }}</td></tr>
                        <tr><td><strong>Content-Type</strong></td><td>{{ analysis.results_mobile_cache.Content_Type }}</td><td>{{ analysis.results_no_cache_mobile.Content_Type }}</td><td>{{ analysis.results_desktop_cache.Content_Type }}</td><td>{{ analysis.results_no_cache_desktop.Content_Type }}</td></tr>
                        <tr><td><strong>Age</strong></td><td>{{ analysis.results_mobile_cache.Age }}</td><td>{{ analysis.results_no_cache_mobile.Age }}</td><td>{{ analysis.results_desktop_cache.Age }}</td><td>{{ analysis.results_no_cache_desktop.Age }}</td></tr>
                        <tr><td><strong>Age Status</strong></td><td>{{ analysis.results_mobile_cache.Age_Status }}</td><td>{{ analysis.results_no_cache_mobile.Age_Status }}</td><td>{{ analysis.results_desktop_cache.Age_Status }}</td><td>{{ analysis.results_no_cache_desktop.Age_Status }}</td></tr>
                        <tr><td><strong>CDN Cache Status</strong></td><td>{{ analysis.results_mobile_cache.CDN_Cache_Status }}</td><td>{{ analysis.results_no_cache_mobile.CDN_Cache_Status }}</td><td>{{ analysis.results_desktop_cache.CDN_Cache_Status }}</td><td>{{ analysis.results_no_cache_desktop.CDN_Cache_Status }}</td></tr>
                        <tr><td><strong>CF-Cache-Status</strong></td>
                            <td>{{ analysis.results_mobile_cache.CF_Cache_Status }}</td>
                            <td>{{ analysis.results_no_cache_mobile.CF_Cache_Status }}</td>
                            <td>{{ analysis.results_desktop_cache.CF_Cache_Status }}</td>
                            <td>{{ analysis.results_no_cache_desktop.CF_Cache_Status }}</td>
                        </tr>                        
                        <tr><td><strong>Evaluation</strong></td><td>{{ analysis.results_mobile_cache.Cache_Evaluation }}</td><td>{{ analysis.results_no_cache_mobile.Cache_Evaluation }}</td><td>{{ analysis.results_desktop_cache.Cache_Evaluation }}</td><td>{{ analysis.results_no_cache_desktop.Cache_Evaluation }}</td></tr>
                    </tbody>
                </table>

            </div>
        {% else %}
            <p class="text-muted">Enter a URL and click "Analyze Cache" to see results.</p>
        {% endif %}
    </div>

    <script>
    function copyAnalysis() {
        const analysisContainer = document.getElementById('analysis-container');
        const range = document.createRange();
        range.selectNodeContents(analysisContainer);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);

        try {
            document.execCommand('copy');
            alert('Cache analysis copied to clipboard!');
        } catch (err) {
            alert('Failed to copy content. Please try again.');
        }

        selection.removeAllRanges();
    }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

</body>
{% endblock %}
