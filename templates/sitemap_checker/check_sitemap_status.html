{% extends 'base.html' %}
{% load i18n static %}

{% block content %}
<body id="page-top">
    {% include 'topstories/ts_navbar.html' %}


<div class="container mt-5">
    <h2>Sitemap Status Checker</h2>
    <form method="post">
        {% csrf_token %}
        {{ form.as_p }}
        <button type="submit" class="btn btn-primary">Check Status</button>
    </form>

    {% if results %}
        <h3 class="mt-5">Results</h3>
        <ul class="list-group">
            {% for result in results %}
                <li class="list-group-item">{{ result }}</li>
            {% endfor %}
        </ul>
    {% endif %}
</div>
</body>
{% endblock content %}