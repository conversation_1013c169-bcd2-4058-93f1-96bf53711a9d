{% extends 'base.html' %}
{% load i18n static %}

{% block content %}
<head>
  <!-- Styles and Scripts -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <style>
    .chart-img { max-width: 100%; height: auto; margin: 20px 0; }
    .tab-pane > div { margin-bottom: 2rem; }
    .nav-tabs .nav-link { font-weight: 500; color: #495057; border: none; border-bottom: 2px solid transparent; transition: all 0.3s ease; }
    .nav-tabs .nav-link:hover { color: #0d6efd; background-color: #f8f9fa; }
    .nav-tabs .nav-link.active { color: #0d6efd; border-bottom: 3px solid #0d6efd; background-color: #fff; }
    .dashboard-header h1 { font-size: 2rem; font-weight: bold; color: #2c3e50; }
  </style>
</head>

<body id="page-top" class="px-0 py-0">
{% include 'navbar.html' %}

  {% if no_data %}
      <div class="alert alert-info text-center mt-4" role="alert">
        No data available yet for this site/source. Please import articles or wait for scheduled updates.
      </div>

    {% else %}

      <!-- 🔹 SECTION 1: Dashboard Header + Selection -->
      <div class="container-fluid px-4 pb-5">
        <!-- KEEP YOUR TABS & CHART STRUCTURE HERE -->
        <iframe id="downloadFrame" style="display:none;"></iframe>

      <div class="container-fluid py-4">
        <h1 class="mb-4 text-center">SEO Content Analysis Dashboard</h1>
        <!-- 🔹 SECTION 2: Dashboard Tabs -->
      <div class="container py-5">
        <div class="text-center dashboard-header mb-4">
          <p class="lead text-muted">
            Explore content metrics, link structure, visual elements, and publication patterns across authors and sites.
          </p>
        </div>

        <div class="d-flex justify-content-center">
          <div class="card shadow-sm w-100" style="max-width: 900px;">
            <div class="card-body">
              <form method="get" class="row g-3 align-items-end">
                <div class="col-md-6">
                  <label for="site" class="form-label">Select a Website:</label>
                  <select name="site" id="site" class="form-select" onchange="this.form.submit()">
                    {% for name, key in websites.items %}
                      <option value="{{ key }}" {% if selected_site == key %}selected{% endif %}>{{ name }}</option>
                    {% endfor %}
                  </select>
                </div>
                
                <div class="col-md-6">
                  <label for="source" class="form-label">Choose Source:</label>
                  <select name="source" id="source" class="form-select" onchange="this.form.submit()">
                    <option value="api" {% if selected_source == "api" %}selected{% endif %}>API</option>
                    <option value="rss" {% if selected_source == "rss" %}selected{% endif %}>RSS</option>
                  </select>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Download Buttons -->
        <div class="mt-4 text-center">
          <a href="?export=csv&type=author" class="btn btn-outline-primary btn-sm me-2">Download Author CSV</a>
          <a href="?export=csv&type=article" class="btn btn-outline-primary btn-sm me-2">Download Article CSV</a>
        </div>

        <iframe id="downloadFrame" style="display:none;"></iframe>

      </div>

        <!-- Nav tabs -->
        <ul class="nav nav-tabs" id="analysisTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#tab-authors" type="button" role="tab">Author Overview</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tab-structure" type="button" role="tab">Content Structure</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tab-links" type="button" role="tab">Linking Analysis</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tab-media" type="button" role="tab">Visual & Media</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tab-behavior" type="button" role="tab">Publication Behavior</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tab-audit" type="button" role="tab">Audit Summary</button>
          </li>
        </ul>


      <!-- Tab panes -->
      <div class="tab-content mt-4">

        <!-- Author Overview -->
        <div class="tab-pane fade show active" id="tab-authors" role="tabpanel">
          <div class="alert alert-info">
            This section provides a high-level overview of author performance, including total contributions, average content metrics, and publishing activity trends.
          </div>
          <div class="row">
            <div class="col-12 mb-4">
              <div class="card shadow-sm"><div class="card-body">{{ author_table|safe }}</div></div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card shadow-sm h-100 d-flex justify-content-center align-items-center">
                <div class="card-body w-100">{{ total_content_metrics_chart|safe }}</div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card shadow-sm h-100 d-flex justify-content-center align-items-center">
                <div class="card-body w-100">{{ per_article_avg_metrics_chart|safe }}</div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card shadow-sm h-100 d-flex justify-content-center align-items-center">
                <div class="card-body w-100">{{ author_activity_chart|safe }}</div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div class="card shadow-sm h-100 d-flex justify-content-center align-items-center">
                <div class="card-body w-100">{{ missing_elements_chart|safe }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Content Structure -->
        <div class="tab-pane fade" id="tab-structure" role="tabpanel">
          <div class="alert alert-info">
            Analyze the structural elements of articles, such as heading tags (H2s), paragraph counts, and text emphasis (strong tags), to assess content readability and hierarchy.
          </div>          
          <div class="row">
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ h2_heatmap|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ paragraph_count_chart|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ paragraph_length_chart|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ strong_tags_chart|safe }}</div></div></div>
          </div>
        </div>

        <!-- Linking Analysis -->
        <div class="tab-pane fade" id="tab-links" role="tabpanel">
          <div class="alert alert-info">
            Explore internal link usage, including total links, theme-related links (`/tema/`), and related article suggestions, to evaluate SEO interlinking strategies.
          </div>          
          <div class="row">
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ link_heatmap|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ tema_heatmap|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ stacked_links_chart|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ related_links_chart|safe }}</div></div></div>
          </div>
        </div>

        <!-- Visual & Media -->
        <div class="tab-pane fade" id="tab-media" role="tabpanel">
          <div class="alert alert-info">
            Review how visual elements are used in articles, focusing on total image usage and the presence of lazy-loaded images to improve performance and UX.
          </div>          
          <div class="row">
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ lazy_images_chart|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ total_images_chart|safe }}</div></div></div>
          </div>
        </div>

        <!-- Publication Behavior -->
        <div class="tab-pane fade" id="tab-behavior" role="tabpanel">
          <div class="alert alert-info">
            Understand publication patterns by analyzing the timing of article releases by hour and weekday, which can inform editorial and scheduling strategies.
          </div>          
          <div class="row">
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ hourly_distribution_chart|safe }}</div></div></div>
            <div class="col-md-6 mb-4"><div class="card shadow-sm"><div class="card-body">{{ weekday_distribution_chart|safe }}</div></div></div>
          </div>
        </div>

        <!-- Audit Summary -->
        <div class="tab-pane fade" id="tab-audit" role="tabpanel">
          <div class="alert alert-info">
            This tab offers a full table of articles with key audit metrics, including structural completeness and SEO elements, to support editorial quality control.
          </div>          
          <div class="row">
            <div class="col-12 mb-4"><div class="card shadow-sm"><div class="card-body">{{ article_table|safe }}</div></div></div>
          </div>
        </div>

      </div>
      </div>

      <script>
        function addDropdownFilters(tableId, filterCols) {
          const table = $(`#${tableId}`).DataTable({ paging: false, searching: true, orderCellsTop: true, fixedHeader: true });
          $(`#${tableId} thead tr`).clone(true).addClass('filters').appendTo(`#${tableId} thead`);

          table.columns().every(function () {
            const colIdx = this.index();
            const headerText = $(this.header()).text().trim();
            const filterCell = $('.filters th').eq(colIdx).empty();

            if (filterCols.includes(headerText)) {
              const column = this;
              const select = $('<select><option value="">All</option></select>').appendTo(filterCell)
                .on('change', function () {
                  const val = $.fn.dataTable.util.escapeRegex($(this).val());
                  column.search(val ? '^' + val + '$' : '', true, false).draw();
                });

              column.data().unique().sort().each(function (d) {
                if (d && d !== "NaT") {
                  select.append('<option value="' + d + '">' + d + '</option>');
                }
              });
            } else {
              $('<input type="text" placeholder="Search..." />').appendTo(filterCell)
                .on('keyup change', function () {
                  if (table.column(colIdx).search() !== this.value) {
                    table.column(colIdx).search(this.value).draw();
                  }
                });
            }
          });
        }

        $(document).ready(function () {
          addDropdownFilters("articleTable", ["Author", "Category", "Date Published", "Missing Count"]);
          addDropdownFilters("authorTable", ["Author"]);
        });

        
      </script>
    </div>
  {% endif %}
{% endblock %}





