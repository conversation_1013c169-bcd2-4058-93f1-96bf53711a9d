from openai import OpenAI
from decouple import config
import os
import time
import re

def format_sources(filenames):
    """
    Formats the filenames into a list of dictionaries containing the file name
    and the corresponding search link for Google Docs.
    """
    formatted_sources = []

    for filename in filenames:
        match = re.search(r'\(([\w-]+)\)', filename)
        file_id = match.group(1) if match else None
        file_name = filename.replace('.txt', '').replace(f"({file_id})", '').strip() if file_id else filename.strip()

        if file_id:
            search_link = f"https://docs.google.com/document/d/{file_id}/"
            formatted_sources.append({'file_name': file_name, 'search_link': search_link})
        else:
            formatted_sources.append({'file_name': file_name, 'search_link': None})
        
    print(formatted_sources)

    return formatted_sources


def get_openai_api_key():
    """Retrieves the OPENAI_API_KEY from environment variables."""
    api_key = os.getenv("OPENAI_API_KEY") or config("OPENAI_API_KEY", default=None)

    if not api_key:
        raise ValueError("Missing OpenAI API Key! Ensure it's set in the environment variables.")

    return api_key


def initialize_client(api_key=None):
    """Initialize OpenAI API client with a valid API key."""
    if api_key is None:
        api_key = get_openai_api_key()

    return OpenAI(api_key=api_key)


def create_file(client, file_path):
    """Uploads a file to OpenAI's file storage."""
    with open(file_path, "rb") as file_content:
        result = client.files.create(file=file_content, purpose="assistants")
    #print(f"File ID: {result.id} for {file_path}")
    return result.id


def get_or_create_vector_store(client, name="SEO Knowledge Base"):
    """Checks if a vector store exists; if not, creates one."""
    vector_stores = client.vector_stores.list()

    for store in vector_stores.data:
        if store.name == name:
            #print(f"Using existing vector store: {store.id}")
            return store.id

    vector_store = client.vector_stores.create(name=name)
    print(f"Created new vector store: {vector_store.id}")
    return vector_store.id


def add_file_to_vector_store(client, vector_store_id, file_id):
    """Adds a file to a vector store."""
    client.vector_stores.files.create(vector_store_id=vector_store_id, file_id=file_id)
    #print(f"File {file_id} added to vector store.")


def wait_for_file_processing(client, vector_store_id, file_id):
    """Waits until the file processing is completed."""
    while True:
        result = client.vector_stores.files.list(vector_store_id=vector_store_id)
        file_status = None

        for file in result.data:
            if file.id == file_id:
                file_status = file.status
                break

        print(f"File processing status: {file_status}")

        if file_status == "completed":
            return True
        elif file_status == "failed":
            raise RuntimeError("File processing failed.")

        time.sleep(5)  # Wait and check again


def add_files_to_empty_vector_store(client, vector_store_id, input_folder="drbot/input/"):
    """If the vector store is empty, uploads all .txt files from input_folder."""
    existing_files = client.vector_stores.files.list(vector_store_id=vector_store_id)

    if len(existing_files.data) == 0:
        #print("Vector store is empty. Adding new files...")

        for filename in os.listdir(input_folder):
            if filename.endswith(".txt"):
                file_path = os.path.join(input_folder, filename)

                file_id = create_file(client, file_path)
                add_file_to_vector_store(client, vector_store_id, file_id)
                wait_for_file_processing(client, vector_store_id, file_id)

        #print("✅ All files added to the vector store!")
    else:
        print("Vector store already contains files. No action needed.")


def get_response(client, query, vector_store_id):
    """Gets a response from OpenAI using file search with vector store."""
    response = client.responses.create(
        model="gpt-4.1-mini",
        input=query,
        tools=[{
            "type": "file_search",
            "vector_store_ids": [vector_store_id],
        }],
        include=["file_search_call.results"]
    )

    #print("DEBUG - Full OpenAI Response:", response)
    #Accessing the filename from the openai object
    #print("DEBUG - FileName:", response.output[0].results[0].filename)

    if hasattr(response, 'output') and response.output:
        for output_item in response.output:
            #print(output_item)
            if output_item.type == "message" and hasattr(output_item, 'content'):
                #print("DEBUG - OpenAI OUTPUT Response:", output_item)
                text = output_item.content[0].text
                filename = output_item.content[0].annotations[0].filename
            pass
            
            filenames_list = []
            for result in response.output[0].results:
                filename = result.filename
                filenames_list.append(filename)

            filenames_set = set(filenames_list)
            filenames = list(filenames_set)
            
            #print("RESPONSE.OUTPUT: ", filenames)


            
            
                
        return text, filenames

    return "I'm sorry, I couldn't find an answer."


def process_input_files(client, input_folder, vector_store_id):
    """Processes all .txt files in the input folder and adds them to the vector store."""
    for filename in os.listdir(input_folder):
        if filename.endswith(".txt"):
            file_path = os.path.join(input_folder, filename)
            file_id = create_file(client, file_path)
            add_file_to_vector_store(client, vector_store_id, file_id)
            wait_for_file_processing(client, vector_store_id, file_id)


def main():
    api_key = get_openai_api_key()
    client = initialize_client(api_key)
    input_folder = "drbot/input/"  # Folder containing text files
    
    vector_store_id = get_or_create_vector_store(client)  # Reuse or create
    
    process_input_files(client, input_folder, vector_store_id)
    
    query = "What is the best way to optimize images for best SEO performance?"
    response = get_response(client, query, vector_store_id)
    
    #print(response)


if __name__ == "__main__":
    main()
