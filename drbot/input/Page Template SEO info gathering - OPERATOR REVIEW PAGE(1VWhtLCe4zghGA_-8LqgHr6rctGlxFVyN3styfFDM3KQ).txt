﻿[General / SOP] Page Template SEO info gathering - OPERATOR REVIEW PAGE


* OPERATOR REVIEW PAGE 


* Meta Title
   * max 60 characters
   * main keyword - the name of the bookmaker
   * additional keywords that are bookmaker related: review, bonus, free bet. Besides keywords, we can display a current offer - current bonus/ bonus code, etc. If we display an offer in MT or MD, we must update regularly. 
   * date -  current month and year
* Meta Description
   * should be around 160 characters (min 50 & max 160 characters)
   * should be unique for each page and summarises the page content 
   * main keyword - the name of the bookmaker
   * call to action keywords (Use welcome bonus right now!/ Bet on!/ Bet in/ Join us!/ Learn how to win, etc.)
   * market 
   * display a current offer - current bonus/ bonus code, etc. If we display an offer in MT or MD, we must update regularly. 
* Content
      * ABOVE THE FOLD
   * Logo - the unique logo of the site
   * Navigation bar - containing all the most important pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussion/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. Navigation bar doesn’t have to contain all these pages. It depends from site to site. 
   * <H1>
      * No character limitations but try to keep it visually appealing, so make it a manageable length. Make h1 stand out. Make the letters more significant than the rest of the text and headings.
      * The name of the bookmaker + “review”
      * Market
         * Other recommendations regarding headings structure:
      * Headings should describe the content that follows them. It is a good practice to have headings in form of questions. The first <p> below will then answer that question. This tactic is good because it can lead to gaining  Feature snippets and is quite a user-friendly way of writing. 
      * Only use one <h1> for each web page.
      * Don’t skip heading levels. For example, using a <h2> followed by an <h4>. It’s important that your headings are logical and hierarchical. This means that <h2> always follows <h1>.
      * Make sure each heading is unique on individual web pages
      * Make headings clear, useful and relevant.
   * Bookmaker Logo 
   * Intro text - Short paragraph (teaser, short info about the bookmaker - 2 or 3 sentences with keyword) - good to have, also a good place to put some links 
   * Rating
   * Date - should be updated accordingly. Having the author's name on the page is also good practice (EAT). With the new development of E-E-A-T, authors will become a must-have virtually for all pages. Only sites with actual expertise will rank, so I think authors should be there.
   * Short review - bullet point list or table - summary review
      * All tables must be created in <table> tags hardcoded in HTML, and not in <div> or any other tag.
   * CTA - get bonus button/ sign-up button/ cash in now/ free spins, etc. 
      * BELOW THE FOLD
Any widget/block/image below the fold should be lazyloaded.
   * Pros&Cons - optional
   * Instructions - how to register/ how to bet/ how to get the bonus/ how to sign-up = step-by-step guides that we put in lists (<ul> or <ol>) with some screenshots  = good to have but optional 
   * Explanations/ Detailed description of what the bookmaker does <p> = good to have but optional
      * Withdrawals and deposits (explaining all payment options)
      * Website and app
      * Live betting and Live streaming
      * Customer service
      * Loyalty program if the bookmaker has it 
      * Betting account verification
   * Other offers: WC bonus/ Free spins/ Casino bonus/ Welcome bonus/ Promo code  ⇒ if the bookmaker has them
   * FAQ ⇒ headers (h2, h3) as questions and content (<p>) as an answer - recommendation not a obligation
* Schema - not all schemas are required 
   * Breadcrumbs - best practice. It helps bots/ search engines better understand the hierarchy of the pages
   * FAQ - best practice
   * Review snippet
   * Product snippet
   * HowTo schema - good practice. But if we add this schema on the page we do not have to implement FAQ schema also. We shouldn't have both, as then neither may get picked up.
* Images
   * Ensure the ALT text is optimised
   * Use only high-resolution images. The recommendation is to use images of the people. 
   * Compress images to load fast
   * Every image below the fold should be lazyloaded
   * Optimise Your logo
   * SOPs for image optimisation:
Optimizing images for best SEO performanceOptimizing images for best SEO performance - part 2


General recommendations:
* Table of content with anchor links - optional - good to have if the page is too long. It is a good practice to make it foldable to save some space. This feature also helps to gain sitelinks in the SERP which automatically gives us more space and more exposure.
* Any widget/block/image below the fold should be lazyloaded.
* Subpages that address deep-dive and niche keywords. For Example “bonus code”, etc.
* Research the market and competitors 


Navigation bar - containing all the most important pages of the site: 
Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussions/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. 
Navigation bar doesn’t have to contain all these pages. (Underlined are the most important ones for our business). 
For an affiliate site, it is important to put all commercial hub pages into the navigation bar because those pages are the most important for the business. 
Which links should be in the navigation bar depends from site to site. For example, BettingExpert has specialised in betting tips, but some other site does not even have tips at all.
The pages that are most common for the majority of affiliate sites are: 
Bookmakers hub and the good practice is to have a drop-down menu with the most important bookmakers
Bonus hub - the most common offer that affiliate sites are giving to the users is bonus codes (main offer that site has)
Betting tips/ odds (other offers that site has)
Casino hub - if a site has a casino section at all. Some of the sites don’t.
All these pages above are commercial because we are offering something to our users and the main goal is to get people on those pages where they can use our services.
Other important pages are informative pages where we show our expertise in the topic (in this case sports or casino), so we have also these pages n the Nav bar: 
News hub
Events page
Analysis
Live streaming
Guides








* HOMEPAGE
* OPERATOR REVIEW HUB
* OPERATOR BONUS HUB
* OPERATOR BONUS PAGE