﻿Initial audit
Necessary tools
The audit process
Step 1 - Organic traffic trend checkup
Google Analytics - organic traffic checkup
Google Search Console - organic traffic checkup
SEMrush - organic traffic
Step 2 - Commercial organic traffic proportions
Google Analytics - commercial organic traffic proportions checkup
Google Search Console - commercial organic traffic proportions checkup
SEMrush - commercial organic traffic proportions checkup
Step 3 - Commercial ranking performance
SEMrush - commercial ranking performance checkup
Google Search Console - commercial ranking performance checkup
Soft audit
Step 1 - Organic traffic trend checkup
Google Analytics
Google Search Console
Step 2 - Top 5 Organic Conversion Share (12 months)
We are using Google Analytics in order to do this step
Step 3 - Top 5 Organic Traffic Page Proportions (12 months)
Step 4 - Traffic Channel Share (12 months)
Step 5 - Sitewide Organic Traffic Trend (YoY)
Step 6 - Sitewide Organic Conversion Trend (YoY)
General Note - SOFT AUDIT


Initial audit
Necessary tools
The first thing to watch out for is whether you have Google Analytics and Google Search Console access. If so, make sure that the data is tracked correctly and that you have enough to perform the audit. If there is an issue, you will need to rely on tools for traffic estimates, specifically SEMrush or Ahrefs.
The audit process
Step 1 - Organic traffic trend checkup
The point of this step is to check whether there were any historical or recent big dips in traffic, seemingly correlated with any algo updates by Google.
In order to do this, there are 3 main tools that we are using, those being : Google Analytics, Google Search Console and Semrush/Ahrefs. For the purpose of further understanding this, we will try to touch upon all of those tools except for Ahrefs (because Ahrefs and Semrush are pretty much similar in terms of usability and the options they provide). 
One further note when doing traffic analysis is that the preferred time period that should be analyzed is the last 12 months. Have this in mind when using the aforementioned tools. 

Google Analytics - organic traffic checkup  
When using GA for this report we should follow the path : Acquisition>>All Traffic>>Channels>>Organic, since it has more precise data. Also we will be checking sessions so we should sort by it and in the table change the chart from Users to Sessions so we could analyse data correctly. 
1. First, when you open up GA, on the home page screen on the left side click on the “Audience” tab; 
  
________________
Google Search Console - organic traffic checkup
Here is how you use GSC in order to analyse traffic trendline : 
   1. On GSC homepage click on Search Results under Performance tab ;
  
   2. Then select the desired time period ;
  
   3. And there you have it 
  
Here in this overview you can see the trendline for organic clicks and impressions, as well as the average position movement for the site. Similarly to the previous section, the most important thing is to see if there were some big drops in impressions, clicks and the average position, and if so, you should investigate why they happened and whether they have anything to do with known Google updates. 
Note that impressions are sometimes triggered by sitelinks wich either stem from related pages or on-page ToC navigation. This can sometimes skew the impression count so it might seem there is growth where ther is none. There are two ways to check this:
      1. Try to exclude every URL beginning with “#” from the console. 
  

This should exclude the impressions from ToC sitelinks. 
This still leaves regular sitelinks, but it should give you an idea how many sitelink impressions and how many actual impressions the site has.
      2. Look for big drops in CTR year-over-year. Usually, the CTR drops drastically because the number of clicks remains the same, and the impression count skyrockets ue to the sitelinks (1 main blue link + 4 sitelinks = 5 impressions)
SEMrush - organic traffic 
We should always check SEMrush for any dramatic traffic drops correlated closely with any major updates by Google.
Place the domain in the search bar of SEMrush and select the suggestion that has “Domain Overview” written next to it.
  

You should get a dashboard that has the following graphs in its right hand side. Select the data for “All Time” to be able to get a better understanding of the domain's traffic and organic ranking development and you can narrow down the period later if you want to further investigate a specific suspicious period.
  

The Organic Keywords report is quite useful for this as it gives you a timeline of all the Google updates (confirmed and assumed), which you can click on to see what they were about. You can also use the external link to read more about the update.   
The rating system for this check should be the following:[a][b]
      * 3: High performance impact
      * 2: Medium performance impact
      * 1: Low performance impact
      * 0: No problems/check passed
In case there was a big drop in performance regarding organic sessions and we can see that website was hit by Google algorithm we should give a rating 3: High performance impact. Also in case that we see that trend is negative and website is constantly significantly dropping over long period of time.
Rating 2: Medium performance impact case would be if the website had a big drop in the past but it didnt recovered or it is semi recovered.
Rating 1: Low performance impact case would be if website had a drop in the past but it recovered almos fully or fully . Or case that website had smaller drop or fluctuations but no major drops and lately have positive trend.
Rating 0: No problems/check passed case would be if the website have constant positive trend and stil growing.
Take in to account that we are analyzing 2 factors Traffic Changes Correlated with Google  algorithm updates and Traffic trend in last 12 months and they both are affecting the rating. Pay attention on seasonality because it might happen that website is ranking good for some major events for tips pages, odds pages or something else, and it might leed you to think that website had a big drop but it was just seasonality. Also always check year before and whole traffic trend that we have data because it will give you insight what actually happened over time. It can happen that website have positive trend in the last 12 months but before it had huge drop over 500%. 


Write down the conclusions in the field under “Analysis”.
Step 2 - Commercial organic traffic proportions
For this check, we will be using the same tools as we did before, except our goal is to check the top 10 pages bringing in the most organic traffic, see how many of these are commercial and see what percentage of those 10 pages are commercial.
Google Analytics - commercial organic traffic proportions checkup
Here the before-explained process is the same, just after doing everything like before, we click Acquisition>>All Traffic>>Channels>>Organic. We should then filter Primary DImension instead of “keyword” to “landing page” so the list will show the top 10 pages by sessions.  


Google Search Console - commercial organic traffic proportions checkup 
In Google Search Console, under the “Performance” tab on the left, we select “Search results”. Then adjust the date to “Last 12 months” and then under “Pages” tab below we can see “Top Pages” listed. 
  











SEMrush - commercial organic traffic proportions checkup
First we type in the website that we want to look up and change to “root domain” and we click “Search”. Then we click on the “Organic Research '' tab and click on the “Pages” tab. This will show all of the top pages for that domain. 
  

The rating system for this check should be the following:
3: High performance impact
2: Medium performance impact
1: Low performance impact
0: No problems/check passed


The rating is defined by analyzing two factors. Frst one is number of commercial pages in the top 10 pages receiving organic traffic and second one is sum of their percentages in comparison of total percentage  of organic traffic. We need also to take into account percentage distribution between the commercial pages received organic traffic.
The ideal case would be to have all 10 pages commercial and they received a big part of organic traffic which would be rated as rating 0: No problems/check passed.
The worst case would be that none of the pages are commercial or just one or two are commercial but they have a very low percentage of all traffic which would be rated as rating 3: High performance impact.
There are no clear boundaries where ratings 1 and 2 begin and end. It is up to individual case to case and because we are analyzing 2 factors and many other factors in between so there are many combinations and variations.
Rating 1: Low performance impact would be for an example that we have 6 commercial pages with percentage of 30% of total organic traffic. But we need also to consider percentage  distribution between them. If one is dominating and have 28% and rest 5 pages less then 1% that is not good and it might be rated as 2 because it is potential treat if that one best rated pages dropp we have big problem. Or it can be still rated as 1 but with comment in the note that we have potential problem and described more in detail.
Rating 2: Medium performance impact example would be if we have 4 commercial pages with percentage of 10% of total organic traffic. 


[c][d]
Write down the explanation in the field next to the rating if there is anything to note.
Step 3 - Commercial ranking performance
The point of this part is to see how well and for which keywords are Commercial pages performing and evaluate them accordingly.
In this step, we are using SEMrush or Google Search Console to check those keywords that are performing well on commercial pages and estimate how well they are performing. 
SEMrush - commercial ranking performance checkup
________________


      * Under the “Organic Research” tab in the left sidebar, we can see the “Organic Pages” report. 


  

      * Then we click on the top 10 pages and check them one by one for the top-ranking keywords.
  

________________


Google Search Console - commercial ranking performance checkup
      * In GSC we go to the “Performance” tab, then “Search results”, adjust the date and click on the “Pages” tab to see what are the top 10 best-performing pages. 
     From there we select them one by one, and this will further filter our results to just that one page as we can see here. 
  
      * And there we click on the “Queries” tab which will give us queries for which this page is ranking and then we can analyze them.
Commercial pages are usually identified by containing keywords that belong to one of these types of pages:
"[bookmaker/casino brands present on the site]"
- "bonus"
- "offer"
- "tip"
- "preview"
- "stream" / "streaming"
In the template, we already have their respective columns defined. They look like this: 
  



The evaluation is done by looking at keyword rankings on those pages. So if keywords are:
         1. Within the Top 3, they are “HIGH”
         2. Within the top 10, they are “MEDIUM”
         3. Below the top 10, they are “LOW”
         4. Below the top  ~30, they are “Dead” meaning they don’t receive almost any traffic
         5. If we have no ranking at all they are “N/A” meaning not available
Write down the explanation in the field next to the rating in which we will mention keywords for the specific pages that are ranking good along with their impressions in brackets as seen in this example:
[e][f]
  

Here take in consideration amount of words that you found ranking high, medium, or low and their search volumes for exact market. Also pay attention to how important is that bookmaker/operator on that exact market in sense are they ranking for queries related to bet365 or some bookmaker that is not so popular and it doesn’t attract so much organic search volume.
NOTE: It’s quite important to write down the summary in the cell at the very bottom of the table for the Traffic section of the audit. These written summaries are often more important than the ratings themselves.
The rating system for this check should be the following:
3: High performance impact
2: Medium performance impact
1: Low performance impact
0: No problems/check passed
This is individual from website to website based on the website type. The tyes of pages are not important the same for an example to betting type of website live streaming pages might not be so important while to the iGamming website they are probably the most commercial pages. 
Rating 0: No problems/check passed would be in case that all page types are present on the website and rankings are high or few of them are rating medium.
Rating 1: Low performance impact would be the case if one of page types are N/A or low or medium but other are ranking mostly high or medium combination.
Ranking 2: Medium performance impact would be the case if many of page types are N/A or low.
Ranking 3: High performance impact case would be if many of page types are N/A or dead or low. 
________________
Soft audit 


Step 1 - Organic traffic trend checkup
Check Semrush and/or Google Analytics for historic & recent organic traffic performance for each page type :


- What's the current traffic trend from a 12-month perspective?
- Add a note for each analyzed page type, describing any filtering made


Pages to analyze: 
         1. Betting tips/Previews;
         2. Betting operator/Overview;
         3. Operator/Slot Review;
         4. Bonus/Offer;
         5. Knowledge/Guide;
         6. Live streaming;
         7. Bonus/Free Bets Overview;
         8. Misc ( News, et al. ) 


Google Analytics 
In order to do this we go to the Acquisition>>All Traffic>>Channels>>Organic. Also here we could filter out whole sections if the URl structure is done properly by applying advanced filters like “Regular Expressions” and other options. 
  
________________



Now, based on the looks of the trendline, make sure to describe it. Feel free to add any comments further if you find them useful.


Here is what it looks like in the template : 
  

  



Note: In case there are no specific pages that we need to analyze, make sure to leave “N/A” in the corresponding column. As seen in the picture above, under “Live streaming”.
Google Search Console 
In GSC we go under the Performance tab, then Search Results. Then we select the time period “Last 12 months”, select the page that we want and further analyze the trendline.
  
 Also, here we can select “compare” but GSC doesn’t let you compare further than one year in the past. 
  



[g][h]
The rating system for this check should be the following:
3: High performance
2: Medium performance
1: Low performance
0: Critical
Rating 3: High performance would be the case that all or almost all major page types are present and have increasing or stabile trend.
Rating 2: Medium performance would be the case that all commercial page types are present and have increasing or stabile trend, even if one have declining trend.

Rating 1: Low performance would be the case that some commercial page types are N/A  and few are having declining trend or rated dead.
Rating 0: Critical performance would be the case that many commercial page types are N/A  and most of them are having declining trend or rated dead .

________________
Step 2 - Top 5 Organic Conversion Share (12 months)


In this step, we need to check the top 5 pages that are receiving the most organic conversions and list the percentage proportion of organic conversions per page.


We are using Google Analytics in order to do this step


First of all, make sure that you are looking at the “Organic Traffic” segment. After that you go to the “Conversions” tab on the left, then “Goals'' and select “Overview”. Here under “Goal completion location” you will have a list with pages that made conversions. 
  



In case where the Goals are not set we should check Events and Ecommerce instead to see if there are any commercial tracking set so we can perform this check. 


Events are checked by following path : Behaviour- Events-Overview


  

Top events are checked also : Behaviour- Events- Top Events
  



And E-commerce is checked by following this path : Conversions- E-commerce- Overview.
Here in this example it is not set and it’s put in here just as an example.
  
________________

You check the top 5 of them and make sure to enter the corresponding data in percentages as seen here: 
  



The rating system for this check should be the following:
3: High performance
2: Medium performance
1: Low performance
0: Critical
Here we should consider total percentage of conversions on those 5 pages, conversion percentage distribution ratio between the pages and what type of the pages are most common or best rating. If one page is dominating with 30% and the rest have ~1% that is potentially dangerous because if that one page dropp we will be in a very bad situation.
Rating 3: High performance would be the case if most of pages are commercial page types corresponding to the website type, distributed properly with steady declining in percentages and they have ~40% in total. 
Rating 2: Medium performance would be the case if most of pages are commercial page types corresponding to the website type, distributed semy properly with steady declining in percentages and they have ~30% in total.
Rating 1: Low performance would be the case if most of pages are not commercial page types corresponding to the website type, distributed in a way where one page precentage is dominating and they have ~20% in total.
Rating 0: Critical performance would be the case if most of pages are not commercial page types corresponding to the website type, distributed in a way where one page precentage is dominating and they have ~10% in total and less.


[i][j]
________________
Step 3 - Top 5 Organic Traffic Page Proportions (12 months)
This step is fairly easy, you just need to go to  : Acquisition>>All Traffic>>Channels>>Organic
Primary Dimension: Landing page
Checking Sessions in the chart and sorting by sessions in the table.
  



Check the top 5 pages among those and use their percentages to fill the columns in the template.


  

The rating system for this check should be the following:
3: High performance
2: Medium performance
1: Low performance
0: Critical
Here we are checking are there commercial pages among the top five pages which are getting the most organic traffic percentage of the whole website, which pages are they and do we see them in the previous check, and which page type are they.
Rating 3: High performance would be the case if all 5 pages are commercial.
Rating 2: Medium performance would be the case if 3~4 pages are commercial.
Rating 1: Low performance would be the case if 2 pages are commercial.
Rating 0: Critical performance would be the case if none or 1 page are commercial.

[k][l]
Step 4 - Traffic Channel Share (12 months)


In this step, we need to list the session % proportions for each channel. 
For this, we are going to use Google Analytics as the main tool, but also Semrush could be used if needed. 


First, we open Google Analytics, then on the left side we go to “Acquisition”, then “All Traffic”, and then “Channels”. On this page under the “Sessions” tab, we can see how many sessions each channel bought.
  



Then we need to fill the corresponding columns with percentages for each channel. This will give us a general idea about which channel brings the most traffic to the site.
  



Note: For this step, we need to turn off the “Organic Traffic” segmentation! 
The rating system for this check should be the following:
3: High performance
2: Medium performance
1: Low performance
0: Critical
        Here we are checking how much organic traffic proportion percentage we have comparing to the other channels. We can also see direct traffic percentage and it can tell us how popular is the website on the market, is the website popular on social networks, do they work on email marketing, do they have paid campaigns etc.
Rating 3: High performance would be the case if organnic traffic is dominating is ~40% and more.
Rating 2: Medium performance would be the case if organnic traffic is ~30%.
Rating 1: Low performance would be the case if organnic traffic is ~20%.
Rating 0: Critical performance would be the case if organnic traffic is low ~10% and less.







[m][n]
Step 5 - Sitewide Organic Traffic Trend (YoY)
In this step, we need to check what Organic Traffic Trend looks like compared to the previous year. We are looking at the sessions for the selected date (preferably 12 months period ) and comparing them to the same period for the previous year.


This is done using Google Analytics as the main tool if possible.


In GA we go to the :  Acquisition>>All Traffic>>Channels>>Organic.
Set a 12 month period and compare it to the previous year.
Checking Sessions in the chart and sorting by sessions in the table
  
Then we just change the time period to compare. 
  





When all of this is checked, fill the columns with the data that's required.


  



[o][p]
Step 6 - Sitewide Organic Conversion Trend (YoY)


Here we need to compare Organic Conversions for the time period that we are analyzing with the same time period from the previous Year. 


We go to “Conversions”, then “Goals” then we select “Overview”. Here we make Organic Traffic segmentation and select time periods that we wish to compare in the same manner as we did in step 5.
  
 Then we just hover over the “Organic Traffic” segmentation tab, check the data and fill the columns in the template.
  



Note: Make sure “All goals “ are selected under the Goal Option box. 
  



The rating system for this check should be the following:
         * 3: High performance impact
         * 2: Medium performance impact
         * 1: Low performance impact
         * 0: No problems/check passed[q][r]


General Note - SOFT AUDIT


Under this section, you will enter whichever information you think will be useful. Make this a summary of the entire audit - try to mention everything that might be useful for writing the general summary of the entire soft audit. Focus on the most positive and the most negative aspects that might help us in price negotiation.






























































Soft Audit - Update


The Google Analytics tool had an update and the new version GA4 (Google Analytics 4) rolled out. This is the guide for performing the traffic check analysis in the GA4.


         1. Organic traffic trend (12 months)
In order to complete this step you should navigate to GA4—>Life Cycle—>Acquisition—->Traffic Acquisition 
You will be given the following report:
  



What you should first do is change the date range for the last 12 months:


  

Then you will see the organic traffic trend on the following graph:
  

This will be helpful for you in order to see what is the traffic trend and whether there have been any fluctuations in regard to organic traffic.


In the template, it is stated that you’ll also need to check the traffic trend for the following types of traffic:
         * Google News
         * Discover 
         * Images
          
You can check that in GA4 also. However, in order to do this you will need to know what the other traffic types names in GA4 refer to.
         * Direct traffic - It mostly refers to website visitors who land on your website after typing in your URL directly into their browser and we can also include there Google Discover
         * Organic Social - Organic Social is the channel by which users arrive at your site/app via non-ad links on social sites like Facebook or Twitter.
         * Referral Traffic - Visits to your website that result from a click on a link, button, or other elements from a domain that is not associated with your GA account.


However, the primary tool you should use for this check (GN, Discover, and Images        ) is GSC.




Top 5 Organic Conversion Share (12 months)
Here we’ll need to check the top 5 landing pages receiving the most organic conversions.


In order to do this, we’ll have to make a report in GA4. 


GA4—>Explore—>Blank Report


In the exploration blank report, you should:
         * Enter an Exploration name, e.g. Betarades - top 5 organic conversions pages
         * When it comes to the Date Range, you’ll need to choose a ‘Custom’ part and add this day last year so that you’ll be given the data for the last 12 months
  

         * Add Dimensions
         * Landing Page + query string
         * Event name
         * First user source/medium (you’ll need this one since you’ll need to check organic conversions)
  



         * Add Metrics
         * Active Users
         * Conversions
         * Event Count
         * User Conversion Rate
  



         * Add Rows
         * Event name
         * Landing page + query
         * First user source/medium


  

         * Add Values
         * Active Users
         * Conversions
         * Event Count
         * User Conversion Rate
  



         * Add Filters
         * Event name matches
         * CTP


         * First user source/medium matches
         * google/organic






After you’ve added this information, you will be given the following report:
  



You should filter this one out by Conversions, and export this information into Google Sheets. Here is a link leading to the sample example -  Top 5 pages receiving the most conversions - Sample
  





In order to see the percentage of conversions per page, you’ll need to find Conversions in the table to see the total numbers and then divide the number of conversions per page by the total number of conversions.
  





Top 5 Organic Traffic Page Proportion (12 months)


In order to do this, we’ll have to make a report in GA4. 


GA4—>Explore—>Blank Report


In the exploration blank report, you should:


         * Enter an Exploration name, e.g. Betarades - top 5 pages receiving the most organic traffic
         * When it comes to the Date Range, you’ll need to choose a ‘Custom’ part and add this day last year so that you’ll be given the data for the last 12 months
  

         * Add Dimensions
         * Landing page + query string
         * First user source/medium
  



         * Add Metrics
         * Active Users
         * Sessions


  



         * Add Rows
         * First user source/medium
         * Landing page + query string
  

         * Add Values
         * Active Users
         * Sessions
  

         * Add Filters
         * First user source/medium
         * google/organic


Your report will look like this:


  



Note: You’ll need to filter this report out either by Active Users or Sessions, depending on what you’re searching for. Here it is filtered by Active Users.




Here is a Sample. You can take a look at it - Top 5 pages receiving the most organic traffic - Sample


In order to be given percentages, you’ll need to divide the number of organic traffic per page by the total number of organic traffic - filtered out by Active users.
  





Traffic Channel Share (12 months)
GA4—>Reports—>Life Cycle—>Acquisition—->Traffic Acquisition




You’ll the following table:
  

You’ll need to take a look at Sessions and divide the number of sessions of each traffic channel by the total number of sessions in order to get the percentage to be added to the table. 
Sitewide Organic Traffic Trend (YoY)


GA4—>Reports—>Life Cycle—>Acquisition—->Traffic Acquisition


When it comes to the Date Range, you’ll need to go to the Compare—>Same period last year 
  



After adding the Date Range, you’ll be given the table with all Traffic channels and the data for the sessions in the two periods you need plus the % of change. 
So what you need to do is to take a look at the Organic Search and Sessions and add the numbers from there in the table.
  

         * GA4 Example Report - can be accessed through the SCAPI account


Sitewide Organic Conversion Trend (YoY)
GA4—>Reports—>Life Cycle—>Engagement—->Events


In order to perform this check, you’ll also need to go to the Compare—>Same period last year.


However, before doing this you will need to check when GA4 was installed for the particular website you’re analyzing since it might happen that there is not enough data for comparison. In order to do this, you’ll need to choose the date going back to 2020 or 2021 and see what’s happening.
For example:
  



After that, you should go to the Compare—>Same period last year.
Then you should take a look at the table and click on the plus at the right side of the Event name in order to filter out the User source/medium and then you’ll be able to see the organic conversions.


  

  



The only disadvantage of this approach is that you’ll need to scroll down a lot in order to find the CTPs and their Google/organic numbers for the Event count. 
  



When you find it, you’ll be able to add the data to the table.


         * GA4 Example Report - can be accessed through the SCAPI account
























[a]@<EMAIL> Could you please advise with a bit more in-depth set of instructions? What are some more detailed criteria for each rating?
_Assigned to _
[b]Done
[c]@<EMAIL> Same as above, could you please advise on what the criteria are for each rating? maybe give some examples, no need to go into too much detail.
_Assigned to _
[d]Done.
It is hard to make clear boundaries where each rating begins and ends because many possibilities are based on comparing 2 factors number of commercial pages and the sum of their percentages, where we also need to take into account percentage distribution between them. So there are many different cases but I tried to give a few examples and give an impression of how to rate in different situations.
[e]@<EMAIL> Same as above, could you please do a little guide on giving ratings for this section?
_Assigned to _
[f]Done.
[g]@<EMAIL> Same as above.
_Assigned to _
[h]Done
[i]@<EMAIL> Same as above.
_Assigned to _
[j]Done
[k]@<EMAIL> Same as above.
_Assigned to _
[l]Done
[m]@<EMAIL> Same as above.
_Assigned to _
[n]Done.
[o]@<EMAIL> Same as above.
_Assigned to _
[p]The rating is calculated automatically
[q]@<EMAIL> Same as above.
_Assigned to _
[r]The rating is calculated automatically