﻿Screaming Frog setup guide


The only thing that has to be set up so that you could get back to crawls after turning off SF, and so that you could export and share crawls is changing the storage mode to database storage


  

  









I usually don't crawl images, other resources and external links, but that all depends on a specific crawl and analysis she needs to do, so this all is prone to change


  

this is how I usually set up the extraction tab
  







If you’re not auditing JS rendering, then turning off JS resources in the Crawl tab and selecting “Text Only” in rendering is the way to go. If, on the other hand, JS needs to be audited, then crawling and storing JS resources in the “Crawl” tab needs to be ticked, and JavaScript rendering has to be selected. Note that crawls are much slower when JS is turned on  


As far as the “Advanced” tab is concerned, I recommend turning on “Always Follow Redirects” and “Always Follow Canonicals”. The rest can be set up however you like.   


We need to go easy on our servers so, when crawling BC domains always limit the speed to 2 max threads and 2 URL/s. You can crawl other domains as fast as you like, just have in mind that you can be blocked when crawling too fast.  
















Also, when crawling BC domains, we should add “Better Collective -” in front of the User-Agent string, so that our DevOps would know who is crawling. When crawling other sites, of course, it would be good to not mention BC anywhere, and change User Agent to either default SF one, or Googlebot/Chrome.