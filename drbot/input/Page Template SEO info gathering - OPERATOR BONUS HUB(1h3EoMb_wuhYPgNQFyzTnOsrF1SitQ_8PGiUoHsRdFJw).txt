﻿[General / SOP] Page Template SEO info gathering - OPERATOR BONUS HUB


* OPERATOR BONUS HUB


* Meta Title
   * max 60 characters
   * main keyword - online bookmaker bonus (one of the most common options, here just as an example)
   * additional keywords that are bookmaker related: review, bonus, free bet. Besides keywords, we can display a current offer - current bonus/ bonus code, etc. If we display an offer in MT or MD, we must update regularly. 
   * date -  current month and year ⇒ update it regularly
* Meta Description
   * should be around 160 characters (min 50 & max 160 characters)
   * should be unique for each page and summarises the page content ⇒ display a current offer - current bonus/ bonus code, etc. If we display an offer in MT or MD, we must update regularly. 
   * main keyword - bonus
   * call to action keywords (Get your Welcome Bonus!/ Earn in 2022!/ Use the welcome bonus right now!/ Bet on!/ Bet in/ Join us!/ See the best promotions and bonuses!/ Learn how to win, etc.) - very important to have for this type of page
   * date - month and year (update it regularly). This is a good practice, but we do not have to put the date in the MT and the MD we can choose one spot only  
   * market 
* Content
      * ABOVE THE FOLD
   * Logo - the unique logo of the site
   * Navigation bar - containing all the most important pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussion/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. The navigation bar doesn’t have to contain all these pages. It depends from site to site. 
   * Breadcrumbs  -  best practice. It helps bots/ search engines better understand the hierarchy of the pages. Put the schema on it. 
   * <H1>
      * No character limitations but try to keep it visually appealing, so make it a manageable length. Make h1 stand out. Make the letters more significant than the rest of the text and headings.
      * main keyword: best bookmaker (could be any other synonym)  + bonus
      * date - year and month ⇒ good practice
      * market ⇒ good practice but optional
   * Intro text - Short paragraph (teaser, short info about the bookmaker - 2 or 3 sentences with keyword) - good to have
   * Table of bookmakers with bonuses - Display of the main offer/most important thing that users get from us. The table must be hardcoded in HTML and table elements must be server-side rendered. For the tables, we have to use <table> tags and not any other tags. Within the table: 
         * logo of each bookmaker, 
         * offer - CTA (bonus code or “Get the bonus!” button), 
         * bullet point - short description of the bonus benefits, 
         * Review buttons, 
         * Payment options - can be emojis/ pictures/or explained in words(bullet points)
         * CTA - button Claim bonus/Collect bonus
         * 18+ | Play responsibly | Terms & Conditions apply links.
      * BELOW THE FOLD
                Any widget/block/image below the fold should be lazyloaded.
   * Instructions - how to bet/ how to get the bonus = step-by-step guides that we put in lists (<ul> or <ol>) = good to have but optional. 
   * Screenshot images from the bookmaker interface itself to increase our credibility and prove that we actually have experience with the product. Same applies not only to other page types that have instructions but also to everything bookmaker-related we have.
   * Explanations/ Detailed description of what the bookmaker does <p> = good to have but optional
      * Types of bonuses
      * Guide to the bookmaker's bonuses
      * What is the bookmaker bonus
      * Best welcome bonus
      * Why use the bookmaker bonus
      * How do we pick a bonus 
      * Disadvantages of the bookmarks bonus
   * Other offers: WC bonus/ Free spins/ Casino bonus/ Welcome bonus/ Promo code  ⇒ if the bookmakers have them
   * FAQ ⇒ headers (h2, h3) as questions and content (<p>) as an answer - recommendation not a obligation
* Schema - not all schemas are required 
   * Breadcrumbs
   * FAQ - best practice
   * Review snippet
   * Sitelink searchbox - optional
* Images
   * Ensure the ALT text is optimised.
   * Use only high-resolution images. 
   * Compress images to load fast
   * Optimise Your logo
   * Every image below the fold should be lazyloaded
   * SOPs for image optimisation:
Optimizing images for best SEO performanceOptimizing images for best SEO performance - part 2


General recommendations:
* Table of content with anchor links - optional - good to have if the page is too long. It is a good practice to make it foldable to save some space. This feature also helps to gain sitelinks in the SERP which automatically gives us more space and more exposure.
* Any widget/block/image below the fold should be lazyloaded.
* Features like Bonus Calculator - good practice, but optional. Not many pages have this. (SDA has it).
* Research the market and competitors 


Navigation bar - containing all the most important pages of the site: 
Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussions/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. 
Navigation bar doesn’t have to contain all these pages. (Underlined are the most important ones for our business). 
For an affiliate site, it is important to put all commercial hub pages into the navigation bar because those pages are the most important for the business. 
Which links should be in the navigation bar depends from site to site. For example, BettingExpert has specialised in betting tips, but some other site does not even have tips at all.
The pages that are most common for the majority of affiliate sites are: 
Bookmakers hub and the good practice is to have a drop-down menu with the most important bookmakers
Bonus hub - the most common offer that affiliate sites are giving to the users is bonus codes (main offer that site has)
Betting tips/ odds (other offers that site has)
Casino hub - if a site has a casino section at all. Some of the sites don’t.
All these pages above are commercial because we are offering something to our users and the main goal is to get people on those pages where they can use our services.
Other important pages are informative pages where we show our expertise in the topic (in this case sports or casino), so we have also these pages n the Nav bar: 
News hub
Events page
Analysis
Live streaming
Guides








Sites used as examples:
https://pariurix.com/agentii/top-bonus
https://www.betarades.gr/bonus/
https://www.vegasinsider.com/sportsbooks/bonus-codes/
https://www.spilxperten.com/bookmakere/bonus/
https://www.sites-de-apostas.net/bonus-casas-de-apostas
https://bettingsidor.se/odds-bonus/




* HOMEPAGE
* OPERATOR REVIEW HUB
* OPERATOR REVIEW PAGE
* OPERATOR BONUS PAGE