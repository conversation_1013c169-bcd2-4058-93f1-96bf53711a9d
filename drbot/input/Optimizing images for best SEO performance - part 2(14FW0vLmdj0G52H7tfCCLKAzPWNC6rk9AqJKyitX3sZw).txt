﻿Image Optimization Guide


































                        -How to determine image size
                                     - Use Chrome Extension
                                   - Use Mozilla Developers Tools
                                   - Use the ruler option in developer tools
                        - How to set correct dimensions for images
                                -Correct image dimensions with Ezgif
                        -Correct Image Dimensions With Gimp
                                -Correct Image Dimensions With Plugin




   










How to determine image size
Chrome Extension
Image size info  chrome extension 
After the installation new option will be added to the mouse right-click option. 
Hover over the image and just click that option to reveal the image dimensions, display size, and file size.
  

  







Mozilla Developer Tools
Another way to reveal image dimension is to use the Mozilla developers tools option which is available on default once you install the Firefox browser.
If you click the “inspect” option from the right-click menu, this option will open docked at the bottom of the page. Pick the element from the page you want to inspect.
  



<PERSON>r mouse over the image you want to inspect and image size details will be presented.
  







  

Now, you can see that for the current dimension on the website (730x486) a much larger image has been uploaded (960x640) and scaled to (640x427). Finally,  from the client-side, the browser is stretching this image to fit the position dimension to be as close as (730x486).
The difference is quite obvious.








Use the ruler option in developer tools
On developer tools click settings and check the “measure the portion of the page” option on the bottom.


  

A new ruler will be added.
  

Stretch the ruler from the beginning to the end of the image and asses the image size.
  











How to set correct dimensions for images
Correct image dimensions with <PERSON><PERSON>gif


Ezgif is yet another website for image optimization and compression. Easy and intuitive, this website offers wide optimization and conversion options. This online tool is available at this address, it’s free and the user is not obligated to register and log in.
  



Images can be uploaded directly via the URL or from your computer. Click on the resize option.
  

Supported image types are listed below. Our image is (960x640)px and we want to resize this image to fit the position with the size (730x487). Paste the image URL and click upload.
  





The image will be displayed. Our example is 97.73kIb in size. Enter  730px in the width field and select “force original aspect ratio” and click the “resize” button.
  



The final result is displayed. The image is resized to fit the position (730x487) and we also achieved some savings of 24.15%.




Correct Image Dimensions With Gimp 
  

Finally, click the save button and download the picture.




Correct Image Dimensions With Plugin


Resize image after Upload is a WordPress plugin capable of resizing images directly within WP after the image upload, but only for new images.
  

  





When the plugin processes images, it discards the original uploaded file. That means the original file is not maintained on your site, and also, any EXIF data embedded in the original image will not appear in the resized image.
























Optimization And Compression for Wordpress
WordPress - Step By Step Guide


Every WP theme has already a predefined image size for each position and instructions should be included in the official documentation. In general, a content creator should be aware of one position and its dimension, the position that holds the image in a single post, and which is also a post featured image.
Images should be uploaded only through the media library system!




  

Determine the right image dimension
Each theme already has its own demo which is placed somewhere in the marketplace. The best way is to go to the particular demo post page and measure image size using google developers tools.
  



By examining the featured image on the single post we can see that image is scaled to 730x486.667 px. However, the uploaded image is 768x 512 px which is bigger than the required size. Note, this is not the most drastic example but following the rule to prepare images to fit position by dimension can make a big difference in any case.


Prepare the image placeholder in GIMP
For the purpose of this demonstration, I am using GIMP which is a free image editor, powerful, and easy to use.  
I know that image should be 730x 486 px. Using GIMP I am creating a transparent image placeholder to fit this dimension. From the option, I am choosing a background to be transparent to ensure that I have a lighter placeholder for my image.


In the image size section, I am setting up the right image dimension in pixels. Everything else should be the default.
Click OK
Transparent image placeholder is now ready and it’s fitting the featured image size required by the template (730x486).
  







The image that I want to put in the post is quite big, 1200x860 px. Using GIMP I am resizing the image to fit my placeholder. In this case, it’s 730x523 px. My width is ok but my height is a bit bigger.
  



The next step is to paste the scaled image to our placeholder.
  

Move the image until it fits the whole placeholder and set a focus.
  



Select File - Export and give the picture a descriptive name. Click image - flatten the image to remove the alpha channel. After that Click export image in .png format.
This final result has appropriate dimensions and good quality.
  







Optimize image for Web
Open online tool EZGif click browse and upload the image.
  

Our file size is 541.87kib in size. Click convert image to .webp. Since the public in the background is already blurred, click convert and choose jpeg format which is a suitable type for quality reduction.
  

We manage to reduce image quality by nearly 88%. This image is relatively rich in colors so we need to try if this can be a little bit better.
Open GIMP once again and load the prepared png file. Click export as and this time choose jpeg as the file type.
  



Reduce the quality to 70% and click export.
  











Now the image size is much smaller than our starting png.
  





Open EzGif again and upload the jpeg. Click optimize and reduce the quality a bit more.  Now we managed to reduce the image by 41.7%.
  

Finally, click on “.webp” and press the Convert to .webp button. The final result is the .webp image, reduced by 41.57 % from the original jpeg image and scaled in the right format with 35.65 kb in size.
  

So far, we learned that the .png format is not the right format to use on the web and should be avoided. Jpeg is much more suitable for compression. If possible, jpeg should be converted to .webp type as Webp format, on the other hand, the idea is to pick a format that is the best in terms of size and quality ratio.






Upload images using the media library
By default, the WordPress media library will create 9 additional images in different sizes.


Png Image
	Size
	Original (optimised jpeg)
	Size
	New (optimised jpg)
	Size
	Optimised WebP
	Size
	

	

	  

	768x512




74.3kb
	

	

	

	

	  

	730x486


554.9kib
	

	

	  

	730x486




33.1kb
	  

	730x486


22.9kib
	  

	150x100


37.5kib
	  

	150x100




5.1kb
	  

	150x100




5.2kb
	  

	150x100




4.6kib
	  

	150x150


52.7kib
	  

	150x150




6.4kib
	  

	150x150




6.6kib
	  

	150x150


5.6kib
	  

	218x150


75.7kib
	  

	218x150


8.4kib
	  

	218x150


8.7kib
	  

	218x150


7.1kib
	  

	265x198


113.9kib
	  

	265x198


11.7kib
	  

	265x198


11.9kib
	  

	265x198


9.4kib
	  

	300x200


128.2kib


	  

	300x200


12.8kib
	  

	300x200


13.1kib
	  

	300x200


10.3kib
	  

	324x400




238.4kib
	  

	324x400






24.1kib
	  

	324x400






21.8kib
	  

	324x400






15.3kib
	  

	485x360


325.2kib
	  

	485x360


29.8kib
	  

	485x360


27.5kib
	  

	485x360


18.8kib
	  

	696x464


549.9kib
	  

	696x464


51.9kib
	  

	696x464


43kib
	  

	696x464


26.6kib


	

	

	  

	741x464


57.9kib
	None
	None
	None
	None
	





















Dimensions
	Png file-size
	jpeg-Original file-size
	New jpg-Optimized file-size
	WebP file-size
	768x512
	0
	74.3
	0
	0
	730x486
	554.9
	0
	33.1
	22.9
	150x100
	37.5
	5.1
	5.2
	4.6
	150x150
	52.7
	6.4
	6.6
	5.6
	218x150
	75.7
	8.4
	8.7
	7.1
	265x198
	113.9
	11.7
	11.9
	9.4
	300x200
	128.2
	12.8
	13.1
	10.3
	324x400
	238.4
	24.1
	21.8
	15.3
	485x360
	325.2
	29.8
	27.5
	18.8
	696x464
	549.9
	51.9
	43
	26.6
	741x464
	0
	57.9
	0
	0
	





Y-axis = file fize
X-axis = image dimensions
  

  

Now, it’s clearly visible that optimized images with the right dimensions are better for optimization as they have smaller, but the most important correct sizes. The bigger the image size the better compression for the same dimension and the bigger the difference between jpg images and .webp. 


Furthermore, the original image is uploaded with the incorrect size (768x512) and because of that, one more image is created with the size (741x464) as the substitute. This additional step does not exist when images are correct in size.
WebP images are clearly the best option, but optimized jpg images can be also implemented.
The benefits of using optimized images, images with the right size, and new image formats are obvious.
PNG images are the worst solution, they are very heavy, even their thumbnails are big in size and should never be used as a post featured image.












Word press -  Image Optimization Possibilities


Compressing the images is one of the best ways to increase the website speed. Compression may even further help websites to rank better in search engines at some point.


There are two major ways how this can be done:
* Manually
* Using Plugin
* Programmatically






Nonetheless, it all comes down to your personal preference since all of these methods can be used to compress images whether you have already uploaded them or not. ShortPixel can be used for both approaches and it’s also our favorite tool to compress images since it has Cloudflare integration and many more useful features.














Image Compression using Plugin
This is probably the most straightforward method. The majority of image optimization plugins have similar capabilities, CDN offer, backup, and automation. The only difference is the interface and free plan capacity. In other words, while the interface is more related to the users' personal preference, the number of images that can be optimized differs according to the pricing policy. For a free plan, we can use only a small subset of all images.


These are some of the most popular image compression plugins.


* Smush.
* Optimus.
* EWWW Image Optimizer.
* ShortPixel Image Optimizer.
* Compress JPEG and PNG Images.
* Imsanity.
* Imagify.
* reSmush.it.
Manually Compress images


The manual method of image optimization should be a regular practice when creating content as images are also an important part of SEO. The detailed process is explained in the section above. 
Below is a list of some online compression tools:
* JPEG Optimizer
* Kraken
* Tiny PNG
* Jpeg.Io
* ImageRecycle
* Compressor.io
* EzGif




Programmatically Image Compression


WordPress CMS is storing images in a media library. Images are located in the uploads folder and they are organized in the folders by years and months. Depending on the programming language used, this can be done in a variety of ways.


The idea is to browse through each folder and convert and optimize images at the same time. In case we want to convert images to .webp format, a set of new optimized images should be added in those folders with the same name but a different extension (.webp).
Old images should be kept in case they are indexed in the Google database.


We have two possible scenarios here:
1. We want to optimize images without changing the format
2. We want to replace the existing image with the WebP image.




In case we want to perform only optimization, we are going to process existing images replacing them with those that are optimized. This is a straightforward process.


If we want not only to optimize images but to change the type, we need to browse the folders and create .webp images using old ones. The second step is to make changes in two tables (wp_posts, and wp_postmeta). We need to make a query to replace the existing image format with the new one for each post. An example is created according to this post.




  



  





















The final result should be something like this:
  



That’s all that it takes, now all images are served in a new .webp format optimized to 65%.


 




Special Case - Programmatical Image Compression
However, some of the uploaded images are quite big in size. To fix this, we need to identify the image size (Python, screaming frog). Then we need to change the image size manually. This is a special case and the number of big images should be quite reasonably smaller.


Another solution is to use the Golang proxy server. - Case study available.