﻿Initial audit
Necessary tools
The audit process
Step 1 - Security flags/Manual Actions
Step 2 - Rendering test
Step 3 - Cloaking test
Step 4 - Page Speed test
Soft audit
Necessary tools
The audit process
Step 1 - Indexation
Step 2 - Page Speed
Step 3 - Mobile-Friendliness
Step 4 - CMS
Step 5 - Analytics
Initial audit


NB: The points here go in this order: 0 is the best, 3 is the worst
Necessary tools
The first thing to watch out for when it comes to initial tech audit is whether you have Google Search Console access in order to check if there are any security issues/flags. Besides Google Search console, you will need to look for security vulnerabilities using either WPSec (for WP sites) or Sucuri SiteCheck (for non-WP sites).

Google Search Console also comes in handy when it comes to the rendering test, but that data should also be double checked by using Chrome DevTools (Right Click > Inspect), Google Cache and What would JavaScript do? | Onely JavaScript SEO tools. 
Next, the cloaking test can be done either by manually checking the most important pages in Chrome while changing the User-Agent (extension for that: User-Agent Switcher for Chrome), or by crawling the pages using different User-Agents in Screaming Frog and comparing the served content there.

Finally, for pagespeed testing you should use both WebPageTest  and Google’s own PageSpeed Insights.
 
The audit process
Step 1 - Security flags/Manual Actions
The point of this step is to check whether Google has noticed any severe security issues with the site.
While at it, it’s always good to check if they have taken any manual actions against the site also.
  








This is the way the rating should be given based on the result of the check:
* Any of the following errors: 3
  
* Any of the following warnings: 2
  
* No active errors or warnings, but we have info that the site historically had any security issues: 1
* No historical or current flags: 0
It doesn’t happen very often that Google reports security issues with any of the sites we are working with. 
The next step is therefore to look beyond what Google reports. 
For inspecting vulnerabilities on WordPress sites, use WPSec, by simply pasting the URL in the bar and clicking the ‘Start Scan’ button:
  





The first fold shows only the basic information - if the WordPress version the site is currently using is susceptible to attack or not, and if yes, how high the risk factor is.
  



Below the fold, you can find a more detailed report, with severe vulnerabilities displayed separately at the top and colored in red, such as outdated versions of WP.

  

Individual WP plugins and their vulnerabilities, or potential vulnerabilities, if any, are displayed below, as shown on screenshot

  
If the site doesn’t use WordPress as a CMS, you would have to use this tool: Sucuri SiteCheck. 
Using this tool is pretty straightforward as well:
  
Note that even if the site is built with WP, it is worth checking this tool too, as it might find some non WP-related issues.


The rating system for this check should be the following:      
   * Serious WP or  plugin vulnerabilities/High or critical risks: 3
   * Easily fixable vulnerabilities such as outdated versions of WP or plugins: 2
   * Potential vulnerabilities/Protection recommendations: 1
   * No issues: 0
The rating system for this part doesn’t have to be followed strictly, so please down the conclusions in the field under Analysis, as they are more useful to the stakeholders than the ratings themselves. 
Step 2 - Rendering test
For this check, we will primarily be using GSC if it’s available. If you don’t have access to the site’s GSC, it would be good to check Google Cache which can be accessed through the SERP.
Find a set of the most important pages (e.g. homepage, reviews, offers, and tips) and look them up in the URL Inspection Tool in GSC.
  
  

Enter each of those pages separately in the URL search bar (alternatively find that URL by clicking on “Search results” under performance and filtering the URL there) .
  
  

That screen will be updated when the live test is done, so now you can examine the screenshot and the HTML code of the page the way that Google sees it:  


If GSC isn’t available we can take a look at the cached page which can be accessed by clicking on the three dots, right next to the domain name on Google SERP
    
These two methods should give us a pretty good insight into how Google renders and sees the page and if there is any discrepancy between that and what your own browser is rendering.

The biggest differences here can happen in rendering JavaScript. To see exactly how much and in which ways the site is dependent on JS, you can use the Chrome DevTools (Right Click > Inspect). There, when reloading the page and choosing the ‘Network’ tab on the very top of the DevTools window, you can see all the resources your browser has loaded. A little bit further down, it’s possible to filter only JS resources. The list of resources will be displayed on the left-hand side of the window, and you’ll be able to block each individual resource by right-clicking it and choosing the ‘Block request URL’ option.  


Blocked JavaScripts will appear at the bottom of the DevTools window. This way, we can compare how the page is being rendered with each individual JS resource blocked/unblocked and determine which JS is used for what, how it affects the site, and how much the site relies on such resources. Heavy reliance on JS isn’t the best SEO practice.  

Additionally, you can use What would JavaScript do? | Onely JavaScript SEO tools tool to see the comparison between pages when JS is switched on and when it’s switched off. 
A good feature is that it shows the comparison in screenshots, HTML, meta tags, and links, all of which is quite important. Note that these last two mentioned tests do not represent the pages as Google sees them.
  


  
  

Give the ratings (loosely) based on the following criteria:
   * Google can’t render content and/or links (possibly generated by JS): 3
   * Google doesn’t render certain pages from the set properly and isn’t able to display some functionalities of the page: 2
   * The site is heavily reliant on JS, but Google has no problem rendering everything: 1
   * No problems with rendering, content, and links are hardcoded in HTML: 0
Step 3 - Cloaking test
The point of this exercise is to see whether the site serves the same amount of content to users and to Googlebot, which it most certainly should.
The first method to do this test includes plain eyeballing a set of the most important pages of the site (again, the homepage, reviews, offers, and tips). By using this Chrome extension - User-Agent Switcher for Chrome - you’ll be able to change your own User-Agent settings and compare if the site is serving the same content to different devices. 
  

Note that it’s only natural that the viewport changes when changing the User-Agent. The thing to keep an eye on is the amount of content that is being displayed, which should be the same for all devices. It’s good to check the default Chrome desktop version, as well as one of the iOS devices and one Android device.

In order to see which content is being served to Googlebot, we’ll need to use Screaming Frog, and set the Googlebot User-Agent there.

First, with the default User Agent set, you should crawl the previously chosen group of most valuable URLs as a list.
  

Right after that, run the same crawl with the same configuration and the same list of URLs, only changing the User Agent to Googlebot.

  
  

When both crawls are finished, change the mode to Compare (click Yes if a prompt pops-up)
  
  

In compare mode, you should provide two finished crawls that are to be compared. For the current crawl, select the latest one, i.e. the first one from the list (with Googlebot as the User Agent), and for the Previous crawl, select the earlier one, which should be second on the list (crawled with default SF User Agent). 
After selecting the crawls hit the green ‘Compare’ button.
The section in the top-right corner shows the percentage of change between the two crawls. It should be 0 everywhere but prioritize checking Content, H tags, Links, Internal and External HTML files (although Internal should always stay the same when crawling the same list of URLs), etc.
  

Ideally, the following rating system should be applied:
   * A big percentage of change in content and links between the default and the Googlebot UA crawls: 3
   * Bigger visible differences in pages when switching user agents in Chrome: 2
   * Only very minor changes between desktop and mobile user agents: 1
   * No differences whatsoever: 0


Step 4 - Pagespeed test
For testing the site’s page speed, we’ll need to use both the tool called WebPageTest and Google’s own PageSpeed Insights.

First, run the WebPageTest by entering your URL and selecting Advanced Configuration.
  
Choose the following settings:
   * The location should be the one closest to the target market
   * 4G connection (Advanced settings  → Test Settings)
   * Emulating a mobile device (Advanced settings →  Chromium)
  

  

The tool will run 3 consecutive tests and display the median values of the metrics. The Speed Index metric is the one to keep an eye on: 
  


The rating system of this test is pretty straightforward and it’s based only on the Speed Index: 
   * >4494 = 3
   * 2192-4493 = 2
   * 1389-2191 = 1
   * <1388 = 0
The next check is to be done with PageSpeed Insights.
To start, only enter a URL of a web page from your domain in the bar. 
After getting the report, select ‘Origin’ instead of ‘This URL’ in order for PSI to show you the aggregated page speed data for the whole domain instead of the data for just one page/URL.     
  

The metric to keep an eye on is the Largest Contentful Paint. More precisely, you’d need to click on the ‘Expand view’ option in order to see which percentage of users are experiencing poor or  LCP that needs improvement (>2.5s) - these two percentages should be added together. Copy the result percentage to the M&A template in sheets.

Consider increasing the severity rating of the page speed section if this percentage is high (more than 25%, which means that the site doesn’t pass CWV)
NOTE: It’s quite important to write down the summary in the cell at the very bottom of the table for the Tech section of the audit. These written summaries are often more important than the ratings themselves.


Soft audit


NB: The points here go in this order: 3 is the best, 0 is the worst


Necessary tools
The first thing to watch out for is whether you have Google Analytics and Google Search Console access. If so, make sure that the data is tracked correctly and that you have enough to perform the audit. Additionally, regardless of access to GA and GSC, you’ll need Screaming Frog (or OnCrawl, if preferred), as well as a few other online tools such as GTmetrix, WebPageTest,  BuiltWith, PageSpeed Insights, Adswerve - dataLayer Inspector+.    


The audit process


Step 1 - Indexation


The first thing to do here is to run a full crawl of the site either using Screaming Frog or OnCrawl. Load this configuration file, choose Spider mode and then enter the URL of the home page and run the crawl.  


  


The aforementioned configuration includes crawling all the resource URLs the SF will stumble upon and it should also extract all the available data because it’s good to have a full crawl of a site for doing various checks later. 
If the site is too large and the time or your machine won't allow crawling the site completely with all its resources, you can try opening the settings (Configuration → Spider) and unchecking Images, CSS, JavaScript, SWF, as well as External Links, AMP. If site is still to large to crawl 100%, having 5k-10k URLs crawled should be sufficient to conduct a proper evaluation as technical issues are usually tied to a section or type of URL. 
Just ensure that samples from all site categories are caught in the crawl. Site categories are /bookmakers/, /goto/, /blog/, etc.
  

  


If crawling without JS, you can also change the rendering to text only.
  


When copying the number of the encountered URLs to the template in Sheets, it’s important to look at the HTML URLs in the Internal section that can be found on the top-right side in SF. When that report is selected enter the following filter in the search bar - ([Status Code] Equals '200' And [Indexability] Equals 'Indexable'). This is because we would like to see the number of crawled status 200 HTML pages (remove duplicate canonicals) vs. the number of indexed pages in Google in this test.
  





ALTERNATIVE: If using OnCrawl, you’ll have to create a new project (top-right corner on the start page). You can enter the domain name (it’s best to be with TLD) in the first field and the website URL in the second field: 
  



Next, on the configuration page, toggle the ‘Extra settings’ button and configure the crawl as following:
  


Almost everything on the screenshot above are the default settings, with the exception of the ‘Crawl the website as a JavaScript website’ setting. After that click on the pink button in the upper-right corner - ‘Save & Launch the crawl’.
The number to pay attention to is ‘Indexable Canonical Pages’. That number should be comparable to the number of indexed pages we will check later in GSC.  


Now, when you have the data from a full crawl of the site, it’s time to check Google Search Console. 
To check coverage, in the bar on the left hand side, click on ‘Pages’ in the ‘Index’ section.
Add the number of non-indexed and indexed pages, and write it in the ‘Excluded + Valid (GSC)’ column. In the ‘Valid indexed (GSC)’ column, enter only the number of the green pages (in this case 409k).
There’s a formula in the footprint column, so the value of that cell should be calculated automatically (H6/G6 - valid pages divided by excluded+valid).

  


In the ‘Analysis’ section of Indexation, please describe all the potential indexation/crawling/ranking pitfalls you’ve noticed when analysing the crawl of the website. Pay extra attention to: Canonicals, Noindex tags, and internal linking (orphan pages) (follow/nofollow).

A very general guide to giving performance scores in this section:
   * No serious problems with canonicals, noindex tags, orphan pages, follow/nofollow tags + Footprint is >50%  = 3
   * Minor issues with some of the aforementioned categories that can be easily fixed (or a lot of pages that are noindexed for a good reason), footprint 30%-50% = 2
   * Site has some serious security and/or index bloating issues - a lot of 404/301s/non self-referencing canonicals/orphan pages etc, site dependent on JavaScript  = 1
   * Besides the problems mentioned above - Google is crawling but deliberately not indexing important pages (such as daily tips) because of duplicate or thin content, or Google cannot render the pages properly because of JavaScript, broken internal links, etc.  = 0



Step 2 - Page Speed
For testing the page speed performance, you’ll need one more tool in addition to WebPageTest and PageSpeed Insights, and that is GTmetrix.  

Start by analyzing the site in PageSpeed Insights. 
Once again, you’ll need to click on the ‘Origin’ tab, and on the ‘Expand view’ option in order to get detailed and aggregated information on the performance of the whole domain.
  

Fill in the FCP, LCP, and CLS fields in the audit sheet with respective metrics. Write both the result in seconds and the percentage of users with good, poor, and experience that needs improvement in each cell.
In the last column on the right side of the table, add even the PSI performance score which can be found below the first fold in PSI:
  
      * High 70-100
      * Medium 30-70
      * Low <30

Next, test the site in WebPageTest, using the same configuration as the one for the initial audit (jump back to the screenshots):
      * The location should be the one closest to the target market
      * 4G connection (Advanced settings  → Test Settings)
      * Emulating a mobile device (Advanced settings →  Chromium)
After running 3 consecutive tests, the tool will display meridian values. TTFB, Start Render and Fully loaded (marked with a red arrow on the screenshot below) are the metrics that you should copy and paste to the respective columns in the sheet.

Important: When it comes to TTFB, besides the values in seconds, please write even a grade (A, B, C, or D). According to webpagetest.org, the target time is the time needed for the DNS, socket and SSL negotiations + 100ms. A single letter grade will be deducted for every 100ms beyond the target.
  

After that, test the site in GTmetrix. You can run the test with the default settings, as you’ll need an account/paid subscription to change them.
The metrics we need from here are DOM Content Loaded and Fully Loaded:
  

Feel free to write additional comments in the analysis field. For example: are there any optimization points or suggestions in the PSI (please note them if there are any for Total blocking time), or describe the situation in case the site is perhaps well-optimized, but has a low score because of the slow framework, etc.
The final Page Speed performance score should be given as an aggregate of different scores.
TTFB:
      * A=3
      * B=2
      * C=1
      * D and below = 0
Start Render:
      * <1,5s = 3
      * 1,51s - 2,0s = 2
      * 2,1s - 2,9s = 1
      * >3,0s = 0
DOM Content Loaded (GTMetrix):
      * <2,1 = 3
      * 2,2-4,2 = 2
      * >4,2 = 1


Fully Loaded Time (GTMetrix):
      * <0,3s = 3
      * 0,31s - 0,4s = 2
      * 0,41s - 0,5s = 1
      * >0,5s = 0
When you’ve calculated what the aggregate Page Speed score should be, don’t hesitate to take into account any other factor that you’ve noticed during the analysis and that you deem important. Describing the potential issues is more important than following the rules for giving scores.
Step 3 - Mobile-Friendliness


For testing the mobile-friendliness of the site, we’ll use Google’s own Mobile-Friendly Test - Google Search Console tool first. Start by entering your home page URL in the bar, but note that the test should include checking the homepage + one knowledge article + one commercial page type.

  
When Google checks and crawls the page, you should get a result page that looks something like this, where you can see whether the page is mobile-friendly, you can inspect the screenshot/HMTL code, and even see crawl details:  


Repeat this process to check one knowledge article and one commercial page type in addition to the home page.

Additionally, if you have access to the site’s Google Search Console, you can bulk-check mobile-friendliness of all the pages that are being regularly crawled by googlebot.
  



After doing this initial check in GSC, you can fill in the first field in the mobile-friendliness section of the template - just select from the drop-down menu whether the site is mobile-approved or not.
In order to fill in the next field, you can use either Screaming Frog, which you will also need for some following steps, or you can do a manual check in your browser while using the User-Agent Switcher for Chrome extension.
As with the initial audit, click through the site in Chrome on desktop and after that, check those same pages with some of the available mobile User-Agents:
  

To fill in the ‘Mobile Setup’ field in the template, select one of the options from the drop-down menu, according to the results you got from this test:
      * Responsive means that the URL as well as the content of each page stays exactly the same, and that only viewport changes depend on the User-Agent (this setup is the best practice)
      * Separate mobile site suggests that when changing to a mobile User-Agent, the site starts serving different URLs and thus different pages than one would get on desktop.
In this case, the change in the URL usually follows a pattern, i.e. the path (slug) should stay the same, while ‘m.’ subdomain is added to the domain:
Example: https://www.facebook.com/lite/ → https://m.facebook.com/lite/ 
      * Dynamic serving is a setup where the server responds with different HTML (and CSS) on the same URL depending on which user agent requests the page.
      * N/A option is reserved for sites that are unresponsive and don’t have any mobile version at all 
This part of the test could also be done by analysing the crawl data from Screaming Frog.
When it comes to Screaming Frog, a similar comparison to the one described for the initial audit should be done.
Repeat all the steps listed for the initial audit. 
Make sure that the sample of the crawled pages is good, i.e. that you crawl the homepage, knowledge/evergreen articles, commercial pages, etc.

Fill in the template with word counts for mobile and desktop as well as with link counts for mobile and desktop. The Word/Internal links count difference fields have a written formula which should divide mobile counts with desktop counts, but I think it’s better to get the difference percentage from Screaming Frog too, and write it in that field.

Note: If the site uses a separate mobile site setup, you’ll need to configure URL mapping in the comparison mode of the Screaming Frog, so that the crawler can know which URL from one crawl is equivalent to which URL from the other crawl
  
When you open the settings menu and select URL mapping, you’ll need to click on the ‘Add’ button in the lower right corner and add a URL mapping rule. 

In the field on the left, you should enter the URL pattern of the current (latter) crawl, which in this case is the mobile version, as we crawled it second. 
In the field on the right, you should add the URL pattern of the previous (first) crawl, which in this case is the desktop version.
  



When giving the performance scores, you can use the following as guidelines:
The word/internal link count difference:
         * 1.0-0.9 = 3
         * 0.89-0.7 = 2
         * 0.69-0.5 = 1
         * 0.49-0 = 0
Have in mind that it’s a serious issue if the site isn’t mobile-approved, so that can and should decrease the score significantly. Also, if the site isn’t responsive, but has dynamic serving or a separate mobile site - those are not bad practices per se, but they have to be set up correctly, so please do a very thorough evaluation in that case.


Step 4 - CMS
For the purposes of checking a site's CMS, the most useful tool is BuiltWith. They even have a Chrome extension that can save you time, as it can be executed without ever leaving the site/page you want to analyze.

If you are not using the extension, but the website version of it, start by entering the wished URL in their search bar and hitting the ‘Lookup’ button.

This tool displays every single bit and type of technology that was used to build the page you are analysing. It can give you insights into which servers the site uses, if there are any CDNs in use, how Google's scripts are implemented (first of all, Analytics and Tag Manager) and which other 3rd party scripts (mostly in advertising purposes) are present on the site.
What’s most important for this step of the audit is to examine CMS and its plugins.

To find that, you’ll only need to scroll down until you find the Content Management System section, which looks like this: 
  Even before you scroll down to see which CMS the site uses, you should be able to see if any plugins are installed in the CMS - that info should be available already in the first fold, in the widgets section.

  


If there is no Content Management System section available in the results, that means that the site is using a custom CMS which cannot be recognized by the tool.

NB: Some sites can have a couple of combined CMS-es, e.g. a custom CMS with a certain section in WordPress. 
Those things can be noticed only by checking pages from the different categories and sections of the site.


Fill in the template sheet and choose from the drop-down menu if the site is using one or multiple CMSes and write which systems are in use. 

It’s difficult to provide useful guidelines for performance scores for this section, but they can look something like this:

         * All tested pages are in WordPress = 3
         * Static content (blog/news) pages are in WordPress, while some other CMS is used for dynamic content (custom CMS, .NET, ajax etc.) = 2
         * A combination of WordPress + a custom CMS even on static content pages = 1
         * The whole site uses a custom CMS or Wix, Joomla, Drupal etc. = 0






Step 5 - Analytics


I advise that you use a 3rd party chrome extension called Adswerve - dataLayer Inspector+ to check if Google Analytics and Google Tag Manager (especially now in the period of migration to GA4 which can be implemented only through GTM) are implemented correctly, as well as to check which data is being pushed to the dataLayer.


A good thing about this extension is that you don’t have to pin it to the extension bar in Chrome, so it won’t take up space there. Instead, you’ll be able to examine GA, GTM and dataLayer setup through the console in Chrome DevTools (Right click → Inspect).

When you add the extension to your browser, make sure you click on it the first time and allow access to the site you’re on. After that, you won’t need to open the extension itself.

  














Take a sample of representative pages that you want to inspect (as mentioned a couple of times earlier, it’s best to inspect the home page + knowledge/evergreen articles + commercial pages). 
While you are clicking through the site and checking these pages make sure the Chrome DevTools Console is open and that Adswerve extension is working properly. 

With each new opened page the extension should detect to which UA/GA4 datastreams the site is sending data and which GTM property is added to the dataLayer where the information is being pushed.  


Eventually, when you scroll down, you should be able to see that a pageview event is being sent to GA4 and/or UA. 
  

NB: It’s okay if the site is sending a pageview to one UA property and one GA4 property, that’s even expected as many sites are running two versions of analytics simultaneously now during the transition period. 
However, it’s not ok if it’s sending more than one pageview to the same property - that almost certainly means that the site is trying to tamper with the bounce rate!

If there are some useful custom events set in GA such as CTP event, scroll depth, etc. that’s a big plus.

  

A (potential) guide to giving performance scores:
         * GTM + GA4 only or GA4 and UA simultaneously, with correct tracking = 3
         * GTM + UA only = 2
         * No GTM, with manually set UA/some other analytics tool/no analytics tool at all = 1
         * Deliberately incorrect setup, with counting pageviews and other events more than once in the same property = 0
If you notice anything out of the ordinary (such as the aforementioned incorrect setup), please, write it down in the analysis field.

At the very bottom of this table is a large cell reserved for the summary of the whole Tech audit. Write a short summary of each test and don’t hesitate to write even if everything is okay and, of course, pay extra attention to the potential pitfalls.