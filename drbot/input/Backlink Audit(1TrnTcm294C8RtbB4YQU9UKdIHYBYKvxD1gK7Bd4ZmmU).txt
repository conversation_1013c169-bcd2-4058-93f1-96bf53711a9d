﻿Backlink Audit
You should follow how your backlink profile is progressing as competitors may take advantage of it really fast and you can end up with dozens of spammy backlinks that are pointing to your website in under a month.
MOZ - Spam Score
I like starting with testing the website through the Moz Spam Score feature. It’s under Link Explorer >> Spam Score as you can see on the picture below.
  



It’s important to say that everything under 3% spam score is great, between 3-10% medium/risky/spammy and everything above 10% terrible (probably some more serious problems are with their backlink profile).


Cognitive SEO - Site-Wide Links
  

  



Through the Sitewide feature we can identify if the website is paying for backlinks that are mainly placed in the navigation bar. That is a risky technique, but most powerful as with it we get the most SEO value.


Mainly this feature is used to see if the website is having a lot of navigation bar backlinks (site-wide links) if yes, there is something suspicious about that website and it needs further investigation.


Majestic Link Graph
  



These two pictures are extreme differences, from the left you can see the most natural link graph and from the right, you can see the most unnatural link graph, which in either cases, you won’t find in real life examples.


Let’s see the examples from a real-life.




  



From the left you can see Action Network and from the right you can see Betarades link graph. We can see that AN is having some problematic parts of their link graph, but nothing major. If you hover over the lines you can see which link is there and which tier (tier 1, tier 2 or tier 3).


Majestic Neighbourhood Checker


  

In Majestic we can check if we are having a healthy or unhealthy neighbourhood. You can search for websites that have something suspicious in their domain name or you can check 1 by 1 if the list is not too long. 
Have in mind, if the website is on a shared hosting, it will have almost for sure some bad domains in their structure.


Majestic TF/CF
  

VegasInsider
  

Action Network


This is a small representation of how you can compare two domains with TF/CF from Majestic and also External backlinks that are pointing to the domain.


We can see that VI is quite better than AN in terms of TF/CF and also backlinks. The thing to watch here is to see which domain is having more backlinks (or TF/CF proportion) closest to the middle line (diagonal).


Ahrefs Anchor
  

Some parts of words are labeled in white, but you get the point.
Here you can search for some of the adult anchors or some chinese ones. You can easily find patterns with this tactic and see which kind of the websites are trying to harm that website.


Ahrefs Referring IPs
  



It can be an easy step for identifying large amounts of spammy backlinks pointing to the domain. Probably in the first few IPs there will be .blogspot domains but that is because Blogspot is giving it’s domain and hosting to the people that are using the Blogspot.


Ahrefs - Backlinks vs Referring Domains
  



If you see anything like on this picture know that something is behind that website, here we can see more than 7 million backlinks from 24 thousand websites, almost 300 backlinks per website, which is way too much.


This is necessarily not always a bad thing, it can be that a website is linked on our other website from a navigation bar, where that website gets a ton of new backlinks, like we have a goal.pl for example that is linked from a lot of other websites that are connected to each other.


Just always have a context behind it and deeper look into the data.


Google Search Console - If Checking Your Websites
  



When we try to enter these websites, we are redirected to the link like this: http://girlsandtheir.webcam/&_=1611582754917
“Girls and their webcam” is what we can read from the link. It’s becoming really strange. Let’s see the picture for visual representation.


  

(white dots are there with a reason)




One more interesting thing that you can look at in GSC is the ratio between “Linking pages” and “Target pages”, if you see a lot of linking pages and just one target page, that is already looking fishy.
  



After looking at these links, I’ve found that these are the PBNs that we would like to disavow, see the picture for a reference:
  





Link Redirect Trace Extension
  



This is one extension that is well-known and where you can see, for example from a picture if a complete website is redirected to the domain, what tactics that website is doing, which redirects they are using, what is Power/Trust can relate to something similar as TF/CF from Majestic and many more options.


Best tip I can give you is to visit as many websites as you can when checking the backlinks, in this way, you’ll find some patterns. Mainly, same design, structure of the text, backlinks towards their domains, favicon, contact details, about us etc.


What is PBN and How to Identify it?


“PBN (private blog network) backlinks are hyperlinks built from a network of connected sites and blogs. These sites are generally owned by the same individual, who's using them all to create links to their money site.” If you want to read more about this you can visit this link.


Okay, we know what PBNs are, but how to identify them? I’ll place here some general notes and a list can always be filled with new things:


* Same design on multiple websites
* Bad structure of the text
* Cross-backlinking towards their other domains
* Favicon (mainly WordPress default)
* “Contact Us” page looks bad or have just a contact form without text
* “About Us” page have little to no text and generic content without the pictures of real people (not stock photos)
* Blog posts have generic titles e.g. “How to Lose Weight Fast”
* Too many ads (more than 5 ads on a 2000 words page. If larger page, more ads could be tolerated)
* Generic logo
* If pretend to be a real company - doesn’t have phone number & address of company
* Links to social media are broken
* Footer looks bad without too much information


All of the things from the list are subjective things, so for someone one website is PBN, for someone it isn't. That is completely okay, it’s important that really bad websites are PBN for everyone.


If you have any questions or need any additional information, please feel free to reach out to me: <EMAIL>.