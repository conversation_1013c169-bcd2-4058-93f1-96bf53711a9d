﻿Intro notes before you start:
* Use Scapi, <PERSON><PERSON> or your BC account
* Make sure that Scapi account has access to GA and Search console of the brand in question


Trends keys that will be handy when creating Trends connector:
Manda                  e42e41d31fa2503e5ea912eeb2118df6
Scapi                 bc3ee656aae78ae2da881878a9030878
Note: If you are using your account, you can generate your own key here https://strategiq.co/google-trends-connector-for-data-studio/


________________


Let’s start!
________________




Step 1
Open Brand reports folder https://drive.google.com/drive/u/0/folders/16dUgvOKyyZkqGATW8lHqz0uqmFbGhUgp


________________




Step 2
Select brand report that you would like to copy (friendly suggestion: try Pariurix Brand Growth) (Some brand reports might be different than others. For example, BC brand growth is a corporate brand report that is not commercial in nature as we have it for other brands. Betarades reports are oriented towards TV ads, so stick with the one you find relevant the most. Suggestion: use Pariurix brand report to copy over and over again.)
________________




Step 3
Once you opened the report, navigate your mouse pointer to the upper right corner and click copy
  

________________


Step 4
Its time to select (or create) new data sources to feed your new copied report
  



________________




Step 5
Under ‘New data sources’ select one by one and change it to the one you would like to use for the new copied report. Select the data source by clicking on the field. You will get all previous choices listed (scroll up and down to find the one you need). If you haven't used data source before, then, you will need to create it. Click Create New Data Source.
  



Creating a new data source:
Google Analytics:
  





Search connectors and select Google analytics. Then you will be offered to select account > property and view.




  



Once you select the view you want to include in your reports, hit in the upper right corner ‘Connect’. If you want to add special name to your data source you can do it before you hit connect in the upper left corner by clicking on ‘Untitled Data Source’.












Search Console:
Search through connectors and select Search console. 


  





Search console will offer you two types of connectors: URL impression and Site Impression. Make sure you selected the one needed for the charts in the previous step when you selecting the data source:


  



Selecting the right report, and name it properly to avoid any future confusion. Upper left corner where you submit the name of the source and upper right for connecting the data source to your new copied report.


  

Google Trends:
*Important: In order to see the connector for Google Trends first generate API Key to use it.


Search through connectors and select gtrends app.
  





Google Trends setup:
  



When you select Gtrends app, you will be asked to fill in the following details:
* Your API Key
* Phrase (brand terms you want to track can be divided by commas. Example: betarades, betarades.gr)
* Time range (select 2004 - Present > that is the historical overview covering all dates from the beginning of GTrends)
* Region (select targeted country where this brand should be performing the best)


Once you are done, name your data source (upper left corner) and connect data source to report (upper right corner).
________________


Step 6
All sources are connected now, copy your report:
  

Note: Sometimes, Google Trends App can be buggy. Although you followed all the steps above, your report might not be generated. In that case, just hit another time Copy button of the original report and select previously added data sources (since all of them will be visible in the list of data sources), hit copy report after you selected GA, Search console and GTrends. 




________________


Step 7
Changing filters and images


Change the logo of the report


Once you have copied the report, select the logo on the copied one and delete it. Navigate to the upper menu in Data studio and select ‘image’ 


  

Once you uploaded the image to report, at proper position *(upper left corner in the heading of the report), click right on the logo image and select: Report-level (this means that image will be presented across all pages in the report at that same spot.


  





Change filters
There are a few filters you will need to change in order for this report to work for you.
Navigate to the main menu of Data studio and select resources and further ‘Manage filters’:
  



Filters to change
Home page filter > 
change it to whatever relevant Home page URL is for your brand. The rule is the following: include Landing page equal to 
Do not change the rules, just the value for “Equal to”.
Branded Queries > 
This filter uses RegEx rule. (This guide might be helpful https://www.bounteous.com/files/uploads/Regular-Expressions-Google-Analytics_2019.pdf)
Again, do not change the rule, just the value under “RegExp Contains” where you are going to add your regex filter for branded keywords.
NonBranded Queries > 
Its the same as branded the only difference is that this time we are excluding all branded keywords. Again, change the value under “RegExp Contains” > list the same value/filter as you used for Branded Queries.


________________




Step 8


Change settings for Keyword cloud (usually page 5)


Keyword cloud for the new report will not work properly since you are missing custom field in the data source in order to split Branded vs nonBranded keywords.


Check Data source > click on the keyword cloud and on the right you will see two setup sections (Data and Style). Under data, you will see which Data source this keyword cloud used. You will have to edit the data source, and in order to do that, you will have to have enough permissions on your account to manipulate the data. Data source here should be ‘Site Impression’ from Search Console. 
Adding custom field > if you have enough permissions for the data source, you will se small pencil before the source name:


  



After that, you can select the option to create a field. Click ‘Add a field’ 
  



Name your new field ‘Branded Split’ and enter the formula:
  



Formula:
CASE WHEN REGEXP_MATCH(Query, (".*vegas insider.*|.*vegasinsider.*|.*vegasinsider.com.*")) THEN "Branded" ELSE "Non-Branded" END


Red string is RegEx filter that will provide branded search queries (you can use the one defined for Branded Queries filter). Copy the formula and put it in your new Field. 
Hit Save on the bottom and Finished in the upper right corner. 


Set the ‘Branded Filter’ in the Keyword cloud report > There is two filters above the Keyword cloud: Branded split (which in this phase is broken) and Query.


  



Click on the one that is broken and drag/drop the ‘Branded Split’ to it:
  



After you selected Branded split filter and its working, check Data source for Query filter as well. It has to be the same as Keyword cloud and Branded Split.


________________




Step 9
You are all set now!
View your report and make sure that all metrics are working. Go through each page of the report to check if you are missing data somewhere or have broken data. 
If all good, move the report in the Folder Brand Reporting under SEO drive and share it with people related to the brand. Suggestion: share only view permissions unless requested otherwise.