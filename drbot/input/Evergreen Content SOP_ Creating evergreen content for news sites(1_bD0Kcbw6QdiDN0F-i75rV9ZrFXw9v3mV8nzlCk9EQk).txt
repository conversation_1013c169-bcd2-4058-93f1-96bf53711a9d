﻿Evergreen Content SOP: Creating evergreen content for news sites 




Objective:  ensuring consistent creation of valuable, well researched, and engaging evergreen content for news sites.
For: News websites mainly, but also sites having evergreen content in general.
Last Updated: 10/04/2025
Made by: <PERSON><PERSON>
________________




Table of contents


Research Process
Step 1: Top performing entities identification
Step 2: Identifying evergreen content opportunities
Infographics opportunities
Checking for already existing content for this keyword/topic
Check SEO tools
Step 3: Competitor research
Production Process
Step 5: Producing the article - from an SEO perspective
Step 5: Keeping track of evergreen content


Research Process
Step 1: Top performing entities identification 


For a news site that looks to expand on its evergreen content and build on authority, it’s important to understand the entities around which we need to create evergreen content. 


Of course, evergreen content can be created regardless of entities and can be focused on any topic that a site is covering and looking to expand on.
However, for maximum impact, it is recommended that the strongest entities are identified and evergreen content produced first around them. 


Find top entities in Google Search Console 


Look at Google Search Console Search Performance report and identify which entity keywords (players, teams, leagues) have the most clicks, impressions and are ranking at best positions. Set the date filter to 6 months and look at queries data:


  



<PERSON> is the top performing entity for the site https://bolavip.com/en and the example that will be used in this SOP. 


Also, you can refer to <PERSON><PERSON><PERSON> to find the same data: 


1. I recommend using the Optimize report in Marfeel Insights:
  
 


2. Insert the time period you want to look at (I would go for 3 or 6 months)
3. Set the Goal to ‘Pageviews’
4. Drilldown should be by Keyword 
5. Apply a filter for the site you want 
6. Scroll down to the list/table and check which entity keywords had the most pageviews; especially those that are regularly covered and those that worked well but still showcase some discrepancies between the number of produced content vs pageviews (these are the ones where you can add a boost by writing some evergreen content, e.g. in the 2nd screenshot below, we should work on Mike Tomlin since there is probably some evergreen content lacking on this entity since we have a lot of articles produced, but they are giving medium performance; we probably should build more authority on him)


  

  



Step 2: Identifying evergreen content opportunities  


We’ve always used SEO tools to identify relevant keywords, but for the purposes of a news site, we will be looking first into Google Trends: 


1. Go to Trends and open the Explore tab. Set the relevant filter for the country and set the time first to ‘past hour’; if ‘past hour’ does not give you anything, increase to ‘past 4 hours’, but if that doesn’t work, set it to ‘past day’. Input the entity keyword: 


  



2. In the above example, there was some search interest for Andy Reid in the past day which is probably still going to have effect at the current moment. Now it is necessary to look at Related queries to identify any evergreen opportunities: 


  





As seen above, there are two evergreen queries. They are marked as ‘Rising’ and a percentage is given.


What does a rising query pertain to? 


* A rising query was searched for with the term you entered and had the most significant growth in volume over the selected time period. You'll see a percentage of the Rising term's growth compared to the previous time period.


What does a breakout query pertain to? 


* It means that a search term grew by more than 5000%.


3. Apart from these queries being ‘rising’or breakout  over the past day, it is necessary to check if the query is constantly searched for in order to prove its evergreen nature. 


Click on the query of choice, which will open a new window with data for that query. 
Increase the time frame to ‘past 12 months’ and look at the Interest over time graph: 


  



We can see that it hasn’t been searched for at all times in the past year, but there is still much interest spanning across a few months, so it is a valid opportunity. 


Check the other queries too: 


  



P.S. Sometimes it is valuable to look into Related queries for the evergreen ones you found. For example, for the given queries above, the related queries are also evergreen, and you can find ideas for other entities, or an additional query you can include together with the main one (e.g. ‘how old is tammy reid’ might be something to include in the Andy Reid evergreen article since she is his wife): 


  



Just to provide more examples, here is Trends data for Patrick Mahomes, another entity that we are not having as the top-performing one, but that we want to build authority on. 


  



Looks like there has been much interest in Mahomes in the past day, with an upward trend at the moment of searching for this query, which is another indication that we should be writing something about Mahomes at this particular moment. 


Even more important, if we manage to write and publish an evergreen piece at the time when the entity query demonstrates increased search interest, we will be able to have our article ranked both in Top Stories carousels and Discover. 


It is important to understand that time plays a critical role in the success of an evergreen article in Discover and Top Stories. It is best if someone can work on the article in the first 30 minutes from when a query was identified as rising in the graph itself (like for Patrick Mahomes); and we have to publish the evergreen article as soon as possible during the day. 


This way, we get additional traffic from the ‘news’ part of Google, plus regular traffic from ranking as a blue link over a longer time period. Look at some examples below to illustrate the point: 


Example 1:  https://bolavip.com/en/nba/bronny-james-net-worth   




Naturally, we had a spike in traffic at the moment of publishing, and the page was ranking at the top of the first page of SERP, but it also was in Top Stories: 


  



  



We managed to get additional traffic from Discover thanks to the swift reaction to the trend (Bronny James net worth was identified as a rising query) 


Example 2: https://bolavip.com/en/nba/stephen-curry-net-worth 


  







  



Infographics opportunities


While checking for keywords, try to think of potential infographic opportunities. 


Including an infographic in evergreen content leads to more engagement and more potential for shareability and attracting backlinks. 


You can opt for including an infographic straightaway or including it after some time to refresh an article. 


For using infographics, please refer to the Infographics SOP. 
Checking for already existing content for this keyword/topic


To avoid duplication/cannibalization issues, we want to check for any existing content for the given keyword/topic. [a]


Checking in Google Search Console 


Checking for already existing content is most easily done by looking at keywords your site ranks for in GSC.


Go to Search Performance report, set the time frame to 16 months and apply the query filter to include the main keyword (you can also use regex to combine multiple keywords): 


  



If you see that there is some performance for your keywords, check out which pages are/were ranking. For the keywords above, there has been some performance: 


  



When we look at the Pages, we see that it is different URLs that somehow caught these queries for short periods of time, and that a dedicated article doesn’t exist. However, we still want to make sure that these posts won’t meddle with the article we are going to make, so if possible, we should review these and see if there’s a way to interlink them with the new article using clear anchors.


  



Using Google Search to identify existing content 


If you don’t have access to GSC, you can use search operators. They are entered into the Google search box and here’s what you should do: 


1. Use site: operator in the format site:[your domain link] (for example, site:https://bolavip.com/en)
2. Add an advanced search operator to search for specific keywords you planned on targeting in your content: 
   1. intitle: Search only in the page's title for a word or phrase. Use exact-match for phrases.
   2. inurl: Look for a word or phrase in the URL path.
   3. Intext: Search for a word or phrase in the body. 


The advanced search operators are the ones that are most commonly used when identifying duplicate/existing content. To sum up, what you should input in the search bar can be: 
site:https://bolavip.com/en intitle:andy reid net worth (intitle used here, but you can opt for the other two operators depending on title and URL practices your team is following)


Example below:


  



Check SEO tools 


To be completely certain as to which keyword exactly to choose as the primary, it is recommended to check SEO tools (Ahrefs or Semrush). 


Just input the keywords and check their volume. In our case, we would choose ‘how old is andy reid’ instead of ‘andy reid age’ since the former one has greater search volume. 


  



It is up to you whether you think it is a good idea to write content only about an aspect of a topic, or if you want to expand it and target more keywords. In the case of Andy Reid age, it makes sense to create a profile article about this particular person, and include other relevant keywords, which you can identify using Ahrefs: 


Go to ‘Also rank for’ part in Ahrefs from the Overview page to find related queries: 




  



Once we view the entire list, we find keywords such as: 


andy reid retirement, who is andy reid’s twin brother, andy reid kids, andy reid family, andy reid career, andy reid’s wife, andy reid son, etc. 


Therefore, we would probably opt for an article covering all this. 
Step 3: Competitor research 


1. Use VPN or a third-party tool/extension (e.g. https://impersonal.me/ or https://chromewebstore.google.com/detail/seo-search-simulator-by-n/edfjfgjklednkencfhnokmkajbgfhpon?pli=1_ ) to check the SERP. 


I personally use Nightwatch (chrome web store link above). 


  





I look at the first ranking competitor (if it makes sense, that is, if it is not Wikipedia for example). If there is a featured snippet, then we must include the information from the snippet in the form in which it is presented in it. 


  



2. I use SEO META in 1 CLICK to get Headings and titles data. 


It is recommended to produce a content brief based on the all obtained data.
Production Process
Step 5: Producing the article - from an SEO perspective 


As SEOs we need to make sure that SEO best practices are followed.


For that, it’s best to provide a basic content brief for journalists to follow which should include the following necessary elements: 


1. Meta title and meta description
2. Headline (H1) 
3. OG title (known as ‘social title’ in journalist circles) 
   1. This one can be a little more ‘emotional’ and ‘appealing’ since this is what Google most often shows in Discover. 


4. Headings structure 
5. Secondary keywords 
6. Where necessary, indicate what data/information should be presented. 
7. Internal links from/to the article (including suggested anchor text)*




*7. Internal links planning in more detail: 


To make sure we build a topic cluster and show that we have a bunch of resources on a specific topic, we should take care of internal links (mainly referring to in-content ones). 


Two things to include in your content brief: incoming links and outgoing links. 


* How to identify pages to link from? 


* Review your existing content (use one of the methods mentioned in methods to find already existing content) and identify articles related to the topic of the new evergreen post.
* Look for articles with similar themes or concepts, complementary subjects, or broader topics that can include a link to this specific article.
* Check pages that have a high level of traffic or authority as these will help your new article get discovered and indexed faster.


* How to identify pages to link to?


* Review existing content related to the evergreen article. This could be news articles written on a specific topic, or other relevant evergreen content.
* Make sure the linked content enhances the user's understanding of the subject, adding value without overwhelming them.


* Choose the right anchor for both incoming and outgoing links


* Use the target keywords or related keywords (variations of the primary keyword) as anchor text for internal links, ensuring it ties into the topic you’re linking to.
* When adding incoming links (towards the article you’re making) avoid using the exact same anchor text across multiple links. Use variations as mentioned as this helps diversify your links and is more natural for readers.








For some examples, please refer to the document HERE that has a collection of 4 content briefs for evergreen pieces that we made for Bolavip US last year. 


Step 5: Keeping track of evergreen content 


1. Have a repository of evergreen content in a way that suits you most, so that it gets easier and easier to know what has been already covered. Example for Bolavip US.
   1. As you can see in the example doc, we also provide anchor texts for writers in case they find a suitable opportunity to link to an evergreen piece from another post. This is to avoid non-SEO friendly anchor texts and choose the most appropriate variations. 


2. Regularly check for performance and update as new data emerges.
   1. You can also go for infographics to refresh a page that has been dropping in position or to just add something new that is valuable to the user. We have an infographics SOP explaining criteria for implementing infographics and the process for obtaining them. 






[a]Great insight
One total reaction
Maja Jankovic reacted with 🥳 at 2025-04-11 02:02 am