﻿[General / SOP] Page Template SEO info gathering - HOMEPAGE


* HOMEPAGE - recap


* Meta Title 
   * max 60 characters
   * business name
   * explain what the website is all about with additional keywords - major, broad keywords - to get attention
* Meta Description
   * should be around 160 characters (min 50 & max 160 characters)
   * should be unique for each page
   * summarises the website content 
   * call to action keywords (Bet on!/ Bet in/ Join us!/ Learn how to win, etc.)
   * Add a date -  should be updated regularly (on a daily basis)
   * If we are dedicated to one market display that
* Content
   * Logo - the unique logo of the site
   * Navigation bar - containing all the most important pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussions/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. The navigation bar doesn’t have to contain all these pages. It depends from site to site. 
      * Search box (optional, not applicable for all sites’ homepages, but still good to have)
      * Sign in/Login button (if the website has a membership program) 
   * <H1>
      * No character limitation but try to keep it visually appealing, so do not make it too long.
      * Make h1 stand out. Make the letters more prominent than the rest of the text and headings.
   * Date (good to have, mark it up with the schema) - update on a daily basis
   * Disclaimer about Safe Gambling, (18+ | Play responsibly), Terms & Conditions apply - optional
   * Display of the main offer/most important thing that users get from us. - CTA
   * Other offers
* Schemas
   * Logo schema
   * Breadcrumbs - best practice. It helps bots/ search engines better understand the hierarchy of the pages.
   * Sitelink searchbox
   * Events
   * FAQ
* Footer
* Image
   * Add self-referencing canonicals above the footer and images
   * Ensure the ALT text is optimised; use appropriate titles and file names of the images themselves
* Use only high-resolution images. Recommendation is to use images of the people. 
* Avoid using .png images
* Every image below the fold should be lazyloaded
* SOPs for image optimisation:
   * Optimizing images for best SEO performanceOptimizing images for best SEO performance - part 2


General recommendations:
* Optimise homepage loading speed
* Check Mobile Friendliness - responsive design
* Make a good website structure = Make sure that all important pages are linked from the homepage. Review sitelinks shown in Google search and adjust your navigation and homepage structure accordingly.
* Compress images to load fast
* Optimise Your logo
* Add elements to establish trust with users (testimonials, reviews, etc.)
* All important information that we want Google to read and render as soon as possible we have to implement in HTML tags ⇒ tables in <table> tags, links in <a href=...> tags and not JS. 






* HOMEPAGE 
Homepage SEO is important for all websites. In many cases, it is the first page users see and it is one of the pages users will visit, even if they land on any other pages of your website. They will visit your homepage to find out more about your blog or business.


SEO wise it is very important to give Google a big hint (through your title, description, and content), on what the website is all about:


Title Tag (Meta Title):
Utilise all 60 characters you have at your disposal to blend keywords and business information in one sentence. The title tag should contain:
* business name
* additional keywords that explain what the website is all about. Some frequent options are: 
   * online sports betting, 
   * online casinos, 
   * betting predictions and tips, odds, 
   * live streaming, 
   * live betting, 
   * bonus codes, 
   * betting offers,
   * gambling news,
   * bookmaker reviews, etc.
Note: We do not use all these keywords in the meta title of a site, but those are the most common ones. Choose the most convenient ones for your site and implement them in the title. - not crucial


Comment: For the brand sites Google can change the meta title and shows just the name of the site: 
  

  

Even if those sites have different meta titles, it doesn’t mean that we shouldn’t optimise our meta titles. Sometimes it is very unclear what will Google find more important to display. 


Examples of other sites where is important to have a good meta title:


  

  

  

These sites are also very dominant and authoritative in their markets but Google chooses to show their meta titles. 


Sites used as examples:
https://www.vegasinsider.com/
https://www.betarades.gr/
https://www.spilxperten.com/
https://pariurix.com/
https://www.bettingexpert.com/ 


Meta Description:


Besides the Meta title, the Meta Description is what users will see in the SERPs, so you may want to give it a bit of thought.


Meta description length should be around 160 characters (min 50 & max 160 characters).
According to Google Guidelines, a meta description might be a sentence or two or even a short paragraph.


Elements to be included:
* The meta description should be unique for each page
* keywords (do not repeat keywords from Meta title). A good description accurately summarises the website content. Some frequent options for keywords are:  
   * online sports betting, 
   * sports betting information,
   * best online betting sites, 
   * online casinos, 
   * best online casino,
   * legal online casino games,
   * betting predictions and tips, 
   * odds, 
   * winning tips, 
   * hottest tips,
   * live streaming, 
   * live betting, 
   * bonus codes,
   * current bonuses,
   * welcome bonuses.
   * exclusive bonus,
   * free spins,
   * betting offers,
   * gambling news,
   * bookmaker reviews,
   * bookmaker guide, etc.
For Meta description besides mains and broader keywords, we also use long tail keywords (above). 
Also, call to action keywords:
   * Bet on!
   * Bet in
   * Join us!
   * Learn how to win
   * Let's Bet Today
   * Get your daily dose of analyses and picks!
  

  



Comment: Place a keyword in 2 or 3 catching phrases - maybe even in question form. 
The key is to get the attention of the users. 


* Add a date -  should be updated regularly (on daily basis)


* If we are dedicated to one market display that - for example:
  

  



Comment: Have in mind that Google may choose not to show your description but use their own. This may happen if your description is too short or too long, or if it includes a lot of keywords.


Sites used as examples:
https://zagranie.com/ 
https://cazino.ro/ 
https://bettingsidor.se/ 


Heading structure:


Headings are visible on the page, not on the SERPs.
Headings help structure the content of a web page by splitting information out into different sections. They help sighted users scan the page and find information quickly.


Best practice for heading structure: 


* Only use one <h1> for each web page.
* Don’t skip heading levels. For example, using a <h2> followed by an <h4>. It’s important that your headings are logical and hierarchical. This means that <h2> always follows <h1>.
* Make sure each heading is unique on individual web pages.
* Headings should describe the content that follows them.
* Make headings clear, useful and relevant.


<H1> defines the most important heading.
H1 tags should match the user intent.
It would be very similar to the Meta title because they have the assignment to say the same thing - what is the website all about, but do not have to be exactly the same.


Pariurix has the same Meta title and H1:
  

  



Elements to be included and other instructions:
* No character limitation but try to keep it visually appealing, so do not make it too long. Usually shorter than the Meta title. Needs to be the most prominent element above the fold.
   * Make h1 stand out. Make the letters more prominent than the rest of the text and headings.


* additional keywords that explain what the website is all about. Some frequent options are: 
   * online sports betting, 
   * online casinos, 
   * betting predictions and tips, odds, 
   * live streaming, 
   * live betting, 
   * bonus codes, 
   * betting offers,
   * gambling news,
   * bookmaker reviews, etc.
Note: We do not use all these keywords in the meta title of a site, but those are the most common ones. Choose the most convenient ones for your site and implement them in the title. 




Sites used as examples:
https://www.spilxperten.com/
https://pariurix.com/


Content:
* DESKTOP
Above the Fold - should contain:
* Logo - the unique logo of the site
* Navigation bar - containing all the most important pages of the site: 
   * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussions/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. Navigation bar doesn’t have to contain all these pages. (Underlined are the most important ones for our business). 
For an affiliate site, it is important to put all commercial hub pages into the navigation bar because those pages are the most important for the business. 
Which links should be in the navigation bar depends from site to site. For example, BettingExpert has specialised in betting tips, but some other site does not even have tips at all.
Those pages that are most common for the majority of affiliate sites are: 
Bookmakers hub and the good practice is to have a drop-down menu with the most important bookmakers
Bonus hub - the most common offer that affiliate sites are giving to the users is bonus codes (main offer that site has)
Betting tips/ odds (other offers that site has)
Casino hub - if a site has a casino section at all. Some of the sites don’t.
All these pages above are commercial because we are offering something to our users and the main goal is to get people on those pages where they can use our services.
Other important pages are informative pages where we show our expertise in the topic (in this case sports or casino), so we have also these pages n the Nav bar: 
News hub
Events page
Analysis
Live streaming
Guides


   * Search box (optional, not applicable for all sites’ homepages, but still good to have)
   * Sign in/Login button (if the website has a membership program) 
* H1 (explained above)
* Date (good to have, but not necessary for the Homepage)
* A short paragraph (teaser, short info about the website - 2 or 3 sentences with keywords) - not necessary, just one of the possibilities
* Disclaimer about Safe Gambling, (18+ | Play responsibly), Terms & Conditions apply
* Display of the main offer/most important thing that users get from us. That could be one of these sections:
   * Tip of the day/ Active tips/ Ticket of the day
   * Popular matches/ Upcoming events
   * News section
   * Bookmaker reviews 
   * Odds
   * Latest promotions/Bookmakers' predictions
   * Bookmakers bonuses
   * Casino Bonuses
   * Free spins
   * Giveaways - some benefits of the bookmakers
   * Live streamings, etc.


* CTA (button to one of the main offers of the site. It could be to another page of the site or directly to the Bookmakers site). It should be distinguished from the rest of the content. 
Bigger sites usually have CTA to another important page of the website. Pariurix: 
  

But some sites have both - CTA to another page and CTA affiliate link. Bettingsidor: 
  



Below the Fold - (both desktop and mobile) should contain:
After the main offer of the site, everything else goes. Homepage is one big hub page of the whole site and it contains all the most important sections of the site linking to those pages/sections.


Should contain all the important offers that the website has. These are just some of the possibilities: 


* Tip of the day/ Active tips/ Ticket of the day - Pariurix
  



* Popular matches/ Upcoming events - BettingExpert
  



* News section - Spilxperten
  



* Bookmaker reviews - Bettingsidor 
  



* Odds - VegasInsider
  



* Latest promotions/Bookmakers' predictions - Betarades
  



* Bookmakers bonuses - Zagranie
  

* Casino bonuses - Casinos.ro
  



* Free spins


* Giveaways - some benefits of the bookmakers


* Live streamings - Betarades
  



* Step-by-step instructions/ Guides section - Betarades
  



* Latest videos - Betarades
  



* Podcasts - Betarades
  



* Upcoming matches schedule - Spilxperten
  



* Best tipsters - BettingExpert
  



* Separate sports sections: Tennis, Football, Hockey, Basketball, etc. - Zagranie
  



* Separate competition sections: World Cup, Premier Liga, Bundesliga, Olympic games, European Cup, La Liga, NBA, NFL, NCAAF, UFC, Championship League, etc. - SoccerNews
  



* Our mission - SDA
  



* FAQ - Bettingsidor
  



* Footer with: Logo, Contac Us, Social icons (Facebook, Instagram, Twitter, Youtube, Spotify, etc.), Disclaimer, Terms and conditions, Cookie policy, About SpilXperten, Our writers, Responsible gaming, Terms of Service, House rules - Spilxperten
  



A good practice is that the footer contains (Do not stuff the footer with too many links): 
* Logo of the site - that is our brand, so people should recognise it
* Contact us link (user-friendly and EAT)
* Social icons (Facebook, Instagram, Twitter, etc.) - if the site has them
* Disclaimer
* About Us
All these links are not relevant to the business (they are not commercial), but they are good for EAT, because of them users trust us. They can contact us if they have any issues or read about the company so they can see how they rely on. Social icons also have the same effect.


Comment: 
* All these options should be displayed on the Homepage if the website has them.
* It is common that the Homepage doesn't have any textual content on it. Our sites contribute to this fact but definitely it is beneficial to have a few paragraphs explaining to users what they can find on the site and all offers they can get. 
Smaller sites have text on it (like Bettingsidor or Spilxperten).


  



Comment: Here are displayed only some solutions that we are conducting on our sites, but the possibilities are endless. 
* MOBILE
Above the Fold - should contain (same as for the desktop):
* Logo
* Navigation bar
* H1 
* Date 
* Disclaimer about Safe Gambling (18+ | Play responsibly)
* CTA (button to one of the main offers of the site. It could be to another page of the site or directly to the Bookmakers site). 


Sites used as examples:
https://www.vegasinsider.com/
https://www.betarades.gr/
https://www.spilxperten.com/
https://bettingsidor.se/
https://pariurix.com/
https://zagranie.com/
https://www.bettingexpert.com/
https://www.casinos.ro/
https://www.sites-de-apostas.net/
https://www.onlinewedden24.com/
https://www.wettbasis.com/
https://speltips.se/
https://www.goal.pl/




Schema:
* Logo schema (Betarades)
Example of the code: 


<script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organisation",
      "url": "http://www.example.com",
      "logo": "http://www.example.com/images/logo.png"
    }
    </script>


* Breadcrumbs - (Bettingsidor) not necessary, because the Homepage is level zero.
When we implement Breadcrumbs on the whole site it is natural to have it on the Homepage too.


Example of the code: 


<script type="application/ld+json">
{
        "@context": "http://schema.org/",
        "@type": "BreadcrumbList",
        "itemListElement": [
                {
                        "@type": "ListItem",
                        "position": 1,
                        "name": "Home",
                        "item": "https://bettingsidor.se/"
                }
        ]
}
</script>


* Sitelinks searchbox - not very common to show on SERP, but BettingExpert has one even if we didn’t put a schema on it: 
  



Example of the code: 


<script type="application/ld+json">
{
  "@context": "https://schema.org/",
  "@type": "WebSite",
  "name": "BettingExpert",
  "url": "https://www.bettingexpert.com/",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://www.bettingexpert.com/search?q={search_term_string}{search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>




* Events - not a necessity 


Example of the code: 


    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Event",
      "name": "Name",
      "startDate": "2025-07-21T19:00:00-05:00",
      "endDate": "2025-07-21T23:00-05:00",
      "eventStatus": "https://schema.org/EventScheduled",
      "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
      "location": {
        "@type": "VirtualLocation",
        "url": "https://operaonline.stream5.com/"
        },
      "image": [
        "https://example.com/photos/1x1/photo.jpg",
        "https://example.com/photos/4x3/photo.jpg",
        "https://example.com/photos/16x9/photo.jpg"
       ],
      "description": "Description.",
      "offers": {
        "@type": "Offer",
        "url": "https://www.example.com/event/",
        "price": "100",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock",
        "validFrom": "2024-05-21T12:00"
      },
      "performer": {
        "@type": "PerformingGroup",
        "name": "Name1"
      },
      "organiser": {
        "@type": "Organisation",
        "name": "NameOrganization",
        "url": "https://example.com"
      }
    }
    </script>
 


* FAQ - (Casinos.ro) for small sites. Because a small site usually has a Hub bookmaker page as a Homepage. 


Example of the code: 


<script type="application/ld+json">
{
        "@context": "http://schema.org/",
        "@type": "FAQPage",
        "mainEntity": [
                {
                        "@type": "Question",
                        "name": "Where are the best online casinos in Romania?",
                        "acceptedAnswer": {
                                "@type": "Answer",
                                "text": "The best online casinos are those that have a valid ONJN licence and comply with Romanian legislation. Read reviews, check bonuses and games available at the online casino where you want to play and thus you will find out which is the best online casino for you."
                        }
                },
                {
                        "@type": "Question",
                        "name": "Is online gambling safe?",
                        "acceptedAnswer": {
                                "@type": "Answer",
                                "text": "Yes, as long as you play at legal casinos authorised by ONJN. Play at an online casino that is recommended and verified and before any deposit check the bonus and withdrawal conditions. Pay attention to the customer support service as well as to the ratings received from users for the casino you are going to play at."
                        }
                },
                {
                        "@type": "Question",
                        "name": "Which online casino pays out the fastest?",
                        "acceptedAnswer": {
                                "@type": "Answer",
                                "text": "All online casinos pay out very quickly. There are casinos that pay winnings on the same day. Withdrawals are usually processed within two to three business days. There are online casinos that are much faster and process and transfer payments in just minutes."
                        }
                }
        ]
}
</script>


Comment: If we decide to have FAQs on the Homepage the recommendation is to have at least 3 questions with answers because Google shows two or three questions on the SERP. Before implementing, conduct research on the competition to see what they are covering. 


Do not have to implement all these schemas - it depends from site to site. 




OTHER PAGES:
* OPERATOR REVIEW HUB
* OPERATOR REVIEW PAGE
* OPERATOR BONUS HUB
* OPERATOR BONUS PAGE