﻿Disavow Links SOP
 Standard Operating Procedure
  

	













	SOP - Disavow Links
	Implementation Date
	-
	Last Reviewed/Update Date
	21st June 2021
	SOP Owner
	<PERSON><PERSON><PERSON> / <PERSON>
	Secondary Approval
	-
	

General steps for determining which links should be disavowed 
Disavowing of links should be done only in rare cases when we see drops in traffic and rankings after getting suspicious backlinks, and we can attribute the traffic/ranking drop to the backlinks. It’s advisable to disavow links that fulfill the criteria defined below. Note that the ones mentioned at the top, or near the top have a higher priority for disavowing, and the priority decreases as we approach the 7th point since the links are less toxic and damaging:


1. Suspicious anchor texts
2. High spam score
3. Spammy networks with medium/high spam scores
4. Spammy sites
5. Spammy directory sites
6. Sites are live
7. Non-niche related websites with low or no-excitant traffic
8. Old blogs on blogger.com


Note: during the first four steps, we are creating lists and performing checks, but the rest of the steps from 4 to 9 should be done simultaneously with the previous 4 steps.




Detailed Description
Suspicious anchor texts
Start in Ahrefs and check the Live section, not historical, and discover suspicious anchor texts (viagra, etc. Chinese and other similar language anchors). For Chinese and other similar languages, this requires a detailed check, if sites have traffic and seem ok, with no high spam score, they can stay. Check anchors with some random numbers, such as www.domain.com/*********** or random numbers without the domain name in the anchor. They are mostly from spammy PBN networks, so you need to check the domains behind the anchors too.


1. Insert the desired URL into the Ahrefs search


  





2. Navigate to the “Anchors” report in the side menu


  





3. Perform check always in the “Live” section


  





4. In the search on the right-hand side insert and check term by term anchors such as viagra, sex, porn, xxx, girls, pills, loan, Cialis, bank, and similar terms. For the sites with other languages perform the check with the same terms but translated into that particular language as well.
  





5. Manually go try all live anchors texts and identify and check the suspicious anchors on the languages different from the language on the site that you are checking such as Chinese, Japanese, Thailand, etc.


  



Spammy networks with medium/high spam scores
In the Ahrefs tool from the same list of all anchors, try to identify spammy private blogging networks “PBN” by analyzing anchors. Notice that the anchor texts for these spammy domains can be normal, and it will be hard to identify them quickly. While checking URLs try to identify them since most sites in the same spammy PBN network have the same anchors, such as http://the-globe.com/, http://theglobe.org/, the same or very similar design, they might seem unnoticeable since they have a network of a large number of sites and no one has a high spam score in particular, but they are spammy. It’s ok to do random checks of anchors and identify some sites. The link disavowing is a continuous process and it’s not necessary to disavow all bad links at once. 


Also, check in the Majestic tool, the IP neighborhood tool, to check if detected spammy sites are part of one PBN network. They will usually be a part of one PBN network if they are parked on the same domain. 


1. In the Majestic tool, find in the navigation menu “Tools” and in the dropdown choose “Neighborhood Checker”.
  

2. Type in the search box the exact IP address or domain without extensions http:// or https:// or / slash at the end as you can see on the images below.


  





3. The results will show up in the table. In this example case, we have only one domain on this IP address but in case if there is a PBN network the list will be longer. It doesn’t necessarily have to be the case of spammy PBN if in the results we find more sites because sometimes there are more sites on the shared hosting plan. We need to check those sites and see if they belong to the same group/network of sites by indicators like the same or very similar design, same or very similar layout, similar domain names, same content/topic related sites.


  

High spam score
This check is performed in the tool Moz. URLs with high spam score mostly higher than 75-80% should be disavowed by default, and from 50-70% spam score the links can stay as long as their websites have some decent organic traffic, decent Domain Authority, and/or are topically content-related, or if anchors are fine and links seem natural.


1. In the MOZ tool find in the navigation menu “Moz Pro” and in the dropdown choose “Link Explorer”.


  



2. In the search field insert the URL of the site for which you are performing the check.
  





3. Navigate to the “Spam Score” report in the left sidebar menu.


  

4. On the “Spam Score” report page, you will find the chart with the number and percentage of URL spam score, and on the image below you can see the general approach to the check.


  





5. On the same page, you will find the list of the URLs with metrics Spam Score and Domain Authority. 


  



* Spam Score from 70% up to 100% - delete
* Spam Score from 50% up to   70% - detailed check
* Spam Score from 30% up to   50% - regular check
* Spam Score from   0% up to   30% - no need for check


The detailed check would include finding the URLs where our site is linked and see if the link is contextual meaning is the link natural, fitting in the context of the page is it related to the topic, where is the link positioned on the page (header, post body, sidebar, footer), is it visible or hidden (is it actually on the page or just in the HTML code).


The regular check would include the random URL check to get an overall impression of the page and site itself.




Spammy sites 
In the Google Search Console we can find the list of top linking sites that show the root domain for all of the “backlinks” that are pointing towards the site. The whole table is sorted from highest to lowest and features a count of your total external links, and gives us a nice summary of backlinks profile. This report won’t be affected by our site’s disavow file, so links that have been disavowed will still be counted here. Manually go try the list and check the sites, are they recognizable and make sense in the context of our business. If we see any unknown, dishonest, or spammy sites there’s a possibility that these could be placing us in a bad link neighborhood and we should consider disavowing them.


1. Login and chose desired domain property


  



2. Navigate to the “Links” report in the left sidebar and within go to the “Top linking sites” report


  



3. Manually check the sites in the list 


  



Try to identify and check the suspicious backlinks from suspicious sites. Example:
  



Spammy directory sites 


They usually have thousands of links to other sites and are mostly spammy. One example of such sites: http://www.urlje.com/t/2/48 - the site is low quality, with numerous links to other sites, high spam score, and low DA score. If these domains have no traffic, and the DA is low, and the spam score is medium to high, it’s fine to disavow those links. 
This check should be simultaneously performed while going through the lists from Ahrefs and Moz in the previous steps.
DA and PA metrics you can check in the MOZ or faster solution with their chrome addon.


  



Sites are live 
If the sites that send backlinks are not live now, you don’t need to disavow that site, in general. However, you need to check the Ahrefs live section, since the links from that deleted site still might be visible. In that case, we also need to disavow them, since Google probably sees them.
This check should be simultaneously performed while going through the lists from Ahrefs and Moz in the previous steps.


Non-niche related websites with low or non-excitant traffic and unnatural links
Niche unrelated sites are ok to stay, however, if that kind of site has low or no organic traffic, low domain authority, no contextual links, and if you’re sure it doesn’t bring any SEO value, it can be disavowed. In case you’re not sure about it, it’s best to keep it. To determine whether or not to disavow specific sites, you need to assess the traffic, domain authority, spam score, and if the backlinks are unnatural or not. If the site matches a few or all criteria listed here, the site is good for disavowing. 
This check should be simultaneously performed while going through the lists from Ahrefs and Moz in the previous steps.
Old blogs on blogger.com
If Ahrefs shows links coming from deleted blogger.com blogs or blogs that are not being updated for years, unrelated to our niche, with no organic traffic, and low DA, they can be disavowed. 


Sports betting sites with overly optimized anchors and too many links towards the site
If you can identify numerous backlinks (dozens or more) coming from one specific website, with overly optimized anchor text (anchors that are over-optimized in the sense that from one site we have a lot of links with the same anchor, or anchor that does not match the content to which it sends), while the site itself is without traffic, and has a medium/high spam score, it can be disavowed.
How to create a disavow file?
A disavowed file should be created in the .txt format. The best option is to add all the domains or URLs you want to be disavowed in the Google Sheets first, format it, and then download as a .txt file.


Example of Google Sheet file: 


  



Useful formulas for formatting: 


=COUNTIF(A:A,A1)>1 - to help you identify duplicate sites into Google sheet. Choose the column that you used to paste the sites, and add it to the formula. In our case it’s A. Apply conditional formatting to the entire column with populated fields.  If the sites are duplicate, they will be marked green. 


These are certain formatting rules for this file. If you disavow entire domains, you need to put “domain:” before each website. Put only one disavowed website in one line. If you disavow URLs, you add the specific URLs without “domain:”.


Example of how the file should look in .txt in case you want to disavow the entire domains, and not specific links: 
  

If you disavow the entire domain, none of the backlinks from this site will be visible. This is recommended in case the site is with a high spam score, low DA, looks very suspicious, and the link from that site can negatively affect our website. 


Here’s how it looks like then specific URLs are added to the file: 


  



Disavowing the one specific url can be an option when the backlinks is not coming from a particularly spammy or suspicious website, but when you find a link on that site that seems unnatural (with strange anchor text, or not matching to the context of the page) or suspicious (spammy anchor text such as porn, viagra, not visible on the page, but visible in the page source). 
How to upload a disavow file?
The disavow file can be uploaded to Google Search Console. 
1. Go to Google Search Console Disavow Tool”. 
2. When you enter it, choose your property for disavowing like in the image below:
  

3. Click on your desired property for which you want to disavow backlinks. NOTE: Have in mind that you can have a disavow file just for a property version of the website (https://www.betarades.gr/) and not for the full domain (betarades.gr).
4. After you click on your desired property it will show you if there are already disavow files and important is to have that disavow file downloaded and implemented into a new disavow file.


  

5. Once you are finished with finding bad backlinks, click on “Replace”, choose a file from your computer and that’s it. You’ve uploaded a disavow file, congratulations!




Sometimes you can have an already submitted disavowed file into Google Search Console, like in the case above. Do not delete the old disavow file, instead download it and update it with the new sites you would like to disavow, because you want to keep the old spammy sites disavowed, too.
Implementation 


The best practice is to disavow around 100 websites per round and track the effects of it afterward. Disavowing too many backlinks may have harmful effects on your site. The disavowing is therefore done in phases, usually once or twice per year.