﻿Health Check SOP
 Standard Operating Procedure
  

	













	SOP - Health Check
	Implementation Date
	-
	Last Reviewed/Update Date
	-
	SOP Owner
	<PERSON>
	Secondary Approval
	-
	

General steps for performing a site health check 
The purpose of the health check is to look over some of the most important things related to the site's performance, detect problems, and provide potential solutions.


There are two health check templates. Whichever you decide to use please make your copy of the template on your drive. To perform a quick health check of a site just to identify the problems use the Health Check - Template which contains several sections divided per tool in which you can perform the provided checks.
To perform an extended health check of a site use the Health Check - Extended Template whose purpose is to detect the problems, give solutions for them, and also to have separate sheets where you can create reports for each check with screenshots and extracted data from the tools all in one document.
Updated version of the Health Check - Template 2.0 is available and recomended for performing the check (the SOP will be updated and adapted i in near future).
To get screenshots of the whole page from the tools reports use the GoFullPage - Full Page Screen Capture chrome addon.








Tools/Sections:


* Google Search Console
   * Performance
   * Index
   * Experience
   * Enhancements
   * Security & Manual Actions
   * Links
* Ahrefs
* MOZ
* ScreamingFrog
* Lighthouse
* Robots.txt file




Google Search Console (GSC)
In the GSC we will perform checks in several different reports. The first one will be in the Performance section report Search results.


Select the property and navigate to the performance section and Search results report.


  
  



Performance section


  



The overall overview is the first check in this section. Here we can check total clicks, total impressions, average click try rate, and average positions. We are checking overall site performance and looking if there are drops regarding the mentioned metrics.


  



Also, we should check which pages are performing well and which aren't, from which countries we are getting the traffic that matches our market strategy, and from which devices we are getting traffic, finally on the search appearance tab check how we are performing regarding Google SERP widgets.


  



Sections of the site are the second check where we should check how different parts of the site are performing like commercial ones bookmaker pages, bonus pages, tips pages, etc.
We can filter them out by adding the filter for pages and we have a few options as well as regular expressions.


  



Here are a few useful links regarding regular expressions Regex Filtering, regex for SEO: A Guide To Regular Expressions.


Rankings of Top / VIP keywords is the next check where we should check on the queries tab how we are ranking for the major and commercial keywords, the keyword list you can place/find on the VIP Keywords spreadsheet in the template.


  



Regarding keywords, you can also have a look at Tableau and Mangools.




Discover is the next report where we should check the overall performance for Google Discover and detect is there unusual behavior like drops in clicks, an unusual balance between clicks, impressions, CTR


  





Google News is the next report where we should check the overall performance for Google News and detect if is there unusual behavior like drops in clicks, or an unusual balance between clicks, impressions, and CTR.


  







Index section


  





Coverage report is a report where we perform several checks like errors, valid with warnings, and excluded.


Errors are the first check where we are discovering if there are any errors, which type of error is about, and on which pages they refer. These URLs are pages that couldn't be indexed for some reason.
Why does it matter? These pages won't appear in Google, which can mean a loss of traffic to our site.
What should I do? Click into an error row in the table to see details, including how to fix it.
  



Here is an example of the error types that can occur.
  



Click on the error type in the table and you will access the subreport listing the URLs related to this error. You can export the data via the Export button above the chart.


  



By clicking on one of the URLs the sidebar will appear with additional help in this case it is a link to the official tool for testing the URL.


  



On the top of the report, there will be a small section with the link “Learn more” usually leading to the https://support.google.com/webmasters … official site where they explain a little bit more about this particular case where you can inform more in-depth.


  



Valid with warnings is the next check where we are discovering the URLs which are pages that have been indexed, but there are some issues and we're not sure if they are intentional on your part.
Why does it matter? These appear in Google now, but perhaps you didn't want them to.
What should I do? Click into a warning row in the table to see details about the warning and decide if the detected issue is intentional on your part.


  







Here is an example of valid warnings that can occur.


  



Click on the warning type in the table and you will access the subreport listing the URLs related to this warning. You can export the data via the Export button above the chart.


  



By clicking on one of the URLs the sidebar will appear with additional help in this case it is a link to the official tool for testing the URL.


  



On the top of the report, there will be a small section with the link “Learn more” usually leading to the https://support.google.com/webmasters … official site where they explain a little bit more about this particular case where you can inform more in-depth.


  



Excluded is the next check where we discover the URLs which are pages that were intentionally not indexed.
Why does it matter? These pages won't appear in Google, but that was probably intentional.
What should I do? Click on a row in the table to see details.


  



Here is an example of excluded types that can occur.


  



Click on the excluded type in the table and you will access the subreport listing the URLs related to this warning. You can export the data via the Export button above the chart.






  



By clicking on one of the URLs the sidebar will appear with additional help in this case it is a link to the official tool for testing the URL.


  



On the top of the report, there will be a small section with the link “Learn more” usually leading to the https://support.google.com/webmasters … official site where they explain a little bit more about this particular case where you can inform more in-depth.


  











Sitemap report is a report where we perform several checks.


Sitemaps submitted check is about checking are our sitemaps are submitted, updated, formatted/divided structurally properly.
Sitemap errors are checks where we are examining do we have any errors, and which errors occurred.


  



Clicking on one of the reported errors we will see the subreport explaining in more detail what is this error about.


  



Experience section


  



In this section, we are checking several things like page experience signals, core web vitals regarding mobile and desktop versions, and mobile usability.


Page experience report check is an overall check of parameters and introduction into the next two checks. Here we can point out the percentages of URLs with a good page experience on mobile and good URLs on a desktop which you can find at the top of the report. In this report. Pages are evaluated separately for mobile and desktop so below you have insight into summary data of the page experience signals for mobile & desktop regarding core web vitals, mobile usability, and HTTPS. If there are some issues just comment them out in the template and proceed with the next checks.


  



Core web vitals is the next check that we perform in the same-named report which contains overall data presented in charts for the mobile and desktop versions separately. The following step is that we perform the check for each of them by clicking on the report.


  



Mobile CWV checks we perform examining the 'Poor' URLs that have at least one 'Poor' issue that should be addressed, as well as the 'Need improvement' URLs that have no 'poor' issues, but at least one 'Need improvement' issue.


  



For each of them, you will have the list below with the status and issue types. Usually status refers to various measures like
* LCP (largest contentful paint): How long it takes the page to render the largest visible element
* FID (first input delay): How long it takes the page to start responding to user actions
* CLS (cumulative layout shift): How much does the page UI shift during page loading.
* Speed issues are assigned separately for desktop and mobile users.
Clicking on them you will get to the sub-report with additional details.


  



There may be only one example but more pages with the same problem. In that case, if you click on the URL example there will be a presented sidebar with an additional list of URLs with links to the Pagespeed Insights testing tool.
  



Mobile usability report is the next test where we should check for errors if these pages have important mobile usability issues. In the table below check the status and error type, clicking on the error you can access the sub-report with additional information.


  









Enhancements section


In this section, there are several checks to perform but they are not all present on all sites/GSC properties. If the site doesn’t have some of the provided checks in GSC just skip them, to make the report look cleaner you can hide rows containing empty checks or delete those rows.
Breadcrumbs
Events
FAQ
AMP
How-to
Logos
Products
Review snippets
Sitelinks searchbox
Unparsable structured data
Videos


  



All checks mentioned above should be done in a similar manner checking errors and warnings their status, type, and URLs/pages that are affected.


Here is one example of a FAQ error report.


  



When clicking on the error type we are accessing the sub-menu where we can find URLs affected. By clicking on the URL in the table below the sidebar will appear with additional information.


  



Example for FAQ warnings report.


  



When clicking on the warning type we are accessing the sub-menu where we can find URLs affected. By clicking on the URL in the table below the sidebar will appear with additional information.


  



All other checks and reports in the enhancements section have the same principle of performing the checks and almost the same kind of reports.










Security & Manual Actions


  



In these two checks, we are checking if there is some problem. If everything is ok they will look like this.




Manual Actions


  



Security issues


  



Links section


  



In this section, there are a few reports regarding internal and external links where we should perform checks.
 External links present the links from outside your property to your property and contain reports top top-linked pages, top-linking sites, top linking text.


Top linked pages is a report where we can check pages in our property linked from external pages. Check manually the Top linking pages report looking for pages with an unusually big number of backlinks to its importance and links to suspicious/weird URLs.


  



Top linking sites is a report where we can check links from outside your property to your property. Values are trimmed to the root domain and grouped. If the current property is listed here, it is because the root domain of the host page has been omitted. For example, if the link is from www.example.com, the value shown here would be example.com. Check manually the top linking sites report for suspicious URLs.


  



The top linking text “anchors” is a report where we can check link text in external pages that link to our property. Check manually the Top linking text report for suspicious anchors.


  



 Internal links section where we are checking links from our property to our property. 


Top linked pages is a report where we can check pages in our property linked from other pages in our property. Check manually the Internal links report, looking for pages with a lot of internal links that they shouldn't have, suspicious URLs with strange extensions that have internal links, or anything looking irregular.


  



Ahrefs




  



Site Audit Overview
To use this report the site needs to have a created project and be crawled. You can access it on the homepage of Ahrefs in the list or try the “Site Audit” link in the navigation menu. In this report, we check a top-level view of a website’s on-page and technical SEO issues. See the Health Score on your website.
Health Score is the percentage of internal URLs on your site that don’t have errors. We take the number of internal URLs without an error, divide it by the total number of internal URLs, and multiply it by 100 to get a percentage. This is a quick way to check whether your website’s technical performance is doing well.


  



See the top issues affecting your site.
The “Top issues” table shows the top 10 errors based on the total number of URLs that match the issue. You can see all issues by clicking “View all issues” below the table.


  



Click on any of the issues to see more details about the issue and instructions on how to fix it.


  



Click on View affected URLs, and it’ll open Page Explorer with the affected URLs.


In case you don’t have the crawl of the site or that you for some reason can’t crawl the site with Ahrefs place the link of your site into the search bar, set the domains with all its subdomains, and click the search.


  
 




This report makes a general overview of the reports on the tabs Backlink profile, Organic search, and Paid search looking for irregularities.
On the “Backlink profile” tab pay attention to things like big drops or spikes in charts regarding:
* referring domains,
  



* referring pages,




  



* new & lost referring domains,


  



* new & lost backlinks,


  







* weird anchors,
  



* does the proportion of referring domains look natural between dofollow, governmental, educational, gov, edu, com, net, org,
  



* also URL rating distribution proportion.


  

On the “Organic search” tab check for drops or spikes on the charts regarding: 
* organic traffic,
  



* organic keywords,
  



* organic positions chart shows how frequently the keywords that a target website or URL is ranking for change their rankings over time do we have an increase or decrease of keywords.
  





Those checks will be like an introduction to the next checks.
Referring domains
There are two reports in Ahrefs where you can check referring domains and you can find them in the sidebar.
  
  

In those reports, you will find lists of unique domains that have at least one link to the target website or URL.
On the top of the reports, you have various filters that you can apply if you want to check specific groups of referring domains.


  



  
 
In the lists visually look for suspicious URLs and manually open the sites to check if there are any adult or spammy sites. Check the most important ones from the first page and the depth/number of URLs checked depends on your time frame.
  



Referring pages


        There are two reports in Ahrefs where you can check referring pages and you can find them in the sidebar.
  
  



Those reports list the pages of external websites that link to the website, subsection, or URL that you analyze.
In the lists visually look for suspicious URLs and manually open them to check if there are any adult or spammy sites. Check the most important ones from the first page and the depth/number of URLs checked depends on your time frame.


  



Also, check the widely distributed links from the sites. Google classes widely distributed links in the footers or templates of various sites as a link scheme that can “negatively impact a site's ranking in search results. These are the kinds of links that you should track down. To help find them, make sure the Backlinks report is set to “Group similar” mode, add a “dofollow” filter, and then sort by the Similar column from high to low. This will show backlinks with the same or similar anchor and surrounding text which are often sitewide links.


  



Hit the caret on any suspicious-looking websites (i.e., the sites from which you wouldn't expect to have this quantity of backlinks).


  



Anchors


There are two reports in Ahrefs where you can check anchors and you can find them in the sidebar.


  
  



Those reports show all anchor phrases and terms of backlinks pointing to a target website or URL. Open them and check visually if there are any adult or weird anchors with signs Chinese/arab signs or anything suspicious.
  







MOZ




Navigate to the “Spam score” report in the navigation bar.
  

Enter the domain in the search bar, set the “root domain” and search.


  

Below you will have the list of suspicious URLs that you should check and decide which ones are spammy and create a list for disawow file.


  



Pay attention to the URLs with scores of 61%-100% which are considered a High Spam Score, also check URLs with scores of 31%-60% which are considered a Medium Spam Score.
ScreamingFrog
  





Run the crawl with the ScreamingFrog tool. After its completion, you will get the summary overall report in the sidebar on the right side. Check the following sections for potential problems:


* Security


  





















* Response codes


  



* Url


  



* Page titles


  







* Meta description


  



* H tags


  



* Content


  



* Images
  



* Canonicals


  



* Pagination


  



* Directives


  



* Hreflang


  



* Javascript


  















* AMP


  



* Structured data


  



* Sitemaps


  



Clicking on each of the rows you will be transferred to the related report tab and get the list of URLs related to the field you have clicked.
You can export that data if needed via the “Export” button.


  















Lighthouse


  



This check is performed for individual URLs regarding the mobile and desktop versions. Make a list of a few important URLs of the site like the homepage, bookmaker overview, bookmaker review, tip page, match page, etc… the end performs checks for each one of them. In the report, there is a slot for checking one URL, to have more slots click on the arrows on the left side, or if you need even more feel free to extend the report as much as needed.
  



Open the incognito window and go to the desired URL. After the page is loaded access the lighthouse by right-clicking on the page and selecting inspect, then from the sidebar navigation menu select lighthouse report.


  



Scan the site for the mobile-first and repeat the same check for the desktop afterward.


  



Click the “generate the report”


Overall scores
You will be presented with the report with the scores from different aspects. 


  

And each of them will have its section report describing problems in detail.


Performance report
  



Check for opportunities:


  

Clicking on the arrow down you will see an expanded report with all the details for further investigation.


  



Check for diagnostics:


  



Clicking on the arrow down you will see an expanded report with all the details for further investigation.


  



Accessibility report


  

Clicking on the arrow down you will see an expanded report with all the details for further investigation.






Best Practices report


  

Clicking on the arrow down you will see an expanded report with all the details for further investigation.


SEO report


  

Clicking on the arrow down you will see an expanded report with all the details for further investigation.


PWA report


  

Clicking on the arrow down you will see an expanded report with all the details for further investigation.


Note down all important issues from all reports that can be fixed and improve the health/performance of the site.
Robots.txt file
You can control which files crawlers may access on your site with a robots.txt file. A robots.txt file lives at the root of your site. So, for the site www.example.com, the robots.txt file lives at www.example.com/robots.txt. robots.txt is a plain text file that follows the Robots Exclusion Standard. A robots.txt file consists of one or more rules. Each rule blocks or allows access for a given crawler to a specified file path on that website. Unless you specify otherwise in your robots.txt file, all files are implicitly allowed for crawling.


Check in the robots.txt file are the directives/rules are defined properly and which bots are disallowed/allowed for crawling which sections on the site.


One of the worst cases you can find is


User-agent: * Disallow: /


meaning "Blocking all web crawlers from all content".


Useful robots.txt rules:


https://developers.google.com/search/docs/advanced/robots/create-robots-txt?hl=en&visit_id=637810486177859014-1690299014&rd=1