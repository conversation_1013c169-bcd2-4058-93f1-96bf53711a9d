﻿Initial audit
Necessary tools
The audit process
Step 1 - Link penalties/demotions
Step 2 - Domain spam test
Step 3 - IP neighborhood check
Step 4 - Domain metrics from different tools
Soft audit
Necessary tools
The audit process
Step 1 - Referring domains vs backlinks ratio
Step 2 - PBN Check: Top 5 Referring IPs
Step 3 - Anchor Text Classification
Step 4 - URL rating distribution %
We will be using the old Ahrefs report for this section. After you’ve placed the root domain there, you will find this information on the right below.
Step 5 - Common Link Types
Step 6 - Topical Trust Flow
Step 7 - RD dofollow %
Step 8 - RD to commercial pages
Step 9 - Domain Summary


Initial audit


NB: The points here go in this order: 0 is the best, 3 is the worst
Necessary tools
The first thing to watch out for is whether you have Google Analytics and Google Search Console access. If so, make sure that the data is tracked correctly and that you have enough to perform the audit. If there is an issue, you will need to rely on tools for traffic estimates, specifically SEMrush or Ahrefs.
You would also need access to MOZ for assessing the link profile and the spam score of the domain.
The final tool you need for this audit is Majestic, which is primarily a link research tool, but can give you some insights into whether the domain was a victim of a negative SEO campaign or spammy backlink tactics.
The audit process
Step 1 - Link penalties/demotions
The point of this step is to check whether a bad link profile was to blame for any manual penalties or demotions due to core updates focused on backlinks.
If you have GSC, you can check the Security and Manual Actions section for any manual demotions that Google might have imposed.
  

This is the way the rating should be given based on the result of the check:
* Active link action, the site is de-indexed in Google: 3
* Active link action, the site is still indexed: 2
* No active link action, but we have info that the site has been historically penalized: 1
* No historical or current action: 0
Regardless of the presence of any negative manual actions in GSC, we should always check SEMrush for any dramatic traffic drops correlated closely with any major link-oriented algorithm updates by Google.
Place the domain in the search bar of SEMrush and select the suggestion that has “Domain Overview” written next to it.
  

You should get a dashboard that has the following graphs on its right-hand side. Select the data for All Time to be able to get a better understanding of the domain's traffic and organic ranking development and you can narrow down the period later if you want to hone in on a specific suspicious period.
  

The Organic Keywords report is quite useful for this as it gives you a timeline of all the Google updates (confirmed and assumed), which you can click on to see what they were about. You can also use the external link to read more about the update. If it had a link component in it and the site’s rankings were hit by it, there is a suspicion that the link profile of the site was responsible for the drop in rankings.
  
  

The rating system for this check should be the following:
* Yes, big, recent drop: 3
* Yes: small, recent drop: 2
* Yes: small to big drop, now recovered: 1
* No: 0
Write down the conclusions in the field under Analysis.
Step 2 - Domain spam test
For this check, we will be using MOZ and their spam check, which will give us the domain’s spam score.
Open the home page of the MOZ tool and enter the domain with no https:// or www. in the field of the Link Explorer and click the magnifying glass or hit enter.
  

You will get the following screen and on the left will find the option of Spam Score. Click on it and view the spam score.
  

Write it down in the spreadsheet and give the ratings based on the following criteria:
* 2 or below: 0
* 3-10%: 1
* 11-25%: 2
* 26+%: 3
If you want to deep dive and give a more detailed comment on the link profile structure, you take a look at why the spam score has the rating that it does and look at the backlinks and referring domains with the biggest spam score. However, this is more of an exercise as this will be done during a Soft SEO Audit.
Write down the explanation in the field next to the rating if there is anything to note.
Step 3 - IP neighborhood check
The point of this exercise is to see whether the site is hosted on the same server as some spammy domains. The spammier its “neighborhood” is, the less quality Google ascribes to your domain. There are two levels to this check - checking who shares the same IP with you and checking who is on the same C Block of your IP. The former is far more important and has a bigger impact, but the latter is not to be ignored either.
Here is an explanation from Fernando Raymond:
“You chose the cheapest hosting plan. It cost you less than half of what an average hosting provider would charge. You believe you got a fantastic deal, but no matter how much you try, your site won’t march up the rankings.
This is because a slew of bad websites – porn, loan scammers, online gambling sites also had the bright idea of hosting their site on the cheapest server. Since Google algorithm has observed that 80% of the content coming out of the C Block of that server is questionable and has lumped you with them.”
In other words, we want to make sure that the domains on the same IP and with the same C Block are legitimate and that there are no controversial or spammy sites or PBNs on them.
This is done through Majestic. The first step is to select Neigbourhood Checker from the Tools dropdown on the home page.
  

Once there, enter your domain without https:// or www. In the search field and hit enter.
  

You will get two lists - the one on the left shows the domains on the same IP as you and the one on the right shows those hosted on the same C Block.
  

Ideally, the domain should be the only one hosted on that exact IP. However, if there are more domains there, you need to check them by
* Visiting the site and making sure it is not a spam site, an adult site, or a site dealing with any controversial topics like selling viagra
* Checkin their spam score via MOZ as described above
* Checking their Topical Trust Flow (the topic Majestic deciphers from the backlink profile of the site) by clicking on the number in the colored square next to the domain
  

* Doing a more in-depth check of the domain’s backlink profile by clicking the gear icon and going through the backlinks and referring domains in Majestic (click on Site Explorer)
  

There are two options to rate the domains on the identical IP - OK or Suspicious. If they are OK, no negative points are given (rating of 0); if they are suspicious, then ascribe a negative rating in line with the negative quality of the neighborhood (1, 2, or 3). Note that this rating will have to be combined with the rating for the C Block to get the final rating.
We continue with the C Block domain check, which is done in the same fashion as the check for the domains on the same IP, the difference being that you cannot go through them all, but browse through 10 random sites in the list and think about the points given above.
Once you have reached the conclusion for the C Block as well, combine the ratings into a unified rating and write down the reasoning behind this rating in the field under Analysis.
With both of these checks always have in mind the following questions:
* What's the quality/feel of these domains? 
* Are they spammy/thin content? 
* Do they seem like PBNs?


Step 4 - Domain metrics from different tools
Moz: Domain Authority
40 or above: 0
39-27: 1
26-14: 2
13-0: 3


Ahrefs: Domain Rating
40 or above: 0
39-27: 1
26-14: 2
13-0: 3


Majestic: Trust Flow
15 or above: 0
14-10: 1
9-4: 2
3-0: 3
In this section, you can check whether their DA or DR has jumped suddenly because it might mean that the site has started to build links aggressively. The reason a site may do this is that either the people doing SEO for the site don’t know how to build a natural-looking link profile or they either want to pump up the metrics so that they seem like the site is really awesome. A site may often lose the backlinks after its sale, and then the metrics will drop. 
To check this you have to place the site in the search bar of Ahrefs, and then navigate to the “Overview section”. This is an old section of Ahrefs, but it is where you will find this information.
  



Scroll down and you will see this graphic. Look at the past two years. The example below is a site whose domain rating has been rising steadily in the last two years. Remember that in the old section of Ahrefs to see metrics for the entire site you have to choose domain with all its subdomains. In the new Ahrefs, it’s different. 
  

Below you will see a site that has a suspicious rise of DR in the last two years. 


  



DA is a metric that is really hard to increase, so if it has a huge jump, then that might not be completely natural. Place the site in Moz in the Link Research section, and then check the Overview section. Be sure that the root domain is chosen here. 
  

Scroll down to see this graphic.
  

The image above is a normal rise of DA. The image below is slightly suspicious considering that the DA increased by 20 in the span of a month, so further investigation is needed. 
  



Also, check the progression of the referring domains they’ve gained during the years. Check the site in the Overview 2.0 section of Ahrefs which is the new version of Ahrefs, subdomain should be chosen since this is a way to see the metrics for the entire site here.   
If it’s a steady progression then it’s fine. 
  

This jump below might indicate that someone started to build links aggressively all of a sudden. 
  



Unfortunately, this jump can also indicate that the site was a victim of the “blogspot” spam that has been happening to both our sites and the competitors. A site may gain thousands of links from various .blogspot sites in one day, so you should also check just how much of the link profile is comprised of blogspots. If it’s 90% then this jump might indicate that the site is ok from the point of building links. The blogspot spam is automated spam which means that the site owners didn’t gain those links on purpose. 
  

To check that, navigate to the Backlinks section, group similar is placed as default. Go into More Filters, and choose Domain name.
  

Pick Contains and then write “blogspot” in the field. 
  









Only blogspot links:
  

All links:
  

Compare how much of the backlink profile is comprised of blogspot links. If it’s more than 15% then the site has likely been hit with a blogspot spam attack. 
Google says that it doesn’t look at these kinds of links when it comes to evaluating the link profile of a site, so this is something that should be mentioned, but it’s not a big problem. We haven’t seen up until now that any of our sites have had a downward trajectory because of this. It’s still good to see this because you can’t really say that the site has “gained or built” 90k links, as in reality, it has gained only, for example, 2k links from non-blogspot sites. Those 2k sites are what we will look at in the soft audit. 
We can also indicate that we should disavow these links if we buy the site since it will take up more of our time after the potential purchase, that is, more of our time and effort spent. 
If the metrics are all in the good range when you only analyze the metrics numbers, and you’ve given this section zero points because of that, and if you see anything spammy regarding how those metrics have been achieved you can place:
3 - if a site has a really suspicious link profile based on the analysis above, it seems they’ve built links aggressively so the metrics increased pretty quickly
2 - if a site has a suspicious link profile in the sense that it has gained a lot of blogspot spam and you’re not sure if the rise in metrics is because of that and you need to take a further look in the soft audit
1 - if a site’s metrics haven’t risen in a suspicious manner, but you think the picture is not completely clear and should be checked further
0 - you can’t find anything suspicious




Step 5 - Redirection
Check whether a site has been redirected from one domain to another recently, for example from xxxx.co domain to xxxx.net domain. If it was redirected 3,4 years ago then that is ok, but if it was redirected recently, then it might mean that they redirected it because the old domain was struck by a penalty and they wanted to mask it because of the sale, so further investigation is needed. You should also ask the site owners why they decided to redirect the site. 
Sites will often have links pointing to their old URLs, so you can find redirect chains and see when the redirection approximately happened. This is also valuable to check since SEO tools for links will often show this as a huge number of domains registered at once for one domain and it can look like a site has decided to build links aggressively from the start, when this possibly might not be true. 
To check this place the root domain into the search section in the new Ahrefs reports, go into the Backlinks report on Ahrefs. Navigate to the More filters section, where you will select No. of redirects.


  



Choose One redirect from the dropdown menu or Redirect chain. It’s not a mistake to also look at both since you can’t choose both to be shown at once. 
  







Choose First seen until the arrow points up since you want to see when the first link pointing to a redirection was seen by Ahrefs. Check when it was first seen and analyze it according to the instructions above. 


  

  

If you want to check this in more detail, you can export the redirect chains in Google sheets since it’s easier to visualize the situation that way. To do this you will need to use the old Ahrefs.
At the end of the left Ahrefs menu, you will find the legacy section. Choose Backlinks. Group Similar is chosen by default. 


  



You don’t have to give points for this section, but this will help you in the final evaluation as to whether the site had been redirected prior to the sale and if they’ve been building links aggressively from the start. 




Soft audit


NB: The points here go in this order for M&As - 3 is the best, 0 is the worst


Caution when using this template for Media partnerships since in those cases 0 is the best, 3 is the worst. In that case you would need to reverse the points that are explained below.


Necessary tools
You will need access to Ahrefs, Moz, and Majestic. 


The audit process


Step 1 - Referring domains vs backlinks ratio


Just place the root domain in Ahrefs and you will see this information beneath. Ahrefs will automatically transfer the path to subdomains since this is a way to see everything regarding a site here. 


  



Place the numbers in the template and the point will be calculated automatically. 


This is important to check because if the site has a big discrepancy between the backlinks and the referring domains it means that it might have a lot of sitewide links. Or it could mean that the majority of their links come from, for example, 4 sites and if we lose those links we would lose a lot of backlinks in turn. Points will be manually calculated in the sheet. 


You should also manually check from where the site has sitewide links. Those links are usually found in the menus, footers, headers, sidebars, etc. They can be in the form of a text or an image (banner).


Go to the referring domains section, and click on Links to Target in order to sort the referring domains according to how many backlinks they’re sending to the site. Open them and see where the link is. If there are too many sites to check, check a couple of the first ones and for the rest, we can assume they’re sitewide links. We can usually say that if a site sends more than 100 links toward a site then we can count it as a sitewide link.






  



As we can see, wettbasis.com has 1 million links from austriansoccerboart.at. When we enter the site we can see that the site has been placed in the sidebar and that is where those links come from. If we were to lose this link we would lose a million backlinks, so that would not be good. 


  



Find a couple of more examples and place your opinion in the template. 




Step 2 - PBN Check: Top 5 Referring IPs
Place the root domain in the search field in the new Ahrefs report. Go to the Referring IPs section on the right.


  



The point here is to check if you have a lot of sites linking to you from the same IP address or the same subnet of the IP address. If you do, then it might show that the site has been using PBNs hosted on the same IP address to create links which is a spammy tactic. Using PBNs from different IP addresses is ok. 




  





Group by subnet is already placed as default which will group all IPs hosted on the same IP address, but you can also use the All IPs if it’s easier for you. Domains section is also already placed to be sorted from top to bottom by default. Go through the sites in the first 5 IP address subnets and check to see if they are PBNs. 


PBN patterns: Check if they are all linking to one another. If they are linking to each other then that is bad practice considering that they are not trying to disguise their link manipulation. 


PBN quality: Check the quality of the PBNs, do they look like PBNs or news sites? Do they have thin content and not a lot of it? Do they have a legitimate contact section and social media profiles?


PBN footprint: are the PBNs hosted on the same subnet or not?




NB: if the site has been hit with a blogspot spam then the first couple of IP addresses might be filled with them and then the PBNs might be hidden somewhere after all of that blogspot spam. In that case, just go one by one until you eliminate all of the blogspots, and then look at the sites you can find in the first 5 IP subnets. 


  

































































You would have to find subnets like these:




  



Points:


3 - they don’t have a lot of sites and PBNs hosted on the same subnet and IP address
2 - they have PBNs that are not interlinked and look ok
1 - they have PBNs that are interlinked and hosted on the same subnet
0 - they have PBNs hosted on the same subnet, they look awful and they are linking to each other
Step 3 - Anchor Text Classification


Go into Ahrefs, place the root domain of the site and export the site’s backlinks. You should use Group similar and this is placed as the default option. If a site has been hit with blogspot spam, you can filter those links out if there are a lot of them (thousands).


More filters > all rules > doesn’t contain > blogspot > show results




  



Export > all xxx number of rows


  



Import into a sheet and then analyze the anchor text profile. 


Short tail are anchors that have less than 2 words, long tail anchors have more than 2 words. You can use this formula to calculate that =IF(A2="","",COUNTA(SPLIT(A2," "))). Just change the A2 cell to whichever cell your anchor text is, and then place the count of each type in the template. 


As far as the brand vs commercial, you can use filters in order to filter out where your brand name (name of the site) is being mentioned. 


Data > filter views > create your own filter




  





Filter by values > clear > type the name of your site in the search field > select all 


  
  





You will then get all of the anchors where your site brand is mentioned. Place the number of occurrences in the template. Be sure to filter out URL mentions since those aren’t the best representatives of brand anchors. In this case wettbasis, wett basis and wettbasis.com are better brand anchors than https://www.wettbasis.com/. You can also include them, not a big problem, but be sure to mention that in the report. I would also filter out anchors that have brand mentions with more than 10 words since those are most likely anchors from sites that automatically link out to every site on the web or are from news directories. The anchors from the image above are perfect representations of branded anchors. 


As far as the commercial anchors are concerned, you can do this by filtering out in the same manner as above all anchors that mention betting, bets, bonus codes, promo codes, welcome offers, etc. Basically, all commercial-type keywords that you can think of. To help you with foreign sites, you can use this formula to translate the anchors: =GOOGLETRANSLATE(A2, “auto”, “en”)


Points to give:


Percentage of commercial anchors - 


0% - 10%: 3


10% - 20%: 2 


20% - 40%: 1


40% - 100%: 0


While you are exploring this part, go into Ahrefs and Majestic into their anchor text section and check if there are any suspicious anchors that might indicate the site was a target of some negative SEO spam  and note it down(adult themes, chinese characters (unless it’s a chinese site of course), anything not related to the site (not regarding generic anchors such as read more, click here, etc)). 


Step 4 - URL rating distribution %
We will be using the old Ahrefs report for this section. After you’ve placed the root domain there, you will find this information on the right below.
  





Just place the percentages that you find here into the template and it will calculate the result.  








Step 5 - Common Link Types


Go again into the old Ahrefs and in the report on the right, you will get information regarding links coming from government and educational institutions. Place the numbers into the template.


Note: Make sure to open a few of the domains designated as educational or governmental. What could happen is that some old domains got repurposed and that these are actually not legitimate .edu and .gov domains. If they are fake, disregard them from the analysis.


  



As far as the webpage type, you will have to export the referring domains (dofollow) and in a separate sheet open each site in order to see their type. The types are: blog, forum, ecommerce, directory, and other. If there are too many referring domains, pick the top 100 for example and open them. 


As far as the points:


If there are gov and edu links = 3 
If there are mostly only blogs, without gov and edu links = 2
If there are only directories and forums mostly = 1
If they don’t have a lot of referring domains at all = 0


  





Step 6 - Topical Trust Flow


For this section we will be using Majestic. After you’ve placed the site (use root domain here), you will find the first top 3 topical trust flows. This metric is related to the main topics of the sites that are linking to the site we’re auditing. For our niche, it is completely normal for the first 3 topical trust flows to be related to sports or gambling for sports betting sites. For casino sites, it is great if the trust flows corresponds to games, video games, or gambling. Sports is also applicable for casino sites. Next in line for gambling sites would be news related trust flows considering that some gambling sites have a developed news section so it’s natural for other news outlets to link to them. 


If we are auditing a primarily sports news site, then the topics should be related to sports, news and regional. Gambling wouldn’t be normal here if the site doesn’t have a gambling section. Just think of what is logical, what topic is semantically related to another when it comes to the type of site you’re auditing.

Best for gambling sites would be in order of importance:


* Sports, gambling, games, video games (for casino), maybe even tech for casino
* News, regional (for example world/Italiano for an Italian site)
* Everything else wouldn’t be considered as good
* Adults are the worst :)




For example Business/Publishing and Printing, Society/Law or Arts/Music aren’t semantically related to the sites that we want. If any of the top 3 topical trust flows is Adult, then you can place 0 points, since that is something that we really don’t want in our link profile. If a site is not Chinese but its trust flow is for example Regional/Chinese then this might also indicate some spammy Chinese links and we can also place 0 points. 








  









































The image above shows an example of a great topical trust flow and we would place 3 points here. 






  



The image above shows an example of an ok topical trust flow since the site in question is related to sports betting. 


Since Majestic is showing only two topical trust flows, it’s important to investigate a little bit further. Go into the topics section on the left. 




  



Topical Trust Flow is calculated by using the strongest links so we can see that the strongest sports links are the biggest contributors. This is great, but the rest of the topics are so-so, so we can place either 3 or 2 (since the rest of the TTF are not exactly what we want). 




  





The image above shows a situation where we would assign 3 points since this is an Italian sports betting site that has a sports news section, so this is ok, nothing suspicious.






  



The image above shows a mostly great situation, except that we would prefer if the first 3 topical trust flows would be sports or gambling. News is great, but maybe not as the 1st TTF, so we can place 2 points here and suggest building more sports- or gambling-related links for the future. 




  



The image above shows an example of an Italian sports betting site, so Regional/Europe is ok, but the other topical trust flows are not so we can place 1 point.




  



Image above shows a European site, but we should place 0 points here since none of the TTF are related to the main topic of the site, which is sports betting. This means that the site doesn’t have a lot of strong semantically related links. 


Step 7 - RD dofollow %


It’s not normal for a site to have all of its backlinks and referring domains as dofollow. You can check this by again going to the old overview in Ahrefs. On the right side, you will find percentages of dofollow vs nofollow referring domains. 






  

  











































The points would go like this:




0% - 40% = 0


40% - 60% = 1


60% - 80% = 2


80% - 100% = 3


However, if a site has 99-100% of links as dofollow, then further investigation is needed. 




You can also check this in Moz. Just go into the Link Research - Overview section again, scroll down and you will see this section where you can check the ratio. 




  



Note for Sara: Perhaps add Majestic as well, check link profiles of several sites in order to see if they really don’t show the blogspot spam.
  

  







Step 8 - RD to commercial pages


We will use Ahrefs here and we’ll only be checking how many referring domains are going towards the commercial sections of the site. Basically, check all of the paths that have commercial value. Example: www.sample.com/odds*


  



Since, for example, this site has several commercial sections, we will check each one and then add all of the referring domains.




  



Points to give:


0-10 = 0
10-30 = 1
30-100 = 2
100+ = 3


Check how does the anchor text profile look for links pointing to commercial pages. Is it diversified or not?


Step 9 - Domain Summary


Based on everything that you’ve seen during this analysis, write a short summary of what is good and what is bad regarding the link profile. Do you see anything that is suspicious, such as mass link buying which might incur a penalty in the future? Do you see any negative SEO attacks? Is there blogspot spam that should be cleaned up in the future? Do you think that we might need to do a disavow if we buy the site? Do they have link building campaigns and how should we move with this in the future if we buy the site? Does that topical trust flow semantically align with the site? Do the majority of their backlinks come from sitewide links?