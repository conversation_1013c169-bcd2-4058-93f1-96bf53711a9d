﻿Infographics SOP 
Usage, criteria for implementation, creation workflow 
Objective: establish an approach to designing and implementing infographics on web pages according to certain criteria. 
For: News websites mainly, but also sites having evergreen content in general
Last Updated: 10/04/2025
Made by: <PERSON><PERSON>


________________




Table of contents 


1. Importance of Infographics
2. Criteria for infographics implementation
Choosing the right page
Criteria per page in more detail
Evergreen Content Pages (Informational / Long-form):
Hard News
Data-Driven News
Listicles (mixed: evergreen and news-based)
Live Blog Posting
3. Infographics Design
Request an infographic from the design team
Tools you can use yourself for infographics designing:
4. Adding the infographic to a page


1. Importance of Infographics 


Benefits of incorporating infographics: 


* Simplifying complex information: break down complex information into digestible visuals. 
* Boosting UX by creating more visual appeal: a large percentage of people prefer visual information over written content. 
* Increasing engagement time: engaging visuals keep users on the page longer (which positively impacts SEO) 
* Attracting backlinks: high quality infographics tend to be shared and cited, so they have a potential to attract backlinks. 
* Image search visibility: they can appear in Google Image results, which could lead to additional traffic.


2. Criteria for infographics implementation 
Choosing the right page 


There is a limited range of opportunities when it comes to pages that are suitable candidates for infographics implementation. 


Here is a general guide: 


Content type
	Infographic Suitability
	Reason 
	Infographic types
	Evergreen (profiles, net worth, records, stats, awards, history, etc.)
example
	High
	

Help break down complex or long-form content into digestible visuals. Highly shareable, linkable, and re-usable.
	



	

	Informational (summary) infographics; timelines; comparisons; rankings
	Data driven news (rankings, stats, records breaking, polls, data driven analyses)
example
	High 
	Turning numbers into impactful visuals like charts, timelines, heatmaps) to make them easier to understand.
	Charts (bar, line, pie); graphs; heat maps; choropleth maps
	Breaking (hard) news
example
	Low
	Only if pre-made infographics exist that would complement the article. Otherwise, it is hard to implement since speed of publishing is important.
	Mini infographics (quick facts, summarizing core points); timeline or sequence; map infographics
	Listicles (falls under evergreen, but can be in the sense of hard news - “top 5 players to watch out for at Super Bowl” and similar)
	Moderate
	Infographics can work as visual summaries (e.g. 5 players with most Super Bowl wins). Mainly intended for increasing shareability.
	List infographics; rankings; comparisons; 
	Live blog posting
	Moderate to low
	Timelines or maps can make live blog posting articles more engaging. This can be planned ahead of an event; ideally as a pre-event info sharing. This can be head to head records, player records, player comparisons, etc. 
	Progress infographics; mini infographics (quick facts)
	

Criteria per page in more detail
Evergreen Content Pages (Informational / Long-form):


✅ Page includes data, rankings, timelines, processes (e.g., “How a transfer works in football” → infographic of steps).
✅ Topic is link-worthy or re-usable (e.g., player records, transfer window breakdown, team records).
✅ Page receives steady or seasonal traffic (infographic is an opportunity to keep visitors engaged longer).

Pro Tip: Use Google Search Console to find opportunities - choose a longer time frame (3/6 months) and filter for pages that have steady impressions, but usually have fewer clicks and low CTR. Identify evergreen opportunities. 
Pro Tip 2: make a Looker studio report like this one used for FSN sites to identify URLs ranking for more than 10 days. It is easy to just quickly browse through and identify opportunities. When you’re dealing with a news site, it will mostly consist of news articles, but this could also be a way to find expansion opportunities, for instance: 
  

* This is an article about the Cowboys signing a player from the Eagles, the article was getting some performance over 14 days and a decent positioning, but CTR wasn’t that great. 
* We could update this with an infographic about the signed player where we visually present his career records and evolution, or we can do a new article about this (player profile). 


















Example: https://bolavip.com/en/nfl/patrick-mahomes-net-worth 
  



Hard News
✅ Content contains stats or recap (e.g., injuries by team, performance by conference, certain league champions visually represented on a map by country, summary of a match including final score, timeline of goals, MVPs, possession, shots on target, etc.).
✅ There is a developing story that benefits from visual updates (e.g. tournament progress).
✅ Topic has something to do with some (potentially) pre-made infographics we can add as additional information complementing the main topic. 
However, note that: 
 For breaking news, speed matters -  visuals must be:
* Verified and fact-checked
* Quickly understandable
* Branded for social sharing (if distribution is the goal)
Example infographic: https://www.the-sun.com/sport/13985402/ticket-katie-taylor-amanda-serrano-trilogy-ringwalk/ 
  

Data-Driven News


✅ Article includes large data sets or rankings (e.g.visualizing “Top goal scorers this week” or “Ronaldo’s records so far”)
✅ You’re presenting performance comparisons or breaking records (“Player A vs Player B”; “Fastest players ever”, etc.) 
✅ Data is original or exclusive (exclusive data analysis - this could also complement opinion based articles if data is being interpreted) 


Example infographic: https://www.nytimes.com/athletic/6266613/2025/04/11/mohamed-salah-goals-assists-form/ 


  



Listicles (mixed: evergreen and news-based) 


✅ List is thematic or ranked: “Top 10 players of this season” (both horizontal and vertical infographics will work and will add some visual appeal to the article) 
✅ The list is relevant to a major event (as a preparation for big events, you can include infographics in articles such as “5 players to watch at the World Cup”) 


Not always needed when:


* The list is very short (e.g. 3 items)
* No visual component applicable (e.g., “Top 5 new rules at Super Bowl”)


Live Blog Posting


✅ There’s a pause or slowdown in live updates (Infographics can serve as a filler between major updates) or as a warm up for the actual LBP
✅ Need for visual timeline or progression (when this could help users understand what happened so far) 


We tend to embed tweets presenting infographics to save time, but if you’re prepping for a live blog a few days before the event, it should be enough time to prepare 1-2 infographics


Embedded tweet infographic example: https://www.aljazeera.com/sports/liveblog/2025/4/8/live-arsenal-vs-real-madrid-uefa-champions-league-semifinal 
  



Example of a pre-made infographic: https://www.bbc.com/sport/football/live/cm2y8j9rgekt?page=13 


  



3. Infographics Design 


There are 2 options: 
1. Request an infographic from the design team 
2. Use a tool to create simple infographics 


Request an infographic from the design team 


* Go to https://betterc.atlassian.net/servicedesk/customer/portal/20 
* Choose Digital Design > Infographics Design 
* Fill in the required details: 


  

It’s important to indicate the deadline and priority, as well as to include the description. When it comes to the placement, you don’t have to provide a URL if a page is yet to be created. 


Width and height are desirable, but you can consult the design team to figure out the best dimension.
The most common format is 600 x 1200. 


Project description is the most important - clearly state what you need and provide a link to a spreadsheet such as this one with required data or send a link to a Wiki page (or other page from which you’d like to take the data). 


* You will receive an update that they’re on it. 
* Approximately, it takes around 2-3 days to get an infographic, but this depends on the design team’s availability.


Tools you can use yourself for infographics designing: 


1. Canva - it has plenty of templates that are ready to use with the free version, but it does require some knowledge to be able to make a suitable infographic. What you could do is ask the Social Media team for some ready made, brand infographic templates that could be easily modified. 
2. https://app.flourish.studio/templates - this app has both a free and a paid version, but a free version should be more than enough to get started. There are pre-made templates for simple data visualisation: 


  



There are available map templates: 


  

	

Bar charts: 


  



Tables that are simple but do have some design compared to HTML tables: 
  



Lineups, shot charts and tactics: 


  





	

To use this: 


1. Sign up for Flourish studio 
2. Make a new visualisation which will open up templates gallery 
3. Choose a template 
4. There are a bunch of design options in the right panel, and once you configure it, it can be downloaded as an image (e.g. snapshot-*************.png) or as HTML (if you’re running on a paid account). 


4. Adding the infographic to a page 
* Placement on the page 
Add to the relevant section of the content, which can be below main info as a summary or it can be above the fold if it is the primary feature of the page. 
* Alt text 
Write a concise and descriptive alt text, e.g: 
“Inforaphic showing Patrick Mahomes key achievements” 
* Schema Markup 
If the infographic is your main image, include it in structured data using ImageObject:
{
  "@context": "https://schema.org",
  "@type": "ImageObject",
  "contentUrl": "https://example.com/images/infographic",
  "description": "Infographic showing…",
  "name": "Player achievements",
  "uploadDate": "2025-04-11"
}