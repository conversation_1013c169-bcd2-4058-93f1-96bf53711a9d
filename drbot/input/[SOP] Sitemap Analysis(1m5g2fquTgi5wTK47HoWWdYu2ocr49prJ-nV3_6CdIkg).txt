﻿[SOP] Sitemap Analysis 


This SOP is designed to provide a comprehensive, step-by-step guide for individuals interested in conducting a Sitemap Analysis. 


A sitemap is a file where you provide information about the pages, videos, and other files on your site, and the relationships between them.
A sitemap tells Google which pages and files you think are important to your site, and also provides valuable information about these files.


Should you have any questions or comments when using this SOP, do not hesitate to reach out to Ana Ristic.


Table of contents (jump links to respective sections)
* How to find a sitemap index in GSC?
* How to configure Screaming Frog in order to crawl the entire website?
* How to configure Screaming Frog in order to crawl the Sitemaps of the particular website?
* Analysis - Step-by-step guide
* Additional things to take a look at
* Implementation options - 2 case studies with different options regarding removing URLs from the sitemap
* Useful resources 
* Previous reports regarding the sitemap analysis




How to find a sitemap index in GSC?
1. Navigate to the Google Search Console
2. Then go to the tab named Sitemaps 


  

   NB: The report will be similar to this one
  

3. Click on the sitemap index file and you will be given a list of all sitemaps present on the website you are analyzing
Take a look at the example below:
  

So, here you will see all the sitemap index URLs plus the total number of discovered pages which will help you to determine whether there is any discrepancy between the number of URLs in the sitemap and the number of URLs overall on the website.
Configuring Screaming Frog
Crawling the entire website with SF plus crawling the Sitemap with SF in order to see the discrepancy between the number of URLs
1. Configuring SF for the entire crawl of the website
      1. Go to the Configuration tab and then go further to the Spider tab
      2. In the Crawl tab, you should tick everything except the following things:        
         1. external links
         2. crawl outside of the start folder
         3. crawl all subdomains
         4. follow internal nofollow
         5. follow external nofollow
         6. XML sitemaps


      3. Then go to the tab named Extraction and here you should not crawl the following elements:
         1. HTTP headers
         2. Cookies
         3. Structured data
         4. PDF
      4. When it comes to the other tabs from the configuration - limits, rendering, advanced, and preferences - everything should be left as it is in the custom configuration.
      5. Make sure that you’ve selected the data to be stored from database storage. Selecting database storage will mean crawl data is saved to disk. This allows the SEO Spider to crawl more URLs, which is exactly what we want here.
  

      6. Speed - make sure that the max threads are set to 2 and max URLs are also set to 2. Look at the image below:
  

2. Configuring SF for the sitemap crawl
      1. Open Screaming Frog
      2. Go to the Mode tab and set the Mode to the List
      3. Go to the Configuration tab
      4. Then click on the Spider tab
      5. And tick everything except the following elements:
         1. follow internal nofollow
         2. follow external nofollow
      6. It is important to tick the following elements regarding the sitemaps:
  

Make sure that you’re crawling linked XML Sitemaps as well as that the function regarding discovering XML Sitemaps via robots.txt is enabled
Also, make sure that you linked the main sitemap index URL. 
      7. Choose ‘Upload A File’ or ‘Download XML Sitemap’
      8. Add a link to the main sitemap index
  



In this way SF will be able to crawl all the sitemap URLs present on a particular website and when you click OK you will be automatically given all the sitemap URLs that are discovered and will be crawled by SF as well as the number of all URLs in the sitemap.
      9. Make sure that the crawl depth is set to 0
      10. Start crawling!
      11. When the crawl is finished, the next thing you’re required to do is to click on the Crawl Analysis
      12. Then if you want to make sure that the ‘Sitemaps’ is ticked you go under ‘Crawl Analysis > Configure’ in order to check this one.
  

      13. When the crawl analysis has been completed the ‘analysis’ progress bar will be at 100% and the filters will no longer have the ‘(Crawl Analysis Required)’ message.
  



      14. Click ‘Sitemaps’ & you will be able to see various filters that will be valuable for your sitemap analysis. The filters are the following:
         1. URLs in Sitemap
         2. URLs not in Sitemap
         3. Orphan URLs
         4. Non-Indexable URLs in Sitemap
         5. URLs in Multiple Sitemaps
         6. XML Sitemap with over 50k URLs
         7. XML Sitemap
      15. If you want to see the XML Sitemap source you can do this by clicking ‘Inlinks’
If you have multiple XML Sitemaps, you’ll want to know which of the XML Sitemaps contains a non-indexable URL, orphan URL, etc.
To do this, simply click on a URL in the top window pane and then click on the ‘Inlinks’ tab at the bottom to populate the lower window pane. The ‘XML Sitemap’ type, is a reference to a URL from an XML Sitemap.




  

Analysis - Step-by-step guide
Export the URLs from the crawl (HTML part)
Export the URLs from the sitemap (HTML part)
  



Make separate tabs in Google Sheets named ‘Sitemap URLs’ and ‘Crawl URLs’
   1. In the Sitemap URLs tab, you should add the following formula:
      1. =IF(ISNA(VLOOKUP(A2,'Crawl URLs'!A:A,1,FALSE)), "No", "Yes")
 This one will help you to realize whether the URLs from the Sitemap are in the Crawl of the website. So, you will be given Yes if a particular URL is both in the sitemap and in the crawl. Oppositely, you will be given a No if the particular URL is in the sitemap but not in the crawl.
  

b. More important will be the Crawl URLs tab, which will show you the URLs in the crawl but not in the sitemap. There is a formula that you should use to help you discover that:
      2. =IF(ISNA(VLOOKUP(A2,'Sitemap URLs'!A:A,1,FALSE)), "No", "Yes")


Manually checking the lists
Therefore, you should go through this list manually and mark the important URLs that should be present in the sitemap.
Take a look at the example below:
          


Exporting Orphan Pages
Then you should export the Orphan pages that are in the Sitemap (this should not be the case) 
NB: It may be that case that there are no Orphan pages present in the Sitemap, and that is nice. 
However, here we hypothetically examine Orphan pages in the Sitemap. 
You should export that from SF. 
1. How to find Orphan pages in the sitemap with the help of SF?
   1. Sitemaps>Orphan URLs 
  

Also, there is a report for that matter in OnCrawl so you can export the data from both tools and see whether there is any discrepancy. 
1. How to find Orphan pages from the Sitemap in OnCrawl?
ii. Click on Indexability>Sitemaps 
  

2. You will be given an interesting chart showing you the following:
   1. Pages both in structure and in sitemaps
   2. Pages in structure not in sitemaps
   3. Sitemaps orphan pages
So, you will be able to see how much, in percentage, orphan pages are and that will be a nice starting point for you. 
  

3. You can just Click on Orphan URLs in the Graph and you will be given a list of Orphan URLs in Sitemaps.
A list will contain the Full URL, Status Code, and File Origin. 
The things you should check further regarding Orphan pages are: 
1. The status code and if there are some orphan pages that are indexable then you should just pull the data from GA to see whether there is any kind of performance issues
2. In order to pull the data you will need a formula to extract only the page path from the URL:
   1. SUBSTITUTE(text_to_search, search_for, replace_with, [occurrence_number]) 
   2. e.g. =SUBSTITUTE(H2,"https://www.sportwettentest.net","")
3. In order to pull the data you should go to GA4 and do the following:
   1. GA4>Choose the Property>Explore>Free form>Add exploration Name>Dimensions>Select: Page path and screen>Metrics>Views, Total Users, Average Session Duration, Bounce Rate, Exits, Entrances>Double click on each metrics so that they will be transferred to Values>Rows>Page path and screens>Filters>Page path and screens>matches regex>add a regex of the page paths you want to analyze
   2. The regex should start with ^ and end with $|
e.g.^/zulabet-live-wetten/$|^/zulabet-fussball-wetten/$|^/wettlexikon-cash-out/sportingbet/$|
NB: when you come to the last page in the regex do not end it with |, just with $
   3. The report will look like this:
  

   4. In this way, you will know which pages have a performance and should be interlinked better and left in the sitemap as well as which pages should be removed from the sitemap as soon as possible (those that do not have a performance at all).
   5. You can export the data and put it into Google Sheets for reference
NB: You need to be aware that Regex in GA has a limit.
   6. Those pages that are having a nice performance can be put into action points and you can say that those are the pages that should be interlinked better in the future. 
Search for 301, 4xx, or 5xx pages in the sitemap
In order to find out whether there are any non-indexable pages with inappropriate status codes, you’ll need to navigate to both SF and OnCrawl and export the following reports:
1. SF - Non-Indexable URLs in Sitemap
   1. Before exporting this report, you’ll need to filter out 301 redirected pages, as well as 4xxx or 5xx, if they exist in the sitemap, and then start with exporting. 
  



   2. OnCrawl - Indexability>Sitemaps
  



Pages in Sitemap report
1. Indexability>Sitemaps>Pages in Sitemaps
2. This report divides pages from different sections of the website, e.g. /goto/, /typy/, /wc2022/, /bookmakers/, and so on, and based on that you’ll be able to see the percentage of pages that are in sitemaps and those that are not in sitemaps from different sections of the website. Therefore, if you have any information that may be a potential threat you can just export this and work on resolving potential issues. 
  

Paginated pages in the Sitemap check
In order to check whether there are any paginated pages present in the sitemap, you should:
1. Navigate to the Screaming Frog
2. Then you should navigate to the Sitemaps>URLs in Sitemap
  

3. Then you’ll need to go through the list of pages to see whether there are any paginated pages. If you have noticed certain pages with pagination, you’ll need to filter them out so that the SF will show you only those pages. 
You can do this by navigating to the Search and filtering out that the Adress Contains(~) /page/ or /pagina/ or however the paginated pages are structured in the website you’re analyzing.
NB: Paginated pages should not be in the sitemap!  
Things you should also check
* Is there an author sitemap
* Is there a news sitemap
* Are the last modification dates okay (this means that you need to take a look at the dates and see whether those are fresh because we want to give Google the freshest information)
* Are there outdated pages present in the sitemaps (e.g. tips from 2017,2018,2019, outdated news articles, outdated forum pages, and so on)
* Are there goto links present in the sitemap (should not be the case)
* Check whether all relevant URLs are present in the sitemap (the Crawl URLs tab will be important for this kind of analysis)


 
Implementation options - 2 case studies with different options regarding removing URLs from the sitemap - noindexing pages/removing pages without deindexation
Setting pages to noindex in order to remove them from the sitemap
1. SPX case - unavailable_after tag causing errors in GSC
  

So, the problem here was that there were the following errors in GSC - Submitted URL marked ‘noindex’. Those URLs had unavailable_after tags that had reached their expiration date. 
 This is how the devs resolved this issue:
1. The unavailable after tag was there for each individual post but it wasn’t something of great help for them, this was just important so that they know the date range we emphasized
2. They selected all the posts directly and removed them from the sitemap with the help of Yoast sitemap functionality. They have found all the posts older than 6 months through the post ids and on that posts, they have added noindex tags for all the posts that should not be present in the sitemap. So, in this way, those pages are automatically removed from the sitemap.
3. So, in the part named ‘Allow search engines to show this Post in search results?’ they have put no, and a noindex tag was added automatically for each post/article that should be removed.
  

   1.  an interesting article regarding excluded content from the sitemap can be found here. They were doing this based on this article.


Q: An important question is: What do we need to tell them in the action points so that they will be able to understand us and implement everything properly?
A: The answer is: We need to tell them which kind of pages we want to remove from the sitemap. 
1. For example, if we want to remove /tips/ articles we need to tell them the exact year of the /tips/ articles and they will find the IDs of the posts and based on that they will noindex those posts and automatize removing them from the sitemap.
Removing pages without setting noindex
2. Another example comes from sites-de-apostas.net. 
1. After the initial sitemap analysis in which we determined that we want to exclude /tips/ pages from 2018, 2019, and 2020 from the sitemap, we wanted to check the crawl rates for those pages in order to see how frequently those pages are getting crawled.
 This was the first part of the task:
  

The purpose of this first part was:
1. To see whether the presence of these URLs in the sitemap causes them to be crawled even though they are old
2. To see whether removing those URLs from the sitemap would cause their crawl rates to drop


The outcome can be seen in this report that Sara Milačićmade:
1. The visits for old tips and offers made up 10% of all Googlebot hits for those sections for the last 3 months 
2. So, it has been decided that the tips and offer pages from 2019 and 2020 should be removed from the sitemap but without noindexing them


So, here we’re requiring that certain type of pages should be removed from the sitemap but without noindexing those pages. 
Q: What should we require from the developers in this case in order to be as concise as possible?
A: We should tell them that we want to get some URLs removed from the sitemap (it is preferred if we provide them with the category of the pages e.g. /tips/) and the date range. 


How do they process the task afterward?
  

1. There is a functionality of the Yoast that will get the URLs removed from the sitemap without setting noindex for them. 
2. This means that there are 2 hooks that developers use in order to do this:
   1. wpseo_posts_where
   2. wpseo_typecount_where


Useful resources to take a look at
* ScreamingFrog’s Guide for configuring
* Sitemap URL’s extractor


Previous reports regarding Sitemap Analysis
* Sportwettentest.net Sitemap Analysis
* Betarades Sitemap Analysis
* Pariurix Sitemap Analysis
* SDA Sitemap Analysis
* Wettfreunde Sitemap Analysis