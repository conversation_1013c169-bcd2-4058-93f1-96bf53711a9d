﻿This is an SOP that gives the rough outlines of the procedure needed for creating a content strategy for a major event. A major event can span a longer period, like the EURO, the World Cup, the NBA Playoffs, the FIBA World Cup, the Olympics etc., but it can also be a single event like a boxing match, the Champions League final, etc. Depending on the scope of the event, some of the points might not be relevant, but the general timeframe remains. The process is divided into four phases - the early phase, the phase right before the event, the event itself, and the post-event period.


Note that if you are an SEO consultant tasked to do this within BC, the preferred output is a clear and concise content plan that will outline which articles to write, what their general structure should be, what keywords they should target, and, in the case of very complex and important pages like the hub page or bookmaker overview pages, content briefs outlining the sections that the writers should handle. The output itself is optional, either in a Google Doc or a Google Sheet, but it has to be clear what the content manager should focus on, research and write at what period before the event. Here is a working example, which will be updated as the event coverage progresses.


First phase - you start before the topics hit popularity        2
Create a hub page that targets the main keyword of the event        2
Use Screaming Frog to find unused internal link opportunities        4
Start an active link building campaign towards the hub page        4
Create articles on topics that are useful for the readers        5
Use Google Trends to find possible topics        6
Keyword research with a pinch of salt        9
Creating a buzz for our website by covering current news from large media outlets        9
Create or update evergreen articles on the topic of the competition        10
Use event schema markup on pages where matches are listed to get a SERP enhancement        10
Specialized bookmaker articles for the event        12
Second phase - keeping up the momentum and starting to work on the tips/match analyses/match previews        13
Updating all the previously mentioned pages with new info as they come        13
Starting to work on the tips/match analysis/match preview pages        14
Continue and even intensify the production of news articles and internally linking them to the relevant evergreen/hub pages        14
Continue with the link building efforts towards key pages, now paying more attention to the specific match pages        14
Get ideas from the competitors content        14
Third phase - the event itself        16
Every match has to be covered by a tip/match analysis/match preview article and they are linked from every article where it makes sense        16
Fourth phase - after the event is over        17




First phase - you start before the topics hit popularity
Create a hub page that targets the main keyword of the event
      * Think about the URL of the hub - do you have an evergreen hub page for this competition or do you create one for each competition
      * If you have separate hubs/pages for other competitions, consider whether they are valuable (they have any search or internal traffic) or whether you should redirect them to the new hub page
         * You can find these in several ways:
            * Using the site: operator to search for the keyword either in the content or in the title, e.g. “site:domain.com keyword” or “site:domain.com intitle:keyword”
            * Use Screaming Frog to crawl the website and then go through the URLs manually or set up a custom search for the keyword of the competition
            * Use the search functionality of the website if it has one
      * The hub page should cover all the major topics on the competition, which can be modified as you develop other pages on the topics
         * Start off with the topics you know about the competition: general information, venues, ticket sales, groups, entries, favourites and then modify it to add more relevant info as you develop the content
         * The content should have the structure of a content hub, which means that it should be the pillar page that covers everything in brief, linking out to supporting articles that go into more detail
      * The internal links from the hub page should include the main keyword of the supporting article, the links from the supporting articles should include the main keyword of the hub page (with some variations)
      * The hub page should receive a prominent place in the navigational menu before the event, as the interest starts picking up, both for internal linking purposes and for ease of access by users
         * For starters, it can be placed in the sidebar/footer and then elevated to the main menu
         * Consider having a special event menu placement for these purposes, which can be modified for every competition
  

      * Funnel internal link equity to the page by placing internal links from the most important pages
         * Bookmaker articles should be used to link to the event content as well as they all offer betting on this competition
         * Specialized bookmaker pages (with paid placements) can also be used to boost the link profile
         * Identify the most important pages on your website by traffic and by links to add internal links from them to the hub page. If the adequate text for placing the link does not exist, add some content (a paragraph) with a well-crafted anchor text in order to get an in-context link that has more value and quality than just navigational links. To identify the most important pages for new internal links:
            * Use the GA Behavior > Site Content > All Pages/Landing pages report to see the pages with the most traffic
            * Use Ahrefs Site Explorer to plugin your domain and go to the Pages > Best by links report to find the pages with the biggest link equity
  

Use Screaming Frog to find unused internal link opportunities
* Go to Configuration > Custom > Search > insert keywords related to the competition you are targeting to find potential places for internal links towards the hub page; note that even mentions of previous iterations of the competition can be good internal linking opportunities if you modify the year
  

            * Use the news content written around this topic to link to the hub page or some of the supporting pages
            * If your website is writing tips, channel internal, in-content, contextual links towards the hub page where it makes sense
Start an active link building campaign towards the hub page
      * Target news and media outlets as they are the ones to get the most traffic from this topic and have the most authority
      * Use Ahrefs Site Explorer to audit your and your competitors’ link profile
         * Place the hub page for this or some of the previous events in the Site Explorer and check the backlinks we and they built
  

         * If it is not as easy to identify the hub pages, place the entire domain (ours or the competitor’s) in the Site Explorer, go to the Backlinks report and search for the competition in the anchor texts to see which domains were used for linking (alternatively, do a manual check of the backlinks)
  

Create articles on topics that are useful for the readers
Here we are talking more about articles that will be useful for the readers regardless of the search intent and that help you round off your topical authority (these are the supporting articles for the pillar hub page)
      * Who are the favourites for winning and the best scorer
      * The schedule of the matches
      * The opening match
      * Important stage URLs should be created in advance - quarterfinal, semi-final, final
         * The pages can target keywords such as “competition name + stage” and then be modified when you see what’s the exact keyword when people start searching for it
      * Group pages with info that we have at the moment
         * Target the keyword “competition name + group XYZ” and modify it when actual searches start coming in
      * Team pages for teams that might be interesting for the target audience - even other countries that are popular (e.g. Portugal for Brazil, Turkey, Poland for Germany)
         * Target the keyword “team + competition” and then modify it when the searches start coming in
      * Important match URLs can be created early
         * Target the team A vs. team B keywords
         * Expand this with odds, predictions and analyses as you get more information and time passes
         * Write a small intro for the match to have the URL start gathering age and authority if your main source of traffic is organic search
         * Note that this does not apply to sites that get the majority of their traffic from Google News - there, the majority of traffic is picked up if you publish the URLs in the few days before the match itself, even on the the day of the match
         * Rethink your keyword strategy based on the search terms people use
      * Venue pages - stadiums and host cities
         * If you already have these pages, just link them to the hub page
      * Most popular players to appear at the competition
      * If the team from the country your site covers qualified for the competition, write about their possible squad and put the emphasis on their matches
      * Where can you get the tickets for the match - important to start early as the ticket sales start early
Use Google Trends to find possible topics
      * Enter the main search term of the competition and go back to the year when it was last played
  

      * Check when the general “search buzz” for the related terms started (when the line started “growing”, March in this case)
      * Focus in on customized time ranges, starting from the month when you saw the activity starting
  

      * Focus on the terms that were trending, both Rising and Top and use them to fuel your article ideas
      * Repeat this for every month of the previous period, including the month of the event, and the month after the event
  

* Alternatively, you can also use Google Trends to regularly monitor topics that are trending in real time and to know about what you should write about
   * Search for the broad term you are interested in, select the country, the period (last 7 or 30 days), the area of interest and either go for Web or News Search (checking both makes sense)
   * This way, you will get more insights into topics that are on the rise or breaking out in the past few days and can cover them as soon as possible
  

  

Keyword research with a pinch of salt
Have in mind that some of the volumes are not correct and that some of the topics are not yet trending/accessible in the research tools
      * You can use either Keywordtool.io, Ahrefs Keywords Explorer or any other keyword research tool
      * Take the seed term and look at all the related queries
      * Have in mind that the search volume shown might be completely off as the volumes are the average for the past 12 months, so if a term just started getting more popular, the volume will likely grow; at the same time, something that was popular last year, will still have a rather sizeable search volume, but, in reality, no one will be searching for that
         * Therefore, make sure to check search volumes for each individual month of that year. That means you can see which month was the most popular and the search volumes in the lead up to the peak, giving you a better idea of the popularity of that search term
  

  

Creating a buzz for our website by covering current news from large media outlets
      * In order to be considered an authority on the subject, we need to have a lot of content on all matters, which also includes news
      * Based on your ambitions and the major milestones before and during the event, create a content calendar with all possible content pieces that should be written before, during and after the event and get an idea of the volume of content you need. Here is an example of of a layout that can be used
      * You need to plan out whether you have the resources for this type of content scale and, if you don’t, ideally, hire a specific freelancer for this coverage
      * The freelancer should follow major sports publications and cover the topics related to the event, teams in it, players etc.
      * Use internal linking from these articles to link to the hub page and related articles
      * Link the news article feed on the hub page 
      * Integrate with Google News
         * If we produce content on this level, we should be able to qualify for Google News
         * News articles unrelated to betting (editorials) improve our chances of getting into them
         * Ticking off everything from the Google News checklist (make a copy) Ken and OP compiled
         * The process of applying to Google News should be started as soon as possible because it takes a while to get approved and for the traffic to start flowing
Create or update evergreen articles on the topic of the competition
      * These can be articles on the previous iterations of the competition, previous champions, prominent teams at the previous competitions (e.g. Brazil at the World Cups), best scorers in history, the trophy
      * The articles should target keywords with an informative intent and complete the topical authority of the entire content hub
      * They can also be used for additional contextual internal linking
Use event schema markup on pages where matches are listed to get a SERP enhancement
      * Automate the process of adding schema to matches listed to get more space in the SERPs, for example, through the process described in this task
      * Make sure that the matches shown are current so you wouldn’t look outdated
      * If two teams are playing, use the SportsTeam type, if it is two persons, use the Person type
      * An example of the markup to be added for every match on the hub page:


* https://www.sites-de-apostas.net/prognosticos-noticias/category/prognosticos-de-apostas 
 <script type="application/ld+json">
{
"@context":"http://schema.org/",
"@type":"SportsEvent",
"location":{"@type":"Place","name":"Copa da Itália","address":"Copa da Itália"},
"name":"Spezia - Lecce",
"competitor":[
        {"@type":"SportsTeam","name":"Spezia"},
        {"@type":"SportsTeam","name":"Lecce"}
],
"startDate":"2021-12-16",
"url":"https://www.sites-de-apostas.net/prognosticos-noticias/palpite-spezia-x-lecce-copa-da-italia-prognostico-16-12-2021"
}</script>


* https://pariurix.com/ponturi (here we have the description and the image specified)
<script type="application/ld+json">
[{"@context":"http:\/\/schema.org","@type":"SportsEvent","location":{"@type":"Place","name":"Baschet NBA","address":"NBA"},"name":"Houston Rockets - New York Knicks","competitor":[{"@type":"SportsTeam","name":"Houston Rockets"},{"@type":"SportsTeam","name":"New York Knicks"}],"image":{"@type":"ImageObject","url":"https:\/\/pariurix.com\/media\/avancronici\/share\/287033\/pontul-zilei-287033.jpg","width":1200,"height":630},"description":"Aceast\u0103 partid\u0103 va reprezenta un duel al echipelor aflate \u00een partea de jos a clasamentului \u00een conferin\u021bele respective. Rockets este a 13-a echip\u0103 din vest cu un bilan\u021b de 9 victorii \u0219i 19 \u00eenfr\u00e2ngeri, \u00een vreme ce Knicks ocup\u0103 abia a 12-a pozi\u021bie \u00een est cu 12 victorii \u0219i 16 \u00eenfr\u00e2ngeri.\nKnicks au mare nevoie de victorie \u00een aceast\u0103 noapte, iar Houston Rockets \u00een contextul actual par a fi victimele perfecte. Gazdele au mari probleme de lot, iar printre absen\u021bi se num\u0103r\u0103 cei mai importan\u021bi doi oameni din lot: Christian Wood \u0219i Eric Gordon. Chiar dac\u0103 \u00een leg\u0103tur\u0103 cu niciunul nu \u0219tim 100% \u00een acest moment c\u0103 vor lipsi, Stephen Silas, principalul echipei, a dat de \u00een\u021beles la conferin\u021ba de pres\u0103 de azi-noapte c\u0103 nu-i va putea folosi pe cei doi nici ast\u0103zi.\nAm v\u0103zut c\u00e2t de mult impact a l\u0103sat absen\u021ba celor doi \u00een partida de azi-noapte, Houston nereu\u0219ind s\u0103 \u00eenscrie mai mult de 89 de puncte \u00een deplasarea de la Cleveland \u00eentr-unul dintre cele mai slabe meciuri ale sezonului pentru ei.\nTom Thibodeau, antrenorul oaspe\u021bilor, r\u0103m\u00e2ne adeptul unui stil mai lent de joc, unul pe care l-a impus \u00eenc\u0103 din perioada c\u00e2nd activa la Chicago Bulls, iar Rockets nu-\u0219i vor putea permite s\u0103 dicteze ritmul \u0219i s\u0103 ridice tempo-ul cu at\u00e2t de multe indisponibilit\u0103\u021bi pe plan ofensiv.\nPronosticul meu merge pe under 218.5 puncte pentru o cot\u0103 de 1.70 oferit\u0103 de casa de pariuri Superbet.","startDate":"2021-12-17 03:00","endDate":"2021-12-17 05:00","url":"https:\/\/pariurix.com\/ponturi\/baschet\/nba\/houston-rockets-new-york-knicks-287033.html"}]        </script>


Note how the location markup was replaced with the league name so we would get more relevant SERP enhancements, even though it is not strictly correct.




Specialized bookmaker articles for the event
      * If it is a large event, the search demand will likely be focused on keywords such as “bookmaker + event”
      * The articles should be interlinked with exact match and descriptive anchor texts so as to avoid cannibalization
      * This should all be internally linked with the hub page, other relevant articles (e.g. if there are enhanced odds for a Brazil match, link to that match, the stage/group page, and to the Brazil team page), and the evergreen bookmaker pages for that bookmaker; additionally, there should be internal links pointing the other way
      * Make sure to follow the search volume and the offers by the bookmakers and to create these articles in due time
      * Another option is to have the main bookmaker reviews updated with the special event offers, targeting the relevant keywords
      * After the event is over, there are several things that can be done with such pages:
         * If the main bookmaker article was updated, revert it to the pre-event version
         * If they are not cannibalizing on the main bookmaker reviews, they can stay indexed and linked to the event hub page and other event-related pages (stage/group pages, schedules, tips//match analysis/match preview articles etc.), but make sure to unlink them internally from other bookmaker-related articles. Note, however, that they are likely to generate zero traffic, impressions and clicks because the queries are no longer relevant. This option is preferred if you want to have old pages to redirect to new ones once the next such event occurs.
         * If the pages are cannibalizing other bookmaker content, it may be advisable to 301 redirect them to the main bookmaker page. Another option is to redirect them to the event hub page for increased topical authority or 410 them if they have no value. Note that this would require an in-depth check of internal linking so there wouldn’t be any internal links leading to 301 or 404 pages.
         * If by any chance the page has started ranking for some generic bookmaker terms and is doing well in the SERPs, consider de-optimizing it for the event-specific terms and optimizing it for the generic terms, while also adding it into the current bookmaker-related content structure by internal linking.


Second phase - keeping up the momentum and starting to work on the tips/match analysis/match preview
Updating all the previously mentioned pages with new info as they come
      * The hub page, as well as the match, group, stage and related pages need to get updated
      * Fuel this with Google Trends for the current period - for news content as well
         * Pay attention to Breakout terms, which are terms that recently skyrocketed in popularity - the sooner you cover them, the more exposure you will get
Starting to work on the tips/match analysis/match preview pages
Depending on the strategy regarding the tips pages, if you haven’t done this already, starting to work on the tips/match analysis/match preview pages is advisable at this time
      * If Google News is the source of traffic, then the articles can be drafted so they can be ready for publication at the time of the match
Continue and even intensify the production of news articles and internally linking them to the relevant evergreen/hub pages
Continue with the link building efforts towards key pages, now paying more attention to the specific match pages
      * Sports media outlets are always good sources of links for this type of content
Get ideas from the competitors content
      * Identify your competitors using one of the following methods:
         * Talk to the content managers and site owners to see who we are traditionally competing against
         * Use Ahrefs’ Site Explorer feature to search for your domain, then click on Competing Domains while paying attention to the country selected. The report will show you the domains that share the most keywords with your site and that are considered your main competitors
  

         * Similarly, you can do this on a page level, the only difference is you would be choosing Competing Pages instead of Domains
         * You can also take the list of the main keywords you are targeting and do several Google searches with a VPN for the target market
      * Do a crawl of the competitor via Screaming Frog to see what articles they have
         * If they have a separate section with a good URL architecture (e.g. everything is in a folder called /world-cup/, you can just crawl that section
         * Be sure to look at the way the articles are interconnected by using one of the visualization tools for the crawl
  

      * Do a Content Gap analysis via Ahrefs to see the keywords the competitors are ranking for that we aren’t and then manually vet the keywords related to your event and see the pages that are targeting these keywords
      * Another option is to place the URL path if the content hub is placed inside a single subfolder on the domain (example.com/wc-2022/page
  

Add keywords to track into GetStat
      * Based on what is trending and what you expect to be the main keywords for the matches you are covering, upload some keyword variations to Getstat
      * This will allow you to track their performance and have a potential insight into whether your keywords are triggering Top Stories, Featured Snippets, etc.
      * This will also unlock the power of Tableau and the KW model 2.0 that will show you what your most valuable keywords are and what should be the focus of your optimization during the World Cup
Third phase - the event itself
Every match has to be covered by a tip article/match analysis/match preview and they are linked from every article where it makes sense
      * If you already had tips/match analysis/match preview articles pre-made, then you need to update them with all the new information and make sure you target the relevant keywords
         * Update the odds if they are not automatic
         * Update the team form, stats and roster according to the newest information
         * If necessary, update the analysis and predictions if something changes so drastically that it changes the outcome of the match
         * Do this after every significant event for the outcome of the match, including other group members matches, player updates etc. This way, you are writing a developing story that adapts to the current circumstances, it’s not just a set-it-and-forget-it kind of thing.
         * As the match comes dangerously close, daily updates are recommended (even several a day)
         * Feel free to expand the articles instead of just updating them so Google can easily notice the story is updating
         * Have a published and updated date clearly stated below the author
      * If you are looking for Google News exposure, then you need to publish right before the day of the match/on the day of the match for maximum exposure; be sure to request indexing so the tip/match analysis/match preview would get indexed as fast as possible
      * The target keywords should be focused on odds and tips
      * Make sure you are following the indexation of your articles in GSC and respond ASAP if something fails to be indexed on its own
      * All other articles keep being updated as the situation changes - results and rankings of teams in group pages
      * The tips/match analyses/match previews for the teams should be linked from the home page, but also from team pages, group pages, stage pages, and stadium pages (the more context, the better)




Featured Snippet Hunting during the event


During the event, we have an opportunity for more exposure if we hunt for featured snippets for World-Cup-related keywords and regular bookmaker-related keywords as the CTR is the greatest for these
* One of the traditional ways to look for keywords that trigger featured snippets is to go to Ahrefs, search for your site in the site explorer, go to the Organic Keywords report, set the SERP features filter to Featured Snippet, tick the box next to Where target ranks and filter the positions to 2-10, or even more conservatively to 2-7
   * This means that we are looking for keywords that have featured snippets that we don’t own, but we are on the first page and have a chance to take them over from the competitors
   * Note, however, that this works better for some longer-lasting snippets and might not be updated as regularly due to Ahrefs’ database limitations
  

* Another way is to use logic and look for keywords that are sure to be searched in relation to the World Cup like “favorites”, “which TV station has the rights to stream the match”, “where can you watch the event live” and checking if any of these trigger a featured snippet
* There is a whole universe of temporary featured snippets that we can hunt for, mostly pertaining to queries related to teams or “team a vs team b”
   * The best inspiration for where to look at these are queries from GSC and the number of impressions they trigger
      * Just filter our queries where we’re ranking on average on the first page (positions 1-10, perhaps 1-8 to accommodate for other SERP features taking up space) and that have a decent amount of impressions (which would be a proxy for search volume, even though sitelinks are also present in impressions, we still need to have some benchmark number)
      * If you want to pinpoint the keywords you are checking for FS to the event at hand, you can put your folder path in the GSC page filter
  

      * Check every query manually to see if anything triggers a featured snippet
         * Note that brand new pages sometimes won’t have any queries as they were published quite recently which then causes a problem that waiting for GSC to register them would waste a lot of precious snippet-hunting time
         * A solution could be to rely on experiences from previous years or concentrate on some VIP keywords that we planned in SEO Content plan
      * Note that you don’t have to target only commercial keywords containing “team a vs team b”, “tips”, or “odds”, but also some less commercial keywords and then funnel this back to commercial pages
   * The queries for tomorrow's or today’s matches can’t be checked beforehand, so on the day of the match or a day before, you can just Google all keywords related to “team a”, “team b”, “team a vs team b odds”, “team a vs team b tips”, etc. and act quickly. There’s no need to be doing this before. 
   * Google Trends can also be used to get keyword ideas, so that can be also added as an option. Use the same procedure that was already described for Google Trends.
After you have identified the keywords that have featured snippets, use this guide to try to take over the featured snippets from competitors.


Fourth phase - after the event is over
   * Follow Google Trends to write about the topics that are trending after the event is over
   * Write about the next big event of such type
   * Write retrospective articles and opinion pieces on the competition, including statistics
   * Leave the hub page linked until the buzz around the competition dies down, then unlink it from the main navigation
      * Another option is to push it to the footer for a while so as to leave more space in the main menu so the crawlers wouldn’t crawl the old content as intensively as the new articles
      * Make sure not to orphan pages after unlinking them, so perhaps it would be a good idea to have an article about major events that would link to all the event hub pages
   * Continue with news production about the events and actors related to the tournament
      * If it is a football championship, a lot of the players are going to find new clubs, so transfer rumours are always a good topic
   * Evaluate the performance of the whole campaign and the individual URLs and cross-reference it with the previous iteration of the same event
      * Some points to consider are (template example):
         * The query
         * The ranking URL
         * The ranking position
         * Organic sessions
         * Impressions and clicks
         * CTPs and NDCs
         * SERP features claimed