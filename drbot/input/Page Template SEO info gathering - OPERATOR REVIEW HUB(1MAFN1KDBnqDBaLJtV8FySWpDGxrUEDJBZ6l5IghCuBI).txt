﻿[General / SOP] Page Template SEO info gathering - OPERATOR REVIEW HUB


* OPERATOR REVIEW HUB -recap


* Meta Title
   * max 60 characters
   * main keyword - bookmakers/ betting operators/ betting companies/ betting agencies
   * additional keywords that are bookmaker related.
   * date - should be updated accordingly 
   * market
* Meta Description
   * should be around 160 characters (min 50 & max 160 characters)
   * should be unique for each page
   * summarises the page content 
   * call to action keywords (Bet on!/ Bet in/ Join us!/ Learn how to win, etc.)
   * market 
* Content
   * Logo - the unique logo of the site
   * Navigation bar - containing all the most important pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussion/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. The navigation bar doesn’t have to contain all these pages. It depends from site to site. 
      * Search box (optional, not applicable for all sites, but still good to have)
      * Sign in/Login button (if the website has a membership program) 
   * <H1>
      * No character limitation but try to keep it visually appealing, so do not make it too long. Make h1 stand out. Make the letters bigger than the rest of the text and headings.
      * year
      * market
   * Short paragraph (teaser, short info about the website - 2 or 3 sentences with keyword) - good to have, not a necessity 
   * Table of all bookmakers - Display of the main offer/most important thing that users get from us. The table must be hardcoded in HTML and table elements must be server-side rendered. For the tables, we have to use <table> tags and not any other tags. 
Within the table: 
      * logo of each bookmaker, 
      * offer - CTA (bonus code, free spins or some other offer or benefit), 
      * bullet point - short description of the benefits, 
      * Review button, 
      * CTA - button Bet now, 
      * rating, 
      * 18+ | Play responsibly | Terms & Conditions apply links.
   * Other offers
   * Other explanations about all the benefits that we offer - paragraphs
   * FAQ
* Schema
   * Breadcrumbs -  best practice. It helps bots/ search engines better understand the hierarchy of the pages
   * FAQ
* Images






* OPERATOR REVIEW HUB


Operator review page/ Bookmaker hub page is a central page for all bookmakers (that we are working with) with a set of interlinked, related content that goes back to that page. This page can be a topic that you regularly cover on your website, but this page usually contains information that targets high-volume keywords and attracts many users. 


With these hub pages, we gain more topical authority, more link authority, etc.


We do not change this page that often, only to cover new incoming information about specific bookmakers that we should inform sour users. (Or in case of optimising it for search engines). 


SEO wise it is very important to give Google a big hint (through your title, description, and content), on what offers you provide and how customers can use it:


Title Tag (Meta Title):
Utilise all 60 characters you have at your disposal to blend keywords and business information in one sentence. The title tag should contain:
* Main keyword - bookmakers/ betting operators/ betting companies/ betting agencies
* additional keywords that are bookmaker related. Some frequent options are: 
   * top recommended online betting companies, 
   * bookmaker reviews,
   * bookmakers with Sign Up Offers,
   * best betting sites, 
   * best Sportsbook, 
   * betting predictions and tips, 
   * bonus codes, 
   * betting offers,
   * best bookmakers with licence,
   * gambling news, etc.
Note: We do not use all these keywords in the meta title of a site, but those are the most common ones. Choose the most convenient ones for your site and implement them in the title. 
* date - Just year or month and year - Betarades = Good practice
  



* market - Pariurix
  



* brand name/ site name - not necessary, just one of the options - Spilxperten[a]
  





Sites used as examples:
https://pariurix.com/agentii
https://www.betarades.gr/bonus/
https://www.bettingexpert.com/bookmakers
https://www.vegasinsider.com/sportsbooks/
https://www.spilxperten.com/bookmakere/


Meta Description:


Besides the Meta title, the Meta Description is what users will see in the SERPs, so you may want to give it a bit of thought.


Meta description length should be around 160 characters (min 50 & max 160 characters).
According to Google Guidelines, a meta description might be a sentence or two or even a short paragraph.


Elements to be included:
* The meta description should be unique for each page
* keywords (do not repeat keywords from Meta title). A good description accurately summarises the page content. Some frequent options for keywords are:  
   * online sports betting agencies,
   * online sports betting, 
   * sports betting information,
   * best online betting sites, 
   * online casinos, 
   * best online casino,
   * top casino offers,
   * legal online casino games,
   * betting predictions and tips,
   * winning tips, 
   * hottest tips,
   * live betting, 
   * bonus codes,
   * current bonuses,
   * welcome bonuses.
   * exclusive bonus,
   * free spins,
   * betting offers,
   * bookmaker reviews,
   * bookmaker guide, etc.
For Meta description besides mains and broader keywords, we also use long tail keywords (above). 
Also, call to action keywords:
   * Find out 
   * See the list of the best bookmakers
   * Bet on!
   * Bet in
   * Join us!
   * Read about the best bookmakers
   * Learn how to win
   * Learn how to sign up
   * Get your daily dose of analyses and picks!


Comment: Place a keyword in 2 or 3 catchy phrases - maybe even in question form. The key is to get the attention of the users. 


* If we are dedicated to one market display that - for example:
  



Comments: 
        Smaller sites have this kind of page as a Homepage. (Bettingsidor, Casino.ro , SDA, etc.)
        
Some sites use emojis in their Meta description. - not necessary. Before implementing the emojis, research the market. [b]


Have in mind that Google may choose not to show your description but use their own. This may happen if your description is too short or too long, or if it includes a lot of keywords.


Sites used as examples:
https://pariurix.com/agentii
https://www.betarades.gr/bonus/
https://www.bettingexpert.com/bookmakers
https://www.vegasinsider.com/sportsbooks/
https://www.spilxperten.com/bookmakere/
https://cazino.ro/
Heading structure:
Headings are visible on the page, not on the SERPs.
Headings help structure a web page's content by splitting information into different sections. They help sighted users scan the page and find information quickly.


Best practice for heading structure: 


* Only use one <h1> for each web page.
* Don’t skip heading levels. For example, using a <h2> followed by an <h4>. It’s important that your headings are logical and hierarchical. This means that <h2> always follows <h1>.
* Make sure each heading is unique on individual web pages.
* Headings should describe the content that follows them. It is a good practice to have headings in form of questions. The first <p> below will then answer that question. This tactic is good because it can lead to gaining  Feature snippets and is quite a user-friendly way of writing. 
* Make headings clear, useful and relevant.


<H1> defines the most important heading.
H1 tags should match the user intent.
It would be very similar to the Meta title because they have the assignment to say the same thing - what is the page about, but do not have to be exactly the same as the Meta title.[c]


Elements to be included and other instructions:[d]
* No character limitation but try to keep it visually appealing, so do not make it too long. Usually shorter than the Meta title. Needs to be the most prominent element above the fold.
   * Make h1 stand out. Make the letters more significant than the rest of the text and headings.
   * The recommendation is to avoid just having the primary keywords as the <h1>. 


* Main keyword - bookmakers/ betting operators/ betting companies/ betting agencies


* additional keywords that explain what the website is all about. Some frequent options are: 
   * online sports betting, 
   * legal and recommended online betting houses, 
   * online casinos, 
   * betting predictions and tips, odds, 
   * live betting, 
   * bonus codes, 
   * betting offers,
   * bookmaker reviews, etc.
Note: We do not use all these keywords in the meta title of a site, but those are the most common ones. Choose the most convenient ones for your site and implement them in the title. 


* market
  



* year - not exclusively obligatory but not bad practice
  



Sites used as examples:
https://bettingsidor.se/
https://www.bettingexpert.com/bookmakers
https://www.vegasinsider.com/sportsbooks/
https://www.spilxperten.com/bookmakere/




Content:
* DESKTOP
Above the Fold - should contain:
* Logo - the unique logo of the site
* Navigation bar - containing all the most important pages of the site: 
   * Navigation bar - containing all the most important pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussions/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. Navigation bar doesn’t have to contain all these pages. (Underlined are the most important ones for our business). 
For an affiliate site, it is important to put all commercial hub pages into the navigation bar because those pages are the most important for the business. 
Which links should be in the navigation bar depends from site to site. For example, BettingExpert has specialised in betting tips, but some other site does not even have tips at all.
The pages that are most common for the majority of affiliate sites are: 
Bookmakers hub and the good practice is to have a drop-down menu with the most important bookmakers
Bonus hub - the most common offer that affiliate sites are giving to the users is bonus codes (main offer that site has)
Betting tips/ odds (other offers that site has)
Casino hub - if a site has a casino section at all. Some of the sites don’t.
All these pages above are commercial because we are offering something to our users and the main goal is to get people on those pages where they can use our services.
Other important pages are informative pages where we show our expertise in the topic (in this case sports or casino), so we have also these pages n the Nav bar: 
News hub
Events page
Analysis
Live streaming
Guides
   * Search box (optional, not applicable for all sites, but still good to have)
   * Sign in/Login button (if the website has a membership program) 
* H1 (explained above)
* Short paragraph (teaser, short info about the website - 2 or 3 sentences with keyword) - good to have, not a necessity 
* Disclaimer about Safe Gambling (18+ | Play responsibly), Terms & Conditions apply -optional because it is market-dependent 


* Table of all bookmakers - Display of the main offer/most important thing that users get from us. All tables should be hardcoded in the HTML so that Google can read them. For tables use only <table> tags and not any other tags. This also applies to links - we have to implement them in <a href=...> tags and not in JS. Within the table: 
   * logo of each bookmaker, 
   * offer - CTA (bonus code, free spins or some other offer or benefit), 
   * bullet point - short description of the benefits, 
   * Review button, 
   * CTA - button Bet now, 
   * rating, 
   * 18+ | Play responsibly | Terms & Conditions apply links.
  



Comment: It is a good practice to put those bookmakers on the numbered list (not necessary). 




Below the Fold - (both desktop and mobile) should contain:
After the main offer of the site, the page should contain all the important offers that the bookmakers have.
Any widget/block/image below the fold should be lazyloaded.


These are just some of the possibilities that we could implement below the fold: 


* How to pick/ how to choose the best bookmaker
* Welcome offers
* Bookmakers with licence
* Mobile app
* Sign-ups offers
* Customer service
* Withdrawals
* Payment methods
* Live streaming
* Live betting
* Odds
* Other offers: free bets, free spins, no deposit bonuses, promotions, 
* Reward program
* Short review of each bookmaker
* Bookmaker website interface
* Live video broadcasts
* Mobile betting
* How safe is it?
* How to register
* Responsible gambling
* FAQ 


* MOBILE
Above the Fold - should contain (same as for the desktop):
* Logo
* Navigation bar
* H1 
* Short paragraph 
* Disclaimer about Safe Gambling (18+ | Play responsibly)
* Table of all bookmakers with CTAs [e]


Schema:
* Breadcrumbs - (Bettingsidor) not necessary.
Example of the code: 


<script type="application/ld+json">
{
        "@context": "http://schema.org/",
        "@type": "BreadcrumbList",
        "itemListElement": [
                {
                        "@type": "ListItem",
                        "position": 1,
                        "name": "Home",
                        "item": "https://bettingsidor.se/"
                }
        ]
}
</script>


* FAQ - 


Example of the code: 


<script type="application/ld+json">
{
        "@context": "http://schema.org/",
        "@type": "FAQPage",
        "mainEntity": [
                {
                        "@type": "Question",
                        "name": "Where are the best online casinos in Romania?",
                        "acceptedAnswer": {
                                "@type": "Answer",
                                "text": "The best online casinos are those that have a valid ONJN licence and comply with Romanian legislation. Read reviews, check bonuses and games available at the online casino where you want to play and thus you will find out which is the best online casino for you."
                        }
                },
                {
                        "@type": "Question",
                        "name": "Is online gambling safe?",
                        "acceptedAnswer": {
                                "@type": "Answer",
                                "text": "Yes, as long as you play at legal casinos authorised by ONJN. Play at an online casino that is recommended and verified and before any deposit check the bonus and withdrawal conditions. Pay attention to the customer support service as well as to the ratings received from users for the casino you are going to play at."
                        }
                },
                {
                        "@type": "Question",
                        "name": "Which online casino pays out the fastest?",
                        "acceptedAnswer": {
                                "@type": "Answer",
                                "text": "All online casinos pay out very quickly. There are casinos that pay winnings on the same day. Withdrawals are usually processed within two to three business days. There are online casinos that are much faster and process and transfer payments in just minutes."
                        }
                }
        ]
}
</script>


Comment: If we decide to have FAQs the recommendation is to have at least 3 questions with answers because Google shows two or three questions on the SERP. Before implementing, conduct research on the competition to see what they are covering. 


Do not have to implement all these schemas - it depends from site to site. 
Images:
* Ensure the ALT text is optimised
* Use only high-resolution images. A recommendation is to use images of the people. 
* Optimise Your logo
* Compress images to load fast
* Every image below the fold should be lazyloaded
* SOPs for image optimisation:
Optimizing images for best SEO performanceOptimizing images for best SEO performance - part 2


General recommendations:
* Optimise homepage loading speed
* Check Mobile Friendliness - responsive design
* Make a good website structure = Make sure that all bookmakers’ review pages are linked from the Operator review hub page. Also, if there are more topically related bookmaker overview pages, try to interlink them in a hierarchically logical way (pillar and spoke content).
* Any widget/block/image below the fold should be lazyloaded.
* Add elements to establish trust with users (testimonials, reviews, etc.)




* HOMEPAGE
* OPERATOR REVIEW PAGE
* OPERATOR BONUS HUB
* OPERATOR BONUS PAGE
[a]Very frequently, Google will do this automatically by rewriting the meta title. Nothing to do here, just a note for the future (we have this case on BettingExpert).
[b]Just a brief explanation of why we might want to do this - because it allegedly attracts more attention and might increase CTR ;)
[c]I agree with you regarding H1's , that they should not deviate too much from what we put in meta title in sense of most important terms for the particular page. So for example if we are using "Best bookmaker reviews" in our meta title we should also consider putting those terms in our H1 in some way. What I would recommend is that H1 should be appealing to users and it should grab their attention so that they would want to stay on the page. 
I see that Natalie already commented on this with a good example on how it can be done so I have nothing else to add to this :)
[d]I would also add that it's be good to avoid just having the primary keyword as the h1. This is not a confirmed thing or anything, but in my experience I have found that H1's with a bit more of an editorial heading perform better. 


So for example, instead of having just 'Best Bookmakers' or 'Bookmakers' you would have '15 Best Bookmakers in the UK' or whatever :)
[e]The whole thing probably won't fit, but we should aim at having at least the top of the table and the first CTA in the first fold.