﻿[General / SOP] Page Template SEO info gathering - OPERATOR BONUS PAGE


* OPERATOR BONUS PAGE


* Meta Title
   * max 60 characters
   * main keyword - online bookmaker bonus (one of the most common options, here just as an example)
   * additional keywords that are bookmaker related: review, bonus, free bet. Besides keywords, we can display a current offer - current bonus/ bonus code, etc. We must regularly update if we display an offer in MT or MD. 
   * date -  current month and year ⇒ update it regularly
* Meta Description
   * should be around 160 characters (min 50 & max 160 characters)
   * should be unique for each page and summarises the page content ⇒ display a current offer - current bonus/ bonus code, etc. We must regularly update if we display an offer in MT or MD. 
   * main keyword - bonus
   * call to action keywords (Get your Welcome Bonus!/ Earn in 2022!/ Use the welcome bonus right now!/ Bet on!/ Bet in/ Join us!/ See the best promotions and bonuses!/ Learn how to win, etc.) - very important to have for this type of page
   * date - month and year (update it regularly). This is a good practice, but we do not have to put the date in the MT and the MD we can choose one spot only  
   * market 
* Content
      * ABOVE THE FOLD
   * Logo - the unique logo of the site
   * Navigation bar - containing all the most essential pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussion/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. The navigation bar doesn’t have to contain all these pages. It depends from site to site. 
   * Breadcrumbs -   best practice. It helps bots/ search engines better understand the hierarchy of the pages
   * <H1>
      * No character limitations but try to keep it visually appealing, so make it a manageable length. Make h1 stand out. Make the letters more significant than the rest of the text and headings.
      * main keyword: name of the bookmaker  + bonus/ bonus code (or some similar keyword that is bonus related)
      * date - year and month ⇒ good practice
      * market ⇒ good practice but optional
      * additional keywords - optional
   * Intro text - Short paragraph (teaser, short info about the bookmaker - 2 or 3 sentences with keyword) - good to have
   * Display of the bonus - the main offer/most important thing that users get from us. Within the table: 
         * logo of the bookmaker, 
         * offer - CTA (bonus code or “Get the bonus!” button), 
         * bullet point - short description of the bonus benefits, 
         * Payment options - can be emojis/ pictures/or explained in words(bullet points)
         * CTA - button Claim bonus/Collect bonus/Copy bonus
         * 18+ | Play responsibly | Terms & Conditions apply links.
      * BELOW THE FOLD
   * Instructions - how to bet/ how to get the bonus/ where to copy the bonus code/ how to use bonus code = step-by-step guides that we put in lists (<ul> or <ol>) = good to have 
   * Explanations/ Detailed description of what the bookmaker does <p> = good to have but optional (depends from bookmaker to bookmaker, depends on the market, etc.)
      * Ongoing promotions
      * All advantages of using the promotions/ bonuses
      * Where users can use the bonus code, etc.
   * Other offers: WC bonus/ Free spins/ Casino bonus/ Welcome bonus/ Promo code  ⇒ if the bookmaker has them (with the explanations)
   * FAQ ⇒ headers (h2, h3) as questions and content (<p>) as an answer - recommendation not an obligation
* Schema - not all schemas are required 
   * Breadcrumbs 
   * FAQ - best practice
   * Review snippet
   * Sitelink searchbox - optional
   * HowTo schema - good practice. But if we add this schema on the page we do not have to implement FAQ schema also. We shouldn't have both, as then neither may get picked up.
* Images
   * Ensure the ALT text is optimised
   * Use only high-resolution images. 
   * Compress images to load fast
   * Optimise Your logo
   * Every image below the fold should be lazyloaded
   * SOPs for image optimisation:
Optimizing images for best SEO performanceOptimizing images for best SEO performance - part 2


General recommendations:
* Table of content with anchor links - optional - good to have if the page is too long. It is a good practice to make it foldable to save some space. This feature also helps to gain sitelinks in the SERP which automatically gives us more space and more exposure.
* Features like Bonus Calculator - good practice, but optional. Only a few sites have this. (SDA has it).
* Any widget/block/image below the fold should be lazyloaded.
* Research the market and competitors 


Navigation bar - containing all the most important pages of the site: 
Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussions/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. 
Navigation bar doesn’t have to contain all these pages. (Underlined are the most important ones for our business). 
For an affiliate site, it is important to put all commercial hub pages into the navigation bar because those pages are the most important for the business. 
Which links should be in the navigation bar depends from site to site. For example, BettingExpert has specialised in betting tips, but some other site does not even have tips at all.
The pages that are most common for the majority of affiliate sites are: 
Bookmakers hub and the good practice is to have a drop-down menu with the most important bookmakers
Bonus hub - the most common offer that affiliate sites are giving to the users is bonus codes (main offer that site has)
Betting tips/ odds (other offers that site has)
Casino hub - if a site has a casino section at all. Some of the sites don’t.
All these pages above are commercial because we are offering something to our users and the main goal is to get people on those pages where they can use our services.
Other important pages are informative pages where we show our expertise in the topic (in this case sports or casino), so we have also these pages n the Nav bar: 
News hub
Events page
Analysis
Live streaming
Guides


Sites used as examples:
https://pariurix.com/agentii/recenzii/superbet/promotii
https://www.betarades.gr/bet365-efes-real-madritis/
https://www.vegasinsider.com/sportsbooks/betmgm/
https://bettingsidor.se/ny-betting-bonus-sa-fungerar-snabbare-revanschen/
https://mybettingsites.co.uk/promo-codes/bet365/
https://bonuscodebets.co.uk/bet365/#gref










* HOMEPAGE
* OPERATOR REVIEW HUB
* OPERATOR REVIEW PAGE
* OPERATOR BONUS HUB