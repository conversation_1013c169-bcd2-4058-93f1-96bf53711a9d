﻿[SOP] Internal Linking 


This SOP is designed to help anyone whose aim is to either audit the internal linking structure of a site/category and/or identify internal linking opportunities.


It is mainly focused on contextual (in-content) links, but keep in mind that there are other strategies for improving internal linking such as the navigation, footer, sidebar or props (“related articles”) which can be useful at times.


Therefore, this document will guide you through the process of:


1. Conducting an internal linking audit 
   1. Crawling with Screaming Frog - the entire site vs. subfolder/subdomain
   2. Exporting data
   3. Analyzing data 
   4. Identifying internal 301s/redirection chains/redirection loops
   5. Identifying broken (404) internal links
   6. Identifying orphan pages
   7. Reviewing JS internal links
   8. Checking Nofollow Links
 
2. Finding internal linking opportunities
   1. Screaming Frog Custom Search + Google Sheets
   2. Screaming Frog Custom Extraction + Google Sheets
   3. Ahrefs Site Audit 
   4. WP Plugin
   5. Manual Search
   6. Finding the right anchor text


Main tool: Screaming Frog


For questions/assistance while using the SOP feel free to contact <PERSON><PERSON>


Internal Linking Audit 


An internal linking audit involves a systematic evaluation of the internal linking structure within a website. 


Internal Linking Audit should contain an overall assessment of the internal linking structure of a website plus the identification of any internal links pointing to pages which do not return status code 200, and internal links that are involved in redirection chains or redirection loops. 


Before you actually start exporting and analyzing data, take a moment to go through the site/section and get a sense of what the internal structure looks like. 
Auditing the Internal Linking Structure
Step 1: Crawling the Site with Screaming Frog 


1. Open Screaming Frog. 
2. Load the following configuration in SF ( File > Configuration > Load ) → https://drive.google.com/file/d/1-BrNny4i7coZ3_4iRgOzyQQmacRqn6b_/view?usp=drive_link 
3. Input the domain URL in the main tab. Adjust the configuration if you’re crawling only a subfolder or a subdomain.
4. Connect APIs (Configuration > API Access ): connect GA4 and Google Search Console. Additional configuration settings for these APIs should be set as well, you can find them under “How to find Orphan Pages” heading. Recommended date range - 12 months, if the site is large, you can opt for 6 months or less.
5. Optionally, connect Ahrefs or Majestic API to see backlinks and referring domains data (check how many credits are left before you connect Ahrefs/Majestic API). [a][b][c]


Note: Incorporating backlinks and referring domains into this analysis is completely optional, but I do recommend it since this will show you which pages are your “popular” pages that can help pass PageRank to your other pages. If you identify pages that have a lot of backlinks, think about whether you can use those pages to boost any other particular page by sending some link juice over to it. 
Additional Step 1: Crawling a Subfolder or a Subdomain with Screaming Frog


1. Crawling a subfolder/category


Input the appropriate subfolder URL in the main tab. Take the following into account: 
* It needs to have a trailing slash at the end so that it can be recognized as a subfolder by SF. In case a subfolder doesn’t have a trailing slash at the end, go to Configuration > Include and input the subfolder with a dot and a star at each end, for example: 
.*/example.*
* If you want to focus only on the specific subfolder (so that SF doesn’t start at the given URL and then continues to check the response of the URLs outside of that folder), go to Configuration > Spider > Check Links Outside of Start Folder and uncheck it. However, the aforementioned setting is useful if you want to find broken links within the folder you’re focusing on, but which link outside of that URL structure. 


2. Crawling a subdomain 


If you wish to audit the internal links only on a particular subdomain, make sure to keep in mind that: 
1. You input the correct URL in the main tab. For example, if the subdomain is www.example.com, make sure not to forget to actually include www. You will get all www URLs in the “Internal" tab, while all other subdomains will be listed in the “External” tab.  


Note: if you wish to see all subdomains, just crawl from the root: https://example.com, and in this case, all subdomains will be listed under “Internal”. 
Another way to do this would be to keep the URL as https://www.example.com but then tick the option in Configuration > Spider > Crawl All Subdomains 
Step 2: Export data from SF 


1. Go to the Internal tab and filter only for HTML. Also, filter out anything that you might want to exclude from the analysis such as goto links (/besog/ stands for goto in Danish below). You could also include only URLs with status code 200 if you’re looking at 301s and 404s separately (which is recommended).
  
  



2. Click on “Export”. 
3. Go to Google Sheets and import the .csv file or just import it directly through SF:
  

4. Delete unnecessary columns and keep only: 
   1. Status Code
   2. Crawl Depth 
   3. Inlinks
   4. Unique Inlinks
   5. Outlinks
   6. Referring Domains (optional)
   7. Backlinks (optional) 
   8. Clicks
   9. Organic Sessions 


5. Then sort by the number of Unique Inlinks ( A → Z ) so that those with fewer inlinks come first - these will probably be 301s and 404s, and then 200s, if you haven’t filtered for 200 ones only before exporting from SF.
Step 3: Analyze the data and draw conclusions 
A few recommendations to follow when analyzing data: 
1. Look at crawl depth: is there any page buried deep in the site structure? Is that page an important one/money page? Are there any orphan pages?[d][e] You can find the instructions on how to find these pages in the How to Find Orphan Pages heading. 
2. Look at unique inlinks: should any particular pages have more links pointing to them to boost their performance?[f][g] For example, if you have a bookmaker review which you want to boost and build a topical cluster around (with articles such as app review, bonus review, how to register and make a deposit, etc.), look at the number of unique inlinks the review is getting and where they’re coming from. If you’re not linking from supporting articles back to the main review, you should implement internal links. 
3. Look at the number of backlinks: is there a page that has a substantial number of backlinks but is not receiving enough inlinks? Or it has lots of inlinks and backlinks, but is sending fewer outlinks (so you might consider linking more to other pages from it to distribute link equity). 
4. Look at clicks and sessions: are there pages receiving lots of clicks and having lots of sessions? This indicates that a certain page is popular, and that you need to make sure it is linked to effectively and directly from the homepage. If there are fewer clicks, you might need to improve internal linking for that page (especially if it is evergreen). 
5. If you’re looking at URLs from a particular category (e.g. bookmaker reviews): are all pages interconnected with the main review in a sensical way? You might want to open and review URLs to get a sense of a category’s structure. 


After you finish with the analysis, you should have an idea around the next steps you will take in improving internal linking for your site/section.
How to Find Internal 301s and Redirection Chains and Loops (Screaming Frog) 


There are various ways you can export this data from SF, but to get the most comprehensive report, I recommend exporting from Reports > Redirects > All Redirects which will give you an overview of all start and final URLs and will mark if the URL is in a redirect chain or hoop (and will map these out as well). 


1. Crawl the site using Screaming Frog. 
2. Go to Reports > Redirects > All Redirects
  

3. Import in Google Sheets and review. Example of an export. 




How to Find Broken (404) Internal Links (Screaming Frog) 


1. Crawl the website. 
2. After the crawl is finished, go to Bulk Export > Response Codes > Client Error (4xx) Inlinks 
  



3. Import into g-sheets and review. Keep in mind that the ‘source’ URL is the very last page to link to the 404 error page. So there might be times that the ‘source’ is a redirect (and possibly in a chain of redirects). You can see if the ‘source’ is a redirect as the ‘Type’ column will say ‘HTTP Redirect’ for example.

To quickly find the original source page of these errors, use ‘All Redirects‘ export under ‘Reports > Redirects > All Redirects’. Open up the report and filter the ‘final status code’ column to ‘404’. The ‘Source’ is the original source page, the ‘address’ is the last source, and the ‘final address’ is the 404 URL.


How to Find Orphan Pages (Screaming Frog) 
https://www.conductor.com/academy/what-are-orphan-pages-how-to-find-fix/ 


   1. Open Screaming Frog and:
   1. Go to Configuration > Spider > Crawl and tick “Crawl Linked XML Sitemaps” or simply supply the sitemap. 
   2. Connect GA in Configuration > API Access. Make sure that the Segment is configured to “Organic Traffic” and then go to General tab to tick “Crawl New URLs Discovered in Google Analytics”  
   3. Connect GSC in the same way. In Search Analytics tab, tick “Crawl New URLs Discovered in Google Search Console” 
   2. Insert the URL in the box and hit Start. 
   3. There are three respective ‘Orphan URLs’ filters under ‘Sitemaps’, ‘Analytics’ and ‘Search Console’ tabs which can only be viewed at the end of a crawl. They require post ‘Crawl Analysis‘ for them to be populated with data, so just go to Crawl Analysis > Start. 
   4. You can see Orphan URLs in SF by going to either Sitemap/Search Console/Analytics tabs and filter for Orphan Pages. 
  



   5. To have an overview of all orphan pages (from sitemaps, GSC, GA) in sheets, go to Reports > Orphan Pages. In the sheet, there will be a ‘Source’ column next to each orphan URL, which provides the source of discovery. 


Reviewing JavaScript internal links 
Some pages may contain hyperlinks that are only discovered in the rendered HTML after JavaScript execution, i.e. they are not in the raw HTML. 
While Google is able to render pages and see client-side only links, we should be including important links server side in the raw HTML. 


To find pages containing such links and review them:


   1. Open Screaming Frog and load the configuration, but double-check if the JS rendering is enabled in Configuration > Spider > Rendering.
   2. Input the desired URL in the main box and hit Start.
   3. Go to the JavaScript tab and filter the list for “Contains JavaScript links”. It is possible to export these pages via Bulk Export > JavaScript > Contains JavaScript Links.
   4. Review by clicking on Unique Outlinks and reviewing the outlinks. You will recognize a JS link when in the “Link Origin” column you see “Rendered HTML”. 
  





Checking Nofollow Links


Internal links should not have a rel=“nofollow” attribute. To be on the safe side, we should check this either by simply eyeballing the report in SF or exporting it and reviewing it. 


   1. Crawl the site using SF. 
   2. Go to Directives tab and filter for “Nofollow” 
  

   3. Review if any internal link has a nofollow attribute. 
Finding Internal Linking Opportunities 
Recommendations: 


   * It would make more sense to do the internal linking opportunities analysis one directory at a time than for the entire site at once, especially for larger sites. 
   * Guidelines for choosing the most appropriate method: 
   1. If the section you’re identifying opportunities for has a considerable number of pages, go for Screaming Frog + Custom Extraction. 
   2. If the section is smaller, you can opt for both SF + Custom Extraction or SF + Custom Search.  
   3. If you’re looking to find very specific opportunities (when you’re analyzing one page, for example), you can use any method, but I recommend Custom Search or Ahrefs Site Audit (especially if the newest audit is available since we have scheduled crawls in Ahrefs). You can make use of manual search if you’re trying to identify some opportunities quickly. 
   4. Ahrefs Site Audit may serve as a general overview to provide some general recommendations as well (especially if the newest audit is available since we have scheduled crawls in Ahrefs)
   5. Use a plugin if it is available in WordPress (only soccernews.nl for now) 


After you conducted the internal linking audit and figured out which pages need improvement, it is time to find the internal linking opportunities.


This can be done in different ways: 


   1. Screaming Frog Custom Search + Google Sheets
   2. Screaming Frog Custom Extraction + Google Sheets
   3. Ahrefs Site Audit 
   4. Manual search 
   5. WP Plugin (if available) 
Screaming Frog Custom Search + Google Sheets


What you’ll need for this method: 


   1. Screaming Frog 
   2. Google Sheets 
   3. A list of keywords you’ll use to identify opportunities 


Step 1: Configuring Custom Search 


   1. Open SF and load the configuration. Adjust the configuration if you’re searching for internal links only within a particular section of the site (by unticking “Check Links Outside of Start Folder” in the Configuration > Spider and using “Include” if needed). 
   2. Go to Configuration > Custom > Search which will prompt the window below. Insert the search query either as only one query or you can combine and search for a number of queries using regex (or just simply click on +Add button).  Make sure to choose “Page Text No Anchors” from the drop down menu on the right as this will allow you to identify instances of the queries which are not already serving as anchors and are, therefore, potential internal linking opportunities. 
  



   3. Insert the start URL in the main box.
   4. Start the crawl. 
   5. After the crawl is complete, go into the Custom Search tab and export the list by clicking on “Export”. 
   6. Import the .csv file in Google Sheets. There will be a column showing the number of occurrences of a particular query on a particular page. 
  



Going through the data Screaming Frog extracted will be a manual job as you have to open each and every URL and see if the occurrence is a suitable internal linking opportunity, so I recommend this for smaller sites, sections or fewer queries you intend to use in the Custom Search option. 


Screaming Frog Custom Extraction + Google Sheets


What you’ll need for this method: 


   1. Screaming Frog 
   2. Google Sheets 
   3. A list of queries you’ll use to identify opportunities 


This method is useful when optimizing sections of a website with a larger number of pages. I don’t recommend using this (or any other method that doesn’t involve a plugin) to do internal linking optimization for the entire website at once as it would be too manual and it would take away too much time. 


The method is based on Craig Campbell’s approach that uses Custom Extraction from SF. 


Step by step guide: 


   1. Compile a list of all URLs within the section you want to optimize. 
   2. Find the CSS selector using DevTools. 
   1. Go to any page, right click and then Inspect. 
   2. Once the DevTools tab opens on the right, click on the arrow in the top left corner.
   3. Hover over the margins of the main content until you see the content highlighted on your page and click once. It is important for the class ID not to include the site's navigation, that is, to include only the main body. After that, right click on the part of the code that is highlighted on the left and choose Copy > Copy selector.
  

  



   4. Open SF and load the configuration. 
   5. Go to Configuration > Custom > Extraction. 
Choose CSSPath, paste the selector you copied and choose Extract text, hit OK: 
  



   6. Add the desired domain or category you want to crawl. To prevent SF from crawling outside of the desired category go to Configuration > Spider an untick Check Links Outside of Start Folder. 
   7. Start the crawl. 
   8. Once it is finished, go to the Custom Extraction tab and click on “Export”. 
   9. Import the .csv file to Google Sheets (or import directly from SF). The columns will seem “stretched out” due to the large amount of text that is extracted for each column. It will look something like this: 
  



   10. Hide the column where the extracted text is (Extractor 1 above) by marking the column, right clicking and choosing “Hide column”. 
   11. Input the queries you compiled and which you want to find occurrences of in the extracted text. Queries are input in the same row, but every query takes one column (see the first screenshot below).
   12. Click on the row right under the first query (column E) and apply the following formula and drag it across other columns: 


=ISNUMBER(SEARCH(find_text, within_text))


This tells G-Sheets to look for your keyword (find_text) in your extracted content (within_text). Example for the sheet in the below screenshot: 


=ISNUMBER(SEARCH(E$1,$D2)) 


“$” sign indicates that row number 1 is locked, while column letter E isn’t. This enables us to drag the formula across other rows. 


   13. Finally, this is what we should get (FALSE = query not found in the extracted text; TRUE = query found in the extracted text)
  



   14. Filter for TRUE columns and go through the results to find the opportunities. You can create a new sheet that would contain the columns such as: 
   * From (Referring page) 
   * To (Target page) 
   * Anchor
   * Placement of the link (this is where you indicate in which heading/sentence the link should be placed) 
  



   15. As a bonus step, you can cross reference backlink and referring domains data (can be exported directly from Ahrefs or SF + Ahrefs API) to find your “most popular” pages. This way, you are making sure to distribute link equity to your “weaker” pages.




Using WP Plugin - LinkWhisper


LinkWhisper is an internal linking automation WordPress plugin that uses NLP technology to scan your site’s content and offer link suggestions. It is especially useful for content-heavy, large websites. 
It is not a free plugin, there needs to be a licence in order to use it. Examples & more info can be found in the presentation by Lazar: Testing LinkWhisper PRO plugin


Currently, it has been purchased only for https://www.soccernews.nl/ and can be accessed only via requesting access. It is still questionable how the plugin would work on production as PHP_memory on the server has to be set to a very high number in order for the plugin to be able to make its own database and give suggestions, which is not a best practice. 


LinkWhisper functionalities


Depending on what you need, you can use the following: 


   1. Suggestions in WP Editor
   1. If you want to add links from the article you’re currently writing/editing, scroll below WP Editor to “Link Whisper Suggested Links”. LW will already have suggested a number of related articles. “Sentences” on the left side of the table are the sentences from the article you’re writing where anchors are marked, and on the right side “Posts” is where related posts to which you can link appear. 
  



   2. Just tick an instance(s) you want and click on “Update links”. 


Note: Tests on soccernews.nl indicate that automatic suggestions are not satisfactory. It seems like it makes suggestions wherever it finds a match between a combination of words within the content of the selected article and a combination of words within the title of any other post/page in the database. 




   2. Add Inbound Internal Links (without entering WP Editor) 
   1. Open this option from the sidebar. 
   2. The option gives a sentence and a post where you can create a link. You can edit the content yourself and select words from the content to be part of anchor text. 
  



   3. Tick the ones you like and scroll to the bottom where you can click on “Add links”. 


3. Auto Linking
This feature allows you to make a list of rules in the form of Keyword-Page pairs. The plugin then automatically adds links in accordance with the set rules. 
   1. Go to the auto linking feature. 
   2. Insert the keyword which you’d like to have as an anchor. 
   3. Insert the URL to which the anchor pertains and towards which links will be built. From the gear icon, choose “Only link once per post” (so that the link is added only one time per article). Moreover, you can tick “Select links before inserting?” which allows revision and selection of links before they are added. 
  

  



   4. Optionally, if you’d like to automate adding keywords, you can choose “Bulk Create Autolink Rules” and upload a .csv file with Keyword-URL pairs. 
  



   5. After the plugin has created a report, a CM can go through it. 




Ahrefs Site Audit 
   1. Log into Ahrefs and go to Site Audit.
   2. A new project can be set, but we have pretty much all sites we work on already set. These Audits are usually scheduled for each site and the appropriate settings are already adjusted.
   3. Click on “Completed” for the desired domain to jump straight to the Audit Report. 
   4. Click on “Internal Link Opportunities” in the sidebar. 
   5. Review the report. 


Personally, I rarely use this feature when searching for internal link opportunities, but it is a good additional point to check especially because you can use filtering options in the report. For example, if you are looking for internal link opportunities for a single page, you can set the filter to “Target page” and paste the URL in the field. This will show you all opportunities for that particular page that Ahrefs identified:


  



Additionally, there are Advanced filters which allow you to add filters in relation to keywords, traffic, indexation, etc and apply multiple filters at once. So, if you wish your anchor to contain a specific keyword and you want a specific target page while having a source that has a canonical (self-referencing), add the filters: 


  



Manual Search


This is a manual process and as such can be time consuming. Keep in mind that this is best used when the site is smaller and you’re looking to find links for a specific page. 
   1. Go to Google Search. 
   2. Use search operators: site:domain + intext:desired keyword
  



   3. This will return the pages where the given query appears in text. The site above is not the largest, but it still has a considerable number of pages, so manually going through 54 results would be tedious. So, as already mentioned, this is best for smaller sites. 
Choosing Appropriate Anchor Text


According to Google, your anchor text should be descriptive and succinct. It should provide context for the user and help them understand where they are being taken when they click on the link.
https://developers.google.com/search/docs/crawling-indexing/links-crawlable#write-good-anchor-text 
How to find variations of a keyword to use as anchor text? 


Apart from a bit of creativity, I recommend using tools such as Ahrefs and Mangools to find variations of keywords. To automate this process, you can use ChatGPT. 


   1. Ahrefs 


   1. Enter your anchor text keyword in the Keywords Explorer tool, choose the country.
   2. Explore the "Terms match" report to find variations of the keyword.
  

   3. Look for related keywords, search suggestions, or alternative phrasings that can be used as anchor text variations.
   4. Take the search volume into account to identify the most relevant and viable options.
   5. Bonus point: if you’re searching for variations to use for particular pages, you can also go to Ahrefs Site Explorer, input the desired URL and go to Organic Keywords. 
Filter by Position so that you exclude position 1, it is best to include positions from 2 to 5, for example so that you can find variations for which there’s a decent volume, but we’re ranking below the first position. Using these variations may help strengthen the positioning on the SERP. 
  



b. Mangools 


      1. Enter your anchor text keyword in the KWFinder tool, choose the country.
      2. Review the generated list of related keywords and look for variations while taking the search volume into account.
  





Note: Both tools will provide valuable insights and will help you find the most suitable variations. Ahrefs is known for its large and comprehensive keyword database, but Mangools has proven to be equally useful. 


C. Chat GPT


Using ChatGPT 3.5 


      1. Log into ChatGPT 
      2. Prepare a list of already existing anchors. 
      3. Prompt ChatGPT to give you variations: “Provide up to 3 variations per given keyword from the list below. Create a table format. (Paste the keywords)
      4. What you’ll get should look something like this: 
  



      5. Review and choose the most suitable ones.


Using GPT for Sheets extension 


      1. Install GPT for Sheets from Extensions > Add-ons > Get add-ons and search for GPT for Sheets and Docs. 
      2. You’ll need to create your own API key, but you will be asked to provide your credit card number. Instructions here: https://www.makeuseof.com/how-use-chatgpt-google-sheets/#:~:text=How%20to%20Add%20the%20GPT%20for%20Sheets%20and%20Docs%20Extension%20to%20Google%20Sheets 
      3. Open the sheet with the desired anchors listed in one column. 
      4. In an adjacent column, use a formula to prompt it to give you variations: 
=GPT("Create variations of ["&$C3&"] in "&D$1&" output format: variation1, variation2, variation3, variation4)")


      5. You can choose any language you want and it will provide variations in that language: 
  



      6. Review and choose the most suitable ones. 




















[a]@<EMAIL> bless you :)
[b]winkwink 😁
[c]I'll leave this comment to live forever
[d]@<EMAIL> , I would add a bit more explanations here (how do you find orphan pages in this document, how do you identify them?
_<NAME_EMAIL>_
[e]@<EMAIL>, that's covered a bit below, but I should definitely add a jump link to the heading. Thanks!
[f]@<EMAIL> , maybe indicate some examples of when this would be needed, what should you look at?
_<NAME_EMAIL>_
[g]@<EMAIL> I added a link to the SF site where the difference between unique inlinks and inlinks is explained, just in case. I added a typical example of a bookmaker review, do you have anything else in mind? A more specific example or rather a general one (like the one I wrote)?