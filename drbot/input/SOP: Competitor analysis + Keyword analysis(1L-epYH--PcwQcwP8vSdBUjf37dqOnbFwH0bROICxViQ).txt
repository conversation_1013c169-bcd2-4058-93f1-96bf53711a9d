﻿SOP: Competitor Analysis + Keyword strategy
This SOP is designed to help you conduct a comprehensive competitor analysis. 


This can be used to do a single page-level competitor analysis, where you will have both a keyword and a URL to analyse. For example, “bet365 bonus code” for the URL https://www.betarades.gr/bet365/. 


In other cases, you may do a domain-wide competitor analysis. For example, https://www.betarades.gr/. 


This SOP is designed to help with both types of analyses, and to help you gain insights on how we can optimise our URL or domain to outrank competitors.
Table of contents (jump links to respective sections)
1. Identify competitors
2. SERP Analysis
   1. SERP history
3. Keyword Gap
4. On-page optimisation
5. Backlinks / link intersect
6. Output document for stakeholders
Identify competitors
The first step in conducting a competitor analysis is to identify who the competitors are. This can be done with a number of tools, including:
* ahrefs
* MOZ
* Google
Domain-wide competitors
In ahrefs’ Site Explorer you can find this by clicking on Competing domains under Organic Search.
  

  

The green bar represents the intersection of keywords that both domains are ranking for (yours and the competing domain). The bigger the green bar, the more overlap there is. 


The blue bar represents your unique keywords, while the yellow bar represents your competitor’s unique keywords.
Page-level competitors
For page-level competitor analysis - if you are looking to outrank for a specific keyword - the most obvious way to do this is to check SERPs. There are many ways to do this:
* Google search with a VPN and impersonal.me
   * When using impersonal.me it’s important to select the correct settings. Under +Options you will see three drop-downs: Interface language, TLD to use, Search from this location. 
  

* ahrefs’ Keyword Explorer → SERP Overview
* Mangools


You can also use ahrefs’ Competing pages function (which is located just under Competing domains that you used just before). However, I would advise to always cross-reference this with the latest SERP results, as ahrefs can be outdated.


Extra step for page-level analysis:
Check how the page is performing on GSC. This will help you establish the overview of the page, which queries it’s ranking for and how. 


If there are additional, related queries that the URL has a significant amount of impressions/clicks/av. positions then you may want to include these additional queries in your competitor analysis. The reason for this is because by strengthening the signals for multiple related keywords you signal to Google that your page is topically relevant for these semantically related queries.


For example, when looking at the URL https://www.betarades.gr/kouponi-pame-stoixima/ we can see on GSC that both ‘στοιχημα’ and ‘παμε στοιχημα’ are gaining a lot of clicks and impression, and are both ranking around #2-#3.
  



Query
	Translated
	Clicks
	Impressions
	Av. position
	στοιχημα
	bet
	369,894
	4,417,350
	2.4
	παμε στοιχημα
	Let’s bet
	196,188
	2,477,640
	3.6
	

Because they are both very important keywords, I would focus on both of them in a competitor analysis. See how the SERPs differ, what the tools say need to be done for each individual query and apply that.
SERP Analysis: Search Intent
Understand the search intent. It’s important to understand how Google understands the query and what types of results it presents.


There are four common types of search intent: 
1. Informational: A search falls under informational intent when the user is looking for specific information. It can be a simple search like “what’s the weather today?” that provides instant results or something complex like “best SEO strategies” that requires a more in-depth explanation. 
2. Navigational: In this case, the searcher is looking for a specific website or app. Common examples of navigational searches include “Facebook login,” “Semrush,” and “Amazon.”
3. Commercial: The intent behind a search is commercial when the user is looking for a specific product but hasn’t made the final decision yet. For example, searches such as “best SEO tools” and “best DSLR cameras” are all commercial searches. 
4. Transactional: Here, the intent is to buy. The searcher has already made a decision to buy a specific product or tool. Examples include searches such as “buy Nikon d500,” “buy Macbook Air,” and “buy groceries online.”


Thus, it’s important to spend some time analysing the SERPs to ensure that the page we have is serving this search intent.


In addition, we need to know if we have multiple pages that are addressing the same search intent. If this is the case then we may be cannibalising our own articles and preventing either of them from ranking well. This step is helpful in determining what type of page Google rewards and to go after that.


Here, you may need to use your own common SEO sense to understand what kind of page is needed.


Google the query and look at:
* What types of articles are ranking? → think about the 4 types of search intent. Is there a mix of informational and commercial pages? Take note of things you see and try to understand how Google is currently trying to address the query so we can ‘answer’ it the way Google prefers.
* Competitor pages that are ranking → what is their content about? What is the search intent?
* BC pages that are ranking → what is the content about? What are we covering that our competitors aren't? What are we missing?
* Cannibalisation → are there other pages from our website ranking for this query? Are these other pages stopping the ‘main’ page from ranking better for this query? You can follow the SOP for Cannibalisation to uncover these cases.
* Do we need to create a new page for the query you are analysing? If so, how do we ensure it won’t cannibalise with existing pages?
Historical SERP Analysis
Understanding the SERP history can give you an idea of how ‘sure’ Google is of what to show for certain queries. If you see a lot of SERP volatility, you can expect fluctuations for your page too. This is very common for queries related to an event and other commercial queries (which comprise a large part of BC’s portfolio). 


For example, in the lead-up to the PGA Championships, you may see a lot of fluctuation for the keyword ‘PGA Championships’ in the SERPs, as publishers will be creating new, fresh content around this and a user will want to have the most updated information.


On the other hand, if a SERP is very stable historically, then you can assume that Google is quite set on what type of content it wants to display. This is quite common for more evergreen types of articles.


If you want to know when your competitors gained/lost positions, you can do a historical SERP analysis.


For a rough overview you can go to ahrefs’ Keyword Explorer, type in the keyword, and go to SERP position history at the bottom. Here you will find the 5 main competitors for the keyword.
  



To find out exactly how the SERP looked on a certain day, you can use ahrefs’ Site Explorer 2.0. You can find this under Overview 2.0. Then, select which keyword to evaluate and click on ‘SERP’ on the far right. Then, select the date that you want to compare it to:
  



This way you can pinpoint the date a change in the SERP happened and analyse what was done differently. E.g. by using waybackmachine.org to compare on-page changes.


Another tool that will help you with this is GetStat.
If you have access to the market in question, you can go directly to GetStat. 
Assuming you are analysing the keyword ‘bet365 bonus code’ in the US market:
1. Click on the market in the Data Views field.
2. Click on the letter ‘b’ to access all keywords starting with ‘b’
3. Click on ‘bet365’
4. In the Keywords field, find the keyword ‘bet365 bonus code’ and click on it.
5. In the field at the bottom you will see the tab Archived SERPs. Here you can change the date and see how the SERPs have changed in the last month (it’s not possible to go further back than that, unfortunately).
  



Yet another tool that shows this data is Tableau’s Rankings Trend dashboard.
The dashboard can be found under: Keywords & Markets / Getstat Rankings / Keywords Ranking trends.


Assuming you are analysing the keyword ‘bet365 bonus code’ in the US market:
1. Click on US-en in the Market tab on the far left.
2. Find the keyword bet365 bonus code in the Keywords tab.
3. Optional filters:
   1. Further filter by either domain or by article.
   2. Change the minimum rank in the last 30 days if your article hasn’t been in the top 30 in the last 30 days.
   3. Change the date range accordingly.
4. Observe the ranking trends overview on the right.


  



Keyword Gap
Uncovering keywords your competitors are ranking for but you aren’t is a great way to develop your content strategy and expand the domain's topical authority. These newly uncovered keywords can either be used to develop an existing page or to potentially create new pages as supporting content. It is important to follow the steps outlined in the previous Search Intent section to assess what to do with these new keywords. 


You can find these keywords with a number of tools:
* Semrush
* ahrefs - content gap
* MOZ
* SurferSEO


Tip: All of these tools allow you to export the reports, which makes analysing the results a lot easier. If you don’t have Excel then you can simply go to Google Sheets and import the Excel file. 


Semrush - Keyword Gap
Once logged in, click on Keyword Gap on the left-hand-side. 
  



Then, enter your URL and your competitor’s URLs in the fields. Specify the relevant level of analysis: Root domain, Exact URL, Subdomain, or Subfolder. 


If you are doing a domain-wide analysis you will likely just need to use Root Domain. If you are doing a page-level analysis, you will want to use Exact URL. 


In addition, it would be great to also look at Root Domain gaps, as well as Subfolder gaps to see if a competitor is covering certain queries more comprehensively in other parts of their website, such as a different subfolder. Looking at a single URL in isolation only gives you a small part of the picture, so broadening the scope a little can help in understanding how Google views a website/subfolder/URL. 
  



Once you have populated all the fields, you will see something like this:


Note: this example is a URL-level analysis of 1 keyword and 3 competitors.
  

You can see the overlap visually, and if you examine the individual tabs you can see various categories of keywords you can target.
* Missing → Keywords for which our domain/URL doesn’t have rankings but all of the other competitors do.
* Weak → Keywords for which our domain/URL ranks lower than any of the other competitors.
* Untapped → Keywords which our domain/URL has no rankings but at least one of the competitors does.


This helps pinpoint areas of weaknesses to see what keywords we need to boost and which keywords we should start to target. 


ahrefs - Content Gap
ahrefs has a near-identical feature as semrush, though arguably not as detailed. To use the tool, follow these steps:


1. Once logged in, enter the URL you wish to analyse into Site Explorer. 
  



2. Enter the competing URLs and ensure that ‘URL’ is selected for all cases. 
   1. By default, ‘At least one of the targets should rank in top 10’ is switched on, but you can play around with this if you want. 
3. Click on ‘Show keywords’.


Here you can see the keywords each competitor is ranking for, and which spot it has in the SERPs. If there is a dash ( – ) instead of a number, it means that the competitor is not ranking for that keyword in the top 10 (of course, if you unselected ‘‘At least one of the targets should rank in top 10’ then there won’t be any dashes).
   


You can add further filters, such as filtering by Search Volume, KD, Word count of the keyword, and also by including or excluding certain terms:
  



Tip: export the list of keywords and conduct your analysis in Google Sheets.


This Content Gap analysis can be done on a page-level, subfolder level, or domain-level. You can specify this by toggling to URL, Prefix, or domain:
  



MOZ - Ranking Keywords
This tool will visualise how your content overlaps with competitors. It is similar to semrush and ahrefs.
  



SurferSEO - True Density
SurferSEO’s SERP Analyzer tool allows you to see how pages in the SERPs stack up against one another. There are a lot of opportunities to filter and tailor your specific view, but for the purpose of keyword gaps, this is what you can do.


Once you have entered your keyword and market, SurferSEO will take a few seconds to process the request. Once done, click on the keyword and scroll down the the Search Results field: 
  

It is important to filter out competitors that you do not want in your analysis here. For example, if there is a page that isn’t a direct competitor, like a bookmaker or a major news corporation, then you may want to exclude this. You do this by clicking on the eye icon on the left.


Another tip is to exclude the results that are ranking below you because why would you want to replicate what they’re doing if you’re already outperforming them? 


Sidenote: if the SERP is very volatile and you see a lot of fluctuation in the SERPs where a result is #1 one day and below you another day, you may want to include that URL as well.


Once you have your filters in place, click on the three dots on the right-hand-side and then Audit.
  



This will open the Surfer Audit for you. For the purpose of the content gap analysis, scroll down to the True Density section. Here you will see the ‘list of important items’ that are required for the query in question.


A tip is to sort the ‘action’ tab by urgent items first (as shown below). Here, SurferSEO will give you suggestions on what to add or remove. Pay attention to the ‘relevance’ tab as well, as not everything will be 100% relevant to the query.
  

Note that some of the phrases are labelled ‘NLP’. This is because SurferSEO has incorporated Natural Language Processing (NLP) algorithm in its analysis tool, now uncovering writing opportunities for: 
1. Sentiment Analysis—find out if top-ranking pages have a positive or negative context
2. Entities Coverage—get better suggestions in True Density. Find out which entities are the most relevant for Google for your specific topic
3. Usage Context—figure out how your competitors use relevant words and phrases


On-page Optimisation
Once you have established the search intent of the query, uncovered your competitor, and identified what your page is missing, you can move onto on-page optimisation suggestions.


There are so many areas to focus on when it comes to ‘on-page optimisation’, such as:
* Headings
* Keywords
* Internal linking
* Anchor texts
* Image optimisation
* Schema
* E-A-T
* Meta data
* And more
The scope will vary with each task, but you can use this checklist as a general guide.


You can use many different SEO tools, including:
* SurferSEO
* Ahrefs
* Semrush
* Waybackmachine.org to compare historical data 
* Manually comparing pages with your SEO brain


SurferSEO: Overall content audit
Here you can run the Audit as mentioned in the previous section to uncover areas of improvement. 
  



SurferSEO splits up the audit between missing common backlinks, True Density, words, exact keywords, partial keywords, number of elements, characters, time to first byte, and load time. 


Another helpful tool in SurferSEO is the Content Editor. Here you can paste in the content and it will take the data from the audit and give you an overall Content Score. 
In addition, SurferSEO will tell you directly what keywords you need to add (and how many times) or what content to remove (and how many times).
  



ahrefs: Keyword gap and a Site Audit that uncovers internal linking opportunities
ahrefs presents a multitude of ways to improve the page; this can be through the Content Gap function as outlined earlier.


Another way to use ahrefs for on-page optimisation is the Site Audit. You can crawl the whole site (if it is small enough) and analyse the internal links and anchor texts. 


Note: there is a limit of 1,000,000 URLs that can be crawled, so this may not be an option for some of the sites in our portfolio. If this is the case, you can opt to crawl only a section of your site.


The Link Opportunities report shows your internal link opportunities based on keywords pages are ranking for and the mentions of these keywords on other pages.
  

A detailed guide for this feature can be found here: https://app.ahrefs.com/academy/how-to-use-ahrefs/site-audit/link-opportunities 


Screaming Frog: a crawler that can uncover internal linking opportunities
Another way to crawl a domain - or parts of a website - is with Screaming Frog.


You can set up the crawl however you want with Screaming Frog: you can either crawl the entire domain, only sections of a domain


Once you have completed the crawl, go to the Internal tab, click on the URL(s) in question, and then click on the Inlinks tab to see the list of internal backlinks to that URL(s). You will be able to see what the anchor text(s) are.


If there are unoptimised anchor texts, or a disproportionate amount of exact match anchor texts, this could be an opportunity to diversify the anchor texts and incorporate more secondary/related keywords in the internal linking strategy.
  



You can also play around with Custom Search functionality, which would essentially replicate the Internal Link Opportunities report by ahrefs. A full guide on how to use Custom Search can be found here.


This is a great tool to use if you want to identify new internal link opportunities - i.e. pages that mention the word (or a variation of it) but do not yet link to a page.


Image optimisation
This can be done manually. Here you want to ensure that the images on the site are:
* Compressed
* The file names are optimised
* The alt attributes are optimised
* If possible, in .webp format
* Images below the fold are lazy loaded


To check if images are compressed enough you can run them through a tool like tinypng.com. In the below example its evident that the image https://betarades2-5c24.kxcdn.com/wp-content/uploads/2021/09/aktinografia-kouponi.jpg  on https://www.betarades.gr/kouponi-pame-stoixima/ can be compressed a further 48%:
  



In addition, the file name should always be tailored to the specific image and the page in question. You should not have random file names like ‘Screenshot-2021-12-08’. Instead, the file name should represent what the image is about.


The alt attributes are also great ways to optimise the page further. These should include the primary or secondary keywords, explain what the image is about, and be contextually relevant.


Also, if possible, images should be in .webp format. WebP is an image file format developed by Google and is a great way to have high-quality images that are far more compressed compared to JPEG, JPG or SVG formats.


Finally, any images that are below the fold should be lazy-loaded. This will help improve the Pagespeed of the page overall, as non-essential images don’t need to be loaded the second the page is opened. 


Schema 
There are many schema options and not all schema types will be relevant for every page. Use https://validator.schema.org/ to check if a page has the schema that it should. If there are errors you should troubleshoot them.


You can also use the following tools:
* Validator.schema.org (this will test any kind of schema)
* Rich Results Test (this only tests schema that results in rich results in SERPs, e.g. FAQ, HowTo, Review etc)
* Screaming Frog


A general rule of thumb with schema:
* The About Us or homepage should be the only ones with Organization schema 
   * ‘Organization’ can be nested under other schema types on other pages, but the standalone Organization schema should only be on one of these pages
* Each author page should have a Person property
* Breadcrumb schema should be in place if possible
* Always explore new schema possibilities for a page!


Waybackmachine.org
A great way to use this tool is to uncover changes made on-page by competitors that may have led to their ranking improvements. Changes in waybackmachine.org should be cross-referenced with changes in historical rankings, which can be done with ahrefs or GetStat as outlined previously.


For example, in a past competitor analysis for betarades.gr it was discovered that a key competitor, sentragoal.gr had implemented a link to the page in question (Stoiximan) in a sub-navigation bar, and this change coincided with their improvement in rankings. 


Below you can see the difference between a few weeks where the page ‘Stoiximan’ was added as the 2nd bookmaker in the sub-navigation bar. On the 8th of December they were ranking on the 2nd page, and on the 11th of January they were ranking on page 1. This could be a key puzzle in their ranking change:


  

Backlink Analysis
We need to evaluate both the backlink quantity and quality. Generally speaking, it is best to do this with the top 5 competitors in the SERPs. To carry out a backlink analysis, we can use a number of tools:
* Ahrefs: URL Rating, Domain Rating
* Moz: Domain Authority, Page Authority, Spam Score
* Majestic: Trust Flow, Citation Flow


Fill in this template to get a quick overview of how BC’s website is performing in relation to competitors:
  

Definitions:
* URL rating: Shows the strength of a target page's link profile on a 100-point scale.
* Domain rating: Shows the strength of a target website's backlink profile compared to the others in our database on a 100-point scale.
* Trust Flow: A score that is weighted by the number of links from a seed set of trusted sites to a given URL, or Domain. 
   * i.e. perceived trustworthiness based on the quality of backlinks.
* Citation Flow: A score that is weighted by the number citations to a given URL, or Domain. 
   * i.e. the popularity of a site based on the number of backlinks
* Domain Authority: Predicts a root domain's ranking potential relative to the domains in our index. Use this score to compare your likelihood to rank above that of your competitors.
* Page Authority: Predicts a page's ranking potential in search engines based on an algorithm of link metrics.
* Spam score: Represents the percentage of sites with similar features we've found to be penalized or banned by Google.
   * Anything up to 3% is okay
The Output 
When compiling all of your research, you need to consider who the main stakeholders are and what the best format for the deliverable is. This could be a Google Doc, a Google Slides presentation, or a Google Sheet with a list of action points - or a combination of these three things.

Examples of competitor analyses can be found below:
* VI Pointsbet analysis
* [VI] VegasInsider Bet 365 keyword & Page analysis
* Pariurix Competitor Analysis: ‘Biletul zilei’ & ‘cota 2’
* Betarades Competitor analysis: bet365, stoiximan, interwetten
* This template for competition analysis that focuses more on the visual aspect


You should be as concise as possible in your deliverable and, if possible, provide a prioritised action list. 


Another tip is to include the methodology so that when you refer back to a document you can easily jump back into it and have all the information at hand.
Reference material