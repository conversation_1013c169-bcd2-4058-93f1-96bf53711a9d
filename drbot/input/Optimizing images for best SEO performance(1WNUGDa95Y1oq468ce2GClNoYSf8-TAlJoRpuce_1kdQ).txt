﻿Image Optimization Guide
































































TABLE OF CONTENTS


* Intro and Motivation
* The Root Cause
* Different File Formats And Image General Usage
* Optimization For Speed
* Google Image Optimization Recommendations
*     Latest image optimization techniques utilization
*            Use the right image format
*                        -  Jpeg image format
                       -   Gif image format
                       -   Png image format
                       -   SVG image format
                       -   WebP image format
*            Use the correct level of compression
                                                 -What is Image Compression
                                                                      - Image Compressors
                                                                      - Compress images with Ezgif
                                                                      - Compress images with Gimp
                                                                      - Compress images with plugin
*            Replace Animated Gif With Video
*            Serve Responsive Images
*            Use progressive image types
*            Serve images with correct dimensions
                        -How to determine image size
                                     - Use Chrome Extension
                                   - Use Mozilla Developers Tools
                                   - Use the ruler option in developer tools
                        - How to set correct dimensions for images
                                -Correct image dimensions with Ezgif
                        -Correct Image Dimensions With Gimp
                                -Correct Image Dimensions With Plugin




   












































































Intro And Motivation




As part of our efforts to optimise most of the sites in our portfolio, we examined around 20 of them. The majority of the issues were related to big and unoptimized images and caching mechanisms. Based on the audit insights, the two issues are highly mutually correlated. In other words, one can implement the most powerful caching mechanism that exists on the market, but the usage of unoptimized images is going to prevent potential enhancements and, in the long run, it will block future performance improvements. Thus, this is the reason why we created this document.


The Root Cause
As for image optimization, as mentioned earlier, two main causes have been identified.


Typically, the final product delivered by the web designer will be a poster-like image, usually in.png format ready to be implemented in the CMS. According to the specific page layout, the image will be sliced into smaller, unoptimized images chunks, usually in the same format, and inserted into the page by the Word-Press developer. Luckily, commercial themes are already optimized up to some point, but still, the main philosophy is not to create images optimized for the mobile-first initiative. That's the reason why we have initially heavy themes in the beginning. Also, among the developers, it's a common practice to use icons and smaller images (usually .svg) directly from the CDN (FontAwesome, MaterialDesign...) in some vector format. Usually, this can bring some benefits if it's not massively used. Nowadays, most of these CDNs for their free plans use slow servers which can create performance issues.


For the second cause are responsible content creators. Usually, when creating SEO-optimized text, image optimization is neglected as trivial. Among the creators, there's a tendency to automate the image insertion process by fetching the images from some image pool in order to save time, but in that way, an unoptimized and uncontrolled stream of images aggregates over time directly affecting site performance. Images obtained in that way are not going to scale, declared theme dimensions will not be used properly, this will impact the caching mechanism and the page will load slowly because it's heavy, worsening the whole user experience which may have an impact on SERP.








Different File Formats And Image General Usage
Today, there are many types of images, and they are widely used to improve the appearance of web pages and their attractiveness. But now, after the launch of the mobile initiative, the need to optimize images has been re-emphasized.
The following table shows the most commonly used image types and their overall usage.
Image type
	Name
	Image Type
	Support Compression
	Usage
	Jpeg (jpg)
	Joint Photographic Experts Group
	Raster
	Yes
	If used, should be compressed and scaled to the right dimension. Good choice for blog article images.
	Png(.png)
	Portable Network Graphic
	Raster
	Yes
	Infographics, banners, screenshots, coupons
	Gif(.gif)
	Graphics Interchange Format
	Raster
	Yes
	Art, tutorials, guides
	WebP(.webp)
	WEBP
	Modern raster
	Yes
	Post images, featured images, image galleries
	Tiff(.tiff)
	Tagged Image file format
	Raster
	No
	For storing images that will be used for print (graphics and publication)
	BMP(.bmp)
	Bitmap
	Raster
	No
	Outdated
	HEIF
	High-Efficiency Image File Format
	Raster
	Yes
	Used by some phones and cameras for image storage
	SVG(.svg)
	Scalable Vector Graphic
	Vector


	No, Scales down without losing the quality
	Small icons, simple logos, responsive requirement
	PDF(.pdf)
	Portable Document Format
	Vector
	No, Scales down without losing the quality
	Infographics, cannot be used within HTML
	



The most common image file types used for the web are presented in the table below where the most used image types are .png (77%), .jpeg(71.8%), and.SVG(27.0%).


  



Altogether with an optimized jpeg, the .webp image type is the most effective in terms of loading speed and compress/quality ratio.


The diagrams below represent the growing historical trend and the percentage of websites that are using WebP.(up to 07 Apr 2022).


  

	  

	



As we can see, the web image type is still not widely accepted, but its trend is undoubtedly on the rise.
With all that, from the image optimization angle (unoptimized and unscaled .jpegs along with .png’s are the main causes for poor site performance), and in general, advice would be


1. DO NOT Use PNG for post images and featured images, including galleries!
2. If JPEG is used, should be compressed and scaled properly!
3. Prioritize if possible Jpeg conversion to WebP!
4. Avoid SVG hosted on CDN free plan, use WebP locally hosted instead.
5. Use exact image dimensions for the particular position on the page.
























Optimization For Speed
According to the Google guide, images are the largest contributor to overall page size, which can make pages slow and expensive to load.  Images are returned for 27.3% of all search queries on Google. (source).


Google image optimization recommendations are based on two approaches:
* The latest image optimization techniques utilization
* of Responsive image techniques implementation


Images are the third-largest asset crawled by Google. A specific bot (Googlebot-Image) is dedicated to that purpose.
  





According to the data collected by MOZ, search prevalence for images is 32.4% and it’s steadily rising. As an example, goalPl is used.




  

	  

	

In this example, we can see that 5% of all requests by Google bot are for Images. Also, month over month the percentage of image requests are increasing.


Google controls more than 92% of the search market share worldwide, so it’s very important to adopt their suggestions for image optimization if we have in mind that a third of all Google searches are for images and 13% of SERPs feature an “Image pack” (source: MOZ).  According to the HubSpot article, the 7th most important Google statistics says that compressing images and text could help 25% of web pages to save more than 250KB and 10% save more than 1MB. These changes reduce bounce rates and increase page rank on Google SERPs and may impact the crawling rate. All this emphasizes the importance of optimization in general.


Google Image Optimization Recommendations
Latest image optimization techniques utilization


Use the right image format
Use the correct level of compression
Use image compressor
Replace the animated gif with a video
Serve responsive images
Serve images with correct dimensions
Use progressive image types
Use CDN


The techniques painted in red are recognized in our portfolio as the problem and a way to do our potential improvement.
Use The Right Image Format
Even though this is something that should be considered by the designers in the early stage of theme development, the right image format used in the right format is a very important part of the optimization. Good and simple design will ofter excel in performance. Always consider image alternatives such are:
* CSS Effects (shadows and gradients)
* Web Fonts


When we undeniable determine that the image is necessary we should select the right image format for the right position.


There are two main types of graphics widely used for the web
1. Vector Graphics (.svg)
2. Raster Graphics (.jpg, .png, .webp etc.)


Vector formats are ideally suited for images that consist of simple geometric shapes. (logos, text, icons, infographics). Vector can be zoomed in and in general, have better resolution. 
Raster images are used for post images, thumbnails, featured images, and image galleries and they should be used as responsive.
As I mentioned before, vector images are suitable for icons and simple images and they should be hosted on a fast CDN and served minified.




In the table below are presented image formats and their recommended usage.




Format
	Category
	Palette
	Usage
	JPG
	Lossy
	MIlion of colors
	Still Images
Photography
	GIF
	Lossless
	Maximum 256 colors
	Simple Animations
Graphics with flat colors
Graphics without gradients
	PNG-8
	Lossless
	Maximum 256 colors
	Similar to GIF
Better transparency but no animation
Great for icons
	PNG-24
	Lossless
	Unlimited Colors
	Similar to PNG-8
Handles still images and transparency
	SVG
	Vector/lossless
	Unlimited Colors
	Graphics/logos for web
Retina/high-dpi screens
	WEBP
	Lossy/Lossless
	Milion of colors
	For images on the web
	Jpg Image format
Depending on your preference, you may refer to this format as either ‘JPEG’ or ‘JPG’ – both are accepted variations of the same acronym – Joint Photographic Experts Group.


Unlike GIF, JPEG is a 16-bit format, which means that it can blend red, blue, and green light to display millions of colors. This makes JPG very ‘photo-friendly’. This is partly why it is a standard format when it comes to most digital cameras on the market.


The JPEG format also allows you the flexibility to choose how much you compress your image – from 0% (heavy compression) to 100% (no compression). Generally, a 60%-75% compression setting will shrink your file considerably while keeping your image looking decent on most screens.


While JPEG is well suited to compressing and rendering photography, it is a lossy compression type which means it’s less useful for the ongoing editing of an image. Exporting JPEG results in a loss of quality, and these losses get worse with each successive export – like a photocopy of a photocopy. This is why professional photographers generally shoot in lossless RAW format.


Choose JPEG for
* Still Images
* Photography
* Images with complex colors and dynamic
* Post images
* Featured images
* Image gallery


Gif Image format
The GIF format is a type of bitmap, but unlike JPEG or PNG, GIF files are limited to a maximum palette of 256 colors. Essentially each GIF image contains a preset ‘box of crayons’ and there is no way to truly mix those colors to make new colors.
While 256 might sound like a lot of crayons to work with, complex photographs typically have many thousands of tones. This color range is lost during the GIF conversion process and this is the key reason not to use GIFs for color photos.
While GIF is generally a poor choice for images with wide color variation, that 256 color limit can help keep file sizes small which is ideal for even the slowest of internet speeds.


Choose GIF for
* Simple Animations
* Small Icons
* Graphics with low-to-pixel variation (i.e lots of flat colors like logos and flags)




Png Image format
A newer file format than GIF and JPEG, the PNG (Portable Network Graphics) is like a marriage between both the GIF and JPEG format thanks to its two variants.


1. PNG-8
2. PNG-24




PNG-8 is similar to GIF in many ways and uses the same 256 color palette (maximum). It has better transparency options and usually exports slightly smaller file sizes. However, PNG-8 has no animation function.


PNG-24 allows you to render images with millions of colors – much like JPEG – but also offers the ability to preserve transparency. Because PNG-24 is a lossless format file type, you are likely to get larger files, but if image quality is more important than file size, PNG-24 is your best option. Even so, services like TinyPNG.com can often make a big difference to your file size. Compared to their cousin JPEG, PNG-24 files are not as universally compatible with every app and platform which makes the format marginally less ideal for web sharing. However, it is capable of being edited without diminished qualities.


Use PNG for
* Web graphics that require transparency
* Color heavy and complex photographs and graphics
* Images that require re-editing and re-exporting




Svg Image format
Unlike the three formats mentioned above, SVG (Scalable Vector Graphics) is not a pure bitmap format. Instead, it is a vector format – a close cousin to Adobe Illustrator’s AI format and EPS – that is steadily becoming an attractive option for web and UI designers.


Sometimes it’s helpful to think of SVG as ‘HTML for illustrations’ and you need to think about it quite differently from other image formats listed.


SVG is best-suited to displaying logos, icons, maps, flags, charts, and other graphics created in vector graphics applications like Illustrator, Sketch, and Inkscape. Written in an XML-based markup, your SVG can be edited in any text editor and modified by JavaScript or CSS. As vectors can be scaled to any size while retaining crisp image quality, they are ideal for responsive design.


Though SVG is a vector format at its core, it is possible (even common) to embed bitmap graphics inside your SVG file – just as you might embed JPEGs in your HTML.


You can do this by either linking to an image source via its URL (as you might link to JPG in a webpage) or by encapsulating the pixel image as a Data URI. This gives SVG unchallenged flexibility and power.


Use SVG for
* Logos and icons with strong, geometric, vector-friendly design
* Graphics that may need to be displayed in multiple sizes and screens
* Graphics that respond to their device
* Graphics that need to be edited, updated, and redeployed.


WebP Image format
When it comes to pagespeed, this format is the preferred one to use. So if you’re debating on a .jpg or .webp version please select the .webp :) 


WebP is a modern image format that provides superior lossless and lossy compression for images on the web. Using WebP, webmasters and web developers can create smaller, richer images that make the web faster.


WebP lossless images are 26% smaller in size compared to PNGs. WebP lossy images are 25-34% smaller than comparable JPEG images at equivalent SSIM quality index.


Lossless WebP supports transparency (also known as alpha channel) at a cost of just 22% additional bytes. For cases when lossy RGB compression is acceptable, lossy WebP also supports transparency, typically providing 3× smaller file sizes compared to PNG.




Use WebP for
* Post images
* Featured images
* Image Galleries






Use the correct level of compression
When saving images for the web, the recommended file size should be less than 2MB (2048 kilobytes) to maintain fast page loading speeds. That being said, we also want images to be sharp and not pixelated. Image quality is contingent on factors such as the final file size and compression settings. With the right compression settings, you can achieve good image quality and a small file size. Generally, you can save a file with an image compression level between 70-80% without seeing pixelation, or the individual points of color.


Note: The amount of compression will vary on a number of factors including the original image’s file size and format. You can use a free tool such as Imagify to further compress the file size.
What is Image Compression
Each image requires a certain amount of memory to be stored. The amount of required allocated memory is based on the image dimensions, how many pixels the image contains, and the number of colors available per pixel (bit depth). Image compression is the process of reducing the amount of memory that an image takes up.


Image Compressors
There are a lot of free image compressors available for free online. They are using a combination of optimization and lossy algorithms to shrink images to the minimum possible size while keeping the required level of quality. They often offer combined optimization options and support different image formats. The only limitation is the size of the upload. They are all basically similar, intuitive, and easy to work with.


Online compressors:
* JPEG Optimizer
* Kraken
* Tiny PNG
* Jpeg.Io
* ImageRecycle
* Compressor.io
* EzGif
Software for image compression:
* PhotoShop
* Gimp


Compress images with Ezgif


We recommend using this browser tool when optimizing you images. It’s fast an easy to convert, compress, and save.


Ezgif is yet another website for image optimization and compression. Easy and intuitive, this website offers wide optimization and conversion options. This online tool is available at this address, it’s free and the user is not obligated to register and log in.
  



To compress an image, you need to click on the WebP Button, jpeg to Webp button, and upload an image. Supported formats are gif, jpg, png, apng, and others. In the example, I am going to use an unoptimized but scaled jpeg image as an example.
  



  





As an example, I have used an image from betarades.gr. Right-click on the image to display image information. For this example, the image width is 595 and the height is 340 pixels.
  



The size of the uploaded document is 243.4 KiB. Click the Convert to WebP button to begin the conversion.
  



Since there’s no improvement click on optimize button, check the Lossy Compression checkbox, and reduce the quality (32%). Check if the image is still within the required quality range and hit Optimize image button.
  



The final result is compressed, webP image 27.77 KiB in size. As a result of this conversion, I manage to achieve an improvement of 90.6%.
  







Compress images with Gimp


GIMP is a free and open-source raster graphics editor used for image manipulation and image editing, free-form drawing, transcoding between different image file formats, and more specialized tasks.


Click file - > open and browse to the image, select and click open.
  

At the bottom of the page, there’s info about the image. This image is 1.9MB in size and the type is jpeg. The goal is to convert this image to web format.
Click on the export button and select webp image extension from the drop-down. 
  





Once you click the export button, another window will pop up.
  



Reduce image quality to 65% and Alpha quality to 70% and click export. Image is now compressed and converted to webP (total size 36 KiB). 




Compress images with the WP plugin
Ewww Image optimizer is Word Press capable to automate image compression which is considered a light WP Smush version.


All you need to do is to configure settings and enable automatic conversion.
  



A bulk image optimizer is also available.








Serve Responsive Images


Images that work well on devices with widely differing screen sizes, resolutions, and other features are responsive images. It is a part of the responsive design of CSS.
Some ways to serve responsive images:


* Adaptive image. Small PHP script that detects the user screen and delivers the proper image dimensions for that screen size.
* Cookies: Serving responsive images with cookies. It detects the screen size using JavaScript and sizes the proper images using PHP before it’s served and loaded on the page.
* HTML Picture Element: This is considered to be the future standard of responsive images. A new element was added <picture> and proposed to allow us to set different image sources. It is still in the draft stage.
* Picture FIll: This is a small JS library capable of mimicking <picture> tag using <span> element.
* WP Plugins: 
1. PB Responsive Image
2. WP Responsive Image
3. Simple Responsive Image
4. Picture Fill WP
Replace the animated gif with a video
GIFs are one of the oldest graphic file formats on the Web. GIFs support transparency as well as animation but include only up to 256 colors within a frame. If a GIF includes an animation, each frame can support a separate palette of up to 256 colors. It can noticeably affect their quality and make them appear pixelated.
On the other hand, video supports unlimited color palettes. Gif also does not support sound.
Why use video instead of Gif? More color for display and reduction in size up to 60 % which leads to faster loading.


Use progressive image types
Progressive images load immediately on the website at first with a low resolution. Then, they increase their resolution as the website loads completely. You may notice a website uses progressive images when the content looks blurry at first. Then, it sharpens within a few tenths of a second.
The most recommended type is .webp because it generally has better compression than JPEG, PNG, and GIF and is designed to supersede them. AVIF and JPEG XL are designed to supersede WebP.
The image below represents a web support overview for 2020, 2021, and 2022.
  







Serve Images With Correct Dimensions
As an extreme example, the website in our portfolio smartbets is using vector images on the main page. Images are from the CDN (font awesome - free plan) and they are loaded un-minified acting as the render-blocking resource causing slow server response and loading speed.




  

	  

	

The image on the right is zoomed 500% and the server is forced to download this vector image using free and slow CDN for the position on the left rendered to 151x150 px. For this position, the vector seems to be a good image type but should be scaled and served locally.


Another example where the incorrect image size is used is the logo. The required logo size for the desktop is  85x27 px but the actual loaded image can be zoomed up to 1390x398. (loaded image)


There are three most common image formats used for the web.
1. .png (Use them if the images are small and you need transparent background)
2. .gif (Use them for very small images like 5x5px background title or animated image)
3. .jpg (Use this or .jpeg images for displaying photos, illustration or images)


How to determine image size
Use Chrome Extension
There’s a variety of browser extensions available on the internet. Image size info  chrome extension is just one of them. After the installation new option will be added to the mouse right-click option. Hower over the image and just click that option to reveal the image dimensions, display size, and file size.
  

  







Use Mozilla Developer Tools
Sometimes it’s not possible to use the browser extension. Another way to reveal image dimension is to use the Mozilla developers tools option which is available on default once you install the Firefox browser.
If you click the “inspect” option from the right-click menu, this option will open docked at the bottom of the page. Pick the element from the page you want to inspect.
  



Hower mouse over the image you want to inspect and image size details will be presented.
  







  

Now, you can see that for the current dimension on the website (730x486) a much larger image has been uploaded (960x640) and scaled to (640x427). Finally,  from the client-side, the browser is stretching this image to fit the position dimension to be as close as (730x486).
The difference is quite obvious.








Use the ruler option in developer tools
On developer tools click settings and check the “measure the portion of the page” option on the bottom.


  

A new ruler will be added.
  

Stretch the ruler from the beginning to the end of the image and asses the image size.
  







Lazy-Load Images And Video
1. Leverage Lazy loading for better loading speed
2. Lazy-loading images
3. Browser-level image lazy-loading
4. Lazysizes to lazy-load images
5. Optimise CSS background images with media queries




How to set correct dimensions for images
Correct image dimensions with Ezgif


Ezgif is yet another website for image optimization and compression. Easy and intuitive, this website offers wide optimization and conversion options. This online tool is available at this address, it’s free and the user is not obligated to register and log in.
  



Images can be uploaded directly via the URL or from your computer. Click on the resize option.
  

Supported image types are listed below. Our image is (960x640)px and we want to resize this image to fit the position with the size (730x487). Paste the image URL and click upload.
  





The image will be displayed. Our example is 97.73kIb in size. Enter  730px in the width field and select “force original aspect ratio” and click the “resize” button.
  



The final result is displayed. The image is resized to fit the position (730x487) and we also achieved some savings of 24.15%.




Correct Image Dimensions With Gimp 
  

Finally, click the save button and download the picture.




Correct Image Dimensions With Plugin


Resize image after Upload is a WordPress plugin capable of resizing images directly within WP after the image upload, but only for new images.
  

  





When the plugin processes images, it discards the original uploaded file. That means the original file is not maintained on your site, and also, any EXIF data embedded in the original image will not appear in the resized image.
























Optimization And Compression for Wordpress
WordPress - Step By Step Guide


Every WP theme has already a predefined image size for each position and instructions should be included in the official documentation. In general, a content creator should be aware of one position and its dimension, the position that holds the image in a single post, and which is also a post featured image.
Images should be uploaded only through the media library system!




  

Determine the right image dimension
Each theme already has its own demo which is placed somewhere in the marketplace. The best way is to go to the particular demo post page and measure image size using google developers tools.
  



By examining the featured image on the single post we can see that image is scaled to 730x486.667 px. However, the uploaded image is 768x 512 px which is bigger than the required size. Note, this is not the most drastic example but following the rule to prepare images to fit position by dimension can make a big difference in any case.


Prepare the image placeholder in GIMP
For the purpose of this demonstration, I am using GIMP which is a free image editor, powerful, and easy to use.  
I know that image should be 730x 486 px. Using GIMP I am creating a transparent image placeholder to fit this dimension. From the option, I am choosing a background to be transparent to ensure that I have a lighter placeholder for my image.


In the image size section, I am setting up the right image dimension in pixels. Everything else should be the default.
Click OK
Transparent image placeholder is now ready and it’s fitting the featured image size required by the template (730x486).
  







The image that I want to put in the post is quite big, 1200x860 px. Using GIMP I am resizing the image to fit my placeholder. In this case, it’s 730x523 px. My width is ok but my height is a bit bigger.
  



The next step is to paste the scaled image to our placeholder.
  

Move the image until it fits the whole placeholder and set a focus.
  



Select File - Export and give the picture a descriptive name. Click image - flatten the image to remove the alpha channel. After that Click export image in .png format.
This final result has appropriate dimensions and good quality.
  







Optimize image for Web
Open online tool EZGif click browse and upload the image.
  

Our file size is 541.87kib in size. Click convert image to .webp. Since the public in the background is already blurred, click convert and choose jpeg format which is a suitable type for quality reduction.
  

We manage to reduce image quality by nearly 88%. This image is relatively rich in colors so we need to try if this can be a little bit better.
Open GIMP once again and load the prepared png file. Click export as and this time choose jpeg as the file type.
  



Reduce the quality to 70% and click export.
  











Now the image size is much smaller than our starting png.
  





Open EzGif again and upload the jpeg. Click optimize and reduce the quality a bit more.  Now we managed to reduce the image by 41.7%.
  

Finally, click on “.webp” and press the Convert to .webp button. The final result is the .webp image, reduced by 41.57 % from the original jpeg image and scaled in the right format with 35.65 kb in size.
  

So far, we learned that the .png format is not the right format to use on the web and should be avoided. Jpeg is much more suitable for compression. If possible, jpeg should be converted to .webp type as Webp format, on the other hand, the idea is to pick a format that is the best in terms of size and quality ratio.






Upload images using the media library
By default, the WordPress media library will create 9 additional images in different sizes.


Png Image
	Size
	Original (optimised jpeg)
	Size
	New (optimised jpg)
	Size
	Optimised WebP
	Size
	

	

	  

	768x512




74.3kb
	

	

	

	

	  

	730x486


554.9kib
	

	

	  

	730x486




33.1kb
	  

	730x486


22.9kib
	  

	150x100


37.5kib
	  

	150x100




5.1kb
	  

	150x100




5.2kb
	  

	150x100




4.6kib
	  

	150x150


52.7kib
	  

	150x150




6.4kib
	  

	150x150




6.6kib
	  

	150x150


5.6kib
	  

	218x150


75.7kib
	  

	218x150


8.4kib
	  

	218x150


8.7kib
	  

	218x150


7.1kib
	  

	265x198


113.9kib
	  

	265x198


11.7kib
	  

	265x198


11.9kib
	  

	265x198


9.4kib
	  

	300x200


128.2kib


	  

	300x200


12.8kib
	  

	300x200


13.1kib
	  

	300x200


10.3kib
	  

	324x400




238.4kib
	  

	324x400






24.1kib
	  

	324x400






21.8kib
	  

	324x400






15.3kib
	  

	485x360


325.2kib
	  

	485x360


29.8kib
	  

	485x360


27.5kib
	  

	485x360


18.8kib
	  

	696x464


549.9kib
	  

	696x464


51.9kib
	  

	696x464


43kib
	  

	696x464


26.6kib


	

	

	  

	741x464


57.9kib
	None
	None
	None
	None
	





















Dimensions
	Png file-size
	jpeg-Original file-size
	New jpg-Optimized file-size
	WebP file-size
	768x512
	0
	74.3
	0
	0
	730x486
	554.9
	0
	33.1
	22.9
	150x100
	37.5
	5.1
	5.2
	4.6
	150x150
	52.7
	6.4
	6.6
	5.6
	218x150
	75.7
	8.4
	8.7
	7.1
	265x198
	113.9
	11.7
	11.9
	9.4
	300x200
	128.2
	12.8
	13.1
	10.3
	324x400
	238.4
	24.1
	21.8
	15.3
	485x360
	325.2
	29.8
	27.5
	18.8
	696x464
	549.9
	51.9
	43
	26.6
	741x464
	0
	57.9
	0
	0
	





Y-axis = file fize
X-axis = image dimensions
  

  

Now, it’s clearly visible that optimized images with the right dimensions are better for optimization as they have smaller, but the most important correct sizes. The bigger the image size the better compression for the same dimension and the bigger the difference between jpg images and .webp. 


Furthermore, the original image is uploaded with the incorrect size (768x512) and because of that, one more image is created with the size (741x464) as the substitute. This additional step does not exist when images are correct in size.
WebP images are clearly the best option, but optimized jpg images can be also implemented.
The benefits of using optimized images, images with the right size, and new image formats are obvious.
PNG images are the worst solution, they are very heavy, even their thumbnails are big in size and should never be used as a post featured image.












Word press -  Image Optimization Possibilities


Compressing the images is one of the best ways to increase the website speed. Compression may even further help websites to rank better in search engines at some point.


There are two major ways how this can be done:
* Manually
* Using Plugin
* Programmatically






Nonetheless, it all comes down to your personal preference since all of these methods can be used to compress images whether you have already uploaded them or not. ShortPixel can be used for both approaches and it’s also our favorite tool to compress images since it has Cloudflare integration and many more useful features.














Image Compression using Plugin
This is probably the most straightforward method. The majority of image optimization plugins have similar capabilities, CDN offer, backup, and automation. The only difference is the interface and free plan capacity. In other words, while the interface is more related to the users' personal preference, the number of images that can be optimized differs according to the pricing policy. For a free plan, we can use only a small subset of all images.


These are some of the most popular image compression plugins.


* Smush.
* Optimus.
* EWWW Image Optimizer.
* ShortPixel Image Optimizer.
* Compress JPEG and PNG Images.
* Imsanity.
* Imagify.
* reSmush.it.
Manually Compress images


The manual method of image optimization should be a regular practice when creating content as images are also an important part of SEO. The detailed process is explained in the section above. 
Below is a list of some online compression tools:
* JPEG Optimizer
* Kraken
* Tiny PNG
* Jpeg.Io
* ImageRecycle
* Compressor.io
* EzGif




Programmatically Image Compression


WordPress CMS is storing images in a media library. Images are located in the uploads folder and organized in the folders by year and month. Depending on the programming language used, this can be done in a variety of ways.


The idea is to browse through each folder and convert and optimize images at the same time. If we want to convert images to .webp format, a set of new optimized images should be added in those folders with the same name but a different extension (.webp).
Old images should be kept in case they are indexed in the Google database.


We have two possible scenarios here:
1. We want to optimize images without changing the format
2. We want to replace the existing image with the WebP image.




In case we want to perform only optimization, we are going to process existing images replacing them with those that are optimized. This is a straightforward process.


If we want not only to optimize images but to change the type, we need to browse the folders and create .webp images using old ones. The second step is to make changes in two tables (wp_posts, and wp_postmeta). We need to make a query to replace the existing image format with the new one for each post. An example is created according to this post.




  



  





















The final result should be something like this:
  



That’s all that it takes, now all images are served in a new .webp format optimized to 65%.


 




Special Case - Programmatical Image Compression
However, some of the uploaded images are quite big in size. To fix this, we need to identify the image size (Python, screaming frog). Then we need to change the image size manually. This is a special case and the number of big images should be quite reasonable smaller.