﻿Initial Audit - Content part
Necessary tools
The audit process
Step 1 - Content penalties/demotions
Step 2 - Commercial content
Betting tips/previews
Betting/operator overview
Operator/slot review
Bonuses/Offers
Bonus/Free bets overview
Live streaming
Live results
Step 3 - Hacked content test
Step 4 - Externally duplicated content
Overall rating
Soft Audit - Content
Necessary tools
The audit process
Step 1 - Mobile experience
Step 2 - Internal content duplication
Step 3 - Content depth
Betting tips/previews
Betting/Operator overview
Operator/Slot review
Bonus/Offer
Knowledge/Guide
Live streaming
Bonus/Free bets overview
Misc. (News et.al.)
Overall rating
Initial Audit - Content part 
Necessary tools
The first thing you should look at is whether you have Google Search Console access. You’ll also need to have SEMrush for the analysis of the content part to see whether a certain website had content/penalties demotions.
The next tool you’ll need is MAJESTIC to see if there are any suspicious anchor texts and referring domains. COPYSCAPE will help you to realize whether the site copies big content elements from other websites.
Apart from the tools, you’ll also need to use Google Search to manually audit some things such as the part examining the commercial content on the website and what type of commercial pages are present.
The audit process
Step 1 - Content penalties/demotions


The point of this first step is to see whether a particular website has received some penalties from Google related to the content.


If you have Google Search Console access, you’ll need to check manual content actions.


  

This is the way the rating should be given based on the result of the check:
* Active content action, the site is de-indexed in Google: 3
* Active content action, the site is still indexed: 2
* No active content action, but we have info that the site has been historically penalized: 1
* No historical or current action: 0
What we’ll also need to check after checking the manual action report is whether there are any dramatic traffic drops in SEMrush correlated closely with Panda or other content-oriented algorithm updates by Google.
Go to the Organic Search tab and place the domain in the search bar of SEMrush and select the suggestion that has Domain Overview written next to it.
  

Select the data for All Time to be able to get a better understanding of the domain's traffic and organic ranking development. Of course, if you want to take a look at a certain suspicious period later on, you can narrow down the period. 
You should take a look at the Organic Keywords Trend report because this report shows you a timeline of all the Google updates and ultimately you’ll be able to see whether a certain website was hit by a particular Google update or not. If the rankings are hit at a certain period and we see that there was a Google update related to the content at that time, there will be a suspicion that the content part of the website caused the drop in rankings.
  

If you click on a particular Google logo, you’ll see the following info:
  

This is the way the rating should be given based on the result of the check:
* Yes, big, recent drop: 3
* Yes: small, recent drop: 2
* Yes: small to big drop, now recovered: 1
* No: 0
Here are brief explanations of some content algorithm updates:
* Panda
           Date: February 24, 2011
Hazards: Duplicate, plagiarised, or thin content; user-generated spam; keyword stuffing.


* Hummingbird
           Date: August 22, 2013
Hazards: Keyword stuffing; low-quality content.


* RankBrain
           Date: October 26, 2015
           Hazards: Lack of query-specific relevance; shallow content; poor UX.


* Bert
           Date: October 22, 2019
           Hazards: Poorly written content; lack of focus; lack of context.


* Google’s Helpful Content update
           Date: August 25, 2022
Hazards: Creating content for search engines instead of focusing on people-first content.


Step 2 - Commercial content


The second part of the content audit should be focused on determining whether a website has any commercial content.
We’ll need to check whether a website has six types of pages: betting tips/previews, betting/operator overview, operator/slot review, bonuses/offers, bonus/free bets overview, and live streaming.
Betting tips/previews


Betting tips/previews relate to the page on the website that covers match preview topics.
Example 
            How to recognize this type of page?
You can recognize this page if you see a page that contains a lot of tips. For example, a page template like this one:
  



Or we can take a look at whether a website has some tips. Usually, the tips pages consist of:
* the basic information about the two teams playing a match, 
* where to watch the match, 
* what happened in the previous matches, 
* the recent performance of both teams, 
* the lineup of matches, 
* stats for both teams and 
* Usually, we can see the tables with bookmakers and what their predictions are for particular matches. 
Betting/operator overview
Betting/operator overview relates to the hub page containing all the operators and what their offer is.
Example
            How to recognize this type of page?
You can recognize this page if you see a page that contains the table with all bookmakers typically named ‘The best bookmakers’ or something similar. This table will contain the basic information about all bookmakers, such as the name of the bookmaker, the bonus type, the CTA button, and a link leading to the operator review page.
This is a typical example:
  



Also, this page should be covering the following type of content:
* the general information about all bookmakers on that website, 
* what are they offering in the particular country, 
* what are the new bookmakers in the country, 
* do you need a license to play with a certain bookmaker and so on.
Operator/slot review
Operator/slot review relates to the one particular page that contains one bookmaker and the review of it. Or it can relate to the page that contains a slot review.
Example
            How to recognize this type of page?
This type of page typically focuses on one operator/bookmaker and it can be recognized if you notice a table related to the features of one particular bookmaker. This table usually covers the following information: the CTA button, what is the bonus code, is there an app, is there a live betting function, is there the bookmaker’s website, and some similar information. 
The table might look something like this:


  



Also, you can see for example the pros and cons part of the review. 
  

How to sign up - step-by-step guides are usually part of the bookmaker review page.
A lot of CTA buttons are characteristic of this type of page.
Other important information related to one bookmaker such as the review of the website, the app, casino, and poker features, bonuses, and so on. 
Bonuses/Offers
Bonuses/Offers relate to the one particular page that contains one bookmaker and the bonus offer/review of it.
Example
            How to recognize this type of page?
This type of page usually covers the following information: 
* what is the bonus code, 
* how to get the bonus code, 
* how to sign–up to get the bonus code,
*  how to use the bonus, 
* is there a welcome bonus, etc.


Bonus/Free bets overview
Bonus/Free bets overview relates to the hub page where we have all the bookmakers listed and the bonus offers for all of them. 
Example
            How to recognize this type of page?
This type of page usually consists of a huge table covering the bonus details for all bookmakers, such as the following one:
  

Or we can see the information related to:
*  which are the top bookmakers with bonuses, 
* the process of getting a welcome bonus, 
* how do online gambling welcome bonuses work, 
* how to take advantage of the bookmaker bonus, 
* terms and conditions, 
* FAQs…
Live streaming
Live streaming relates to the page on the website where we can see whether bookmakers we are cooperating with have an offer of a live streaming function or info where people can watch certain matches.
Example
            How to recognize this type of page? 
This type of page offers live streaming or it can contain info where people can watch the matches they are interested in. An example of this is a page from HLTV and this type of page might look like this:
  

Live results
The Live results type of page may be also a good one to search for. Here users can track the results of the football teams. 
Example


            How to recognize this type of page?
This type of page typically consists of tables with the lineups of future matches. It typically has matches for all the leagues and many sports.


Note: this type of page will be a rare one since it is very difficult and expensive to have live streaming on your website. So, I don’t think this page should be the one to contribute to the negative rating. 
This is the way the rating should be given based on the result of the check:
* 3: High performance impact
* 2: Medium performance impact
* 1: Low performance impact
* 0: No problems/check passed
This is individual from website to website based on its type. The types of pages do not bear the same level of importance. E.g., to a betting type of website, live streaming pages might not be as important while to the iGaming website they are probably the most commercial pages. 
Rating 0: No problems/check passed would be in case when all page types are present on the website or even in case one is missing, depending of the page type importance in sense of their commercial character.
Rating 1: Low performance impact would be the case where one or two of page types are not present.
Ranking 2: Medium performance impact would be the case three page types are not present.
Ranking 3: High performance impact would be if four or more of page types are not present.
.
Step 3 - Hacked content test


This part of the audit serves as a way for us to determine whether the website is having hacked content.


To check this, we’ll need to use Google search. 


Perform a "site:" search with the following keywords:
-intitle:[site tail]
* sms
* porn
* sex
* xxx
* Loan


Also, you can check the situation for the whole website and do the following type of search:
e.g. site:goal.pl “loan”


After checking this, we’ll have to take a look at Majestic to see whether there are any suspicious anchor texts and referring domains.


Go to the Site explorer tab and then click on the Anchor text button.


  



We’ll be able to see what the most common anchor texts visualized are.
  



Below this interesting graph with the most common anchor texts, we’ll be able to see the report containing the anchor text and some metrics related to this particular anchor text, such as primary topical trust flow, referring domains, external links (total, deleted, nofollow), and the trust flow and citation flow. We can also export the data and search for the suspicious keywords that we mentioned previously. 


  



After checking the anchor texts, we need to check the most referred domains to see whether there are any suspicious-looking pages on the list. 


Click on the Site Explorer tab and after that click on the Ref domains button. 
  



We can export the data because this report is very detailed.
It shows us the domains referring to the particular website plus the topical trust flow, the number of linking URLs, the external domains, and the trust flow and citation flow metrics.
The Topical Trust Flow metric will be helpful a lot because it determines the niche or the industry that a website linking to us appears in. If the niche is suspicious, we should examine the domain in detail.


  



This is the way the rating should be given based on the result of the check:
* 3: High performance impact
* 2: Medium performance impact
* 1: Low performance impact
* 0: No problems/check passed
Rating 3: High performance impact would be the case when the website is ranking for any of the words from the list, has adult content or any other related negative content from the list, or has spammy anchors.
Rating 2: Medium performance impact would be the case where the website has a significant number of bad anchors, especially if they are among top ones, the website had adult content or any related from the list but now it is deleted, or had rankings for keywords from the list.
Rating 1: Low performance impact would be the case when the website has a few bad anchors.
Rating 0: No problems/check passed would be the case when the website is not ranking for any of the words from the list, does not have adult content or any other related negative content from the list, or any spammy anchors.
Have in mind that word from the list “sex” is in some languages and markets referring to the gender and that is not a problem. But if it is in relation to the adult content that is a big problem. Also always check words from the list on the english version and translated onto the language related to the market you are doing the audit for.


Step 4 - Externally duplicated content
This step will help us realize whether the site copies big content elements from other websites.


The first step is to check the following page types in Copyscape:
* Betting tips/previews
* Betting operator/overview
* Operator/slot review
* Bonuses/offers
* Bonus/Free bets overview
* Live streaming
* Knowledge/Guide
* Miscellaneous (News et.al.)


It was mentioned previously what these page types refer to exactly. However, here we have two more page types to take a look at in this part of the audit:


* Knowledge guide -  betting education articles. 


E.g. We have a knowledge guide section on BettingExpert - https://www.bettingexpert.com/academy which may serve as an example.


This section typically focuses on:
*  betting fundamentals, 
* bet types, 
* how bookmakers make money, 
* betting on sports, 
* what are the basics for successful betting, 
* sports betting systems/strategies and so on.


* Miscellaneous (News et.al.) - news articles.
E.g. We have a news section also on BettingExpert - https://www.bettingexpert.com/news so this might as well serve as an example.
This section typically focuses on sports news, betting news, and so on. 
The point of this section is to gain some news topical authority and for some of our websites, it also helps us to appear in the Top Stories and gain Google news traffic. 


Of course, it might happen that a website does not have all page types, but we’ll need to check those that already exist.
 
Go to Copyscape and enter a particular URL here - from each section one or two:
  

Copyscape will show you the top 10 results and this sample is enough for you to see whether we have copies of certain content on the web.
  

You’ll see a lot of pages listed. However, note that those might be pages not covering the same type of content, but just repeating the same pattern of words and style of writing. So, we’ll need to dive deeper into this. 
You should click on each page to get the following report:


  



The ‘’copied’’ text will be highlighted.


The other option we can test out here is to see intext situation on the SERPs.
So, we should copy a certain part of our page and type, for example:


intext: På denne side giver vi dig overblikket over de bedste bookmakere i Danmark. Hvilke bookmakere er der tale om, og hvorfor er netop disse bookmakere særligt gode for dig som spiller? 


By doing this type of search we’ll be able to see whether some other websites have this type of content and whether a website we are analyzing is copying the content from others. 


Or we can use allintext: option. This option will help us to identify results containing all of the specified words somewhere on the page. 
Overall rating
This is the way the rating should be given based on the result of the check:
* 3: High performance impact
* 2: Medium performance impact
* 1: Low performance impact
* 0: No problems/check passed
Note: After going through these 4 steps and checking all these things in detail, you should write a summary dealing with what you have seen and what you’ve seen as good or bad for this part of the audit. Were there any content penalties? If there were some penalties, is the website recovered now? Are there commercial pages present on the website? Is the website affected by some hacked content? Is the website copying the content from other websites on the internet?
Soft Audit - Content
Necessary tools
The tools you’ll need for this part of the M&A audit are ScreamingFrog and Siteliner. ScreamingFrog will help you to see the word count for every page you should take a look at.
Siteliner will help you discover if there is any internal content duplication - among the pages of the website.
The audit process
Step 1 - Mobile experience
First of all, we’ll have to see how a website looks from a mobile perspective since almost all websites today have more mobile than desktop users.


To do that, we should first use our mobile phone to see if the mobile experience is okay for our future users.
So, take a look at all page templates that exist on a website and check the following things:


- Is all content on desktop displayed on mobile?
- Design glitches - is the design appropriate for mobile?
- Mobile menu functionality - is the menu functioning properly?
- Click a CTA to see how it works and where it is leading the users.


Also, we can run a couple of URLs (we can use some pages with the same patterns) through the mobile-friendly test tool.


First of all, we need to copy a particular URL and place it here:
  



And you’ll get the following results:
  



Also, you’ll need to grade each page type mobile performance in the following manner:
* Satisfying
* Unsatisfying 
* N/A if there is not a certain page type present on the website
This is the way the overall rating for this first step should be given based on the result of the check:
* 3: High performance
* 2: Medium performance
* 1: Low performance
* 0: Critical
Step 2 - Internal content duplication
This check should be performed in order for us to discover what's the highest measured content duplication rate among the most common page types. 




Siteliner has a separate duplicate content report. 
It gives an easy-to-see view that shows you which pages have a match percentage, and which pages match other pages.
You should enter the URL of the domain here:
  

And then after the analysis is done, you should scroll down until you see this report:
  

Then you should click where it is written click here and you’ll get the following report:
  





If you want to see duplicate content highlighted, you should click on the particular URL in the Siteliner’s duplicate content report:  


And then you’ll get the following report:
  

Those highlighted words can be considered duplicate content and on the right side, you’ll get a couple of URLs with the same content.


Note: you should take into consideration that sometimes it will not exactly be duplicate content. Instead, you’ll be given pages that contain the same content in sidebars or navigation.


Note no.2: In the template, it is stated that we should use Copyscape for this internal content duplication analysis. However, Copyscape can be helpful for external duplication analysis, so we should remove this tool from the template in the future.


How do you grade content duplication issues?


This match percentage will be helpful for you to grade the content duplication issues in the template. 
  



Note: You should count and use the average percentage based on the percentage you got for several pages. The percentage should be entered here in the template:
  



This is the way the overall rating should be given based on the result of the check:
* 3: High performance
* 2: Medium performance
* 1: Low performance
* 0: Critical
Step 3 - Content depth 
This part of the audit will help you to realise what is the content depth of the page types. Content depth is an arbitrary score or rating of how comprehensive the coverage of a specific topic is within a piece of content.


You should take a look at the following page types:
* Betting tips/previews - relates to the page on the website that covers match preview topics.
* Betting/Operator overview - relates to the hub page containing all the operators and what their offer is.
* Operator/Slot review - relates to the one particular page that contains one bookmaker and the review of it. Or it can relate to the page that contains a slot review.
* Bonus/Offer - relates to the one particular page that contains one bookmaker and the bonus offer/review of it.
* Knowledge/Guide - betting education articles.
* Live streaming - relates to the page on the website where we can see whether bookmakers we are cooperating with have an offer of a live streaming function or info where people can watch certain matches.
* Bonus/Free bets overview - relates to the hub page containing all bookmakers and their bonus offers.
* Misc. (News et.al.) - news articles.


What are the necessary elements for all page types?
 The elements that a certain page type should consist of are marked in red color.
Betting tips/previews
-Min. 500 words
The number of words can be checked in the SEO Minion extension or through ScreamingFrog.


If you decide to use SEO Minion, you should first go to the Analyze on-page SEO tab:
  

Then you will get the URL report and you will get both the number of words and the number of characters. The number of words is what matters in this case.
  



If you decide to use Screaming frog you will see the Word count in the internal tab.
  

 –Match preview analysis - the general analysis of what is expected from the match
 –Match info - where does it take place, who is playing, what are the expectations
 –Team/match stats - what happened the last time the teams played a match, what is the general statistics
 – Betting odds CTAs - CTAs for various bookmakers and odds




Betting/Operator overview
           – Intro text
Usually, a short text about what you can read on the page and what the page is about in general.
           –Operator comparison table with a comprehensive list of operators
  

           –Operator key data in the table
As you can see in the picture above, there are key data for each operator, such as what is the bonus, CTAs, and a link leading to the review page.
           –Star rating
           –Go to operator + read review CTAs


Operator/Slot review
–Min. 2000 words
You can check the word count in the same manner as we’ve mentioned previously - by using the SEO Minion extension or ScreamingFrog.
(–Slot play preview)
–Text review
–Star rating
–Welcome offer
             What is the welcome offer and how to get it?
–Deposit/withdrawal methods
How to deposit money, how long it may take, what are the options for getting the money paid…
–Mobile-friendliness
Does this bookmaker have an app? Is it mobile-friendly? How does it work?
–Live gambling options
Is live betting available? Which sports are available for live betting?
–Support
Customer service and telephone. Basic information about how to contact a certain bookmaker.
–Sports/casino markets
Are there casino or poker features available? What is the casino bonus? What is the poker bonus? What types of games are available for the users?
–Sign-up guide
How to sign up, step-by-step instructions with screenshots from the bookmaker's website.
–Go to operator CTA
Bonus/Offer
–Min. 1200 words
–Bonus code
The bonus code should be placed somewhere above the fold or near it for our users to receive what they need immediately.
–Offer details
This relates to the bonus code details - how does the bonus code work for a certain bookmaker, what is the minimum deposit, is there a sign-up bonus, how to get the welcome bonus online, and so on.
–Compliance info
Terms and conditions information.
–Table: multiple bonuses
Table with different types of bonuses a certain bookmaker offers.
–Sign-up/bonus claiming guide
Step-by-step instructions on how to sign-up and how to get the sign-up bonus.
–Claim bonus CTA
Knowledge/Guide
–Strategy w/ clear examples
We have similar sections on BettingExpert and Sites-de-Apostas.
This section needs to be explained and the strategy behind it needs to be clear - what will you learn, why is betting education important, who wrote this, the idea behind this, how to avoid mistakes, and so on. 
These two pages serve as examples of well-structured betting guide sections - the BettingExpert section and the Sites-de-Apostas section.
–How-to guide
This section also needs to have various how-to guides for betting. 
For example, this page has one part that explains step-by-step how to choose the bookmaker.  Or here is an example of the page on how to bet on football. This page consists of various chapters dealing with how to bet on various leagues.
So, there are many possibilities for how-to guides. 
–Security/risks
This is also an important part of this section since it deals with betting education articles. So, this type of content should focus on what are the potential risks of being involved in the betting world, responsible gaming, how not to become addicted, and so on.
This article serves as a nice example.


Live streaming
–Match/league info
General information about the occurring match.
–Guide & cost for live streaming
–On-page live-streaming video option
The users should either be given a live streaming video to take a look at or they should be given a CTA leading to the live stream.
–Sign-up/stream CTA


Bonus/Free bets overview
–Table: Multiple & different types of offers
  

This is an example of the table. What are different bookmakers offering in terms of bonuses plus CTAs for each of them and a link leading to the review pages?
–Offer amounts
–Offer limits & compliance info
What are the terms and conditions and are there any bonus offer limits present for certain bookmakers?
–Operator
–Claim offer CTA




Misc. (News et.al.)
When it comes to news content, there are no clear specifications in the template.


I would say that you should look at the navigation bar and see whether a website has a news section and if it has, you should take a look at what type of news it has and whether it can be expanded in the future.


You should grade each page’s content and elements in the following way:
* Satisfying 
* Mediocre
* Unsatisfying 
* N/A
Overall rating
This is the way the overall rating should be given based on the result of the check:
* 3: High performance
* 2: Medium performance
* 1: Low performance
* 0: Critical


Based on everything that you’ve seen, write a summary dealing with what you have seen as good and bad regarding the content on a website. Do you see anything suspicious? Does the website have some commercial pages, if it does, are they enough and relevant for our niche? Does the website have some internal content duplication? If it has, do you think it will be easy to get rid of it? Is the website suitable for mobile users? And many other things.