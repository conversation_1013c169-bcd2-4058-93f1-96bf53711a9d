﻿Page Template TIPS PAGE


* Meta Title
   * max 60 characters
   * main keyword - the name of the match
   * date and time
* Meta Description
   * should be around 160 characters (min 50 & max 160 characters)
   * should be unique for each page and summarises the page content 
   * main keyword - the name of the match (team a vs. team b)
   * call to action keywords 
* Content
      * ABOVE THE FOLD
   * Logo - the unique logo of the site
   * Navigation bar - containing all the most important pages of the site: 
      * Bookmakers, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Blog, Live stream, Discussion/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc. The navigation bar doesn’t have to contain all these pages. It depends from site to site. 
   * <H1>
      * No character limitations but try to keep it visually appealing, so make it a manageable length. Make h1 clear - the typical name is the name of the match
         * Other recommendations regarding headings structure:
      * Headings should describe the content that follows them. It is a good practice to have headings in the form of questions. The first <p> below will then answer that question. This type of page sometimes does not have a complex heading structure
      * Only use one <h1> for each web page.
      * Don’t skip heading levels. For example, using a <h2> followed by an <h4>. It’s important that your headings are logical and hierarchical. This means that <h2> always follows <h1>.
      * Make sure each heading is unique on individual web pages
      * Make headings clear, useful and relevant.
   * Bookmaker Logo 
   * Date - should be updated accordingly. Having the author's name on the page is also good practice (EAT). With the new development of E-E-A-T, authors will become a must-have virtually for all pages. These are time-sensitive pages so date and time must be displayed. 
   * Author / Tipster
   * Short introduction- bullet point list or table - summary review
      * All tables must be created in <table> tags hardcoded in HTML, and not in <div> or any other tag.
      * Provide a brief overview of the topic and why it's important for the reader.
      * State the purpose of the tips page and what readers can expect to learn.
   * CTA 
      * BELOW THE FOLD
Any widget/block/image below the fold should be lazyloaded.
   * Statistic - Incorporate relevant statistics, research, or data to reinforce the tips.
   * Tip Descriptions:
* Present each tip in a separate section or bullet point.
* Start with a brief statement introducing the tip.
* Provide detailed information, step-by-step instructions, or actionable advice for each tip.
* Use concise language and bullet points for easy scanning.
* Include relevant keywords naturally within the tip descriptions.
   * External References: Link to authoritative and reputable external sources that support your tips. Provide credit and additional context for information that you reference. Social icons/ social cards (Twitter cards, etc.) - optional
   * Visuals -  Use images, diagrams, or infographics to visually enhance your tips. - optional
      * Video - optional
   * Table with the upcoming matches 
   * Other offers: WC bonus/ Free spins/ Casino bonus/ Welcome bonus/ Promo code  ⇒ if the bookmaker has them
* Schema 
   * Breadcrumbs 
   * Event
   * Article
* Images 
   * Ensure the ALT text is optimised
   * Use only high-resolution images. The recommendation is to use images of the people. 
   * Compress images to load fast
   * Every image below the fold should be lazyloaded
   * Optimise Your logo