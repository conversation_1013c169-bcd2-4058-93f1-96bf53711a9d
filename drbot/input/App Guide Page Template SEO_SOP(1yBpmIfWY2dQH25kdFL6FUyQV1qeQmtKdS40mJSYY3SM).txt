﻿Operator Review Page: App Guide Page Template SEO_SOP
App Guide Page Figma Design File for Reference: https://www.figma.com/file/OZj8FOgTGnfY3QutZmG0UN/Page-Templates%3A-Operator-Review-Page?type=design&node-id=719-2383&mode=design&t=AaljOq8XSoGjndfM-0 






OPERATOR REVIEW PAGE: APP GUIDE 
NOTE: The elements should be adjusted based on a particular market. 
NOTE 2: The design should be adjusted to support both desktop and mobile views without any major visible changes in the design.
NOTE 3: There is a chance of potential cannibalization between this type of page and How To pages. Please be mindful of this and try to avoid having duplicate content on these different page types. For example "how to download a [bookmaker] app" should only be a topic for one of these two page templates on the same site.


Title tag (Meta Title)
* Maximum characters - 60.
* Main keyword - <operator name> + Sports Betting App (example). The main keyword could be any variation of this as long as it matches the intent and the market’s requirements (i.e. competitors’ choice of keywords).
* If possible, use additional keywords that are operator-related (e.g. review, bonus, free bet, free download, etc.) depending on what the page offers aside from the app-related information.
* Avoid using special characters. If used - before implementing the emojis, research the market (to see whether the competitors are using them and whether it would make a difference). 
* If applicable, our brand name can be incorporated to increase brand recognition and trust.
* If possible, perform research for whether the competitors had their meta titles changed by Google on SERP, to see what type of title Google “likes”.


Meta Description
* Minimum & maximum characters - min 50 & max 160.
* Should be unique for each page.
* It is a good practice that it should include the main keyword.
* Should summarize the page’s content.
* Should provide an accurate preview of the content to prevent bounce rates.
* Avoid the use of special characters. If used - before implementing the emojis, research the market. 
* Can include call-to-action keywords (e.g. Visit <operator>, Download <operator> app, Read Our Review, etc.)
* If applicable, our brand name can be incorporated to increase brand recognition and trust.
* If possible, perform research for whether the competitors had their meta descriptions changed by Google on SERP, to see what type of description Google “likes”.


Content 
* Above the fold:
* Navigation Bar - containing all the most important pages of the site (e.g. Operators, Tips, Predictions, Bonuses, Casino, Analysis, News, Events, Apps, Blog, Live stream, Discussion/Forum/Chat, Awards/Prizes, Guides/Tutorials, etc.). The navigation bar doesn’t have to contain all these pages; it depends on the demands of the website.
* Operator Logo -  the unique logo of the operator.
* Rating - mark it with the review schema. Despite not being able to get a rich snippet out of this, Google uses this markup to understand the content of the page better and also uses this information to enhance search listings.
* Breadcrumbs -  best practice. It helps search engines better understand the hierarchy of the pages, helps with user experience, and maximizes the potential of sitelinks appearing on the SERPs. Putting the schema on the breadcrumbs is a must.
* <h1> - there must be only one <h1> per page. No character limitations but try to keep a manageable length so it appears visually appealing. Make <h1> stand out. Make the letters more significant than the rest of the text and headings. Can be in the form of a question. 
* Author name - this is a good practice. Helps with E-E-A-T. Add a link to the author’s hubpage if there is one.
* Date - current date/month/year. Update regularly. This is good to have on review pages for good user experience, featured snippets, and rich results. Mark it up with the schema.
* First CTA - display of the main offer/most important thing that users get from us, e.g. Claim bonus/Collect bonus, Get <operator> Welcome Offer, etc. (depending on what the operator offers).
* Optional disclaimer about Safe Gambling, (18+ | Play responsibly), and Terms & Conditions - this is optional since this is market-dependent and is different for every country.
* Short introductory text - Provide a brief overview of the topic and why it's important for the reader. 
* Benefits/Unique selling points table - A well-structured, small, and simple HTML table that can potentially be featured as a rich snippet, increasing the visibility of the page in search results.
* Table of contents - A list or a dropdown list with all or most important <h2> headings of the page. The jumplinks (anchor links) help with user experience. If possible, make it foldable to save space. This feature also helps to gain sitelinks in the SERP which takes up more space in result pages and gives more exposure. This table can be included in the sidebar in the desktop view to save space. Make sure to have a table of contents in mobile view as well.
* Pros & Cons table - A well-structured, small, and simple HTML comparison table with the operator’s pros and cons. Mark it in the schema as positiveNotes/negativeNotes properties. This table can aslo be included in the sidebar in the desktop view to save space.
* Make sure that all these components are somehow visible in mobile view as well, since the mobile-first principle is nowadays very important for SEO.


* Below the fold:
* Step-by-step app installation process/ How to download - Include screenshots from the operator’s interface. Screenshot images from the operator interface increase our credibility and prove that we actually have experience with the product. This can be done in the form of a carousel to save space. This is a good UX component that displays content in a slideshow-like manner. Carousels can "autoplay" or be navigated manually by users. These can be done in HTML where child elements (images) are placed inside the parent <div> element. Add a how-to schema for this.
* If perhaps a video showing how to download the app is used instead of the screenshots, make sure to add a VideoObject schema markup for it.
* After displaying the most important thing that users get from us/the main offer of the site, the page should contain some of the important offers that the operator has, along with some more CTA buttons.
* Any widget/block/image below the fold should be lazyloaded.
* Don’t skip heading levels (for example, using a <h2> followed by an <h4>). It’s important that your headings are logical and hierarchical. This means that <h2> always follows <h1>.
* Headings (h2, h3) should describe the content that follows them. It is a good practice to have headings in the form of questions so that the first <p> (paragraph) below will then answer that question.
* System Requirements - Minimum and recommended specifications needed to run the operator’s app - can be displayed in the form of a simple HTML table. These can be marked up with https://schema.org/requirements schema.
* Frequently asked questions - It is a good practice to have this section towards the end of the page. Despite not being able to get a rich snippet out of this, add it because it helps Google discern what the page is about (i.e. its content) and it is very good for user experience. The abovementioned <h2> and <h2> headers that are in the form of a question can be used as questions and a brief summary of paragraphs as the answers. Include FAQ in the schema.
* Related articles/Latest news - add a list of our other articles about the same operator or a list of the latest operator’s news if there are any, towards the end of the page. This is a good opportunity for interlinking and helps with user experience. Another option is to have this list in the sidebar in the desktop view.
* Footer - A good practice is that the footer contains: the logo of the site - that is our brand, so people should recognize it (optimize your logo, and use logo schema); Contact Us link (user-friendly and helps with E-E-A-T); linked social icons (Facebook, Instagram, Twitter, etc.) - if the site has them; disclaimer about safe gambling; About Us. Do not stuff the footer with too many links, it all depends on the demands of the website.


Schema


* Review schema (https://schema.org/Review) should include: 
* author property (https://schema.org/author), 
* Rating schema (https://schema.org/Rating),
* positiveNotes/negativeNotes properties for pros&cons section (https://schema.org/positiveNotes and https://schema.org/negativeNotes), 
* VideoObject (https://schema.org/VideoObject),
* requirements property (https://schema.org/requirements). 


* WebPage (https://schema.org/WebPage) should include: 
* WebWite schema (https://schema.org/WebSite), 
* BreadcrumbsList (https://schema.org/BreadcrumbList), 
* logo property (https://schema.org/logo),  
* Organization (https://schema.org/Organization),
*  Date (https://schema.org/Date). 


* FAQ document containing multiple question/answer schema properties (https://schema.org/Question and https://schema.org/Answer) OR HowTo schema (https://schema.org/HowTo). A HowTo schema is a good practice, and by implementing it, a step-by-step instruction is made more appealing and informative in search results, potentially increasing click-through rates and the visibility of content. It’s an effective way to enhance the presentation of the how-to article on the SERP. 
NOTE: However, if a HowTo schema is added then an FAQ schema should not be implemented. Having both creates a risk that neither gets picked up and neither performs on the SERP. Therefore, one out of these two schemas should be used.




Images
* Avoid .png images, and only use progressive image types such as .webp, and if that is not possible then .jpg is ok. 
* Use appropriate titles and file names of the images themselves.
* Ensure the ALT text is optimized.
* Use only high-resolution images. 
* Compress images accordingly to load fast.
* Every image below the fold should be lazyloaded.
* Serve responsive images.
* Serve images with correct dimensions.
* In the case of an image carousel - wrap each of these images in a <div> element with the .carousel-item class so the browser knows they belong in the carousel (potentially helpful step-by-step explanation for how to do this can be found on this page How to Create a Carousel Slider for Your Website in Bootstrap CSS).
* SOPs for image optimization:
Optimizing images for best SEO performanceOptimizing images for best SEO performance - part 2


Additional recommendations
* Check Mobile Friendliness - responsive design. Mobile responsive design is crucial for app-related queries; and it is also important in the context of the mobile-first principle.
* Optimise page loading speed.
* Make sure the website has a good structure.
* All tables should be created in <table> tags, and not in <div> or any other tag. The table’s main elements should be hardcoded in the HTML so that Google can read it, and they must be server-side rendered. All the individual data cells within the table should be marked as <td> elements.
* HTML lists should be in the form <ul> (unordered list) or <ol> (ordered list).
* It is important to make the links href ones, not JavaScript-based.




BC sites/pages used for reference
* sites-de-apostas.net (https://www.sites-de-apostas.net/sportingbet-mobile, https://www.sites-de-apostas.net/kto-app, https://www.sites-de-apostas.net/betano-app, https://www.sites-de-apostas.net/betsul-app, https://www.sites-de-apostas.net/betmais-app, https://www.sites-de-apostas.net/22bet-app);


* goal.pl (https://www.goal.pl/bukmacherzy/sts/aplikacja/, https://www.goal.pl/bukmacherzy/superbet/aplikacja/, https://www.goal.pl/bukmacherzy/pzbuk/aplikacja/, https://www.goal.pl/bukmacherzy/fortuna/aplikacja/, https://www.goal.pl/bukmacherzy/forbet/aplikacja/, https://www.goal.pl/bukmacherzy/betclic/aplikacja/);


* wettfreunde.net (https://www.wettfreunde.net/happybet-app/, https://www.wettfreunde.net/bet365-app/, https://www.wettfreunde.net/bet3000-app/, https://www.wettfreunde.net/bet-at-home-app/).