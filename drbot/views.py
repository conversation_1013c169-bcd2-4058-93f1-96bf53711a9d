from django.shortcuts import render, redirect
from django.http import <PERSON>sonR<PERSON>ponse
from .drbot_utils.drbot_utils import (
    initialize_client, get_response, get_or_create_vector_store, get_openai_api_key, add_files_to_empty_vector_store, format_sources
)
from .forms import Chat<PERSON><PERSON>

def chat_view(request):
    """Handles user input from the chatbot form and retrieves responses using OpenAI File Search."""
    form = ChatForm()

    # Initialize chat history as a list
    chat_history = []

    if request.method == "POST":
        form = ChatForm(request.POST)

        if form.is_valid():
            user_input = form.cleaned_data["user_input"].strip()

            if not user_input:
                return render(request, "drbot/chat.html", {"form": form, "error": "Message cannot be empty", "chat_history": chat_history})

            api_key = get_openai_api_key()
            client = initialize_client(api_key)

            # Get existing vector store or create a new one
            vector_store_id = get_or_create_vector_store(client)

            # Ensure the vector store is not empty
            add_files_to_empty_vector_store(client, vector_store_id)

            try:
                bot_message, filenames = get_response(client, user_input, vector_store_id)

                # Format the filenames into structured sources
                formatted_sources = format_sources(filenames)

                # Append user message and bot message to chat history
                chat_history.append({'user': user_input, 'bot': bot_message})

            except Exception as e:
                return render(request, "drbot/chat.html", {"form": form, "error": f"OpenAI API error: {str(e)}", "chat_history": chat_history})

            return render(request, "drbot/chat.html", {
                "form": form,
                "user_message": user_input,
                "bot_message": bot_message,
                "sources": formatted_sources,
                "chat_history": chat_history,  # Pass chat history to template
            })

    return render(request, "drbot/chat.html", {"form": form, "chat_history": chat_history})

def update_vector_store(request):
    """Manually updates the vector store with new files."""
    if request.method == "POST":
        client = initialize_client()
        vector_store_id = get_or_create_vector_store(client)

        try:
            add_files_to_empty_vector_store(client, vector_store_id)
            return JsonResponse({"message": "Vector store updated successfully!"})
        except Exception as e:
            return JsonResponse({"error": f"Error updating vector store: {str(e)}"}, status=500)

    return JsonResponse({"error": "Invalid request method"}, status=400)
