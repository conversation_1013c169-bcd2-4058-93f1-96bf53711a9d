from django.db import models
from django.urls import reverse


# Create your models here.

class SitemapCheckerApplication(models.Model):
    app_name = models.CharField(max_length = 255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)


    def __str__(self):
        return self.app_name


    def get_absolute_url(self):
        return reverse('appname', kwargs={
            'id':self.app_name
        })


class SitemapCheckURLs(models.Model):
    url = models.URLField(unique=True)

    def __str__(self):
        return self.url
