import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from django.shortcuts import render
from django.http import JsonResponse
from .forms import SitemapForm
from sitemap_checker.scripts.sitemap_fetch import fetch_sitemap_index
from sitemap_checker.scripts.sitemap_check import check_status_code
from alerts.management.commands.slack_sender import send_slack_message

# Set up logging to a variable instead of a file for now
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] - %(message)s", datefmt="%Y-%m-%d %H:%M:%S")

def check_sitemap_status(request):
    results = []
    notify_user = False  # Flag to determine if we need to send a notification
    slack_handle = getattr(request.user, 'slack_handle', None)  # Get the user's Slack handle
    print(slack_handle)

    if request.method == 'POST':
        form = SitemapForm(request.POST)
        if form.is_valid():
            index_url = form.cleaned_data['sitemap_url']

            # Fetch sitemap URLs
            sitemap_urls = fetch_sitemap_index(index_url)

            if sitemap_urls:
                logging.info(f'Total sitemaps found: {len(sitemap_urls)}')
                logging.info('Checking status codes of individual sitemaps:')

                # Use ThreadPoolExecutor to check status codes in parallel
                with ThreadPoolExecutor(max_workers=8) as executor:
                    future_to_url = {executor.submit(check_status_code, url): url for url in sitemap_urls}
                    for future in as_completed(future_to_url):
                        try:
                            url, status_code = future.result()
                            result = f'{url} - Status code: {status_code}'
                            results.append(result)
                            logging.info(result)

                            # Check if status code is not 200
                            if status_code != 200:
                                notify_user = True  # Set the flag to notify the user
                        except Exception as e:
                            logging.error(f'Error processing URL: {e}')
                            results.append(f'Error processing URL: {e}')
                            notify_user = True  # Set the flag to notify user of processing errors
            else:
                logging.warning(f'No sitemaps found at {index_url}')
                results.append(f'No sitemaps found at {index_url}')
        else:
            results.append("Invalid URL provided.")

        # Format the results into a single message
        message = "Sitemap Status Check Results:\n" + "\n".join(results)

        # Conditionally send a Slack message if any status code isn't 200
        if notify_user and slack_handle:
            send_slack_message(slack_handle, message)

    else:
        form = SitemapForm()

    return render(request, 'sitemap_checker/check_sitemap_status.html', {'form': form, 'results': results})
