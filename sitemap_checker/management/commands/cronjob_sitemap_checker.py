import logging
from urllib.parse import urlparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from django.core.management.base import BaseCommand
from alerts.models import CustomUser
from sitemap_checker.models import SitemapCheckURLs
from sitemap_checker.scripts.sitemap_check import check_status_code
from sitemap_checker.scripts.sitemap_fetch import fetch_sitemap_index
from alerts.management.commands.slack_sender import send_slack_message  # Assuming a Slack sending function

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    # filename='sitemap_status.log',
    # filemode='w'
)

class Command(BaseCommand):
    help = "Check the status code of sitemap URLs and send alerts if the status is non-200."

    def handle(self, *args, **kwargs):
        logging.info("Command is starting...")

        # Retrieve sitemap index URLs from the database
        sitemap_index_urls = SitemapCheckURLs.objects.values_list('url', flat=True)
        if not sitemap_index_urls:
            logging.info("No sitemap index URLs found in SitemapCheckURLs.")
            return

        logging.info(f"Retrieved sitemap index URLs: {list(sitemap_index_urls)}")

        # Retrieve users with alerts and assigned domains
        users_with_alerts_and_domains = CustomUser.objects.filter(
            receive_alerts=True,
            domains__isnull=False
        ).distinct()
        print(users_with_alerts_and_domains)

        # Process each sitemap index URL
        for index_url in sitemap_index_urls:
            sitemap_urls = fetch_sitemap_index(index_url)
            if sitemap_urls:
                logging.info(f"Total sitemaps found in {index_url}: {len(sitemap_urls)}")
                logging.info("Checking status codes of individual sitemaps.")

                # Check each sitemap URL concurrently
                with ThreadPoolExecutor(max_workers=8) as executor:
                    future_to_url = {executor.submit(check_status_code, url): url for url in sitemap_urls}

                    for future in as_completed(future_to_url):
                        url = future_to_url[future]
                        try:
                            # Get the result of the future, which is (URL, status_code)
                            url, status_code = future.result()
                            logging.info(f"{url} - Status code: {status_code}")

                            # Send alert only if the status code is non-200
                            if status_code != 200:
                                url_domain = urlparse(url).netloc
                                logging.info(f"Non-200 status code for domain: {url_domain}")

                                # Filter users by checking if their domain is contained in the URL domain
                                matching_users = [
                                    user for user in users_with_alerts_and_domains
                                    if any(domain.domain in url_domain for domain in user.domains.all())
                                ]
                                print(matching_users)

                                slack_handles = [user.slack_handle for user in matching_users if user.slack_handle]

                                if slack_handles:
                                    message = f"Sitemap URL check alert! {url} returned a status code of {status_code}."
                                    for handle in slack_handles:
                                        send_slack_message(handle, message)
                                        logging.info(f"Sent Slack message to {handle}: {message}")
                                else:
                                    logging.warning(f"No users with alerts for domain in URL: {url_domain}")

                        except Exception as e:
                            logging.error(f"Error processing URL {url}: {e}")
            else:
                logging.warning(f"No sitemaps found in {index_url}")
            logging.info(f"Finished processing sitemap index: {index_url}\n")

        logging.info("Command completed. All sitemap URLs have been processed.")
