this.dpd_components=function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={exports:{},id:r,loaded:!1};return e[r].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.Pipe=void 0;var o=n(1),s=r(o);t.Pipe=s.default},function(e,t,n){(function(e){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function a(){var e=(new Date).getTime();return"undefined"!=typeof performance&&"function"==typeof performance.now&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)})}Object.defineProperty(t,"__esModule",{value:!0});var u=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),c=n(7),l=(r(c),n(5)),p=r(l),d=function(t){function n(e){o(this,n);var t=s(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e));return t.uid=a(),t.add_callback(),t}return i(n,t),u(n,[{key:"add_callback",value:function(){if(!e.dpd_comms){e.dpd_comms={send:function(e){if(this.senders.length>0)for(var t in this.senders)this.senders[t].send(e);else this.messages.push(e)},receive:function(e){for(var t in this.callbacks)this.callbacks[t](e)},add_callback:function(e){dpd_comms.callbacks.push(e)},add_sender:function(e){this.senders.push(e);var t=this.messages;this.messages=[];for(var n in t)this.send(t[n])},callbacks:[],senders:[],messages:[]};for(var t=function(t){e.dpd_comms.receive(t)},n=window;n;)n.dpd_wsb?(n.dpd_wsb.add_callback(t),e.dpd_comms.add_sender(n.dpd_wsb)):(n.dpd_wsb_pre||(n.dpd_wsb_pre={callbacks:[],sender_targets:[]}),n.dpd_wsb_pre.callbacks.push(t),n.dpd_wsb_pre.sender_targets.push(e.dpd_comms)),n=n!=window.parent?window.parent:null}e.dpd_comms.add_callback(this.handleMessage.bind(this)),this.send_info()}},{key:"send_info",value:function(){e.dpd_comms.send({type:"connection_triplet",uid:this.uid,label:this.props.label,channel_name:this.props.channel_name}),setTimeout(this.send_info.bind(this),1e4)}},{key:"handleMessage",value:function(e){e.label==this.props.label&&this.props.setProps&&this.props.setProps({value:e.value})}},{key:"render",value:function(){return null}}]),n}(c.Component);t.default=d,d.propTypes={id:p.default.string,label:p.default.string,channel_name:p.default.string,value:p.default.string,setProps:p.default.func}}).call(t,function(){return this}())},function(e,t){"use strict";function n(e){return function(){return e}}var r=function(){};r.thatReturns=n,r.thatReturnsFalse=n(!1),r.thatReturnsTrue=n(!0),r.thatReturnsNull=n(null),r.thatReturnsThis=function(){return this},r.thatReturnsArgument=function(e){return e},e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r,s,i,a,u){if(o(t),!e){var c;if(void 0===t)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[n,r,s,i,a,u],p=0;c=new Error(t.replace(/%s/g,function(){return l[p++]})),c.name="Invariant Violation"}throw c.framesToPop=1,c}}var o=function(e){};e.exports=r},function(e,t,n){"use strict";var r=n(2),o=n(3),s=n(6);e.exports=function(){function e(e,t,n,r,i,a){a!==s&&o(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t};return n.checkPropTypes=r,n.PropTypes=n,n}},function(e,t,n){e.exports=n(4)()},function(e,t){"use strict";var n="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";e.exports=n},function(e,t){!function(){e.exports=this.React}()}]);