/*! For license information please see dash_bootstrap_components.min.js.LICENSE.txt */
(()=>{var e=[e=>{"use strict";e.exports=window.React},(e,t,n)=>{"use strict";e.exports=n(15)},(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var s=typeof n;if("string"===s||"number"===s)e.push(n);else if(Array.isArray(n)){if(n.length){var o=a.apply(null,n);o&&e.push(o)}}else if("object"===s)if(n.toString===Object.prototype.toString)for(var i in n)r.call(n,i)&&n[i]&&e.push(i);else e.push(n.toString())}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()},e=>{"use strict";e.exports=function(){}},e=>{"use strict";e.exports=function(e,t,n,r,a,s,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,a,s,o,i],u=0;(l=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];function r(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];var a=null;return t.forEach((function(e){if(null==a){var t=e.apply(void 0,n);null!=t&&(a=t)}})),a}return(0,a.default)(r)};var r,a=(r=n(6))&&r.__esModule?r:{default:r};e.exports=t.default},(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(t,n,r,a,s,o){var i=a||"<<anonymous>>",l=o||r;if(null==n[r])return t?new Error("Required "+s+" `"+l+"` was not specified in `"+i+"`."):null;for(var c=arguments.length,u=Array(c>6?c-6:0),d=6;d<c;d++)u[d-6]=arguments[d];return e.apply(void 0,[n,r,i,s,l].concat(u))}var n=t.bind(null,!1);return n.isRequired=t.bind(null,!0),n},e.exports=t.default},(e,t,n)=>{"use strict";var r=n(8);function a(){}function s(){}s.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,s,o){if(o!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:a};return n.PropTypes=n,n}},e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},e=>{"use strict";e.exports=function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return/^[a-z][a-z0-9+.-]*:/.test(e)}},(e,t,n)=>{"use strict";var r=n(11);e.exports=function(e){var t=typeof e;if("string"===t){var n=e;if(0==(e=+e)&&r(n))return!1}else if("number"!==t)return!1;return e-e<1}},e=>{"use strict";e.exports=function(e){for(var t,n=e.length,r=0;r<n;r++)if(((t=e.charCodeAt(r))<9||t>13)&&32!==t&&133!==t&&160!==t&&5760!==t&&6158!==t&&(t<8192||t>8205)&&8232!==t&&8233!==t&&8239!==t&&8287!==t&&8288!==t&&12288!==t&&65279!==t)return!1;return!0}},(e,t,n)=>{var r;window,e.exports=(r=n(0),function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(e,t){e.exports=r},function(e,t,n){"use strict";n.r(t),n.d(t,"asyncDecorator",(function(){return o})),n.d(t,"inheritAsyncDecorator",(function(){return i})),n.d(t,"isReady",(function(){return l})),n.d(t,"History",(function(){return d}));var r=n(0);function a(e,t,n,r,a,s,o){try{var i=e[s](o),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,a)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(r,s){var o=e.apply(t,n);function i(e){a(o,r,s,i,l,"next",e)}function l(e){a(o,r,s,i,l,"throw",e)}i(void 0)}))}}var o=function(e,t){var n,a={isReady:new Promise((function(e){n=e})),get:Object(r.lazy)((function(){return Promise.resolve(t()).then((function(e){return setTimeout(s(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n(!0);case 2:a.isReady=!0;case 3:case"end":return e.stop()}}),e)}))),0),e}))}))};return Object.defineProperty(e,"_dashprivate_isLazyComponentReady",{get:function(){return a.isReady}}),a.get},i=function(e,t){Object.defineProperty(e,"_dashprivate_isLazyComponentReady",{get:function(){return l(t)}})},l=function(e){return e&&e._dashprivate_isLazyComponentReady};function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var u="_dashprivate_historychange",d=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(u))}},{key:"onChange",value:function(e){return window.addEventListener(u,e),function(){return window.removeEventListener(u,e)}}}],null&&c(t.prototype,null),n&&c(t,n),e}()}]))},e=>{var t=function(e){"use strict";var t,n=Object.prototype,r=n.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",o=a.asyncIterator||"@@asyncIterator",i=a.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,s=Object.create(a.prototype),o=new P(r||[]);return s._invoke=function(e,t,n){var r=d;return function(a,s){if(r===f)throw new Error("Generator is already running");if(r===m){if("throw"===a)throw s;return T()}for(n.method=a,n.arg=s;;){var o=n.delegate;if(o){var i=E(o,n);if(i){if(i===g)continue;return i}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===d)throw r=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=f;var l=u(e,t,n);if("normal"===l.type){if(r=n.done?m:p,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r=m,n.method="throw",n.arg=l.arg)}}}(e,n,o),s}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d="suspendedStart",p="suspendedYield",f="executing",m="completed",g={};function b(){}function y(){}function h(){}var v={};l(v,s,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(C([])));x&&x!==n&&r.call(x,s)&&(v=x);var O=h.prototype=b.prototype=Object.create(v);function w(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function n(a,s,o,i){var l=u(e[a],e,s);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==typeof d&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,o,i)}),(function(e){n("throw",e,o,i)})):t.resolve(d).then((function(e){c.value=e,o(c)}),(function(e){return n("throw",e,o,i)}))}i(l.arg)}var a;this._invoke=function(e,r){function s(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(s,s):s()}}function E(e,n){var r=e.iterator[n.method];if(r===t){if(n.delegate=null,"throw"===n.method){if(e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method))return g;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var a=u(r,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,g;var s=a.arg;return s?s.done?(n[e.resultName]=s.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function C(e){if(e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}return{next:T}}function T(){return{value:t,done:!0}}return y.prototype=h,l(O,"constructor",h),l(h,"constructor",y),y.displayName=l(h,i,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,l(e,i,"GeneratorFunction")),e.prototype=Object.create(O),e},e.awrap=function(e){return{__await:e}},w(N.prototype),l(N.prototype,o,(function(){return this})),e.AsyncIterator=N,e.async=function(t,n,r,a,s){void 0===s&&(s=Promise);var o=new N(c(t,n,r,a),s);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},w(O),l(O,i,"Generator"),l(O,s,(function(){return this})),l(O,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=C,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return i.type="throw",i.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var s=this.tryEntries.length-1;s>=0;--s){var o=this.tryEntries[s],i=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var s=a;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var o=s?s.completion:{};return o.type=e,o.arg=t,s?(this.method="next",this.next=s.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;k(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:C(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}(e.exports);try{regeneratorRuntime=t}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}},,(e,t,n)=>{"use strict";var r=n(0),a=60103;if(t.Fragment=60107,"function"==typeof Symbol&&Symbol.for){var s=Symbol.for;a=s("react.element"),t.Fragment=s("react.fragment")}var o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i=Object.prototype.hasOwnProperty,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,s={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!l.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===s[r]&&(s[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:s,_owner:o.current}}t.jsx=c,t.jsxs=c},(e,t,n)=>{e.exports=n(7)()},(e,t,n)=>{e.exports=n(13)}],t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";function e(){return e=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.apply(this,arguments)}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function a(e,n){if(e){if("string"==typeof e)return t(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}function s(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}function o(e,t){if(null==e)return{};var n,r,a=s(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}n.r(r),n.d(r,{Accordion:()=>Ae,AccordionItem:()=>Me,Alert:()=>Ot,Badge:()=>Bt,Breadcrumb:()=>Vt,Button:()=>Jt,ButtonGroup:()=>rn,Card:()=>jn,CardBody:()=>Cn,CardFooter:()=>Rn,CardGroup:()=>An,CardHeader:()=>Fn,CardImg:()=>Kn,CardImgOverlay:()=>Wn,CardLink:()=>Gn,Carousel:()=>lr,Checkbox:()=>ur,Checklist:()=>Qr,Col:()=>sa,Collapse:()=>la,Container:()=>fa,DropdownMenu:()=>fi,DropdownMenuItem:()=>bi,Fade:()=>xi,Form:()=>el,FormFeedback:()=>rl,FormFloating:()=>ol,FormText:()=>dl,Input:()=>xl,InputGroup:()=>kl,InputGroupText:()=>Tl,Label:()=>Fl,ListGroup:()=>Ul,ListGroupItem:()=>Ql,Modal:()=>Rc,ModalBody:()=>Ac,ModalFooter:()=>Bc,ModalHeader:()=>Vc,ModalTitle:()=>Qc,Nav:()=>nu,NavItem:()=>Hu,NavLink:()=>Vu,Navbar:()=>Cu,NavbarBrand:()=>Ru,NavbarSimple:()=>Bu,NavbarToggler:()=>Iu,Offcanvas:()=>Xu,Pagination:()=>cd,Popover:()=>Ld,PopoverBody:()=>$d,PopoverHeader:()=>Bd,Progress:()=>Qd,RadioButton:()=>rp,RadioItems:()=>tp,Row:()=>up,Select:()=>fp,Spinner:()=>_p,Switch:()=>Op,Tab:()=>Np,Table:()=>Jp,Tabs:()=>Vp,Textarea:()=>tf,Toast:()=>mf,Tooltip:()=>yf});var i=n(0),l=n.n(i),c=n(16),u=n.n(c);function d(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}function p(e){return function t(n){return 0===arguments.length||d(n)?t:e.apply(this,arguments)}}function f(e){return function t(n,r){switch(arguments.length){case 0:return t;case 1:return d(n)?t:p((function(t){return e(n,t)}));default:return d(n)&&d(r)?t:d(n)?p((function(t){return e(t,r)})):d(r)?p((function(t){return e(n,t)})):e(n,r)}}}const m=f((function(e,t){for(var n={},r={},a=0,s=e.length;a<s;)r[e[a]]=1,a+=1;for(var o in t)r.hasOwnProperty(o)||(n[o]=t[o]);return n}));var g=n(2),b=n.n(g);function y(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function h(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function v(e,t,n){var r=(0,i.useRef)(void 0!==e),a=(0,i.useState)(t),s=a[0],o=a[1],l=void 0!==e,c=r.current;return r.current=l,!l&&c&&s!==t&&o(t),[l?e:s,(0,i.useCallback)((function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];n&&n.apply(void 0,[e].concat(r)),o(e)}),[n])]}function _(t,n){return Object.keys(n).reduce((function(r,a){var o,i=r,l=i[y(a)],c=i[a],u=s(i,[y(a),a].map(h)),d=n[a],p=v(c,l,t[d]),f=p[0],m=p[1];return e({},u,((o={})[a]=f,o[d]=m,o))}),t)}n(4);var x=n(1);const O=i.createContext({prefixes:{},breakpoints:["xxl","xl","lg","md","sm","xs"]}),{Consumer:w,Provider:N}=O;function E(e,t){const{prefixes:n}=(0,i.useContext)(O);return e||n[t]||t}function j(){const{breakpoints:e}=(0,i.useContext)(O);return e}function k(){const{dir:e}=(0,i.useContext)(O);return"rtl"===e}function P(e){return e&&e.ownerDocument||document}var C=/([A-Z])/g,T=/^ms-/;function S(e){return function(e){return e.replace(C,"-$1").toLowerCase()}(e).replace(T,"-ms-")}var R=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;const D=function(e,t){var n="",r="";if("string"==typeof t)return e.style.getPropertyValue(S(t))||function(e,t){return function(e){var t=P(e);return t&&t.defaultView||window}(e).getComputedStyle(e,t)}(e).getPropertyValue(S(t));Object.keys(t).forEach((function(a){var s=t[a];s||0===s?function(e){return!(!e||!R.test(e))}(a)?r+=a+"("+s+") ":n+=S(a)+": "+s+";":e.style.removeProperty(S(a))})),r&&(n+="transform: "+r+";"),e.style.cssText+=";"+n};function L(e,t){return L=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},L(e,t)}const I=window.ReactDOM;var A=n.n(I);const $=l().createContext(null);var M="unmounted",F="exited",B="entering",z="entered",K="exiting",H=function(e){var t,n;function r(t,n){var r;r=e.call(this,t,n)||this;var a,s=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?s?(a=F,r.appearStatus=B):a=z:a=t.unmountOnExit||t.mountOnEnter?M:F,r.state={status:a},r.nextCallback=null,r}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,L(t,n),r.getDerivedStateFromProps=function(e,t){return e.in&&t.status===M?{status:F}:null};var a=r.prototype;return a.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},a.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==B&&n!==z&&(t=B):n!==B&&n!==z||(t=K)}this.updateStatus(!1,t)},a.componentWillUnmount=function(){this.cancelNextCallback()},a.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},a.updateStatus=function(e,t){void 0===e&&(e=!1),null!==t?(this.cancelNextCallback(),t===B?this.performEnter(e):this.performExit()):this.props.unmountOnExit&&this.state.status===F&&this.setState({status:M})},a.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,a=this.props.nodeRef?[r]:[A().findDOMNode(this),r],s=a[0],o=a[1],i=this.getTimeouts(),l=r?i.appear:i.enter;e||n?(this.props.onEnter(s,o),this.safeSetState({status:B},(function(){t.props.onEntering(s,o),t.onTransitionEnd(l,(function(){t.safeSetState({status:z},(function(){t.props.onEntered(s,o)}))}))}))):this.safeSetState({status:z},(function(){t.props.onEntered(s)}))},a.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:A().findDOMNode(this);t?(this.props.onExit(r),this.safeSetState({status:K},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:F},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:F},(function(){e.props.onExited(r)}))},a.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},a.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},a.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},a.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:A().findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var a=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],s=a[0],o=a[1];this.props.addEndListener(s,o)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},a.render=function(){var e=this.state.status;if(e===M)return null;var t=this.props,n=t.children,r=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,s(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return l().createElement($.Provider,{value:null},"function"==typeof n?n(e,r):l().cloneElement(l().Children.only(n),r))},r}(l().Component);function q(){}H.contextType=$,H.propTypes={},H.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:q,onEntering:q,onEntered:q,onExit:q,onExiting:q,onExited:q},H.UNMOUNTED=M,H.EXITED=F,H.ENTERING=B,H.ENTERED=z,H.EXITING=K;const W=H,U=!("undefined"==typeof window||!window.document||!window.document.createElement);var V=!1,G=!1;try{var Y={get passive(){return V=!0},get once(){return G=V=!0}};U&&(window.addEventListener("test",Y,Y),window.removeEventListener("test",Y,!0))}catch(e){}const X=function(e,t,n,r){if(r&&"boolean"!=typeof r&&!G){var a=r.once,s=r.capture,o=n;!G&&a&&(o=n.__once||function e(r){this.removeEventListener(t,e,s),n.call(this,r)},n.__once=o),e.addEventListener(t,o,V?r:s)}e.addEventListener(t,n,r)},Q=function(e,t,n,r){var a=r&&"boolean"!=typeof r?r.capture:r;e.removeEventListener(t,n,a),n.__once&&e.removeEventListener(t,n.__once,a)},J=function(e,t,n,r){return X(e,t,n,r),function(){Q(e,t,n,r)}};function Z(e,t,n,r){var a,s;null==n&&(s=-1===(a=D(e,"transitionDuration")||"").indexOf("ms")?1e3:1,n=parseFloat(a)*s||0);var o=function(e,t,n){void 0===n&&(n=5);var r=!1,a=setTimeout((function(){r||function(e,t,n,r){if(void 0===n&&(n=!1),void 0===r&&(r=!0),e){var a=document.createEvent("HTMLEvents");a.initEvent("transitionend",n,r),e.dispatchEvent(a)}}(e,0,!0)}),t+n),s=J(e,"transitionend",(function(){r=!0}),{once:!0});return function(){clearTimeout(a),s()}}(e,n,r),i=J(e,"transitionend",t);return function(){o(),i()}}function ee(e,t){const n=D(e,t)||"",r=-1===n.indexOf("ms")?1e3:1;return parseFloat(n)*r}function te(e,t){const n=ee(e,"transitionDuration"),r=ee(e,"transitionDelay"),a=Z(e,(n=>{n.target===e&&(a(),t(n))}),n+r)}const ne=function(...e){return e.filter((e=>null!=e)).reduce(((e,t)=>{if("function"!=typeof t)throw new Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(...n){e.apply(this,n),t.apply(this,n)}}),null)};function re(e){e.offsetHeight}var ae=function(e){return e&&"function"!=typeof e?function(t){e.current=t}:e};const se=function(e,t){return(0,i.useMemo)((function(){return function(e,t){var n=ae(e),r=ae(t);return function(e){n&&n(e),r&&r(e)}}(e,t)}),[e,t])};function oe(e){return e&&"setState"in e?A().findDOMNode(e):null!=e?e:null}const ie=l().forwardRef((({onEnter:e,onEntering:t,onEntered:n,onExit:r,onExiting:a,onExited:s,addEndListener:o,children:c,childRef:u,...d},p)=>{const f=(0,i.useRef)(null),m=se(f,u),g=e=>{m(oe(e))},b=e=>t=>{e&&f.current&&e(f.current,t)},y=(0,i.useCallback)(b(e),[e]),h=(0,i.useCallback)(b(t),[t]),v=(0,i.useCallback)(b(n),[n]),_=(0,i.useCallback)(b(r),[r]),O=(0,i.useCallback)(b(a),[a]),w=(0,i.useCallback)(b(s),[s]),N=(0,i.useCallback)(b(o),[o]);return(0,x.jsx)(W,{ref:p,...d,onEnter:y,onEntered:v,onEntering:h,onExit:_,onExited:w,onExiting:O,addEndListener:N,nodeRef:f,children:"function"==typeof c?(e,t)=>c(e,{...t,ref:g}):l().cloneElement(c,{ref:g})})})),le={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function ce(e,t){const n=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],r=le[e];return n+parseInt(D(t,r[0]),10)+parseInt(D(t,r[1]),10)}const ue={[F]:"collapse",[K]:"collapsing",[B]:"collapsing",[z]:"collapse show"},de={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,getDimensionValue:ce},pe=l().forwardRef((({onEnter:e,onEntering:t,onEntered:n,onExit:r,onExiting:a,className:s,children:o,dimension:c="height",getDimensionValue:u=ce,...d},p)=>{const f="function"==typeof c?c():c,m=(0,i.useMemo)((()=>ne((e=>{e.style[f]="0"}),e)),[f,e]),g=(0,i.useMemo)((()=>ne((e=>{const t=`scroll${f[0].toUpperCase()}${f.slice(1)}`;e.style[f]=`${e[t]}px`}),t)),[f,t]),y=(0,i.useMemo)((()=>ne((e=>{e.style[f]=null}),n)),[f,n]),h=(0,i.useMemo)((()=>ne((e=>{e.style[f]=`${u(f,e)}px`,re(e)}),r)),[r,u,f]),v=(0,i.useMemo)((()=>ne((e=>{e.style[f]=null}),a)),[f,a]);return(0,x.jsx)(ie,{ref:p,addEndListener:te,...d,"aria-expanded":d.role?d.in:null,onEnter:m,onEntering:g,onEntered:y,onExit:h,onExiting:v,childRef:o.ref,children:(e,t)=>l().cloneElement(o,{...t,className:b()(s,o.props.className,ue[e],"width"===f&&"collapse-horizontal")})})}));pe.defaultProps=de;const fe=pe;function me(e,t){return Array.isArray(e)?e.includes(t):e===t}const ge=i.createContext({});ge.displayName="AccordionContext";const be=ge,ye=i.forwardRef((({as:e="div",bsPrefix:t,className:n,children:r,eventKey:a,...s},o)=>{const{activeEventKey:l}=(0,i.useContext)(be);return t=E(t,"accordion-collapse"),(0,x.jsx)(fe,{ref:o,in:me(l,a),...s,className:b()(n,t),children:(0,x.jsx)(e,{children:i.Children.only(r)})})}));ye.displayName="AccordionCollapse";const he=ye,ve=i.createContext({eventKey:""});ve.displayName="AccordionItemContext";const _e=ve,xe=i.forwardRef((({as:e="div",bsPrefix:t,className:n,...r},a)=>{t=E(t,"accordion-body");const{eventKey:s}=(0,i.useContext)(_e);return(0,x.jsx)(he,{eventKey:s,children:(0,x.jsx)(e,{ref:a,...r,className:b()(n,t)})})}));xe.displayName="AccordionBody";const Oe=xe,we=i.forwardRef((({as:e="button",bsPrefix:t,className:n,onClick:r,...a},s)=>{t=E(t,"accordion-button");const{eventKey:o}=(0,i.useContext)(_e),l=function(e,t){const{activeEventKey:n,onSelect:r,alwaysOpen:a}=(0,i.useContext)(be);return s=>{let o=e===n?null:e;a&&(o=Array.isArray(n)?n.includes(e)?n.filter((t=>t!==e)):[...n,e]:[e]),null==r||r(o,s),null==t||t(s)}}(o,r),{activeEventKey:c}=(0,i.useContext)(be);return"button"===e&&(a.type="button"),(0,x.jsx)(e,{ref:s,onClick:l,...a,"aria-expanded":o===c,className:b()(n,t,!me(c,o)&&"collapsed")})}));we.displayName="AccordionButton";const Ne=we,Ee=i.forwardRef((({as:e="h2",bsPrefix:t,className:n,children:r,onClick:a,...s},o)=>(t=E(t,"accordion-header"),(0,x.jsx)(e,{ref:o,...s,className:b()(n,t),children:(0,x.jsx)(Ne,{onClick:a,children:r})}))));Ee.displayName="AccordionHeader";const je=Ee,ke=i.forwardRef((({as:e="div",bsPrefix:t,className:n,eventKey:r,...a},s)=>{t=E(t,"accordion-item");const o=(0,i.useMemo)((()=>({eventKey:r})),[r]);return(0,x.jsx)(_e.Provider,{value:o,children:(0,x.jsx)(e,{ref:s,...a,className:b()(n,t)})})}));ke.displayName="AccordionItem";const Pe=ke,Ce=i.forwardRef(((e,t)=>{const{as:n="div",activeKey:r,bsPrefix:a,className:s,onSelect:o,flush:l,alwaysOpen:c,...u}=_(e,{activeKey:"onSelect"}),d=E(a,"accordion"),p=(0,i.useMemo)((()=>({activeEventKey:r,onSelect:o,alwaysOpen:c})),[r,o,c]);return(0,x.jsx)(be.Provider,{value:p,children:(0,x.jsx)(n,{ref:t,...u,className:b()(s,d,l&&`${d}-flush`)})})}));Ce.displayName="Accordion";const Te=Object.assign(Ce,{Button:Ne,Collapse:he,Item:Pe,Header:je,Body:Oe});var Se=function(e){return e&&!Array.isArray(e)?[e]:e},Re=function(e){return e.props._dashprivate_layout&&e.props._dashprivate_layout.props?e.props._dashprivate_layout.props:e.props},De=["children","active_item","always_open","start_collapsed","loading_state","key","setProps","class_name","className"],Le=["children","title","item_id","loading_state","class_name","className"],Ie=function(n){var r=n.children,s=n.active_item,c=n.always_open,u=n.start_collapsed,d=n.loading_state,p=n.key,f=n.setProps,g=n.class_name,b=n.className,y=o(n,De);r=Se(r),(0,i.useEffect)((function(){if(f&&void 0===s&&!u){var e=r&&(Re(r[0]).item_id||"item-0");f({active_item:c?[e]:e})}}),[]);var h=r&&r.map((function(n,r){var i=Re(n),u=(i.children,i.title),d=i.item_id,p=i.loading_state,g=i.class_name,b=i.className,y=o(i,Le),h=d||"item-"+r;return l().createElement(Te.Item,e({key:h,eventKey:h,className:g||b},m(["setProps","persistence","persistence_type","persisted_props"],y),{"data-dash-is-loading":p&&p.is_loading||void 0}),l().createElement(Te.Header,{onClick:function(){var e,n,r;e=h,f&&(n=c?Array.isArray(s)?s.includes(e)?s.filter((function(t){return t!==e})):[e].concat(function(e){if(Array.isArray(e))return t(e)}(r=s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||a(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):[e]:s!==e?e:null,f({active_item:n}))},style:{marginTop:"0rem",marginBottom:"0rem"}},u),l().createElement(Te.Body,null,n))}));return l().createElement(Te,e({key:p,"data-dash-is-loading":d&&d.is_loading||void 0,activeKey:s,defaultActiveKey:u?null:s,alwaysOpen:c,className:g||b},m(["setProps","persistence","persistence_type","persisted_props"],y)),h)};Ie.defaultProps={persisted_props:["active_item"],persistence_type:"local",start_collapsed:!1,always_open:!1},Ie.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,flush:u().bool,active_item:u().oneOfType([u().string,u().arrayOf(u().string)]),always_open:u().bool,start_collapsed:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["active_item"])),persistence_type:u().oneOf(["local","session","memory"])};const Ae=Ie;var $e=function(e){return l().createElement(l().Fragment,null,e.children)};$e.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,title:u().string,item_id:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Me=$e;function Fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Be=function(e){var t=(0,i.useRef)(e);return(0,i.useEffect)((function(){t.current=e}),[e]),t};function ze(e){var t=Be(e);return(0,i.useCallback)((function(){return t.current&&t.current.apply(t,arguments)}),[t])}function Ke(){return(0,i.useState)(null)}function He(){var e=(0,i.useRef)(!0),t=(0,i.useRef)((function(){return e.current}));return(0,i.useEffect)((function(){return function(){e.current=!1}}),[]),t.current}function qe(e){var t=(0,i.useRef)(null);return(0,i.useEffect)((function(){t.current=e})),t.current}var We=void 0!==n.g&&n.g.navigator&&"ReactNative"===n.g.navigator.product;const Ue="undefined"!=typeof document||We?i.useLayoutEffect:i.useEffect;new WeakMap;const Ve=["as","disabled"];function Ge({tagName:e,disabled:t,href:n,target:r,rel:a,onClick:s,tabIndex:o=0,type:i}){e||(e=null!=n||null!=r||null!=a?"a":"button");const l={tagName:e};if("button"===e)return[{type:i||"button",disabled:t},l];const c=r=>{(t||"a"===e&&function(e){return!e||"#"===e.trim()}(n))&&r.preventDefault(),t?r.stopPropagation():null==s||s(r)};return"a"===e&&(n||(n="#"),t&&(n=void 0)),[{role:"button",disabled:void 0,tabIndex:t?void 0:o,href:n,target:"a"===e?r:void 0,"aria-disabled":t||void 0,rel:"a"===e?a:void 0,onClick:c,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),c(e))}},l]}const Ye=i.forwardRef(((e,t)=>{let{as:n,disabled:r}=e,a=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,Ve);const[s,{tagName:o}]=Ge(Object.assign({tagName:n,disabled:r},a));return(0,x.jsx)(o,Object.assign({},a,s,{ref:t}))}));Ye.displayName="Button";const Xe=Ye,Qe=["onKeyDown"],Je=i.forwardRef(((e,t)=>{let{onKeyDown:n}=e,r=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,Qe);const[a]=Ge(Object.assign({tagName:"a"},r)),s=ze((e=>{a.onKeyDown(e),null==n||n(e)}));return((o=r.href)&&"#"!==o.trim()||r.role)&&"button"!==r.role?(0,x.jsx)("a",Object.assign({ref:t},r,{onKeyDown:n})):(0,x.jsx)("a",Object.assign({ref:t},r,a,{onKeyDown:s}));var o}));Je.displayName="Anchor";const Ze=Je,et={[B]:"show",[z]:"show"},tt=i.forwardRef((({className:e,children:t,transitionClasses:n={},...r},a)=>{const s=(0,i.useCallback)(((e,t)=>{re(e),null==r.onEnter||r.onEnter(e,t)}),[r]);return(0,x.jsx)(ie,{ref:a,addEndListener:te,...r,onEnter:s,childRef:t.ref,children:(r,a)=>i.cloneElement(t,{...a,className:b()("fade",e,t.props.className,et[r],n[r])})})}));tt.defaultProps={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1},tt.displayName="Fade";const nt=tt,rt={"aria-label":u().string,onClick:u().func,variant:u().oneOf(["white"])},at=i.forwardRef((({className:e,variant:t,...n},r)=>(0,x.jsx)("button",{ref:r,type:"button",className:b()("btn-close",t&&`btn-close-${t}`,e),...n})));at.displayName="CloseButton",at.propTypes=rt,at.defaultProps={"aria-label":"Close"};const st=at,ot=e=>i.forwardRef(((t,n)=>(0,x.jsx)("div",{...t,ref:n,className:b()(t.className,e)})));var it=/-(.)/g;const lt=e=>{return e[0].toUpperCase()+(t=e,t.replace(it,(function(e,t){return t.toUpperCase()}))).slice(1);var t};function ct(e,{displayName:t=lt(e),Component:n,defaultProps:r}={}){const a=i.forwardRef((({className:t,bsPrefix:r,as:a=n||"div",...s},o)=>{const i=E(r,e);return(0,x.jsx)(a,{ref:o,className:b()(t,i),...s})}));return a.defaultProps=r,a.displayName=t,a}const ut=ot("h4");ut.displayName="DivStyledAsH4";const dt=ct("alert-heading",{Component:ut}),pt=ct("alert-link",{Component:Ze}),ft={variant:"primary",show:!0,transition:nt,closeLabel:"Close alert"},mt=i.forwardRef(((e,t)=>{const{bsPrefix:n,show:r,closeLabel:a,closeVariant:s,className:o,children:i,variant:l,onClose:c,dismissible:u,transition:d,...p}=_(e,{show:"onClose"}),f=E(n,"alert"),m=ze((e=>{c&&c(!1,e)})),g=!0===d?nt:d,y=(0,x.jsxs)("div",{role:"alert",...g?void 0:p,ref:t,className:b()(o,f,l&&`${f}-${l}`,u&&`${f}-dismissible`),children:[u&&(0,x.jsx)(st,{onClick:m,"aria-label":a,variant:s}),i]});return g?(0,x.jsx)(g,{unmountOnExit:!0,...p,ref:void 0,in:r,children:y}):r?y:null}));mt.displayName="Alert",mt.defaultProps=ft;const gt=Object.assign(mt,{Link:pt,Heading:dt});var bt=new Set(["primary","secondary","success","danger","warning","info","light","dark","white","transparent"]),yt=new Set(["primary","secondary","success","danger","warning","info","light","dark","muted","white","black-50","white-50"]),ht=["children","dismissable","duration","is_open","loading_state","setProps","color","style","class_name","className","fade"];function vt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _t(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?vt(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var xt=function(t){var n=t.children,r=t.dismissable,a=t.duration,s=t.is_open,c=t.loading_state,u=t.setProps,d=t.color,p=t.style,f=t.class_name,g=t.className,b=t.fade,y=o(t,ht),h=(0,i.useRef)(null);(0,i.useEffect)((function(){a&&(s?h.current=setTimeout(v,a):h.current&&(clearTimeout(h.current),h.current=null))}),[s]);var v=function(){u&&u({is_open:!1})},_=bt.has(d);return l().createElement(gt,e({show:s,dismissible:r,onClose:r?v:null,variant:_?d:null,className:f||g,transition:b,style:_?p:_t({backgroundColor:d},p)},m(["persistence","persisted_props","persistence_type","setProps"],y),{"data-dash-is-loading":c&&c.is_loading||void 0}),n)};xt.defaultProps={color:"success",is_open:!0,duration:null,persisted_props:["is_open"],persistence_type:"local"},xt.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,color:u().string,is_open:u().bool,fade:u().bool,dismissable:u().bool,duration:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["is_open"])),persistence_type:u().oneOf(["local","session","memory"])};const Ot=xt,wt=i.forwardRef((({bsPrefix:e,bg:t,pill:n,text:r,className:a,as:s="span",...o},i)=>{const l=E(e,"badge");return(0,x.jsx)(s,{ref:i,...o,className:b()(a,l,n&&"rounded-pill",r&&`text-${r}`,t&&`bg-${t}`)})}));wt.displayName="Badge",wt.defaultProps={bg:"primary",pill:!1};const Nt=wt;function Et(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function jt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function kt(e){return kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kt(e)}function Pt(e,t){if(t&&("object"===kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return jt(e)}function Ct(e){return Ct=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},Ct(e)}var Tt=n(9),St=n.n(Tt),Rt=["children","external_link","preOnClick","target","linkTarget","href","download"];function Dt(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}function Lt(e,t){return null==e?St()(t):e}Dt.prototype=window.Event.prototype;var It=function(t){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&L(e,t)}(c,t);var n,r,a,s,i=(a=c,s=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Ct(a);if(s){var n=Ct(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Pt(this,e)});function c(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),(t=i.call(this,e)).updateLocation=t.updateLocation.bind(jt(t)),t}return n=c,(r=[{key:"updateLocation",value:function(e){if(!(e.metaKey||e.shiftKey||e.altKey||e.ctrlKey))if(this.props.disabled)e.preventDefault();else{this.props.preOnClick&&this.props.preOnClick();var t=this.props,n=t.external_link,r=t.href;if(r&&!Lt(n,r)){e.preventDefault();var a=this.props.href;window.history.pushState({},"",a),window.dispatchEvent(new Dt("_dashprivate_pushstate")),window.scrollTo(0,0)}}}},{key:"render",value:function(){var t=this,n=this.props,r=n.children,a=n.external_link,s=(n.preOnClick,n.target),i=n.linkTarget,c=n.href,u=n.download,d=o(n,Rt),p=c&&Lt(a,c);return l().createElement("a",e({href:c,target:p?s||i:void 0,download:u&&p?u:void 0},d,{onClick:function(e){return t.updateLocation(e)}}),r)}}])&&Et(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),c}(i.Component);It.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,href:u().string,disabled:u().bool,external_link:u().bool,preOnClick:u().func,target:u().string,download:u().string},It.defaultProps={disabled:!1,external_link:null};var At=["children","href","loading_state","setProps","color","style","className","class_name","text_color"];function $t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Mt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$t(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$t(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ft=function(t){var n=t.children,r=t.href,a=t.loading_state,s=t.setProps,i=t.color,c=t.style,u=t.className,d=t.class_name,p=t.text_color,f=o(t,At),g=bt.has(i);return f[r?"preOnClick":"onClick"]=function(){s&&s({n_clicks:t.n_clicks+1,n_clicks_timestamp:Date.now()})},l().createElement(Nt,e({as:r&&It,href:r,bg:g?i:null,text:p,className:d||u,style:g?c:Mt({backgroundColor:i},c)},m(["setProps","n_clicks","n_clicks_timestamp"],f),{"data-dash-is-loading":a&&a.is_loading||void 0}),n)};Ft.defaultProps={color:"secondary",n_clicks:0,n_clicks_timestamp:-1},Ft.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,color:u().string,text_color:u().string,pill:u().bool,href:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),external_link:u().bool,n_clicks:u().number,n_clicks_timestamp:u().number,target:u().string,title:u().string};const Bt=Ft,zt=i.forwardRef((({bsPrefix:e,active:t,children:n,className:r,as:a="li",linkAs:s=Ze,linkProps:o,href:i,title:l,target:c,...u},d)=>{const p=E(e,"breadcrumb-item");return(0,x.jsx)(a,{ref:d,...u,className:b()(p,r,{active:t}),"aria-current":t?"page":void 0,children:t?n:(0,x.jsx)(s,{...o,href:i,title:l,target:c,children:n})})}));zt.displayName="BreadcrumbItem",zt.defaultProps={active:!1,linkProps:{}};const Kt=zt,Ht=i.forwardRef((({bsPrefix:e,className:t,listProps:n,children:r,label:a,as:s="nav",...o},i)=>{const l=E(e,"breadcrumb");return(0,x.jsx)(s,{"aria-label":a,className:t,ref:i,...o,children:(0,x.jsx)("ol",{...n,className:b()(l,null==n?void 0:n.className),children:r})})}));Ht.displayName="Breadcrumb",Ht.defaultProps={label:"breadcrumb",listProps:{}};const qt=Object.assign(Ht,{Item:Kt});var Wt=["items","tag","loading_state","class_name","className","item_class_name","itemClassName","item_style"],Ut=function(t){var n=t.items,r=t.tag,a=t.loading_state,s=t.class_name,i=t.className,c=t.item_class_name,u=t.itemClassName,d=(t.item_style,o(t,Wt));return l().createElement(qt,e({as:r,"data-dash-is-loading":a&&a.is_loading||void 0,className:s||i},d),(n||[]).map((function(e,t){return l().createElement(qt.Item,{key:"".concat(e.value).concat(t),active:e.active,linkAs:e.href&&It,className:c||u,href:e.href,linkProps:e.href&&{external_link:e.external_link}},e.label)})))};Ut.propTypes={id:u().string,items:u().arrayOf(u().shape({label:u().string,href:u().string,active:u().bool,external_link:u().bool,target:u().string,title:u().string})),style:u().object,item_style:u().object,class_name:u().string,className:u().string,item_class_name:u().string,itemClassName:u().string,key:u().string,tag:u().object,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Vt=Ut,Gt=i.forwardRef((({as:e,bsPrefix:t,variant:n,size:r,active:a,className:s,...o},i)=>{const l=E(t,"btn"),[c,{tagName:u}]=Ge({tagName:e,...o}),d=u;return(0,x.jsx)(d,{...c,...o,ref:i,className:b()(s,l,a&&"active",n&&`${l}-${n}`,r&&`${l}-${r}`,o.href&&o.disabled&&"disabled")})}));Gt.displayName="Button",Gt.defaultProps={variant:"primary",active:!1,disabled:!1};const Yt=Gt;var Xt=["children","disabled","href","loading_state","setProps","n_clicks","target","type","download","name","value","className","class_name","color","outline","onClick"],Qt=function(t){var n=t.children,r=t.disabled,a=t.href,s=t.loading_state,i=t.setProps,c=t.n_clicks,u=t.target,d=t.type,p=t.download,f=t.name,g=t.value,b=t.className,y=t.class_name,h=t.color,v=t.outline,_=t.onClick,x=o(t,Xt),O=a&&!r;return x[O?"preOnClick":"onClick"]=_||function(){!r&&i&&i({n_clicks:c+1,n_clicks_timestamp:Date.now()})},O&&(x.linkTarget=u),l().createElement(Yt,e({as:O?It:"button",variant:v?"outline-".concat(h):h,type:O?void 0:d,href:r?void 0:a,disabled:r,download:O?p:void 0,name:O?void 0:f,value:O?void 0:g,className:y||b},m(["n_clicks_timestamp"],x),{"data-dash-is-loading":s&&s.is_loading||void 0}),n)};Qt.defaultProps={n_clicks:0,n_clicks_timestamp:-1},Qt.propTypes={id:u().string,children:u().node,class_name:u().string,className:u().string,style:u().object,key:u().string,href:u().string,external_link:u().bool,n_clicks:u().number,n_clicks_timestamp:u().number,active:u().bool,color:u().string,disabled:u().bool,size:u().string,title:u().string,outline:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),target:u().string,type:u().oneOf(["button","reset","submit"]),download:u().string,name:u().string,value:u().string};const Jt=Qt,Zt=i.forwardRef((({bsPrefix:e,size:t,vertical:n,className:r,as:a="div",...s},o)=>{const i=E(e,"btn-group");let l=i;return n&&(l=`${i}-vertical`),(0,x.jsx)(a,{...s,ref:o,className:b()(r,l,t&&`${i}-${t}`)})}));Zt.displayName="ButtonGroup",Zt.defaultProps={vertical:!1,role:"group"};const en=Zt;var tn=["children","loading_state","class_name","className"],nn=function(t){var n=t.children,r=t.loading_state,a=t.class_name,s=t.className,i=o(t,tn);return l().createElement(en,e({className:a||s},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};nn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,vertical:u().bool,size:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const rn=nn,an=i.forwardRef((({bsPrefix:e,className:t,variant:n,as:r="img",...a},s)=>{const o=E(e,"card-img");return(0,x.jsx)(r,{ref:s,className:b()(n?`${o}-${n}`:o,t),...a})}));an.displayName="CardImg";const sn=an,on=i.createContext(null);on.displayName="CardHeaderContext";const ln=on,cn=i.forwardRef((({bsPrefix:e,className:t,as:n="div",...r},a)=>{const s=E(e,"card-header"),o=(0,i.useMemo)((()=>({cardHeaderBsPrefix:s})),[s]);return(0,x.jsx)(ln.Provider,{value:o,children:(0,x.jsx)(n,{ref:a,...r,className:b()(t,s)})})}));cn.displayName="CardHeader";const un=cn,dn=ot("h5"),pn=ot("h6"),fn=ct("card-body"),mn=ct("card-title",{Component:dn}),gn=ct("card-subtitle",{Component:pn}),bn=ct("card-link",{Component:"a"}),yn=ct("card-text",{Component:"p"}),hn=ct("card-footer"),vn=ct("card-img-overlay"),_n=i.forwardRef((({bsPrefix:e,className:t,bg:n,text:r,border:a,body:s,children:o,as:i="div",...l},c)=>{const u=E(e,"card");return(0,x.jsx)(i,{ref:c,...l,className:b()(t,u,n&&`bg-${n}`,r&&`text-${r}`,a&&`border-${a}`),children:s?(0,x.jsx)(fn,{children:o}):o})}));_n.displayName="Card",_n.defaultProps={body:!1};const xn=Object.assign(_n,{Img:sn,Title:mn,Subtitle:gn,Body:fn,Link:bn,Text:yn,Header:un,Footer:hn,ImgOverlay:vn});var On=["children","color","inverse","outline","style","loading_state","className","class_name"];function wn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Nn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wn(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var En=function(t){var n=t.children,r=t.color,a=t.inverse,s=t.outline,i=t.style,c=t.loading_state,u=t.className,d=t.class_name,p=o(t,On),f=bt.has(r);return l().createElement(xn,e({"data-dash-is-loading":c&&c.is_loading||void 0,text:a?"white":null,bg:f&&!s?r:null,border:f&&s?r:null,style:f?i:Nn({backgroundColor:r},i),className:d||u},m(["setProps"],p)),n)};En.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,color:u().string,body:u().bool,outline:u().bool,inverse:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const jn=En;var kn=["children","loading_state","className","class_name"],Pn=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,kn);return l().createElement(xn.Body,e({"data-dash-is-loading":r&&r.is_loading||void 0,className:s||a},m(["setProps"],i)),n)};Pn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Cn=Pn;var Tn=["children","loading_state","className","class_name"],Sn=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Tn);return l().createElement(xn.Footer,e({"data-dash-is-loading":r&&r.is_loading||void 0,className:s||a},m(["setProps"],i)),n)};Sn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Rn=Sn,Dn=ct("card-group");var Ln=["children","loading_state","className","class_name"],In=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Ln);return l().createElement(Dn,e({"data-dash-is-loading":r&&r.is_loading||void 0,className:s||a},m(["setProps"],i)),n)};In.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const An=In;var $n=["children","loading_state","className","class_name"],Mn=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,$n);return l().createElement(un,e({"data-dash-is-loading":r&&r.is_loading||void 0,className:s||a},m(["setProps"],i)),n)};Mn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Fn=Mn;var Bn=["children","loading_state","className","class_name","top","bottom"],zn=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.top,c=t.bottom,u=o(t,Bn);return l().createElement(sn,e({"data-dash-is-loading":r&&r.is_loading||void 0,className:s||a,variant:i?"top":c?"bottom":null},m(["setProps"],u)),n)};zn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,top:u().bool,bottom:u().bool,src:u().string,alt:u().string,title:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Kn=zn;var Hn=["children","loading_state","className","class_name"],qn=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Hn);return l().createElement(xn.ImgOverlay,e({"data-dash-is-loading":r&&r.is_loading||void 0,className:s||a},m(["setProps"],i)),n)};qn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Wn=qn;var Un=["children","loading_state","disabled","className","class_name"],Vn=function(t){var n=t.children,r=t.loading_state,a=t.disabled,s=t.className,i=t.class_name,c=o(t,Un);return l().createElement(xn.Link,e({"data-dash-is-loading":r&&r.is_loading||void 0,as:It,preOnClick:function(){!a&&t.setProps&&t.setProps({n_clicks:t.n_clicks+1,n_clicks_timestamp:Date.now()})},disabled:a,className:i||s},m(["setProps","n_clicks","n_clicks_timestamp"],c)),n)};Vn.defaultProps={n_clicks:0,n_clicks_timestamp:-1},Vn.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,href:u().string,external_link:u().bool,n_clicks:u().number,n_clicks_timestamp:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),target:u().string};const Gn=Vn;function Yn(e){var t,n,r=(t=e,(n=(0,i.useRef)(t)).current=t,n);(0,i.useEffect)((function(){return function(){return r.current()}}),[])}var Xn=Math.pow(2,31)-1;function Qn(e,t,n){var r=n-Date.now();e.current=r<=Xn?setTimeout(t,r):setTimeout((function(){return Qn(e,t,n)}),Xn)}function Jn(){var e=He(),t=(0,i.useRef)();return Yn((function(){return clearTimeout(t.current)})),(0,i.useMemo)((function(){var n=function(){return clearTimeout(t.current)};return{set:function(r,a){void 0===a&&(a=0),e()&&(n(),a<=Xn?t.current=setTimeout(r,a):Qn(t,r,Date.now()+a))},clear:n}}),[])}const Zn=ct("carousel-caption"),er=i.forwardRef((({as:e="div",bsPrefix:t,className:n,...r},a)=>{const s=b()(n,E(t,"carousel-item"));return(0,x.jsx)(e,{ref:a,...r,className:s})}));er.displayName="CarouselItem";const tr=er;function nr(e,t){let n=0;return i.Children.map(e,(e=>i.isValidElement(e)?t(e,n++):e))}const rr={slide:!0,fade:!1,controls:!0,indicators:!0,indicatorLabels:[],defaultActiveIndex:0,interval:5e3,keyboard:!0,pause:"hover",wrap:!0,touch:!0,prevIcon:(0,x.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:"Previous",nextIcon:(0,x.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:"Next"},ar=i.forwardRef(((e,t)=>{const{as:n="div",bsPrefix:r,slide:a,fade:s,controls:o,indicators:l,indicatorLabels:c,activeIndex:u,onSelect:d,onSlide:p,onSlid:f,interval:m,keyboard:g,onKeyDown:y,pause:h,onMouseOver:v,onMouseOut:O,wrap:w,touch:N,onTouchStart:j,onTouchMove:P,onTouchEnd:C,prevIcon:T,prevLabel:S,nextIcon:R,nextLabel:D,variant:L,className:I,children:A,...$}=_(e,{activeIndex:"onSelect"}),M=E(r,"carousel"),F=k(),B=(0,i.useRef)(null),[z,K]=(0,i.useState)("next"),[H,q]=(0,i.useState)(!1),[W,U]=(0,i.useState)(!1),[V,G]=(0,i.useState)(u||0);(0,i.useEffect)((()=>{W||u===V||(B.current?K(B.current):K((u||0)>V?"next":"prev"),a&&U(!0),G(u||0))}),[u,W,V,a]),(0,i.useEffect)((()=>{B.current&&(B.current=null)}));let Y,X=0;!function(e,t){let n=0;i.Children.forEach(e,(e=>{i.isValidElement(e)&&((e,t)=>{++X,t===u&&(Y=e.props.interval)})(e,n++)}))}(A);const Q=Be(Y),J=(0,i.useCallback)((e=>{if(W)return;let t=V-1;if(t<0){if(!w)return;t=X-1}B.current="prev",null==d||d(t,e)}),[W,V,d,w,X]),Z=ze((e=>{if(W)return;let t=V+1;if(t>=X){if(!w)return;t=0}B.current="next",null==d||d(t,e)})),ee=(0,i.useRef)();(0,i.useImperativeHandle)(t,(()=>({element:ee.current,prev:J,next:Z})));const ne=ze((()=>{!document.hidden&&function(e){if(!(e&&e.style&&e.parentNode&&e.parentNode.style))return!1;const t=getComputedStyle(e);return"none"!==t.display&&"hidden"!==t.visibility&&"none"!==getComputedStyle(e.parentNode).display}(ee.current)&&(F?J():Z())})),ae="next"===z?"start":"end";var se,oe,le;se=()=>{a||(null==p||p(V,ae),null==f||f(V,ae))},oe=[V],le=(0,i.useRef)(!0),(0,i.useEffect)((function(){if(!le.current)return se();le.current=!1}),oe);const ce=`${M}-item-${z}`,ue=`${M}-item-${ae}`,de=(0,i.useCallback)((e=>{re(e),null==p||p(V,ae)}),[p,V,ae]),pe=(0,i.useCallback)((()=>{U(!1),null==f||f(V,ae)}),[f,V,ae]),fe=(0,i.useCallback)((e=>{if(g&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":return e.preventDefault(),void(F?Z(e):J(e));case"ArrowRight":return e.preventDefault(),void(F?J(e):Z(e))}null==y||y(e)}),[g,y,J,Z,F]),me=(0,i.useCallback)((e=>{"hover"===h&&q(!0),null==v||v(e)}),[h,v]),ge=(0,i.useCallback)((e=>{q(!1),null==O||O(e)}),[O]),be=(0,i.useRef)(0),ye=(0,i.useRef)(0),he=Jn(),ve=(0,i.useCallback)((e=>{be.current=e.touches[0].clientX,ye.current=0,"hover"===h&&q(!0),null==j||j(e)}),[h,j]),_e=(0,i.useCallback)((e=>{e.touches&&e.touches.length>1?ye.current=0:ye.current=e.touches[0].clientX-be.current,null==P||P(e)}),[P]),xe=(0,i.useCallback)((e=>{if(N){const t=ye.current;Math.abs(t)>40&&(t>0?J(e):Z(e))}"hover"===h&&he.set((()=>{q(!1)}),m||void 0),null==C||C(e)}),[N,h,J,Z,he,m,C]),Oe=null!=m&&!H&&!W,we=(0,i.useRef)();(0,i.useEffect)((()=>{var e,t;if(!Oe)return;const n=F?J:Z;return we.current=window.setInterval(document.visibilityState?ne:n,null!=(e=null!=(t=Q.current)?t:m)?e:void 0),()=>{null!==we.current&&clearInterval(we.current)}}),[Oe,J,Z,Q,m,ne,F]);const Ne=(0,i.useMemo)((()=>l&&Array.from({length:X},((e,t)=>e=>{null==d||d(t,e)}))),[l,X,d]);return(0,x.jsxs)(n,{ref:ee,...$,onKeyDown:fe,onMouseOver:me,onMouseOut:ge,onTouchStart:ve,onTouchMove:_e,onTouchEnd:xe,className:b()(I,M,a&&"slide",s&&`${M}-fade`,L&&`${M}-${L}`),children:[l&&(0,x.jsx)("div",{className:`${M}-indicators`,children:nr(A,((e,t)=>(0,x.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=c&&c.length?c[t]:`Slide ${t+1}`,className:t===V?"active":void 0,onClick:Ne?Ne[t]:void 0,"aria-current":t===V},t)))}),(0,x.jsx)("div",{className:`${M}-inner`,children:nr(A,((e,t)=>{const n=t===V;return a?(0,x.jsx)(ie,{in:n,onEnter:n?de:void 0,onEntered:n?pe:void 0,addEndListener:te,children:(t,r)=>i.cloneElement(e,{...r,className:b()(e.props.className,n&&"entered"!==t&&ce,("entered"===t||"exiting"===t)&&"active",("entering"===t||"exiting"===t)&&ue)})}):i.cloneElement(e,{className:b()(e.props.className,n&&"active")})}))}),o&&(0,x.jsxs)(x.Fragment,{children:[(w||0!==u)&&(0,x.jsxs)(Ze,{className:`${M}-control-prev`,onClick:J,children:[T,S&&(0,x.jsx)("span",{className:"visually-hidden",children:S})]}),(w||u!==X-1)&&(0,x.jsxs)(Ze,{className:`${M}-control-next`,onClick:Z,children:[R,D&&(0,x.jsx)("span",{className:"visually-hidden",children:D})]})]})]})}));ar.displayName="Carousel",ar.defaultProps=rr;const sr=Object.assign(ar,{Caption:Zn,Item:tr});var or=["items","active_index","style","class_name","className","loading_state","setProps","interval"],ir=function(t){var n=t.items,r=t.active_index,a=t.style,s=t.class_name,i=t.className,c=t.loading_state,u=t.setProps,d=t.interval,p=o(t,or),f=n.map((function(e){return e.imgClassName=void 0!==e.imgClassName?e.imgClassName:"d-block w-100",l().createElement(sr.Item,{key:e.key},l().createElement("img",{src:e.src,className:e.img_class_name||e.imgClassName,style:e.img_style,alt:e.alt}),l().createElement(sr.Caption,{className:e.caption_class_name||e.captionClassName},e.header&&l().createElement("h5",null,e.header),e.caption&&l().createElement("p",null,e.caption)))}));return l().createElement("div",{style:a,className:s||i},l().createElement(sr,e({"data-dash-is-loading":c&&c.is_loading||void 0,activeIndex:r,onSelect:function(e){return u({active_index:e})},interval:d||null},m(["persistence","persisted_props","persistence_type","setProps"],p)),f))};ir.defaultProps={active_index:0,controls:!0,indicators:!0,persisted_props:["active_index"],persistence_type:"local"},ir.propTypes={id:u().string,style:u().object,class_name:u().string,className:u().string,items:u().arrayOf(u().exact({key:u().string,src:u().string,alt:u().string,img_class_name:u().string,imgClassName:u().string,img_style:u().object,header:u().string,caption:u().string,caption_class_name:u().string,captionClassName:u().string})).isRequired,active_index:u().number,controls:u().bool,indicators:u().bool,ride:u().oneOf(["carousel"]),slide:u().bool,variant:u().oneOf(["dark"]),interval:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["active_index"])),persistence_type:u().oneOf(["local","session","memory"]),setProps:u().func};const lr=ir;var cr=function(e){var t=e.value,n=e.disabled,r=e.className,a=e.class_name,s=e.style,o=e.id,i=e.input_class_name,c=e.inputClassName,u=e.input_style,d=e.label,p=e.label_id,f=e.label_style,m=e.label_class_name,g=e.labelClassName,y=e.loading_state,h=e.name,v=e.setProps;return l().createElement("div",{className:b()("form-check",a||r),style:s,"data-dash-is-loading":y&&y.is_loading||void 0},l().createElement("input",{id:o,name:h,checked:t,className:b()("form-check-input",i||c),disabled:n,style:u,type:"checkbox",onChange:function(){n||v&&v({value:!t})}}),l().createElement("label",{id:p,style:f,className:b()(m||g,"form-check-label","form-label"),htmlFor:o},d))};cr.propTypes={id:u().string,class_name:u().string,className:u().string,style:u().object,input_style:u().object,inputStyle:u().object,input_class_name:u().string,inputClassName:u().string,label:u().string,label_id:u().string,label_style:u().object,labelStyle:u().object,label_class_name:u().string,labelClassName:u().string,name:u().string,disabled:u().bool,value:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),setProps:u().func},cr.defaultProps={inputStyle:{},input_style:null,inputClassName:"",input_class_name:"",labelStyle:{},label_style:null,labelClassName:"",label_class_name:"",persisted_props:["value"],persistence_type:"local",value:!1,disabled:!1};const ur=cr;function dr(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}function pr(e,t,n){for(var r=0,a=n.length;r<a;){if(e(t,n[r]))return!0;r+=1}return!1}function fr(e,t){return Object.prototype.hasOwnProperty.call(t,e)}const mr="function"==typeof Object.is?Object.is:function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};var gr=Object.prototype.toString;const br=function(){return"[object Arguments]"===gr.call(arguments)?function(e){return"[object Arguments]"===gr.call(e)}:function(e){return fr("callee",e)}}();var yr=!{toString:null}.propertyIsEnumerable("toString"),hr=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],vr=function(){return arguments.propertyIsEnumerable("length")}(),_r=function(e,t){for(var n=0;n<e.length;){if(e[n]===t)return!0;n+=1}return!1};const xr="function"!=typeof Object.keys||vr?p((function(e){if(Object(e)!==e)return[];var t,n,r=[],a=vr&&br(e);for(t in e)!fr(t,e)||a&&"length"===t||(r[r.length]=t);if(yr)for(n=hr.length-1;n>=0;)fr(t=hr[n],e)&&!_r(r,t)&&(r[r.length]=t),n-=1;return r})):p((function(e){return Object(e)!==e?[]:Object.keys(e)})),Or=p((function(e){return null===e?"Null":void 0===e?"Undefined":Object.prototype.toString.call(e).slice(8,-1)}));function wr(e,t,n,r){var a=dr(e);function s(e,t){return Nr(e,t,n.slice(),r.slice())}return!pr((function(e,t){return!pr(s,t,e)}),dr(t),a)}function Nr(e,t,n,r){if(mr(e,t))return!0;var a,s,o=Or(e);if(o!==Or(t))return!1;if(null==e||null==t)return!1;if("function"==typeof e["fantasy-land/equals"]||"function"==typeof t["fantasy-land/equals"])return"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t)&&"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e);if("function"==typeof e.equals||"function"==typeof t.equals)return"function"==typeof e.equals&&e.equals(t)&&"function"==typeof t.equals&&t.equals(e);switch(o){case"Arguments":case"Array":case"Object":if("function"==typeof e.constructor&&"Promise"===(a=e.constructor,null==(s=String(a).match(/^function (\w*)/))?"":s[1]))return e===t;break;case"Boolean":case"Number":case"String":if(typeof e!=typeof t||!mr(e.valueOf(),t.valueOf()))return!1;break;case"Date":if(!mr(e.valueOf(),t.valueOf()))return!1;break;case"Error":return e.name===t.name&&e.message===t.message;case"RegExp":if(e.source!==t.source||e.global!==t.global||e.ignoreCase!==t.ignoreCase||e.multiline!==t.multiline||e.sticky!==t.sticky||e.unicode!==t.unicode)return!1}for(var i=n.length-1;i>=0;){if(n[i]===e)return r[i]===t;i-=1}switch(o){case"Map":return e.size===t.size&&wr(e.entries(),t.entries(),n.concat([e]),r.concat([t]));case"Set":return e.size===t.size&&wr(e.values(),t.values(),n.concat([e]),r.concat([t]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var l=xr(e);if(l.length!==xr(t).length)return!1;var c=n.concat([e]),u=r.concat([t]);for(i=l.length-1;i>=0;){var d=l[i];if(!fr(d,t)||!Nr(t[d],e[d],c,u))return!1;i-=1}return!0}const Er=f((function(e,t){return Nr(e,t,[],[])}));function jr(e,t){return function(e,t,n){var r,a;if("function"==typeof e.indexOf)switch(typeof t){case"number":if(0===t){for(r=1/t;n<e.length;){if(0===(a=e[n])&&1/a===r)return n;n+=1}return-1}if(t!=t){for(;n<e.length;){if("number"==typeof(a=e[n])&&a!=a)return n;n+=1}return-1}return e.indexOf(t,n);case"string":case"boolean":case"function":case"undefined":return e.indexOf(t,n);case"object":if(null===t)return e.indexOf(t,n)}for(;n<e.length;){if(Er(e[n],t))return n;n+=1}return-1}(t,e,0)>=0}const kr=f(jr);function Pr(e,t){switch(e){case 0:return function(){return t.apply(this,arguments)};case 1:return function(e){return t.apply(this,arguments)};case 2:return function(e,n){return t.apply(this,arguments)};case 3:return function(e,n,r){return t.apply(this,arguments)};case 4:return function(e,n,r,a){return t.apply(this,arguments)};case 5:return function(e,n,r,a,s){return t.apply(this,arguments)};case 6:return function(e,n,r,a,s,o){return t.apply(this,arguments)};case 7:return function(e,n,r,a,s,o,i){return t.apply(this,arguments)};case 8:return function(e,n,r,a,s,o,i,l){return t.apply(this,arguments)};case 9:return function(e,n,r,a,s,o,i,l,c){return t.apply(this,arguments)};case 10:return function(e,n,r,a,s,o,i,l,c,u){return t.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}function Cr(e,t,n){return function(){for(var r=[],a=0,s=e,o=0;o<t.length||a<arguments.length;){var i;o<t.length&&(!d(t[o])||a>=arguments.length)?i=t[o]:(i=arguments[a],a+=1),r[o]=i,d(i)||(s-=1),o+=1}return s<=0?n.apply(this,r):Pr(s,Cr(e,r,n))}}const Tr=f((function(e,t){return 1===e?p(t):Pr(e,Cr(e,[],t))})),Sr=p((function(e){return Tr(e.length,(function(t,n){var r=Array.prototype.slice.call(arguments,0);return r[0]=n,r[1]=t,e.apply(this,r)}))})),Rr=Array.isArray||function(e){return null!=e&&e.length>=0&&"[object Array]"===Object.prototype.toString.call(e)};function Dr(e){return null!=e&&"function"==typeof e["@@transducer/step"]}function Lr(e,t,n){return function(){if(0===arguments.length)return n();var r=Array.prototype.slice.call(arguments,0),a=r.pop();if(!Rr(a)){for(var s=0;s<e.length;){if("function"==typeof a[e[s]])return a[e[s]].apply(a,r);s+=1}if(Dr(a)){var o=t.apply(null,r);return o(a)}}return n.apply(this,arguments)}}const Ir=p((function(e){return!!Rr(e)||!!e&&"object"==typeof e&&!function(e){return"[object String]"===Object.prototype.toString.call(e)}(e)&&(1===e.nodeType?!!e.length:0===e.length||e.length>0&&e.hasOwnProperty(0)&&e.hasOwnProperty(e.length-1))}));var Ar=function(){function e(e){this.f=e}return e.prototype["@@transducer/init"]=function(){throw new Error("init not implemented on XWrap")},e.prototype["@@transducer/result"]=function(e){return e},e.prototype["@@transducer/step"]=function(e,t){return this.f(e,t)},e}();const $r=f((function(e,t){return Pr(e.length,(function(){return e.apply(t,arguments)}))}));function Mr(e,t,n){for(var r=n.next();!r.done;){if((t=e["@@transducer/step"](t,r.value))&&t["@@transducer/reduced"]){t=t["@@transducer/value"];break}r=n.next()}return e["@@transducer/result"](t)}function Fr(e,t,n,r){return e["@@transducer/result"](n[r]($r(e["@@transducer/step"],e),t))}var Br="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";const zr=function(){return this.xf["@@transducer/init"]()},Kr=function(e){return this.xf["@@transducer/result"](e)};var Hr=function(){function e(e,t){this.xf=t,this.f=e}return e.prototype["@@transducer/init"]=zr,e.prototype["@@transducer/result"]=Kr,e.prototype["@@transducer/step"]=function(e,t){return this.f(t)?this.xf["@@transducer/step"](e,t):e},e}();const qr=f(Lr(["filter"],f((function(e,t){return new Hr(e,t)})),(function(e,t){return n=t,"[object Object]"===Object.prototype.toString.call(n)?function(e,t,n){if("function"==typeof e&&(e=function(e){return new Ar(e)}(e)),Ir(n))return function(e,t,n){for(var r=0,a=n.length;r<a;){if((t=e["@@transducer/step"](t,n[r]))&&t["@@transducer/reduced"]){t=t["@@transducer/value"];break}r+=1}return e["@@transducer/result"](t)}(e,t,n);if("function"==typeof n["fantasy-land/reduce"])return Fr(e,t,n,"fantasy-land/reduce");if(null!=n[Br])return Mr(e,t,n[Br]());if("function"==typeof n.next)return Mr(e,t,n);if("function"==typeof n.reduce)return Fr(e,t,n,"reduce");throw new TypeError("reduce: list must be array or iterable")}((function(n,r){return e(t[r])&&(n[r]=t[r]),n}),{},xr(t)):function(e,t){for(var n=0,r=t.length,a=[];n<r;)e(t[n])&&(a[a.length]=t[n]),n+=1;return a}(e,t);var n}))),Wr=f((function(e,t){return qr((n=e,function(){return!n.apply(this,arguments)}),t);var n})),Ur=f((function(e,t){return Wr(Sr(jr)(e),t)})),Vr=f((function(e,t){return function(e,t){var n;t=t||[];var r=(e=e||[]).length,a=t.length,s=[];for(n=0;n<r;)s[s.length]=e[n],n+=1;for(n=0;n<a;)s[s.length]=t[n],n+=1;return s}(t,[e])}));function Gr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Yr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gr(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Xr=function(e){var t=e.className,n=e.class_name,r=e.id,a=e.options,s=e.style,o=e.key,i=e.loading_state,c=e.name,u=a.map((function(t){return function(t){var n=e.id,r=e.inputClassName,a=e.input_class_name,s=e.inputCheckedClassName,o=e.input_checked_class_name,i=e.inputStyle,u=e.input_style,d=e.inputCheckedStyle,p=e.input_checked_style,f=e.labelClassName,m=e.label_class_name,g=e.labelCheckedClassName,y=e.label_checked_class_name,h=e.labelStyle,v=e.label_style,_=e.labelCheckedStyle,x=e.label_checked_style,O=e.setProps,w=e.inline,N=e.value,E=e.switch,j=kr(t.value,N),k=j?Yr(Yr({},u||i),p||d):u||i,P=j?Yr(Yr({},v||h),x||_):v||h,C=t.input_id||"_dbcprivate_checklist_".concat(n,"_input_").concat(t.value);return l().createElement("div",{className:b()("form-check",w&&"form-check-inline",E&&"form-switch"),key:t.value},l().createElement("input",{id:C,name:c,value:t.value,checked:j,className:b()("form-check-input",a||r,j&&(o||s)),disabled:Boolean(t.disabled),style:k,type:"checkbox",onChange:function(){var e;e=kr(t.value,N)?Ur([t.value],N):Vr(t.value,N),O({value:e})}}),l().createElement("label",{id:t.label_id,style:P,className:b()("form-check-label",m||f,j&&(y||g)),key:t.value,htmlFor:C},t.label))}(t)}));return l().createElement("div",{id:r,style:s,className:n||t,key:o,"data-dash-is-loading":i&&i.is_loading||void 0},u)};Xr.propTypes={id:u().string,options:u().arrayOf(u().exact({label:u().oneOfType([u().string,u().number]).isRequired,value:u().oneOfType([u().string,u().number]).isRequired,disabled:u().bool,input_id:u().string,label_id:u().string})),value:u().arrayOf(u().oneOfType([u().string,u().number])),class_name:u().string,className:u().string,style:u().object,key:u().string,input_style:u().object,inputStyle:u().object,input_checked_style:u().object,inputCheckedStyle:u().object,input_class_name:u().string,inputClassName:u().string,input_checked_class_name:u().string,inputCheckedClassName:u().string,label_style:u().object,labelStyle:u().object,label_checked_style:u().object,labelCheckedStyle:u().object,label_class_name:u().string,labelClassName:u().string,label_checked_class_name:u().string,labelCheckedClassName:u().string,setProps:u().func,inline:u().bool,switch:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),name:u().string},Xr.defaultProps={inputStyle:{},input_style:null,inputClassName:"",input_class_name:"",labelStyle:{},label_style:null,labelClassName:"",label_class_name:"",options:[],value:[],persisted_props:["value"],persistence_type:"local"};const Qr=Xr,Jr=i.forwardRef(((e,t)=>{const[{className:n,...r},{as:a="div",bsPrefix:s,spans:o}]=function({as:e,bsPrefix:t,className:n,...r}){t=E(t,"col");const a=j(),s=[],o=[];return a.forEach((e=>{const n=r[e];let a,i,l;delete r[e],"object"==typeof n&&null!=n?({span:a,offset:i,order:l}=n):a=n;const c="xs"!==e?`-${e}`:"";a&&s.push(!0===a?`${t}${c}`:`${t}${c}-${a}`),null!=l&&o.push(`order${c}-${l}`),null!=i&&o.push(`offset${c}-${i}`)})),[{...r,className:b()(n,...s,...o)},{as:e,bsPrefix:t,spans:s}]}(e);return(0,x.jsx)(a,{...r,ref:t,className:b()(n,!o.length&&s)})}));Jr.displayName="Col";const Zr=Jr;var ea=["children","width","xs","sm","md","lg","xl","xxl","align","className","class_name","loading_state"],ta={start:"align-self-start",center:"align-self-center",end:"align-self-end",stretch:"align-self-stretch",baseline:"align-self-baseline"},na=function(t){var n=t.children,r=t.width,a=t.xs,s=t.sm,i=t.md,c=t.lg,u=t.xl,d=t.xxl,p=t.align,f=t.className,g=t.class_name,y=t.loading_state,h=o(t,ea);[r,a,s,i,c,u,d].forEach((function(e){"object"===kt(e)&&null!==e&&(e.span=e.size)}));var v=p&&ta[p],_=b()(g||f,v);return l().createElement(Zr,e({xs:a||r,sm:s,md:i,lg:c,xl:u,xxl:d,className:_},m(["setProps"],h),{"data-dash-is-loading":y&&y.is_loading||void 0}),n)},ra=u().oneOfType([u().number,u().string]),aa=u().oneOfType([u().string,u().number,u().bool,u().shape({size:u().oneOfType([u().bool,u().number,u().string]),order:ra,offset:ra})]);na.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,width:aa,xs:aa,sm:aa,md:aa,lg:aa,xl:aa,xxl:aa,align:u().oneOf(["start","center","end","stretch","baseline"]),loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const sa=na;var oa=["children","is_open","navbar","loading_state","className","class_name","tag"],ia=l().forwardRef((function(t,n){var r=t.children,a=t.is_open,s=t.navbar,i=t.loading_state,c=t.className,u=t.class_name,d=t.tag,p=o(t,oa);return l().createElement(fe,e({in:a,as:d,className:u||c},m(["setProps"],p),{"data-dash-is-loading":i&&i.is_loading||void 0}),l().createElement("div",{ref:n,className:s&&"navbar-collapse"},r))}));ia.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,is_open:u().bool,navbar:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const la=ia,ca=i.forwardRef((({bsPrefix:e,fluid:t,as:n="div",className:r,...a},s)=>{const o=E(e,"container"),i="string"==typeof t?`-${t}`:"-fluid";return(0,x.jsx)(n,{ref:s,...a,className:b()(r,t?`${o}${i}`:o)})}));ca.displayName="Container",ca.defaultProps={fluid:!1};const ua=ca;var da=["children","loading_state","className","class_name","tag"],pa=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.tag,c=o(t,da);return l().createElement(ua,e({as:i,className:s||a},m(["setProps"],c),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};pa.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,fluid:u().oneOfType([u().bool,u().string]),tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const fa=pa;function ma(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,s=[],o=!0,i=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(s.push(r.value),!t||s.length!==t);o=!0);}catch(e){i=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(i)throw a}}return s}}(e,t)||a(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ga=Function.prototype.bind.call(Function.prototype.call,[].slice);function ba(e,t){return ga(e.querySelectorAll(t))}function ya(){return(0,i.useReducer)((function(e){return!e}),!1)[1]}const ha=i.createContext(null);var va=Object.prototype.hasOwnProperty;function _a(e,t,n){for(n of e.keys())if(xa(n,t))return n}function xa(e,t){var n,r,a;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&xa(e[r],t[r]););return-1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e){if((a=r)&&"object"==typeof a&&!(a=_a(t,a)))return!1;if(!t.has(a))return!1}return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e){if((a=r[0])&&"object"==typeof a&&!(a=_a(t,a)))return!1;if(!xa(r[1],t.get(a)))return!1}return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return-1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return-1===r}if(!n||"object"==typeof e){for(n in r=0,e){if(va.call(e,n)&&++r&&!va.call(t,n))return!1;if(!(n in t)||!xa(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!=e&&t!=t}function Oa(e){return e.split("-")[0]}function wa(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Na(e){return e instanceof wa(e).Element||e instanceof Element}function Ea(e){return e instanceof wa(e).HTMLElement||e instanceof HTMLElement}function ja(e){return"undefined"!=typeof ShadowRoot&&(e instanceof wa(e).ShadowRoot||e instanceof ShadowRoot)}var ka=Math.max,Pa=Math.min,Ca=Math.round;function Ta(e,t){void 0===t&&(t=!1);var n=e.getBoundingClientRect(),r=1,a=1;if(Ea(e)&&t){var s=e.offsetHeight,o=e.offsetWidth;o>0&&(r=Ca(n.width)/o||1),s>0&&(a=Ca(n.height)/s||1)}return{width:n.width/r,height:n.height/a,top:n.top/a,right:n.right/r,bottom:n.bottom/a,left:n.left/r,x:n.left/r,y:n.top/a}}function Sa(e){var t=Ta(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Ra(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&ja(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Da(e){return e?(e.nodeName||"").toLowerCase():null}function La(e){return wa(e).getComputedStyle(e)}function Ia(e){return["table","td","th"].indexOf(Da(e))>=0}function Aa(e){return((Na(e)?e.ownerDocument:e.document)||window.document).documentElement}function $a(e){return"html"===Da(e)?e:e.assignedSlot||e.parentNode||(ja(e)?e.host:null)||Aa(e)}function Ma(e){return Ea(e)&&"fixed"!==La(e).position?e.offsetParent:null}function Fa(e){for(var t=wa(e),n=Ma(e);n&&Ia(n)&&"static"===La(n).position;)n=Ma(n);return n&&("html"===Da(n)||"body"===Da(n)&&"static"===La(n).position)?t:n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&Ea(e)&&"fixed"===La(e).position)return null;var n=$a(e);for(ja(n)&&(n=n.host);Ea(n)&&["html","body"].indexOf(Da(n))<0;){var r=La(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Ba(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function za(e,t,n){return ka(e,Pa(t,n))}function Ka(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Ha(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var qa="top",Wa="bottom",Ua="right",Va="left",Ga="auto",Ya=[qa,Wa,Ua,Va],Xa="start",Qa="end",Ja="viewport",Za="popper",es=Ya.reduce((function(e,t){return e.concat([t+"-"+Xa,t+"-"+Qa])}),[]),ts=[].concat(Ya,[Ga]).reduce((function(e,t){return e.concat([t,t+"-"+Xa,t+"-"+Qa])}),[]),ns=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];const rs={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,a=e.options,s=n.elements.arrow,o=n.modifiersData.popperOffsets,i=Oa(n.placement),l=Ba(i),c=[Va,Ua].indexOf(i)>=0?"height":"width";if(s&&o){var u=function(e,t){return Ka("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Ha(e,Ya))}(a.padding,n),d=Sa(s),p="y"===l?qa:Va,f="y"===l?Wa:Ua,m=n.rects.reference[c]+n.rects.reference[l]-o[l]-n.rects.popper[c],g=o[l]-n.rects.reference[l],b=Fa(s),y=b?"y"===l?b.clientHeight||0:b.clientWidth||0:0,h=m/2-g/2,v=u[p],_=y-d[c]-u[f],x=y/2-d[c]/2+h,O=za(v,x,_),w=l;n.modifiersData[r]=((t={})[w]=O,t.centerOffset=O-x,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Ra(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function as(e){return e.split("-")[1]}var ss={top:"auto",right:"auto",bottom:"auto",left:"auto"};function os(e){var t,n=e.popper,r=e.popperRect,a=e.placement,s=e.variation,o=e.offsets,i=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,p=o.x,f=void 0===p?0:p,m=o.y,g=void 0===m?0:m,b="function"==typeof u?u({x:f,y:g}):{x:f,y:g};f=b.x,g=b.y;var y=o.hasOwnProperty("x"),h=o.hasOwnProperty("y"),v=Va,_=qa,x=window;if(c){var O=Fa(n),w="clientHeight",N="clientWidth";O===wa(n)&&"static"!==La(O=Aa(n)).position&&"absolute"===i&&(w="scrollHeight",N="scrollWidth"),(a===qa||(a===Va||a===Ua)&&s===Qa)&&(_=Wa,g-=(d&&O===x&&x.visualViewport?x.visualViewport.height:O[w])-r.height,g*=l?1:-1),a!==Va&&(a!==qa&&a!==Wa||s!==Qa)||(v=Ua,f-=(d&&O===x&&x.visualViewport?x.visualViewport.width:O[N])-r.width,f*=l?1:-1)}var E,j=Object.assign({position:i},c&&ss),k=!0===u?function(e){var t=e.x,n=e.y,r=window.devicePixelRatio||1;return{x:Ca(t*r)/r||0,y:Ca(n*r)/r||0}}({x:f,y:g}):{x:f,y:g};return f=k.x,g=k.y,l?Object.assign({},j,((E={})[_]=h?"0":"",E[v]=y?"0":"",E.transform=(x.devicePixelRatio||1)<=1?"translate("+f+"px, "+g+"px)":"translate3d("+f+"px, "+g+"px, 0)",E)):Object.assign({},j,((t={})[_]=h?g+"px":"",t[v]=y?f+"px":"",t.transform="",t))}const is={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,a=void 0===r||r,s=n.adaptive,o=void 0===s||s,i=n.roundOffsets,l=void 0===i||i,c={placement:Oa(t.placement),variation:as(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,os(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,os(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ls={passive:!0};const cs={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,a=r.scroll,s=void 0===a||a,o=r.resize,i=void 0===o||o,l=wa(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&c.forEach((function(e){e.addEventListener("scroll",n.update,ls)})),i&&l.addEventListener("resize",n.update,ls),function(){s&&c.forEach((function(e){e.removeEventListener("scroll",n.update,ls)})),i&&l.removeEventListener("resize",n.update,ls)}},data:{}};var us={left:"right",right:"left",bottom:"top",top:"bottom"};function ds(e){return e.replace(/left|right|bottom|top/g,(function(e){return us[e]}))}var ps={start:"end",end:"start"};function fs(e){return e.replace(/start|end/g,(function(e){return ps[e]}))}function ms(e){var t=wa(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function gs(e){return Ta(Aa(e)).left+ms(e).scrollLeft}function bs(e){var t=La(e),n=t.overflow,r=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+r)}function ys(e){return["html","body","#document"].indexOf(Da(e))>=0?e.ownerDocument.body:Ea(e)&&bs(e)?e:ys($a(e))}function hs(e,t){var n;void 0===t&&(t=[]);var r=ys(e),a=r===(null==(n=e.ownerDocument)?void 0:n.body),s=wa(r),o=a?[s].concat(s.visualViewport||[],bs(r)?r:[]):r,i=t.concat(o);return a?i:i.concat(hs($a(o)))}function vs(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function _s(e,t){return t===Ja?vs(function(e){var t=wa(e),n=Aa(e),r=t.visualViewport,a=n.clientWidth,s=n.clientHeight,o=0,i=0;return r&&(a=r.width,s=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(o=r.offsetLeft,i=r.offsetTop)),{width:a,height:s,x:o+gs(e),y:i}}(e)):Na(t)?function(e){var t=Ta(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):vs(function(e){var t,n=Aa(e),r=ms(e),a=null==(t=e.ownerDocument)?void 0:t.body,s=ka(n.scrollWidth,n.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),o=ka(n.scrollHeight,n.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),i=-r.scrollLeft+gs(e),l=-r.scrollTop;return"rtl"===La(a||n).direction&&(i+=ka(n.clientWidth,a?a.clientWidth:0)-s),{width:s,height:o,x:i,y:l}}(Aa(e)))}function xs(e){var t,n=e.reference,r=e.element,a=e.placement,s=a?Oa(a):null,o=a?as(a):null,i=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(s){case qa:t={x:i,y:n.y-r.height};break;case Wa:t={x:i,y:n.y+n.height};break;case Ua:t={x:n.x+n.width,y:l};break;case Va:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var c=s?Ba(s):null;if(null!=c){var u="y"===c?"height":"width";switch(o){case Xa:t[c]=t[c]-(n[u]/2-r[u]/2);break;case Qa:t[c]=t[c]+(n[u]/2-r[u]/2)}}return t}function Os(e,t){void 0===t&&(t={});var n=t,r=n.placement,a=void 0===r?e.placement:r,s=n.boundary,o=void 0===s?"clippingParents":s,i=n.rootBoundary,l=void 0===i?Ja:i,c=n.elementContext,u=void 0===c?Za:c,d=n.altBoundary,p=void 0!==d&&d,f=n.padding,m=void 0===f?0:f,g=Ka("number"!=typeof m?m:Ha(m,Ya)),b=u===Za?"reference":Za,y=e.rects.popper,h=e.elements[p?b:u],v=function(e,t,n){var r="clippingParents"===t?function(e){var t=hs($a(e)),n=["absolute","fixed"].indexOf(La(e).position)>=0&&Ea(e)?Fa(e):e;return Na(n)?t.filter((function(e){return Na(e)&&Ra(e,n)&&"body"!==Da(e)})):[]}(e):[].concat(t),a=[].concat(r,[n]),s=a[0],o=a.reduce((function(t,n){var r=_s(e,n);return t.top=ka(r.top,t.top),t.right=Pa(r.right,t.right),t.bottom=Pa(r.bottom,t.bottom),t.left=ka(r.left,t.left),t}),_s(e,s));return o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}(Na(h)?h:h.contextElement||Aa(e.elements.popper),o,l),_=Ta(e.elements.reference),x=xs({reference:_,element:y,strategy:"absolute",placement:a}),O=vs(Object.assign({},y,x)),w=u===Za?O:_,N={top:v.top-w.top+g.top,bottom:w.bottom-v.bottom+g.bottom,left:v.left-w.left+g.left,right:w.right-v.right+g.right},E=e.modifiersData.offset;if(u===Za&&E){var j=E[a];Object.keys(N).forEach((function(e){var t=[Ua,Wa].indexOf(e)>=0?1:-1,n=[qa,Wa].indexOf(e)>=0?"y":"x";N[e]+=j[n]*t}))}return N}const ws={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var a=n.mainAxis,s=void 0===a||a,o=n.altAxis,i=void 0===o||o,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,p=n.altBoundary,f=n.flipVariations,m=void 0===f||f,g=n.allowedAutoPlacements,b=t.options.placement,y=Oa(b),h=l||(y!==b&&m?function(e){if(Oa(e)===Ga)return[];var t=ds(e);return[fs(e),t,fs(t)]}(b):[ds(b)]),v=[b].concat(h).reduce((function(e,n){return e.concat(Oa(n)===Ga?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,a=n.boundary,s=n.rootBoundary,o=n.padding,i=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?ts:l,u=as(r),d=u?i?es:es.filter((function(e){return as(e)===u})):Ya,p=d.filter((function(e){return c.indexOf(e)>=0}));0===p.length&&(p=d);var f=p.reduce((function(t,n){return t[n]=Os(e,{placement:n,boundary:a,rootBoundary:s,padding:o})[Oa(n)],t}),{});return Object.keys(f).sort((function(e,t){return f[e]-f[t]}))}(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:g}):n)}),[]),_=t.rects.reference,x=t.rects.popper,O=new Map,w=!0,N=v[0],E=0;E<v.length;E++){var j=v[E],k=Oa(j),P=as(j)===Xa,C=[qa,Wa].indexOf(k)>=0,T=C?"width":"height",S=Os(t,{placement:j,boundary:u,rootBoundary:d,altBoundary:p,padding:c}),R=C?P?Ua:Va:P?Wa:qa;_[T]>x[T]&&(R=ds(R));var D=ds(R),L=[];if(s&&L.push(S[k]<=0),i&&L.push(S[R]<=0,S[D]<=0),L.every((function(e){return e}))){N=j,w=!1;break}O.set(j,L)}if(w)for(var I=function(e){var t=v.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return N=t,"break"},A=m?3:1;A>0&&"break"!==I(A);A--);t.placement!==N&&(t.modifiersData[r]._skip=!0,t.placement=N,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Ns(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Es(e){return[qa,Ua,Wa,Va].some((function(t){return e[t]>=0}))}const js={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.offset,s=void 0===a?[0,0]:a,o=ts.reduce((function(e,n){return e[n]=function(e,t,n){var r=Oa(e),a=[Va,qa].indexOf(r)>=0?-1:1,s="function"==typeof n?n(Object.assign({},t,{placement:e})):n,o=s[0],i=s[1];return o=o||0,i=(i||0)*a,[Va,Ua].indexOf(r)>=0?{x:i,y:o}:{x:o,y:i}}(n,t.rects,s),e}),{}),i=o[t.placement],l=i.x,c=i.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=o}},ks={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.mainAxis,s=void 0===a||a,o=n.altAxis,i=void 0!==o&&o,l=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,p=n.tether,f=void 0===p||p,m=n.tetherOffset,g=void 0===m?0:m,b=Os(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),y=Oa(t.placement),h=as(t.placement),v=!h,_=Ba(y),x="x"===_?"y":"x",O=t.modifiersData.popperOffsets,w=t.rects.reference,N=t.rects.popper,E="function"==typeof g?g(Object.assign({},t.rects,{placement:t.placement})):g,j="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(O){if(s){var C,T="y"===_?qa:Va,S="y"===_?Wa:Ua,R="y"===_?"height":"width",D=O[_],L=D+b[T],I=D-b[S],A=f?-N[R]/2:0,$=h===Xa?w[R]:N[R],M=h===Xa?-N[R]:-w[R],F=t.elements.arrow,B=f&&F?Sa(F):{width:0,height:0},z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},K=z[T],H=z[S],q=za(0,w[R],B[R]),W=v?w[R]/2-A-q-K-j.mainAxis:$-q-K-j.mainAxis,U=v?-w[R]/2+A+q+H+j.mainAxis:M+q+H+j.mainAxis,V=t.elements.arrow&&Fa(t.elements.arrow),G=V?"y"===_?V.clientTop||0:V.clientLeft||0:0,Y=null!=(C=null==k?void 0:k[_])?C:0,X=D+U-Y,Q=za(f?Pa(L,D+W-Y-G):L,D,f?ka(I,X):I);O[_]=Q,P[_]=Q-D}if(i){var J,Z="x"===_?qa:Va,ee="x"===_?Wa:Ua,te=O[x],ne="y"===x?"height":"width",re=te+b[Z],ae=te-b[ee],se=-1!==[qa,Va].indexOf(y),oe=null!=(J=null==k?void 0:k[x])?J:0,ie=se?re:te-w[ne]-N[ne]-oe+j.altAxis,le=se?te+w[ne]+N[ne]-oe-j.altAxis:ae,ce=f&&se?function(e,t,n){var r=za(e,t,n);return r>n?n:r}(ie,te,le):za(f?ie:re,te,f?le:ae);O[x]=ce,P[x]=ce-te}t.modifiersData[r]=P}},requiresIfExists:["offset"]};function Ps(e,t,n){void 0===n&&(n=!1);var r,a,s=Ea(t),o=Ea(t)&&function(e){var t=e.getBoundingClientRect(),n=Ca(t.width)/e.offsetWidth||1,r=Ca(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),i=Aa(t),l=Ta(e,o),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(s||!s&&!n)&&(("body"!==Da(t)||bs(i))&&(c=(r=t)!==wa(r)&&Ea(r)?{scrollLeft:(a=r).scrollLeft,scrollTop:a.scrollTop}:ms(r)),Ea(t)?((u=Ta(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):i&&(u.x=gs(i))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function Cs(e){var t=new Map,n=new Set,r=[];function a(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&a(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||a(e)})),r}var Ts={placement:"bottom",modifiers:[],strategy:"absolute"};function Ss(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}const Rs=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,a=t.defaultOptions,s=void 0===a?Ts:a;return function(e,t,n){void 0===n&&(n=s);var a,o,i={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ts,s),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:i,setOptions:function(n){var a="function"==typeof n?n(i.options):n;d(),i.options=Object.assign({},s,i.options,a),i.scrollParents={reference:Na(e)?hs(e):e.contextElement?hs(e.contextElement):[],popper:hs(t)};var o,c,p=function(e){var t=Cs(e);return ns.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((o=[].concat(r,i.options.modifiers),c=o.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return i.orderedModifiers=p.filter((function(e){return e.enabled})),i.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,a=e.effect;if("function"==typeof a){var s=a({state:i,name:t,instance:u,options:r});l.push(s||function(){})}})),u.update()},forceUpdate:function(){if(!c){var e=i.elements,t=e.reference,n=e.popper;if(Ss(t,n)){i.rects={reference:Ps(t,Fa(n),"fixed"===i.options.strategy),popper:Sa(n)},i.reset=!1,i.placement=i.options.placement,i.orderedModifiers.forEach((function(e){return i.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<i.orderedModifiers.length;r++)if(!0!==i.reset){var a=i.orderedModifiers[r],s=a.fn,o=a.options,l=void 0===o?{}:o,d=a.name;"function"==typeof s&&(i=s({state:i,options:l,name:d,instance:u})||i)}else i.reset=!1,r=-1}}},update:(a=function(){return new Promise((function(e){u.forceUpdate(),e(i)}))},function(){return o||(o=new Promise((function(e){Promise.resolve().then((function(){o=void 0,e(a())}))}))),o}),destroy:function(){d(),c=!0}};if(!Ss(e,t))return u;function d(){l.forEach((function(e){return e()})),l=[]}return u.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,a=t.rects.popper,s=t.modifiersData.preventOverflow,o=Os(t,{elementContext:"reference"}),i=Os(t,{altBoundary:!0}),l=Ns(o,r),c=Ns(i,a,s),u=Es(l),d=Es(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=xs({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},is,cs,js,ws,ks,rs]}),Ds=["enabled","placement","strategy","modifiers"],Ls={name:"applyStyles",enabled:!1,phase:"afterWrite",fn:()=>{}},Is={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:({state:e})=>()=>{const{reference:t,popper:n}=e.elements;if("removeAttribute"in t){const e=(t.getAttribute("aria-describedby")||"").split(",").filter((e=>e.trim()!==n.id));e.length?t.setAttribute("aria-describedby",e.join(",")):t.removeAttribute("aria-describedby")}},fn:({state:e})=>{var t;const{popper:n,reference:r}=e.elements,a=null==(t=n.getAttribute("role"))?void 0:t.toLowerCase();if(n.id&&"tooltip"===a&&"setAttribute"in r){const e=r.getAttribute("aria-describedby");if(e&&-1!==e.split(",").indexOf(n.id))return;r.setAttribute("aria-describedby",e?`${e},${n.id}`:n.id)}}},As=[],$s=function(e,t,n={}){let{enabled:r=!0,placement:a="bottom",strategy:s="absolute",modifiers:o=As}=n,l=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(n,Ds);const c=(0,i.useRef)(o),u=(0,i.useRef)(),d=(0,i.useCallback)((()=>{var e;null==(e=u.current)||e.update()}),[]),p=(0,i.useCallback)((()=>{var e;null==(e=u.current)||e.forceUpdate()}),[]),[f,m]=(y=(0,i.useState)({placement:a,update:d,forceUpdate:p,attributes:{},styles:{popper:{},arrow:{}}}),h=He(),[y[0],(0,i.useCallback)((function(e){if(h())return y[1](e)}),[h,y[1]])]),g=(0,i.useMemo)((()=>({name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:({state:e})=>{const t={},n={};Object.keys(e.elements).forEach((r=>{t[r]=e.styles[r],n[r]=e.attributes[r]})),m({state:e,styles:t,attributes:n,update:d,forceUpdate:p,placement:e.placement})}})),[d,p,m]),b=(0,i.useMemo)((()=>(xa(c.current,o)||(c.current=o),c.current)),[o]);var y,h;return(0,i.useEffect)((()=>{u.current&&r&&u.current.setOptions({placement:a,strategy:s,modifiers:[...b,g,Ls]})}),[s,a,g,r,b]),(0,i.useEffect)((()=>{if(r&&null!=e&&null!=t)return u.current=Rs(e,t,Object.assign({},l,{placement:a,strategy:s,modifiers:[...b,Is,g]})),()=>{null!=u.current&&(u.current.destroy(),u.current=void 0,m((e=>Object.assign({},e,{attributes:{},styles:{popper:{}}}))))}}),[r,e,t]),f};function Ms(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}var Fs=n(3),Bs=n.n(Fs);const zs=()=>{},Ks=e=>e&&("current"in e?e.current:e),Hs={click:"mousedown",mouseup:"mousedown",pointerup:"pointerdown"},qs=function(e,t=zs,{disabled:n,clickTrigger:r="click"}={}){const a=(0,i.useRef)(!1),s=(0,i.useRef)(!1),o=(0,i.useCallback)((t=>{const n=Ks(e);var r;Bs()(!!n,"ClickOutside captured a close event but does not have a ref to compare it to. useClickOutside(), should be passed a ref that resolves to a DOM node"),a.current=!n||!!((r=t).metaKey||r.altKey||r.ctrlKey||r.shiftKey)||!function(e){return 0===e.button}(t)||!!Ms(n,t.target)||s.current,s.current=!1}),[e]),l=ze((t=>{const n=Ks(e);n&&Ms(n,t.target)&&(s.current=!0)})),c=ze((e=>{a.current||t(e)}));(0,i.useEffect)((()=>{if(n||null==e)return;const t=P(Ks(e));let a=(t.defaultView||window).event,s=null;Hs[r]&&(s=J(t,Hs[r],l,!0));const i=J(t,r,o,!0),u=J(t,r,(e=>{e!==a?c(e):a=void 0}));let d=[];return"ontouchstart"in t.documentElement&&(d=[].slice.call(t.body.children).map((e=>J(e,"mousemove",zs)))),()=>{null==s||s(),i(),u(),d.forEach((e=>e()))}}),[e,n,r,o,l,c])};function Ws(e={}){return Array.isArray(e)?e:Object.keys(e).map((t=>(e[t].name=t,e[t])))}function Us({enabled:e,enableEvents:t,placement:n,flip:r,offset:a,fixed:s,containerPadding:o,arrowElement:i,popperConfig:l={}}){var c,u,d,p;const f=function(e){const t={};return Array.isArray(e)?(null==e||e.forEach((e=>{t[e.name]=e})),t):e||t}(l.modifiers);return Object.assign({},l,{placement:n,enabled:e,strategy:s?"fixed":l.strategy,modifiers:Ws(Object.assign({},f,{eventListeners:{enabled:t},preventOverflow:Object.assign({},f.preventOverflow,{options:o?Object.assign({padding:o},null==(c=f.preventOverflow)?void 0:c.options):null==(u=f.preventOverflow)?void 0:u.options}),offset:{options:Object.assign({offset:a},null==(d=f.offset)?void 0:d.options)},arrow:Object.assign({},f.arrow,{enabled:!!i,options:Object.assign({},null==(p=f.arrow)?void 0:p.options,{element:i})}),flip:Object.assign({enabled:!!r},f.flip)}))})}const Vs=["children"],Gs=()=>{};function Ys(e={}){const t=(0,i.useContext)(ha),[n,r]=Ke(),a=(0,i.useRef)(!1),{flip:s,offset:o,rootCloseEvent:l,fixed:c=!1,placement:u,popperConfig:d={},enableEventListeners:p=!0,usePopper:f=!!t}=e,m=null==(null==t?void 0:t.show)?!!e.show:t.show;m&&!a.current&&(a.current=!0);const{placement:g,setMenu:b,menuElement:y,toggleElement:h}=t||{},v=$s(h,y,Us({placement:u||g||"bottom-start",enabled:f,enableEvents:null==p?m:p,offset:o,flip:s,fixed:c,arrowElement:n,popperConfig:d})),_=Object.assign({ref:b||Gs,"aria-labelledby":null==h?void 0:h.id},v.attributes.popper,{style:v.styles.popper}),x={show:m,placement:g,hasShown:a.current,toggle:null==t?void 0:t.toggle,popper:f?v:null,arrowProps:f?Object.assign({ref:r},v.attributes.arrow,{style:v.styles.arrow}):{}};return qs(y,(e=>{null==t||t.toggle(!1,e)}),{clickTrigger:l,disabled:!m}),[_,x]}function Xs(e){let{children:t}=e,n=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,Vs);const[r,a]=Ys(n);return(0,x.jsx)(x.Fragment,{children:t(r,a)})}Xs.displayName="DropdownMenu",Xs.defaultProps={usePopper:!0};const Qs=Xs;function Js(e,t,n,r){Object.defineProperty(e,t,{get:n,set:r,enumerable:!0,configurable:!0})}var Zs={};Js(Zs,"SSRProvider",(()=>no)),Js(Zs,"useSSRSafeId",(()=>ao)),Js(Zs,"useIsSSR",(()=>so));const eo={prefix:String(Math.round(1e10*Math.random())),current:0},to=l().createContext(eo);function no(e){let t=(0,i.useContext)(to),n=(0,i.useMemo)((()=>({prefix:t===eo?"":`${t.prefix}-${++t.current}`,current:0})),[t]);return l().createElement(to.Provider,{value:n},e.children)}let ro=Boolean("undefined"!=typeof window&&window.document&&window.document.createElement);function ao(e){let t=(0,i.useContext)(to);return t!==eo||ro||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server."),(0,i.useMemo)((()=>e||`react-aria${t.prefix}-${++t.current}`),[e])}function so(){let e=(0,i.useContext)(to)!==eo,[t,n]=(0,i.useState)(e);return"undefined"!=typeof window&&e&&(0,i.useLayoutEffect)((()=>{n(!1)}),[]),t}const oo=e=>{var t;return"menu"===(null==(t=e.getAttribute("role"))?void 0:t.toLowerCase())},io=()=>{};function lo(){const e=ao(),{show:t=!1,toggle:n=io,setToggle:r,menuElement:a}=(0,i.useContext)(ha)||{},s=(0,i.useCallback)((e=>{n(!t,e)}),[t,n]),o={id:e,ref:r||io,onClick:s,"aria-expanded":!!t};return a&&oo(a)&&(o["aria-haspopup"]=!0),[o,{show:t,toggle:n}]}function co({children:e}){const[t,n]=lo();return(0,x.jsx)(x.Fragment,{children:e(t,n)})}co.displayName="DropdownToggle";const uo=co,po=(e,t=null)=>null!=e?String(e):t||null,fo=i.createContext(null),mo=i.createContext(null);mo.displayName="NavContext";const go=mo;function bo(e){return`data-rr-ui-${e}`}const yo=["eventKey","disabled","onClick","active","as"];function ho({key:e,href:t,active:n,disabled:r,onClick:a}){const s=(0,i.useContext)(fo),o=(0,i.useContext)(go),{activeKey:l}=o||{},c=po(e,t),u=null==n&&null!=e?po(l)===c:n;return[{onClick:ze((e=>{r||(null==a||a(e),s&&!e.isPropagationStopped()&&s(c,e))})),"aria-disabled":r||void 0,"aria-selected":u,[bo("dropdown-item")]:""},{isActive:u}]}const vo=i.forwardRef(((e,t)=>{let{eventKey:n,disabled:r,onClick:a,active:s,as:o=Xe}=e,i=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,yo);const[l]=ho({key:n,href:i.href,disabled:r,onClick:a,active:s});return(0,x.jsx)(o,Object.assign({},i,{ref:t},l))}));vo.displayName="DropdownItem";const _o=vo,xo=(0,i.createContext)(U?window:void 0);function Oo(){return(0,i.useContext)(xo)}function wo(){const e=ya(),t=(0,i.useRef)(null),n=(0,i.useCallback)((n=>{t.current=n,e()}),[e]);return[t,n]}function No({defaultShow:e,show:t,onSelect:n,onToggle:r,itemSelector:a=`* [${bo("dropdown-item")}]`,focusFirstItemOnShow:s,placement:o="bottom-start",children:l}){const c=Oo(),[u,d]=v(t,e,r),[p,f]=wo(),m=p.current,[g,b]=wo(),y=g.current,h=qe(u),_=(0,i.useRef)(null),O=(0,i.useRef)(!1),w=(0,i.useContext)(fo),N=(0,i.useCallback)(((e,t,n=(null==t?void 0:t.type))=>{d(e,{originalEvent:t,source:n})}),[d]),E=ze(((e,t)=>{null==n||n(e,t),N(!1,t,"select"),t.isPropagationStopped()||null==w||w(e,t)})),j=(0,i.useMemo)((()=>({toggle:N,placement:o,show:u,menuElement:m,toggleElement:y,setMenu:f,setToggle:b})),[N,o,u,m,y,f,b]);m&&h&&!u&&(O.current=m.contains(m.ownerDocument.activeElement));const k=ze((()=>{y&&y.focus&&y.focus()})),P=ze((()=>{const e=_.current;let t=s;if(null==t&&(t=!(!p.current||!oo(p.current))&&"keyboard"),!1===t||"keyboard"===t&&!/^key.+$/.test(e))return;const n=ba(p.current,a)[0];n&&n.focus&&n.focus()}));(0,i.useEffect)((()=>{u?P():O.current&&(O.current=!1,k())}),[u,O,k,P]),(0,i.useEffect)((()=>{_.current=null}));const C=(e,t)=>{if(!p.current)return null;const n=ba(p.current,a);let r=n.indexOf(e)+t;return r=Math.max(0,Math.min(r,n.length)),n[r]};return function(e,t,n,r){void 0===r&&(r=!1);var a=ze((e=>{var t,n;const{key:r}=e,a=e.target,s=null==(t=p.current)?void 0:t.contains(a),o=null==(n=g.current)?void 0:n.contains(a);if(/input|textarea/i.test(a.tagName)&&(" "===r||"Escape"!==r&&s||"Escape"===r&&"search"===a.type))return;if(!s&&!o)return;if(!("Tab"!==r||p.current&&u))return;_.current=e.type;const i={originalEvent:e,source:e.type};switch(r){case"ArrowUp":{const t=C(a,-1);return t&&t.focus&&t.focus(),void e.preventDefault()}case"ArrowDown":if(e.preventDefault(),u){const e=C(a,1);e&&e.focus&&e.focus()}else d(!0,i);return;case"Tab":X(a.ownerDocument,"keyup",(e=>{var t;("Tab"!==e.key||e.target)&&null!=(t=p.current)&&t.contains(e.target)||d(!1,i)}),{once:!0});break;case"Escape":"Escape"===r&&(e.preventDefault(),e.stopPropagation()),d(!1,i)}}));(0,i.useEffect)((function(){var n="function"==typeof e?e():e;return n.addEventListener(t,a,r),function(){return n.removeEventListener(t,a,r)}}),[e])}((0,i.useCallback)((()=>c.document),[c]),"keydown"),(0,x.jsx)(fo.Provider,{value:E,children:(0,x.jsx)(ha.Provider,{value:j,children:l})})}xo.Provider,No.displayName="Dropdown",No.Menu=Qs,No.Toggle=uo,No.Item=_o;const Eo=No,jo=i.createContext({});jo.displayName="DropdownContext";const ko=jo,Po=i.forwardRef((({bsPrefix:e,className:t,eventKey:n,disabled:r=!1,onClick:a,active:s,as:o=Ze,...i},l)=>{const c=E(e,"dropdown-item"),[u,d]=ho({key:n,href:i.href,disabled:r,onClick:a,active:s});return(0,x.jsx)(o,{...i,...u,ref:l,className:b()(t,c,d.isActive&&"active",r&&"disabled")})}));Po.displayName="DropdownItem";const Co=Po,To=i.createContext(null);To.displayName="InputGroupContext";const So=To,Ro=i.createContext(null);Ro.displayName="NavbarContext";const Do=Ro;function Lo(e,t){return e}function Io(e,t,n){let r=e?n?"bottom-start":"bottom-end":n?"bottom-end":"bottom-start";return"up"===t?r=e?n?"top-start":"top-end":n?"top-end":"top-start":"end"===t?r=e?n?"left-end":"right-end":n?"left-start":"right-start":"start"===t&&(r=e?n?"right-end":"left-end":n?"right-start":"left-start"),r}const Ao=i.forwardRef((({bsPrefix:e,className:t,align:n,rootCloseEvent:r,flip:a,show:s,renderOnMount:o,as:l="div",popperConfig:c,variant:u,...d},p)=>{let f=!1;const m=(0,i.useContext)(Do),g=E(e,"dropdown-menu"),{align:y,drop:h,isRTL:v}=(0,i.useContext)(ko);n=n||y;const _=(0,i.useContext)(So),O=[];if(n)if("object"==typeof n){const e=Object.keys(n);if(e.length){const t=e[0],r=n[t];f="start"===r,O.push(`${g}-${t}-${r}`)}}else"end"===n&&(f=!0);const w=Io(f,h,v),[N,{hasShown:j,popper:k,show:P,toggle:C}]=Ys({flip:a,rootCloseEvent:r,show:s,usePopper:!m&&0===O.length,offset:[0,2],popperConfig:c,placement:w});if(N.ref=se(Lo(p),N.ref),Ue((()=>{P&&(null==k||k.update())}),[P]),!j&&!o&&!_)return null;"string"!=typeof l&&(N.show=P,N.close=()=>null==C?void 0:C(!1),N.align=n);let T=d.style;return null!=k&&k.placement&&(T={...d.style,...N.style},d["x-placement"]=k.placement),(0,x.jsx)(l,{...d,...N,style:T,...(O.length||m)&&{"data-bs-popper":"static"},className:b()(t,g,P&&"show",f&&`${g}-end`,u&&`${g}-${u}`,...O)})}));Ao.displayName="DropdownMenu",Ao.defaultProps={flip:!0};const $o=Ao,Mo=i.forwardRef((({bsPrefix:e,split:t,className:n,childBsPrefix:r,as:a=Yt,...s},o)=>{const l=E(e,"dropdown-toggle"),c=(0,i.useContext)(ha),u=(0,i.useContext)(So);void 0!==r&&(s.bsPrefix=r);const[d]=lo();return d.ref=se(d.ref,Lo(o)),(0,x.jsx)(a,{className:b()(n,l,t&&`${l}-split`,!!u&&(null==c?void 0:c.show)&&"show"),...d,...s})}));Mo.displayName="DropdownToggle";const Fo=Mo,Bo=ct("dropdown-header",{defaultProps:{role:"heading"}}),zo=ct("dropdown-divider",{Component:"hr",defaultProps:{role:"separator"}}),Ko=ct("dropdown-item-text",{Component:"span"}),Ho=i.forwardRef(((e,t)=>{const{bsPrefix:n,drop:r,show:a,className:s,align:o,onSelect:l,onToggle:c,focusFirstItemOnShow:u,as:d="div",navbar:p,autoClose:f,...m}=_(e,{show:"onToggle"}),g=(0,i.useContext)(So),y=E(n,"dropdown"),h=k(),v=ze(((e,t)=>{var n;t.originalEvent.currentTarget!==document||"keydown"===t.source&&"Escape"!==t.originalEvent.key||(t.source="rootClose"),n=t.source,(!1===f?"click"===n:"inside"===f?"rootClose"!==n:"outside"!==f||"select"!==n)&&(null==c||c(e,t))})),O=Io("end"===o,r,h),w=(0,i.useMemo)((()=>({align:o,drop:r,isRTL:h})),[o,r,h]);return(0,x.jsx)(ko.Provider,{value:w,children:(0,x.jsx)(Eo,{placement:O,show:a,onSelect:l,onToggle:v,focusFirstItemOnShow:u,itemSelector:`.${y}-item:not(.disabled):not(:disabled)`,children:g?m.children:(0,x.jsx)(d,{...m,ref:t,className:b()(s,a&&"show",(!r||"down"===r)&&y,"up"===r&&"dropup","end"===r&&"dropend","start"===r&&"dropstart")})})})}));Ho.displayName="Dropdown",Ho.defaultProps={navbar:!1,align:"start",autoClose:!0};const qo=Object.assign(Ho,{Toggle:Fo,Menu:$o,Item:Co,ItemText:Ko,Divider:zo,Header:Bo});n(5);const Wo=i.createContext(null),Uo=["as","active","eventKey"];function Vo({key:e,onClick:t,active:n,id:r,role:a,disabled:s}){const o=(0,i.useContext)(fo),l=(0,i.useContext)(go),c=(0,i.useContext)(Wo);let u=n;const d={role:a};if(l){a||"tablist"!==l.role||(d.role="tab");const t=l.getControllerId(null!=e?e:null),s=l.getControlledId(null!=e?e:null);d[bo("event-key")]=e,d.id=t||r,u=null==n&&null!=e?l.activeKey===e:n,!u&&(null!=c&&c.unmountOnExit||null!=c&&c.mountOnEnter)||(d["aria-controls"]=s)}return"tab"===d.role&&(s&&(d.tabIndex=-1,d["aria-disabled"]=!0),u?d["aria-selected"]=u:d.tabIndex=-1),d.onClick=ze((n=>{s||(null==t||t(n),null!=e&&o&&!n.isPropagationStopped()&&o(e,n))})),[d,{isActive:u}]}const Go=i.forwardRef(((e,t)=>{let{as:n=Xe,active:r,eventKey:a}=e,s=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,Uo);const[o,i]=Vo(Object.assign({key:po(a,s.href),active:r},s));return o[bo("active")]=i.isActive,(0,x.jsx)(n,Object.assign({},s,o,{ref:t}))}));Go.displayName="NavItem";const Yo=Go,Xo=["as","onSelect","activeKey","role","onKeyDown"],Qo=()=>{},Jo=bo("event-key"),Zo=i.forwardRef(((e,t)=>{let{as:n="div",onSelect:r,activeKey:a,role:s,onKeyDown:o}=e,l=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,Xo);const c=ya(),u=(0,i.useRef)(!1),d=(0,i.useContext)(fo),p=(0,i.useContext)(Wo);let f,m;p&&(s=s||"tablist",a=p.activeKey,f=p.getControlledId,m=p.getControllerId);const g=(0,i.useRef)(null),b=e=>{const t=g.current;if(!t)return null;const n=ba(t,`[${Jo}]:not([aria-disabled=true])`),r=t.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;const a=n.indexOf(r);if(-1===a)return null;let s=a+e;return s>=n.length&&(s=0),s<0&&(s=n.length-1),n[s]},y=(e,t)=>{null!=e&&(null==r||r(e,t),null==d||d(e,t))};(0,i.useEffect)((()=>{if(g.current&&u.current){const e=g.current.querySelector(`[${Jo}][aria-selected=true]`);null==e||e.focus()}u.current=!1}));const h=se(t,g);return(0,x.jsx)(fo.Provider,{value:y,children:(0,x.jsx)(go.Provider,{value:{role:s,activeKey:po(a),getControlledId:f||Qo,getControllerId:m||Qo},children:(0,x.jsx)(n,Object.assign({},l,{onKeyDown:e=>{if(null==o||o(e),!p)return;let t;switch(e.key){case"ArrowLeft":case"ArrowUp":t=b(-1);break;case"ArrowRight":case"ArrowDown":t=b(1);break;default:return}t&&(e.preventDefault(),y(t.dataset[("EventKey","rrUiEventKey")]||null,e),u.current=!0,c())},ref:h,role:s}))})})}));Zo.displayName="Nav";const ei=Object.assign(Zo,{Item:Yo}),ti=ct("nav-item"),ni=i.forwardRef((({bsPrefix:e,className:t,as:n=Ze,active:r,eventKey:a,...s},o)=>{e=E(e,"nav-link");const[i,l]=Vo({key:po(a,s.href),active:r,...s});return(0,x.jsx)(n,{...s,...i,ref:o,className:b()(t,e,s.disabled&&"disabled",l.isActive&&"active")})}));ni.displayName="NavLink",ni.defaultProps={disabled:!1};const ri=ni,ai=i.forwardRef(((e,t)=>{const{as:n="div",bsPrefix:r,variant:a,fill:s,justify:o,navbar:l,navbarScroll:c,className:u,activeKey:d,...p}=_(e,{activeKey:"onSelect"}),f=E(r,"nav");let m,g,y=!1;const h=(0,i.useContext)(Do),v=(0,i.useContext)(ln);return h?(m=h.bsPrefix,y=null==l||l):v&&({cardHeaderBsPrefix:g}=v),(0,x.jsx)(ei,{as:n,ref:t,activeKey:d,className:b()(u,{[f]:!y,[`${m}-nav`]:y,[`${m}-nav-scroll`]:y&&c,[`${g}-${a}`]:!!g,[`${f}-${a}`]:!!a,[`${f}-fill`]:s,[`${f}-justified`]:o}),...p})}));ai.displayName="Nav",ai.defaultProps={justify:!1,fill:!1};const si=Object.assign(ai,{Item:ti,Link:ri});var oi=l().createContext({}),ii=["caret","bsPrefix","split","className","childBsPrefix","as"];const li=l().forwardRef((function(t,n){var r=t.caret,a=t.bsPrefix,s=t.split,c=t.className,u=t.childBsPrefix,d=t.as,p=void 0===d?Yt:d,f=o(t,ii),m=E(a,"dropdown-toggle"),g=(0,i.useContext)(ha),y=(0,i.useContext)(So);void 0!==u&&(f.bsPrefix=u);var h=ma(lo(),1)[0];return h.ref=se(h.ref,Lo(n)),l().createElement(p,e({className:b()(c,r&&m,s&&"".concat(m,"-split"),!!y&&(null==g?void 0:g.show)&&"show")},h,f))}));var ci=["children","nav","label","disabled","caret","in_navbar","addon_type","size","right","align_end","menu_variant","direction","loading_state","color","group","toggle_style","toggleClassName","toggle_class_name","className","class_name"];function ui(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function di(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ui(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ui(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var pi=function(t){var n=t.children,r=t.nav,a=t.label,s=t.disabled,c=t.caret,u=t.in_navbar,d=(t.addon_type,t.size),p=t.right,f=t.align_end,g=t.menu_variant,b=t.direction,y=t.loading_state,h=t.color,v=t.group,_=t.toggle_style,x=t.toggleClassName,O=t.toggle_class_name,w=t.className,N=t.class_name,E=o(t,ci),j=ma((0,i.useState)(!1),2),k=j[0],P=j[1],C=bt.has(h)||"link"===h,T=function(){s||P(!k)};return l().createElement(oi.Provider,{value:{toggle:T,isOpen:k}},l().createElement(qo,e({as:r?si.Item:v?en:void 0,show:k,disabled:s,navbar:u,className:N||w,drop:"left"===b?"start":"right"===b?"end":b,onToggle:function(e,t){t&&"select"===t.source||P(e)},align:f||p?"end":"start"},m(["setProps"],E),{"data-dash-is-loading":y&&y.is_loading||void 0}),l().createElement(li,{caret:c,as:r?si.Link:void 0,onClick:T,disabled:s,size:d,variant:C?h:void 0,style:C?_:di({backgroundColor:h},_),className:O||x},a),l().createElement(qo.Menu,{renderOnMount:!0,variant:"dark"===g?"dark":void 0},n)))};pi.defaultProps={caret:!0,disabled:!1,menu_variant:"light"},pi.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,label:u().string,direction:u().oneOf(["down","start","end","up","left","right","end"]),align_end:u().bool,right:u().bool,in_navbar:u().bool,addon_type:u().oneOfType([u().bool,u().oneOf(["prepend","append"])]),disabled:u().bool,nav:u().bool,caret:u().bool,color:u().string,menu_variant:u().oneOf(["light","dark"]),toggle_style:u().object,toggle_class_name:u().string,toggleClassName:u().string,size:u().oneOf(["sm","md","lg"]),loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),group:u().bool};const fi=pi;var mi=["children","href","loading_state","target","disabled","n_clicks","toggle","setProps","className","class_name","header","divider"],gi=function(t){var n=t.children,r=t.href,a=t.loading_state,s=t.target,c=t.disabled,u=t.n_clicks,d=t.toggle,p=t.setProps,f=t.className,g=t.class_name,b=t.header,y=t.divider,h=o(t,mi),v=(0,i.useContext)(oi),_=r&&!c;return h[_?"preOnClick":"onClick"]=function(e){return function(e){!c&&p&&p({n_clicks:u+1,n_clicks_timestamp:Date.now()}),d&&v.isOpen&&v.toggle(e)}(e)},b?l().createElement(qo.Header,null,n):y?l().createElement(qo.Divider,null):l().createElement(qo.Item,e({as:_?It:"button",href:_?r:void 0,disabled:c,target:_?s:void 0,className:g||f},m(["setProps"],h),{"data-dash-is-loading":a&&a.is_loading||void 0}),n)};gi.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,active:u().bool,disabled:u().bool,divider:u().bool,header:u().bool,href:u().string,toggle:u().bool,external_link:u().bool,n_clicks:u().number,n_clicks_timestamp:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),target:u().string},gi.defaultProps={n_clicks:0,n_clicks_timestamp:-1,toggle:!0};const bi=gi;var yi=["children","is_in","loading_state","style","className","class_name","tag"];function hi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?hi(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):hi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _i=l().forwardRef((function(t,n){var r=t.children,a=t.is_in,s=t.loading_state,c=t.style,u=t.className,d=t.class_name,p=t.tag,f=o(t,yi),g=ma((0,i.useState)(!a),2),b=g[0],y=g[1];return l().createElement(nt,e({in:a,style:b?vi({visibility:"hidden"},c):c,onEnter:function(){return y(!1)},onExited:function(){return y(!0)},className:d||u,as:p},m(["setProps"],f),{"data-dash-is-loading":s&&s.is_loading||void 0}),l().createElement("div",{ref:n},r))}));_i.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,is_in:u().bool,timeout:u().oneOfType([u().number,u().shape({enter:u().number,exit:u().number}).isRequired]),appear:u().bool,enter:u().bool,exit:u().bool,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const xi=_i,Oi={type:u().string,tooltip:u().bool,as:u().elementType},wi=i.forwardRef((({as:e="div",className:t,type:n="valid",tooltip:r=!1,...a},s)=>(0,x.jsx)(e,{...a,ref:s,className:b()(t,`${n}-${r?"tooltip":"feedback"}`)})));wi.displayName="Feedback",wi.propTypes=Oi;const Ni=wi,Ei=i.createContext({}),ji=i.forwardRef((({id:e,bsPrefix:t,className:n,type:r="checkbox",isValid:a=!1,isInvalid:s=!1,as:o="input",...l},c)=>{const{controlId:u}=(0,i.useContext)(Ei);return t=E(t,"form-check-input"),(0,x.jsx)(o,{...l,ref:c,type:r,id:e||u,className:b()(n,t,a&&"is-valid",s&&"is-invalid")})}));ji.displayName="FormCheckInput";const ki=ji,Pi=i.forwardRef((({bsPrefix:e,className:t,htmlFor:n,...r},a)=>{const{controlId:s}=(0,i.useContext)(Ei);return e=E(e,"form-check-label"),(0,x.jsx)("label",{...r,ref:a,htmlFor:n||s,className:b()(t,e)})}));Pi.displayName="FormCheckLabel";const Ci=Pi,Ti=i.forwardRef((({id:e,bsPrefix:t,bsSwitchPrefix:n,inline:r=!1,disabled:a=!1,isValid:s=!1,isInvalid:o=!1,feedbackTooltip:l=!1,feedback:c,feedbackType:u,className:d,style:p,title:f="",type:m="checkbox",label:g,children:y,as:h="input",...v},_)=>{t=E(t,"form-check"),n=E(n,"form-switch");const{controlId:O}=(0,i.useContext)(Ei),w=(0,i.useMemo)((()=>({controlId:e||O})),[O,e]),N=!y&&null!=g&&!1!==g||function(e,t){return i.Children.toArray(e).some((e=>i.isValidElement(e)&&e.type===t))}(y,Ci),j=(0,x.jsx)(ki,{...v,type:"switch"===m?"checkbox":m,ref:_,isValid:s,isInvalid:o,disabled:a,as:h});return(0,x.jsx)(Ei.Provider,{value:w,children:(0,x.jsx)("div",{style:p,className:b()(d,N&&t,r&&`${t}-inline`,"switch"===m&&n),children:y||(0,x.jsxs)(x.Fragment,{children:[j,N&&(0,x.jsx)(Ci,{title:f,children:g}),c&&(0,x.jsx)(Ni,{type:u,tooltip:l,children:c})]})})})}));Ti.displayName="FormCheck";const Si=Object.assign(Ti,{Input:ki,Label:Ci}),Ri=i.forwardRef((({bsPrefix:e,type:t,size:n,htmlSize:r,id:a,className:s,isValid:o=!1,isInvalid:l=!1,plaintext:c,readOnly:u,as:d="input",...p},f)=>{const{controlId:m}=(0,i.useContext)(Ei);let g;return e=E(e,"form-control"),g=c?{[`${e}-plaintext`]:!0}:{[e]:!0,[`${e}-${n}`]:n},(0,x.jsx)(d,{...p,type:t,size:r,ref:f,readOnly:u,id:a||m,className:b()(s,g,o&&"is-valid",l&&"is-invalid","color"===t&&`${e}-color`)})}));Ri.displayName="FormControl";const Di=Object.assign(Ri,{Feedback:Ni}),Li=ct("form-floating"),Ii=i.forwardRef((({controlId:e,as:t="div",...n},r)=>{const a=(0,i.useMemo)((()=>({controlId:e})),[e]);return(0,x.jsx)(Ei.Provider,{value:a,children:(0,x.jsx)(t,{...n,ref:r})})}));Ii.displayName="FormGroup";const Ai=Ii,$i=i.forwardRef((({as:e="label",bsPrefix:t,column:n,visuallyHidden:r,className:a,htmlFor:s,...o},l)=>{const{controlId:c}=(0,i.useContext)(Ei);t=E(t,"form-label");let u="col-form-label";"string"==typeof n&&(u=`${u} ${u}-${n}`);const d=b()(a,t,r&&"visually-hidden",n&&u);return s=s||c,n?(0,x.jsx)(Zr,{ref:l,as:"label",className:d,htmlFor:s,...o}):(0,x.jsx)(e,{ref:l,className:d,htmlFor:s,...o})}));$i.displayName="FormLabel",$i.defaultProps={column:!1,visuallyHidden:!1};const Mi=$i,Fi=i.forwardRef((({bsPrefix:e,className:t,id:n,...r},a)=>{const{controlId:s}=(0,i.useContext)(Ei);return e=E(e,"form-range"),(0,x.jsx)("input",{...r,type:"range",ref:a,className:b()(t,e),id:n||s})}));Fi.displayName="FormRange";const Bi=Fi,zi=i.forwardRef((({bsPrefix:e,size:t,htmlSize:n,className:r,isValid:a=!1,isInvalid:s=!1,id:o,...l},c)=>{const{controlId:u}=(0,i.useContext)(Ei);return e=E(e,"form-select"),(0,x.jsx)("select",{...l,size:n,ref:c,className:b()(r,e,t&&`${e}-${t}`,a&&"is-valid",s&&"is-invalid"),id:o||u})}));zi.displayName="FormSelect";const Ki=zi,Hi=i.forwardRef((({bsPrefix:e,className:t,as:n="small",muted:r,...a},s)=>(e=E(e,"form-text"),(0,x.jsx)(n,{...a,ref:s,className:b()(t,e,r&&"text-muted")}))));Hi.displayName="FormText";const qi=Hi,Wi=i.forwardRef(((e,t)=>(0,x.jsx)(Si,{...e,ref:t,type:"switch"})));Wi.displayName="Switch";const Ui=Object.assign(Wi,{Input:Si.Input,Label:Si.Label}),Vi=i.forwardRef((({bsPrefix:e,className:t,children:n,controlId:r,label:a,...s},o)=>(e=E(e,"form-floating"),(0,x.jsxs)(Ai,{ref:o,className:b()(t,e),controlId:r,...s,children:[n,(0,x.jsx)("label",{htmlFor:r,children:a})]}))));Vi.displayName="FloatingLabel";const Gi=Vi,Yi={_ref:u().any,validated:u().bool,as:u().elementType},Xi=i.forwardRef((({className:e,validated:t,as:n="form",...r},a)=>(0,x.jsx)(n,{...r,ref:a,className:b()(e,t&&"was-validated")})));Xi.displayName="Form",Xi.propTypes=Yi;const Qi=Object.assign(Xi,{Group:Ai,Control:Di,Floating:Li,Check:Si,Switch:Ui,Label:Mi,Text:qi,Range:Bi,Select:Ki,FloatingLabel:Gi});var Ji=["children","loading_state","n_submit","prevent_default_on_submit","setProps","className","class_name"],Zi=function(t){var n=t.children,r=t.loading_state,a=t.n_submit,s=t.prevent_default_on_submit,i=t.setProps,c=t.className,u=t.class_name,d=o(t,Ji);return l().createElement(Qi,e({onSubmit:function(e){s&&e.preventDefault(),i&&i({n_submit:a+1,n_submit_timestamp:Date.now()})},className:u||c},m(["n_submit_timestamp"],d),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Zi.defaultProps={prevent_default_on_submit:!0,n_submit:0,n_submit_timestamp:-1},Zi.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,action:u().string,method:u().oneOf(["GET","POST"]),n_submit:u().number,n_submit_timestamp:u().number,prevent_default_on_submit:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const el=Zi;var tl=["children","loading_state","className","class_name"],nl=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,tl);return l().createElement(Di.Feedback,e({className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};nl.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,type:u().string,tooltip:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const rl=nl;var al=["children","html_for","className","class_name","loading_state"],sl=function(t){var n=t.children,r=t.html_for,a=t.className,s=t.class_name,i=t.loading_state,c=o(t,al);return l().createElement(Li,e({htmlFor:r,className:s||a},m(["setProps"],c),{"data-dash-is-loading":i&&i.is_loading||void 0}),n)};sl.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,html_for:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const ol=sl;var il=["children","loading_state","color","style","className","class_name"];function ll(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function cl(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ll(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ll(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ul=function(t){var n=t.children,r=t.loading_state,a=t.color,s=t.style,i=t.className,c=t.class_name,u=o(t,il),d=yt.has(a),p=b()(c||i,d&&"text-".concat(a));return l().createElement(qi,e({style:d?s:cl({color:a},s),className:p},m(["setProps"],u),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};ul.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,color:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const dl=ul,pl=p((function(e){return null==e}));var fl=n(10),ml=n.n(fl),gl=["type","value","n_blur","n_submit","valid","invalid","plaintext","size","html_size","setProps","debounce","loading_state","className","class_name","autoComplete","autocomplete","autoFocus","autofocus","inputMode","inputmode","maxLength","maxlength","minLength","minlength","readonly","tabIndex","tabindex"];function bl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function yl(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bl(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var hl=function(e){return ml()(e)?+e:NaN},vl=function(e,t){return e===t||isNaN(e)&&isNaN(t)},_l=function(t){var n=t.type,r=t.value,a=t.n_blur,s=t.n_submit,c=t.valid,u=t.invalid,d=t.plaintext,p=t.size,f=t.html_size,g=t.setProps,y=t.debounce,h=t.loading_state,v=t.className,_=t.class_name,x=t.autoComplete,O=t.autocomplete,w=t.autoFocus,N=t.autofocus,E=t.inputMode,j=t.inputmode,k=t.maxLength,P=t.maxlength,C=t.minLength,T=t.minlength,S=t.readonly,R=t.tabIndex,D=t.tabindex,L=o(t,gl),I=(0,i.useRef)(null),A=d?"form-control-plaintext":"form-control",$=b()(_||v,u&&"is-invalid",c&&"is-valid",!!p&&"form-control-".concat(p),A);(0,i.useEffect)((function(){if("number"===n){var e=I.current.value,t=I.current.checkValidity()?hl(e):NaN,a=hl(r);vl(a,t)||(I.current.value=pl(a)?a:r)}else{var s=I.current.value;r!==s&&(I.current.value=null!=r?r:"")}}),[r]);var M=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("number"===n){var t=I.current.value,a=I.current.checkValidity()?hl(t):NaN,s=hl(r);vl(s,a)?Object.keys(e).length&&g(e):g(yl(yl({},e),{},{value:a}))}else e.value=I.current.value,g(e)};return l().createElement("input",e({ref:I,type:n,className:$,onChange:function(){y||M()},onBlur:function(){if(g){var e={n_blur:a+1,n_blur_timestamp:Date.now()};y?M(e):g(e)}},onKeyPress:function(e){if(g&&"Enter"===e.key){var t={n_submit:s+1,n_submit_timestamp:Date.now()};y?M(t):g(t)}}},m(["n_blur_timestamp","n_submit_timestamp","persistence","persistence_type","persisted_props"],L),{valid:c?"true":void 0,invalid:u?"true":void 0,"data-dash-is-loading":h&&h.is_loading||void 0,autoComplete:O||x,autoFocus:N||w,inputMode:j||E,maxLength:P||k,minLength:T||C,readOnly:S,tabIndex:D||R,size:f}))};_l.propTypes={id:u().string,style:u().object,class_name:u().string,className:u().string,key:u().string,type:u().oneOf(["text","number","password","email","range","search","tel","url","hidden"]),value:u().oneOfType([u().string,u().number]),disabled:u().bool,autocomplete:u().string,autoComplete:u().string,autofocus:u().oneOfType([u().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),u().bool]),autoFocus:u().oneOfType([u().oneOf(["autoFocus","autofocus","AUTOFOCUS"]),u().bool]),inputmode:u().oneOf(["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","katakana","numeric","tel","email","url"]),inputMode:u().oneOf(["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","katakana","numeric","tel","email","url"]),list:u().string,max:u().oneOfType([u().string,u().number]),maxlength:u().oneOfType([u().string,u().number]),maxLength:u().oneOfType([u().string,u().number]),min:u().oneOfType([u().string,u().number]),minlength:u().oneOfType([u().string,u().number]),minLength:u().oneOfType([u().string,u().number]),step:u().oneOfType([u().string,u().number]),html_size:u().string,size:u().string,valid:u().bool,invalid:u().bool,required:u().oneOfType([u().oneOf(["required","REQUIRED"]),u().bool]),plaintext:u().bool,readonly:u().oneOfType([u().bool,u().oneOf(["readOnly","readonly","READONLY"])]),placeholder:u().oneOfType([u().string,u().number]),name:u().string,pattern:u().string,n_submit:u().number,n_submit_timestamp:u().number,n_blur:u().number,n_blur_timestamp:u().number,debounce:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),tabindex:u().string,tabIndex:u().string},_l.defaultProps={n_blur:0,n_blur_timestamp:-1,n_submit:0,n_submit_timestamp:-1,debounce:!1,persisted_props:["value"],persistence_type:"local",step:"any"};const xl=_l,Ol=ct("input-group-text",{Component:"span"}),wl=i.forwardRef((({bsPrefix:e,size:t,hasValidation:n,className:r,as:a="div",...s},o)=>{e=E(e,"input-group");const l=(0,i.useMemo)((()=>({})),[]);return(0,x.jsx)(So.Provider,{value:l,children:(0,x.jsx)(a,{ref:o,...s,className:b()(r,e,t&&`${e}-${t}`,n&&"has-validation")})})}));wl.displayName="InputGroup";const Nl=Object.assign(wl,{Text:Ol,Radio:e=>(0,x.jsx)(Ol,{children:(0,x.jsx)(ki,{type:"radio",...e})}),Checkbox:e=>(0,x.jsx)(Ol,{children:(0,x.jsx)(ki,{type:"checkbox",...e})})});var El=["children","loading_state","className","class_name"],jl=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,El);return l().createElement(Nl,e({className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};jl.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,size:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const kl=jl;var Pl=["children","loading_state","className","class_name"],Cl=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Pl);return l().createElement(Nl.Text,e({className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Cl.propTypes={id:u().string,children:u().node,style:u().object,key:u().string,class_name:u().string,className:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Tl=Cl;var Sl=["children","html_for","width","xs","sm","md","lg","xl","xxl","align","size","className","class_name","color","style","loading_state","check"];function Rl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dl(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Rl(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Rl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ll={start:"align-self-start",center:"align-self-center",end:"align-self-end"},Il=["width","xs","sm","md","lg","xl","xxl"],Al=function(t){var n=t.children,r=t.html_for,a=t.width,s=t.xs,i=t.sm,c=t.md,u=t.lg,d=t.xl,p=t.xxl,f=t.align,g=t.size,y=t.className,h=t.class_name,v=t.color,_=t.style,x=t.loading_state,O=t.check,w=o(t,Sl),N=yt.has(v),E=Il.filter((function(e){return t[e]}));[a,s,i,c,u,d,p].forEach((function(e){"object"===kt(e)&&null!==e&&(e.span=e.size)}));var j=f&&Ll[f],k=b()(h||y,E.length&&j,v&&N&&"text-".concat(v),O&&"form-check-label");return l().createElement(Mi,e({htmlFor:r,column:g||E.length>0,xs:s||a,sm:i,md:c,lg:u,xl:d,xxl:p,className:k,style:N?_:Dl({color:v},_)},m(["setProps"],w),{"data-dash-is-loading":x&&x.is_loading||void 0}),n)},$l=u().oneOfType([u().number,u().string]),Ml=u().oneOfType([u().string,u().number,u().shape({size:$l,order:$l,offset:$l})]);Al.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,hidden:u().bool,size:u().string,html_for:u().string,check:u().bool,width:Ml,xs:Ml,sm:Ml,md:Ml,lg:Ml,xl:Ml,align:u().oneOf(["start","center","end"]),color:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})},Al.defaultProps={align:"center"};const Fl=Al,Bl=i.forwardRef((({bsPrefix:e,active:t,disabled:n,eventKey:r,className:a,variant:s,action:o,as:i,...l},c)=>{e=E(e,"list-group-item");const[u,d]=Vo({key:po(r,l.href),active:t,...l}),p=ze((e=>{if(n)return e.preventDefault(),void e.stopPropagation();u.onClick(e)}));n&&void 0===l.tabIndex&&(l.tabIndex=-1,l["aria-disabled"]=!0);const f=i||(o?l.href?"a":"button":"div");return(0,x.jsx)(f,{ref:c,...l,...u,onClick:p,className:b()(a,e,d.isActive&&"active",n&&"disabled",s&&`${e}-${s}`,o&&`${e}-action`)})}));Bl.displayName="ListGroupItem";const zl=Bl,Kl=i.forwardRef(((e,t)=>{const{className:n,bsPrefix:r,variant:a,horizontal:s,numbered:o,as:i="div",...l}=_(e,{activeKey:"onSelect"}),c=E(r,"list-group");let u;return s&&(u=!0===s?"horizontal":`horizontal-${s}`),(0,x.jsx)(ei,{ref:t,...l,as:i,className:b()(n,c,a&&`${c}-${a}`,u&&`${c}-${u}`,o&&`${c}-numbered`)})}));Kl.displayName="ListGroup";const Hl=Object.assign(Kl,{Item:zl});var ql=["children","loading_state","className","class_name","flush","tag"],Wl=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.flush,c=t.tag,u=o(t,ql);return l().createElement(Hl,e({className:s||a,variant:i?"flush":null,as:c},m(["setProps"],u),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Wl.defaultProps={tag:"ul"},Wl.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,flush:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),horizontal:u().oneOfType([u().bool,u().string])};const Ul=Wl;var Vl=["children","disabled","href","loading_state","target","n_clicks","setProps","color","style","className","class_name"];function Gl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Yl(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gl(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Xl=function(t){var n=t.children,r=t.disabled,a=t.href,s=t.loading_state,i=t.target,c=t.n_clicks,u=t.setProps,d=t.color,p=t.style,f=t.className,g=t.class_name,b=o(t,Vl),y=bt.has(d),h=a&&!r;return b[h?"preOnClick":"onClick"]=function(){!r&&u&&u({n_clicks:c+1,n_clicks_timestamp:Date.now()})},l().createElement(zl,e({as:h?It:"li",href:a,target:h?i:void 0,disabled:r,variant:y?d:null,style:y?p:Yl({backgroundColor:d},p),className:g||f},m(["n_clicks_timestamp"],b),{"data-dash-is-loading":s&&s.is_loading||void 0}),n)};Xl.defaultProps={n_clicks:0,n_clicks_timestamp:-1},Xl.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,active:u().bool,disabled:u().bool,color:u().string,action:u().bool,href:u().string,external_link:u().bool,n_clicks:u().number,n_clicks_timestamp:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),target:u().string};const Ql=Xl;var Jl;function Zl(e){if((!Jl&&0!==Jl||e)&&U){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),Jl=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return Jl}function ec(e){void 0===e&&(e=P());try{var t=e.activeElement;return t&&t.nodeName?t:null}catch(t){return e.body}}const tc=bo("modal-open"),nc=class{constructor({ownerDocument:e,handleContainerOverflow:t=!0,isRTL:n=!1}={}){this.handleContainerOverflow=t,this.isRTL=n,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(e=document){const t=e.defaultView;return Math.abs(t.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){const t={overflow:"hidden"},n=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[n]:r.style[n]},e.scrollBarWidth&&(t[n]=`${parseInt(D(r,n)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(tc,""),D(r,t)}reset(){[...this.modals].forEach((e=>this.remove(e)))}removeContainerStyle(e){const t=this.getElement();t.removeAttribute(tc),Object.assign(t.style,e.style)}add(e){let t=this.modals.indexOf(e);return-1!==t||(t=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==t||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state))),t}remove(e){const t=this.modals.indexOf(e);-1!==t&&(this.modals.splice(t,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}},rc=(e,t)=>{var n;return U?null==e?(t||P()).body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),null!=(n=e)&&n.nodeType&&e||null):null};function ac(e,t){const n=Oo(),[r,a]=(0,i.useState)((()=>rc(e,null==n?void 0:n.document)));if(!r){const t=rc(e);t&&a(t)}return(0,i.useEffect)((()=>{t&&r&&t(r)}),[t,r]),(0,i.useEffect)((()=>{const t=rc(e);t!==r&&a(t)}),[e,r]),r}const sc=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","backdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let oc;const ic=(0,i.forwardRef)(((e,t)=>{let{show:n=!1,role:r="dialog",className:a,style:s,children:o,backdrop:l=!0,keyboard:c=!0,onBackdropClick:u,onEscapeKeyDown:d,transition:p,backdropTransition:f,autoFocus:m=!0,enforceFocus:g=!0,restoreFocus:b=!0,restoreFocusOptions:y,renderDialog:h,renderBackdrop:v=(e=>(0,x.jsx)("div",Object.assign({},e))),manager:_,container:O,onShow:w,onHide:N=(()=>{}),onExit:E,onExited:j,onExiting:k,onEnter:P,onEntering:C,onEntered:T}=e,S=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,sc);const R=ac(O),D=function(e){const t=Oo(),n=e||function(e){return oc||(oc=new nc({ownerDocument:null==e?void 0:e.document})),oc}(t),r=(0,i.useRef)({dialog:null,backdrop:null});return Object.assign(r.current,{add:()=>n.add(r.current),remove:()=>n.remove(r.current),isTopModal:()=>n.isTopModal(r.current),setDialogRef:(0,i.useCallback)((e=>{r.current.dialog=e}),[]),setBackdropRef:(0,i.useCallback)((e=>{r.current.backdrop=e}),[])})}(_),L=He(),I=qe(n),[$,M]=(0,i.useState)(!n),F=(0,i.useRef)(null);(0,i.useImperativeHandle)(t,(()=>D),[D]),U&&!I&&n&&(F.current=ec()),p||n||$?n&&$&&M(!1):M(!0);const B=ze((()=>{if(D.add(),V.current=J(document,"keydown",q),W.current=J(document,"focus",(()=>setTimeout(K)),!0),w&&w(),m){const e=ec(document);D.dialog&&e&&!Ms(D.dialog,e)&&(F.current=e,D.dialog.focus())}})),z=ze((()=>{var e;D.remove(),null==V.current||V.current(),null==W.current||W.current(),b&&(null==(e=F.current)||null==e.focus||e.focus(y),F.current=null)}));(0,i.useEffect)((()=>{n&&R&&B()}),[n,R,B]),(0,i.useEffect)((()=>{$&&z()}),[$,z]),Yn((()=>{z()}));const K=ze((()=>{if(!g||!L()||!D.isTopModal())return;const e=ec();D.dialog&&e&&!Ms(D.dialog,e)&&D.dialog.focus()})),H=ze((e=>{e.target===e.currentTarget&&(null==u||u(e),!0===l&&N())})),q=ze((e=>{c&&27===e.keyCode&&D.isTopModal()&&(null==d||d(e),e.defaultPrevented||N())})),W=(0,i.useRef)(),V=(0,i.useRef)(),G=p;if(!R||!(n||G&&!$))return null;const Y=Object.assign({role:r,ref:D.setDialogRef,"aria-modal":"dialog"===r||void 0},S,{style:s,className:a,tabIndex:-1});let X=h?h(Y):(0,x.jsx)("div",Object.assign({},Y,{children:i.cloneElement(o,{role:"document"})}));G&&(X=(0,x.jsx)(G,{appear:!0,unmountOnExit:!0,in:!!n,onExit:E,onExiting:k,onExited:(...e)=>{M(!0),null==j||j(...e)},onEnter:P,onEntering:C,onEntered:T,children:X}));let Q=null;if(l){const e=f;Q=v({ref:D.setBackdropRef,onClick:H}),e&&(Q=(0,x.jsx)(e,{appear:!0,in:!!n,children:Q}))}return(0,x.jsx)(x.Fragment,{children:A().createPortal((0,x.jsxs)(x.Fragment,{children:[Q,X]}),R)})}));ic.displayName="Modal";const lc=Object.assign(ic,{Manager:nc});function cc(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}function uc(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const dc=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",pc=".sticky-top",fc=".navbar-toggler";class mc extends nc{adjustAndStore(e,t,n){const r=t.style[e];t.dataset[e]=r,D(t,{[e]:`${parseFloat(D(t,e))+n}px`})}restore(e,t){const n=t.dataset[e];void 0!==n&&(delete t.dataset[e],D(t,{[e]:n}))}setContainerStyle(e){super.setContainerStyle(e);const t=this.getElement();var n,r;if(r="modal-open",(n=t).classList?n.classList.add(r):cc(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)),!e.scrollBarWidth)return;const a=this.isRTL?"paddingLeft":"paddingRight",s=this.isRTL?"marginLeft":"marginRight";ba(t,dc).forEach((t=>this.adjustAndStore(a,t,e.scrollBarWidth))),ba(t,pc).forEach((t=>this.adjustAndStore(s,t,-e.scrollBarWidth))),ba(t,fc).forEach((t=>this.adjustAndStore(s,t,e.scrollBarWidth)))}removeContainerStyle(e){super.removeContainerStyle(e);const t=this.getElement();var n,r;r="modal-open",(n=t).classList?n.classList.remove(r):"string"==typeof n.className?n.className=uc(n.className,r):n.setAttribute("class",uc(n.className&&n.className.baseVal||"",r));const a=this.isRTL?"paddingLeft":"paddingRight",s=this.isRTL?"marginLeft":"marginRight";ba(t,dc).forEach((e=>this.restore(a,e))),ba(t,pc).forEach((e=>this.restore(s,e))),ba(t,fc).forEach((e=>this.restore(s,e)))}}let gc;function bc(e){return gc||(gc=new mc(e)),gc}const yc=mc,hc=i.createContext({onHide(){}}),vc=i.forwardRef((({bsPrefix:e,className:t,contentClassName:n,centered:r,size:a,fullscreen:s,children:o,scrollable:i,...l},c)=>{const u=`${e=E(e,"modal")}-dialog`,d="string"==typeof s?`${e}-fullscreen-${s}`:`${e}-fullscreen`;return(0,x.jsx)("div",{...l,ref:c,className:b()(u,t,a&&`${e}-${a}`,r&&`${u}-centered`,i&&`${u}-scrollable`,s&&d),children:(0,x.jsx)("div",{className:b()(`${e}-content`,n),children:o})})}));vc.displayName="ModalDialog";var _c=["bsPrefix","className","style","dialogClassName","contentClassName","children","dialogAs","aria-labelledby","aria-describedby","aria-label","show","animation","backdrop","keyboard","onEscapeKeyDown","onShow","onHide","container","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","onEntered","onExit","onExiting","onEnter","onEntering","onExited","backdropClassName","zIndex","manager"];function xc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Oc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xc(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var wc={show:!1,backdrop:!0,keyboard:!0,autoFocus:!0,enforceFocus:!0,restoreFocus:!0,animation:!0,dialogAs:vc};function Nc(t){return l().createElement(nt,e({},t,{timeout:null}))}function Ec(t){return l().createElement(nt,e({},t,{timeout:null}))}var jc=l().forwardRef((function(t,n){var r=t.bsPrefix,a=t.className,s=t.style,c=t.dialogClassName,u=t.contentClassName,d=t.children,p=t.dialogAs,f=t["aria-labelledby"],m=t["aria-describedby"],g=t["aria-label"],y=t.show,h=t.animation,v=t.backdrop,_=t.keyboard,x=t.onEscapeKeyDown,O=t.onShow,w=t.onHide,N=t.container,j=t.autoFocus,C=t.enforceFocus,T=t.restoreFocus,S=t.restoreFocusOptions,R=t.onEntered,D=t.onExit,L=t.onExiting,I=t.onEnter,A=t.onEntering,$=t.onExited,M=t.backdropClassName,F=t.zIndex,B=t.manager,z=o(t,_c),K=ma((0,i.useState)({}),2),H=K[0],q=K[1],W=ma((0,i.useState)(!1),2),V=W[0],G=W[1],Y=(0,i.useRef)(!1),J=(0,i.useRef)(!1),ee=(0,i.useRef)(null),te=ma(Ke(),2),ne=te[0],re=te[1],ae=se(n,re),oe=ze(w),ie=k();r=E(r,"modal");var le=(0,i.useMemo)((function(){return{onHide:oe}}),[oe]);function ce(){return B||bc({isRTL:ie})}function ue(e){if(U){var t=ce().getScrollbarWidth()>0,n=e.scrollHeight>P(e).documentElement.clientHeight;q({paddingRight:t&&!n?Zl():void 0,paddingLeft:!t&&n?Zl():void 0})}}var de=ze((function(){ne&&ue(ne.dialog)}));Yn((function(){var e;Q(window,"resize",de),null===(e=ee.current)||void 0===e||e.call(ee)}));var pe=function(){Y.current=!0},fe=function(e){Y.current&&ne&&e.target===ne.dialog&&(J.current=!0),Y.current=!1},me=function(){G(!0),ee.current=Z(ne.dialog,(function(){G(!1)}))},ge=function(e){"static"!==v?J.current||e.target!==e.currentTarget?J.current=!1:null==w||w():function(e){e.target===e.currentTarget&&me()}(e)},be=(0,i.useCallback)((function(t){return l().createElement("div",e({},t,{className:b()("".concat(r,"-backdrop"),M,!h&&"show"),style:{zIndex:F}}))}),[h,M,r,F]),ye=Oc(Oc({},s),H);return ye.display="block",l().createElement(hc.Provider,{value:le},l().createElement(lc,{show:y,ref:ae,backdrop:v,container:N,keyboard:!0,autoFocus:j,enforceFocus:C,restoreFocus:T,restoreFocusOptions:S,onEscapeKeyDown:function(e){_||"static"!==v?_&&x&&x(e):(e.preventDefault(),me())},onShow:O,onHide:w,onEnter:function(e,t){e&&ue(e),null==I||I(e,t)},onEntering:function(e,t){null==A||A(e,t),X(window,"resize",de)},onEntered:R,onExit:function(e){var t;null===(t=ee.current)||void 0===t||t.call(ee),null==D||D(e)},onExiting:L,onExited:function(e){e&&(e.style.display=""),null==$||$(e),Q(window,"resize",de)},manager:ce(),transition:h?Nc:void 0,backdropTransition:h?Ec:void 0,renderBackdrop:be,renderDialog:function(t){return l().createElement("div",e({role:"dialog"},t,{style:ye,className:b()(a,r,V&&"".concat(r,"-static")),onClick:v?ge:void 0,onMouseUp:fe,"aria-label":g,"aria-labelledby":f,"aria-describedby":m}),l().createElement(p,e({},z,{onMouseDown:pe,className:c,contentClassName:u}),d))}}))}));jc.defaultProps=wc;const kc=jc;var Pc=["children","is_open","setProps","className","class_name","autoFocus","autofocus","labelledBy","labelledby","modalClassName","modal_class_name","contentClassName","content_class_name","backdropClassName","backdrop_class_name","tag","loading_state","fade","style","zindex","zIndex"];function Cc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Tc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cc(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Sc=function(t){var n=t.children,r=t.is_open,a=t.setProps,s=t.className,i=t.class_name,c=t.autoFocus,u=t.autofocus,d=t.labelledBy,p=t.labelledby,f=t.modalClassName,g=t.modal_class_name,b=t.contentClassName,y=t.content_class_name,h=t.backdropClassName,v=t.backdrop_class_name,_=t.tag,x=t.loading_state,O=t.fade,w=t.style,N=t.zindex,E=t.zIndex,j=o(t,Pc);return l().createElement(kc,e({animation:O,dialogAs:_,dialogClassName:i||s,className:g||f,contentClassName:y||b,backdropClassName:v||h,autoFocus:u||c,"aria-labelledby":p||d,show:r,onHide:function(){a&&a({is_open:!1})},style:N||E?Tc(Tc({},w),{},{zIndex:N||E}):w,zIndex:N||E},m(["persistence","persistence_type","persisted_props"],j),{"data-dash-is-loading":x&&x.is_loading||void 0}),n)};Sc.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,tag:u().string,is_open:u().bool,centered:u().bool,scrollable:u().bool,autofocus:u().bool,autoFocus:u().bool,size:u().string,role:u().string,labelledby:u().string,labelledBy:u().string,keyboard:u().bool,backdrop:u().oneOfType([u().bool,u().oneOf(["static"])]),modal_class_name:u().string,modalClassName:u().string,backdrop_class_name:u().string,backdropClassName:u().string,content_class_name:u().string,contentClassName:u().string,fade:u().bool,fullscreen:u().oneOf([u().bool,u().oneOf(["sm-down","md-down","lg-down","xl-down","xxl-down"])]),zindex:u().oneOfType([u().number,u().string]),zIndex:u().oneOfType([u().number,u().string])};const Rc=Sc,Dc=ct("modal-body");var Lc=["children","loading_state","className","class_name","tag"],Ic=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.tag,c=o(t,Lc);return l().createElement(Dc,e({as:i,className:s||a,"data-dash-is-loading":r&&r.is_loading||void 0},m(["setProps"],c)),n)};Ic.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Ac=Ic,$c=ct("modal-footer");var Mc=["children","loading_state","className","class_name","tag"],Fc=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.tag,c=o(t,Mc);return l().createElement($c,e({as:i,className:s||a,"data-dash-is-loading":r&&r.is_loading||void 0},m(["setProps"],c)),n)};Fc.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Bc=Fc,zc=i.forwardRef((({closeLabel:e,closeVariant:t,closeButton:n,onHide:r,children:a,...s},o)=>{const l=(0,i.useContext)(hc),c=ze((()=>{null==l||l.onHide(),null==r||r()}));return(0,x.jsxs)("div",{ref:o,...s,children:[a,n&&(0,x.jsx)(st,{"aria-label":e,variant:t,onClick:c})]})}));zc.defaultProps={closeLabel:"Close",closeButton:!1};const Kc=zc,Hc=i.forwardRef((({bsPrefix:e,className:t,...n},r)=>(e=E(e,"modal-header"),(0,x.jsx)(Kc,{ref:r,...n,className:b()(t,e)}))));Hc.displayName="ModalHeader",Hc.defaultProps={closeLabel:"Close",closeButton:!1};const qc=Hc;var Wc=["children","loading_state","className","class_name","tag","close_button"],Uc=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.tag,c=t.close_button,u=o(t,Wc);return l().createElement(qc,e({as:i,className:s||a,closeButton:c,"data-dash-is-loading":r&&r.is_loading||void 0},m(["setProps"],u)),n)};Uc.defaultProps={close_button:!0},Uc.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,close_button:u().bool,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Vc=Uc,Gc=ct("modal-title",{Component:ot("h4")});var Yc=["children","loading_state","className","class_name","tag"],Xc=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.tag,c=o(t,Yc);return l().createElement(Gc,e({as:i,className:s||a,"data-dash-is-loading":r&&r.is_loading||void 0},m(["setProps"],c)),n)};Xc.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Qc=Xc;var Jc=["children","loading_state","className","class_name","pills","justified","horizontal","vertical","navbar_scroll"],Zc={start:"justify-content-start",center:"justify-content-center",end:"justify-content-end",around:"justify-content-around",between:"justify-content-between"},eu={xs:"flex-xs-column",sm:"flex-sm-column",md:"flex-md-column",lg:"flex-lg-column",xl:"flex-xl-column"},tu=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.pills,c=t.justified,u=t.horizontal,d=t.vertical,p=t.navbar_scroll,f=o(t,Jc),g=u&&Zc[u],y=!0===d?"flex-column":d&&eu[d],h=b()(s||a,g,y);return l().createElement(si,e({className:h,variant:i?"pills":null,justify:c,navbarScroll:p},m(["setProps"],f),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};tu.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,pills:u().bool,card:u().bool,fill:u().bool,justified:u().bool,vertical:u().oneOfType([u().bool,u().string]),horizontal:u().oneOf(["start","center","end","between","around"]),navbar:u().bool,navbar_scroll:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const nu=tu,ru=i.forwardRef((({bsPrefix:e,className:t,as:n,...r},a)=>{e=E(e,"navbar-brand");const s=n||(r.href?"a":"span");return(0,x.jsx)(s,{...r,ref:a,className:b()(t,e)})}));ru.displayName="NavbarBrand";const au=ru,su=i.forwardRef((({children:e,bsPrefix:t,...n},r)=>{t=E(t,"navbar-collapse");const a=(0,i.useContext)(Do);return(0,x.jsx)(fe,{in:!(!a||!a.expanded),...n,children:(0,x.jsx)("div",{ref:r,className:t,children:e})})}));su.displayName="NavbarCollapse";const ou=su,iu=i.forwardRef((({bsPrefix:e,className:t,children:n,label:r,as:a="button",onClick:s,...o},l)=>{e=E(e,"navbar-toggler");const{onToggle:c,expanded:u}=(0,i.useContext)(Do)||{},d=ze((e=>{s&&s(e),c&&c()}));return"button"===a&&(o.type="button"),(0,x.jsx)(a,{...o,ref:l,onClick:d,"aria-label":r,className:b()(t,e,!u&&"collapsed"),children:n||(0,x.jsx)("span",{className:`${e}-icon`})})}));iu.displayName="NavbarToggle",iu.defaultProps={label:"Toggle navigation"};const lu=iu,cu=ct("offcanvas-body"),uu={[B]:"show",[z]:"show"},du=i.forwardRef((({bsPrefix:e,className:t,children:n,...r},a)=>(e=E(e,"offcanvas"),(0,x.jsx)(ie,{ref:a,addEndListener:te,...r,childRef:n.ref,children:(r,a)=>i.cloneElement(n,{...a,className:b()(t,n.props.className,(r===B||r===K)&&`${e}-toggling`,uu[r])})}))));du.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1},du.displayName="OffcanvasToggling";const pu=du,fu=i.forwardRef((({bsPrefix:e,className:t,...n},r)=>(e=E(e,"offcanvas-header"),(0,x.jsx)(Kc,{ref:r,...n,className:b()(t,e)}))));fu.displayName="OffcanvasHeader",fu.defaultProps={closeLabel:"Close",closeButton:!1};const mu=fu,gu=ct("offcanvas-title",{Component:ot("h5")});function bu(e){return(0,x.jsx)(pu,{...e})}function yu(e){return(0,x.jsx)(nt,{...e})}const hu=i.forwardRef((({bsPrefix:e,className:t,children:n,"aria-labelledby":r,placement:a,show:s,backdrop:o,keyboard:l,scroll:c,onEscapeKeyDown:u,onShow:d,onHide:p,container:f,autoFocus:m,enforceFocus:g,restoreFocus:y,restoreFocusOptions:h,onEntered:v,onExit:_,onExiting:O,onEnter:w,onEntering:N,onExited:j,backdropClassName:k,manager:P,...C},T)=>{const S=(0,i.useRef)();e=E(e,"offcanvas");const{onToggle:R}=(0,i.useContext)(Do)||{},D=ze((()=>{null==R||R(),null==p||p()})),L=(0,i.useMemo)((()=>({onHide:D})),[D]),I=(0,i.useCallback)((t=>(0,x.jsx)("div",{...t,className:b()(`${e}-backdrop`,k)})),[k,e]);return(0,x.jsx)(hc.Provider,{value:L,children:(0,x.jsx)(lc,{show:s,ref:T,backdrop:o,container:f,keyboard:l,autoFocus:m,enforceFocus:g&&!c,restoreFocus:y,restoreFocusOptions:h,onEscapeKeyDown:u,onShow:d,onHide:D,onEnter:(e,...t)=>{e&&(e.style.visibility="visible"),null==w||w(e,...t)},onEntering:N,onEntered:v,onExit:_,onExiting:O,onExited:(e,...t)=>{e&&(e.style.visibility=""),null==j||j(...t)},manager:P||(c?(S.current||(S.current=new yc({handleContainerOverflow:!1})),S.current):bc()),transition:bu,backdropTransition:yu,renderBackdrop:I,renderDialog:s=>(0,x.jsx)("div",{role:"dialog",...s,...C,className:b()(t,e,`${e}-${a}`),"aria-labelledby":r,children:n})})})}));hu.displayName="Offcanvas",hu.defaultProps={show:!1,backdrop:!0,keyboard:!0,scroll:!1,autoFocus:!0,enforceFocus:!0,restoreFocus:!0,placement:"start"};const vu=Object.assign(hu,{Body:cu,Header:mu,Title:gu}),_u=i.forwardRef(((e,t)=>{const n=(0,i.useContext)(Do);return(0,x.jsx)(vu,{ref:t,show:!(null==n||!n.expanded),...e})}));_u.displayName="NavbarOffcanvas";const xu=_u,Ou=ct("navbar-text",{Component:"span"}),wu=i.forwardRef(((e,t)=>{const{bsPrefix:n,expand:r,variant:a,bg:s,fixed:o,sticky:l,className:c,as:u="nav",expanded:d,onToggle:p,onSelect:f,collapseOnSelect:m,...g}=_(e,{expanded:"onToggle"}),y=E(n,"navbar"),h=(0,i.useCallback)(((...e)=>{null==f||f(...e),m&&d&&(null==p||p(!1))}),[f,m,d,p]);void 0===g.role&&"nav"!==u&&(g.role="navigation");let v=`${y}-expand`;"string"==typeof r&&(v=`${v}-${r}`);const O=(0,i.useMemo)((()=>({onToggle:()=>null==p?void 0:p(!d),bsPrefix:y,expanded:!!d})),[y,d,p]);return(0,x.jsx)(Do.Provider,{value:O,children:(0,x.jsx)(fo.Provider,{value:h,children:(0,x.jsx)(u,{ref:t,...g,className:b()(c,y,r&&v,a&&`${y}-${a}`,s&&`bg-${s}`,l&&`sticky-${l}`,o&&`fixed-${o}`)})})})}));wu.defaultProps={expand:!0,variant:"light",collapseOnSelect:!1},wu.displayName="Navbar";const Nu=Object.assign(wu,{Brand:au,Collapse:ou,Offcanvas:xu,Text:Ou,Toggle:lu});var Eu=["children","color","style","loading_state","className","class_name","light","dark","tag"];function ju(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ku(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ju(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ju(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Pu=function(t){var n=t.children,r=t.color,a=t.style,s=t.loading_state,i=t.className,c=t.class_name,u=(t.light,t.dark),d=t.tag,p=o(t,Eu),f=bt.has(r);return l().createElement(Nu,e({variant:u?"dark":"light",as:d,bg:f?r:null,style:ku({backgroundColor:!f&&r},a),className:c||i},m(["setProps"],p),{"data-dash-is-loading":s&&s.is_loading||void 0}),n)};Pu.defaultProps={color:"light",light:!0,expand:"md"},Pu.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,light:u().bool,dark:u().bool,fixed:u().string,sticky:u().oneOf(["top"]),color:u().string,role:u().string,tag:u().string,expand:u().oneOfType([u().bool,u().string]),loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Cu=Pu;var Tu=["children","loading_state","className","class_name"],Su=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Tu);return l().createElement(au,e({className:s||a},m(["setProps"],i),{as:t.href?It:"span","data-dash-is-loading":r&&r.is_loading||void 0}),n)};Su.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,external_link:u().bool,href:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Ru=Su;var Du=["children","loading_state","className","class_name"],Lu=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Du);return l().createElement(lu,e({onClick:function(){t.setProps&&t.setProps({n_clicks:t.n_clicks+1,n_clicks_timestamp:Date.now()})},className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Lu.defaultProps={n_clicks:0,n_clicks_timestamp:-1},Lu.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,type:u().string,n_clicks:u().number,n_clicks_timestamp:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Iu=Lu;var Au=["children","brand","brand_href","brand_style","brand_external_link","links_left","fluid","color","dark","light","style","loading_state","className","class_name"];function $u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Mu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$u(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Fu=function(t){var n=t.children,r=t.brand,a=t.brand_href,s=t.brand_style,c=t.brand_external_link,u=t.links_left,d=t.fluid,p=t.color,f=t.dark,g=(t.light,t.style),b=t.loading_state,y=t.className,h=t.class_name,v=o(t,Au),_=bt.has(p),x=ma((0,i.useState)(!1),2),O=x[0],w=x[1];return l().createElement(Nu,e({variant:f?"dark":"light",bg:_?p:null,color:_?p:null,style:_?g:Mu({backgroundColor:p},g),className:h||y},m(["setProps"],v),{"data-dash-is-loading":b&&b.is_loading||void 0}),l().createElement(ua,{fluid:d},r&&l().createElement(Ru,{href:a,style:s,external_link:c},r),l().createElement(Iu,{onClick:function(){return w(!O)}}),l().createElement(Nu.Collapse,{in:O},l().createElement(nu,{className:u?"me-auto":"ms-auto"},n))))};Fu.defaultProps={fluid:!1,color:"light",light:!0,expand:"md",links_left:!1},Fu.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,brand:u().string,brand_href:u().string,brand_style:u().object,brand_external_link:u().bool,fluid:u().bool,links_left:u().bool,light:u().bool,dark:u().bool,fixed:u().string,sticky:u().string,color:u().string,expand:u().oneOfType([u().bool,u().string]),loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Bu=Fu;var zu=["children","loading_state","className","class_name"],Ku=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,zu);return l().createElement(ti,e({className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Ku.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Hu=Ku;var qu=n(12),Wu=["children","disabled","className","class_name","active","loading_state","setProps","n_clicks","href"],Uu=function(t){var n=ma((0,i.useState)(!1),2),r=n[0],a=n[1],s=t.children,c=t.disabled,u=t.className,d=t.class_name,p=t.active,f=t.loading_state,g=t.setProps,y=t.n_clicks,h=t.href,v=o(t,Wu),_=function(e){a(!0===p||"exact"===p&&e===h||"partial"===p&&e.startsWith(h))};(0,i.useEffect)((function(){_(window.location.pathname),"string"==typeof p&&qu.History.onChange((function(){_(window.location.pathname)}))}),[p]);var x=b()(d||u,"nav-link",{active:r,disabled:c});return l().createElement(It,e({className:x,disabled:c,preOnClick:function(){!c&&g&&g({n_clicks:y+1,n_clicks_timestamp:Date.now()})},href:h},m(["n_clicks_timestamp"],v),{"data-dash-is-loading":f&&f.is_loading||void 0}),s)};Uu.defaultProps={active:!1,disabled:!1,n_clicks:0,n_clicks_timestamp:-1},Uu.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,href:u().string,active:u().oneOfType([u().bool,u().oneOf(["partial","exact"])]),disabled:u().bool,external_link:u().bool,n_clicks:u().number,n_clicks_timestamp:u().number,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),target:u().string};const Vu=Uu;var Gu=["is_open","setProps","children","loading_state","class_name","className","backdrop","backdrop_class_name","backdropClassName","labelledby","labelledBy","scrollable","autofocus","autoFocus","close_button","title"],Yu=function(t){var n=t.is_open,r=t.setProps,a=t.children,s=t.loading_state,i=t.class_name,c=t.className,u=t.backdrop,d=t.backdrop_class_name,p=t.backdropClassName,f=t.labelledby,m=t.labelledBy,g=t.scrollable,b=t.autofocus,y=t.autoFocus,h=t.close_button,v=t.title,_=o(t,Gu),x=function(){r&&r({is_open:!n})},O=v||h?l().createElement(vu.Header,{closeButton:h,onHide:"static"===u&&h?x:null},l().createElement(vu.Title,null,v)):null;return l().createElement(vu,e({autoFocus:b||y,"aria-labelledby":f||m,className:i||c,backdropClassName:d||p,scroll:g,show:n,onHide:"static"!==u?x:null,backdrop:u||"static"===u,"data-dash-is-loading":s&&s.is_loading||void 0},_),O,l().createElement(vu.Body,null,a))};Yu.defaultProps={close_button:!0,is_open:!1,backdrop:!0},Yu.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,labelledby:u().string,labelledBy:u().string,backdrop:u().oneOfType([u().bool,u().oneOf(["static"])]),backdrop_class_name:u().string,backdropClassName:u().string,keyboard:u().bool,is_open:u().bool,placement:u().oneOf(["start","end","top","bottom"]),scrollable:u().bool,autofocus:u().bool,autoFocus:u().bool,title:u().string,close_button:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Xu=Yu,Qu=i.forwardRef((({active:e,disabled:t,className:n,style:r,activeLabel:a,children:s,...o},i)=>{const l=e||t?"span":Ze;return(0,x.jsx)("li",{ref:i,style:r,className:b()(n,"page-item",{active:e,disabled:t}),children:(0,x.jsxs)(l,{className:"page-link",disabled:t,...o,children:[s,e&&a&&(0,x.jsx)("span",{className:"visually-hidden",children:a})]})})}));Qu.defaultProps={active:!1,disabled:!1,activeLabel:"(current)"},Qu.displayName="PageItem";const Ju=Qu;function Zu(e,t,n=e){function r({children:e,...r}){return(0,x.jsxs)(Qu,{...r,children:[(0,x.jsx)("span",{"aria-hidden":"true",children:e||t}),(0,x.jsx)("span",{className:"visually-hidden",children:n})]})}return r.displayName=e,r}const ed=Zu("First","«"),td=Zu("Prev","‹","Previous"),nd=Zu("Ellipsis","…","More"),rd=Zu("Next","›"),ad=Zu("Last","»"),sd=i.forwardRef((({bsPrefix:e,className:t,size:n,...r},a)=>{const s=E(e,"pagination");return(0,x.jsx)("ul",{ref:a,...r,className:b()(t,s,n&&`${s}-${n}`)})}));sd.displayName="Pagination";const od=Object.assign(sd,{First:ed,Prev:td,Ellipsis:nd,Item:Ju,Next:rd,Last:ad});var id=["step","active_page","min_value","fully_expanded","previous_next","first_last","setProps","class_name","className","loading_state"],ld=function(t){var n=t.step,r=t.active_page,a=t.min_value,s=t.fully_expanded,i=t.previous_next,c=t.first_last,u=t.setProps,d=t.class_name,p=t.className,f=t.loading_state,m=o(t,id),g=t.max_value;(g-a)%n!=0&&(g=g+n-(g-a)%n);var b=function(e){u&&u({active_page:e})},y=function(e){return l().createElement(od.Item,{key:e,active:e===r,onClick:function(){return b(e)}},e)},h=[];if(c&&h.push(l().createElement(od.First,{key:"first",disabled:r===a,onClick:function(){return b(a)}})),i&&h.push(l().createElement(od.Prev,{key:"previous",disabled:r===a,onClick:function(){return b(r-n)}})),s||Math.floor((g-a)/n)+1<=7)for(var v=a;v<=g;v+=n)h.push(y(v));else h.push(y(a)),r<=a+3*n?(h.push(y(a+n)),h.push(y(a+2*n)),h.push(y(a+3*n)),h.push(y(a+4*n)),h.push(l().createElement(od.Ellipsis,{disabled:!0,key:"ellipsis"}))):r>=g-3*n?(h.push(l().createElement(od.Ellipsis,{disabled:!0,key:"ellipsis"})),h.push(y(g-4*n)),h.push(y(g-3*n)),h.push(y(g-2*n)),h.push(y(g-n))):(h.push(l().createElement(od.Ellipsis,{disabled:!0,key:"ellipsis-1"})),h.push(y(r-n)),h.push(y(r)),h.push(y(r+n)),h.push(l().createElement(od.Ellipsis,{disabled:!0,key:"ellipsis-2"}))),h.push(y(g));return i&&h.push(l().createElement(od.Next,{key:"next",disabled:r===g,onClick:function(){return b(r+n)}})),c&&h.push(l().createElement(od.Last,{key:"last",disabled:r===g,onClick:function(){return b(g)}})),l().createElement(od,e({className:d||p,"data-dash-is-loading":f&&f.is_loading||void 0},m),h)};ld.defaultProps={min_value:1,step:1,active_page:1,fully_expanded:!0,previous_next:!1,first_last:!1},ld.propTypes={id:u().string,class_name:u().string,className:u().string,style:u().object,size:u().oneOf(["sm","lg"]),min_value:u().number,max_value:u().number.isRequired,step:u().number,active_page:u().number,fully_expanded:u().bool,previous_next:u().bool,first_last:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const cd=ld,ud=ct("popover-body");function dd(e,t,n,r,a,s,o){try{var i=e[s](o),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,a)}function pd(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var s=e.apply(t,n);function o(e){dd(s,r,a,o,i,"next",e)}function i(e){dd(s,r,a,o,i,"throw",e)}o(void 0)}))}}var fd=n(17),md=n.n(fd);const gd=()=>{},bd=i.forwardRef(((e,t)=>{const{flip:n,offset:r,placement:a,containerPadding:s,popperConfig:o={},transition:l}=e,[c,u]=Ke(),[d,p]=Ke(),f=se(u,t),m=ac(e.container),g=ac(e.target),[b,y]=(0,i.useState)(!e.show),h=$s(g,c,Us({placement:a,enableEvents:!!e.show,containerPadding:s||5,flip:n,offset:r,arrowElement:d,popperConfig:o}));e.show?b&&y(!1):e.transition||b||y(!0);const v=(...t)=>{y(!0),e.onExited&&e.onExited(...t)},_=e.show||l&&!b;if(function(e,t,{disabled:n,clickTrigger:r}={}){const a=t||gd;qs(e,a,{disabled:n,clickTrigger:r});const s=ze((e=>{27===e.keyCode&&a(e)}));(0,i.useEffect)((()=>{if(n||null==e)return;const t=P(Ks(e));let r=(t.defaultView||window).event;const a=J(t,"keyup",(e=>{e!==r?s(e):r=void 0}));return()=>{a()}}),[e,n,s])}(c,e.onHide,{disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent}),!_)return null;let O=e.children(Object.assign({},h.attributes.popper,{style:h.styles.popper,ref:f}),{popper:h,placement:a,show:!!e.show,arrowProps:Object.assign({},h.attributes.arrow,{style:h.styles.arrow,ref:p})});if(l){const{onExit:t,onExiting:n,onEnter:r,onEntering:a,onEntered:s}=e;O=(0,x.jsx)(l,{in:e.show,appear:!0,onExit:t,onExiting:n,onExited:v,onEnter:r,onEntering:a,onEntered:s,children:O})}return m?A().createPortal(O,m):null}));bd.displayName="Overlay";const yd=bd,hd=ct("popover-header");i.Component;const vd=i.forwardRef((({bsPrefix:e,placement:t,className:n,style:r,children:a,body:s,arrowProps:o,popper:i,show:l,...c},u)=>{const d=E(e,"popover"),p=k(),[f]=(null==t?void 0:t.split("-"))||[],m=function(e,t){let n=e;return"left"===e?n=t?"end":"start":"right"===e&&(n=t?"start":"end"),n}(f,p);return(0,x.jsxs)("div",{ref:u,role:"tooltip",style:r,"x-placement":f,className:b()(n,d,f&&`bs-popover-${m}`),...c,children:[(0,x.jsx)("div",{className:"popover-arrow",...o}),s?(0,x.jsx)(ud,{children:a}):a]})}));vd.defaultProps={placement:"right"};const _d=Object.assign(vd,{Header:hd,Body:ud,POPPER_OFFSET:[0,8]}),xd={transition:nt,rootClose:!1,show:!1,placement:"top"},Od=i.forwardRef((({children:e,transition:t,popperConfig:n={},...r},a)=>{const s=(0,i.useRef)({}),[o,l]=function(e){const t=(0,i.useRef)(null),n=E(void 0,"popover"),r=(0,i.useMemo)((()=>({name:"offset",options:{offset:()=>t.current&&cc(t.current,n)?e||_d.POPPER_OFFSET:e||[0,0]}})),[e,n]);return[t,[r]]}(r.offset),c=se(a,o),u=!0===t?nt:t||void 0;return(0,x.jsx)(yd,{...r,ref:c,popperConfig:{...n,modifiers:l.concat(n.modifiers||[])},transition:u,children:(n,{arrowProps:r,popper:a,show:o})=>{var l,c;!function(e,t){const{ref:n}=e,{ref:r}=t;e.ref=n.__wrapped||(n.__wrapped=e=>n(oe(e))),t.ref=r.__wrapped||(r.__wrapped=e=>r(oe(e)))}(n,r);const u=null==a?void 0:a.placement,d=Object.assign(s.current,{state:null==a?void 0:a.state,scheduleUpdate:null==a?void 0:a.update,placement:u,outOfBoundaries:(null==a||null==(l=a.state)||null==(c=l.modifiersData.hide)?void 0:c.isReferenceHidden)||!1});return"function"==typeof e?e({...n,placement:u,show:o,...!t&&o&&{className:"show"},popper:d,arrowProps:r}):i.cloneElement(e,{...n,placement:u,arrowProps:r,popper:d,className:b()(e.props.className,!t&&o&&"show"),style:{...e.props.style,...n.style}})}})}));Od.displayName="Overlay",Od.defaultProps=xd;const wd=Od;var Nd=["children","target","delay","trigger","defaultShow","setProps","autohide"],Ed=(0,i.createContext)({});const jd=function(t){var n,r,a,s,c,u=t.children,d=t.target,p=t.delay,f=t.trigger,m=t.defaultShow,g=t.setProps,b=t.autohide,y=o(t,Nd),h=(!1,n=ma((0,i.useState)(false),2),r=n[0],a=n[1],s=(0,i.useRef)(r),(0,i.useEffect)((function(){s.current=r}),[r]),[r,a,s]),v=ma(h,3),_=v[0],x=v[1],O=v[2],w=(0,i.useRef)(null),N=(0,i.useRef)(null),E=(0,i.useRef)(null),j="string"==typeof f?f.split(" "):[],k="object"!==kt(c=d)?c:"{"+Object.keys(c).sort().map((function(e){return JSON.stringify(e)+":"+((t=c[e])&&t.wild||JSON.stringify(t));var t})).join(",")+"}",P=ma((0,i.useState)(!1),2),C=P[0],T=P[1],S=function(){O.current&&(N.current=clearTimeout(N.current),x(!1),g&&g({is_open:!1}))},R=function(){!O.current&&E.current?(E.current=clearTimeout(E.current),S()):O.current&&(clearTimeout(N.current),N.current=setTimeout(S,p.hide))},D=function(){O.current||(E.current=clearTimeout(E.current),x(!0),g&&g({is_open:!0}))},L=function(){O.current&&N.current?(N.current=clearTimeout(N.current),D()):O.current||(clearTimeout(E.current),E.current=setTimeout(D,p.show))},I=function(e){var t,n;t=e.target,(n=w.current)&&(t===n||n.contains(t))&&(N.current&&(N.current=clearTimeout(N.current)),O.current?R():L())};(0,i.useEffect)((function(){setTimeout((function(){return x(m)}),50)}),[m]),(0,i.useEffect)((function(){j.indexOf("legacy")>-1?T(!0):T(!1)}),[j]);var A=function(e){return new Promise((function(t){return setTimeout(t,e)}))},$=function(){var e=pd(md().mark((function e(t){var n,r,a=arguments;return md().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=a.length>1&&void 0!==a[1]?a[1]:0,!(null===(r=document.getElementById(t))&&n<4)){e.next=6;break}return e.next=5,A(100*Math.pow(2,n));case 5:return e.abrupt("return",$(t,n+1));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return(0,i.useEffect)((function(){var e=function(){var e=pd(md().mark((function e(){return md().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,$(k);case 2:w.current=e.sent,(t=w.current)&&(j.indexOf("hover")>-1&&(t.addEventListener("mouseover",L,!0),t.addEventListener("mouseout",R,!0)),j.indexOf("focus")>-1&&(t.addEventListener("focusin",D,!0),t.addEventListener("focusout",S,!0)),(j.indexOf("click")>-1||j.indexOf("legacy")>-1)&&document.addEventListener("click",I,!0),t.addEventListener("keydown",(function(e){"Escape"===e.key&&S()})));case 4:case"end":return e.stop()}var t}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[k]),l().createElement(Ed.Provider,{value:{handleMouseOverTooltipContent:function(e){j.indexOf("hover")>-1&&!b&&(N.current&&(N.current=clearTimeout(N.current)),D())},handleMouseLeaveTooltipContent:function(e){j.indexOf("hover")>-1&&!b&&(E.current&&(E.current=clearTimeout(E.current)),e.persist(),R())}}},l().createElement(wd,e({show:_,rootClose:C,onHide:function(){x(!1),g&&g({is_open:!1})},target:w.current},y),u))};var kd=["placement","className","style","children","body","arrowProps","popper","show","hideArrow"],Pd=["bsPrefix","placement","className","style","children","arrowProps","popper","show"],Cd=function(e){var t=e;return"left"===e?t="start":"right"===e&&(t="end"),t},Td=l().forwardRef((function(t,n){var r=t.placement,a=t.className,s=t.style,c=t.children,u=t.body,d=t.arrowProps,p=(t.popper,t.show,t.hideArrow),f=o(t,kd),m=ma((null==r?void 0:r.split("-"))||[],1)[0],g=Cd(m),y=(0,i.useContext)(Ed),h=y.handleMouseOverTooltipContent,v=y.handleMouseLeaveTooltipContent;return l().createElement("div",e({ref:n,role:"tooltip",style:s,"x-placement":m,className:b()(a,"popover",m&&"bs-popover-".concat(g)),onMouseOver:h,onMouseLeave:v},f),!p&&l().createElement("div",e({className:"popover-arrow"},d)),u?l().createElement(ud,null,c):c)})),Sd=l().forwardRef((function(t,n){t.bsPrefix;var r=t.placement,a=t.className,s=t.style,c=t.children,u=t.arrowProps,d=(t.popper,t.show,o(t,Pd)),p=ma((null==r?void 0:r.split("-"))||[],1)[0],f=Cd(p),m=(0,i.useContext)(Ed),g=m.handleMouseOverTooltipContent,y=m.handleMouseLeaveTooltipContent;return l().createElement("div",e({ref:n,style:s,role:"tooltip","x-placement":p,className:b()(a,"tooltip","bs-tooltip-".concat(f)),onMouseOver:g,onMouseLeave:y},d),l().createElement("div",e({className:"tooltip-arrow"},u)),l().createElement("div",{className:"tooltip-inner"},c))})),Rd=["children","is_open","loading_state","className","class_name","style","id","hide_arrow","offset","body"],Dd=function(t){var n=t.children,r=t.is_open,a=t.loading_state,s=t.className,i=t.class_name,c=t.style,u=t.id,d=t.hide_arrow,p=t.offset,f=t.body,g=o(t,Rd),b=p?{modifiers:[{name:"offset",options:{offset:"string"==typeof p?p.split(",").map((function(e){return parseInt(e)})):[0,p]}}]}:{};return l().createElement(jd,e({"data-dash-is-loading":a&&a.is_loading||void 0,defaultShow:r,popperConfig:b},m(["persistence","persisted_props","persistence_type"],g)),l().createElement(Td,{style:c,id:u,className:i||s,hideArrow:d,body:f},n))};Dd.defaultProps={delay:{show:0,hide:50},placement:"right",flip:!0,autohide:!1,persisted_props:["is_open"],persistence_type:"local"},Dd.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,placement:u().oneOf(["auto","auto-start","auto-end","top","top-start","top-end","right","right-start","right-end","bottom","bottom-start","bottom-end","left","left-start","left-end"]),target:u().oneOfType([u().string,u().object]),trigger:u().string,is_open:u().bool,hide_arrow:u().bool,inner_class_name:u().string,innerClassName:u().string,delay:u().oneOfType([u().shape({show:u().number,hide:u().number}),u().number]),offset:u().oneOfType([u().string,u().number]),flip:u().bool,body:u().bool,autohide:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["is_open"])),persistence_type:u().oneOf(["local","session","memory"])};const Ld=Dd;var Id=["children","loading_state","className","class_name"],Ad=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Id);return l().createElement(ud,e({className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Ad.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const $d=Ad;var Md=["children","loading_state","className","class_name"],Fd=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=o(t,Md);return l().createElement(hd,e({className:s||a},m(["setProps"],i),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Fd.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,tag:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Bd=Fd;var zd=["min","now","max","label","visuallyHidden","striped","animated","className","style","variant","barStyle"],Kd=["isChild"],Hd=["min","now","max","label","visuallyHidden","striped","animated","variant","className","children","barStyle"],qd=["children","loading_state","color","className","class_name","value","hide_label","bar"];function Wd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ud(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wd(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vd(e,t,n){var r=(e-t)/(n-t)*100;return Math.round(1e3*r)/1e3}function Gd(t,n){var r,a=t.min,s=t.now,i=t.max,c=t.label,u=t.visuallyHidden,d=t.striped,p=t.animated,f=t.className,m=t.style,g=t.variant,y=t.barStyle,h=o(t,zd);return l().createElement("div",e({ref:n},h,{role:"progressbar",className:b()(f,"progress-bar",(r={},Fe(r,"bg-".concat(g),g),Fe(r,"progress-bar-animated",p),Fe(r,"progress-bar-striped",p||d),r)),style:Ud(Ud({width:"".concat(Vd(s,a,i),"%")},m),y),"aria-valuenow":s,"aria-valuemin":a,"aria-valuemax":i}),u?l().createElement("span",{className:"visually-hidden"},c):c)}var Yd=l().forwardRef((function(t,n){var r=t.isChild,a=o(t,Kd);if(r)return Gd(a,n);var s=a.min,c=a.now,u=a.max,d=a.label,p=a.visuallyHidden,f=a.striped,m=a.animated,g=a.variant,y=a.className,h=a.children,v=a.barStyle,_=o(a,Hd);return l().createElement("div",e({ref:n},_,{className:b()(y,"progress")}),h?nr(h,(function(e){return(0,i.cloneElement)(e,{isChild:!0})})):Gd({min:s,now:c,max:u,label:d,visuallyHidden:p,striped:f,animated:m,variant:g,barStyle:v},n))}));Yd.defaultProps={min:0,max:100,animated:!1,isChild:!1,visuallyHidden:!1,striped:!1};var Xd=function(t){var n=t.children,r=t.loading_state,a=t.color,s=t.className,i=t.class_name,c=t.value,u=t.hide_label,d=t.bar,p=o(t,qd),f=bt.has(a);return l().createElement(Yd,e({className:i||s},m(["setProps"],p),{"data-dash-is-loading":r&&r.is_loading||void 0,now:c,isChild:d,variant:f?a:null,visuallyHidden:u,barStyle:f?{}:{backgroundColor:a}}),n)};Xd.defaultProps={hide_label:!1},Xd.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,bar:u().bool,min:u().number,max:u().number,value:u().oneOfType([u().string,u().number]),label:u().string,hide_label:u().bool,animated:u().bool,striped:u().bool,color:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Qd=Xd;function Jd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Zd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jd(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ep=function(e){var t=e.id,n=e.className,r=e.class_name,a=e.style,s=e.options,o=e.key,i=e.loading_state,c=e.name,u=s.map((function(t){return function(t){var n=e.id,r=e.inputClassName,a=e.input_class_name,s=e.inputCheckedStyle,o=e.input_checked_style,i=e.inputStyle,u=e.input_style,d=e.inputCheckedClassName,p=e.input_checked_class_name,f=e.labelClassName,m=e.label_class_name,g=e.labelCheckedClassName,y=e.label_checked_class_name,h=e.labelStyle,v=e.label_style,_=e.labelCheckedStyle,x=e.label_checked_style,O=e.setProps,w=e.inline,N=e.value,E=e.switch,j=t.value===N,k=j?Zd(Zd({},v||h),x||_):v||h,P=t.input_id||"_dbcprivate_radioitems_".concat(n,"_input_").concat(t.value);return l().createElement("div",{className:b()("form-check",w&&"form-check-inline",E&&"form-switch"),key:t.value},l().createElement("input",{id:P,name:c,value:t.value,checked:j,className:b()("form-check-input",a||r,j&&(p||d)),disabled:Boolean(t.disabled),style:j?o||s:u||i,type:"radio",onChange:function(){O({value:t.value})}}),l().createElement("label",{id:t.label_id,style:k,className:b()("form-check-label",m||f,j&&(y||g)),key:t.value,htmlFor:P},t.label))}(t)}));return l().createElement("div",{id:t,className:r||n,style:a,key:o,"data-dash-is-loading":i&&i.is_loading||void 0},u)};ep.propTypes={id:u().string,key:u().string,options:u().arrayOf(u().exact({label:u().oneOfType([u().string,u().number]).isRequired,value:u().oneOfType([u().string,u().number]).isRequired,disabled:u().bool,input_id:u().string,label_id:u().string})),value:u().oneOfType([u().string,u().number]),style:u().object,class_name:u().string,className:u().string,input_style:u().object,inputStyle:u().object,input_checked_style:u().object,inputCheckedStyle:u().object,input_class_name:u().string,inputClassName:u().string,input_checked_class_name:u().string,inputCheckedClassName:u().string,label_style:u().object,labelStyle:u().object,label_checked_style:u().object,labelCheckedStyle:u().object,label_class_name:u().string,labelClassName:u().string,label_checked_class_name:u().string,labelCheckedClassName:u().string,setProps:u().func,inline:u().bool,switch:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),name:u().string},ep.defaultProps={inputStyle:{},input_style:null,inputClassName:"",input_class_name:"",labelStyle:{},label_style:null,labelClassName:"",label_class_name:"",options:[],persisted_props:["value"],persistence_type:"local"};const tp=ep;var np=function(e){var t=e.value,n=e.disabled,r=e.className,a=e.class_name,s=e.style,o=e.id,i=e.input_class_name,c=e.inputClassName,u=e.input_style,d=e.label,p=e.label_id,f=e.label_style,m=e.label_class_name,g=e.labelClassName,y=e.loading_state,h=e.name,v=e.setProps;return l().createElement("div",{className:b()("form-check",a||r),style:s,"data-dash-is-loading":y&&y.is_loading||void 0},l().createElement("input",{id:o,name:h,checked:t,className:b()("form-check-input",i||c),disabled:n,style:u,type:"radio",onClick:function(){n||v&&v({value:!t})},onChange:function(){}}),l().createElement("label",{id:p,style:f,className:b()(m||g,"form-check-label","form-label"),htmlFor:o},d))};np.propTypes={id:u().string,class_name:u().string,className:u().string,style:u().object,input_style:u().object,inputStyle:u().object,input_class_name:u().string,inputClassName:u().string,label:u().string,label_id:u().string,label_style:u().object,labelStyle:u().object,label_class_name:u().string,labelClassName:u().string,name:u().string,value:u().bool,disabled:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),setProps:u().func},np.defaultProps={inputStyle:{},input_style:null,inputClassName:"",input_class_name:"",labelStyle:{},label_style:null,labelClassName:"",label_class_name:"",persisted_props:["value"],persistence_type:"local",value:!1,disabled:!1};const rp=np,ap=i.forwardRef((({bsPrefix:e,className:t,as:n="div",...r},a)=>{const s=E(e,"row"),o=j(),i=`${s}-cols`,l=[];return o.forEach((e=>{const t=r[e];let n;delete r[e],null!=t&&"object"==typeof t?({cols:n}=t):n=t;const a="xs"!==e?`-${e}`:"";null!=n&&l.push(`${i}${a}-${n}`)})),(0,x.jsx)(n,{ref:a,...r,className:b()(t,s,...l)})}));ap.displayName="Row";const sp=ap;var op=["children","className","class_name","align","justify","loading_state"],ip={start:"align-items-start",center:"align-items-center",end:"align-items-end",stretch:"align-items-stretch",baseline:"align-items-baseline"},lp={start:"justify-content-start",center:"justify-content-center",end:"justify-content-end",around:"justify-content-around",between:"justify-content-between",evenly:"justify-content-evenly"},cp=function(t){var n=t.children,r=t.className,a=t.class_name,s=t.align,i=t.justify,c=t.loading_state,u=o(t,op),d=s&&ip[s],p=i&&lp[i],f=b()(a||r,d,p);return l().createElement(sp,e({className:f},m(["setProps"],u),{"data-dash-is-loading":c&&c.is_loading||void 0}),n)};cp.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,align:u().oneOf(["start","center","end","stretch","baseline"]),justify:u().oneOf(["start","center","end","around","between","evenly"]),loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const up=cp;var dp=["className","class_name","html_size","valid","invalid","value"],pp=function(t){var n=t.className,r=t.class_name,a=t.html_size,s=t.valid,i=t.invalid,c=t.value,u=o(t,dp);return l().createElement(Ki,e({},m(["setProps","options","persistence","persistence_type","persisted_props","loading_state"],u),{isInvalid:i,isValid:s,onChange:function(e){t.setProps&&t.setProps({value:e.target.value})},className:r||n,htmlSize:a,value:c||""}),l().createElement("option",{value:"",disabled:!0,hidden:!0},t.placeholder),t.options&&t.options.map((function(e){return l().createElement("option",{key:e.value,value:e.value,disabled:e.disabled,title:e.title},e.label)})))};pp.defaultProps={value:"",persisted_props:["value"],persistence_type:"local",placeholder:""},pp.propTypes={id:u().string,style:u().object,class_name:u().string,className:u().string,key:u().string,placeholder:u().string,value:u().oneOfType([u().string,u().number]),options:u().arrayOf(u().exact({label:u().oneOfType([u().string,u().number]).isRequired,value:u().string.isRequired,disabled:u().bool,title:u().string})),disabled:u().bool,required:u().oneOfType([u().oneOf(["required","REQUIRED"]),u().bool]),valid:u().bool,invalid:u().bool,size:u().string,html_size:u().string,persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),name:u().string};const fp=pp,mp=i.forwardRef((({bsPrefix:e,variant:t,animation:n,size:r,as:a="div",className:s,...o},i)=>{const l=`${e=E(e,"spinner")}-${n}`;return(0,x.jsx)(a,{ref:i,...o,className:b()(s,l,r&&`${l}-${r}`,t&&`text-${t}`)})}));mp.displayName="Spinner";const gp=mp;var bp=["children","color","loading_state","spinner_style","spinnerClassName","spinner_class_name","fullscreen","fullscreenClassName","fullscreen_class_name","fullscreen_style","delay_hide","delay_show","show_initially","type"];function yp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function hp(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yp(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var vp=function(t){var n=t.children,r=t.color,a=t.loading_state,s=t.spinner_style,c=t.spinnerClassName,u=t.spinner_class_name,d=t.fullscreen,p=t.fullscreenClassName,f=t.fullscreen_class_name,g=t.fullscreen_style,b=t.delay_hide,y=t.delay_show,h=t.show_initially,v=t.type,_=o(t,bp),x=ma((0,i.useState)(h),2),O=x[0],w=x[1],N=(0,i.useRef)(),E=(0,i.useRef)();(0,i.useEffect)((function(){a&&(a.is_loading?(N.current&&(N.current=clearTimeout(N.current)),O||E.current||(E.current=setTimeout((function(){w(!0),E.current=null}),y))):(E.current&&(E.current=clearTimeout(E.current)),O&&!N.current&&(N.current=setTimeout((function(){w(!1),N.current=null}),b))))}),[b,y,a]);var j=bt.has(r),k=hp({position:"fixed",width:"100vw",height:"100vh",top:0,left:0,backgroundColor:"white",display:"flex",justifyContent:"center",alignItems:"center",zIndex:99,visibility:"visible"},g),P=function(t){var n=t.style;return l().createElement(gp,e({variant:j?r:null,animation:v,style:hp({color:!j&&r},n),className:u||c},m(["setProps"],_)))};if(n){var C=hp({display:"block",margin:"1rem auto"},s);return l().createElement("div",{style:O?{visibility:"hidden",position:"relative"}:{}},n,O&&l().createElement("div",{style:d?k:{visibility:"visible",position:"absolute",top:0,height:"100%",width:"100%",display:"flex",justifyContent:"center",alignItems:"center"},className:d&&(f||p)},l().createElement(P,{style:C})))}return d?l().createElement("div",{className:f||p,style:k},l().createElement(P,{style:s})):l().createElement(P,{style:s})};vp._dashprivate_isLoadingComponent=!0,vp.defaultProps={delay_hide:0,delay_show:0,show_initially:!0,type:"border"},vp.propTypes={id:u().string,children:u().node,fullscreen_style:u().object,spinner_style:u().object,fullscreen_class_name:u().string,fullscreenClassName:u().string,spinner_class_name:u().string,spinnerClassName:u().string,color:u().string,type:u().string,size:u().string,fullscreen:u().bool,delay_hide:u().number,delay_show:u().number,show_initially:u().bool};const _p=vp;var xp=function(e){var t=e.value,n=e.disabled,r=e.className,a=e.class_name,s=e.style,o=e.id,i=e.input_class_name,c=e.inputClassName,u=e.input_style,d=e.label,p=e.label_id,f=e.label_style,m=e.label_class_name,g=e.labelClassName,y=e.loading_state,h=e.name,v=e.setProps;return l().createElement("div",{className:b()("form-check form-switch",a||r),style:s,"data-dash-is-loading":y&&y.is_loading||void 0},l().createElement("input",{id:o,name:h,checked:t,className:b()("form-check-input",i||c),disabled:n,style:u,type:"checkbox",onChange:function(){n||v&&v({value:!t})}}),l().createElement("label",{id:p,style:f,className:b()(m||g,"form-check-label","form-label"),htmlFor:o},d))};xp.propTypes={id:u().string,class_name:u().string,className:u().string,style:u().object,input_style:u().object,inputStyle:u().object,input_class_name:u().string,inputClassName:u().string,label:u().string,label_id:u().string,label_style:u().object,labelStyle:u().object,label_class_name:u().string,labelClassName:u().string,name:u().string,value:u().bool,disabled:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"]),setProps:u().func},xp.defaultProps={inputStyle:{},input_style:null,inputClassName:"",input_class_name:"",labelStyle:{},label_style:null,labelClassName:"",label_class_name:"",persisted_props:["value"],persistence_type:"local",value:!1,disabled:!1};const Op=xp;var wp=function(e){return l().createElement("div",null,e.children)};wp.defaultProps={disabled:!1},wp.propTypes={id:u().string,children:u().node,style:u().object,tab_style:u().object,active_tab_style:u().object,label_style:u().object,active_label_style:u().object,class_name:u().string,className:u().string,tab_class_name:u().string,tabClassName:u().string,active_tab_class_name:u().string,activeTabClassName:u().string,label_class_name:u().string,labelClassName:u().string,active_label_class_name:u().string,activeLabelClassName:u().string,key:u().string,label:u().string,tab_id:u().string,disabled:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Np=wp,Ep=function({children:e,in:t,mountOnEnter:n,unmountOnExit:r}){const a=(0,i.useRef)(t);return(0,i.useEffect)((()=>{t&&(a.current=!0)}),[t]),t?e:r||!a.current&&n?null:e},jp=["active","eventKey","mountOnEnter","transition","unmountOnExit","role","onEnter","onEntering","onEntered","onExit","onExiting","onExited"],kp=["activeKey","getControlledId","getControllerId"],Pp=["as"];function Cp(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}function Tp(e){let{active:t,eventKey:n,mountOnEnter:r,transition:a,unmountOnExit:s,role:o="tabpanel",onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:p,onExited:f}=e,m=Cp(e,jp);const g=(0,i.useContext)(Wo);if(!g)return[Object.assign({},m,{role:o}),{eventKey:n,isActive:t,mountOnEnter:r,transition:a,unmountOnExit:s,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:p,onExited:f}];const{activeKey:b,getControlledId:y,getControllerId:h}=g,v=Cp(g,kp),_=po(n);return[Object.assign({},m,{role:o,id:y(n),"aria-labelledby":h(n)}),{eventKey:n,isActive:null==t&&null!=_?po(b)===_:t,transition:a||v.transition,mountOnEnter:null!=r?r:v.mountOnEnter,unmountOnExit:null!=s?s:v.unmountOnExit,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:p,onExited:f}]}const Sp=i.forwardRef(((e,t)=>{let{as:n="div"}=e,r=Cp(e,Pp);const[a,{isActive:s,onEnter:o,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:p,unmountOnExit:f,transition:m=Ep}]=Tp(r);return(0,x.jsx)(Wo.Provider,{value:null,children:(0,x.jsx)(fo.Provider,{value:null,children:(0,x.jsx)(m,{in:s,onEnter:o,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:p,unmountOnExit:f,children:(0,x.jsx)(n,Object.assign({},a,{ref:t,hidden:!s,"aria-hidden":!s}))})})})}));Sp.displayName="TabPanel";const Rp=e=>{const{id:t,generateChildId:n,onSelect:r,activeKey:a,defaultActiveKey:s,transition:o,mountOnEnter:l,unmountOnExit:c,children:u}=e,[d,p]=v(a,s,r),f=ao(t),m=(0,i.useMemo)((()=>n||((e,t)=>f?`${f}-${t}-${e}`:null)),[f,n]),g=(0,i.useMemo)((()=>({onSelect:p,activeKey:d,transition:o,mountOnEnter:l||!1,unmountOnExit:c||!1,getControlledId:e=>m(e,"tabpane"),getControllerId:e=>m(e,"tab")})),[p,d,o,l,c,m]);return(0,x.jsx)(Wo.Provider,{value:g,children:(0,x.jsx)(fo.Provider,{value:p||null,children:u})})};Rp.Panel=Sp;const Dp=Rp;function Lp(e){return"boolean"==typeof e?e?nt:void 0:e}const Ip=({transition:e,...t})=>(0,x.jsx)(Dp,{...t,transition:Lp(e)});Ip.displayName="TabContainer";const Ap=Ip,$p=ct("tab-content"),Mp=i.forwardRef((({bsPrefix:e,transition:t,...n},r)=>{const[{className:a,as:s="div",...o},{isActive:i,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:p,onExited:f,mountOnEnter:m,unmountOnExit:g,transition:y=nt}]=Tp({...n,transition:Lp(t)}),h=E(e,"tab-pane");return(0,x.jsx)(Wo.Provider,{value:null,children:(0,x.jsx)(fo.Provider,{value:null,children:(0,x.jsx)(y,{in:i,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:p,onExited:f,mountOnEnter:m,unmountOnExit:g,children:(0,x.jsx)(s,{...o,ref:r,className:b()(a,h,i&&"active")})})})})}));Mp.displayName="TabPane";const Fp=Mp,Bp={eventKey:u().oneOfType([u().string,u().number]),title:u().node.isRequired,disabled:u().bool,tabClassName:u().string,tabAttrs:u().object},zp=()=>{throw new Error("ReactBootstrap: The `Tab` component is not meant to be rendered! It's an abstract component that is only valid as a direct Child of the `Tabs` Component. For custom tabs components use TabPane and TabsContainer directly")};zp.propTypes=Bp;const Kp=Object.assign(zp,{Container:Ap,Content:$p,Pane:Fp});var Hp=["children","tab_id","id","label","tab_style","active_tab_style","label_style","active_label_style","tabClassName","tab_class_name","activeTabClassName","active_tab_class_name","labelClassName","label_class_name","activeLabelClassName","active_label_class_name","loading_state"];function qp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Wp(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qp(Object(n),!0).forEach((function(t){Fe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Up=function(t){var n=t.children,r=t.id,a=t.className,s=t.class_name,c=t.style,u=t.active_tab,d=t.key,p=t.loading_state,f=t.setProps;n=Se(n),(0,i.useEffect)((function(){f&&void 0===u&&f({active_tab:n&&(Re(n[0]).tab_id||"tab-0")})}),[]);var g=n&&n.map((function(e,t){var n=Re(e),r=n.key||n.tab_id||"tab-"+t,a=u===r;return l().createElement(si.Item,{id:n.id,key:r,style:a?Wp(Wp({},n.tab_style),n.active_tab_style):n.tab_style,className:b()(n.tab_class_name||n.tabClassName,a&&(n.active_tab_class_name||n.activeTabClassName))},l().createElement(si.Link,{className:b()(n.label_class_name||n.labelClassName,a&&(n.active_label_class_name||n.activeLabelClassName),{active:a}),style:Wp(Wp(Wp({},a&&n.active_label_style),!n.disabled&&{cursor:"pointer"}),n.label_style),disabled:n.disabled,onClick:function(){var e;n.disabled||(e=r,f&&u!==e&&f({active_tab:e}))}},n.label))})),y=n&&n.map((function(t,n){var r=Re(t),a=(r.children,r.tab_id),s=(r.id,r.label,r.tab_style,r.active_tab_style,r.label_style,r.active_label_style,r.tabClassName,r.tab_class_name,r.activeTabClassName,r.active_tab_class_name,r.labelClassName,r.label_class_name,r.activeLabelClassName,r.active_label_class_name,r.loading_state),i=o(r,Hp),c=a||"tab-"+n;return l().createElement(Kp.Pane,e({eventKey:c,key:c},m(["setProps","persistence","persistence_type","persisted_props"],i),{"data-dash-is-loading":s&&s.is_loading||void 0}),t)}));return l().createElement(Kp.Container,{key:d,activeKey:u,onSelect:function(e){return f({active_tab:e})},"data-dash-is-loading":p&&p.is_loading||void 0},l().createElement(si,{id:r,variant:"tabs",as:"ul",className:s||a,style:c},g),l().createElement(Kp.Content,null,y))};Up.defaultProps={persisted_props:["active_tab"],persistence_type:"local"},Up.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,active_tab:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["active_tab"])),persistence_type:u().oneOf(["local","session","memory"])};const Vp=Up,Gp=i.forwardRef((({bsPrefix:e,className:t,striped:n,bordered:r,borderless:a,hover:s,size:o,variant:i,responsive:l,...c},u)=>{const d=E(e,"table"),p=b()(t,d,i&&`${d}-${i}`,o&&`${d}-${o}`,n&&`${d}-striped`,r&&`${d}-bordered`,a&&`${d}-borderless`,s&&`${d}-hover`),f=(0,x.jsx)("table",{...c,className:p,ref:u});if(l){let e=`${d}-responsive`;return"string"==typeof l&&(e=`${e}-${l}`),(0,x.jsx)("div",{className:e,children:f})}return f})),Yp=Gp;var Xp=["children","loading_state","className","class_name","color","dark"],Qp=function(t){var n=t.children,r=t.loading_state,a=t.className,s=t.class_name,i=t.color,c=t.dark,u=o(t,Xp);return l().createElement(Yp,e({className:s||a,variant:i||(c?"dark":void 0)},m(["setProps"],u),{"data-dash-is-loading":r&&r.is_loading||void 0}),n)};Qp.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,size:u().string,bordered:u().bool,borderless:u().bool,striped:u().bool,color:u().string,dark:u().bool,hover:u().bool,responsive:u().oneOfType([u().bool,u().string]),loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const Jp=Qp;var Zp=["value","n_clicks","n_blur","n_submit","setProps","className","class_name","invalid","valid","size","debounce","loading_state","autoFocus","autofocus","maxLength","maxlength","minLength","minlength","readOnly","readonly","accessKey","accesskey","contentEditable","contenteditable","contextMenu","contextmenu","spellCheck","spellcheck","tabIndex","tabindex"],ef=function(t){var n=t.value,r=t.n_clicks,a=t.n_blur,s=t.n_submit,c=t.setProps,u=t.className,d=t.class_name,p=t.invalid,f=t.valid,g=t.size,y=t.debounce,h=t.loading_state,v=t.autoFocus,_=t.autofocus,x=t.maxLength,O=t.maxlength,w=t.minLength,N=t.minlength,E=t.readOnly,j=t.readonly,k=t.accessKey,P=t.accesskey,C=t.contentEditable,T=t.contenteditable,S=t.contextMenu,R=t.contextmenu,D=t.spellCheck,L=t.spellcheck,I=t.tabIndex,A=t.tabindex,$=o(t,Zp),M=ma((0,i.useState)(n||""),2),F=M[0],B=M[1];(0,i.useEffect)((function(){n!==F&&B(n||"")}),[n]);var z=b()(d||u,p&&"is-invalid",f&&"is-valid",!!g&&"form-control-".concat(g),"form-control");return l().createElement("textarea",e({value:F,className:z,onChange:function(e){var t=e.target.value;B(t),!y&&c&&c({value:t})},onBlur:function(e){if(c){var t={n_blur:a+1,n_blur_timestamp:Date.now()};y&&(t.value=e.target.value),c(t)}},onKeyPress:function(e){if(c&&"Enter"===e.key){var t={n_submit:s+1,n_submit_timestamp:Date.now()};y&&(t.value=e.target.value),c(t)}},onClick:function(){c&&c({n_clicks:r+1,n_clicks_timestamp:Date.now()})},autoFocus:_||v,maxLength:O||x,minLength:N||w,readOnly:j||E,accessKey:P||k,contentEditable:T||C,contextMenu:R||S,spellCheck:L||D,tabIndex:A||I},m(["n_blur_timestamp","n_submit_timestamp","persistence","persistence_type","persisted_props"],$),{"data-dash-is-loading":h&&h.is_loading||void 0}))};ef.propTypes={id:u().string,key:u().string,value:u().string,autofocus:u().string,autoFocus:u().string,cols:u().oneOfType([u().string,u().number]),disabled:u().oneOfType([u().string,u().bool]),form:u().string,maxlength:u().oneOfType([u().string,u().number]),maxLength:u().oneOfType([u().string,u().number]),minlength:u().oneOfType([u().string,u().number]),minLength:u().oneOfType([u().string,u().number]),name:u().string,placeholder:u().string,readonly:u().oneOfType([u().bool,u().oneOf(["readOnly","readonly","READONLY"])]),readOnly:u().oneOfType([u().bool,u().oneOf(["readOnly","readonly","READONLY"])]),required:u().oneOfType([u().oneOf(["required","REQUIRED"]),u().bool]),rows:u().oneOfType([u().string,u().number]),wrap:u().string,accesskey:u().string,accessKey:u().string,class_name:u().string,className:u().string,contenteditable:u().oneOfType([u().string,u().number]),contentEditable:u().oneOfType([u().string,u().number]),contextmenu:u().string,contextMenu:u().string,dir:u().string,draggable:u().oneOfType([u().oneOf(["true","false"]),u().bool]),hidden:u().string,lang:u().string,spellcheck:u().oneOfType([u().oneOf(["true","false"]),u().bool]),spellCheck:u().oneOfType([u().oneOf(["true","false"]),u().bool]),style:u().object,tabindex:u().oneOfType([u().string,u().number]),tabIndex:u().oneOfType([u().string,u().number]),title:u().string,setProps:u().func,size:u().string,valid:u().bool,invalid:u().bool,n_blur:u().number,n_blur_timestamp:u().number,n_submit:u().number,n_submit_timestamp:u().number,n_clicks:u().number,n_clicks_timestamp:u().number,debounce:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["value"])),persistence_type:u().oneOf(["local","session","memory"])},ef.defaultProps={n_blur:0,n_blur_timestamp:-1,n_submit:0,n_submit_timestamp:-1,n_clicks:0,n_clicks_timestamp:-1,debounce:!1,persisted_props:["value"],persistence_type:"local",value:""};const tf=ef,nf={[B]:"showing",[K]:"showing show"},rf=i.forwardRef(((e,t)=>(0,x.jsx)(nt,{...e,ref:t,transitionClasses:nf})));rf.displayName="ToastFade";const af=rf,sf=i.createContext({onClose(){}}),of=i.forwardRef((({bsPrefix:e,closeLabel:t,closeVariant:n,closeButton:r,className:a,children:s,...o},l)=>{e=E(e,"toast-header");const c=(0,i.useContext)(sf),u=ze((e=>{null==c||null==c.onClose||c.onClose(e)}));return(0,x.jsxs)("div",{ref:l,...o,className:b()(e,a),children:[s,r&&(0,x.jsx)(st,{"aria-label":t,variant:n,onClick:u,"data-dismiss":"toast"})]})}));of.displayName="ToastHeader",of.defaultProps={closeLabel:"Close",closeButton:!0};const lf=of,cf=ct("toast-body"),uf=i.forwardRef((({bsPrefix:e,className:t,transition:n=af,show:r=!0,animation:a=!0,delay:s=5e3,autohide:o=!1,onClose:l,bg:c,...u},d)=>{e=E(e,"toast");const p=(0,i.useRef)(s),f=(0,i.useRef)(l);(0,i.useEffect)((()=>{p.current=s,f.current=l}),[s,l]);const m=Jn(),g=!(!o||!r),y=(0,i.useCallback)((()=>{g&&(null==f.current||f.current())}),[g]);(0,i.useEffect)((()=>{m.set(y,p.current)}),[m,y]);const h=(0,i.useMemo)((()=>({onClose:l})),[l]),v=!(!n||!a),_=(0,x.jsx)("div",{...u,ref:d,className:b()(e,t,c&&`bg-${c}`,!v&&(r?"show":"hide")),role:"alert","aria-live":"assertive","aria-atomic":"true"});return(0,x.jsx)(sf.Provider,{value:h,children:v&&n?(0,x.jsx)(n,{in:r,unmountOnExit:!0,children:_}):_})}));uf.displayName="Toast";const df=Object.assign(uf,{Body:cf,Header:lf});var pf=["children","loading_state","header","icon","header_style","headerClassName","header_class_name","body_style","bodyClassName","body_class_name","dismissable","duration","n_dismiss","is_open","setProps","className","class_name","color"],ff=function(t){var n=t.children,r=t.loading_state,a=t.header,s=t.icon,c=t.header_style,u=t.headerClassName,d=t.header_class_name,p=t.body_style,f=t.bodyClassName,g=t.body_class_name,y=t.dismissable,h=t.duration,v=t.n_dismiss,_=t.is_open,x=t.setProps,O=t.className,w=t.class_name,N=t.color,E=o(t,pf),j=function(){x&&x({is_open:!1,n_dismiss:v+1,n_dismiss_timestamp:Date.now()})},k=(0,i.useRef)(null);return(0,i.useEffect)((function(){h&&(_?k.current=setTimeout(j,h):k.current&&(clearTimeout(k.current),k.current=null))}),[_]),l().createElement(df,e({show:_,onClose:y&&j,className:w||O,bg:N,"data-dash-is-loading":r&&r.is_loading||void 0},m(["n_dismiss_timestamp","persistence","persisted_props","persistence_type","setProps"],E)),l().createElement(df.Header,{style:c,className:d||u,closeButton:y},s&&l().createElement("svg",{className:"rounded text-".concat(s),width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid slice",focusable:"false",role:"img"},l().createElement("rect",{fill:"currentColor",width:"100%",height:"100%"})),l().createElement("strong",{className:b()("me-auto",s&&"ms-2")},a)),l().createElement(df.Body,{style:p,className:g||f},n))};ff.defaultProps={is_open:!0,n_dismiss:0,n_dismiss_timestamp:-1,dismissable:!1,persisted_props:["is_open"],persistence_type:"local"},ff.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,header_style:u().object,header_class_name:u().string,headerClassName:u().string,body_style:u().object,body_class_name:u().string,bodyClassName:u().string,tag:u().string,is_open:u().bool,key:u().string,header:u().string,dismissable:u().bool,duration:u().number,n_dismiss:u().number,n_dismiss_timestamp:u().number,icon:u().string,color:u().string,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string}),persistence:u().oneOfType([u().bool,u().string,u().number]),persisted_props:u().arrayOf(u().oneOf(["is_open"])),persistence_type:u().oneOf(["local","session","memory"])};const mf=ff;var gf=["id","children","is_open","loading_state","className","class_name","style","fade"],bf=function(t){var n=t.id,r=t.children,a=t.is_open,s=t.loading_state,i=t.className,c=t.class_name,u=t.style,d=t.fade,p=o(t,gf);return l().createElement(jd,e({"data-dash-is-loading":s&&s.is_loading||void 0,defaultShow:a},p,{transition:d}),l().createElement(Sd,{id:n,style:u,className:c||i},r))};bf.defaultProps={delay:{show:0,hide:50},placement:"auto",flip:!0,autohide:!0,fade:!0,trigger:"hover focus"},bf.propTypes={id:u().string,children:u().node,style:u().object,class_name:u().string,className:u().string,key:u().string,target:u().oneOfType([u().string,u().object]),placement:u().oneOf(["auto","auto-start","auto-end","top","top-start","top-end","right","right-start","right-end","bottom","bottom-start","bottom-end","left","left-start","left-end"]),flip:u().bool,delay:u().shape({show:u().number,hide:u().number}),autohide:u().bool,fade:u().bool,trigger:u().string,is_open:u().bool,loading_state:u().shape({is_loading:u().bool,prop_name:u().string,component_name:u().string})};const yf=bf})(),window.dash_bootstrap_components=r})();