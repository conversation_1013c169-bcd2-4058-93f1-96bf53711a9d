{"src/components/accordion/Accordion.js": {"description": "A self contained Accordion component. Build up the children using the\nAccordionItem component.", "displayName": "Accordion", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "flush": {"type": {"name": "bool"}, "required": false, "description": "Renders accordion edge-to-edge with its parent container"}, "active_item": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}]}, "required": false, "description": "The item_id of the currently active item. If item_id has not been specified\nfor the active item, this will default to item-i, where i is the index\n(starting from 0) of the item.\n\nIf `always_open=True`, this needs to be a list of string IDs."}, "always_open": {"type": {"name": "bool"}, "required": false, "description": "You can make accordion items stay open when another item is opened by\nusing the always_open prop.", "defaultValue": {"value": "false", "computed": false}}, "start_collapsed": {"type": {"name": "bool"}, "required": false, "description": "Set to True for all items to be collapsed initially.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'active_item'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['active_item']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/accordion/AccordionItem.js": {"description": "A component to build up the children of the accordion.", "displayName": "AccordionItem", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "title": {"type": {"name": "string"}, "required": false, "description": "The title on display in the collapsed accordion item."}, "item_id": {"type": {"name": "string"}, "required": false, "description": "Optional identifier for item used for determining which item is visible\nif not specified, and AccordionItem is being used inside Accordion component, the itemId\nwill be set to \"item-i\" where i is (zero indexed) position of item in list\nitems pased to Accordion component."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/alert/Alert.js": {"description": "Alert allows you to create contextual feedback messages on user actions.\n\nControl the visibility using callbacks with the `is_open` prop, or set it to\nauto-dismiss with the `duration` prop.", "displayName": "<PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "color": {"type": {"name": "string"}, "required": false, "description": "Alert color, options: primary, secondary, success, info, warning, danger,\nlink or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)\nDefault: secondary.", "defaultValue": {"value": "'success'", "computed": false}}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether alert is open. Default: True.", "defaultValue": {"value": "true", "computed": false}}, "fade": {"type": {"name": "bool"}, "required": false, "description": "If True, a fade animation will be applied when `is_open` is toggled. If\nFalse the Alert will simply appear and disappear."}, "dismissable": {"type": {"name": "bool"}, "required": false, "description": "If true, add a close button that allows <PERSON><PERSON> to be dismissed."}, "duration": {"type": {"name": "number"}, "required": false, "description": "Duration in milliseconds after which the Alert dismisses itself.", "defaultValue": {"value": "null", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'is_open'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['is_open']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/badge/Badge.js": {"description": "Badges can be used to add counts or labels to other components.", "displayName": "Badge", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "color": {"type": {"name": "string"}, "required": false, "description": "Badge color, options: primary, secondary, success, info, warning, danger,\nlink or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)\nDefault: secondary.", "defaultValue": {"value": "'secondary'", "computed": false}}, "text_color": {"type": {"name": "string"}, "required": false, "description": "Badge color, options: primary, secondary, success, info, warning, danger,\nlink or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)\nDefault: secondary."}, "pill": {"type": {"name": "bool"}, "required": false, "description": "Make badge \"pill\" shaped (rounded ends, like a pill). Default: False."}, "href": {"type": {"name": "string"}, "required": false, "description": "Attach link to badge."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the Badge. Default: span."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "target": {"type": {"name": "string"}, "required": false, "description": "Target attribute to pass on to the link. Only applies to external links."}, "title": {"type": {"name": "string"}, "required": false, "description": "Sets the title attribute of the underlying HTML button."}}}, "src/components/breadcrumb/Breadcrumb.js": {"description": "Use breadcrumbs to create a navigation breadcrumb in your app.", "displayName": "Breadcrumb", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "items": {"type": {"name": "arrayOf", "value": {"name": "shape", "value": {"label": {"name": "string", "description": "Label to display inside the breadcrumbs.", "required": false}, "href": {"name": "string", "description": "URL of the resource to link to", "required": false}, "active": {"name": "bool", "description": "Apply 'active' style to this component.", "required": false}, "external_link": {"name": "bool", "description": "If true, the browser will treat this as an external link, forcing a\npage refresh at the new location. If false, this just changes the\nlocation without triggering a page refresh. Use this if you are\nobserving dcc.Location, for instance. Defaults to true for absolute\nURLs and false otherwise.", "required": false}, "target": {"name": "string", "description": "Target attribute to pass on to the link. Only applies to external links.", "required": false}, "title": {"name": "string", "description": "title attribute for the inner a element", "required": false}}}}, "required": false, "description": "The details of the items to render inside of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "item_style": {"type": {"name": "object"}, "required": false, "description": "Defines inline CSS styles that will be added to each item in the\nbreadcrumbs."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - Use class_name instead.\n\nOften used with CSS to style elements with common properties."}, "item_class_name": {"type": {"name": "string"}, "required": false, "description": "Class name to apply to each item."}, "itemClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - use item_class_name instead.\n\nClass name ot apply to each item."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "object"}, "required": false, "description": "HTML tag to use for the outer breadcrumb component. Default: \"nav\"."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/button/Button.js": {"description": "A component for creating Bootstrap buttons with different style options. The\nButton component can act as a HTML button, link (<a>) or can be used like a\ndash_core_components style `Link` for navigating between pages of a Dash app.\n\nUse the `n_clicks` prop to trigger callbacks when the button has been\nclicked.", "displayName": "<PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "href": {"type": {"name": "string"}, "required": false, "description": "Pass a URL (relative or absolute) to make the menu entry a link."}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "Use of *_timestamp props has been deprecated in <PERSON> in favour of dash.callback_context.\nSee \"How do I determine which Input has changed?\" in the Dash FAQs https://dash.plot.ly/faqs.\n\nAn integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "active": {"type": {"name": "bool"}, "required": false, "description": "Whether button is in active state. Default: False."}, "color": {"type": {"name": "string"}, "required": false, "description": "Button color, options: primary, secondary, success, info, warning, danger,\nlink. Default: primary."}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Disable button (make unclickable). Default: False."}, "size": {"type": {"name": "string"}, "required": false, "description": "Button size, options: 'lg', 'md', 'sm'."}, "title": {"type": {"name": "string"}, "required": false, "description": "Sets the title attribute of the underlying HTML button."}, "outline": {"type": {"name": "bool"}, "required": false, "description": "Set outline button style, which removes background images and colors for a\nlightweight style."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "target": {"type": {"name": "string"}, "required": false, "description": "Target attribute to pass on to link if using Button as an external link."}, "type": {"type": {"name": "enum", "value": [{"value": "'button'", "computed": false}, {"value": "'reset'", "computed": false}, {"value": "'submit'", "computed": false}]}, "required": false, "description": "The default behavior of the button. Possible values are: \"button\", \"reset\",\n\"submit\". If left unspecified the default depends on usage: for buttons\nassociated with a form (e.g. a dbc.Button inside a dbc.Form) the default is\n\"submit\". Otherwise the default is \"button\"."}, "download": {"type": {"name": "string"}, "required": false, "description": "Indicates that the hyperlink is to be used for downloading a resource."}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the button, submitted as a pair with the button’s value as part\nof the form data."}, "value": {"type": {"name": "string"}, "required": false, "description": "Defines the value associated with the button’s name when it’s submitted\nwith the form data. This value is passed to the server in params when the\nform is submitted."}}}, "src/components/buttongroup/ButtonGroup.js": {"description": "A component for creating groups of buttons. Can be used with `Button` or\n`DropdownMenu`.", "displayName": "ButtonGroup", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "vertical": {"type": {"name": "bool"}, "required": false, "description": "Group buttons vertically."}, "size": {"type": {"name": "string"}, "required": false, "description": "Size of button group, options: 'sm', 'md', 'lg'."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/Card.js": {"description": "Component for creating Bootstrap cards. Use in conjunction with CardBody,\nCardImg, CardLink, CardHeader and CardFooter. Can also be used in\nconjunction with CardColumns, CardDeck, CardGroup for different layout\noptions.", "displayName": "Card", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "color": {"type": {"name": "string"}, "required": false, "description": "Card color, options: primary, secondary, success, info, warning, danger,\nlight, dark or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name).\nDefault is light."}, "body": {"type": {"name": "bool"}, "required": false, "description": "Apply the `card-body` class to the card, so that there is no need to also\ninclude a CardBody component in the children of this Card. Default: False"}, "outline": {"type": {"name": "bool"}, "required": false, "description": "Apply color styling to just the border of the card."}, "inverse": {"type": {"name": "bool"}, "required": false, "description": "Invert text colours for use with a darker background."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardBody.js": {"description": "Wrap the content of your `Card` in `CardBody` to apply padding and other\nstyles.", "displayName": "CardBody", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the card body, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardFooter.js": {"description": "Use the CardFooter component to add a footer to any card.", "displayName": "<PERSON><PERSON><PERSON>er", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the card footer, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardGroup.js": {"description": "Use CardGroup to render cards as a single, attached element of columns with\nequal width and height.", "displayName": "CardGroup", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the card group, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardHeader.js": {"description": "Use the CardHeader component to add a header to any card.", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the card header, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardImg.js": {"description": "Use CardImg to add images to your cards.", "displayName": "CardImg", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the card body, default: div"}, "top": {"type": {"name": "bool"}, "required": false, "description": "Set to True if image is at top of card. This will apply the card-img-top\nclass which rounds the top corners to match the corners of the card."}, "bottom": {"type": {"name": "bool"}, "required": false, "description": "Set to True if image is at bottom of card. This will apply the\ncard-img-bottom class which rounds the bottom corners to match the corners\nof the card."}, "src": {"type": {"name": "string"}, "required": false, "description": "The URI of the embeddable content."}, "alt": {"type": {"name": "string"}, "required": false, "description": "Alternative text in case an image can't be displayed."}, "title": {"type": {"name": "string"}, "required": false, "description": "Text to be displayed as a tooltip when hovering"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardImgOverlay.js": {"description": "Use CardImgOverlay to turn an image into the background of your card and add\ntext on top of it.", "displayName": "CardImgOverlay", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the card image overlay, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/card/CardLink.js": {"description": "Use card link to add consistently styled links to your cards. Links can be\nused like buttons, external links, or internal Dash style links.", "displayName": "CardLink", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "href": {"type": {"name": "string"}, "required": false, "description": "URL of the resource to link to"}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "target": {"type": {"name": "string"}, "required": false, "description": "Target attribute to pass on to the link. Only applies to external links."}}}, "src/components/carousel/Carousel.js": {"description": "Component for creating Bootstrap carousel.  This component is a slideshow\nfor cycling through a series of content.", "displayName": "Carousel", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of the component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles of the carousel container. Will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Defines the className of the carousel container. Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nefines the className of the carousel container. Often used with CSS to style elements with common properties."}, "items": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"key": {"name": "string", "description": "A unique identifier for the slide, used to improve performance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info.", "required": false}, "src": {"name": "string", "description": "The URL of the image", "required": false}, "alt": {"name": "string", "description": "The alternate text for an image, if the image cannot be displayed", "required": false}, "img_class_name": {"name": "string", "description": "The className for the image.  The default is 'd-block w-100'", "required": false}, "imgClassName": {"name": "string", "description": "**DEPRECATED** Use `img_class_name` instead.\n\nThe className for the image.  The default is 'd-block w-100'", "required": false}, "img_style": {"name": "object", "description": "The style for the image", "required": false}, "header": {"name": "string", "description": "The header of the text on the slide. It is displayed in a <h5> element", "required": false}, "caption": {"name": "string", "description": "The caption of the item.  The text is displayed in a <p> element", "required": false}, "caption_class_name": {"name": "string", "description": "The class name for the header and caption container", "required": false}, "captionClassName": {"name": "string", "description": "**DEPRECATED** Use `caption_class_name` instead.\n\nThe class name for the header and caption container", "required": false}}}}, "required": true, "description": "The items to display on the slides in the carousel"}, "active_index": {"type": {"name": "number"}, "required": false, "description": "The current visible slide number", "defaultValue": {"value": "0", "computed": false}}, "controls": {"type": {"name": "bool"}, "required": false, "description": "Show the Carousel previous and next arrows for changing the current slide", "defaultValue": {"value": "true", "computed": false}}, "indicators": {"type": {"name": "bool"}, "required": false, "description": "Show a set of slide position indicators", "defaultValue": {"value": "true", "computed": false}}, "ride": {"type": {"name": "enum", "value": [{"value": "'carousel'", "computed": false}]}, "required": false, "description": "Autoplays the carousel after the user manually cycles the first item. If \"carousel\", autoplays the carousel on load."}, "slide": {"type": {"name": "bool"}, "required": false, "description": "controls whether the slide animation on the Carousel works or not"}, "variant": {"type": {"name": "enum", "value": [{"value": "'dark'", "computed": false}]}, "required": false, "description": "Add `variant=\"dark\"` to the Carousel for darker controls, indicators, and\ncaptions."}, "interval": {"type": {"name": "number"}, "required": false, "description": "the interval at which the carousel automatically cycles (default: 5000)\nIf set to None, carousel will not Autoplay (i.e. will not automatically cycle)."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'active_index'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['active_index']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/collapse/Collapse.js": {"description": "Hide or show content with a vertical collapsing animation. Visibility of the\nchildren is controlled by the `is_open` prop which can be targetted by\ncallbacks.", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether collapse is currently open."}, "navbar": {"type": {"name": "bool"}, "required": false, "description": "Set to True when using a collapse inside a navbar."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/dropdownmenu/DropdownMenu.js": {"description": "DropdownMenu creates an overlay useful for grouping together links and other\ncontent to organise navigation or other interactive elements.", "displayName": "DropdownMenu", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "label": {"type": {"name": "string"}, "required": false, "description": "Label for the DropdownMenu toggle."}, "direction": {"type": {"name": "enum", "value": [{"value": "'down'", "computed": false}, {"value": "'start'", "computed": false}, {"value": "'end'", "computed": false}, {"value": "'up'", "computed": false}, {"value": "'left'", "computed": false}, {"value": "'right'", "computed": false}, {"value": "'end'", "computed": false}]}, "required": false, "description": "Direction in which to expand the DropdownMenu. Default: 'down'. `left`\nand `right` have been deprecated, and `start` and `end` should be used\ninstead."}, "align_end": {"type": {"name": "bool"}, "required": false, "description": "Align the DropdownMenu along the right side of its parent. Default: False."}, "right": {"type": {"name": "bool"}, "required": false, "description": "**DEPRECATED** Use `align_end` instead.\n\nAlign the DropdownMenu along the right side of its parent. Default: False."}, "in_navbar": {"type": {"name": "bool"}, "required": false, "description": "Set this to True if the DropdownMenu is inside a navbar. Default: False."}, "addon_type": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'prepend'", "computed": false}, {"value": "'append'", "computed": false}]}]}, "required": false, "description": "Set this to 'prepend' or 'append' if the DropdownMenu is being used in an input group."}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Disable the dropdown.", "defaultValue": {"value": "false", "computed": false}}, "nav": {"type": {"name": "bool"}, "required": false, "description": "Set this to True if the DropdownMenu is inside a nav for styling consistent\nwith other nav items. Default: False."}, "caret": {"type": {"name": "bool"}, "required": false, "description": "Add a caret to the DropdownMenu toggle. Default: True.", "defaultValue": {"value": "true", "computed": false}}, "color": {"type": {"name": "string"}, "required": false, "description": "Set the color of the DropdownMenu toggle. Available options are: 'primary',\n'secondary', 'success', 'warning', 'danger', 'info', 'link' or any valid CSS\ncolor of your choice (e.g. a hex code, a decimal code or a CSS color name)\nDefault: 'primary'"}, "menu_variant": {"type": {"name": "enum", "value": [{"value": "'light'", "computed": false}, {"value": "'dark'", "computed": false}]}, "required": false, "description": "Set `menu_variant=\"dark\"` to create a dark-mode drop down instead", "defaultValue": {"value": "'light'", "computed": false}}, "toggle_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the DropdownMenu toggle."}, "toggle_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the DropdownMenu toggle."}, "toggleClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `toggle_class_name` instead.\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the DropdownMenu toggle."}, "size": {"type": {"name": "enum", "value": [{"value": "'sm'", "computed": false}, {"value": "'md'", "computed": false}, {"value": "'lg'", "computed": false}]}, "required": false, "description": "Size of the DropdownMenu. 'sm' corresponds to small, 'md' to medium\nand 'lg' to large."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "group": {"type": {"name": "bool"}, "required": false, "description": "Set group to True if the DropdownMenu is inside a ButtonGroup."}}}, "src/components/dropdownmenu/DropdownMenuItem.js": {"description": "Use DropdownMenuItem to build up the content of a DropdownMenu.", "displayName": "DropdownMenuItem", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "active": {"type": {"name": "bool"}, "required": false, "description": "Style this item as 'active'."}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Style this item as 'disabled'."}, "divider": {"type": {"name": "bool"}, "required": false, "description": "Set to True if this entry is a divider. Typically, it will have\nno children."}, "header": {"type": {"name": "bool"}, "required": false, "description": "Set to True if this is a header, rather than a conventional\nmenu item."}, "href": {"type": {"name": "string"}, "required": false, "description": "Pass a URL (relative or absolute) to make the menu entry a link."}, "toggle": {"type": {"name": "bool"}, "required": false, "description": "Whether to toggle the DropdownMenu on click. Default: True.", "defaultValue": {"value": "true", "computed": false}}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "target": {"type": {"name": "string"}, "required": false, "description": "Target attribute to pass on to the link. Only applies to external links."}}}, "src/components/fade/Fade.js": {"description": "Hide or show content with a fading animation. Visibility of the children is\ncontrolled by the `is_open` prop which can be targetted by callbacks.", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "is_in": {"type": {"name": "bool"}, "required": false, "description": "Controls whether the children of the Fade component are currently visible\nor not."}, "timeout": {"type": {"name": "union", "value": [{"name": "number"}, {"name": "shape", "value": {"enter": {"name": "number", "required": false}, "exit": {"name": "number", "required": false}}}]}, "required": false, "description": "The duration of the transition, in milliseconds.\n\nYou may specify a single timeout for all transitions like: `timeout=500`\nor individually like: timeout={'enter': 300, 'exit': 500}"}, "appear": {"type": {"name": "bool"}, "required": false, "description": "Show fade-in animation on initial page load. Default: True."}, "enter": {"type": {"name": "bool"}, "required": false, "description": "Enable or disable enter transitions. Default: True."}, "exit": {"type": {"name": "bool"}, "required": false, "description": "Enable or disable exit transitions. Default: True."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the fade component. Default: div."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/form/Form.js": {"description": "The Form component can be used to organise collections of input components\nand apply consistent styling.", "displayName": "Form", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "action": {"type": {"name": "string"}, "required": false, "description": "The URI of a program that processes the information submitted via the form."}, "method": {"type": {"name": "enum", "value": [{"value": "'GET'", "computed": false}, {"value": "'POST'", "computed": false}]}, "required": false, "description": "Defines which HTTP method to use when submitting the form. Can be GET\n(default) or POST."}, "n_submit": {"type": {"name": "number"}, "required": false, "description": "Number of times the `Enter` key was pressed while the input had focus.", "defaultValue": {"value": "0", "computed": false}}, "n_submit_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time that `En<PERSON>` was pressed.", "defaultValue": {"value": "-1", "computed": false}}, "prevent_default_on_submit": {"type": {"name": "bool"}, "required": false, "description": "The form calls preventDefault on submit events. If you want form data to\nbe posted to the endpoint specified by `action` on submit events, set\nprevent_default_on_submit to False. Defaults to True.", "defaultValue": {"value": "true", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/form/FormFeedback.js": {"description": "The FormFeedback component can be used to provide feedback on input values\nin a form. Add the form feedback to your layout and set the `valid` or\n`invalid` props of the associated input to toggle visibility.", "displayName": "FormFeedback", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "type": {"type": {"name": "string"}, "required": false, "description": "Either 'valid' or 'invalid'."}, "tooltip": {"type": {"name": "bool"}, "required": false, "description": "Use styled tooltips to display validation feedback."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/form/FormFloating.js": {"description": "A component for adding float labels to form controls in forms.", "displayName": "FormFloating", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "html_for": {"type": {"name": "string"}, "required": false, "description": "Set the `for` attribute of the label to bind it to a particular element"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/form/FormText.js": {"description": "Add explanatory text below your input components.", "displayName": "FormText", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "color": {"type": {"name": "string"}, "required": false, "description": "Text color, options: primary, secondary, success, warning, danger, info,\nmuted, light, dark, body, white, black-50, white-50 or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/form/Label.js": {"description": "A component for adding labels to inputs in forms with added sizing controls.", "displayName": "Label", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "hidden": {"type": {"name": "bool"}, "required": false, "description": "Hide label from UI, but allow it to be discovered by screen-readers."}, "size": {"type": {"name": "string"}, "required": false, "description": "Set size of label. Options 'sm', 'md' (default) or 'lg'."}, "html_for": {"type": {"name": "string"}, "required": false, "description": "Set the `for` attribute of the label to bind it to a particular element"}, "check": {"type": {"name": "bool"}, "required": false, "description": "Set to True when using to label a Checkbox or RadioButton."}, "width": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify width of label for use in grid layouts. Accepts the same values\nas the Col component."}, "xs": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify label width on extra small screen\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "sm": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify label width on a small screen\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "md": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify label width on a medium screen\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "lg": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify label width on a large screen\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "xl": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify label width on an extra large screen\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "align": {"type": {"name": "enum", "value": [{"value": "'start'", "computed": false}, {"value": "'center'", "computed": false}, {"value": "'end'", "computed": false}]}, "required": false, "description": "Set vertical alignment of the label, options: 'start', 'center', 'end',\ndefault: 'center'", "defaultValue": {"value": "'center'", "computed": false}}, "color": {"type": {"name": "string"}, "required": false, "description": "Text color, options: primary, secondary, success, warning, danger, info,\nmuted, light, dark, body, white, black-50, white-50 or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/input/Checkbox.js": {"description": "Checklist is a component that encapsulates several checkboxes.\nThe values and labels of the checklist is specified in the `options`\nproperty and the checked items are specified with the `value` property.\nEach checkbox is rendered as an input / label pair. `Checklist` must be\ngiven an `id` to work properly.", "displayName": "Checkbox", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components in callbacks.\nThe ID needs to be unique across all of the components in an app."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nThe class of the container (div)"}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "input_style": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> checkbox element.", "defaultValue": {"value": "null", "computed": false}}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_style` instead.\n\nThe style of the <input> checkbox element.", "defaultValue": {"value": "{}", "computed": false}}, "input_class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_class_name` instead.\n\nThe class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "label": {"type": {"name": "string"}, "required": false, "description": "The label of the <input> element"}, "label_id": {"type": {"name": "string"}, "required": false, "description": "The id of the label"}, "label_style": {"type": {"name": "object"}, "required": false, "description": "Inline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "null", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_style` instead.\n\nInline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "{}", "computed": false}}, "label_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_class_name` instead.\n\nCSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Disable the Checkbox.", "defaultValue": {"value": "false", "computed": false}}, "value": {"type": {"name": "bool"}, "required": false, "description": "The value of the input.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/input/Checklist.js": {"description": "Checklist is a component that encapsulates several checkboxes.\nThe values and labels of the checklist is specified in the `options`\nproperty and the checked items are specified with the `value` property.\nEach checkbox is rendered as an input / label pair. `Checklist` must be\ngiven an `id` to work properly.", "displayName": "Checklist", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components in callbacks.\nThe ID needs to be unique across all of the components in an app."}, "options": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "description": "The checkbox's label", "required": true}, "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "description": "The value of the checkbox. This value corresponds to the items\nspecified in the `value` property.", "required": true}, "disabled": {"name": "bool", "description": "If true, this checkbox is disabled and can't be clicked on.", "required": false}, "input_id": {"name": "string", "description": "id for this option's input, can be used to attach tooltips or apply\nCSS styles", "required": false}, "label_id": {"name": "string", "description": "id for this option's label, can be used to attach tooltips or apply\nCSS styles", "required": false}}}}, "required": false, "description": "An array of options", "defaultValue": {"value": "[]", "computed": false}}, "value": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}}, "required": false, "description": "The currently selected value", "defaultValue": {"value": "[]", "computed": false}}, "class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nThe class of the container (div)"}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "input_style": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> checkbox element.", "defaultValue": {"value": "null", "computed": false}}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_style` instead.\n\nThe style of the <input> checkbox element.", "defaultValue": {"value": "{}", "computed": false}}, "input_checked_style": {"type": {"name": "object"}, "required": false, "description": "Additional inline style arguments to apply to <input> elements on checked\nitems."}, "inputCheckedStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_checked_style` instead.\n\nAdditional inline style arguments to apply to <input> elements on checked\nitems."}, "input_class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_class_name` instead.\n\nThe class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "input_checked_class_name": {"type": {"name": "string"}, "required": false, "description": "Additional CSS classes to apply to the <input> element when the\ncorresponding checkbox is checked."}, "inputCheckedClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_checked_class_name` instead.\n\nAdditional CSS classes to apply to the <input> element when the\ncorresponding checkbox is checked."}, "label_style": {"type": {"name": "object"}, "required": false, "description": "Inline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "null", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_style` instead.\n\nInline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "{}", "computed": false}}, "label_checked_style": {"type": {"name": "object"}, "required": false, "description": "Additional inline style arguments to apply to <label> elements on checked\nitems."}, "labelCheckedStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_checked_style` instead.\n\nAdditional inline style arguments to apply to <label> elements on checked\nitems."}, "label_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_class_name` instead.\n\nCSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "label_checked_class_name": {"type": {"name": "string"}, "required": false, "description": "Additional CSS classes to apply to the <label> element when the\ncorresponding checkbox is checked."}, "labelCheckedClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_checked_class_name` instead.\n\nAdditional CSS classes to apply to the <label> element when the\ncorresponding checkbox is checked."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "inline": {"type": {"name": "bool"}, "required": false, "description": "Arrange Checklist inline"}, "switch": {"type": {"name": "bool"}, "required": false, "description": "Set to True to render toggle-like switches instead of checkboxes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}}}, "src/components/input/Input.js": {"description": "A basic HTML input control for entering text, numbers, or passwords, with\nBootstrap styles automatically applied. This component is much like its\ncounterpart in dash_core_components, but with a few additions such as the\n`valid` and `invalid` props for providing user feedback.\n\nNote that checkbox and radio types are supported through\nthe Checklist and RadioItems component. Dates, times, and file uploads\nare supported through separate components in other libraries.", "displayName": "Input", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "type": {"type": {"name": "enum", "value": [{"value": "\"text\"", "computed": false}, {"value": "'number'", "computed": false}, {"value": "'password'", "computed": false}, {"value": "'email'", "computed": false}, {"value": "'range'", "computed": false}, {"value": "'search'", "computed": false}, {"value": "'tel'", "computed": false}, {"value": "'url'", "computed": false}, {"value": "'hidden'", "computed": false}]}, "required": false, "description": "The type of control to render"}, "value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The value of the Input"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Set to True to disable the Input."}, "autocomplete": {"type": {"name": "string"}, "required": false, "description": "This attribute indicates whether the value of the control can be\nautomatically completed by the browser."}, "autoComplete": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `autocomplete` instead.\n\nThis attribute indicates whether the value of the control can be\nautomatically completed by the browser."}, "autofocus": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'autoFocus'", "computed": false}, {"value": "'autofocus'", "computed": false}, {"value": "'AUTOFOCUS'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "The element should be automatically focused after the page loaded.\nautoFocus is an HTML boolean attribute - it is enabled by a boolean or\n'autoFocus'. Alternative capitalizations `autofocus` & `AUTOFOCUS`\nare also acccepted."}, "autoFocus": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'autoFocus'", "computed": false}, {"value": "'autofocus'", "computed": false}, {"value": "'AUTOFOCUS'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "**DEPRECATED** Use `autofocus` instead.\n\nThe element should be automatically focused after the page loaded.\nautoFocus is an HTML boolean attribute - it is enabled by a boolean or\n'autoFocus'. Alternative capitalizations `autofocus` & `AUTOFOCUS`\nare also acccepted."}, "inputmode": {"type": {"name": "enum", "value": [{"value": "\"verbatim\"", "computed": false}, {"value": "\"latin\"", "computed": false}, {"value": "\"latin-name\"", "computed": false}, {"value": "\"latin-prose\"", "computed": false}, {"value": "\"full-width-latin\"", "computed": false}, {"value": "\"kana\"", "computed": false}, {"value": "\"katakana\"", "computed": false}, {"value": "\"numeric\"", "computed": false}, {"value": "\"tel\"", "computed": false}, {"value": "\"email\"", "computed": false}, {"value": "\"url\"", "computed": false}]}, "required": false, "description": "Provides a hint to the browser as to the type of data that might be\nentered by the user while editing the element or its contents."}, "inputMode": {"type": {"name": "enum", "value": [{"value": "\"verbatim\"", "computed": false}, {"value": "\"latin\"", "computed": false}, {"value": "\"latin-name\"", "computed": false}, {"value": "\"latin-prose\"", "computed": false}, {"value": "\"full-width-latin\"", "computed": false}, {"value": "\"kana\"", "computed": false}, {"value": "\"katakana\"", "computed": false}, {"value": "\"numeric\"", "computed": false}, {"value": "\"tel\"", "computed": false}, {"value": "\"email\"", "computed": false}, {"value": "\"url\"", "computed": false}]}, "required": false, "description": "**DEPRECATED** Use `inputmode` instead.\n\nProvides a hint to the browser as to the type of data that might be\nentered by the user while editing the element or its contents."}, "list": {"type": {"name": "string"}, "required": false, "description": "Identifies a list of pre-defined options to suggest to the user.\nThe value must be the id of a <datalist> element in the same document.\nThe browser displays only options that are valid values for this\ninput element.\nThis attribute is ignored when the type attribute's value is\nhidden, checkbox, radio, file, or a button type."}, "max": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The maximum (numeric or date-time) value for this item, which must not be\nless than its minimum (min attribute) value."}, "maxlength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "If the value of the type attribute is text, email, search, password, tel,\nor url, this attribute specifies the maximum number of characters\n(in UTF-16 code units) that the user can enter. For other control types,\nit is ignored. It can exceed the value of the size attribute. If it is not\nspecified, the user can enter an unlimited number of characters.\nSpecifying a negative number results in the default behavior (i.e. the\nuser can enter an unlimited number of characters). The constraint is\nevaluated only when the value of the attribute has been changed."}, "maxLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "**DEPRECATED** Use `maxlength` instead.\n\nIf the value of the type attribute is text, email, search, password, tel,\nor url, this attribute specifies the maximum number of characters\n(in UTF-16 code units) that the user can enter. For other control types,\nit is ignored. It can exceed the value of the size attribute. If it is not\nspecified, the user can enter an unlimited number of characters.\nSpecifying a negative number results in the default behavior (i.e. the\nuser can enter an unlimited number of characters). The constraint is\nevaluated only when the value of the attribute has been changed."}, "min": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The minimum (numeric or date-time) value for this item, which must not be\ngreater than its maximum (max attribute) value."}, "minlength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "If the value of the type attribute is text, email, search, password, tel,\nor url, this attribute specifies the minimum number of characters (in\nUnicode code points) that the user can enter. For other control types, it\nis ignored."}, "minLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "**DEPRECATED** Use `minlength` instead.\n\nIf the value of the type attribute is text, email, search, password, tel,\nor url, this attribute specifies the minimum number of characters (in\nUnicode code points) that the user can enter. For other control types, it\nis ignored."}, "step": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Works with the min and max attributes to limit the increments at which a\nnumeric or date-time value can be set. It can be the string any or a\npositive floating point number. If this attribute is not set to any, the\ncontrol accepts only values at multiples of the step value greater than\nthe minimum.", "defaultValue": {"value": "'any'", "computed": false}}, "html_size": {"type": {"name": "string"}, "required": false, "description": "The initial size of the control. This value is in pixels unless the value\nof the type attribute is text or password, in which case it is an integer\nnumber of characters. This attribute applies only when the type attribute\nis set to text, search, tel, url, email, or password, otherwise it is\nignored. In addition, the size must be greater than zero. If you do not\nspecify a size, a default value of 20 is used."}, "size": {"type": {"name": "string"}, "required": false, "description": "Set the size of the Input. Options: 'sm' (small), 'md' (medium)\nor 'lg' (large). Default is 'md'."}, "valid": {"type": {"name": "bool"}, "required": false, "description": "Apply valid style to the Input for feedback purposes. This will cause\nany FormFeedback in the enclosing div with valid=True to display."}, "invalid": {"type": {"name": "bool"}, "required": false, "description": "Apply invalid style to the Input for feedback purposes. This will cause\nany FormFeedback in the enclosing div with valid=False to display."}, "required": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'required'", "computed": false}, {"value": "'REQUIRED'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "This attribute specifies that the user must fill in a value before\nsubmitting a form. It cannot be used when the type attribute is hidden,\nimage, or a button type (submit, reset, or button). The :optional and\n:required CSS pseudo-classes will be applied to the field as appropriate.\nrequired is an HTML boolean attribute - it is enabled by a boolean or\n'required'. Alternative capitalizations `REQUIRED`\nare also acccepted."}, "plaintext": {"type": {"name": "bool"}, "required": false, "description": "Set to true for an input styled as plain text with the default form field\nstyling removed and the correct margins and padding preserved. Typically\nyou will want to use this in conjunction with readonly=True."}, "readonly": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'readOnly'", "computed": false}, {"value": "'readonly'", "computed": false}, {"value": "'READONLY'", "computed": false}]}]}, "required": false, "description": "Indicates whether the element can be edited."}, "placeholder": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "A hint to the user of what can be entered in the control . The placeholder\ntext must not contain carriage returns or line-feeds. Note: Do not use the\nplaceholder attribute instead of a <label> element, their purposes are\ndifferent. The <label> attribute describes the role of the form element\n(i.e. it indicates what kind of information is expected), and the\nplaceholder attribute is a hint about the format that the content should\ntake. There are cases in which the placeholder attribute is never\ndisplayed to the user, so the form must be understandable without it."}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}, "pattern": {"type": {"name": "string"}, "required": false, "description": "A regular expression that the control's value is checked against. The\npattern must match the entire value, not just some subset. Use the title\nattribute to describe the pattern to help the user. This attribute applies\nwhen the value of the type attribute is text, search, tel, url, email, or\npassword, otherwise it is ignored. The regular expression language is the\nsame as JavaScript RegExp algorithm, with the 'u' parameter that makes it\ntreat the pattern as a sequence of unicode code points. The pattern is not\nsurrounded by forward slashes."}, "n_submit": {"type": {"name": "number"}, "required": false, "description": "Number of times the `Enter` key was pressed while the input had focus.", "defaultValue": {"value": "0", "computed": false}}, "n_submit_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time that `En<PERSON>` was pressed.", "defaultValue": {"value": "-1", "computed": false}}, "n_blur": {"type": {"name": "number"}, "required": false, "description": "Number of times the input lost focus.", "defaultValue": {"value": "0", "computed": false}}, "n_blur_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the input lost focus.", "defaultValue": {"value": "-1", "computed": false}}, "debounce": {"type": {"name": "bool"}, "required": false, "description": "If true, changes to input will be sent back to the Dash server\nonly when the enter key is pressed or when the component loses\nfocus.  If it's false, it will sent the value back on every\nchange.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "tabindex": {"type": {"name": "string"}, "required": false, "description": "Overrides the browser's default tab order and follows the one specified instead."}, "tabIndex": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `tabindex` instead.\n\nOverrides the browser's default tab order and follows the one specified instead."}}}, "src/components/input/InputGroup.js": {"description": "A component for grouping together inputs and buttons, dropdowns or text.", "displayName": "InputGroup", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "size": {"type": {"name": "string"}, "required": false, "description": "Set the size of the Input. Options: 'sm' (small), 'md' (medium)\nor 'lg' (large). Default is 'md'."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/input/InputGroupText.js": {"description": "Use for wrapping text in InputGroups.", "displayName": "InputGroupText", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/input/RadioButton.js": {"description": "Checklist is a component that encapsulates several checkboxes.\nThe values and labels of the checklist is specified in the `options`\nproperty and the checked items are specified with the `value` property.\nEach checkbox is rendered as an input / label pair. `Checklist` must be\ngiven an `id` to work properly.", "displayName": "RadioButton", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components in callbacks.\nThe ID needs to be unique across all of the components in an app."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nThe class of the container (div)"}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "input_style": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> checkbox element.", "defaultValue": {"value": "null", "computed": false}}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_style` instead.\n\nThe style of the <input> checkbox element.", "defaultValue": {"value": "{}", "computed": false}}, "input_class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_class_name` instead.\n\nThe class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "label": {"type": {"name": "string"}, "required": false, "description": "The label of the <input> element"}, "label_id": {"type": {"name": "string"}, "required": false, "description": "The id of the label"}, "label_style": {"type": {"name": "object"}, "required": false, "description": "Inline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "null", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_style` instead.\n\nInline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "{}", "computed": false}}, "label_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_class_name` instead.\n\nCSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}, "value": {"type": {"name": "bool"}, "required": false, "description": "The value of the input.", "defaultValue": {"value": "false", "computed": false}}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Disable the RadioButton.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/input/RadioItems.js": {"description": "RadioItems is a component that encapsulates several radio item inputs.\nThe values and labels of the RadioItems is specified in the `options`\nproperty and the seleced item is specified with the `value` property.\nEach radio item is rendered as an input and associated label which are\nsiblings of each other.", "displayName": "RadioItems", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components in callbacks.\nThe ID needs to be unique across all of the components in an app."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "options": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "description": "The radio item's label", "required": true}, "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "description": "The value of the radio item. This value corresponds to the items\nspecified in the `value` property.", "required": true}, "disabled": {"name": "bool", "description": "If true, this radio item is disabled and can't be clicked on.", "required": false}, "input_id": {"name": "string", "description": "id for this option's input, can be used to attach tooltips or apply\nCSS styles", "required": false}, "label_id": {"name": "string", "description": "id for this option's label, can be used to attach tooltips or apply\nCSS styles", "required": false}}}}, "required": false, "description": "An array of options", "defaultValue": {"value": "[]", "computed": false}}, "value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The currently selected value"}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nThe class of the container (div)"}, "input_style": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> radio element", "defaultValue": {"value": "null", "computed": false}}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_style` instead.\n\nThe style of the <input> radio element", "defaultValue": {"value": "{}", "computed": false}}, "input_checked_style": {"type": {"name": "object"}, "required": false, "description": "Additional inline style arguments to apply to <input> elements on checked\nitems."}, "inputCheckedStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_checked_style` instead.\n\nAdditional inline style arguments to apply to <input> elements on checked\nitems."}, "input_class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> radio element", "defaultValue": {"value": "''", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_class_name` instead.\n\nThe class of the <input> radio element", "defaultValue": {"value": "''", "computed": false}}, "input_checked_class_name": {"type": {"name": "string"}, "required": false, "description": "Additional CSS classes to apply to the <input> element when the\ncorresponding radio is checked."}, "inputCheckedClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_checked_class_name` instead.\n\nAdditional CSS classes to apply to the <input> element when the\ncorresponding radio is checked."}, "label_style": {"type": {"name": "object"}, "required": false, "description": "Inline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "null", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_style` instead.\n\nInline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "{}", "computed": false}}, "label_checked_style": {"type": {"name": "object"}, "required": false, "description": "Additional inline style arguments to apply to <label> elements on checked\nitems."}, "labelCheckedStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_checked_style` instead.\n\nAdditional inline style arguments to apply to <label> elements on checked\nitems."}, "label_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_class_name` instead.\n\nCSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "label_checked_class_name": {"type": {"name": "string"}, "required": false, "description": "Additional CSS classes to apply to the <label> element when the\ncorresponding radio is checked."}, "labelCheckedClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_checked_class_name` instead.\n\nAdditional CSS classes to apply to the <label> element when the\ncorresponding radio is checked."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "inline": {"type": {"name": "bool"}, "required": false, "description": "Arrange RadioItems inline"}, "switch": {"type": {"name": "bool"}, "required": false, "description": "Set to True to render toggle-like switches instead of radios."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}}}, "src/components/input/Select.js": {"description": "Create a HTML select element with Bootstrap styles. Specify options as a\nlist of dictionaries with keys label, value and disabled.", "displayName": "Select", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "placeholder": {"type": {"name": "string"}, "required": false, "description": "Placeholder text to display before a selection is made.", "defaultValue": {"value": "''", "computed": false}}, "value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The value of the currently selected option.", "defaultValue": {"value": "''", "computed": false}}, "options": {"type": {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "union", "value": [{"name": "string"}, {"name": "number"}], "description": "The options's label", "required": true}, "value": {"name": "string", "description": "The value of the option. This value corresponds to the items\nspecified in the `value` property.", "required": true}, "disabled": {"name": "bool", "description": "If true, this checkbox is disabled and can't be clicked on.", "required": false}, "title": {"name": "string", "description": "The HTML 'title' attribute for the option. Allows for information on\nhover. For more information on this attribute, see\nhttps://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/title", "required": false}}}}, "required": false, "description": "An array of options for the select"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Set to True to disable the Select."}, "required": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'required'", "computed": false}, {"value": "'REQUIRED'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "This attribute specifies that the user must fill in a value before\nsubmitting a form. It cannot be used when the type attribute is hidden,\nimage, or a button type (submit, reset, or button). The :optional and\n:required CSS pseudo-classes will be applied to the field as appropriate.\nrequired is an HTML boolean attribute - it is enabled by a boolean or\n'required'. Alternative capitalizations `REQUIRED`\nare also acccepted."}, "valid": {"type": {"name": "bool"}, "required": false, "description": "Apply valid style to the Input for feedback purposes. This will cause\nany FormFeedback in the enclosing div with valid=True to display."}, "invalid": {"type": {"name": "bool"}, "required": false, "description": "Apply invalid style to the Input for feedback purposes. This will cause\nany FormFeedback in the enclosing div with valid=False to display."}, "size": {"type": {"name": "string"}, "required": false, "description": "Set the size of the Input. Options: 'sm' (small), 'md' (medium)\nor 'lg' (large). Default is 'md'."}, "html_size": {"type": {"name": "string"}, "required": false, "description": "This represents the number of rows in the select that should be visible at\none time. It will result in the Select being rendered as a scrolling list\nbox rather than a dropdown."}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}}}, "src/components/input/Switch.js": {"description": "Checklist is a component that encapsulates several checkboxes.\nThe values and labels of the checklist is specified in the `options`\nproperty and the checked items are specified with the `value` property.\nEach checkbox is rendered as an input / label pair. `Checklist` must be\ngiven an `id` to work properly.", "displayName": "Switch", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components in callbacks.\nThe ID needs to be unique across all of the components in an app."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nThe class of the container (div)"}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "input_style": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> checkbox element.", "defaultValue": {"value": "null", "computed": false}}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `input_style` instead.\n\nThe style of the <input> checkbox element.", "defaultValue": {"value": "{}", "computed": false}}, "input_class_name": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `input_class_name` instead.\n\nThe class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "label": {"type": {"name": "string"}, "required": false, "description": "The label of the <input> element"}, "label_id": {"type": {"name": "string"}, "required": false, "description": "The id of the label"}, "label_style": {"type": {"name": "object"}, "required": false, "description": "Inline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "null", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "**DEPRECATED** Use `label_style` instead.\n\nInline style arguments to apply to the <label> element for each item.", "defaultValue": {"value": "{}", "computed": false}}, "label_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_class_name` instead.\n\nCSS classes to apply to the <label> element for each item.", "defaultValue": {"value": "''", "computed": false}}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}, "value": {"type": {"name": "bool"}, "required": false, "description": "The value of the input.", "defaultValue": {"value": "false", "computed": false}}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Disable the Switch.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/input/Textarea.js": {"description": "A basic HTML textarea for entering multiline text based on the corresponding\ncomponent in dash-core-components", "displayName": "Textarea", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "value": {"type": {"name": "string"}, "required": false, "description": "The value of the textarea", "defaultValue": {"value": "''", "computed": false}}, "autofocus": {"type": {"name": "string"}, "required": false, "description": "The element should be automatically focused after the page loaded."}, "autoFocus": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `autofocus` instead\n\nThe element should be automatically focused after the page loaded."}, "cols": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the number of columns in a textarea."}, "disabled": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "bool"}]}, "required": false, "description": "Indicates whether the user can interact with the element."}, "form": {"type": {"name": "string"}, "required": false, "description": "Indicates the form that is the owner of the element."}, "maxlength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the maximum number of characters allowed in the element."}, "maxLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "**DEPRECATED** Use `maxlength` instead\n\nDefines the maximum number of characters allowed in the element."}, "minlength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the minimum number of characters allowed in the element."}, "minLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "**DEPRECATED** Use `minlength` instead\n\nDefines the minimum number of characters allowed in the element."}, "name": {"type": {"name": "string"}, "required": false, "description": "Name of the element. For example used by the server to identify the fields in form submits."}, "placeholder": {"type": {"name": "string"}, "required": false, "description": "Provides a hint to the user of what can be entered in the field."}, "readonly": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'readOnly'", "computed": false}, {"value": "'readonly'", "computed": false}, {"value": "'READONLY'", "computed": false}]}]}, "required": false, "description": "Indicates whether the element can be edited."}, "readOnly": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'readOnly'", "computed": false}, {"value": "'readonly'", "computed": false}, {"value": "'READONLY'", "computed": false}]}]}, "required": false, "description": "**DEPRECATED** Use `readonly` instead\n\nIndicates whether the element can be edited."}, "required": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'required'", "computed": false}, {"value": "'REQUIRED'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "This attribute specifies that the user must fill in a value before\nsubmitting a form. It cannot be used when the type attribute is hidden,\nimage, or a button type (submit, reset, or button). The :optional and\n:required CSS pseudo-classes will be applied to the field as appropriate.\nrequired is an HTML boolean attribute - it is enabled by a boolean or\n'required'. Alternative capitalizations `REQUIRED`\nare also acccepted."}, "rows": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the number of rows in a text area."}, "wrap": {"type": {"name": "string"}, "required": false, "description": "Indicates whether the text should be wrapped."}, "accesskey": {"type": {"name": "string"}, "required": false, "description": "Defines a keyboard shortcut to activate or add focus to the element."}, "accessKey": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `accesskey` instead\n\nDefines a keyboard shortcut to activate or add focus to the element."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "contenteditable": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Indicates whether the element's content is editable."}, "contentEditable": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "**DEPRECATED** Use `contenteditable` instead\n\nIndicates whether the element's content is editable."}, "contextmenu": {"type": {"name": "string"}, "required": false, "description": "Defines the ID of a <menu> element which will serve as the element's context menu."}, "contextMenu": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `contextmenu` instead\n\nDefines the ID of a <menu> element which will serve as the element's context menu."}, "dir": {"type": {"name": "string"}, "required": false, "description": "Defines the text direction. Allowed values are ltr (Left-To-Right) or rtl (Right-To-Left)"}, "draggable": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'true'", "computed": false}, {"value": "'false'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "Defines whether the element can be dragged."}, "hidden": {"type": {"name": "string"}, "required": false, "description": "Prevents rendering of given element, while keeping child elements, e.g. script elements, active."}, "lang": {"type": {"name": "string"}, "required": false, "description": "Defines the language used in the element."}, "spellcheck": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'true'", "computed": false}, {"value": "'false'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "Indicates whether spell checking is allowed for the element."}, "spellCheck": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'true'", "computed": false}, {"value": "'false'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "**DEPRECATED** Use `spellcheck` instead\n\nIndicates whether spell checking is allowed for the element."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "tabindex": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Overrides the browser's default tab order and follows the one specified instead."}, "tabIndex": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "**DEPRECATED** Use `tabindex` instead\n\nOverrides the browser's default tab order and follows the one specified instead."}, "title": {"type": {"name": "string"}, "required": false, "description": "Text to be displayed in a tooltip when hovering over the element."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "size": {"type": {"name": "string"}, "required": false, "description": "Set the size of the Textarea, valid options are 'sm', 'md', or 'lg'"}, "valid": {"type": {"name": "bool"}, "required": false, "description": "Apply valid style to the Textarea for feedback purposes. This will cause\nany FormFeedback in the enclosing div with valid=True to display."}, "invalid": {"type": {"name": "bool"}, "required": false, "description": "Apply invalid style to the Textarea for feedback purposes. This will cause\nany FormFeedback in the enclosing div with valid=False to display."}, "n_blur": {"type": {"name": "number"}, "required": false, "description": "Number of times the input lost focus.", "defaultValue": {"value": "0", "computed": false}}, "n_blur_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the input lost focus.", "defaultValue": {"value": "-1", "computed": false}}, "n_submit": {"type": {"name": "number"}, "required": false, "description": "Number of times the `Enter` key was pressed while the textarea had focus.", "defaultValue": {"value": "0", "computed": false}}, "n_submit_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time that `En<PERSON>` was pressed.", "defaultValue": {"value": "-1", "computed": false}}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "debounce": {"type": {"name": "bool"}, "required": false, "description": "If true, changes to input will be sent back to the Dash server only on enter or when losing focus.\nIf it's false, it will sent the value back on every change.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/layout/Col.js": {"description": "Component for creating Bootstrap columns to control the layout of your page.\n\nUse the width argument to specify width, or use the breakpoint arguments\n(xs, sm, md, lg, xl) to control the width of the columns on different screen\nsizes to achieve a responsive layout.", "displayName": "Col", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "width": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify the width of the column. Behind the scenes this sets behaviour at\nthe xs breakpoint, and will be overriden if xs is specified.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "xs": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify column behaviour on an extra small screen.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "sm": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify column behaviour on a small screen.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "md": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify column behaviour on a medium screen.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "lg": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify column behaviour on a large screen.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "xl": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify column behaviour on an extra large screen.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "xxl": {"type": {"name": "custom", "raw": "columnProps"}, "required": false, "description": "Specify column behaviour on an extra extra large screen.\n\nValid arguments are boolean, an integer in the range 1-12 inclusive, or a\ndictionary with keys 'offset', 'order', 'size'. See the documentation for\nmore details."}, "align": {"type": {"name": "enum", "value": [{"value": "'start'", "computed": false}, {"value": "'center'", "computed": false}, {"value": "'end'", "computed": false}, {"value": "'stretch'", "computed": false}, {"value": "'baseline'", "computed": false}]}, "required": false, "description": "Set vertical alignment of this column's content in the parent row. Options\nare 'start', 'center', 'end', 'stretch', 'baseline'."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/layout/Container.js": {"description": "Containers provide a means to center and horizontally pad your site’s\ncontents.", "displayName": "Container", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "fluid": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}]}, "required": false, "description": "If True the container-fluid class will be applied, and the Container will\nexpand to fill available space. A non-fluid container resizes responsively\nto a fixed width at the different breakpoints.\n\nYou can also set the fluid property to one of the Bootstrap breakpoints:\n\"sm\", \"md\", \"lg\", \"xl\" or \"xxl\", so that the container fluidly expands to\nfill the screen until the specified breakpoint, then resize responsively\nat higher breakpoints."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to apply the container class to, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/layout/Row.js": {"description": "Row is one of the core layout components in Bootstrap. Build up your layout\nas a series of rows of columns. <PERSON> has arguments for controlling the\nvertical and horizontal alignment of its children, as well as the spacing\nbetween columns.", "displayName": "Row", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "align": {"type": {"name": "enum", "value": [{"value": "'start'", "computed": false}, {"value": "'center'", "computed": false}, {"value": "'end'", "computed": false}, {"value": "'stretch'", "computed": false}, {"value": "'baseline'", "computed": false}]}, "required": false, "description": "Set vertical alignment of columns in this row. Options are 'start',\n'center', 'end', 'stretch' and 'baseline'."}, "justify": {"type": {"name": "enum", "value": [{"value": "'start'", "computed": false}, {"value": "'center'", "computed": false}, {"value": "'end'", "computed": false}, {"value": "'around'", "computed": false}, {"value": "'between'", "computed": false}, {"value": "'evenly'", "computed": false}]}, "required": false, "description": "Set horizontal spacing and alignment of columns in this row. Options are\n'start', 'center', 'end', 'around' and 'between'."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/listgroup/ListGroup.js": {"description": "Bootstrap list groups are a flexible way to display a series of content. Use\nin conjunction with `ListGroupItem`, `ListGroupItemHeading` and\n`ListGroupItemText`.", "displayName": "ListGroup", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the list, default: ul", "defaultValue": {"value": "'ul'", "computed": false}}, "flush": {"type": {"name": "bool"}, "required": false, "description": "When True the `list-group-flush` class is applied which removes some borders\nand rounded corners from the list group in order that they can be rendered\nedge-to-edge in the parent container (e.g. a Card)."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "horizontal": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}]}, "required": false, "description": "Set to True for a horizontal ListGroup, or supply one of the breakpoints\nas a string for a ListGroup that is horizontal at that breakpoint and up.\n\nNote that horizontal ListGroups cannot be combined with flush ListGroups,\nso if flush is True then horizontal has no effect."}}}, "src/components/listgroup/ListGroupItem.js": {"description": "Create a single item in a `ListGroup`.", "displayName": "ListGroupItem", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the listgroupitem, default: li"}, "active": {"type": {"name": "bool"}, "required": false, "description": "Apply active style to item"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Apply disabled style to item"}, "color": {"type": {"name": "string"}, "required": false, "description": "Item color, options: primary, secondary, success, info, warning,\ndanger, or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)\ndefault: secondary"}, "action": {"type": {"name": "bool"}, "required": false, "description": "Apply list-group-item-action class for hover animation etc."}, "href": {"type": {"name": "string"}, "required": false, "description": "Pass a URL (relative or absolute) to make the list group item a link."}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "target": {"type": {"name": "string"}, "required": false, "description": "Target attribute to pass on to the link. Only applies to external links."}}}, "src/components/modal/Modal.js": {"description": "Create a toggleable dialog using the Modal component. Toggle the visibility\nwith the `is_open` prop.", "displayName": "Modal", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the Modal, default: div"}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether modal is currently open."}, "centered": {"type": {"name": "bool"}, "required": false, "description": "If true, vertically center modal on page."}, "scrollable": {"type": {"name": "bool"}, "required": false, "description": "It true, scroll the modal body rather than the entire modal when it is too\nlong to all fit on the screen."}, "autofocus": {"type": {"name": "bool"}, "required": false, "description": "Puts the focus on the modal when initialized."}, "autoFocus": {"type": {"name": "bool"}, "required": false, "description": "**DEPRECATED** Use `autofocus` instead\n\n\tPuts the focus on the modal when initialized."}, "size": {"type": {"name": "string"}, "required": false, "description": "Set the size of the modal. Options sm, lg, xl for small, large or extra\nlarge sized modals, or leave undefined for default size."}, "role": {"type": {"name": "string"}, "required": false, "description": "The ARIA role attribute."}, "labelledby": {"type": {"name": "string"}, "required": false, "description": "The ARIA labelledby attribute"}, "labelledBy": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `labelledby` instead\n\nThe ARIA labelledby attribute"}, "keyboard": {"type": {"name": "bool"}, "required": false, "description": "Close the modal when escape key is pressed."}, "backdrop": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'static'", "computed": false}]}]}, "required": false, "description": "Includes a modal-backdrop element. Alternatively, specify 'static' for a\nbackdrop which doesn't close the modal on click."}, "modal_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS class to apply to the modal."}, "modalClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `modal_class_name` instead\n\nCSS class to apply to the modal."}, "backdrop_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS class to apply to the backdrop."}, "backdropClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `backdrop_class_name` instead\n\nCSS class to apply to the backdrop."}, "content_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS class to apply to the modal content."}, "contentClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `content_class_name` instead\n\nCSS class to apply to the modal content."}, "fade": {"type": {"name": "bool"}, "required": false, "description": "Set to false for a modal that simply appears rather than fades into view."}, "fullscreen": {"type": {"name": "enum", "value": [{"value": "PropTypes.bool", "computed": true}, {"value": "PropTypes.oneOf(['sm-down', 'md-down', 'lg-down', 'xl-down', 'xxl-down'])", "computed": true}]}, "required": false, "description": "Renders a fullscreen modal. Specifying a breakpoint will render the modal\nas fullscreen below the breakpoint size."}, "zindex": {"type": {"name": "union", "value": [{"name": "number"}, {"name": "string"}]}, "required": false, "description": "Set the z-index of the modal. Default 1050."}, "zIndex": {"type": {"name": "union", "value": [{"name": "number"}, {"name": "string"}]}, "required": false, "description": "**DEPRECATED** Use `zindex` instead\n\nSet the z-index of the modal. Default 1050."}}}, "src/components/modal/ModalBody.js": {"description": "Use this component to add consistent padding to the body (main content) of\nyour Modals.", "displayName": "ModalBody", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the ModalBody, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/modal/ModalFooter.js": {"description": "Add a footer to any modal.", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the ModalFooter, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/modal/ModalHeader.js": {"description": "Add a header to any modal.", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "close_button": {"type": {"name": "bool"}, "required": false, "description": "Add a close button to the header that can be used to close the modal.", "defaultValue": {"value": "true", "computed": false}}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the ModalHeader, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/modal/ModalTitle.js": {"description": "Add a title to any modal. Should be used as a child of the ModalHeader.", "displayName": "ModalTitle", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the ModalTitle, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/nav/Nav.js": {"description": "Nav can be used to group together a collection of navigation links.", "displayName": "Nav", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "pills": {"type": {"name": "bool"}, "required": false, "description": "Apply pill styling to nav items. Active items will be indicated by a pill."}, "card": {"type": {"name": "bool"}, "required": false, "description": "Set to True when using Nav with pills styling inside a CardHeader."}, "fill": {"type": {"name": "bool"}, "required": false, "description": "Expand the nav items to fill available horizontal space."}, "justified": {"type": {"name": "bool"}, "required": false, "description": "Expand the nav items to fill available horizontal space, making sure\nevery nav item has the same width."}, "vertical": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}]}, "required": false, "description": "Stack NavItems vertically. Set to True for a vertical Nav on all screen\nsizes, or pass one of the Bootstrap breakpoints ('xs', 'sm', 'md', 'lg',\n'xl') for a Nav which is vertical at that breakpoint and above, and\nhorizontal on smaller screens."}, "horizontal": {"type": {"name": "enum", "value": [{"value": "'start'", "computed": false}, {"value": "'center'", "computed": false}, {"value": "'end'", "computed": false}, {"value": "'between'", "computed": false}, {"value": "'around'", "computed": false}]}, "required": false, "description": "Specify the horizontal alignment of the NavItems. Options are 'start',\n'center', or 'end'."}, "navbar": {"type": {"name": "bool"}, "required": false, "description": "Set to True if using Nav in Navbar component. This applies the `navbar-nav`\nclass to the Nav which uses more lightweight styles to match the parent\nNavbar better."}, "navbar_scroll": {"type": {"name": "bool"}, "required": false, "description": "Enable vertical scrolling within the toggleable contents of a collapsed Navbar."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/nav/NavItem.js": {"description": "Create a single item in a `Nav`.", "displayName": "NavItem", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/nav/NavLink.js": {"description": "Add a link to a `Nav`. Can be used as a child of `NavItem` or of `Nav`\ndirectly.", "displayName": "NavLink", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "href": {"type": {"name": "string"}, "required": false, "description": "The URL of the linked resource."}, "active": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'partial'", "computed": false}, {"value": "'exact'", "computed": false}]}]}, "required": false, "description": "Apply 'active' style to this component. Set to \"exact\" to automatically\ntoggle active status when the current pathname matches href, or to\n\"partial\" to automatically toggle on a partial match. Assumes that href is\na relative url such as /link rather than an absolute such as\nhttps://example.com/link\n\nFor example\n- dbc.NavLink(..., href=\"/my-page\", active=\"exact\") will be active on\n  \"/my-page\" but not \"/my-page/other\" or \"/random\"\n- dbc.NavLink(..., href=\"/my-page\", active=\"partial\") will be active on\n  \"/my-page\" and \"/my-page/other\" but not \"/random\"", "defaultValue": {"value": "false", "computed": false}}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Disable the link", "defaultValue": {"value": "false", "computed": false}}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "target": {"type": {"name": "string"}, "required": false, "description": "Target attribute to pass on to the link. Only applies to external links."}}}, "src/components/nav/Navbar.js": {"description": "The Navbar component can be used to make fully customisable navbars.", "displayName": "<PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "light": {"type": {"name": "bool"}, "required": false, "description": "Applies the `navbar-light` class to the Navbar, causing text in the children\nof the Navbar to use dark colors for contrast / visibility.", "defaultValue": {"value": "true", "computed": false}}, "dark": {"type": {"name": "bool"}, "required": false, "description": "Applies the `navbar-dark` class to the Navbar, causing text in the children\nof the Navbar to use light colors for contrast / visibility."}, "fixed": {"type": {"name": "string"}, "required": false, "description": "Fix the navbar's position at the top or bottom of the page, options: top, bottom"}, "sticky": {"type": {"name": "enum", "value": [{"value": "'top'", "computed": false}]}, "required": false, "description": "Position the navbar at the top of the viewport, but only after scrolling past it.\nA convenience prop for the sticky-top positioning class. Not supported in <= IE11\nand other older browsers\n\nWith `sticky`, the navbar remains in the viewport when you scroll. By\ncontrast, with `fixed`, the navbar will remain at the top or bottom of\nthe page.  sticky='top'"}, "color": {"type": {"name": "string"}, "required": false, "description": "Sets the color of the Navbar. Main options are primary, light and dark, default light.\n\nYou can also choose one of the other contextual classes provided by Bootstrap\n(secondary, success, warning, danger, info, white) or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)", "defaultValue": {"value": "'light'", "computed": false}}, "role": {"type": {"name": "string"}, "required": false, "description": "The ARIA role attribute."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the Navbar, default 'nav'"}, "expand": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}]}, "required": false, "description": "Specify screen size at which to expand the menu bar, e.g. sm, md, lg etc.", "defaultValue": {"value": "'md'", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/nav/NavbarBrand.js": {"description": "Call out attention to a brand name or site title within a navbar.", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat this as an external link,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "href": {"type": {"name": "string"}, "required": false, "description": "URL of the linked resource"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/nav/NavbarSimple.js": {"description": "A self-contained navbar ready for use. If you need more customisability try\n`Navbar` instead.", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "brand": {"type": {"name": "string"}, "required": false, "description": "Brand text, to go top left of the navbar."}, "brand_href": {"type": {"name": "string"}, "required": false, "description": "Link to attach to brand."}, "brand_style": {"type": {"name": "object"}, "required": false, "description": "CSS style options for brand."}, "brand_external_link": {"type": {"name": "bool"}, "required": false, "description": "If true, the browser will treat the brand link as external,\nforcing a page refresh at the new location. If false,\nthis just changes the location without triggering a page\nrefresh. Use this if you are observing dcc.Location, for\ninstance. Defaults to true for absolute URLs and false\notherwise."}, "fluid": {"type": {"name": "bool"}, "required": false, "description": "The contents of the Navbar are wrapped in a container, use fluid=True to\nmake this container fluid, so that in particular, the contents of the\nnavbar fill the available horizontal space.", "defaultValue": {"value": "false", "computed": false}}, "links_left": {"type": {"name": "bool"}, "required": false, "description": "Align the navlinks in the navbar to the left. Default: False.", "defaultValue": {"value": "false", "computed": false}}, "light": {"type": {"name": "bool"}, "required": false, "description": "Applies the `navbar-light` class to the NavbarSimple, causing text in the\nchildren of the Navbar to use dark colors for contrast / visibility.", "defaultValue": {"value": "true", "computed": false}}, "dark": {"type": {"name": "bool"}, "required": false, "description": "Applies the `navbar-dark` class to the NavbarSimple, causing text in the\nchildren of the Navbar to use light colors for contrast / visibility."}, "fixed": {"type": {"name": "string"}, "required": false, "description": "Fix the navbar's position at the top or bottom of the page, options: top,\nbottom"}, "sticky": {"type": {"name": "string"}, "required": false, "description": "Stick the navbar to the top or the bottom of the viewport, options: top, bottom\n\nWith `sticky`, the navbar remains in the viewport when you scroll. By\ncontrast, with `fixed`, the navbar will remain at the top or bottom of\nthe page."}, "color": {"type": {"name": "string"}, "required": false, "description": "Sets the color of the NavbarSimple. Main options are primary, light and dark, default light.\n\nYou can also choose one of the other contextual classes provided by Bootstrap\n(secondary, success, warning, danger, info, white) or any valid CSS color of\nyour choice (e.g. a hex code, a decimal code or a CSS color name)", "defaultValue": {"value": "'light'", "computed": false}}, "expand": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}]}, "required": false, "description": "Specify breakpoint at which to expand the menu bar. Options are: 'xs',\n'sm', 'md', 'lg', or 'xl'. Below this breakpoint the navbar will collapse\nand navitems will be placed in a togglable collapse element.", "defaultValue": {"value": "'md'", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/nav/NavbarToggler.js": {"description": "Use this component to create a navbar toggle to show navlinks when the\nnavbar collapses on smaller screens.", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "type": {"type": {"name": "string"}, "required": false, "description": "Toggle type, default: button."}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times\nthat this element has been clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the time (in ms since 1970)\nat which n_clicks changed. This can be used to tell\nwhich button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/offcanvas/Offcanvas.js": {"description": "Create a toggleable hidden sidebar using the Offcanvas component.\nToggle the visibility with the `is_open` prop.", "displayName": "<PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - Use class_name instead.\n\nOften used with CSS to style elements with common properties."}, "labelledby": {"type": {"name": "string"}, "required": false, "description": "The ARIA labelledby attribute"}, "labelledBy": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `labelledby` instead\n\nThe ARIA labelledby attribute"}, "backdrop": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'static'", "computed": false}]}]}, "required": false, "description": "Includes an offcanvas-backdrop element. Alternatively, specify 'static' for a\nbackdrop which doesn't close the modal on click.", "defaultValue": {"value": "true", "computed": false}}, "backdrop_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS class to apply to the backdrop."}, "backdropClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - Use backdrop_class_name instead.\n\nCSS class to apply to the backdrop."}, "keyboard": {"type": {"name": "bool"}, "required": false, "description": "Close the offcanvas when escape key is pressed."}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether offcanvas is currently open.", "defaultValue": {"value": "false", "computed": false}}, "placement": {"type": {"name": "enum", "value": [{"value": "'start'", "computed": false}, {"value": "'end'", "computed": false}, {"value": "'top'", "computed": false}, {"value": "'bottom'", "computed": false}]}, "required": false, "description": "Which side of the viewport the offcanvas will appear from."}, "scrollable": {"type": {"name": "bool"}, "required": false, "description": "Allow body scrolling while off<PERSON><PERSON> is open."}, "autofocus": {"type": {"name": "bool"}, "required": false, "description": "Puts the focus on the offcanvas when initialized."}, "autoFocus": {"type": {"name": "bool"}, "required": false, "description": "**DEPRECATED** Use `autofocus` instead\n\n\tPuts the focus on the modal when initialized."}, "title": {"type": {"name": "string"}, "required": false, "description": "The header title"}, "close_button": {"type": {"name": "bool"}, "required": false, "description": "Specify whether the Component should contain a close button\nin the header", "defaultValue": {"value": "true", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/pagination/Pagination.js": {"description": "The container for presentational components for building a pagination UI.\nIndividual pages should be added as children using the `PaginationItem`\ncomponent.", "displayName": "Pagination", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - Use class_name instead.\n\nOften used with CSS to style elements with common properties."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "size": {"type": {"name": "enum", "value": [{"value": "'sm'", "computed": false}, {"value": "'lg'", "computed": false}]}, "required": false, "description": "Set the size of all page items in the pagination."}, "min_value": {"type": {"name": "number"}, "required": false, "description": "Minimum (leftmost) value to appear in the pagination.", "defaultValue": {"value": "1", "computed": false}}, "max_value": {"type": {"name": "number"}, "required": true, "description": "Maximum (rightmost) value to appear in the pagination. Must be defined.\nIf the `min_value` and `step` together cannot reach this value, then the\nnext stepped value is used as the maximum."}, "step": {"type": {"name": "number"}, "required": false, "description": "Page increment step.", "defaultValue": {"value": "1", "computed": false}}, "active_page": {"type": {"name": "number"}, "required": false, "description": "The currently active page", "defaultValue": {"value": "1", "computed": false}}, "fully_expanded": {"type": {"name": "bool"}, "required": false, "description": "When True, this will display all numbers between `min_value` and\n`max_value`.", "defaultValue": {"value": "true", "computed": false}}, "previous_next": {"type": {"name": "bool"}, "required": false, "description": "When True, this will display a previous and next icon before and after\nthe individual page numbers.", "defaultValue": {"value": "false", "computed": false}}, "first_last": {"type": {"name": "bool"}, "required": false, "description": "When True, this will display a first and last icon at the beginning\nand end of the component.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/popover/Popover.js": {"description": "Popover creates a toggleable overlay that can be used to provide additional\ninformation or content to users without having to load a new page or open a\nnew window.\n\nUse the `PopoverHeader` and `PopoverBody` components to control the layout\nof the children.", "displayName": "Popover", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "placement": {"type": {"name": "enum", "value": [{"value": "'auto'", "computed": false}, {"value": "'auto-start'", "computed": false}, {"value": "'auto-end'", "computed": false}, {"value": "'top'", "computed": false}, {"value": "'top-start'", "computed": false}, {"value": "'top-end'", "computed": false}, {"value": "'right'", "computed": false}, {"value": "'right-start'", "computed": false}, {"value": "'right-end'", "computed": false}, {"value": "'bottom'", "computed": false}, {"value": "'bottom-start'", "computed": false}, {"value": "'bottom-end'", "computed": false}, {"value": "'left'", "computed": false}, {"value": "'left-start'", "computed": false}, {"value": "'left-end'", "computed": false}]}, "required": false, "description": "Specify popover placement.", "defaultValue": {"value": "'right'", "computed": false}}, "target": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "object"}]}, "required": false, "description": "ID of the component to attach the popover to."}, "trigger": {"type": {"name": "string"}, "required": false, "description": "Space separated list of triggers (e.g. \"click hover focus legacy\"). These\nspecify ways in which the target component can toggle the popover. If not\nspecified you must toggle the popover yourself using callbacks. Options\nare:\n- \"click\": toggles the popover when the target is clicked.\n- \"hover\": toggles the popover when the target is hovered over with the\ncursor.\n- \"focus\": toggles the popover when the target receives focus\n- \"legacy\": toggles the popover when the target is clicked, but will also\ndismiss the popover when the user clicks outside of the popover."}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether the Popover is open or not."}, "hide_arrow": {"type": {"name": "bool"}, "required": false, "description": "Hide popover arrow."}, "inner_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS class to apply to the popover."}, "innerClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `inner_class_name` instead.\n\nCSS class to apply to the popover."}, "delay": {"type": {"name": "union", "value": [{"name": "shape", "value": {"show": {"name": "number", "required": false}, "hide": {"name": "number", "required": false}}}, {"name": "number"}]}, "required": false, "description": "Optionally override show/hide delays", "defaultValue": {"value": "{show: 0, hide: 50}", "computed": false}}, "offset": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Offset of the popover relative to its target. The offset can be passed as\na comma separated pair of values e.g. \"0,8\", where the first number,\nskidding, displaces the popover along the reference element. The second\nnumber, distance, displaces the popover away from, or toward, the\nreference element in the direction of its placement. A positive number\ndisplaces it further away, while a negative number lets it overlap the\nreference. See https://popper.js.org/docs/v2/modifiers/offset/ for more\ninfo.\n\nAlternatively, you can provide just a single 'distance' number e.g. 8 to\ndisplace it horizontally."}, "flip": {"type": {"name": "bool"}, "required": false, "description": "Whether to flip the direction of the popover if too close to the container\nedge, default True.", "defaultValue": {"value": "true", "computed": false}}, "body": {"type": {"name": "bool"}, "required": false, "description": "When body is `True`, the Popover will render all children in a\n`PopoverBody` automatically."}, "autohide": {"type": {"name": "bool"}, "required": false, "description": "Optionally hide popover when hovering over content - default False.", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'is_open'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['is_open']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/popover/PopoverBody.js": {"description": "Componnet for wrapping the body (i.e. main content) of a `Popover`.", "displayName": "PopoverBody", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the PopoverBody, default: div"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/popover/PopoverHeader.js": {"description": "Creates a header for use inside the `Popover` component.", "displayName": "PopoverHeader", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the PopoverHeader, default: h3"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/progress/Progress.js": {"description": "", "displayName": "Progress", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component. Use this to nest progress bars."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "bar": {"type": {"name": "bool"}, "required": false, "description": "Set to True when nesting Progress inside another Progress component to\ncreate a multi-progress bar."}, "min": {"type": {"name": "number"}, "required": false, "description": "Lower limit for value, default: 0"}, "max": {"type": {"name": "number"}, "required": false, "description": "Upper limit for value, default: 100"}, "value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Specify progress, value from min to max inclusive."}, "label": {"type": {"name": "string"}, "required": false, "description": "Adds a label to the progress bar."}, "hide_label": {"type": {"name": "bool"}, "required": false, "description": "Set to True to hide the label.", "defaultValue": {"value": "false", "computed": false}}, "animated": {"type": {"name": "bool"}, "required": false, "description": "Animate the bar, must have striped set to <PERSON> to work."}, "striped": {"type": {"name": "bool"}, "required": false, "description": "Use striped progress bar"}, "color": {"type": {"name": "string"}, "required": false, "description": "Set color of the progress bar, options: primary, secondary, success,\nwarning, danger, info or any valid CSS color\nof your choice (e.g. a hex code, a decimal code or a CSS color name)."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/spinner/Spinner.js": {"description": "Render Bootstrap style loading spinners using only CSS.\n\nThis component can be used standalone to render a loading spinner, or it can\nbe used like `dash_core_components.Loading` by giving it children. In the\nlatter case the chosen spinner will display while the children are loading.", "displayName": "Spinner", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component."}, "fullscreen_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles for the container when fullscreen=True."}, "spinner_style": {"type": {"name": "object"}, "required": false, "description": "Inline CSS styles to apply to the spinner."}, "fullscreen_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "fullscreenClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - use `fullscreen_class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "spinner_class_name": {"type": {"name": "string"}, "required": false, "description": "CSS class names to apply to the spinner."}, "spinnerClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - use `spinner_class_name` instead.\n\nCSS class names to apply to the spinner."}, "color": {"type": {"name": "string"}, "required": false, "description": "Sets the color of the Spinner. Main options are Bootstrap contextual\ncolors: primary, secondary, success, info, warning, danger, light, dark,\nbody, muted, white-50, black-50. You can also specify any valid CSS color\nof your choice (e.g. a hex code, a decimal code or a CSS color name)\n\nIf not specified will default to text colour."}, "type": {"type": {"name": "string"}, "required": false, "description": "The type of spinner. Options 'border' and 'grow'. Default 'border'.", "defaultValue": {"value": "'border'", "computed": false}}, "size": {"type": {"name": "string"}, "required": false, "description": "The spinner size. Options are 'sm', and 'md'."}, "fullscreen": {"type": {"name": "bool"}, "required": false, "description": "Boolean that determines if the loading spinner will be displayed\nfull-screen or not."}, "delay_hide": {"type": {"name": "number"}, "required": false, "description": "When using the spinner as a loading spinner, add a time delay (in ms) to\nthe spinner being removed to prevent flickering.", "defaultValue": {"value": "0", "computed": false}}, "delay_show": {"type": {"name": "number"}, "required": false, "description": "When using the spinner as a loading spinner, add a time delay (in ms) to\nthe spinner being shown after the loading_state is set to true.", "defaultValue": {"value": "0", "computed": false}}, "show_initially": {"type": {"name": "bool"}, "required": false, "description": "Whether the Spinner should show on app start-up before the loading state\nhas been determined. Default True.", "defaultValue": {"value": "true", "computed": false}}}}, "src/components/table/Table.js": {"description": "A component for applying Bootstrap styles to HTML tables. Use this as a\ndrop-in replacement for `html.Table`, or generate a table from a Pandas\nDataFrame using `dbc.Table.from_dataframe`.", "displayName": "Table", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "size": {"type": {"name": "string"}, "required": false, "description": "Specify table size, options: 'sm', 'md', 'lg'"}, "bordered": {"type": {"name": "bool"}, "required": false, "description": "Apply the `table-bordered` class which adds borders on all sides of the\ntable and cells."}, "borderless": {"type": {"name": "bool"}, "required": false, "description": "Apply the `table-borderless` class which removes all borders from the\ntable and cells."}, "striped": {"type": {"name": "bool"}, "required": false, "description": "Apply the `table-striped` class which applies 'zebra striping' to rows in\nthe table body."}, "color": {"type": {"name": "string"}, "required": false, "description": "Table color, options: primary, secondary, success, info, warning, danger,\ndark, light. Default: secondary."}, "dark": {"type": {"name": "bool"}, "required": false, "description": "**DEPRECATED** - Use color=\"dark\" instead.\n\nApply the `table-dark` class for dark cell backgrounds and light text."}, "hover": {"type": {"name": "bool"}, "required": false, "description": "Apply the `table-hover` class which enables a hover state on table rows\nwithin the table body."}, "responsive": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}]}, "required": false, "description": "Set to True or one of the breakpoints 'sm', 'md', 'lg', 'xl' to make table\nscroll horizontally at lower breakpoints."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/tabs/Tab.js": {"description": "Create a single tab. Should be used as a component of Tabs.", "displayName": "Tab", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the content of the Tab"}, "tab_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the NavItem in the tab."}, "active_tab_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the NavItem in the tab when it is active."}, "label_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the NavLink in the tab."}, "active_label_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the NavLink in the tab when it is active"}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "tab_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavItem in the tab."}, "tabClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `tab_class_name` instead\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavItem in the tab."}, "active_tab_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavItem in the tab when it\nis active."}, "activeTabClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `active_tab_class_name` instead\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavItem in the tab when it\nis active."}, "label_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavLink in the tab."}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `label_class_name` instead\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavLink in the tab."}, "active_label_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavLink in the tab when\nit is active."}, "activeLabelClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `active_label_class_name` instead\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the NavLink in the tab when\nit is active."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "label": {"type": {"name": "string"}, "required": false, "description": "The tab's label, displayed in the tab itself."}, "tab_id": {"type": {"name": "string"}, "required": false, "description": "Optional identifier for tab used for determining which tab is visible\nif not specified, and Tab is being used inside Tabs component, the tabId\nwill be set to \"tab-i\" where i is (zero indexed) position of tab in list\ntabs pased to Tabs component."}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Determines if tab is disabled or not - defaults to false", "defaultValue": {"value": "false", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/tabs/Tabs.js": {"description": "Create Bootstrap styled tabs. Use the `active_tab` property to set, or get\nget the currently active tab in a callback.", "displayName": "Tabs", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "active_tab": {"type": {"name": "string"}, "required": false, "description": "The tab_id of the currently active tab. If tab_id has not been specified\nfor the active tab, this will default to tab-i, where i is the index\n(starting from 0) of the tab."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'active_tab'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `active_tab` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['active_tab']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/toast/Toast.js": {"description": "Toasts can be used to push messages and notifactions to users. Control\nvisibility of the toast with the `is_open` prop, or use `duration` to set a\ntimer for auto-dismissal.", "displayName": "Toast", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "header_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the header of the toast."}, "header_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the header of the toast."}, "headerClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - use `header_class_name` instead\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the header of the toast."}, "body_style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set. The styles\nset here apply to the body of the toast."}, "body_class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the body of the toast."}, "bodyClassName": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** - use `body_class_name` instead.\n\nOften used with CSS to style elements with common properties. The classes\nspecified with this prop will be applied to the body of the toast."}, "tag": {"type": {"name": "string"}, "required": false, "description": "HTML tag to use for the Toast, default: div"}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether Toast is currently open.", "defaultValue": {"value": "true", "computed": false}}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "header": {"type": {"name": "string"}, "required": false, "description": "Text to populate the header with"}, "dismissable": {"type": {"name": "bool"}, "required": false, "description": "Set to True to add a dismiss button to the header which will close the\ntoast on click", "defaultValue": {"value": "false", "computed": false}}, "duration": {"type": {"name": "number"}, "required": false, "description": "Duration in milliseconds after which the Alert dismisses itself."}, "n_dismiss": {"type": {"name": "number"}, "required": false, "description": "An integer that represents the number of times that the dismiss button has\nbeen clicked on.", "defaultValue": {"value": "0", "computed": false}}, "n_dismiss_timestamp": {"type": {"name": "number"}, "required": false, "description": "Use of *_timestamp props has been deprecated in <PERSON> in favour of dash.callback_context.\nSee \"How do I determine which Input has changed?\" in the Dash FAQs https://dash.plot.ly/faqs.\n\nAn integer that represents the time (in ms since 1970) at which n_dismiss\nchanged. This can be used to tell which button was changed most recently.", "defaultValue": {"value": "-1", "computed": false}}, "icon": {"type": {"name": "string"}, "required": false, "description": "Add a contextually coloured icon to the header of the toast. Options are:\n\"primary\", \"secondary\", \"success\", \"warning\", \"danger\", \"info\", \"light\" or\n\"dark\"."}, "color": {"type": {"name": "string"}, "required": false, "description": "Toast color, options: primary, secondary, success, info, warning, danger,\nlight, dark. Default: secondary."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'is_open'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['is_open']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/tooltip/Tooltip.js": {"description": "A component for adding tooltips to any element, no callbacks required!\n\nSimply add the Tooltip to you layout, and give it a target (id of a\ncomponent to which the tooltip should be attached)", "displayName": "<PERSON><PERSON><PERSON>", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "class_name": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "className": {"type": {"name": "string"}, "required": false, "description": "**DEPRECATED** Use `class_name` instead.\n\nOften used with CSS to style elements with common properties."}, "key": {"type": {"name": "string"}, "required": false, "description": "A unique identifier for the component, used to improve\nperformance by React.js while rendering components\nSee https://reactjs.org/docs/lists-and-keys.html for more info"}, "target": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "object"}]}, "required": false, "description": "The id of the element to attach the tooltip to"}, "placement": {"type": {"name": "enum", "value": [{"value": "'auto'", "computed": false}, {"value": "'auto-start'", "computed": false}, {"value": "'auto-end'", "computed": false}, {"value": "'top'", "computed": false}, {"value": "'top-start'", "computed": false}, {"value": "'top-end'", "computed": false}, {"value": "'right'", "computed": false}, {"value": "'right-start'", "computed": false}, {"value": "'right-end'", "computed": false}, {"value": "'bottom'", "computed": false}, {"value": "'bottom-start'", "computed": false}, {"value": "'bottom-end'", "computed": false}, {"value": "'left'", "computed": false}, {"value": "'left-start'", "computed": false}, {"value": "'left-end'", "computed": false}]}, "required": false, "description": "How to place the tooltip.", "defaultValue": {"value": "'auto'", "computed": false}}, "flip": {"type": {"name": "bool"}, "required": false, "description": "Whether to flip the direction of the popover if too close to the container\nedge, default True.", "defaultValue": {"value": "true", "computed": false}}, "delay": {"type": {"name": "shape", "value": {"show": {"name": "number", "required": false}, "hide": {"name": "number", "required": false}}}, "required": false, "description": "Control the delay of hide and show events.", "defaultValue": {"value": "{show: 0, hide: 50}", "computed": false}}, "autohide": {"type": {"name": "bool"}, "required": false, "description": "Optionally hide tooltip when hovering over tooltip content - default True.", "defaultValue": {"value": "true", "computed": false}}, "fade": {"type": {"name": "bool"}, "required": false, "description": "If True, a fade animation will be applied when `is_open` is toggled. If\nFalse the Alert will simply appear and disappear.", "defaultValue": {"value": "true", "computed": false}}, "trigger": {"type": {"name": "string"}, "required": false, "description": "Space separated list of triggers (e.g. \"click hover focus legacy\"). These\nspecify ways in which the target component can toggle the tooltip. If\nomitted you must toggle the tooltip yourself using callbacks. Options\nare:\n- \"click\": toggles the popover when the target is clicked.\n- \"hover\": toggles the popover when the target is hovered over with the\ncursor.\n- \"focus\": toggles the popover when the target receives focus\n- \"legacy\": toggles the popover when the target is clicked, but will also\ndismiss the popover when the user clicks outside of the popover.\n\nDefault is \"hover focus\"", "defaultValue": {"value": "'hover focus'", "computed": false}}, "is_open": {"type": {"name": "bool"}, "required": false, "description": "Whether the Tooltip is open or not."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}}