{"src/components/Checklist.react.js": {"description": "Checklist is a component that encapsulates several checkboxes.\nThe values and labels of the checklist are specified in the `options`\nproperty and the checked items are specified with the `value` property.\nEach checkbox is rendered as an input with a surrounding label.", "displayName": "Checklist", "methods": [], "props": {"options": {"type": {"name": "union", "value": [{"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}, "description": "Array of options where the label and the value are the same thing - [string|number|bool]"}, {"name": "object", "description": "Simpler `options` representation in dictionary format. The order is not guaranteed.\n{`value1`: `label1`, `value2`: `label2`, ... }\nwhich is equal to\n[{label: `label1`, value: `value1`}, {label: `label2`, value: `value2`}, ...]"}, {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "node", "description": "The option's label", "required": true}, "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}], "description": "The value of the option. This value\ncorresponds to the items specified in the\n`value` property.", "required": true}, "disabled": {"name": "bool", "description": "If true, this option is disabled and cannot be selected.", "required": false}, "title": {"name": "string", "description": "The HTML 'title' attribute for the option. Allows for\ninformation on hover. For more information on this attribute,\nsee https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/title", "required": false}}}, "description": "An array of options {label: [string|number], value: [string|number]},\nan optional disabled field can be used for each option"}]}, "required": false, "description": "An array of options", "defaultValue": {"value": "[]", "computed": false}}, "value": {"type": {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}}, "required": false, "description": "The currently selected value", "defaultValue": {"value": "[]", "computed": false}}, "inline": {"type": {"name": "bool"}, "required": false, "description": "Indicates whether labelStyle should be inline or not\nTrue: Automatically set { 'display': 'inline-block' } to labelStyle\nFalse: No additional styles are passed into labelStyle.", "defaultValue": {"value": "false", "computed": false}}, "className": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> checkbox element", "defaultValue": {"value": "{}", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> checkbox element", "defaultValue": {"value": "''", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "The style of the <label> that wraps the checkbox input\n and the option's label", "defaultValue": {"value": "{}", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "The class of the <label> that wraps the checkbox input\n and the option's label", "defaultValue": {"value": "''", "computed": false}}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Clipboard.react.js": {"description": "The Clipboard component copies text to the clipboard", "displayName": "Clipboard", "methods": [{"name": "stringifyId", "docblock": null, "modifiers": [], "params": [{"name": "id", "type": null}], "returns": null}, {"name": "copySuccess", "docblock": null, "modifiers": ["async"], "params": [{"name": "content", "type": null}], "returns": null}, {"name": "getTargetText", "docblock": null, "modifiers": [], "params": [], "returns": null}, {"name": "loading", "docblock": null, "modifiers": ["async"], "params": [], "returns": null}, {"name": "copyToClipboard", "docblock": null, "modifiers": ["async"], "params": [], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID used to identify this component."}, "target_id": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "object"}]}, "required": false, "description": "The id of target component containing text to copy to the clipboard.\nThe inner text of the `children` prop will be copied to the clipboard.  If none, then the text from the\n `value` prop will be copied.", "defaultValue": {"value": "null", "computed": false}}, "content": {"type": {"name": "string"}, "required": false, "description": "The text to  be copied to the clipboard if the `target_id` is None.", "defaultValue": {"value": "null", "computed": false}}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "The number of times copy button was clicked", "defaultValue": {"value": "0", "computed": false}}, "title": {"type": {"name": "string"}, "required": false, "description": "The text shown as a tooltip when hovering over the copy icon."}, "style": {"type": {"name": "object"}, "required": false, "description": "The icon's styles"}, "className": {"type": {"name": "string"}, "required": false, "description": "The class  name of the icon element"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/ConfirmDialog.react.js": {"description": "ConfirmDialog is used to display the browser's native \"confirm\" modal,\nwith an optional message and two buttons (\"OK\" and \"Cancel\").\nThis ConfirmDialog can be used in conjunction with buttons when the user\nis performing an action that should require an extra step of verification.", "displayName": "ConfirmDialog", "methods": [{"name": "_update", "docblock": null, "modifiers": [], "params": [{"name": "shouldTriggerDisplay", "type": null}], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "message": {"type": {"name": "string"}, "required": false, "description": "Message to show in the popup."}, "submit_n_clicks": {"type": {"name": "number"}, "required": false, "description": "Number of times the submit button was clicked", "defaultValue": {"value": "0", "computed": false}}, "submit_n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the submit button was clicked.", "defaultValue": {"value": "-1", "computed": false}}, "cancel_n_clicks": {"type": {"name": "number"}, "required": false, "description": "Number of times the popup was canceled.", "defaultValue": {"value": "0", "computed": false}}, "cancel_n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the cancel button was clicked.", "defaultValue": {"value": "-1", "computed": false}}, "displayed": {"type": {"name": "bool"}, "required": false, "description": "Set to true to send the ConfirmDialog."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/ConfirmDialogProvider.react.js": {"description": "A wrapper component that will display a confirmation dialog\nwhen its child component has been clicked on.\n\nFor example:\n```\ndcc.ConfirmDialogProvider(\n    html.Button('click me', id='btn'),\n    message='Danger - Are you sure you want to continue.'\n    id='confirm')\n```", "displayName": "ConfirmDialogProvider", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "message": {"type": {"name": "string"}, "required": false, "description": "Message to show in the popup."}, "submit_n_clicks": {"type": {"name": "number"}, "required": false, "description": "Number of times the submit was clicked", "defaultValue": {"value": "0", "computed": false}}, "submit_n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the submit button was clicked.", "defaultValue": {"value": "-1", "computed": false}}, "cancel_n_clicks": {"type": {"name": "number"}, "required": false, "description": "Number of times the popup was canceled.", "defaultValue": {"value": "0", "computed": false}}, "cancel_n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the cancel button was clicked.", "defaultValue": {"value": "-1", "computed": false}}, "displayed": {"type": {"name": "bool"}, "required": false, "description": "Is the modal currently displayed."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "children": {"type": {"name": "any"}, "required": false, "description": "The children to hijack clicks from and display the popup."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/DatePickerRange.react.js": {"description": "DatePickerRange is a tailor made component designed for selecting\ntimespan across multiple days off of a calendar.\n\nThe DatePicker integrates well with the Python datetime module with the\nstartDate and endDate being returned in a string format suitable for\ncreating datetime objects.\n\nThis component is based off of Airbnb's react-dates react component\nwhich can be found here: https://github.com/airbnb/react-dates", "displayName": "DatePickerRange", "methods": [], "props": {"start_date": {"type": {"name": "string"}, "required": false, "description": "Specifies the starting date for the component.\nAccepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "end_date": {"type": {"name": "string"}, "required": false, "description": "Specifies the ending date for the component.\nAccepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "min_date_allowed": {"type": {"name": "string"}, "required": false, "description": "Specifies the lowest selectable date for the component.\nAccepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "max_date_allowed": {"type": {"name": "string"}, "required": false, "description": "Specifies the highest selectable date for the component.\nAccepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "disabled_days": {"type": {"name": "arrayOf", "value": {"name": "string"}}, "required": false, "description": "Specifies additional days between min_date_allowed and max_date_allowed\nthat should be disabled. Accepted datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'", "defaultValue": {"value": "[]", "computed": false}}, "minimum_nights": {"type": {"name": "number"}, "required": false, "description": "Specifies a minimum number of nights that must be selected between\nthe startDate and the endDate"}, "updatemode": {"type": {"name": "enum", "value": [{"value": "'singledate'", "computed": false}, {"value": "'bothdates'", "computed": false}]}, "required": false, "description": "Determines when the component should update\nits value. If `bothdates`, then the DatePicker\nwill only trigger its value when the user has\nfinished picking both dates. If `singledate`, then\nthe DatePicker will update its value\nas one date is picked.", "defaultValue": {"value": "'singledate'", "computed": false}}, "start_date_placeholder_text": {"type": {"name": "string"}, "required": false, "description": "Text that will be displayed in the first input\nbox of the date picker when no date is selected. Default value is 'Start Date'"}, "end_date_placeholder_text": {"type": {"name": "string"}, "required": false, "description": "Text that will be displayed in the second input\nbox of the date picker when no date is selected. Default value is 'End Date'"}, "initial_visible_month": {"type": {"name": "string"}, "required": false, "description": "Specifies the month that is initially presented when the user\nopens the calendar. Accepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "clearable": {"type": {"name": "bool"}, "required": false, "description": "Whether or not the dropdown is \"clearable\", that is, whether or\nnot a small \"x\" appears on the right of the dropdown that removes\nthe selected value.", "defaultValue": {"value": "false", "computed": false}}, "reopen_calendar_on_clear": {"type": {"name": "bool"}, "required": false, "description": "If True, the calendar will automatically open when cleared", "defaultValue": {"value": "false", "computed": false}}, "display_format": {"type": {"name": "string"}, "required": false, "description": "Specifies the format that the selected dates will be displayed\nvalid formats are variations of \"MM YY DD\". For example:\n\"MM YY DD\" renders as '05 10 97' for May 10th 1997\n\"MMMM, YY\" renders as 'May, 1997' for May 10th 1997\n\"M, D, YYYY\" renders as '07, 10, 1997' for September 10th 1997\n\"MMMM\" renders as 'May' for May 10 1997"}, "month_format": {"type": {"name": "string"}, "required": false, "description": "Specifies the format that the month will be displayed in the calendar,\nvalid formats are variations of \"MM YY\". For example:\n\"MM YY\" renders as '05 97' for May 1997\n\"MMMM, YYYY\" renders as 'May, 1997' for May 1997\n\"MMM, YY\" renders as 'Sep, 97' for September 1997"}, "first_day_of_week": {"type": {"name": "enum", "value": [{"value": "0", "computed": false}, {"value": "1", "computed": false}, {"value": "2", "computed": false}, {"value": "3", "computed": false}, {"value": "4", "computed": false}, {"value": "5", "computed": false}, {"value": "6", "computed": false}]}, "required": false, "description": "Specifies what day is the first day of the week, values must be\nfrom [0, ..., 6] with 0 denoting Sunday and 6 denoting Saturday", "defaultValue": {"value": "0", "computed": false}}, "show_outside_days": {"type": {"name": "bool"}, "required": false, "description": "If True the calendar will display days that rollover into\nthe next month"}, "stay_open_on_select": {"type": {"name": "bool"}, "required": false, "description": "If True the calendar will not close when the user has selected a value\nand will wait until the user clicks off the calendar", "defaultValue": {"value": "false", "computed": false}}, "calendar_orientation": {"type": {"name": "enum", "value": [{"value": "'vertical'", "computed": false}, {"value": "'horizontal'", "computed": false}]}, "required": false, "description": "Orientation of calendar, either vertical or horizontal.\nValid options are 'vertical' or 'horizontal'.", "defaultValue": {"value": "'horizontal'", "computed": false}}, "number_of_months_shown": {"type": {"name": "number"}, "required": false, "description": "Number of calendar months that are shown when calendar is opened", "defaultValue": {"value": "1", "computed": false}}, "with_portal": {"type": {"name": "bool"}, "required": false, "description": "If True, calendar will open in a screen overlay portal,\nnot supported on vertical calendar", "defaultValue": {"value": "false", "computed": false}}, "with_full_screen_portal": {"type": {"name": "bool"}, "required": false, "description": "If True, calendar will open in a full screen overlay portal, will\ntake precedent over 'withPortal' if both are set to true,\nnot supported on vertical calendar", "defaultValue": {"value": "false", "computed": false}}, "day_size": {"type": {"name": "number"}, "required": false, "description": "Size of rendered calendar days, higher number\nmeans bigger day size and larger calendar overall", "defaultValue": {"value": "39", "computed": false}}, "is_RTL": {"type": {"name": "bool"}, "required": false, "description": "Determines whether the calendar and days operate\nfrom left to right or from right to left", "defaultValue": {"value": "false", "computed": false}}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "If True, no dates can be selected.", "defaultValue": {"value": "false", "computed": false}}, "start_date_id": {"type": {"name": "string"}, "required": false, "description": "The HTML element ID of the start date input field.\nNot used by Dash, only by CSS."}, "end_date_id": {"type": {"name": "string"}, "required": false, "description": "The HTML element ID of the end date input field.\nNot used by Dash, only by CSS."}, "style": {"type": {"name": "object"}, "required": false, "description": "CSS styles appended to wrapper div"}, "className": {"type": {"name": "string"}, "required": false, "description": "Appends a CSS class to the wrapper div component."}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, any `persisted_props` that the\nuser has changed while using the app will keep those changes, as long as\nthe new prop value also matches what was given originally.\nUsed in conjunction with `persistence_type` and `persisted_props`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'start_date'", "computed": false}, {"value": "'end_date'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page.", "defaultValue": {"value": "['start_date', 'end_date']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/DatePickerSingle.react.js": {"description": "DatePickerSingle is a tailor made component designed for selecting\na single day off of a calendar.\n\nThe DatePicker integrates well with the Python datetime module with the\nstartDate and endDate being returned in a string format suitable for\ncreating datetime objects.\n\nThis component is based off of Airbnb's react-dates react component\nwhich can be found here: https://github.com/airbnb/react-dates", "displayName": "DatePickerSingle", "methods": [], "props": {"date": {"type": {"name": "string"}, "required": false, "description": "Specifies the starting date for the component, best practice is to pass\nvalue via datetime object"}, "min_date_allowed": {"type": {"name": "string"}, "required": false, "description": "Specifies the lowest selectable date for the component.\nAccepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "max_date_allowed": {"type": {"name": "string"}, "required": false, "description": "Specifies the highest selectable date for the component.\nAccepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "disabled_days": {"type": {"name": "arrayOf", "value": {"name": "string"}}, "required": false, "description": "Specifies additional days between min_date_allowed and max_date_allowed\nthat should be disabled. Accepted datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'", "defaultValue": {"value": "[]", "computed": false}}, "placeholder": {"type": {"name": "string"}, "required": false, "description": "Text that will be displayed in the input\nbox of the date picker when no date is selected.\nDefault value is 'Start Date'"}, "initial_visible_month": {"type": {"name": "string"}, "required": false, "description": "Specifies the month that is initially presented when the user\nopens the calendar. Accepts datetime.datetime objects or strings\nin the format 'YYYY-MM-DD'"}, "clearable": {"type": {"name": "bool"}, "required": false, "description": "Whether or not the dropdown is \"clearable\", that is, whether or\nnot a small \"x\" appears on the right of the dropdown that removes\nthe selected value.", "defaultValue": {"value": "false", "computed": false}}, "reopen_calendar_on_clear": {"type": {"name": "bool"}, "required": false, "description": "If True, the calendar will automatically open when cleared", "defaultValue": {"value": "false", "computed": false}}, "display_format": {"type": {"name": "string"}, "required": false, "description": "Specifies the format that the selected dates will be displayed\nvalid formats are variations of \"MM YY DD\". For example:\n\"MM YY DD\" renders as '05 10 97' for May 10th 1997\n\"MMMM, YY\" renders as 'May, 1997' for May 10th 1997\n\"M, D, YYYY\" renders as '07, 10, 1997' for September 10th 1997\n\"MMMM\" renders as 'May' for May 10 1997"}, "month_format": {"type": {"name": "string"}, "required": false, "description": "Specifies the format that the month will be displayed in the calendar,\nvalid formats are variations of \"MM YY\". For example:\n\"MM YY\" renders as '05 97' for May 1997\n\"MMMM, YYYY\" renders as 'May, 1997' for May 1997\n\"MMM, YY\" renders as 'Sep, 97' for September 1997"}, "first_day_of_week": {"type": {"name": "enum", "value": [{"value": "0", "computed": false}, {"value": "1", "computed": false}, {"value": "2", "computed": false}, {"value": "3", "computed": false}, {"value": "4", "computed": false}, {"value": "5", "computed": false}, {"value": "6", "computed": false}]}, "required": false, "description": "Specifies what day is the first day of the week, values must be\nfrom [0, ..., 6] with 0 denoting Sunday and 6 denoting Saturday", "defaultValue": {"value": "0", "computed": false}}, "show_outside_days": {"type": {"name": "bool"}, "required": false, "description": "If True the calendar will display days that rollover into\nthe next month", "defaultValue": {"value": "true", "computed": false}}, "stay_open_on_select": {"type": {"name": "bool"}, "required": false, "description": "If True the calendar will not close when the user has selected a value\nand will wait until the user clicks off the calendar", "defaultValue": {"value": "false", "computed": false}}, "calendar_orientation": {"type": {"name": "enum", "value": [{"value": "'vertical'", "computed": false}, {"value": "'horizontal'", "computed": false}]}, "required": false, "description": "Orientation of calendar, either vertical or horizontal.\nValid options are 'vertical' or 'horizontal'.", "defaultValue": {"value": "'horizontal'", "computed": false}}, "number_of_months_shown": {"type": {"name": "number"}, "required": false, "description": "Number of calendar months that are shown when calendar is opened", "defaultValue": {"value": "1", "computed": false}}, "with_portal": {"type": {"name": "bool"}, "required": false, "description": "If True, calendar will open in a screen overlay portal,\nnot supported on vertical calendar", "defaultValue": {"value": "false", "computed": false}}, "with_full_screen_portal": {"type": {"name": "bool"}, "required": false, "description": "If True, calendar will open in a full screen overlay portal, will\ntake precedent over 'withPortal' if both are set to True,\nnot supported on vertical calendar", "defaultValue": {"value": "false", "computed": false}}, "day_size": {"type": {"name": "number"}, "required": false, "description": "Size of rendered calendar days, higher number\nmeans bigger day size and larger calendar overall", "defaultValue": {"value": "39", "computed": false}}, "is_RTL": {"type": {"name": "bool"}, "required": false, "description": "Determines whether the calendar and days operate\nfrom left to right or from right to left", "defaultValue": {"value": "false", "computed": false}}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "If True, no dates can be selected.", "defaultValue": {"value": "false", "computed": false}}, "style": {"type": {"name": "object"}, "required": false, "description": "CSS styles appended to wrapper div"}, "className": {"type": {"name": "string"}, "required": false, "description": "Appends a CSS class to the wrapper div component."}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `date` that the user has\nchanged while using the app will keep that change, as long as\nthe new `date` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'date'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `date` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['date']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Download.react.js": {"description": "The Download component opens a download dialog when the data property changes.", "displayName": "Download", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components in callbacks."}, "data": {"type": {"name": "exact", "value": {"filename": {"name": "string", "description": "Suggested filename in the download dialogue.", "required": true}, "content": {"name": "string", "description": "File content.", "required": true}, "base64": {"name": "bool", "description": "Set to true, when data is base64 encoded.", "required": false}, "type": {"name": "string", "description": "Blob type, usually a MIME-type.", "required": false}}}, "required": false, "description": "On change, a download is invoked."}, "base64": {"type": {"name": "bool"}, "required": false, "description": "Default value for base64, used when not set as part of the data property.", "defaultValue": {"value": "false", "computed": false}}, "type": {"type": {"name": "string"}, "required": false, "description": "Default value for type, used when not set as part of the data property.", "defaultValue": {"value": "'text/plain'", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-supplied function for updating props."}}}, "src/components/Dropdown.react.js": {"description": "Dropdown is an interactive dropdown element for selecting one or more\nitems.\nThe values and labels of the dropdown items are specified in the `options`\nproperty and the selected item(s) are specified with the `value` property.\n\nUse a dropdown when you have many options (more than 5) or when you are\nconstrained for space. Otherwise, you can use RadioItems or a Checklist,\nwhich have the benefit of showing the users all of the items at once.", "displayName": "Dropdown", "methods": [], "props": {"options": {"type": {"name": "union", "value": [{"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}, "description": "Array of options where the label and the value are the same thing - [string|number|bool]"}, {"name": "object", "description": "Simpler `options` representation in dictionary format. The order is not guaranteed.\n{`value1`: `label1`, `value2`: `label2`, ... }\nwhich is equal to\n[{label: `label1`, value: `value1`}, {label: `label2`, value: `value2`}, ...]"}, {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "node", "description": "The option's label", "required": true}, "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}], "description": "The value of the option. This value\ncorresponds to the items specified in the\n`value` property.", "required": true}, "disabled": {"name": "bool", "description": "If true, this option is disabled and cannot be selected.", "required": false}, "title": {"name": "string", "description": "The HTML 'title' attribute for the option. Allows for\ninformation on hover. For more information on this attribute,\nsee https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/title", "required": false}, "search": {"name": "string", "description": "Optional search value for the option, to use if the label\nis a component or provide a custom search value different\nfrom the label. If no search value and the label is a\ncomponent, the `value` will be used for search.", "required": false}}}, "description": "An array of options {label: [string|number], value: [string|number]},\nan optional disabled field can be used for each option"}]}, "required": false, "description": "An array of options {label: [string|number], value: [string|number]},\nan optional disabled field can be used for each option"}, "value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}, {"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}}]}, "required": false, "description": "The value of the input. If `multi` is false (the default)\nthen value is just a string that corresponds to the values\nprovided in the `options` property. If `multi` is true, then\nmultiple values can be selected at once, and `value` is an\narray of items with values corresponding to those in the\n`options` prop."}, "multi": {"type": {"name": "bool"}, "required": false, "description": "If true, the user can select multiple values", "defaultValue": {"value": "false", "computed": false}}, "clearable": {"type": {"name": "bool"}, "required": false, "description": "Whether or not the dropdown is \"clearable\", that is, whether or\nnot a small \"x\" appears on the right of the dropdown that removes\nthe selected value.", "defaultValue": {"value": "true", "computed": false}}, "searchable": {"type": {"name": "bool"}, "required": false, "description": "Whether to enable the searching feature or not", "defaultValue": {"value": "true", "computed": false}}, "search_value": {"type": {"name": "string"}, "required": false, "description": "The value typed in the DropDown for searching."}, "placeholder": {"type": {"name": "string"}, "required": false, "description": "The grey, default text shown when no option is selected"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "If true, this dropdown is disabled and the selection cannot be changed.", "defaultValue": {"value": "false", "computed": false}}, "optionHeight": {"type": {"name": "number"}, "required": false, "description": "height of each option. Can be increased when label lengths would wrap around", "defaultValue": {"value": "35", "computed": false}}, "maxHeight": {"type": {"name": "number"}, "required": false, "description": "height of the options dropdown.", "defaultValue": {"value": "200", "computed": false}}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "className": {"type": {"name": "string"}, "required": false, "description": "className of the dropdown element"}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the input changes"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Graph.react.js": {"description": "Graph can be used to render any plotly.js-powered data visualization.\n\nYou can define callbacks based on user interaction with Graphs such as\nhovering, clicking or selecting", "displayName": "PlotlyGraph", "methods": [{"name": "clearState", "docblock": null, "modifiers": [], "params": [{"name": "dataKey", "type": null}], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "responsive": {"type": {"name": "enum", "value": [{"value": "true", "computed": false}, {"value": "false", "computed": false}, {"value": "'auto'", "computed": false}]}, "required": false, "description": "If True, the Plotly.js plot will be fully responsive to window resize\nand parent element resize event. This is achieved by overriding\n`config.responsive` to True, `figure.layout.autosize` to True and unsetting\n`figure.layout.height` and `figure.layout.width`.\nIf False, the Plotly.js plot not be responsive to window resize and\nparent element resize event. This is achieved by overriding `config.responsive`\nto False and `figure.layout.autosize` to False.\nIf 'auto' (default), the Graph will determine if the Plotly.js plot can be made fully\nresponsive (True) or not (False) based on the values in `config.responsive`,\n`figure.layout.autosize`, `figure.layout.height`, `figure.layout.width`.\nThis is the legacy behavior of the Graph component.\n\nNeeds to be combined with appropriate dimension / styling through the `style` prop\nto fully take effect.", "defaultValue": {"value": "'auto'", "computed": false}}, "clickData": {"type": {"name": "object"}, "required": false, "description": "Data from latest click event. Read-only.", "defaultValue": {"value": "null", "computed": false}}, "clickAnnotationData": {"type": {"name": "object"}, "required": false, "description": "Data from latest click annotation event. Read-only.", "defaultValue": {"value": "null", "computed": false}}, "hoverData": {"type": {"name": "object"}, "required": false, "description": "Data from latest hover event. Read-only.", "defaultValue": {"value": "null", "computed": false}}, "clear_on_unhover": {"type": {"name": "bool"}, "required": false, "description": "If True, `clear_on_unhover` will clear the `hoverData` property\nwhen the user \"unhovers\" from a point.\nIf False, then the `hoverData` property will be equal to the\ndata from the last point that was hovered over.", "defaultValue": {"value": "false", "computed": false}}, "selectedData": {"type": {"name": "object"}, "required": false, "description": "Data from latest select event. Read-only.", "defaultValue": {"value": "null", "computed": false}}, "relayoutData": {"type": {"name": "object"}, "required": false, "description": "Data from latest relayout event which occurs\nwhen the user zooms or pans on the plot or other\nlayout-level edits. Has the form `{<attr string>: <value>}`\ndescribing the changes made. Read-only.", "defaultValue": {"value": "null", "computed": false}}, "extendData": {"type": {"name": "union", "value": [{"name": "array"}, {"name": "object"}]}, "required": false, "description": "Data that should be appended to existing traces. Has the form\n`[updateData, traceIndices, maxPoints]`, where `updateData` is an object\ncontaining the data to extend, `traceIndices` (optional) is an array of\ntrace indices that should be extended, and `maxPoints` (optional) is\neither an integer defining the maximum number of points allowed or an\nobject with key:value pairs matching `updateData`\nReference the Plotly.extendTraces API for full usage:\nhttps://plotly.com/javascript/plotlyjs-function-reference/#plotlyextendtraces", "defaultValue": {"value": "null", "computed": false}}, "prependData": {"type": {"name": "union", "value": [{"name": "array"}, {"name": "object"}]}, "required": false, "description": "Data that should be prepended to existing traces. Has the form\n`[updateData, traceIndices, maxPoints]`, where `updateData` is an object\ncontaining the data to prepend, `traceIndices` (optional) is an array of\ntrace indices that should be prepended, and `maxPoints` (optional) is\neither an integer defining the maximum number of points allowed or an\nobject with key:value pairs matching `updateData`\nReference the Plotly.prependTraces API for full usage:\nhttps://plotly.com/javascript/plotlyjs-function-reference/#plotlyprependtraces", "defaultValue": {"value": "null", "computed": false}}, "restyleData": {"type": {"name": "array"}, "required": false, "description": "Data from latest restyle event which occurs\nwhen the user toggles a legend item, changes\nparcoords selections, or other trace-level edits.\nHas the form `[edits, indices]`, where `edits` is an object\n`{<attr string>: <value>}` describing the changes made,\nand `indices` is an array of trace indices that were edited.\nRead-only.", "defaultValue": {"value": "null", "computed": false}}, "figure": {"type": {"name": "exact", "value": {"data": {"name": "arrayOf", "value": {"name": "object"}, "required": false}, "layout": {"name": "object", "required": false}, "frames": {"name": "arrayOf", "value": {"name": "object"}, "required": false}}}, "required": false, "description": "Plotly `figure` object. See schema:\nhttps://plotly.com/javascript/reference\n\n`config` is set separately by the `config` property", "defaultValue": {"value": "{\n    data: [],\n    layout: {},\n    frames: [],\n}", "computed": false}}, "style": {"type": {"name": "object"}, "required": false, "description": "Generic style overrides on the plot div"}, "className": {"type": {"name": "string"}, "required": false, "description": "className of the parent div"}, "mathjax": {"type": {"name": "bool"}, "required": false, "description": "If true, loads mathjax v3 (tex-svg) into the page and use it in the graph", "defaultValue": {"value": "false", "computed": false}}, "animate": {"type": {"name": "bool"}, "required": false, "description": "Beta: If true, animate between updates using\nplotly.js's `animate` function", "defaultValue": {"value": "false", "computed": false}}, "animation_options": {"type": {"name": "object"}, "required": false, "description": "Beta: Object containing animation settings.\nOnly applies if `animate` is `true`", "defaultValue": {"value": "{\n    frame: {\n        redraw: false,\n    },\n    transition: {\n        duration: 750,\n        ease: 'cubic-in-out',\n    },\n}", "computed": false}}, "config": {"type": {"name": "exact", "value": {"staticPlot": {"name": "bool", "description": "No interactivity, for export or image generation", "required": false}, "plotlyServerURL": {"name": "string", "description": "Base URL for a Plotly cloud instance, if `showSendToCloud` is enabled", "required": false}, "editable": {"name": "bool", "description": "We can edit titles, move annotations, etc - sets all pieces of `edits`\nunless a separate `edits` config item overrides individual parts", "required": false}, "editSelection": {"name": "bool", "description": "Enables moving selections", "required": false}, "edits": {"name": "exact", "value": {"annotationPosition": {"name": "bool", "description": "The main anchor of the annotation, which is the\ntext (if no arrow) or the arrow (which drags the whole thing leaving\nthe arrow length & direction unchanged)", "required": false}, "annotationTail": {"name": "bool", "description": "Just for annotations with arrows, change the length and direction of the arrow", "required": false}, "annotationText": {"name": "bool", "required": false}, "axisTitleText": {"name": "bool", "required": false}, "colorbarPosition": {"name": "bool", "required": false}, "colorbarTitleText": {"name": "bool", "required": false}, "legendPosition": {"name": "bool", "required": false}, "legendText": {"name": "bool", "description": "Edit the trace name fields from the legend", "required": false}, "shapePosition": {"name": "bool", "required": false}, "titleText": {"name": "bool", "description": "The global `layout.title`", "required": false}}, "description": "A set of editable properties", "required": false}, "autosizable": {"name": "bool", "description": "DO autosize once regardless of layout.autosize\n(use default width or height values otherwise)", "required": false}, "responsive": {"name": "bool", "description": "Whether to change layout size when the window size changes", "required": false}, "queueLength": {"name": "number", "description": "Set the length of the undo/redo queue", "required": false}, "fillFrame": {"name": "bool", "description": "If we DO autosize, do we fill the container or the screen?", "required": false}, "frameMargins": {"name": "number", "description": "If we DO autosize, set the frame margins in percents of plot size", "required": false}, "scrollZoom": {"name": "bool", "description": "Mousewheel or two-finger scroll zooms the plot", "required": false}, "doubleClick": {"name": "enum", "value": [{"value": "false", "computed": false}, {"value": "'reset'", "computed": false}, {"value": "'autosize'", "computed": false}, {"value": "'reset+autosize'", "computed": false}], "description": "Double click interaction (false, 'reset', 'autosize' or 'reset+autosize')", "required": false}, "doubleClickDelay": {"name": "number", "description": "Delay for registering a double-click event in ms. The\nminimum value is 100 and the maximum value is 1000. By\ndefault this is 300.", "required": false}, "showTips": {"name": "bool", "description": "New users see some hints about interactivity", "required": false}, "showAxisDragHandles": {"name": "bool", "description": "Enable axis pan/zoom drag handles", "required": false}, "showAxisRangeEntryBoxes": {"name": "bool", "description": "Enable direct range entry at the pan/zoom drag points\n(drag handles must be enabled above)", "required": false}, "showLink": {"name": "bool", "description": "Link to open this plot in plotly", "required": false}, "sendData": {"name": "bool", "description": "If we show a link, does it contain data or just link to a plotly file?", "required": false}, "linkText": {"name": "string", "description": "Text appearing in the sendData link", "required": false}, "displayModeBar": {"name": "enum", "value": [{"value": "true", "computed": false}, {"value": "false", "computed": false}, {"value": "'hover'", "computed": false}], "description": "Display the mode bar (true, false, or 'hover')", "required": false}, "showSendToCloud": {"name": "bool", "description": "Should we include a modebar button to send this data to a\nPlotly Cloud instance, linked by `plotlyServerURL`.\nBy default this is false.", "required": false}, "showEditInChartStudio": {"name": "bool", "description": "Should we show a modebar button to send this data to a\nPlotly Chart Studio plot. If both this and showSendToCloud\nare selected, only showEditInChartStudio will be\nhonored. By default this is false.", "required": false}, "modeBarButtonsToRemove": {"name": "array", "description": "Remove mode bar button by name.\nAll modebar button names at https://github.com/plotly/plotly.js/blob/master/src/components/modebar/buttons.js\nCommon names include:\nsendDataToCloud;\n(2D) zoom2d, pan2d, select2d, lasso2d, zoomIn2d, zoomOut2d, autoScale2d, resetScale2d;\n(Cartesian) hoverClosestCartesian, hoverCompareCartesian;\n(3D) zoom3d, pan3d, orbitRotation, tableRotation, handleDrag3d, resetCameraDefault3d, resetCameraLastSave3d, hoverClosest3d;\n(Geo) zoomInGeo, zoomOutGeo, resetGeo, hoverClosestGeo;\nhoverClosestGl2d, hoverClosestPie, toggleHover, resetViews.", "required": false}, "modeBarButtonsToAdd": {"name": "array", "description": "Add mode bar button using config objects", "required": false}, "modeBarButtons": {"name": "any", "description": "Fully custom mode bar buttons as nested array,\nwhere the outer arrays represents button groups, and\nthe inner arrays have buttons config objects or names of default buttons", "required": false}, "toImageButtonOptions": {"name": "exact", "value": {"format": {"name": "enum", "value": [{"value": "'jpeg'", "computed": false}, {"value": "'png'", "computed": false}, {"value": "'webp'", "computed": false}, {"value": "'svg'", "computed": false}], "description": "The file format to create", "required": false}, "filename": {"name": "string", "description": "The name given to the downloaded file", "required": false}, "width": {"name": "number", "description": "Width of the downloaded file, in px", "required": false}, "height": {"name": "number", "description": "Height of the downloaded file, in px", "required": false}, "scale": {"name": "number", "description": "Extra resolution to give the file after\nrendering it with the given width and height", "required": false}}, "description": "Modifications to how the toImage modebar button works", "required": false}, "displaylogo": {"name": "bool", "description": "Add the plotly logo on the end of the mode bar", "required": false}, "watermark": {"name": "bool", "description": "Add the plotly logo even with no modebar", "required": false}, "plotGlPixelRatio": {"name": "number", "description": "Increase the pixel ratio for Gl plot images", "required": false}, "topojsonURL": {"name": "string", "description": "URL to topojson files used in geo charts", "required": false}, "mapboxAccessToken": {"name": "any", "description": "Mapbox access token (required to plot mapbox trace types)\nIf using an Mapbox Atlas server, set this option to '',\nso that plotly.js won't attempt to authenticate to the public Mapbox server.", "required": false}, "locale": {"name": "string", "description": "The locale to use. Locales may be provided with the plot\n(`locales` below) or by loading them on the page, see:\nhttps://github.com/plotly/plotly.js/blob/master/dist/README.md#to-include-localization", "required": false}, "locales": {"name": "object", "description": "Localization definitions, if you choose to provide them with the\nplot rather than registering them globally.", "required": false}}}, "required": false, "description": "Plotly.js config options.\nSee https://plotly.com/javascript/configuration-options/\nfor more info.", "defaultValue": {"value": "{}", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Function that updates the state tree."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}, "composes": ["../fragments/Graph.privateprops"]}, "src/components/Input.react.js": {"description": "A basic HTML input control for entering text, numbers, or passwords.\n\nNote that checkbox and radio types are supported through\nthe Checklist and RadioItems component. Dates, times, and file uploads\nare also supported through separate components.", "displayName": "Input", "methods": [{"name": "setInputValue", "docblock": null, "modifiers": [], "params": [{"name": "base", "type": null}, {"name": "value", "type": null}], "returns": null}, {"name": "setPropValue", "docblock": null, "modifiers": [], "params": [{"name": "base", "type": null}, {"name": "value", "type": null}], "returns": null}, {"name": "onEvent", "docblock": null, "modifiers": [], "params": [], "returns": null}, {"name": "onBlur", "docblock": null, "modifiers": [], "params": [], "returns": null}, {"name": "onKeyPress", "docblock": null, "modifiers": [], "params": [{"name": "e", "type": null}], "returns": null}, {"name": "onChange", "docblock": null, "modifiers": [], "params": [], "returns": null}], "props": {"value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The value of the input"}, "type": {"type": {"name": "enum", "value": [{"value": "'text'", "computed": false}, {"value": "'number'", "computed": false}, {"value": "'password'", "computed": false}, {"value": "'email'", "computed": false}, {"value": "'range'", "computed": false}, {"value": "'search'", "computed": false}, {"value": "'tel'", "computed": false}, {"value": "'url'", "computed": false}, {"value": "'hidden'", "computed": false}]}, "required": false, "description": "The type of control to render.", "defaultValue": {"value": "'text'", "computed": false}}, "debounce": {"type": {"name": "bool"}, "required": false, "description": "If true, changes to input will be sent back to the Dash server only on enter or when losing focus.\nIf it's false, it will sent the value back on every change.", "defaultValue": {"value": "false", "computed": false}}, "placeholder": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "A hint to the user of what can be entered in the control . The placeholder text must not contain carriage returns or line-feeds. Note: Do not use the placeholder attribute instead of a <label> element, their purposes are different. The <label> attribute describes the role of the form element (i.e. it indicates what kind of information is expected), and the placeholder attribute is a hint about the format that the content should take. There are cases in which the placeholder attribute is never displayed to the user, so the form must be understandable without it."}, "n_submit": {"type": {"name": "number"}, "required": false, "description": "Number of times the `Enter` key was pressed while the input had focus.", "defaultValue": {"value": "0", "computed": false}}, "n_submit_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time that `En<PERSON>` was pressed.", "defaultValue": {"value": "-1", "computed": false}}, "inputMode": {"type": {"name": "enum", "value": [{"value": "'verbatim'", "computed": false}, {"value": "'latin'", "computed": false}, {"value": "'latin-name'", "computed": false}, {"value": "'latin-prose'", "computed": false}, {"value": "'full-width-latin'", "computed": false}, {"value": "'kana'", "computed": false}, {"value": "'katakana'", "computed": false}, {"value": "'numeric'", "computed": false}, {"value": "'tel'", "computed": false}, {"value": "'email'", "computed": false}, {"value": "'url'", "computed": false}]}, "required": false, "description": "Provides a hint to the browser as to the type of data that might be\nentered by the user while editing the element or its contents."}, "autoComplete": {"type": {"name": "string"}, "required": false, "description": "This attribute indicates whether the value of the control can be automatically completed by the browser."}, "readOnly": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'readOnly'", "computed": false}, {"value": "'readonly'", "computed": false}, {"value": "'READONLY'", "computed": false}]}]}, "required": false, "description": "This attribute indicates that the user cannot modify the value of the control. The value of the attribute is irrelevant. If you need read-write access to the input value, do not add the \"readonly\" attribute. It is ignored if the value of the type attribute is hidden, range, color, checkbox, radio, file, or a button type (such as button or submit).\nreadOnly is an HTML boolean attribute - it is enabled by a boolean or\n'readOnly'. Alternative capitalizations `readonly` & `READONLY`\nare also acccepted."}, "required": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'required'", "computed": false}, {"value": "'REQUIRED'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "This attribute specifies that the user must fill in a value before submitting a form. It cannot be used when the type attribute is hidden, image, or a button type (submit, reset, or button). The :optional and :required CSS pseudo-classes will be applied to the field as appropriate.\nrequired is an HTML boolean attribute - it is enabled by a boolean or\n'required'. Alternative capitalizations `REQUIRED`\nare also acccepted."}, "autoFocus": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'autoFocus'", "computed": false}, {"value": "'autofocus'", "computed": false}, {"value": "'AUTOFOCUS'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "The element should be automatically focused after the page loaded.\nautoFocus is an HTML boolean attribute - it is enabled by a boolean or\n'autoFocus'. Alternative capitalizations `autofocus` & `AUTOFOCUS`\nare also acccepted."}, "disabled": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'disabled'", "computed": false}, {"value": "'DISABLED'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "If true, the input is disabled and can't be clicked on.\ndisabled is an HTML boolean attribute - it is enabled by a boolean or\n'disabled'. Alternative capitalizations `DISABLED`"}, "list": {"type": {"name": "string"}, "required": false, "description": "Identifies a list of pre-defined options to suggest to the user.\nThe value must be the id of a <datalist> element in the same document.\nThe browser displays only options that are valid values for this\ninput element.\nThis attribute is ignored when the type attribute's value is\nhidden, checkbox, radio, file, or a button type."}, "multiple": {"type": {"name": "bool"}, "required": false, "description": "This Boolean attribute indicates whether the user can enter more than one value. This attribute applies when the type attribute is set to email or file, otherwise it is ignored."}, "spellCheck": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'true'", "computed": false}, {"value": "'false'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "Setting the value of this attribute to true indicates that the element needs to have its spelling and grammar checked. The value default indicates that the element is to act according to a default behavior, possibly based on the parent element's own spellcheck value. The value false indicates that the element should not be checked."}, "name": {"type": {"name": "string"}, "required": false, "description": "The name of the control, which is submitted with the form data."}, "min": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The minimum (numeric or date-time) value for this item, which must not be greater than its maximum (max attribute) value."}, "max": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "The maximum (numeric or date-time) value for this item, which must not be less than its minimum (min attribute) value."}, "step": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Works with the min and max attributes to limit the increments at which a numeric or date-time value can be set. It can be the string any or a positive floating point number. If this attribute is not set to any, the control accepts only values at multiples of the step value greater than the minimum.", "defaultValue": {"value": "'any'", "computed": false}}, "minLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "If the value of the type attribute is text, email, search, password, tel, or url, this attribute specifies the minimum number of characters (in Unicode code points) that the user can enter. For other control types, it is ignored."}, "maxLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "If the value of the type attribute is text, email, search, password, tel, or url, this attribute specifies the maximum number of characters (in UTF-16 code units) that the user can enter. For other control types, it is ignored. It can exceed the value of the size attribute. If it is not specified, the user can enter an unlimited number of characters. Specifying a negative number results in the default behavior (i.e. the user can enter an unlimited number of characters). The constraint is evaluated only when the value of the attribute has been changed."}, "pattern": {"type": {"name": "string"}, "required": false, "description": "A regular expression that the control's value is checked against. The pattern must match the entire value, not just some subset. Use the title attribute to describe the pattern to help the user. This attribute applies when the value of the type attribute is text, search, tel, url, email, or password, otherwise it is ignored. The regular expression language is the same as JavaScript RegExp algorithm, with the 'u' parameter that makes it treat the pattern as a sequence of unicode code points. The pattern is not surrounded by forward slashes."}, "selectionStart": {"type": {"name": "string"}, "required": false, "description": "The offset into the element's text content of the first selected character. If there's no selection, this value indicates the offset to the character following the current text input cursor position (that is, the position the next character typed would occupy)."}, "selectionEnd": {"type": {"name": "string"}, "required": false, "description": "The offset into the element's text content of the last selected character. If there's no selection, this value indicates the offset to the character following the current text input cursor position (that is, the position the next character typed would occupy)."}, "selectionDirection": {"type": {"name": "string"}, "required": false, "description": "The direction in which selection occurred. This is \"forward\" if the selection was made from left-to-right in an LTR locale or right-to-left in an RTL locale, or \"backward\" if the selection was made in the opposite direction. On platforms on which it's possible this value isn't known, the value can be \"none\"; for example, on macOS, the default direction is \"none\", then as the user begins to modify the selection using the keyboard, this will change to reflect the direction in which the selection is expanding."}, "n_blur": {"type": {"name": "number"}, "required": false, "description": "Number of times the input lost focus.", "defaultValue": {"value": "0", "computed": false}}, "n_blur_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the input lost focus.", "defaultValue": {"value": "-1", "computed": false}}, "size": {"type": {"name": "string"}, "required": false, "description": "The initial size of the control. This value is in pixels unless the value of the type attribute is text or password, in which case it is an integer number of characters. Starting in, this attribute applies only when the type attribute is set to text, search, tel, url, email, or password, otherwise it is ignored. In addition, the size must be greater than zero. If you do not specify a size, a default value of 20 is used.' simply states \"the user agent should ensure that at least that many characters are visible\", but different characters can have different widths in certain fonts. In some browsers, a certain string with x characters will not be entirely visible even if size is defined to at least x."}, "style": {"type": {"name": "object"}, "required": false, "description": "The input's inline styles"}, "className": {"type": {"name": "string"}, "required": false, "description": "The class of the input element"}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Interval.react.js": {"description": "A component that repeatedly increments a counter `n_intervals`\nwith a fixed time delay between each increment.\nInterval is good for triggering a component on a recurring basis.\nThe time delay is set with the property \"interval\" in milliseconds.", "displayName": "Interval", "methods": [{"name": "handleTimer", "docblock": null, "modifiers": [], "params": [{"name": "props", "type": null}], "returns": null}, {"name": "resetTimer", "docblock": null, "modifiers": [], "params": [{"name": "props", "type": null}], "returns": null}, {"name": "clearTimer", "docblock": null, "modifiers": [], "params": [], "returns": null}, {"name": "reportInterval", "docblock": null, "modifiers": [], "params": [], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "interval": {"type": {"name": "number"}, "required": false, "description": "This component will increment the counter `n_intervals` every\n`interval` milliseconds", "defaultValue": {"value": "1000", "computed": false}}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "If True, the counter will no longer update"}, "n_intervals": {"type": {"name": "number"}, "required": false, "description": "Number of times the interval has passed", "defaultValue": {"value": "0", "computed": false}}, "max_intervals": {"type": {"name": "number"}, "required": false, "description": "Number of times the interval will be fired.\nIf -1, then the interval has no limit (the default)\nand if 0 then the interval stops running.", "defaultValue": {"value": "-1", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "<PERSON> assigned callback"}}}, "src/components/Link.react.js": {"description": "Link allows you to create a clickable link within a multi-page app.\n\nFor links with destinations outside the current app, `html.A` is a better\ncomponent to use.", "displayName": "Link", "methods": [{"name": "updateLocation", "docblock": null, "modifiers": [], "params": [{"name": "e", "type": null}], "returns": null}], "props": {"children": {"type": {"name": "node"}, "required": false, "description": "The children of this component"}, "href": {"type": {"name": "string"}, "required": true, "description": "The URL of a linked resource."}, "target": {"type": {"name": "string"}, "required": false, "description": "Specifies where to open the link reference."}, "refresh": {"type": {"name": "bool"}, "required": false, "description": "Controls whether or not the page will refresh when the link is clicked", "defaultValue": {"value": "false", "computed": false}}, "title": {"type": {"name": "string"}, "required": false, "description": "Adds the title attribute to your link, which can contain supplementary\ninformation."}, "className": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/Loading.react.js": {"description": "A Loading component that wraps any other component and displays a spinner until the wrapped component has rendered.", "displayName": "Loading", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "children": {"type": {"name": "union", "value": [{"name": "arrayOf", "value": {"name": "node"}}, {"name": "node"}]}, "required": false, "description": "Array that holds components to render"}, "type": {"type": {"name": "enum", "value": [{"value": "'graph'", "computed": false}, {"value": "'cube'", "computed": false}, {"value": "'circle'", "computed": false}, {"value": "'dot'", "computed": false}, {"value": "'default'", "computed": false}]}, "required": false, "description": "Property that determines which spinner to show\none of 'graph', 'cube', 'circle', 'dot', or 'default'.", "defaultValue": {"value": "'default'", "computed": false}}, "fullscreen": {"type": {"name": "bool"}, "required": false, "description": "Boolean that makes the spinner display full-screen"}, "debug": {"type": {"name": "bool"}, "required": false, "description": "If true, the spinner will display the component_name and prop_name\nwhile loading"}, "className": {"type": {"name": "string"}, "required": false, "description": "Additional CSS class for the spinner root DOM node"}, "parent_className": {"type": {"name": "string"}, "required": false, "description": "Additional CSS class for the outermost dcc.Loading parent div DOM node"}, "style": {"type": {"name": "object"}, "required": false, "description": "Additional CSS styling for the spinner root DOM node"}, "parent_style": {"type": {"name": "object"}, "required": false, "description": "Additional CSS styling for the outermost dcc.Loading parent div DOM node"}, "color": {"type": {"name": "string"}, "required": false, "description": "Primary colour used for the loading spinners", "defaultValue": {"value": "'#119DFF'", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/Location.react.js": {"description": "Update and track the current window.location object through the window.history state.\nUse in conjunction with the `dash_core_components.Link` component to make apps with multiple pages.", "displayName": "Location", "methods": [{"name": "updateLocation", "docblock": null, "modifiers": [], "params": [{"name": "props", "type": null}], "returns": null}, {"name": "onLocationChange", "docblock": null, "modifiers": [], "params": [], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": true, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "pathname": {"type": {"name": "string"}, "required": false, "description": "pathname in window.location - e.g., \"/my/full/pathname\""}, "search": {"type": {"name": "string"}, "required": false, "description": "search in window.location - e.g., \"?myargument=1\""}, "hash": {"type": {"name": "string"}, "required": false, "description": "hash in window.location - e.g., \"#myhash\""}, "href": {"type": {"name": "string"}, "required": false, "description": "href in window.location - e.g., \"/my/full/pathname?myargument=1#myhash\""}, "refresh": {"type": {"name": "bool"}, "required": false, "description": "Refresh the page when the location is updated?", "defaultValue": {"value": "true", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/LogoutButton.react.js": {"description": "Logout button to submit a form post request to the `logout_url` prop.\nUsage is intended for dash-deployment-server authentication.\n\nDDS usage:\n\n`dcc.LogoutButton(logout_url=os.getenv('DASH_LOGOUT_URL'))`\n\nCustom usage:\n\n- Implement a login mechanism.\n- Create a flask route with a post method handler.\n`@app.server.route('/logout', methods=['POST'])`\n  - The logout route should perform what's necessary for the user to logout.\n  - If you store the session in a cookie, clear the cookie:\n  `rep = flask.Response(); rep.set_cookie('session', '', expires=0)`\n\n- Create a logout button component and assign it the logout_url\n`dcc.LogoutButton(logout_url='/logout')`\n\nSee https://dash.plotly.com/dash-core-components/logout_button\nfor more documentation and examples.", "displayName": "LogoutButton", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "Id of the button."}, "label": {"type": {"name": "string"}, "required": false, "description": "Text of the button", "defaultValue": {"value": "'Logout'", "computed": false}}, "logout_url": {"type": {"name": "string"}, "required": false, "description": "Url to submit a post logout request."}, "style": {"type": {"name": "object"}, "required": false, "description": "Style of the button"}, "method": {"type": {"name": "string"}, "required": false, "description": "Http method to submit the logout form.", "defaultValue": {"value": "'post'", "computed": false}}, "className": {"type": {"name": "string"}, "required": false, "description": "CSS class for the button."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/Markdown.react.js": {"description": "A component that renders Markdown text as specified by the\nGitHub Markdown spec. These component uses\n[react-markdown](https://rexxars.github.io/react-markdown/) under the hood.", "displayName": "DashMarkdown", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "className": {"type": {"name": "string"}, "required": false, "description": "Class name of the container element"}, "mathjax": {"type": {"name": "bool"}, "required": false, "description": "If true, loads mathjax v3 (tex-svg) into the page and use it in the markdown", "defaultValue": {"value": "false", "computed": false}}, "dangerously_allow_html": {"type": {"name": "bool"}, "required": false, "description": "A boolean to control raw HTML escaping.\nSetting HTML from code is risky because it's easy to\ninadvertently expose your users to a cross-site scripting (XSS)\n(https://en.wikipedia.org/wiki/Cross-site_scripting) attack.", "defaultValue": {"value": "false", "computed": false}}, "link_target": {"type": {"name": "string"}, "required": false, "description": "A string for the target attribute to use on links (such as \"_blank\")"}, "children": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "arrayOf", "value": {"name": "string"}}]}, "required": false, "description": "A markdown string (or array of strings) that adheres to the CommonMark spec"}, "dedent": {"type": {"name": "bool"}, "required": false, "description": "Remove matching leading whitespace from all lines.\nLines that are empty, or contain *only* whitespace, are ignored.\nBoth spaces and tab characters are removed, but only if they match;\nwe will not convert tabs to spaces or vice versa.", "defaultValue": {"value": "true", "computed": false}}, "highlight_config": {"type": {"name": "exact", "value": {"theme": {"name": "enum", "value": [{"value": "'dark'", "computed": false}, {"value": "'light'", "computed": false}], "description": "Color scheme; default 'light'", "required": false}}}, "required": false, "description": "Config options for syntax highlighting.", "defaultValue": {"value": "{}", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "style": {"type": {"name": "object"}, "required": false, "description": "User-defined inline styles for the rendered Markdown"}}}, "src/components/RadioItems.react.js": {"description": "RadioItems is a component that encapsulates several radio item inputs.\nThe values and labels of the RadioItems is specified in the `options`\nproperty and the seleced item is specified with the `value` property.\nEach radio item is rendered as an input with a surrounding label.", "displayName": "RadioItems", "methods": [], "props": {"options": {"type": {"name": "union", "value": [{"name": "arrayOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}, "description": "Array of options where the label and the value are the same thing - [string|number|bool]"}, {"name": "object", "description": "Simpler `options` representation in dictionary format. The order is not guaranteed.\n{`value1`: `label1`, `value2`: `label2`, ... }\nwhich is equal to\n[{label: `label1`, value: `value1`}, {label: `label2`, value: `value2`}, ...]"}, {"name": "arrayOf", "value": {"name": "exact", "value": {"label": {"name": "node", "description": "The option's label", "required": true}, "value": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}], "description": "The value of the option. This value\ncorresponds to the items specified in the\n`value` property.", "required": true}, "disabled": {"name": "bool", "description": "If true, this option is disabled and cannot be selected.", "required": false}, "title": {"name": "string", "description": "The HTML 'title' attribute for the option. Allows for\ninformation on hover. For more information on this attribute,\nsee https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/title", "required": false}}}, "description": "An array of options {label: [string|number], value: [string|number]},\nan optional disabled field can be used for each option"}]}, "required": false, "description": "An array of options, or inline dictionary of options", "defaultValue": {"value": "[]", "computed": false}}, "value": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}, {"name": "bool"}]}, "required": false, "description": "The currently selected value"}, "inline": {"type": {"name": "bool"}, "required": false, "description": "Indicates whether labelStyle should be inline or not\nTrue: Automatically set { 'display': 'inline-block' } to labelStyle\nFalse: No additional styles are passed into labelStyle.", "defaultValue": {"value": "false", "computed": false}}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the container (div)"}, "className": {"type": {"name": "string"}, "required": false, "description": "The class of the container (div)"}, "inputStyle": {"type": {"name": "object"}, "required": false, "description": "The style of the <input> radio element", "defaultValue": {"value": "{}", "computed": false}}, "inputClassName": {"type": {"name": "string"}, "required": false, "description": "The class of the <input> radio element", "defaultValue": {"value": "''", "computed": false}}, "labelStyle": {"type": {"name": "object"}, "required": false, "description": "The style of the <label> that wraps the radio input\n and the option's label", "defaultValue": {"value": "{}", "computed": false}}, "labelClassName": {"type": {"name": "string"}, "required": false, "description": "The class of the <label> that wraps the radio input\n and the option's label", "defaultValue": {"value": "''", "computed": false}}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/RangeSlider.react.js": {"description": "A double slider with two handles.\nUsed for specifying a range of numerical values.", "displayName": "RangeSlider", "methods": [], "props": {"min": {"type": {"name": "number"}, "required": false, "description": "Minimum allowed value of the slider"}, "max": {"type": {"name": "number"}, "required": false, "description": "Maximum allowed value of the slider"}, "step": {"type": {"name": "number"}, "required": false, "description": "Value by which increments or decrements are made"}, "marks": {"type": {"name": "objectOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "exact", "value": {"label": {"name": "string", "required": false}, "style": {"name": "object", "required": false}}}]}}, "required": false, "description": "Marks on the slider.\nThe key determines the position (a number),\nand the value determines what will show.\nIf you want to set the style of a specific mark point,\nthe value should be an object which\ncontains style and label properties."}, "value": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "The value of the input"}, "drag_value": {"type": {"name": "arrayOf", "value": {"name": "number"}}, "required": false, "description": "The value of the input during a drag"}, "allowCross": {"type": {"name": "bool"}, "required": false, "description": "allowCross could be set as true to allow those handles to cross."}, "pushable": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "number"}]}, "required": false, "description": "pushable could be set as true to allow pushing of\nsurrounding handles when moving an handle.\nWhen set to a number, the number will be the\nminimum ensured distance between handles."}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "If true, the handles can't be moved."}, "count": {"type": {"name": "number"}, "required": false, "description": "Determine how many ranges to render, and multiple handles\nwill be rendered (number + 1)."}, "dots": {"type": {"name": "bool"}, "required": false, "description": "When the step value is greater than 1,\nyou can set the dots to true if you want to\nrender the slider with dots."}, "included": {"type": {"name": "bool"}, "required": false, "description": "If the value is true, it means a continuous\nvalue is included. Otherwise, it is an independent value."}, "tooltip": {"type": {"name": "exact", "value": {"always_visible": {"name": "bool", "description": "Determines whether tooltips should always be visible\n(as opposed to the default, visible on hover)", "required": false}, "placement": {"name": "enum", "value": [{"value": "'left'", "computed": false}, {"value": "'right'", "computed": false}, {"value": "'top'", "computed": false}, {"value": "'bottom'", "computed": false}, {"value": "'topLeft'", "computed": false}, {"value": "'topRight'", "computed": false}, {"value": "'bottomLeft'", "computed": false}, {"value": "'bottomRight'", "computed": false}], "description": "Determines the placement of tooltips\nSee https://github.com/react-component/tooltip#api\ntop/bottom{*} sets the _origin_ of the tooltip, so e.g. `topLeft`\nwill in reality appear to be on the top right of the handle", "required": false}}}, "required": false, "description": "Configuration for tooltips describing the current slider values"}, "updatemode": {"type": {"name": "enum", "value": [{"value": "'mouseup'", "computed": false}, {"value": "'drag'", "computed": false}]}, "required": false, "description": "Determines when the component should update its `value`\nproperty. If `mouseup` (the default) then the slider\nwill only trigger its value when the user has finished\ndragging the slider. If `drag`, then the slider will\nupdate its value continuously as it is being dragged.\nNote that for the latter case, the `drag_value`\nproperty could be used instead.", "defaultValue": {"value": "'mouseup'", "computed": false}}, "vertical": {"type": {"name": "bool"}, "required": false, "description": "If true, the slider will be vertical"}, "verticalHeight": {"type": {"name": "number"}, "required": false, "description": "The height, in px, of the slider if it is vertical.", "defaultValue": {"value": "400", "computed": false}}, "className": {"type": {"name": "string"}, "required": false, "description": "Additional CSS class for the root DOM node"}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value or drag_value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Slider.react.js": {"description": "A slider component with a single handle.", "displayName": "Slide<PERSON>", "methods": [], "props": {"min": {"type": {"name": "number"}, "required": false, "description": "Minimum allowed value of the slider"}, "max": {"type": {"name": "number"}, "required": false, "description": "Maximum allowed value of the slider"}, "step": {"type": {"name": "number"}, "required": false, "description": "Value by which increments or decrements are made"}, "marks": {"type": {"name": "objectOf", "value": {"name": "union", "value": [{"name": "string"}, {"name": "exact", "value": {"label": {"name": "string", "required": false}, "style": {"name": "object", "required": false}}}]}}, "required": false, "description": "Marks on the slider.\nThe key determines the position (a number),\nand the value determines what will show.\nIf you want to set the style of a specific mark point,\nthe value should be an object which\ncontains style and label properties."}, "value": {"type": {"name": "number"}, "required": false, "description": "The value of the input"}, "drag_value": {"type": {"name": "number"}, "required": false, "description": "The value of the input during a drag"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "If true, the handles can't be moved."}, "dots": {"type": {"name": "bool"}, "required": false, "description": "When the step value is greater than 1,\nyou can set the dots to true if you want to\nrender the slider with dots."}, "included": {"type": {"name": "bool"}, "required": false, "description": "If the value is true, it means a continuous\nvalue is included. Otherwise, it is an independent value."}, "tooltip": {"type": {"name": "exact", "value": {"always_visible": {"name": "bool", "description": "Determines whether tooltips should always be visible\n(as opposed to the default, visible on hover)", "required": false}, "placement": {"name": "enum", "value": [{"value": "'left'", "computed": false}, {"value": "'right'", "computed": false}, {"value": "'top'", "computed": false}, {"value": "'bottom'", "computed": false}, {"value": "'topLeft'", "computed": false}, {"value": "'topRight'", "computed": false}, {"value": "'bottomLeft'", "computed": false}, {"value": "'bottomRight'", "computed": false}], "description": "Determines the placement of tooltips\nSee https://github.com/react-component/tooltip#api\ntop/bottom{*} sets the _origin_ of the tooltip, so e.g. `topLeft`\nwill in reality appear to be on the top right of the handle", "required": false}}}, "required": false, "description": "Configuration for tooltips describing the current slider value"}, "updatemode": {"type": {"name": "enum", "value": [{"value": "'mouseup'", "computed": false}, {"value": "'drag'", "computed": false}]}, "required": false, "description": "Determines when the component should update its `value`\nproperty. If `mouseup` (the default) then the slider\nwill only trigger its value when the user has finished\ndragging the slider. If `drag`, then the slider will\nupdate its value continuously as it is being dragged.\nIf you want different actions during and after drag,\nleave `updatemode` as `mouseup` and use `drag_value`\nfor the continuously updating value.", "defaultValue": {"value": "'mouseup'", "computed": false}}, "vertical": {"type": {"name": "bool"}, "required": false, "description": "If true, the slider will be vertical"}, "verticalHeight": {"type": {"name": "number"}, "required": false, "description": "The height, in px, of the slider if it is vertical.", "defaultValue": {"value": "400", "computed": false}}, "className": {"type": {"name": "string"}, "required": false, "description": "Additional CSS class for the root DOM node"}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value or drag_value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Store.react.js": {"description": "Easily keep data on the client side with this component.\nThe data is not inserted in the DOM.\nData can be in memory, localStorage or sessionStorage.\nThe data will be kept with the id as key.", "displayName": "Store", "methods": [{"name": "onStorageChange", "docblock": null, "modifiers": [], "params": [{"name": "e", "type": null}], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": true, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "storage_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "The type of the web storage.\n\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'memory'", "computed": false}}, "data": {"type": {"name": "union", "value": [{"name": "object"}, {"name": "array"}, {"name": "number"}, {"name": "string"}, {"name": "bool"}]}, "required": false, "description": "The stored data for the id."}, "clear_data": {"type": {"name": "bool"}, "required": false, "description": "Set to true to remove the data contained in `data_key`.", "defaultValue": {"value": "false", "computed": false}}, "modified_timestamp": {"type": {"name": "number"}, "required": false, "description": "The last time the storage was modified.", "defaultValue": {"value": "-1", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}}}, "src/components/Tab.react.js": {"description": "Part of dcc.Tabs - this is the child Tab component used to render a tabbed page.\nIts children will be set as the content of that tab, which if clicked will become visible.", "displayName": "Tab", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "label": {"type": {"name": "string"}, "required": false, "description": "The tab's label"}, "children": {"type": {"name": "node"}, "required": false, "description": "The content of the tab - will only be displayed if this tab is selected"}, "value": {"type": {"name": "string"}, "required": false, "description": "Value for determining which Tab is currently selected"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Determines if tab is disabled or not - defaults to false", "defaultValue": {"value": "false", "computed": false}}, "disabled_style": {"type": {"name": "object"}, "required": false, "description": "Overrides the default (inline) styles when disabled", "defaultValue": {"value": "{\n    color: '#d6d6d6',\n}", "computed": false}}, "disabled_className": {"type": {"name": "string"}, "required": false, "description": "Appends a class to the Tab component when it is disabled."}, "className": {"type": {"name": "string"}, "required": false, "description": "Appends a class to the Tab component."}, "selected_className": {"type": {"name": "string"}, "required": false, "description": "Appends a class to the Tab component when it is selected."}, "style": {"type": {"name": "object"}, "required": false, "description": "Overrides the default (inline) styles for the Tab component."}, "selected_style": {"type": {"name": "object"}, "required": false, "description": "Overrides the default (inline) styles for the Tab component when it is selected."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/Tabs.react.js": {"description": "A Dash component that lets you render pages with tabs - the Tabs component's children\ncan be dcc.Tab components, which can hold a label that will be displayed as a tab, and can in turn hold\nchildren components that will be that tab's content.", "displayName": "Tabs", "methods": [{"name": "valueOrDefault", "docblock": null, "modifiers": [], "params": [], "returns": null}, {"name": "parseChildrenTo<PERSON>y", "docblock": null, "modifiers": [], "params": [], "returns": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "docblock": null, "modifiers": [], "params": [{"name": "value", "type": null}], "returns": null}], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "value": {"type": {"name": "string"}, "required": false, "description": "The value of the currently selected Tab"}, "className": {"type": {"name": "string"}, "required": false, "description": "Appends a class to the Tabs container holding the individual Tab components."}, "content_className": {"type": {"name": "string"}, "required": false, "description": "Appends a class to the Tab content container holding the children of the Tab that is selected."}, "parent_className": {"type": {"name": "string"}, "required": false, "description": "Appends a class to the top-level parent container holding both the Tabs container and the content container."}, "style": {"type": {"name": "object"}, "required": false, "description": "Appends (inline) styles to the Tabs container holding the individual Tab components."}, "parent_style": {"type": {"name": "object"}, "required": false, "description": "Appends (inline) styles to the top-level parent container holding both the Tabs container and the content container."}, "content_style": {"type": {"name": "object"}, "required": false, "description": "Appends (inline) styles to the tab content container holding the children of the Tab that is selected."}, "vertical": {"type": {"name": "bool"}, "required": false, "description": "Renders the tabs vertically (on the side)", "defaultValue": {"value": "false", "computed": false}}, "mobile_breakpoint": {"type": {"name": "number"}, "required": false, "description": "Breakpoint at which tabs are rendered full width (can be 0 if you don't want full width tabs on mobile)", "defaultValue": {"value": "800", "computed": false}}, "children": {"type": {"name": "union", "value": [{"name": "arrayOf", "value": {"name": "node"}}, {"name": "node"}]}, "required": false, "description": "Array that holds Tab components"}, "colors": {"type": {"name": "exact", "value": {"border": {"name": "string", "required": false}, "primary": {"name": "string", "required": false}, "background": {"name": "string", "required": false}}}, "required": false, "description": "Holds the colors used by the Tabs and Tab components. If you set these, you should specify colors for all properties, so:\ncolors: {\n   border: '#d6d6d6',\n   primary: '#1975FA',\n   background: '#f9f9f9'\n }", "defaultValue": {"value": "{\n    border: '#d6d6d6',\n    primary: '#1975FA',\n    background: '#f9f9f9',\n}", "computed": false}}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Textarea.react.js": {"description": "A basic HTML textarea for entering multiline text.", "displayName": "Textarea", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "value": {"type": {"name": "string"}, "required": false, "description": "The value of the textarea"}, "autoFocus": {"type": {"name": "string"}, "required": false, "description": "The element should be automatically focused after the page loaded."}, "cols": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the number of columns in a textarea."}, "disabled": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "bool"}]}, "required": false, "description": "Indicates whether the user can interact with the element."}, "form": {"type": {"name": "string"}, "required": false, "description": "Indicates the form that is the owner of the element."}, "maxLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the maximum number of characters allowed in the element."}, "minLength": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the minimum number of characters allowed in the element."}, "name": {"type": {"name": "string"}, "required": false, "description": "Name of the element. For example used by the server to identify the fields in form submits."}, "placeholder": {"type": {"name": "string"}, "required": false, "description": "Provides a hint to the user of what can be entered in the field."}, "readOnly": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "enum", "value": [{"value": "'readOnly'", "computed": false}, {"value": "'readonly'", "computed": false}, {"value": "'READONLY'", "computed": false}]}]}, "required": false, "description": "Indicates whether the element can be edited.\nreadOnly is an HTML boolean attribute - it is enabled by a boolean or\n'readOnly'. Alternative capitalizations `readonly` & `READONLY`\nare also acccepted."}, "required": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'required'", "computed": false}, {"value": "'REQUIRED'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "Indicates whether this element is required to fill out or not.\nrequired is an HTML boolean attribute - it is enabled by a boolean or\n'required'. Alternative capitalizations `REQUIRED`\nare also acccepted."}, "rows": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Defines the number of rows in a text area."}, "wrap": {"type": {"name": "string"}, "required": false, "description": "Indicates whether the text should be wrapped."}, "accessKey": {"type": {"name": "string"}, "required": false, "description": "Defines a keyboard shortcut to activate or add focus to the element."}, "className": {"type": {"name": "string"}, "required": false, "description": "Often used with CSS to style elements with common properties."}, "contentEditable": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "bool"}]}, "required": false, "description": "Indicates whether the element's content is editable."}, "contextMenu": {"type": {"name": "string"}, "required": false, "description": "Defines the ID of a <menu> element which will serve as the element's context menu."}, "dir": {"type": {"name": "string"}, "required": false, "description": "Defines the text direction. Allowed values are ltr (Left-To-Right) or rtl (Right-To-Left)"}, "draggable": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'true'", "computed": false}, {"value": "'false'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "Defines whether the element can be dragged."}, "hidden": {"type": {"name": "string"}, "required": false, "description": "Prevents rendering of given element, while keeping child elements, e.g. script elements, active."}, "lang": {"type": {"name": "string"}, "required": false, "description": "Defines the language used in the element."}, "spellCheck": {"type": {"name": "union", "value": [{"name": "enum", "value": [{"value": "'true'", "computed": false}, {"value": "'false'", "computed": false}]}, {"name": "bool"}]}, "required": false, "description": "Indicates whether spell checking is allowed for the element."}, "style": {"type": {"name": "object"}, "required": false, "description": "Defines CSS styles which will override styles previously set."}, "tabIndex": {"type": {"name": "union", "value": [{"name": "string"}, {"name": "number"}]}, "required": false, "description": "Overrides the browser's default tab order and follows the one specified instead."}, "title": {"type": {"name": "string"}, "required": false, "description": "Text to be displayed in a tooltip when hovering over the element."}, "n_blur": {"type": {"name": "number"}, "required": false, "description": "Number of times the textarea lost focus.", "defaultValue": {"value": "0", "computed": false}}, "n_blur_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the textarea lost focus.", "defaultValue": {"value": "-1", "computed": false}}, "n_clicks": {"type": {"name": "number"}, "required": false, "description": "Number of times the textarea has been clicked.", "defaultValue": {"value": "0", "computed": false}}, "n_clicks_timestamp": {"type": {"name": "number"}, "required": false, "description": "Last time the textarea was clicked.", "defaultValue": {"value": "-1", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}, "persistence": {"type": {"name": "union", "value": [{"name": "bool"}, {"name": "string"}, {"name": "number"}]}, "required": false, "description": "Used to allow user interactions in this component to be persisted when\nthe component - or the page - is refreshed. If `persisted` is truthy and\nhasn't changed from its previous value, a `value` that the user has\nchanged while using the app will keep that change, as long as\nthe new `value` also matches what was given originally.\nUsed in conjunction with `persistence_type`."}, "persisted_props": {"type": {"name": "arrayOf", "value": {"name": "enum", "value": [{"value": "'value'", "computed": false}]}}, "required": false, "description": "Properties whose user interactions will persist after refreshing the\ncomponent or the page. Since only `value` is allowed this prop can\nnormally be ignored.", "defaultValue": {"value": "['value']", "computed": false}}, "persistence_type": {"type": {"name": "enum", "value": [{"value": "'local'", "computed": false}, {"value": "'session'", "computed": false}, {"value": "'memory'", "computed": false}]}, "required": false, "description": "Where persisted user changes will be stored:\nmemory: only kept in memory, reset on page refresh.\nlocal: window.localStorage, data is kept after the browser quit.\nsession: window.sessionStorage, data is cleared once the browser quit.", "defaultValue": {"value": "'local'", "computed": false}}}}, "src/components/Tooltip.react.js": {"description": "A tooltip with an absolute position.", "displayName": "<PERSON><PERSON><PERSON>", "methods": [], "props": {"children": {"type": {"name": "node"}, "required": false, "description": "The contents of the tooltip"}, "id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "className": {"type": {"name": "string"}, "required": false, "description": "The class of the tooltip", "defaultValue": {"value": "''", "computed": false}}, "style": {"type": {"name": "object"}, "required": false, "description": "The style of the tooltip"}, "bbox": {"type": {"name": "exact", "value": {"x0": {"name": "number", "required": false}, "y0": {"name": "number", "required": false}, "x1": {"name": "number", "required": false}, "y1": {"name": "number", "required": false}}}, "required": false, "description": "The bounding box coordinates of the item to label, in px relative to\nthe positioning parent of the Tooltip component."}, "show": {"type": {"name": "bool"}, "required": false, "description": "Whether to show the tooltip", "defaultValue": {"value": "true", "computed": false}}, "direction": {"type": {"name": "enum", "value": [{"value": "'top'", "computed": false}, {"value": "'right'", "computed": false}, {"value": "'bottom'", "computed": false}, {"value": "'left'", "computed": false}]}, "required": false, "description": "The side of the `bbox` on which the tooltip should open.", "defaultValue": {"value": "'right'", "computed": false}}, "border_color": {"type": {"name": "string"}, "required": false, "description": "Color of the tooltip border, as a CSS color string.", "defaultValue": {"value": "'#d6d6d6'", "computed": false}}, "background_color": {"type": {"name": "string"}, "required": false, "description": "Color of the tooltip background, as a CSS color string.", "defaultValue": {"value": "'white'", "computed": false}}, "loading_text": {"type": {"name": "string"}, "required": false, "description": "The text displayed in the tooltip while loading", "defaultValue": {"value": "'Loading...'", "computed": false}}, "zindex": {"type": {"name": "number"}, "required": false, "description": "The `z-index` CSS property to assign to the tooltip. Components with\nhigher values will be displayed on top of components with lower values.", "defaultValue": {"value": "1", "computed": false}}, "targetable": {"type": {"name": "bool"}, "required": false, "description": "Whether the tooltip itself can be targeted by pointer events.\nFor tooltips triggered by hover events, typically this should be left\n`false` to avoid the tooltip interfering with those same events.", "defaultValue": {"value": "false", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-assigned callback that gets fired when the value changes."}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}, "src/components/Upload.react.js": {"description": "Upload components allow your app to accept user-uploaded files via drag'n'drop", "displayName": "Upload", "methods": [], "props": {"id": {"type": {"name": "string"}, "required": false, "description": "The ID of this component, used to identify dash components\nin callbacks. The ID needs to be unique across all of the\ncomponents in an app."}, "contents": {"type": {"name": "union", "value": [{"name": "string", "description": "If `multiple` is `false`, then the contents will be a string"}, {"name": "arrayOf", "value": {"name": "string"}, "description": "If `multiple` is `true`, then the contents will be a list of strings"}]}, "required": false, "description": "The contents of the uploaded file as a binary string"}, "filename": {"type": {"name": "union", "value": [{"name": "string", "description": "If `multiple` is `false`, then the contents will be a string"}, {"name": "arrayOf", "value": {"name": "string"}, "description": "If `multiple` is `true`, then the contents will be a list of strings"}]}, "required": false, "description": "The name of the file(s) that was(were) uploaded.\nNote that this does not include the path of the file\n(for security reasons)."}, "last_modified": {"type": {"name": "union", "value": [{"name": "number", "description": "If `multiple` is `false`, then the contents will be a number"}, {"name": "arrayOf", "value": {"name": "number"}, "description": "If `multiple` is `true`, then the contents will be a list of numbers"}]}, "required": false, "description": "The last modified date of the file that was uploaded in unix time\n(seconds since 1970)."}, "children": {"type": {"name": "union", "value": [{"name": "node"}, {"name": "string"}]}, "required": false, "description": "Contents of the upload component"}, "accept": {"type": {"name": "string"}, "required": false, "description": "Allow specific types of files.\nSee https://github.com/okonet/attr-accept for more information.\nKeep in mind that mime type determination is not reliable across\nplatforms. CSV files, for example, are reported as text/plain\nunder macOS but as application/vnd.ms-excel under Windows.\nIn some cases there might not be a mime type set at all.\nSee: https://github.com/react-dropzone/react-dropzone/issues/276"}, "disabled": {"type": {"name": "bool"}, "required": false, "description": "Enable/disable the upload component entirely", "defaultValue": {"value": "false", "computed": false}}, "disable_click": {"type": {"name": "bool"}, "required": false, "description": "Disallow clicking on the component to open the file dialog", "defaultValue": {"value": "false", "computed": false}}, "max_size": {"type": {"name": "number"}, "required": false, "description": "Maximum file size in bytes. If `-1`, then infinite", "defaultValue": {"value": "-1", "computed": false}}, "min_size": {"type": {"name": "number"}, "required": false, "description": "Minimum file size in bytes", "defaultValue": {"value": "0", "computed": false}}, "multiple": {"type": {"name": "bool"}, "required": false, "description": "Allow dropping multiple files", "defaultValue": {"value": "false", "computed": false}}, "className": {"type": {"name": "string"}, "required": false, "description": "HTML class name of the component"}, "className_active": {"type": {"name": "string"}, "required": false, "description": "HTML class name of the component while active"}, "className_reject": {"type": {"name": "string"}, "required": false, "description": "HTML class name of the component if rejected"}, "className_disabled": {"type": {"name": "string"}, "required": false, "description": "HTML class name of the component if disabled"}, "style": {"type": {"name": "object"}, "required": false, "description": "CSS styles to apply", "defaultValue": {"value": "{}", "computed": false}}, "style_active": {"type": {"name": "object"}, "required": false, "description": "CSS styles to apply while active", "defaultValue": {"value": "{\n    borderStyle: 'solid',\n    borderColor: '#6c6',\n    backgroundColor: '#eee',\n}", "computed": false}}, "style_reject": {"type": {"name": "object"}, "required": false, "description": "CSS styles if rejected", "defaultValue": {"value": "{\n    borderStyle: 'solid',\n    borderColor: '#c66',\n    backgroundColor: '#eee',\n}", "computed": false}}, "style_disabled": {"type": {"name": "object"}, "required": false, "description": "CSS styles if disabled", "defaultValue": {"value": "{\n    opacity: 0.5,\n}", "computed": false}}, "setProps": {"type": {"name": "func"}, "required": false, "description": "Dash-supplied function for updating props"}, "loading_state": {"type": {"name": "shape", "value": {"is_loading": {"name": "bool", "description": "Determines if the component is loading or not", "required": false}, "prop_name": {"name": "string", "description": "Holds which property is loading", "required": false}, "component_name": {"name": "string", "description": "Holds the name of the component that is loading", "required": false}}}, "required": false, "description": "Object that holds the loading state object coming from dash-renderer"}}}}