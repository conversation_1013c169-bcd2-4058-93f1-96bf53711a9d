{"version": 3, "file": "dash_core_components-shared.js", "mappings": ";mHAAA,OAOC,WACA,aAEA,IAAIA,EAAS,CAAC,EAAEC,eAGhB,SAASC,IAGR,IAFA,IAAIC,EAAU,GAELC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAC1C,IAAIG,EAAMF,UAAUD,GACpB,GAAKG,EAAL,CAEA,IAAIC,SAAiBD,EAErB,GAAgB,WAAZC,GAAoC,WAAZA,EAC3BL,EAAQM,KAAKF,QACP,GAAIG,MAAMC,QAAQJ,IACxB,GAAIA,EAAID,OAAQ,CACf,IAAIM,EAAQV,EAAWW,MAAM,KAAMN,GAC/BK,GACHT,EAAQM,KAAKG,EAEf,OACM,GAAgB,WAAZJ,EAAsB,CAChC,GAAID,EAAIO,WAAaC,OAAOC,UAAUF,WAAaP,EAAIO,SAASA,WAAWG,SAAS,iBAAkB,CACrGd,EAAQM,KAAKF,EAAIO,YACjB,QACD,CAEA,IAAK,IAAII,KAAOX,EACXP,EAAOmB,KAAKZ,EAAKW,IAAQX,EAAIW,IAChCf,EAAQM,KAAKS,EAGhB,CAxBkB,CAyBnB,CAEA,OAAOf,EAAQiB,KAAK,IACrB,CAEqCC,EAAOC,SAC3CpB,EAAWqB,QAAUrB,EACrBmB,EAAOC,QAAUpB,QAKhB,KAFwB,EAAF,WACtB,OAAOA,CACP,UAFoB,OAEpB,YAIH,CApDA,0BCPA,IAGIsB,EAHO,EAAQ,OAGDA,OAElBH,EAAOC,QAAUE,yBCLjB,IAAIA,EAAS,EAAQ,OACjBC,EAAY,EAAQ,OACpBC,EAAiB,EAAQ,MAOzBC,EAAiBH,EAASA,EAAOI,iBAAcC,EAkBnDR,EAAOC,QATP,SAAoBQ,GAClB,OAAa,MAATA,OACeD,IAAVC,EAdQ,qBADL,gBAiBJH,GAAkBA,KAAkBZ,OAAOe,GAC/CL,EAAUK,GACVJ,EAAeI,EACrB,yBCxBA,IAAIC,EAA8B,iBAAV,EAAAC,GAAsB,EAAAA,GAAU,EAAAA,EAAOjB,SAAWA,QAAU,EAAAiB,EAEpFX,EAAOC,QAAUS,yBCHjB,IAAIP,EAAS,EAAQ,OAGjBS,EAAclB,OAAOC,UAGrBf,EAAiBgC,EAAYhC,eAO7BiC,EAAuBD,EAAYnB,SAGnCa,EAAiBH,EAASA,EAAOI,iBAAcC,EA6BnDR,EAAOC,QApBP,SAAmBQ,GACjB,IAAIK,EAAQlC,EAAekB,KAAKW,EAAOH,GACnCS,EAAMN,EAAMH,GAEhB,IACEG,EAAMH,QAAkBE,EACxB,IAAIQ,GAAW,CACJ,CAAX,MAAOC,GAAI,CAEb,IAAIC,EAASL,EAAqBf,KAAKW,GAQvC,OAPIO,IACEF,EACFL,EAAMH,GAAkBS,SAEjBN,EAAMH,IAGVY,CACT,oBC1CA,IAOIL,EAPcnB,OAAOC,UAOcF,SAavCO,EAAOC,QAJP,SAAwBQ,GACtB,OAAOI,EAAqBf,KAAKW,EACnC,yBCnBA,IAAIC,EAAa,EAAQ,OAGrBS,EAA0B,iBAARC,MAAoBA,MAAQA,KAAK1B,SAAWA,QAAU0B,KAGxEC,EAAOX,GAAcS,GAAYG,SAAS,cAATA,GAErCtB,EAAOC,QAAUoB,qBCsBjBrB,EAAOC,QALP,SAAkBQ,GAChB,IAAIc,SAAcd,EAClB,OAAgB,MAATA,IAA0B,UAARc,GAA4B,YAARA,EAC/C,qBCAAvB,EAAOC,QAJP,SAAsBQ,GACpB,OAAgB,MAATA,GAAiC,iBAATA,CACjC,oCCjBa,IAAIe,EAAE,mBAAoBrB,QAAQA,OAAOsB,IAAIC,EAAEF,EAAErB,OAAOsB,IAAI,iBAAiB,MAAME,EAAEH,EAAErB,OAAOsB,IAAI,gBAAgB,MAAMR,EAAEO,EAAErB,OAAOsB,IAAI,kBAAkB,MAAMG,EAAEJ,EAAErB,OAAOsB,IAAI,qBAAqB,MAAMd,EAAEa,EAAErB,OAAOsB,IAAI,kBAAkB,MAAMI,EAAEL,EAAErB,OAAOsB,IAAI,kBAAkB,MAAMK,EAAEN,EAAErB,OAAOsB,IAAI,iBAAiB,MAAMM,EAAEP,EAAErB,OAAOsB,IAAI,oBAAoB,MAAMO,EAAER,EAAErB,OAAOsB,IAAI,yBAAyB,MAAMQ,EAAET,EAAErB,OAAOsB,IAAI,qBAAqB,MAAMS,EAAEV,EAAErB,OAAOsB,IAAI,kBAAkB,MAAMU,EAAEX,EACpfrB,OAAOsB,IAAI,uBAAuB,MAAMW,EAAEZ,EAAErB,OAAOsB,IAAI,cAAc,MAAMY,EAAEb,EAAErB,OAAOsB,IAAI,cAAc,MAAMa,EAAEd,EAAErB,OAAOsB,IAAI,eAAe,MAAMc,EAAEf,EAAErB,OAAOsB,IAAI,qBAAqB,MAAMe,EAAEhB,EAAErB,OAAOsB,IAAI,mBAAmB,MAAMgB,EAAEjB,EAAErB,OAAOsB,IAAI,eAAe,MAClQ,SAASiB,EAAEC,GAAG,GAAG,iBAAkBA,GAAG,OAAOA,EAAE,CAAC,IAAIC,EAAED,EAAEE,SAAS,OAAOD,GAAG,KAAKlB,EAAE,OAAOiB,EAAEA,EAAEpB,MAAQ,KAAKQ,EAAE,KAAKC,EAAE,KAAKf,EAAE,KAAKN,EAAE,KAAKiB,EAAE,KAAKM,EAAE,OAAOS,EAAE,QAAQ,OAAOA,EAAEA,GAAGA,EAAEE,UAAY,KAAKf,EAAE,KAAKG,EAAE,KAAKI,EAAE,KAAKD,EAAE,KAAKP,EAAE,OAAOc,EAAE,QAAQ,OAAOC,GAAG,KAAKjB,EAAE,OAAOiB,EAAE,CAAC,CAAC,SAASE,EAAEH,GAAG,OAAOD,EAAEC,KAAKX,CAAC,CAAC/B,EAAQ8C,UAAUhB,EAAE9B,EAAQ+C,eAAehB,EAAE/B,EAAQgD,gBAAgBnB,EAAE7B,EAAQiD,gBAAgBrB,EAAE5B,EAAQkD,QAAQzB,EAAEzB,EAAQmD,WAAWnB,EAAEhC,EAAQoD,SAASpC,EAAEhB,EAAQqD,KAAKjB,EAAEpC,EAAQsD,KAAKnB,EAAEnC,EAAQuD,OAAO7B,EAChf1B,EAAQwD,SAAS9C,EAAEV,EAAQyD,WAAW9B,EAAE3B,EAAQ0D,SAASzB,EAAEjC,EAAQ2D,YAAY,SAASjB,GAAG,OAAOG,EAAEH,IAAID,EAAEC,KAAKZ,CAAC,EAAE9B,EAAQ4D,iBAAiBf,EAAE7C,EAAQ6D,kBAAkB,SAASnB,GAAG,OAAOD,EAAEC,KAAKb,CAAC,EAAE7B,EAAQ8D,kBAAkB,SAASpB,GAAG,OAAOD,EAAEC,KAAKd,CAAC,EAAE5B,EAAQ+D,UAAU,SAASrB,GAAG,MAAM,iBAAkBA,GAAG,OAAOA,GAAGA,EAAEE,WAAWnB,CAAC,EAAEzB,EAAQgE,aAAa,SAAStB,GAAG,OAAOD,EAAEC,KAAKV,CAAC,EAAEhC,EAAQiE,WAAW,SAASvB,GAAG,OAAOD,EAAEC,KAAK1B,CAAC,EAAEhB,EAAQkE,OAAO,SAASxB,GAAG,OAAOD,EAAEC,KAAKN,CAAC,EAC1dpC,EAAQmE,OAAO,SAASzB,GAAG,OAAOD,EAAEC,KAAKP,CAAC,EAAEnC,EAAQoE,SAAS,SAAS1B,GAAG,OAAOD,EAAEC,KAAKhB,CAAC,EAAE1B,EAAQqE,WAAW,SAAS3B,GAAG,OAAOD,EAAEC,KAAKhC,CAAC,EAAEV,EAAQsE,aAAa,SAAS5B,GAAG,OAAOD,EAAEC,KAAKf,CAAC,EAAE3B,EAAQuE,WAAW,SAAS7B,GAAG,OAAOD,EAAEC,KAAKT,CAAC,EAC1OjC,EAAQwE,mBAAmB,SAAS9B,GAAG,MAAM,iBAAkBA,GAAG,mBAAoBA,GAAGA,IAAI1B,GAAG0B,IAAIX,GAAGW,IAAIhC,GAAGgC,IAAIf,GAAGe,IAAIT,GAAGS,IAAIR,GAAG,iBAAkBQ,GAAG,OAAOA,IAAIA,EAAEE,WAAWR,GAAGM,EAAEE,WAAWT,GAAGO,EAAEE,WAAWhB,GAAGc,EAAEE,WAAWf,GAAGa,EAAEE,WAAWZ,GAAGU,EAAEE,WAAWN,GAAGI,EAAEE,WAAWL,GAAGG,EAAEE,WAAWJ,GAAGE,EAAEE,WAAWP,EAAE,EAAErC,EAAQyE,OAAOhC,sCCXjU1C,EAAOC,QAAU,EAAjB,kDCIF,IAAI0E,EAAU,WACV,GAAmB,oBAARC,IACP,OAAOA,IASX,SAASC,EAASC,EAAKjF,GACnB,IAAIqB,GAAU,EAQd,OAPA4D,EAAIC,MAAK,SAAUC,EAAOC,GACtB,OAAID,EAAM,KAAOnF,IACbqB,EAAS+D,GACF,EAGf,IACO/D,CACX,CACA,OAAsB,WAClB,SAASgE,IACLC,KAAKC,YAAc,EACvB,CAsEA,OArEA1F,OAAO2F,eAAeH,EAAQvF,UAAW,OAAQ,CAI7C2F,IAAK,WACD,OAAOH,KAAKC,YAAYnG,MAC5B,EACAsG,YAAY,EACZC,cAAc,IAMlBN,EAAQvF,UAAU2F,IAAM,SAAUzF,GAC9B,IAAIoF,EAAQJ,EAASM,KAAKC,YAAavF,GACnCmF,EAAQG,KAAKC,YAAYH,GAC7B,OAAOD,GAASA,EAAM,EAC1B,EAMAE,EAAQvF,UAAU8F,IAAM,SAAU5F,EAAKY,GACnC,IAAIwE,EAAQJ,EAASM,KAAKC,YAAavF,IAClCoF,EACDE,KAAKC,YAAYH,GAAO,GAAKxE,EAG7B0E,KAAKC,YAAYhG,KAAK,CAACS,EAAKY,GAEpC,EAKAyE,EAAQvF,UAAU+F,OAAS,SAAU7F,GACjC,IAAI8F,EAAUR,KAAKC,YACfH,EAAQJ,EAASc,EAAS9F,IACzBoF,GACDU,EAAQC,OAAOX,EAAO,EAE9B,EAKAC,EAAQvF,UAAUkG,IAAM,SAAUhG,GAC9B,SAAUgF,EAASM,KAAKC,YAAavF,EACzC,EAIAqF,EAAQvF,UAAUmG,MAAQ,WACtBX,KAAKC,YAAYQ,OAAO,EAC5B,EAMAV,EAAQvF,UAAUoG,QAAU,SAAUC,EAAUC,QAChC,IAARA,IAAkBA,EAAM,MAC5B,IAAK,IAAIC,EAAK,EAAGC,EAAKhB,KAAKC,YAAac,EAAKC,EAAGlH,OAAQiH,IAAM,CAC1D,IAAIlB,EAAQmB,EAAGD,GACfF,EAASlG,KAAKmG,EAAKjB,EAAM,GAAIA,EAAM,GACvC,CACJ,EACOE,CACX,CA1EqB,EA2ExB,CAjGa,GAsGVkB,EAA8B,oBAAXC,QAA8C,oBAAbC,UAA4BD,OAAOC,WAAaA,SAGpGC,OACsB,IAAX,EAAA5F,GAA0B,EAAAA,EAAO6F,OAASA,KAC1C,EAAA7F,EAES,oBAATS,MAAwBA,KAAKoF,OAASA,KACtCpF,KAEW,oBAAXiF,QAA0BA,OAAOG,OAASA,KAC1CH,OAGJ/E,SAAS,cAATA,GASPmF,EACqC,mBAA1BC,sBAIAA,sBAAsBC,KAAKJ,GAE/B,SAAUP,GAAY,OAAOY,YAAW,WAAc,OAAOZ,EAASa,KAAKC,MAAQ,GAAG,IAAO,GAAK,EAwEzGC,EAAiB,CAAC,MAAO,QAAS,SAAU,OAAQ,QAAS,SAAU,OAAQ,UAE/EC,EAAwD,oBAArBC,iBAInCC,EAA0C,WAM1C,SAASA,IAML/B,KAAKgC,YAAa,EAMlBhC,KAAKiC,sBAAuB,EAM5BjC,KAAKkC,mBAAqB,KAM1BlC,KAAKmC,WAAa,GAClBnC,KAAKoC,iBAAmBpC,KAAKoC,iBAAiBZ,KAAKxB,MACnDA,KAAKqC,QAjGb,SAAmBxB,EAAUyB,GACzB,IAAIC,GAAc,EAAOC,GAAe,EAAOC,EAAe,EAO9D,SAASC,IACDH,IACAA,GAAc,EACd1B,KAEA2B,GACAG,GAER,CAQA,SAASC,IACLtB,EAAwBoB,EAC5B,CAMA,SAASC,IACL,IAAIE,EAAYnB,KAAKC,MACrB,GAAIY,EAAa,CAEb,GAAIM,EAAYJ,EA7CN,EA8CN,OAMJD,GAAe,CACnB,MAEID,GAAc,EACdC,GAAe,EACff,WAAWmB,EAQH,IANZH,EAAeI,CACnB,CACA,OAAOF,CACX,CA4CuBG,CAAS9C,KAAKqC,QAAQb,KAAKxB,MAC9C,CA+JA,OAxJA+B,EAAyBvH,UAAUuI,YAAc,SAAUC,IACjDhD,KAAKmC,WAAWc,QAAQD,IAC1BhD,KAAKmC,WAAWlI,KAAK+I,GAGpBhD,KAAKgC,YACNhC,KAAKkD,UAEb,EAOAnB,EAAyBvH,UAAU2I,eAAiB,SAAUH,GAC1D,IAAII,EAAYpD,KAAKmC,WACjBrC,EAAQsD,EAAUH,QAAQD,IAEzBlD,GACDsD,EAAU3C,OAAOX,EAAO,IAGvBsD,EAAUtJ,QAAUkG,KAAKgC,YAC1BhC,KAAKqD,aAEb,EAOAtB,EAAyBvH,UAAU6H,QAAU,WACnBrC,KAAKsD,oBAIvBtD,KAAKqC,SAEb,EASAN,EAAyBvH,UAAU8I,iBAAmB,WAElD,IAAIC,EAAkBvD,KAAKmC,WAAWqB,QAAO,SAAUR,GACnD,OAAOA,EAASS,eAAgBT,EAASU,WAC7C,IAOA,OADAH,EAAgB3C,SAAQ,SAAUoC,GAAY,OAAOA,EAASW,iBAAmB,IAC1EJ,EAAgBzJ,OAAS,CACpC,EAOAiI,EAAyBvH,UAAU0I,SAAW,WAGrCjC,IAAajB,KAAKgC,aAMvBb,SAASyC,iBAAiB,gBAAiB5D,KAAKoC,kBAChDlB,OAAO0C,iBAAiB,SAAU5D,KAAKqC,SACnCR,GACA7B,KAAKkC,mBAAqB,IAAIJ,iBAAiB9B,KAAKqC,SACpDrC,KAAKkC,mBAAmB2B,QAAQ1C,SAAU,CACtC2C,YAAY,EACZC,WAAW,EACXC,eAAe,EACfC,SAAS,MAIb9C,SAASyC,iBAAiB,qBAAsB5D,KAAKqC,SACrDrC,KAAKiC,sBAAuB,GAEhCjC,KAAKgC,YAAa,EACtB,EAOAD,EAAyBvH,UAAU6I,YAAc,WAGxCpC,GAAcjB,KAAKgC,aAGxBb,SAAS+C,oBAAoB,gBAAiBlE,KAAKoC,kBACnDlB,OAAOgD,oBAAoB,SAAUlE,KAAKqC,SACtCrC,KAAKkC,oBACLlC,KAAKkC,mBAAmBiC,aAExBnE,KAAKiC,sBACLd,SAAS+C,oBAAoB,qBAAsBlE,KAAKqC,SAE5DrC,KAAKkC,mBAAqB,KAC1BlC,KAAKiC,sBAAuB,EAC5BjC,KAAKgC,YAAa,EACtB,EAQAD,EAAyBvH,UAAU4H,iBAAmB,SAAUpB,GAC5D,IAAIoD,EAAKpD,EAAGqD,aAAcA,OAAsB,IAAPD,EAAgB,GAAKA,EAEvCxC,EAAehC,MAAK,SAAUlF,GACjD,SAAU2J,EAAapB,QAAQvI,EACnC,KAEIsF,KAAKqC,SAEb,EAMAN,EAAyBuC,YAAc,WAInC,OAHKtE,KAAKuE,YACNvE,KAAKuE,UAAY,IAAIxC,GAElB/B,KAAKuE,SAChB,EAMAxC,EAAyBwC,UAAY,KAC9BxC,CACX,CAjM6C,GA0MzCyC,EAAqB,SAAWC,EAAQC,GACxC,IAAK,IAAI3D,EAAK,EAAGC,EAAKzG,OAAOoK,KAAKD,GAAQ3D,EAAKC,EAAGlH,OAAQiH,IAAM,CAC5D,IAAIrG,EAAMsG,EAAGD,GACbxG,OAAO2F,eAAeuE,EAAQ/J,EAAK,CAC/BY,MAAOoJ,EAAMhK,GACb0F,YAAY,EACZwE,UAAU,EACVvE,cAAc,GAEtB,CACA,OAAOoE,CACV,EAQGI,EAAc,SAAWJ,GAOzB,OAHkBA,GAAUA,EAAOK,eAAiBL,EAAOK,cAAcC,aAGnD3D,CACzB,EAGG4D,EAAYC,EAAe,EAAG,EAAG,EAAG,GAOxC,SAASC,EAAQ5J,GACb,OAAO6J,WAAW7J,IAAU,CAChC,CAQA,SAAS8J,EAAeC,GAEpB,IADA,IAAIC,EAAY,GACPvE,EAAK,EAAGA,EAAKlH,UAAUC,OAAQiH,IACpCuE,EAAUvE,EAAK,GAAKlH,UAAUkH,GAElC,OAAOuE,EAAUC,QAAO,SAAUC,EAAMC,GAEpC,OAAOD,EAAON,EADFG,EAAO,UAAYI,EAAW,UAE9C,GAAG,EACP,CAyGA,IAAIC,EAGkC,oBAAvBC,mBACA,SAAUlB,GAAU,OAAOA,aAAkBI,EAAYJ,GAAQkB,kBAAoB,EAKzF,SAAUlB,GAAU,OAAQA,aAAkBI,EAAYJ,GAAQmB,YAC3C,mBAAnBnB,EAAOoB,OAAyB,EAiB/C,SAASC,EAAerB,GACpB,OAAKxD,EAGDyE,EAAqBjB,GAhH7B,SAA2BA,GACvB,IAAIsB,EAAOtB,EAAOoB,UAClB,OAAOZ,EAAe,EAAG,EAAGc,EAAKC,MAAOD,EAAKE,OACjD,CA8GeC,CAAkBzB,GAvGjC,SAAmCA,GAG/B,IAAI0B,EAAc1B,EAAO0B,YAAaC,EAAe3B,EAAO2B,aAS5D,IAAKD,IAAgBC,EACjB,OAAOpB,EAEX,IAAIK,EAASR,EAAYJ,GAAQ4B,iBAAiB5B,GAC9C6B,EA3CR,SAAqBjB,GAGjB,IAFA,IACIiB,EAAW,CAAC,EACPvF,EAAK,EAAGwF,EAFD,CAAC,MAAO,QAAS,SAAU,QAEDxF,EAAKwF,EAAYzM,OAAQiH,IAAM,CACrE,IAAI0E,EAAWc,EAAYxF,GACvBzF,EAAQ+J,EAAO,WAAaI,GAChCa,EAASb,GAAYP,EAAQ5J,EACjC,CACA,OAAOgL,CACX,CAkCmBE,CAAYnB,GACvBoB,EAAWH,EAASI,KAAOJ,EAASK,MACpCC,EAAUN,EAASO,IAAMP,EAASQ,OAKlCd,EAAQd,EAAQG,EAAOW,OAAQC,EAASf,EAAQG,EAAOY,QAqB3D,GAlByB,eAArBZ,EAAO0B,YAOH1F,KAAK2F,MAAMhB,EAAQS,KAAcN,IACjCH,GAASZ,EAAeC,EAAQ,OAAQ,SAAWoB,GAEnDpF,KAAK2F,MAAMf,EAASW,KAAaR,IACjCH,GAAUb,EAAeC,EAAQ,MAAO,UAAYuB,KAoDhE,SAA2BnC,GACvB,OAAOA,IAAWI,EAAYJ,GAAQtD,SAAS8F,eACnD,CA/CSC,CAAkBzC,GAAS,CAK5B,IAAI0C,EAAgB9F,KAAK2F,MAAMhB,EAAQS,GAAYN,EAC/CiB,EAAiB/F,KAAK2F,MAAMf,EAASW,GAAWR,EAMpB,IAA5B/E,KAAKgG,IAAIF,KACTnB,GAASmB,GAEoB,IAA7B9F,KAAKgG,IAAID,KACTnB,GAAUmB,EAElB,CACA,OAAOnC,EAAeqB,EAASI,KAAMJ,EAASO,IAAKb,EAAOC,EAC9D,CAyCWqB,CAA0B7C,GALtBO,CAMf,CAiCA,SAASC,EAAe5H,EAAGC,EAAG0I,EAAOC,GACjC,MAAO,CAAE5I,EAAGA,EAAGC,EAAGA,EAAG0I,MAAOA,EAAOC,OAAQA,EAC/C,CAMA,IAAIsB,EAAmC,WAMnC,SAASA,EAAkB9C,GAMvBzE,KAAKwH,eAAiB,EAMtBxH,KAAKyH,gBAAkB,EAMvBzH,KAAK0H,aAAezC,EAAe,EAAG,EAAG,EAAG,GAC5CjF,KAAKyE,OAASA,CAClB,CAyBA,OAlBA8C,EAAkB/M,UAAUmN,SAAW,WACnC,IAAIC,EAAO9B,EAAe9F,KAAKyE,QAE/B,OADAzE,KAAK0H,aAAeE,EACZA,EAAK5B,QAAUhG,KAAKwH,gBACxBI,EAAK3B,SAAWjG,KAAKyH,eAC7B,EAOAF,EAAkB/M,UAAUqN,cAAgB,WACxC,IAAID,EAAO5H,KAAK0H,aAGhB,OAFA1H,KAAKwH,eAAiBI,EAAK5B,MAC3BhG,KAAKyH,gBAAkBG,EAAK3B,OACrB2B,CACX,EACOL,CACX,CApDsC,GAsDlCO,EAOA,SAA6BrD,EAAQsD,GACjC,IA/FoB/G,EACpB3D,EAAUC,EAAU0I,EAAkBC,EAEtC+B,EACAJ,EA2FIK,GA9FJ5K,GADoB2D,EA+FiB+G,GA9F9B1K,EAAGC,EAAI0D,EAAG1D,EAAG0I,EAAQhF,EAAGgF,MAAOC,EAASjF,EAAGiF,OAElD+B,EAAoC,oBAApBE,gBAAkCA,gBAAkB3N,OACpEqN,EAAOrN,OAAO4N,OAAOH,EAAOxN,WAEhCgK,EAAmBoD,EAAM,CACrBvK,EAAGA,EAAGC,EAAGA,EAAG0I,MAAOA,EAAOC,OAAQA,EAClCY,IAAKvJ,EACLqJ,MAAOtJ,EAAI2I,EACXc,OAAQb,EAAS3I,EACjBoJ,KAAMrJ,IAEHuK,GAyFHpD,EAAmBxE,KAAM,CAAEyE,OAAQA,EAAQwD,YAAaA,GAC5D,EAIAG,EAAmC,WAWnC,SAASA,EAAkBvH,EAAUwH,EAAYC,GAc7C,GAPAtI,KAAKuI,oBAAsB,GAM3BvI,KAAKwI,cAAgB,IAAIhJ,EACD,mBAAbqB,EACP,MAAM,IAAI4H,UAAU,2DAExBzI,KAAK0I,UAAY7H,EACjBb,KAAK2I,YAAcN,EACnBrI,KAAK4I,aAAeN,CACxB,CAmHA,OA5GAF,EAAkB5N,UAAUqJ,QAAU,SAAUY,GAC5C,IAAK5K,UAAUC,OACX,MAAM,IAAI2O,UAAU,4CAGxB,GAAuB,oBAAZzK,SAA6BA,mBAAmBzD,OAA3D,CAGA,KAAMkK,aAAkBI,EAAYJ,GAAQzG,SACxC,MAAM,IAAIyK,UAAU,yCAExB,IAAII,EAAe7I,KAAKwI,cAEpBK,EAAanI,IAAI+D,KAGrBoE,EAAavI,IAAImE,EAAQ,IAAI8C,EAAkB9C,IAC/CzE,KAAK2I,YAAY5F,YAAY/C,MAE7BA,KAAK2I,YAAYtG,UAZjB,CAaJ,EAOA+F,EAAkB5N,UAAUsO,UAAY,SAAUrE,GAC9C,IAAK5K,UAAUC,OACX,MAAM,IAAI2O,UAAU,4CAGxB,GAAuB,oBAAZzK,SAA6BA,mBAAmBzD,OAA3D,CAGA,KAAMkK,aAAkBI,EAAYJ,GAAQzG,SACxC,MAAM,IAAIyK,UAAU,yCAExB,IAAII,EAAe7I,KAAKwI,cAEnBK,EAAanI,IAAI+D,KAGtBoE,EAAatI,OAAOkE,GACfoE,EAAarD,MACdxF,KAAK2I,YAAYxF,eAAenD,MAXpC,CAaJ,EAMAoI,EAAkB5N,UAAU2J,WAAa,WACrCnE,KAAK+I,cACL/I,KAAKwI,cAAc7H,QACnBX,KAAK2I,YAAYxF,eAAenD,KACpC,EAOAoI,EAAkB5N,UAAUiJ,aAAe,WACvC,IAAIuF,EAAQhJ,KACZA,KAAK+I,cACL/I,KAAKwI,cAAc5H,SAAQ,SAAUqI,GAC7BA,EAAYtB,YACZqB,EAAMT,oBAAoBtO,KAAKgP,EAEvC,GACJ,EAOAb,EAAkB5N,UAAUmJ,gBAAkB,WAE1C,GAAK3D,KAAK0D,YAAV,CAGA,IAAI5C,EAAMd,KAAK4I,aAEXpI,EAAUR,KAAKuI,oBAAoBW,KAAI,SAAUD,GACjD,OAAO,IAAInB,EAAoBmB,EAAYxE,OAAQwE,EAAYpB,gBACnE,IACA7H,KAAK0I,UAAU/N,KAAKmG,EAAKN,EAASM,GAClCd,KAAK+I,aAPL,CAQJ,EAMAX,EAAkB5N,UAAUuO,YAAc,WACtC/I,KAAKuI,oBAAoB9H,OAAO,EACpC,EAMA2H,EAAkB5N,UAAUkJ,UAAY,WACpC,OAAO1D,KAAKuI,oBAAoBzO,OAAS,CAC7C,EACOsO,CACX,CAnJsC,GAwJlChF,EAA+B,oBAAZ+F,QAA0B,IAAIA,QAAY,IAAI3J,EAKjE4J,EAOA,SAASA,EAAevI,GACpB,KAAMb,gBAAgBoJ,GAClB,MAAM,IAAIX,UAAU,sCAExB,IAAK5O,UAAUC,OACX,MAAM,IAAI2O,UAAU,4CAExB,IAAIJ,EAAatG,EAAyBuC,cACtCtB,EAAW,IAAIoF,EAAkBvH,EAAUwH,EAAYrI,MAC3DoD,EAAU9C,IAAIN,KAAMgD,EACxB,EAIJ,CACI,UACA,YACA,cACFpC,SAAQ,SAAUyI,GAChBD,EAAe5O,UAAU6O,GAAU,WAC/B,IAAIrI,EACJ,OAAQA,EAAKoC,EAAUjD,IAAIH,OAAOqJ,GAAQhP,MAAM2G,EAAInH,UACxD,CACJ,IAEA,IAAIiG,OAEuC,IAA5BsB,EAASgI,eACThI,EAASgI,eAEbA,EAGX,+BCz5BAvO,EAAOC,QANP,SAAgCmB,GAC9B,QAAa,IAATA,EACF,MAAM,IAAIqN,eAAe,6DAE3B,OAAOrN,CACT,EACyCpB,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,2BCO9GD,EAAOC,QAbP,SAAyB0O,EAAK9O,EAAKY,GAWjC,OAVIZ,KAAO8O,EACTjP,OAAO2F,eAAesJ,EAAK9O,EAAK,CAC9BY,MAAOA,EACP8E,YAAY,EACZC,cAAc,EACduE,UAAU,IAGZ4E,EAAI9O,GAAOY,EAENkO,CACT,EACkC3O,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,2BCbvG,SAAS2O,IAYP,OAXA5O,EAAOC,QAAU2O,EAAWlP,OAAOmP,OAASnP,OAAOmP,OAAOlI,OAAS,SAAUiD,GAC3E,IAAK,IAAI7K,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAI+P,EAAS9P,UAAUD,GACvB,IAAK,IAAIc,KAAOiP,EACVpP,OAAOC,UAAUf,eAAekB,KAAKgP,EAAQjP,KAC/C+J,EAAO/J,GAAOiP,EAAOjP,GAG3B,CACA,OAAO+J,CACT,EAAG5J,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,QACjE2O,EAASpP,MAAM2F,KAAMnG,UAC9B,CACAgB,EAAOC,QAAU2O,EAAU5O,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,2BCThGD,EAAOC,QALP,SAAgC0O,GAC9B,OAAOA,GAAOA,EAAID,WAAaC,EAAM,CACnC,QAAWA,EAEf,EACyC3O,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,+BCL9G,IAAI8O,EAAU,iBACd,SAASC,EAAyBC,GAChC,GAAuB,mBAAZX,QAAwB,OAAO,KAC1C,IAAIY,EAAoB,IAAIZ,QACxBa,EAAmB,IAAIb,QAC3B,OAAQU,EAA2B,SAAkCC,GACnE,OAAOA,EAAcE,EAAmBD,CAC1C,GAAGD,EACL,CAgCAjP,EAAOC,QA/BP,SAAiC0O,EAAKM,GACpC,IAAKA,GAAeN,GAAOA,EAAID,WAC7B,OAAOC,EAET,GAAY,OAARA,GAAiC,WAAjBI,EAAQJ,IAAoC,mBAARA,EACtD,MAAO,CACL,QAAWA,GAGf,IAAIS,EAAQJ,EAAyBC,GACrC,GAAIG,GAASA,EAAMvJ,IAAI8I,GACrB,OAAOS,EAAM9J,IAAIqJ,GAEnB,IAAIU,EAAS,CAAC,EACVC,EAAwB5P,OAAO2F,gBAAkB3F,OAAO6P,yBAC5D,IAAK,IAAI1P,KAAO8O,EACd,GAAY,YAAR9O,GAAqBH,OAAOC,UAAUf,eAAekB,KAAK6O,EAAK9O,GAAM,CACvE,IAAI2P,EAAOF,EAAwB5P,OAAO6P,yBAAyBZ,EAAK9O,GAAO,KAC3E2P,IAASA,EAAKlK,KAAOkK,EAAK/J,KAC5B/F,OAAO2F,eAAegK,EAAQxP,EAAK2P,GAEnCH,EAAOxP,GAAO8O,EAAI9O,EAEtB,CAMF,OAJAwP,EAAgB,QAAIV,EAChBS,GACFA,EAAM3J,IAAIkJ,EAAKU,GAEVA,CACT,EAC0CrP,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,+BCxC/G,IAAIwP,EAA+B,EAAQ,MAgB3CzP,EAAOC,QAfP,SAAkC6O,EAAQY,GACxC,GAAc,MAAVZ,EAAgB,MAAO,CAAC,EAC5B,IACIjP,EAAKd,EADL6K,EAAS6F,EAA6BX,EAAQY,GAElD,GAAIhQ,OAAOiQ,sBAAuB,CAChC,IAAIC,EAAmBlQ,OAAOiQ,sBAAsBb,GACpD,IAAK/P,EAAI,EAAGA,EAAI6Q,EAAiB3Q,OAAQF,IACvCc,EAAM+P,EAAiB7Q,GACnB2Q,EAAStH,QAAQvI,IAAQ,GACxBH,OAAOC,UAAUkQ,qBAAqB/P,KAAKgP,EAAQjP,KACxD+J,EAAO/J,GAAOiP,EAAOjP,GAEzB,CACA,OAAO+J,CACT,EAC2C5J,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,0BCJhHD,EAAOC,QAZP,SAAuC6O,EAAQY,GAC7C,GAAc,MAAVZ,EAAgB,MAAO,CAAC,EAC5B,IAEIjP,EAAKd,EAFL6K,EAAS,CAAC,EACVkG,EAAapQ,OAAOoK,KAAKgF,GAE7B,IAAK/P,EAAI,EAAGA,EAAI+Q,EAAW7Q,OAAQF,IACjCc,EAAMiQ,EAAW/Q,GACb2Q,EAAStH,QAAQvI,IAAQ,IAC7B+J,EAAO/J,GAAOiP,EAAOjP,IAEvB,OAAO+J,CACT,EACgD5J,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,0BCZrH,SAAS8P,EAAgBC,EAAG9N,GAK1B,OAJAlC,EAAOC,QAAU8P,EAAkBrQ,OAAOuQ,eAAiBvQ,OAAOuQ,eAAetJ,OAAS,SAAyBqJ,EAAG9N,GAEpH,OADA8N,EAAEE,UAAYhO,EACP8N,CACT,EAAGhQ,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,QACjE8P,EAAgBC,EAAG9N,EAC5B,CACAlC,EAAOC,QAAU8P,EAAiB/P,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,2BCPvG,SAAS8O,EAAQJ,GAGf,OAAQ3O,EAAOC,QAAU8O,EAAU,mBAAqB5O,QAAU,iBAAmBA,OAAOgQ,SAAW,SAAUxB,GAC/G,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAO,mBAAqBxO,QAAUwO,EAAIyB,cAAgBjQ,QAAUwO,IAAQxO,OAAOR,UAAY,gBAAkBgP,CAC1H,EAAG3O,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC,QAAU8O,EAAQJ,EAC5F,CACA3O,EAAOC,QAAU8O,EAAS/O,EAAOC,QAAQyO,YAAa,EAAM1O,EAAOC,QAAiB,QAAID,EAAOC", "sources": ["webpack:///./node_modules/classnames/index.js", "webpack:///./node_modules/lodash/_Symbol.js", "webpack:///./node_modules/lodash/_baseGetTag.js", "webpack:///./node_modules/lodash/_freeGlobal.js", "webpack:///./node_modules/lodash/_getRawTag.js", "webpack:///./node_modules/lodash/_objectToString.js", "webpack:///./node_modules/lodash/_root.js", "webpack:///./node_modules/lodash/isObject.js", "webpack:///./node_modules/lodash/isObjectLike.js", "webpack:///./node_modules/react-is/cjs/react-is.production.min.js", "webpack:///./node_modules/react-is/index.js", "webpack:///./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js", "webpack:///./node_modules/@babel/runtime/helpers/assertThisInitialized.js", "webpack:///./node_modules/@babel/runtime/helpers/defineProperty.js", "webpack:///./node_modules/@babel/runtime/helpers/extends.js", "webpack:///./node_modules/@babel/runtime/helpers/interopRequireDefault.js", "webpack:///./node_modules/@babel/runtime/helpers/interopRequireWildcard.js", "webpack:///./node_modules/@babel/runtime/helpers/objectWithoutProperties.js", "webpack:///./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "webpack:///./node_modules/@babel/runtime/helpers/setPrototypeOf.js", "webpack:///./node_modules/@babel/runtime/helpers/typeof.js"], "sourcesContent": ["/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\tvar nativeCodeString = '[native code]';\n\n\tfunction classNames() {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg)) {\n\t\t\t\tif (arg.length) {\n\t\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\t\tif (inner) {\n\t\t\t\t\t\tclasses.push(inner);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\t\t\tclasses.push(arg.toString());\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tfor (var key in arg) {\n\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n", "var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n", "var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n", "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nmodule.exports = isObject;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nmodule.exports = isObjectLike;\n", "/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';var b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\nSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\nfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;\nexports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;exports.isAsyncMode=function(a){return A(a)||z(a)===l};exports.isConcurrentMode=A;exports.isContextConsumer=function(a){return z(a)===k};exports.isContextProvider=function(a){return z(a)===h};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return z(a)===n};exports.isFragment=function(a){return z(a)===e};exports.isLazy=function(a){return z(a)===t};\nexports.isMemo=function(a){return z(a)===r};exports.isPortal=function(a){return z(a)===d};exports.isProfiler=function(a){return z(a)===g};exports.isStrictMode=function(a){return z(a)===f};exports.isSuspense=function(a){return z(a)===p};\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};exports.typeOf=z;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _extends() {\n  module.exports = _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _extends.apply(this, arguments);\n}\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nmodule.exports = _interopRequireWildcard, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(obj);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": ["hasOwn", "hasOwnProperty", "classNames", "classes", "i", "arguments", "length", "arg", "argType", "push", "Array", "isArray", "inner", "apply", "toString", "Object", "prototype", "includes", "key", "call", "join", "module", "exports", "default", "Symbol", "getRawTag", "objectToString", "symToStringTag", "toStringTag", "undefined", "value", "freeGlobal", "g", "objectProto", "nativeObjectToString", "isOwn", "tag", "unmasked", "e", "result", "freeSelf", "self", "root", "Function", "type", "b", "for", "c", "d", "f", "h", "k", "l", "m", "n", "p", "q", "r", "t", "v", "w", "x", "y", "z", "a", "u", "$$typeof", "A", "AsyncMode", "ConcurrentMode", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isValidElementType", "typeOf", "MapShim", "Map", "getIndex", "arr", "some", "entry", "index", "class_1", "this", "__entries__", "defineProperty", "get", "enumerable", "configurable", "set", "delete", "entries", "splice", "has", "clear", "for<PERSON>ach", "callback", "ctx", "_i", "_a", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "global$1", "Math", "requestAnimationFrame$1", "requestAnimationFrame", "bind", "setTimeout", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "mutationObserverSupported", "MutationObserver", "ResizeObserverController", "connected_", "mutationEventsAdded_", "mutationsObserver_", "observers_", "onTransitionEnd_", "refresh", "delay", "leadingCall", "trailingCall", "lastCallTime", "resolvePending", "proxy", "timeout<PERSON><PERSON><PERSON>", "timeStamp", "throttle", "addObserver", "observer", "indexOf", "connect_", "removeObserver", "observers", "disconnect_", "updateObservers_", "activeObservers", "filter", "gatherActive", "hasActive", "broadcastActive", "addEventListener", "observe", "attributes", "childList", "characterData", "subtree", "removeEventListener", "disconnect", "_b", "propertyName", "getInstance", "instance_", "defineConfigurable", "target", "props", "keys", "writable", "getWindowOf", "ownerDocument", "defaultView", "emptyRect", "createRectInit", "toFloat", "parseFloat", "getBordersSize", "styles", "positions", "reduce", "size", "position", "isSVGGraphicsElement", "SVGGraphicsElement", "SVGElement", "getBBox", "getContentRect", "bbox", "width", "height", "getSVGContentRect", "clientWidth", "clientHeight", "getComputedStyle", "paddings", "positions_1", "getPaddings", "horizPad", "left", "right", "vertPad", "top", "bottom", "boxSizing", "round", "documentElement", "isDocumentElement", "vertScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abs", "getHTMLElementContentRect", "ResizeObservation", "broadcastWidth", "broadcastHeight", "contentRect_", "isActive", "rect", "broadcastRect", "ResizeObserverEntry", "rectInit", "Constr", "contentRect", "DOMRectReadOnly", "create", "ResizeObserverSPI", "controller", "callbackCtx", "activeObservations_", "observations_", "TypeError", "callback_", "controller_", "callbackCtx_", "observations", "unobserve", "clearActive", "_this", "observation", "map", "WeakMap", "ResizeObserver", "method", "ReferenceError", "__esModule", "obj", "_extends", "assign", "source", "_typeof", "_getRequireWildcardCache", "nodeInterop", "cacheBabelInterop", "cacheNodeInterop", "cache", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "desc", "objectWithoutPropertiesLoose", "excluded", "getOwnPropertySymbols", "sourceSymbolKeys", "propertyIsEnumerable", "sourceKeys", "_setPrototypeOf", "o", "setPrototypeOf", "__proto__", "iterator", "constructor"], "sourceRoot": ""}