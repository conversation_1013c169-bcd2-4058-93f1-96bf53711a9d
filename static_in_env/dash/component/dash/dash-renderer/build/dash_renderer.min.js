/*! For license information please see dash_renderer.min.js.LICENSE.txt */
!function(){var t={800:function(t,e,r){var n;window,t.exports=(n=r(196),function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(t,e){t.exports=n},function(t,e,r){"use strict";r.r(e),r.d(e,"asyncDecorator",(function(){return a})),r.d(e,"inheritAsyncDecorator",(function(){return u})),r.d(e,"isReady",(function(){return c})),r.d(e,"History",(function(){return l}));var n=r(0);function o(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function i(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function u(t){o(a,n,i,u,c,"next",t)}function c(t){o(a,n,i,u,c,"throw",t)}u(void 0)}))}}var a=function(t,e){var r,o={isReady:new Promise((function(t){r=t})),get:Object(n.lazy)((function(){return Promise.resolve(e()).then((function(t){return setTimeout(i(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r(!0);case 2:o.isReady=!0;case 3:case"end":return t.stop()}}),t)}))),0),t}))}))};return Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return o.isReady}}),o.get},u=function(t,e){Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return c(e)}})},c=function(t){return t&&t._dashprivate_isLazyComponentReady};function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var f="_dashprivate_historychange",l=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r;return e=t,r=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(f))}},{key:"onChange",value:function(t){return window.addEventListener(f,t),function(){return window.removeEventListener(f,t)}}}],null&&s(e.prototype,null),r&&s(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}()}]))},566:function(t,e){"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isContextConsumer=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case l:case p:return t;default:switch(t=t&&t.$$typeof){case s:case c:case f:case d:case h:case u:return t;default:return e}}case n:return e}}}(t)===c}},100:function(t,e,r){"use strict";t.exports=r(566)},405:function(t,e,r){"use strict";var n=r(196),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},i=n.useState,a=n.useEffect,u=n.useLayoutEffect,c=n.useDebugValue;function s(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!o(t,r)}catch(t){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),n=i({inst:{value:r,getSnapshot:e}}),o=n[0].inst,f=n[1];return u((function(){o.value=r,o.getSnapshot=e,s(o)&&f({inst:o})}),[t,r,e]),a((function(){return s(o)&&f({inst:o}),t((function(){s(o)&&f({inst:o})}))}),[t]),c(r),r};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:f},332:function(t,e,r){"use strict";var n=r(196),o=r(270),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},a=o.useSyncExternalStore,u=n.useRef,c=n.useEffect,s=n.useMemo,f=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,r,n,o){var l=u(null);if(null===l.current){var p={hasValue:!1,value:null};l.current=p}else p=l.current;l=s((function(){function t(t){if(!c){if(c=!0,a=t,t=n(t),void 0!==o&&p.hasValue){var e=p.value;if(o(e,t))return u=e}return u=t}if(e=u,i(a,t))return e;var r=n(t);return void 0!==o&&o(e,r)?e:(a=t,u=r)}var a,u,c=!1,s=void 0===r?null:r;return[function(){return t(e())},null===s?void 0:function(){return t(s())}]}),[e,r,n,o]);var h=a(t,l[0],l[1]);return c((function(){p.hasValue=!0,p.value=h}),[h]),f(h),h}},270:function(t,e,r){"use strict";t.exports=r(405)},826:function(t,e,r){"use strict";t.exports=r(332)},489:function(t,e){"use strict";e.parse=function(t,e){if("string"!=typeof t)throw new TypeError("argument str must be a string");for(var r={},n=(e||{}).decode||o,i=0;i<t.length;){var u=t.indexOf("=",i);if(-1===u)break;var c=t.indexOf(";",i);if(-1===c)c=t.length;else if(c<u){i=t.lastIndexOf(";",u-1)+1;continue}var s=t.slice(i,u).trim();if(void 0===r[s]){var f=t.slice(u+1,c).trim();34===f.charCodeAt(0)&&(f=f.slice(1,-1)),r[s]=a(f,n)}i=c+1}return r},e.serialize=function(t,e,o){var a=o||{},u=a.encode||i;if("function"!=typeof u)throw new TypeError("option encode is invalid");if(!n.test(t))throw new TypeError("argument name is invalid");var c=u(e);if(c&&!n.test(c))throw new TypeError("argument val is invalid");var s=t+"="+c;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(f)}if(a.domain){if(!n.test(a.domain))throw new TypeError("option domain is invalid");s+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw new TypeError("option path is invalid");s+="; Path="+a.path}if(a.expires){var l=a.expires;if(!function(t){return"[object Date]"===r.call(t)||t instanceof Date}(l)||isNaN(l.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+l.toUTCString()}if(a.httpOnly&&(s+="; HttpOnly"),a.secure&&(s+="; Secure"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":s+="; Priority=Low";break;case"medium":s+="; Priority=Medium";break;case"high":s+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"strict":s+="; SameSite=Strict";break;case"none":s+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s};var r=Object.prototype.toString,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(t){return-1!==t.indexOf("%")?decodeURIComponent(t):t}function i(t){return encodeURIComponent(t)}function a(t,e){try{return e(t)}catch(e){return t}}},789:function(t,e,r){"use strict";var n=r(81),o=r.n(n),i=r(645),a=r.n(i)()(o());a.push([t.id,"._dash-undo-redo {\n    position: fixed;\n    bottom: 30px;\n    left: 30px;\n    font-size: 20px;\n    text-align: center;\n    z-index: 9999;\n    background-color: rgba(255, 255, 255, 0.9);\n\n}\n._dash-undo-redo>div {\n    position: relative;\n}\n._dash-undo-redo-link {\n    color: #0074D9;\n    cursor: pointer;\n    margin-left: 10px;\n    margin-right: 10px;\n    display: inline-block;\n    opacity: 0.2;\n}\n._dash-undo-redo-link:hover {\n    opacity: 1;\n}\n._dash-undo-redo-link ._dash-icon-undo {\n    font-size: 20px;\n    transform: rotate(270deg);\n}\n._dash-undo-redo-link ._dash-icon-redo {\n    font-size: 20px;\n    transform: rotate(90deg);\n}\n._dash-undo-redo-link ._dash-undo-redo-label {\n    font-size: 15px;\n}\n",""]),e.Z=a},645:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r="",n=void 0!==e[5];return e[4]&&(r+="@supports (".concat(e[4],") {")),e[2]&&(r+="@media ".concat(e[2]," {")),n&&(r+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),r+=t(e),n&&(r+="}"),e[2]&&(r+="}"),e[4]&&(r+="}"),r})).join("")},e.i=function(t,r,n,o,i){"string"==typeof t&&(t=[[null,t,void 0]]);var a={};if(n)for(var u=0;u<this.length;u++){var c=this[u][0];null!=c&&(a[c]=!0)}for(var s=0;s<t.length;s++){var f=[].concat(t[s]);n&&a[f[0]]||(void 0!==i&&(void 0===f[5]||(f[1]="@layer".concat(f[5].length>0?" ".concat(f[5]):""," {").concat(f[1],"}")),f[5]=i),r&&(f[2]?(f[1]="@media ".concat(f[2]," {").concat(f[1],"}"),f[2]=r):f[2]=r),o&&(f[4]?(f[1]="@supports (".concat(f[4],") {").concat(f[1],"}"),f[4]=o):f[4]="".concat(o)),e.push(f))}},e}},81:function(t){"use strict";t.exports=function(t){return t[1]}},357:function(t,e){function r(t,e,r,n){var i={};return function(a){if(!i[a]){var u={},c=[],s=[];for(s.push({node:a,processed:!1});s.length>0;){var f=s[s.length-1],l=f.processed,p=f.node;if(l)s.pop(),c.pop(),u[p]=!1,i[p]=!0,e&&0!==t[p].length||r.push(p);else{if(i[p]){s.pop();continue}if(u[p]){if(n){s.pop();continue}throw c.push(p),new o(c)}u[p]=!0,c.push(p);for(var h=t[p],d=h.length-1;d>=0;d--)s.push({node:h[d],processed:!1});f.processed=!0}}}}}var n=e.f=function(t){this.nodes={},this.outgoingEdges={},this.incomingEdges={},this.circular=t&&!!t.circular};n.prototype={size:function(){return Object.keys(this.nodes).length},addNode:function(t,e){this.hasNode(t)||(this.nodes[t]=2===arguments.length?e:t,this.outgoingEdges[t]=[],this.incomingEdges[t]=[])},removeNode:function(t){this.hasNode(t)&&(delete this.nodes[t],delete this.outgoingEdges[t],delete this.incomingEdges[t],[this.incomingEdges,this.outgoingEdges].forEach((function(e){Object.keys(e).forEach((function(r){var n=e[r].indexOf(t);n>=0&&e[r].splice(n,1)}),this)})))},hasNode:function(t){return this.nodes.hasOwnProperty(t)},getNodeData:function(t){if(this.hasNode(t))return this.nodes[t];throw new Error("Node does not exist: "+t)},setNodeData:function(t,e){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);this.nodes[t]=e},addDependency:function(t,e){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);if(!this.hasNode(e))throw new Error("Node does not exist: "+e);return-1===this.outgoingEdges[t].indexOf(e)&&this.outgoingEdges[t].push(e),-1===this.incomingEdges[e].indexOf(t)&&this.incomingEdges[e].push(t),!0},removeDependency:function(t,e){var r;this.hasNode(t)&&(r=this.outgoingEdges[t].indexOf(e))>=0&&this.outgoingEdges[t].splice(r,1),this.hasNode(e)&&(r=this.incomingEdges[e].indexOf(t))>=0&&this.incomingEdges[e].splice(r,1)},clone:function(){var t=this,e=new n;return Object.keys(t.nodes).forEach((function(r){e.nodes[r]=t.nodes[r],e.outgoingEdges[r]=t.outgoingEdges[r].slice(0),e.incomingEdges[r]=t.incomingEdges[r].slice(0)})),e},directDependenciesOf:function(t){if(this.hasNode(t))return this.outgoingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},directDependantsOf:function(t){if(this.hasNode(t))return this.incomingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},dependenciesOf:function(t,e){if(this.hasNode(t)){var n=[];r(this.outgoingEdges,e,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},dependantsOf:function(t,e){if(this.hasNode(t)){var n=[];r(this.incomingEdges,e,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},overallOrder:function(t){var e=this,n=[],o=Object.keys(this.nodes);if(0===o.length)return n;if(!this.circular){var i=r(this.outgoingEdges,!1,[],this.circular);o.forEach((function(t){i(t)}))}var a=r(this.outgoingEdges,t,n,this.circular);return o.filter((function(t){return 0===e.incomingEdges[t].length})).forEach((function(t){a(t)})),this.circular&&o.filter((function(t){return-1===n.indexOf(t)})).forEach((function(t){a(t)})),n},entryNodes:function(){var t=this;return Object.keys(this.nodes).filter((function(e){return 0===t.incomingEdges[e].length}))}},n.prototype.directDependentsOf=n.prototype.directDependantsOf,n.prototype.dependentsOf=n.prototype.dependantsOf;var o=function(t){var e="Dependency Cycle Found: "+t.join(" -> "),r=new Error(e);return r.cyclePath=t,Object.setPrototypeOf(r,Object.getPrototypeOf(this)),Error.captureStackTrace&&Error.captureStackTrace(r,o),r};o.prototype=Object.create(Error.prototype,{constructor:{value:Error,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf(o,Error)},924:function(t,e,r){"use strict";var n=r(244);t.exports=function(t){var e=typeof t;if("string"===e){var r=t;if(0==(t=+t)&&n(r))return!1}else if("number"!==e)return!1;return t-t<1}},679:function(t,e,r){"use strict";var n=r(864),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function c(t){return n.isMemo(t)?a:u[t.$$typeof]||o}u[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[n.Memo]=a;var s=Object.defineProperty,f=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,d=Object.prototype;t.exports=function t(e,r,n){if("string"!=typeof r){if(d){var o=h(r);o&&o!==d&&t(e,o,n)}var a=f(r);l&&(a=a.concat(l(r)));for(var u=c(e),y=c(r),v=0;v<a.length;++v){var g=a[v];if(!(i[g]||n&&n[g]||y&&y[g]||u&&u[g])){var b=p(r,g);try{s(e,g,b)}catch(t){}}}}return e}},143:function(t){"use strict";t.exports=function(t,e,r,n,o,i,a,u){if(!t){var c;if(void 0===e)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[r,n,o,i,a,u],f=0;(c=new Error(e.replace(/%s/g,(function(){return s[f++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}},244:function(t){"use strict";t.exports=function(t){for(var e,r=t.length,n=0;n<r;n++)if(((e=t.charCodeAt(n))<9||e>13)&&32!==e&&133!==e&&160!==e&&5760!==e&&6158!==e&&(e<8192||e>8205)&&8232!==e&&8233!==e&&8239!==e&&8287!==e&&8288!==e&&12288!==e&&65279!==e)return!1;return!0}},414:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},921:function(t,e){"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,u=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,f=r?Symbol.for("react.async_mode"):60111,l=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,d=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,g=r?Symbol.for("react.block"):60121,b=r?Symbol.for("react.fundamental"):60117,m=r?Symbol.for("react.responder"):60118,w=r?Symbol.for("react.scope"):60119;function O(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case f:case l:case i:case u:case a:case h:return t;default:switch(t=t&&t.$$typeof){case s:case p:case v:case y:case c:return t;default:return e}}case o:return e}}}function _(t){return O(t)===l}e.AsyncMode=f,e.ConcurrentMode=l,e.ContextConsumer=s,e.ContextProvider=c,e.Element=n,e.ForwardRef=p,e.Fragment=i,e.Lazy=v,e.Memo=y,e.Portal=o,e.Profiler=u,e.StrictMode=a,e.Suspense=h,e.isAsyncMode=function(t){return _(t)||O(t)===f},e.isConcurrentMode=_,e.isContextConsumer=function(t){return O(t)===s},e.isContextProvider=function(t){return O(t)===c},e.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===n},e.isForwardRef=function(t){return O(t)===p},e.isFragment=function(t){return O(t)===i},e.isLazy=function(t){return O(t)===v},e.isMemo=function(t){return O(t)===y},e.isPortal=function(t){return O(t)===o},e.isProfiler=function(t){return O(t)===u},e.isStrictMode=function(t){return O(t)===a},e.isSuspense=function(t){return O(t)===h},e.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===i||t===l||t===u||t===a||t===h||t===d||"object"==typeof t&&null!==t&&(t.$$typeof===v||t.$$typeof===y||t.$$typeof===c||t.$$typeof===s||t.$$typeof===p||t.$$typeof===b||t.$$typeof===m||t.$$typeof===w||t.$$typeof===g)},e.typeOf=O},864:function(t,e,r){"use strict";t.exports=r(921)},379:function(t){"use strict";var e=[];function r(t){for(var r=-1,n=0;n<e.length;n++)if(e[n].identifier===t){r=n;break}return r}function n(t,n){for(var i={},a=[],u=0;u<t.length;u++){var c=t[u],s=n.base?c[0]+n.base:c[0],f=i[s]||0,l="".concat(s," ").concat(f);i[s]=f+1;var p=r(l),h={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)e[p].references++,e[p].updater(h);else{var d=o(h,n);n.byIndex=u,e.splice(u,0,{identifier:l,updater:d,references:1})}a.push(l)}return a}function o(t,e){var r=e.domAPI(e);return r.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;r.update(t=e)}else r.remove()}}t.exports=function(t,o){var i=n(t=t||[],o=o||{});return function(t){t=t||[];for(var a=0;a<i.length;a++){var u=r(i[a]);e[u].references--}for(var c=n(t,o),s=0;s<i.length;s++){var f=r(i[s]);0===e[f].references&&(e[f].updater(),e.splice(f,1))}i=c}}},569:function(t){"use strict";var e={};t.exports=function(t,r){var n=function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}e[t]=r}return e[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},216:function(t){"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},565:function(t,e,r){"use strict";t.exports=function(t){var e=r.nc;e&&t.setAttribute("nonce",e)}},795:function(t){"use strict";t.exports=function(t){var e=t.insertStyleElement(t);return{update:function(r){!function(t,e,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleTagTransform(n,t,e.options)}(e,t,r)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},589:function(t){"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},196:function(t){"use strict";t.exports=window.React}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,exports:{}};return t[n](i,i.exports,r),i.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var n={};!function(){"use strict";var t="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==t&&t,e="URLSearchParams"in t,r="Symbol"in t&&"iterator"in Symbol,n="FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(t){return!1}}(),o="FormData"in t,i="ArrayBuffer"in t;if(i)var a=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],u=ArrayBuffer.isView||function(t){return t&&a.indexOf(Object.prototype.toString.call(t))>-1};function c(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function s(t){return"string"!=typeof t&&(t=String(t)),t}function f(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return r&&(e[Symbol.iterator]=function(){return e}),e}function l(t){this.map={},t instanceof l?t.forEach((function(t,e){this.append(e,t)}),this):Array.isArray(t)?t.forEach((function(t){this.append(t[0],t[1])}),this):t&&Object.getOwnPropertyNames(t).forEach((function(e){this.append(e,t[e])}),this)}function p(t){if(t.bodyUsed)return Promise.reject(new TypeError("Already read"));t.bodyUsed=!0}function h(t){return new Promise((function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}}))}function d(t){var e=new FileReader,r=h(e);return e.readAsArrayBuffer(t),r}function y(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function v(){return this.bodyUsed=!1,this._initBody=function(t){var r;this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:n&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:o&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:e&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():i&&n&&(r=t)&&DataView.prototype.isPrototypeOf(r)?(this._bodyArrayBuffer=y(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):i&&(ArrayBuffer.prototype.isPrototypeOf(t)||u(t))?this._bodyArrayBuffer=y(t):this._bodyText=t=Object.prototype.toString.call(t):this._bodyText="",this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):e&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},n&&(this.blob=function(){var t=p(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?p(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer)):this.blob().then(d)}),this.text=function(){var t,e,r,n=p(this);if(n)return n;if(this._bodyBlob)return t=this._bodyBlob,r=h(e=new FileReader),e.readAsText(t),r;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),r=new Array(e.length),n=0;n<e.length;n++)r[n]=String.fromCharCode(e[n]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},o&&(this.formData=function(){return this.text().then(m)}),this.json=function(){return this.text().then(JSON.parse)},this}l.prototype.append=function(t,e){t=c(t),e=s(e);var r=this.map[t];this.map[t]=r?r+", "+e:e},l.prototype.delete=function(t){delete this.map[c(t)]},l.prototype.get=function(t){return t=c(t),this.has(t)?this.map[t]:null},l.prototype.has=function(t){return this.map.hasOwnProperty(c(t))},l.prototype.set=function(t,e){this.map[c(t)]=s(e)},l.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},l.prototype.keys=function(){var t=[];return this.forEach((function(e,r){t.push(r)})),f(t)},l.prototype.values=function(){var t=[];return this.forEach((function(e){t.push(e)})),f(t)},l.prototype.entries=function(){var t=[];return this.forEach((function(e,r){t.push([r,e])})),f(t)},r&&(l.prototype[Symbol.iterator]=l.prototype.entries);var g=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function b(t,e){if(!(this instanceof b))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var r,n,o=(e=e||{}).body;if(t instanceof b){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new l(t.headers)),this.method=t.method,this.mode=t.mode,this.signal=t.signal,o||null==t._bodyInit||(o=t._bodyInit,t.bodyUsed=!0)}else this.url=String(t);if(this.credentials=e.credentials||this.credentials||"same-origin",!e.headers&&this.headers||(this.headers=new l(e.headers)),this.method=(n=(r=e.method||this.method||"GET").toUpperCase(),g.indexOf(n)>-1?n:r),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&o)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(o),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==e.cache&&"no-cache"!==e.cache)){var i=/([?&])_=[^&]*/;i.test(this.url)?this.url=this.url.replace(i,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}function m(t){var e=new FormData;return t.trim().split("&").forEach((function(t){if(t){var r=t.split("="),n=r.shift().replace(/\+/g," "),o=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(n),decodeURIComponent(o))}})),e}function w(t,e){if(!(this instanceof w))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');e||(e={}),this.type="default",this.status=void 0===e.status?200:e.status,this.ok=this.status>=200&&this.status<300,this.statusText=void 0===e.statusText?"":""+e.statusText,this.headers=new l(e.headers),this.url=e.url||"",this._initBody(t)}b.prototype.clone=function(){return new b(this,{body:this._bodyInit})},v.call(b.prototype),v.call(w.prototype),w.prototype.clone=function(){return new w(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new l(this.headers),url:this.url})},w.error=function(){var t=new w(null,{status:0,statusText:""});return t.type="error",t};var O=[301,302,303,307,308];w.redirect=function(t,e){if(-1===O.indexOf(e))throw new RangeError("Invalid status code");return new w(null,{status:e,headers:{location:t}})};var _=t.DOMException;try{new _}catch(t){(_=function(t,e){this.message=t,this.name=e;var r=Error(t);this.stack=r.stack}).prototype=Object.create(Error.prototype),_.prototype.constructor=_}function E(e,r){return new Promise((function(o,a){var u=new b(e,r);if(u.signal&&u.signal.aborted)return a(new _("Aborted","AbortError"));var c=new XMLHttpRequest;function f(){c.abort()}c.onload=function(){var t,e,r={status:c.status,statusText:c.statusText,headers:(t=c.getAllResponseHeaders()||"",e=new l,t.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t})).forEach((function(t){var r=t.split(":"),n=r.shift().trim();if(n){var o=r.join(":").trim();e.append(n,o)}})),e)};r.url="responseURL"in c?c.responseURL:r.headers.get("X-Request-URL");var n="response"in c?c.response:c.responseText;setTimeout((function(){o(new w(n,r))}),0)},c.onerror=function(){setTimeout((function(){a(new TypeError("Network request failed"))}),0)},c.ontimeout=function(){setTimeout((function(){a(new TypeError("Network request failed"))}),0)},c.onabort=function(){setTimeout((function(){a(new _("Aborted","AbortError"))}),0)},c.open(u.method,function(e){try{return""===e&&t.location.href?t.location.href:e}catch(t){return e}}(u.url),!0),"include"===u.credentials?c.withCredentials=!0:"omit"===u.credentials&&(c.withCredentials=!1),"responseType"in c&&(n?c.responseType="blob":i&&u.headers.get("Content-Type")&&-1!==u.headers.get("Content-Type").indexOf("application/octet-stream")&&(c.responseType="arraybuffer")),!r||"object"!=typeof r.headers||r.headers instanceof l?u.headers.forEach((function(t,e){c.setRequestHeader(e,t)})):Object.getOwnPropertyNames(r.headers).forEach((function(t){c.setRequestHeader(t,s(r.headers[t]))})),u.signal&&(u.signal.addEventListener("abort",f),c.onreadystatechange=function(){4===c.readyState&&u.signal.removeEventListener("abort",f)}),c.send(void 0===u._bodyInit?null:u._bodyInit)}))}E.polyfill=!0,t.fetch||(t.fetch=E,t.Headers=l,t.Request=b,t.Response=w)}(),function(){"use strict";r.r(n);var t=r(196),e=r.n(t),o=window.ReactDOM,i=r.n(o),a=window.PropTypes,u=r.n(a),c=r(270),s=r(826),f=function(t){t()},l=function(){return f},p=(0,t.createContext)(null);function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},h.apply(this,arguments)}function d(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}var y=r(679),v=r.n(y),g=r(100),b=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function m(t,e,r,n,o){var i,a,u,c,s,f=o.areStatesEqual,l=o.areOwnPropsEqual,p=o.areStatePropsEqual,h=!1;return function(o,d){return h?function(o,h){var d,y,v=!l(h,a),g=!f(o,i,h,a);return i=o,a=h,v&&g?(u=t(i,a),e.dependsOnOwnProps&&(c=e(n,a)),s=r(u,c,a)):v?(t.dependsOnOwnProps&&(u=t(i,a)),e.dependsOnOwnProps&&(c=e(n,a)),s=r(u,c,a)):g?(d=t(i,a),y=!p(d,u),u=d,y&&(s=r(u,c,a)),s):s}(o,d):(u=t(i=o,a=d),c=e(n,a),s=r(u,c,a),h=!0,s)}}function w(t,e){var r=e.initMapStateToProps,n=e.initMapDispatchToProps,o=e.initMergeProps,i=d(e,b);return m(r(t,i),n(t,i),o(t,i),t,i)}function O(t){return function(e){var r=t(e);function n(){return r}return n.dependsOnOwnProps=!1,n}}function _(t){return t.dependsOnOwnProps?Boolean(t.dependsOnOwnProps):1!==t.length}function E(t,e){return function(e,r){r.displayName;var n=function(t,e){return n.dependsOnOwnProps?n.mapToProps(t,e):n.mapToProps(t,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(e,r){n.mapToProps=t,n.dependsOnOwnProps=_(t);var o=n(e,r);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=_(o),o=n(e,r)),o},n}}function j(t,e){return function(r,n){throw new Error("Invalid value of type ".concat(typeof t," for ").concat(e," argument when connecting component ").concat(n.wrappedComponentName,"."))}}function x(t){return t&&"object"==typeof t?O((function(e){return function(t,e){var r={},n=function(n){var o=t[n];"function"==typeof o&&(r[n]=function(){return e(o.apply(void 0,arguments))})};for(var o in t)n(o);return r}(t,e)})):t?"function"==typeof t?E(t):j(t,"mapDispatchToProps"):O((function(t){return{dispatch:t}}))}function S(t){return t?"function"==typeof t?E(t):j(t,"mapStateToProps"):O((function(){return{}}))}function P(t,e,r){return h({},r,t,e)}function A(t){return t?"function"==typeof t?function(t){return function(e,r){r.displayName;var n,o=r.areMergedPropsEqual,i=!1;return function(e,r,a){var u=t(e,r,a);return i?o(u,n)||(n=u):(i=!0,n=u),n}}}(t):j(t,"mergeProps"):function(){return P}}var k={notify(){},get:function(){return[]}};function T(t,e){var r,n=k;function o(){a.onStateChange&&a.onStateChange()}function i(){r||(r=e?e.addNestedSub(o):t.subscribe(o),n=function(){var t=l(),e=null,r=null;return{clear(){e=null,r=null},notify(){t((function(){for(var t=e;t;)t.callback(),t=t.next}))},get(){for(var t=[],r=e;r;)t.push(r),r=r.next;return t},subscribe(t){var n=!0,o=r={callback:t,next:null,prev:r};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:r=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}var a={addNestedSub:function(t){return i(),n.subscribe(t)},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(r)},trySubscribe:i,tryUnsubscribe:function(){r&&(r(),r=void 0,n.clear(),n=k)},getListeners:function(){return n}};return a}var L="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect;function I(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}function R(t,e){if(I(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;var r=Object.keys(t),n=Object.keys(e);if(r.length!==n.length)return!1;for(var o=0;o<r.length;o++)if(!Object.prototype.hasOwnProperty.call(e,r[o])||!I(t[r[o]],e[r[o]]))return!1;return!0}function C(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||N(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(t,e){if(t){if("string"==typeof t)return D(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?D(t,e):void 0}}function D(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var M=["reactReduxForwardedRef"],U=function(){throw new Error("uSES not initialized!")},q=[null,null];function G(t,e,r){L((function(){return t.apply(void 0,function(t){if(Array.isArray(t))return D(t)}(r=e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||N(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var r}),r)}function B(t,e,r,n,o,i){t.current=n,r.current=!1,o.current&&(o.current=null,i())}function F(t,e,r,n,o,i,a,u,c,s,f){if(!t)return function(){};var l=!1,p=null,h=function(){if(!l&&u.current){var t,r,h=e.getState();try{t=n(h,o.current)}catch(t){r=t,p=t}r||(p=null),t===i.current?a.current||s():(i.current=t,c.current=t,a.current=!0,f())}};return r.onStateChange=h,r.trySubscribe(),h(),function(){if(l=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}function H(t,e){return t===e}var $,z=function(r,n,o){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=(i.pure,i.areStatesEqual),u=void 0===a?H:a,c=i.areOwnPropsEqual,s=void 0===c?R:c,f=i.areStatePropsEqual,l=void 0===f?R:f,y=i.areMergedPropsEqual,b=void 0===y?R:y,m=i.forwardRef,O=void 0!==m&&m,_=i.context,E=void 0===_?p:_,j=E,P=S(r),k=x(n),I=A(o),N=Boolean(r),D=function(r){var n=r.displayName||r.name||"Component",o="Connect(".concat(n,")"),i={shouldHandleStateChanges:N,displayName:o,wrappedComponentName:n,WrappedComponent:r,initMapStateToProps:P,initMapDispatchToProps:k,initMergeProps:I,areStatesEqual:u,areStatePropsEqual:l,areOwnPropsEqual:s,areMergedPropsEqual:b};function a(n){var o=(0,t.useMemo)((function(){var t=n.reactReduxForwardedRef,e=d(n,M);return[n.context,t,e]}),[n]),a=C(o,3),u=a[0],c=a[1],s=a[2],f=(0,t.useMemo)((function(){return u&&u.Consumer&&(0,g.isContextConsumer)(e().createElement(u.Consumer,null))?u:j}),[u,j]),l=(0,t.useContext)(f),p=Boolean(n.store)&&Boolean(n.store.getState)&&Boolean(n.store.dispatch),y=Boolean(l)&&Boolean(l.store),v=p?n.store:l.store,b=y?l.getServerState:v.getState,m=(0,t.useMemo)((function(){return w(v.dispatch,i)}),[v]),O=(0,t.useMemo)((function(){if(!N)return q;var t=T(v,p?void 0:l.subscription),e=t.notifyNestedSubs.bind(t);return[t,e]}),[v,p,l]),_=C(O,2),E=_[0],x=_[1],S=(0,t.useMemo)((function(){return p?l:h({},l,{subscription:E})}),[p,l,E]),P=(0,t.useRef)(),A=(0,t.useRef)(s),k=(0,t.useRef)(),I=(0,t.useRef)(!1),R=((0,t.useRef)(!1),(0,t.useRef)(!1)),D=(0,t.useRef)();L((function(){return R.current=!0,function(){R.current=!1}}),[]);var H,$=(0,t.useMemo)((function(){return function(){return k.current&&s===A.current?k.current:m(v.getState(),s)}}),[v,s]),z=(0,t.useMemo)((function(){return function(t){return E?F(N,v,E,m,A,P,I,R,k,x,t):function(){}}}),[E]);G(B,[A,P,I,s,k,x]);try{H=U(z,$,b?function(){return m(b(),s)}:$)}catch(t){throw D.current&&(t.message+="\nThe error may be correlated with this previous error:\n".concat(D.current.stack,"\n\n")),t}L((function(){D.current=void 0,k.current=void 0,P.current=H}));var J=(0,t.useMemo)((function(){return e().createElement(r,h({},H,{ref:c}))}),[c,r,H]);return(0,t.useMemo)((function(){return N?e().createElement(f.Provider,{value:S},J):J}),[f,J,S])}var c=e().memo(a);if(c.WrappedComponent=r,c.displayName=a.displayName=o,O){var f=e().forwardRef((function(t,r){return e().createElement(c,h({},t,{reactReduxForwardedRef:r}))}));return f.displayName=o,f.WrappedComponent=r,v()(f,r)}return v()(c,r)};return D},J=function(r){var n=r.store,o=r.context,i=r.children,a=r.serverState,u=(0,t.useMemo)((function(){var t=T(n);return{store:n,subscription:t,getServerState:a?function(){return a}:void 0}}),[n,a]),c=(0,t.useMemo)((function(){return n.getState()}),[n]);L((function(){var t=u.subscription;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==n.getState()&&t.notifyNestedSubs(),function(){t.tryUnsubscribe(),t.onStateChange=void 0}}),[u,c]);var s=o||p;return e().createElement(s.Provider,{value:u},i)};function Y(t,e){var r;e=e||[];var n=(t=t||[]).length,o=e.length,i=[];for(r=0;r<n;)i[i.length]=t[r],r+=1;for(r=0;r<o;)i[i.length]=e[r],r+=1;return i}function W(t){return null!=t&&"object"==typeof t&&!0===t["@@functional/placeholder"]}function K(t){return function e(r){return 0===arguments.length||W(r)?e:t.apply(this,arguments)}}function V(t,e){switch(t){case 0:return function(){return e.apply(this,arguments)};case 1:return function(t){return e.apply(this,arguments)};case 2:return function(t,r){return e.apply(this,arguments)};case 3:return function(t,r,n){return e.apply(this,arguments)};case 4:return function(t,r,n,o){return e.apply(this,arguments)};case 5:return function(t,r,n,o,i){return e.apply(this,arguments)};case 6:return function(t,r,n,o,i,a){return e.apply(this,arguments)};case 7:return function(t,r,n,o,i,a,u){return e.apply(this,arguments)};case 8:return function(t,r,n,o,i,a,u,c){return e.apply(this,arguments)};case 9:return function(t,r,n,o,i,a,u,c,s){return e.apply(this,arguments)};case 10:return function(t,r,n,o,i,a,u,c,s,f){return e.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}function Z(t){return function e(r,n){switch(arguments.length){case 0:return e;case 1:return W(r)?e:K((function(e){return t(r,e)}));default:return W(r)&&W(n)?e:W(r)?K((function(e){return t(e,n)})):W(n)?K((function(e){return t(r,e)})):t(r,n)}}}function Q(t,e,r){return function(){for(var n=[],o=0,i=t,a=0;a<e.length||o<arguments.length;){var u;a<e.length&&(!W(e[a])||o>=arguments.length)?u=e[a]:(u=arguments[o],o+=1),n[a]=u,W(u)||(i-=1),a+=1}return i<=0?r.apply(this,n):V(i,Q(t,n,r))}}s.useSyncExternalStoreWithSelector,function(t){U=t}(c.useSyncExternalStore),$=o.unstable_batchedUpdates,f=$;var X=Z((function(t,e){return 1===t?K(e):V(t,Q(t,[],e))})),tt=X,et=K((function(t){return tt(t.length,(function(){var e=0,r=arguments[0],n=arguments[arguments.length-1],o=Array.prototype.slice.call(arguments,0);return o[0]=function(){var t=r.apply(this,Y(arguments,[e,n]));return e+=1,t},t.apply(this,o)}))})),rt=et,nt=Array.isArray||function(t){return null!=t&&t.length>=0&&"[object Array]"===Object.prototype.toString.call(t)};function ot(t){return null!=t&&"function"==typeof t["@@transducer/step"]}function it(t,e,r){return function(){if(0===arguments.length)return r();var n=arguments[arguments.length-1];if(!nt(n)){for(var o=0;o<t.length;){if("function"==typeof n[t[o]])return n[t[o]].apply(n,Array.prototype.slice.call(arguments,0,-1));o+=1}if(ot(n)){var i=e.apply(null,Array.prototype.slice.call(arguments,0,-1));return i(n)}}return r.apply(this,arguments)}}function at(t){return t&&t["@@transducer/reduced"]?t:{"@@transducer/value":t,"@@transducer/reduced":!0}}var ut=function(){return this.xf["@@transducer/init"]()},ct=function(t){return this.xf["@@transducer/result"](t)},st=function(){function t(t,e){this.xf=e,this.f=t,this.all=!0}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=function(t){return this.all&&(t=this.xf["@@transducer/step"](t,!0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)||(this.all=!1,t=at(this.xf["@@transducer/step"](t,!1))),t},t}(),ft=Z(it(["all"],Z((function(t,e){return new st(t,e)})),(function(t,e){for(var r=0;r<e.length;){if(!t(e[r]))return!1;r+=1}return!0}))),lt=ft;function pt(t,e){for(var r=0,n=e.length,o=Array(n);r<n;)o[r]=t(e[r]),r+=1;return o}function ht(t){return"[object String]"===Object.prototype.toString.call(t)}var dt=K((function(t){return!!nt(t)||!!t&&"object"==typeof t&&!ht(t)&&(0===t.length||t.length>0&&t.hasOwnProperty(0)&&t.hasOwnProperty(t.length-1))})),yt=function(){function t(t){this.f=t}return t.prototype["@@transducer/init"]=function(){throw new Error("init not implemented on XWrap")},t.prototype["@@transducer/result"]=function(t){return t},t.prototype["@@transducer/step"]=function(t,e){return this.f(t,e)},t}(),vt=Z((function(t,e){return V(t.length,(function(){return t.apply(e,arguments)}))})),gt=vt;function bt(t,e,r){for(var n=r.next();!n.done;){if((e=t["@@transducer/step"](e,n.value))&&e["@@transducer/reduced"]){e=e["@@transducer/value"];break}n=r.next()}return t["@@transducer/result"](e)}function mt(t,e,r,n){return t["@@transducer/result"](r[n](gt(t["@@transducer/step"],t),e))}var wt="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";function Ot(t,e,r){if("function"==typeof t&&(t=function(t){return new yt(t)}(t)),dt(r))return function(t,e,r){for(var n=0,o=r.length;n<o;){if((e=t["@@transducer/step"](e,r[n]))&&e["@@transducer/reduced"]){e=e["@@transducer/value"];break}n+=1}return t["@@transducer/result"](e)}(t,e,r);if("function"==typeof r["fantasy-land/reduce"])return mt(t,e,r,"fantasy-land/reduce");if(null!=r[wt])return bt(t,e,r[wt]());if("function"==typeof r.next)return bt(t,e,r);if("function"==typeof r.reduce)return mt(t,e,r,"reduce");throw new TypeError("reduce: list must be array or iterable")}var _t=function(){function t(t,e){this.xf=e,this.f=t}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=ct,t.prototype["@@transducer/step"]=function(t,e){return this.xf["@@transducer/step"](t,this.f(e))},t}(),Et=Z((function(t,e){return new _t(t,e)}));function jt(t,e){return Object.prototype.hasOwnProperty.call(e,t)}var xt=Object.prototype.toString,St=function(){return"[object Arguments]"===xt.call(arguments)?function(t){return"[object Arguments]"===xt.call(t)}:function(t){return jt("callee",t)}}(),Pt=St,At=!{toString:null}.propertyIsEnumerable("toString"),kt=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],Tt=function(){return arguments.propertyIsEnumerable("length")}(),Lt=function(t,e){for(var r=0;r<t.length;){if(t[r]===e)return!0;r+=1}return!1},It="function"!=typeof Object.keys||Tt?K((function(t){if(Object(t)!==t)return[];var e,r,n=[],o=Tt&&Pt(t);for(e in t)!jt(e,t)||o&&"length"===e||(n[n.length]=e);if(At)for(r=kt.length-1;r>=0;)jt(e=kt[r],t)&&!Lt(n,e)&&(n[n.length]=e),r-=1;return n})):K((function(t){return Object(t)!==t?[]:Object.keys(t)})),Rt=Z(it(["fantasy-land/map","map"],Et,(function(t,e){switch(Object.prototype.toString.call(e)){case"[object Function]":return tt(e.length,(function(){return t.call(this,e.apply(this,arguments))}));case"[object Object]":return Ot((function(r,n){return r[n]=t(e[n]),r}),{},It(e));default:return pt(t,e)}}))),Ct=Rt,Nt=Number.isInteger||function(t){return t<<0===t},Dt=Z((function(t,e){var r=t<0?e.length+t:t;return ht(e)?e.charAt(r):e[r]})),Mt=Z((function(t,e){if(null!=e)return Nt(t)?Dt(t,e):e[t]})),Ut=Z((function(t,e){return Ct(Mt(t),e)}));function qt(t){return function e(r,n,o){switch(arguments.length){case 0:return e;case 1:return W(r)?e:Z((function(e,n){return t(r,e,n)}));case 2:return W(r)&&W(n)?e:W(r)?Z((function(e,r){return t(e,n,r)})):W(n)?Z((function(e,n){return t(r,e,n)})):K((function(e){return t(r,n,e)}));default:return W(r)&&W(n)&&W(o)?e:W(r)&&W(n)?Z((function(e,r){return t(e,r,o)})):W(r)&&W(o)?Z((function(e,r){return t(e,n,r)})):W(n)&&W(o)?Z((function(e,n){return t(r,e,n)})):W(r)?K((function(e){return t(e,n,o)})):W(n)?K((function(e){return t(r,e,o)})):W(o)?K((function(e){return t(r,n,e)})):t(r,n,o)}}}var Gt=qt(Ot),Bt=function(){function t(t,e){this.xf=e,this.f=t,this.any=!1}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=function(t){return this.any||(t=this.xf["@@transducer/step"](t,!1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)&&(this.any=!0,t=at(this.xf["@@transducer/step"](t,!0))),t},t}(),Ft=Z(it(["any"],Z((function(t,e){return new Bt(t,e)})),(function(t,e){for(var r=0;r<e.length;){if(t(e[r]))return!0;r+=1}return!1}))),Ht=Ft,$t=Z((function(t,e){return"function"==typeof e["fantasy-land/ap"]?e["fantasy-land/ap"](t):"function"==typeof t.ap?t.ap(e):"function"==typeof t?function(r){return t(r)(e(r))}:Ot((function(t,r){return Y(t,Ct(r,e))}),[],t)})),zt=Z((function(t,e){return Y(e,[t])})),Jt=K((function(t){for(var e=It(t),r=e.length,n=[],o=0;o<r;)n[o]=t[e[o]],o+=1;return n})),Yt=K((function(t){return null==t})),Wt=qt((function t(e,r,n){if(0===e.length)return r;var o=e[0];if(e.length>1){var i=!Yt(n)&&jt(o,n)?n[o]:Nt(e[1])?[]:{};r=t(Array.prototype.slice.call(e,1),r,i)}return function(t,e,r){if(Nt(t)&&nt(r)){var n=[].concat(r);return n[t]=e,n}var o={};for(var i in r)o[i]=r[i];return o[t]=e,o}(o,r,n)})),Kt=qt((function(t,e,r){return Wt([t],e,r)}));function Vt(t){return function e(r){for(var n,o,i,a=[],u=0,c=r.length;u<c;){if(dt(r[u]))for(i=0,o=(n=t?e(r[u]):r[u]).length;i<o;)a[a.length]=n[i],i+=1;else a[a.length]=r[u];u+=1}return a}}var Zt=K((function(t){return null===t?"Null":void 0===t?"Undefined":Object.prototype.toString.call(t).slice(8,-1)}));function Qt(t,e,r,n){var o,i=function(o){for(var i=e.length,a=0;a<i;){if(t===e[a])return r[a];a+=1}for(var u in e[a]=t,r[a]=o,t)t.hasOwnProperty(u)&&(o[u]=n?Qt(t[u],e,r,!0):t[u]);return o};switch(Zt(t)){case"Object":return i(Object.create(Object.getPrototypeOf(t)));case"Array":return i([]);case"Date":return new Date(t.valueOf());case"RegExp":return o=t,new RegExp(o.source,(o.global?"g":"")+(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.sticky?"y":"")+(o.unicode?"u":""));case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return t.slice();default:return t}}var Xt=K((function(t){return null!=t&&"function"==typeof t.clone?t.clone():Qt(t,[],[],!0)})),te=K((function(t){return function(e,r){return t(e,r)?-1:t(r,e)?1:0}}));function ee(t,e){return function(){var r=arguments.length;if(0===r)return e();var n=arguments[r-1];return nt(n)||"function"!=typeof n[t]?e.apply(this,arguments):n[t].apply(n,Array.prototype.slice.call(arguments,0,r-1))}}var re=qt(ee("slice",(function(t,e,r){return Array.prototype.slice.call(r,t,e)})));function ne(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e||"[object AsyncFunction]"===e||"[object GeneratorFunction]"===e||"[object AsyncGeneratorFunction]"===e}function oe(t){for(var e,r=[];!(e=t.next()).done;)r.push(e.value);return r}function ie(t,e,r){for(var n=0,o=r.length;n<o;){if(t(e,r[n]))return!0;n+=1}return!1}var ae="function"==typeof Object.is?Object.is:function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e};function ue(t,e,r,n){var o=oe(t);function i(t,e){return ce(t,e,r.slice(),n.slice())}return!ie((function(t,e){return!ie(i,e,t)}),oe(e),o)}function ce(t,e,r,n){if(ae(t,e))return!0;var o,i,a=Zt(t);if(a!==Zt(e))return!1;if("function"==typeof t["fantasy-land/equals"]||"function"==typeof e["fantasy-land/equals"])return"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e)&&"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t);if("function"==typeof t.equals||"function"==typeof e.equals)return"function"==typeof t.equals&&t.equals(e)&&"function"==typeof e.equals&&e.equals(t);switch(a){case"Arguments":case"Array":case"Object":if("function"==typeof t.constructor&&"Promise"===(o=t.constructor,null==(i=String(o).match(/^function (\w*)/))?"":i[1]))return t===e;break;case"Boolean":case"Number":case"String":if(typeof t!=typeof e||!ae(t.valueOf(),e.valueOf()))return!1;break;case"Date":if(!ae(t.valueOf(),e.valueOf()))return!1;break;case"Error":return t.name===e.name&&t.message===e.message;case"RegExp":if(t.source!==e.source||t.global!==e.global||t.ignoreCase!==e.ignoreCase||t.multiline!==e.multiline||t.sticky!==e.sticky||t.unicode!==e.unicode)return!1}for(var u=r.length-1;u>=0;){if(r[u]===t)return n[u]===e;u-=1}switch(a){case"Map":return t.size===e.size&&ue(t.entries(),e.entries(),r.concat([t]),n.concat([e]));case"Set":return t.size===e.size&&ue(t.values(),e.values(),r.concat([t]),n.concat([e]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var c=It(t);if(c.length!==It(e).length)return!1;var s=r.concat([t]),f=n.concat([e]);for(u=c.length-1;u>=0;){var l=c[u];if(!jt(l,e)||!ce(e[l],t[l],s,f))return!1;u-=1}return!0}var se=Z((function(t,e){return ce(t,e,[],[])}));function fe(t,e){return function(t,e,r){var n,o;if("function"==typeof t.indexOf)switch(typeof e){case"number":if(0===e){for(n=1/e;r<t.length;){if(0===(o=t[r])&&1/o===n)return r;r+=1}return-1}if(e!=e){for(;r<t.length;){if("number"==typeof(o=t[r])&&o!=o)return r;r+=1}return-1}return t.indexOf(e,r);case"string":case"boolean":case"function":case"undefined":return t.indexOf(e,r);case"object":if(null===e)return t.indexOf(e,r)}for(;r<t.length;){if(se(t[r],e))return r;r+=1}return-1}(e,t,0)>=0}function le(t){return'"'+t.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}var pe=function(t){return(t<10?"0":"")+t},he="function"==typeof Date.prototype.toISOString?function(t){return t.toISOString()}:function(t){return t.getUTCFullYear()+"-"+pe(t.getUTCMonth()+1)+"-"+pe(t.getUTCDate())+"T"+pe(t.getUTCHours())+":"+pe(t.getUTCMinutes())+":"+pe(t.getUTCSeconds())+"."+(t.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"};function de(t,e){for(var r=0,n=e.length,o=[];r<n;)t(e[r])&&(o[o.length]=e[r]),r+=1;return o}function ye(t){return"[object Object]"===Object.prototype.toString.call(t)}var ve=function(){function t(t,e){this.xf=e,this.f=t}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=ct,t.prototype["@@transducer/step"]=function(t,e){return this.f(e)?this.xf["@@transducer/step"](t,e):t},t}(),ge=Z(it(["fantasy-land/filter","filter"],Z((function(t,e){return new ve(t,e)})),(function(t,e){return ye(e)?Ot((function(r,n){return t(e[n])&&(r[n]=e[n]),r}),{},It(e)):de(t,e)}))),be=Z((function(t,e){return ge((r=t,function(){return!r.apply(this,arguments)}),e);var r})),me=be;function we(t,e){var r=function(r){var n=e.concat([t]);return fe(r,n)?"<Circular>":we(r,n)},n=function(t,e){return pt((function(e){return le(e)+": "+r(t[e])}),e.slice().sort())};switch(Object.prototype.toString.call(t)){case"[object Arguments]":return"(function() { return arguments; }("+pt(r,t).join(", ")+"))";case"[object Array]":return"["+pt(r,t).concat(n(t,me((function(t){return/^\d+$/.test(t)}),It(t)))).join(", ")+"]";case"[object Boolean]":return"object"==typeof t?"new Boolean("+r(t.valueOf())+")":t.toString();case"[object Date]":return"new Date("+(isNaN(t.valueOf())?r(NaN):le(he(t)))+")";case"[object Null]":return"null";case"[object Number]":return"object"==typeof t?"new Number("+r(t.valueOf())+")":1/t==-1/0?"-0":t.toString(10);case"[object String]":return"object"==typeof t?"new String("+r(t.valueOf())+")":le(t);case"[object Undefined]":return"undefined";default:if("function"==typeof t.toString){var o=t.toString();if("[object Object]"!==o)return o}return"{"+n(t,It(t)).join(", ")+"}"}}var Oe=K((function(t){return we(t,[])})),_e=Z((function(t,e){if(nt(t)){if(nt(e))return t.concat(e);throw new TypeError(Oe(e)+" is not an array")}if(ht(t)){if(ht(e))return t+e;throw new TypeError(Oe(e)+" is not a string")}if(null!=t&&ne(t["fantasy-land/concat"]))return t["fantasy-land/concat"](e);if(null!=t&&ne(t.concat))return t.concat(e);throw new TypeError(Oe(t)+' does not have a method named "concat" or "fantasy-land/concat"')})),Ee=Z((function(t,e){return e>t?e:t})),je=Z((function(t,e){return tt(Gt(Ee,0,Ut("length",e)),(function(){var r=arguments,n=this;return t.apply(n,pt((function(t){return t.apply(n,r)}),e))}))})),xe=je,Se=function(){function t(t,e,r,n){this.valueFn=t,this.valueAcc=e,this.keyFn=r,this.xf=n,this.inputs={}}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=function(t){var e;for(e in this.inputs)if(jt(e,this.inputs)&&(t=this.xf["@@transducer/step"](t,this.inputs[e]))["@@transducer/reduced"]){t=t["@@transducer/value"];break}return this.inputs=null,this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){var r=this.keyFn(e);return this.inputs[r]=this.inputs[r]||[r,this.valueAcc],this.inputs[r][1]=this.valueFn(this.inputs[r][1],e),t},t}(),Pe=Q(4,[],it([],Q(4,[],(function(t,e,r,n){return new Se(t,e,r,n)})),(function(t,e,r,n){return Ot((function(n,o){var i=r(o),a=t(jt(i,n)?n[i]:Qt(e,[],[],!1),o);return a&&a["@@transducer/reduced"]?at(n):(n[i]=a,n)}),{},n)})));function Ae(t,e,r){var n,o=typeof t;switch(o){case"string":case"number":return 0===t&&1/t==-1/0?!!r._items["-0"]||(e&&(r._items["-0"]=!0),!1):null!==r._nativeSet?e?(n=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===n):r._nativeSet.has(t):o in r._items?t in r._items[o]||(e&&(r._items[o][t]=!0),!1):(e&&(r._items[o]={},r._items[o][t]=!0),!1);case"boolean":if(o in r._items){var i=t?1:0;return!!r._items[o][i]||(e&&(r._items[o][i]=!0),!1)}return e&&(r._items[o]=t?[!1,!0]:[!0,!1]),!1;case"function":return null!==r._nativeSet?e?(n=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===n):r._nativeSet.has(t):o in r._items?!!fe(t,r._items[o])||(e&&r._items[o].push(t),!1):(e&&(r._items[o]=[t]),!1);case"undefined":return!!r._items[o]||(e&&(r._items[o]=!0),!1);case"object":if(null===t)return!!r._items.null||(e&&(r._items.null=!0),!1);default:return(o=Object.prototype.toString.call(t))in r._items?!!fe(t,r._items[o])||(e&&r._items[o].push(t),!1):(e&&(r._items[o]=[t]),!1)}}var ke=function(){function t(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}return t.prototype.add=function(t){return!Ae(t,!0,this)},t.prototype.has=function(t){return Ae(t,!1,this)},t}(),Te=Z((function(t,e){for(var r=[],n=0,o=t.length,i=e.length,a=new ke,u=0;u<i;u+=1)a.add(e[u]);for(;n<o;)a.add(t[n])&&(r[r.length]=t[n]),n+=1;return r})),Le=qt((function(t,e,r){var n=Array.prototype.slice.call(r,0);return n.splice(t,e),n})),Ie=Z((function t(e,r){if(null==r)return r;switch(e.length){case 0:return r;case 1:return function(t,e){if(null==e)return e;if(Nt(t)&&nt(e))return Le(t,1,e);var r={};for(var n in e)r[n]=e[n];return delete r[t],r}(e[0],r);default:var n=e[0],o=Array.prototype.slice.call(e,1);return null==r[n]?function(t,e){if(Nt(t)&&nt(e))return[].concat(e);var r={};for(var n in e)r[n]=e[n];return r}(n,r):Kt(n,t(o,r[n]),r)}})),Re=Z((function(t,e){return Ie([t],e)})),Ce=function(){function t(t,e){this.xf=e,this.n=t,this.i=0}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=ct,t.prototype["@@transducer/step"]=function(t,e){this.i+=1;var r=0===this.n?t:this.xf["@@transducer/step"](t,e);return this.n>=0&&this.i>=this.n?at(r):r},t}(),Ne=Z(it(["take"],Z((function(t,e){return new Ce(t,e)})),(function(t,e){return re(0,t<0?1/0:t,e)}))),De=K((function(t){return null!=t&&"function"==typeof t["fantasy-land/empty"]?t["fantasy-land/empty"]():null!=t&&null!=t.constructor&&"function"==typeof t.constructor["fantasy-land/empty"]?t.constructor["fantasy-land/empty"]():null!=t&&"function"==typeof t.empty?t.empty():null!=t&&null!=t.constructor&&"function"==typeof t.constructor.empty?t.constructor.empty():nt(t)?[]:ht(t)?"":ye(t)?{}:Pt(t)?function(){return arguments}():(e=t,"[object Uint8ClampedArray]"===(r=Object.prototype.toString.call(e))||"[object Int8Array]"===r||"[object Uint8Array]"===r||"[object Int16Array]"===r||"[object Uint16Array]"===r||"[object Int32Array]"===r||"[object Uint32Array]"===r||"[object Float32Array]"===r||"[object Float64Array]"===r||"[object BigInt64Array]"===r||"[object BigUint64Array]"===r?t.constructor.from(""):void 0);var e,r})),Me=De,Ue=Z((function t(e,r){if(!ye(r)&&!nt(r))return r;var n,o,i,a=r instanceof Array?[]:{};for(o in r)i=typeof(n=e[o]),a[o]="function"===i?n(r[o]):n&&"object"===i?t(n,r[o]):r[o];return a})),qe=function(){function t(t,e){this.xf=e,this.f=t,this.found=!1}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,void 0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)&&(this.found=!0,t=at(this.xf["@@transducer/step"](t,e))),t},t}(),Ge=Z(it(["find"],Z((function(t,e){return new qe(t,e)})),(function(t,e){for(var r=0,n=e.length;r<n;){if(t(e[r]))return e[r];r+=1}}))),Be=Ge,Fe=function(){function t(t,e){this.xf=e,this.f=t,this.idx=-1,this.found=!1}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,-1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.idx+=1,this.f(e)&&(this.found=!0,t=at(this.xf["@@transducer/step"](t,this.idx))),t},t}(),He=Z(it([],Z((function(t,e){return new Fe(t,e)})),(function(t,e){for(var r=0,n=e.length;r<n;){if(t(e[r]))return r;r+=1}return-1}))),$e=He,ze=K(Vt(!0)),Je=Z(ee("forEach",(function(t,e){for(var r=e.length,n=0;n<r;)t(e[n]),n+=1;return e}))),Ye=Je,We=Z((function(t,e){for(var r=It(e),n=0;n<r.length;){var o=r[n];t(e[o],o,e),n+=1}return e})),Ke=We,Ve=Z(ee("groupBy",Pe((function(t,e){return t.push(e),t}),[]))),Ze=Z((function(t,e){if(0===t.length||Yt(e))return!1;for(var r=e,n=0;n<t.length;){if(Yt(r)||!jt(t[n],r))return!1;r=r[t[n]],n+=1}return!0})),Qe=Z((function(t,e){return Ze([t],e)}));function Xe(t){return t}var tr=K(Xe),er=Z(fe),rr=K((function(t){return tt(t.length,(function(e,r){var n=Array.prototype.slice.call(arguments,0);return n[0]=r,n[1]=e,t.apply(this,n)}))})),nr=rr,or=function(){function t(t,e){this.xf=e,this.f=t,this.set=new ke}return t.prototype["@@transducer/init"]=ut,t.prototype["@@transducer/result"]=ct,t.prototype["@@transducer/step"]=function(t,e){return this.set.add(this.f(e))?this.xf["@@transducer/step"](t,e):t},t}(),ir=Z(it([],Z((function(t,e){return new or(t,e)})),(function(t,e){for(var r,n,o=new ke,i=[],a=0;a<e.length;)r=t(n=e[a]),o.add(r)&&i.push(n),a+=1;return i}))),ar=ir(tr),ur=Z((function(t,e){var r,n;return t.length>e.length?(r=t,n=e):(r=e,n=t),ar(de(nr(fe)(r),n))})),cr="function"==typeof Object.assign?Object.assign:function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),r=1,n=arguments.length;r<n;){var o=arguments[r];if(null!=o)for(var i in o)jt(i,o)&&(e[i]=o[i]);r+=1}return e},sr=K((function(t){return null!=t&&se(t,Me(t))})),fr=K((function(t){return xe((function(){return Array.prototype.slice.call(arguments,0)}),t)})),lr=fr,pr=Z((function(t,e){return function(r){return function(n){return Ct((function(t){return e(t,n)}),r(t(n)))}}})),hr=Z((function(t,e){return t.map((function(t){for(var r,n=e,o=0;o<t.length;){if(null==n)return;r=t[o],n=Nt(r)?Dt(r,n):n[r],o+=1}return n}))})),dr=Z((function(t,e){return hr([t],e)[0]})),yr=K((function(t){return pr(dr(t),Wt(t))})),vr=Z((function(t,e){return t<e})),gr=Z((function(t,e){var r={};return V(e.length,(function(){var n=t.apply(this,arguments);return jt(n,r)||(r[n]=e.apply(this,arguments)),r[n]}))})),br=gr,mr=qt((function(t,e,r){var n,o={};for(n in e)jt(n,e)&&(o[n]=jt(n,r)?t(n,e[n],r[n]):e[n]);for(n in r)jt(n,r)&&!jt(n,o)&&(o[n]=r[n]);return o})),wr=mr,Or=qt((function t(e,r,n){return wr((function(r,n,o){return ye(n)&&ye(o)?t(e,n,o):e(r,n,o)}),r,n)})),_r=Or,Er=Z((function(t,e){return _r((function(t,e,r){return r}),t,e)})),jr=Z((function(t,e){return cr({},e,t)})),xr=Z((function(t,e){return cr({},t,e)})),Sr=qt((function(t,e,r){return wr((function(e,r,n){return t(r,n)}),e,r)})),Pr=Sr,Ar=Z((function(t,e){for(var r={},n={},o=0,i=t.length;o<i;)n[t[o]]=1,o+=1;for(var a in e)n.hasOwnProperty(a)||(r[a]=e[a]);return r})),kr=K((function(t){var e,r=!1;return V(t.length,(function(){return r?e:(r=!0,e=t.apply(this,arguments))}))})),Tr=kr,Lr=lr([ge,me]),Ir=Z((function(t,e){return null==e||e!=e?t:e})),Rr=qt((function(t,e,r){return Ir(t,dr(e,r))})),Cr=Z((function(t,e){for(var r={},n=0;n<t.length;)t[n]in e&&(r[t[n]]=e[t[n]]),n+=1;return r})),Nr=Z((function(t,e){var r={};for(var n in e)t(e[n],n,e)&&(r[n]=e[n]);return r})),Dr=qt((function(t,e,r){return se(e,Mt(t,r))})),Mr=qt((function(t,e,r){return Ir(t,Mt(e,r))})),Ur=Z((function(t,e){return t.map((function(t){return dr([t],e)}))})),qr=K((function(t){return function(){return t}})),Gr=function t(e){return{value:e,map:function(r){return t(r(e))}}},Br=qt((function(t,e,r){return t((function(t){return Gr(e(t))}))(r).value})),Fr=qt((function(t,e,r){return Br(t,qr(e),r)})),Hr=Z((function(t,e){return Array.prototype.slice.call(e,0).sort(t)})),$r=Z((function(t,e){return se(Ne(t.length,e),t)})),zr=K((function(t){var e=[];for(var r in t)jt(r,t)&&(e[e.length]=[r,t[r]]);return e})),Jr=(String.prototype.trim,function(t){return{value:t,"fantasy-land/map":function(){return this}}}),Yr=Z((function(t,e){return t(Jr)(e).value})),Wr=Z((function(t,e){for(var r=[],n=0,o=Math.min(t.length,e.length);n<o;)r[n]=[t[n],e[n]],n+=1;return r})),Kr=Z((function(t,e){for(var r=0,n=Math.min(t.length,e.length),o={};r<n;)o[t[r]]=e[r],r+=1;return o}));function Vr(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Zr(Object(r),!0).forEach((function(e){Vr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xr(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var tn="function"==typeof Symbol&&Symbol.observable||"@@observable",en=function(){return Math.random().toString(36).substring(7).split("").join(".")},rn={INIT:"@@redux/INIT"+en(),REPLACE:"@@redux/REPLACE"+en(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+en()}};function nn(t){if("object"!=typeof t||null===t)return!1;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function on(t,e,r){var n;if("function"==typeof e&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(Xr(0));if("function"==typeof e&&void 0===r&&(r=e,e=void 0),void 0!==r){if("function"!=typeof r)throw new Error(Xr(1));return r(on)(t,e)}if("function"!=typeof t)throw new Error(Xr(2));var o=t,i=e,a=[],u=a,c=!1;function s(){u===a&&(u=a.slice())}function f(){if(c)throw new Error(Xr(3));return i}function l(t){if("function"!=typeof t)throw new Error(Xr(4));if(c)throw new Error(Xr(5));var e=!0;return s(),u.push(t),function(){if(e){if(c)throw new Error(Xr(6));e=!1,s();var r=u.indexOf(t);u.splice(r,1),a=null}}}function p(t){if(!nn(t))throw new Error(Xr(7));if(void 0===t.type)throw new Error(Xr(8));if(c)throw new Error(Xr(9));try{c=!0,i=o(i,t)}finally{c=!1}for(var e=a=u,r=0;r<e.length;r++)(0,e[r])();return t}function h(t){if("function"!=typeof t)throw new Error(Xr(10));o=t,p({type:rn.REPLACE})}function d(){var t,e=l;return t={subscribe:function(t){if("object"!=typeof t||null===t)throw new Error(Xr(11));function r(){t.next&&t.next(f())}return r(),{unsubscribe:e(r)}}},t[tn]=function(){return this},t}return p({type:rn.INIT}),(n={dispatch:p,subscribe:l,getState:f,replaceReducer:h})[tn]=d,n}function an(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return 0===e.length?function(t){return t}:1===e.length?e[0]:e.reduce((function(t,e){return function(){return t(e.apply(void 0,arguments))}}))}function un(t){return function(e){var r=e.dispatch,n=e.getState;return function(e){return function(o){return"function"==typeof o?o(r,n,t):e(o)}}}}var cn=un();cn.withExtraArgument=un;var sn=cn,fn=r(357),ln=r(924),pn=r.n(ln);function hn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function dn(t){return function(t){if(Array.isArray(t))return gn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||vn(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||vn(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vn(t,e){if(t){if("string"==typeof t)return gn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gn(t,e):void 0}}function gn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bn(t){var e=Qe("url_base_pathname",t),r=Qe("requests_pathname_prefix",t);if("Object"!==Zt(t)||!e&&!r)throw new Error('\n            Trying to make an API request but neither\n            "url_base_pathname" nor "requests_pathname_prefix"\n            is in `config`. `config` is: ',t);var n=r?t.requests_pathname_prefix:t.url_base_pathname;return"/"===n.charAt(n.length-1)?n:n+"/"}var mn=["props","children"],wn=function t(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;if(Array.isArray(e))e.forEach((function(e,i){o?t(dr(o,e),r,_e(n,_e([i],o))):t(e,r,zt(i,n))}));else if("Object"===Zt(e)){r(e,n);var i=dr(mn,e);if(i){var a=_e(n,mn);t(i,r,a)}var u=Rr([],[e.namespace,e.type],window.__dashprivate_childrenProps);u.forEach((function(o){if(o.includes("[]")){var i=yn(o.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),2),a=i[0],u=i[1],c=_e(["props"],a),s=_e(n,c);t(dr(c,e),r,s,u)}else{var f=_e(n,["props"].concat(dn(o.split("."))));t(dr(["props"].concat(dn(o.split("."))),e),r,f)}}))}},On=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._ev={}}var e,r;return e=t,r=[{key:"on",value:function(t,e){var r=this;return(this._ev[t]=this._ev[t]||[]).push(e),function(){return r.removeListener(t,e)}}},{key:"removeListener",value:function(t,e){var r=this._ev[t];if(r){var n=r.indexOf(e);n>-1&&r.splice(n,1)}}},{key:"emit",value:function(t){for(var e=this,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];var i=this._ev[t];i&&i.forEach((function(t){return t.apply(e,n)}))}},{key:"once",value:function(t,e){var r=this,n=this.on(t,(function(){n();for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];e.apply(r,o)}))}}],r&&hn(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function _n(t,e,r,n){var o=r||{strs:{},objs:{}},i=o.strs,a=o.objs,u=function(t){return e.some((function(e,r){return t[r]!==e}))},c=e.length,s=c?ge(u,i):{},f={};return c&&Ke((function(t,e){var r=ge((function(t){var e=t.path;return u(e)}),t);r.length&&(f[e]=r)}),a),wn(t,(function(t,r){var n=dr(["props","id"],t);if(n)if("object"==typeof n){var o=Object.keys(n).sort(),i=Ur(o,n),a=o.join(",");(f[a]=f[a]||[]).push({values:i,path:_e(e,r)})}else s[n]=_e(e,r)})),{strs:s,objs:f,events:n||r.events}}function En(t,e){if("object"==typeof e){var r=Object.keys(e).sort(),n=r.join(","),o=t.objs[n];if(!o)return!1;var i=Ur(r,e),a=Be(Dr("values",i),o);return a&&a.path}return t.strs[e]}var jn=function(t){var e=t.type,r=t.namespace,n=window[r];if(n){if(n[e])return n[e];throw new Error("Component ".concat(e," not found in ").concat(r))}throw new Error("".concat(r," was not found."))};function xn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Sn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xn(Object(r),!0).forEach((function(e){Pn(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Pn(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function An(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||kn(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kn(t,e){if(t){if("string"==typeof t)return Tn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Tn(t,e):void 0}}function Tn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ln=function(t){return t.startsWith("..")},In={wild:"ALL",multi:1},Rn={wild:"MATCH"},Cn={wild:"ALLSMALLER",multi:1,expand:1},Nn={ALL:In,MATCH:Rn,ALLSMALLER:Cn},Dn={Output:{ALL:In,MATCH:Rn},Input:Nn,State:Nn},Mn=["string","number","boolean"],Un=[".","{"];function qn(t){var e=t.lastIndexOf(".");return{id:Gn(t.substr(0,e)),property:t.substr(e+1)}}function Gn(t){return function(t){return t.startsWith("{")}(t)?function(t){return Ct((function(t){return Array.isArray(t)&&Nn[t[0]]||t}),JSON.parse(t))}(t):t}function Bn(t){return"object"!=typeof t?t:"{"+Object.keys(t).sort().map((function(e){return JSON.stringify(e)+":"+((r=t[e])&&r.wild||JSON.stringify(r));var r})).join(",")+"}"}function Fn(t,e){var r=pn()(e);if(pn()(t)){if(r){var n=Number(t),o=Number(e);return n>o?1:n<o?-1:0}return-1}if(r)return 1;var i="boolean"==typeof t;return i!==("boolean"==typeof e)?i?-1:1:t>e?1:t<e?-1:0}var Hn=function(t){return"string"==typeof t?t+"z":"z"};function $n(t,e,r,n){var o=t[e]=t[e]||{};(o[r]=o[r]||[]).push(n)}function zn(t,e,r,n){for(var o=Object.keys(e).sort(),i=o.join(","),a=Ur(o,e),u=t[i]=t[i]||{},c=u[r]=u[r]||[],s=!1,f=0;f<c.length;f++)if(se(a,c[f].values)){s=c[f];break}s||(s={keys:o,values:a,callbacks:[]},c.push(s)),s.callbacks.push(n)}var Jn=function(t){var e=An(t,2),r=e[0],n=e[1],o=r&&r.wild,i=n&&n.wild;return o&&i?!(r===Rn&&n===Cn||r===Cn&&n===Rn):r===n||o||i};function Yn(t,e){var r,n=t.id,o=t.property,i=It(n).sort(),a=Ur(i,n),u=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=kn(t))){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(e);try{for(u.s();!(r=u.n()).done;){var c=r.value,s=c.id;if(c.property===o&&"string"!=typeof s&&se(It(s).sort(),i)&&lt(Jn,Wr(a,Ur(i,s))))return c}}catch(t){u.e(t)}finally{u.f()}return!1}function Wn(t,e){var r=new fn.f,n={},o=Ct(Ue({id:Gn})),i=Ct((function(t){var e,r=t.output,n=Ue({inputs:o,state:o},t);return n.outputs=Ct((function(t){return Kt("out",!0,qn(t))}),Ln(r)?(e=r).substr(2,e.length-4).split("..."):[r]),n}),t),a=!1;!function(t,e){var r={},n=[];t.forEach((function(t){var o=t.inputs,i=t.outputs,a=t.state,u=!0;1!==i.length||i[0].id||i[0].property||(u=!1,e("A callback is missing Outputs",["Please provide an output for this callback:",JSON.stringify(t,null,2)]));var c="In the callback for output(s):\n  "+i.map(lo).join("\n  ");o.length||e("A callback is missing Inputs",[c,"there are no `Input` elements.","Without `Input` elements, it will never get called.","","Subscribing to `Input` components will cause the","callback to be called whenever their values change."]),[[i,"Output"],[o,"Input"],[a,"State"]].forEach((function(t){var r=An(t,2),n=r[0],o=r[1];("Output"!==o||u)&&(Array.isArray(n)||e("Callback ".concat(o,"(s) must be an Array"),[c,"For ".concat(o,"(s) we found:"),JSON.stringify(n),"but we expected an Array."]),n.forEach((function(t,r){!function(t,e,r,n,o){var i=t.id,a=t.property;if("string"==typeof a&&a||o("Callback property error",[e,"".concat(r,"[").concat(n,"].property = ").concat(JSON.stringify(a)),"but we expected `property` to be a non-empty string."]),"object"==typeof i)sr(i)&&o("Callback item missing ID",[e,"".concat(r,"[").concat(n,"].id = {}"),"Every item linked to a callback needs an ID"]),Ke((function(t,i){i||o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id has key "').concat(i,'"'),"Keys must be non-empty strings."]),"object"==typeof t&&t.wild?Dn[r][t.wild]!==t&&o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id["').concat(i,'"] = ').concat(t.wild),"Allowed wildcards for ".concat(r,"s are:"),It(Dn[r]).join(", ")]):er(typeof t,Mn)||o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id["').concat(i,'"] = ').concat(JSON.stringify(t)),"Wildcard callback ID values must be either wildcards","or constants of one of these types:",Mn.join(", ")])}),i);else if("string"==typeof i){i||o("Callback item missing ID",[e,"".concat(r,"[").concat(n,'].id = "').concat(i,'"'),"Every item linked to a callback needs an ID"]);var u=Un.filter((function(t){return er(t,i)}));u.length&&o("Callback invalid ID string",[e,"".concat(r,"[").concat(n,"].id = '").concat(i,"'"),"characters '".concat(u.join("', '"),"' are not allowed.")])}else o("Callback ID type error",[e,"".concat(r,"[").concat(n,"].id = ").concat(JSON.stringify(i)),"IDs must be strings or wildcard-compatible objects."])}(t,c,o,r,e)})))})),function(t,e,r,n,o){var i={},a=[];t.forEach((function(t,u){var c=t.id,s=t.property;if("string"==typeof c){var f=lo({id:c,property:s});i[f]?r("Duplicate callback Outputs",[e,"Output ".concat(u," (").concat(f,") is already used by this callback.")]):n[f]?r("Duplicate callback outputs",[e,"Output ".concat(u," (").concat(f,") is already in use."),"Any given output can only have one callback that sets it.","To resolve this situation, try combining these into","one callback function, distinguishing the trigger","by using `dash.callback_context` if necessary."]):i[f]=1}else{var l={id:c,property:s},p=Yn(l,a),h=p||Yn(l,o);if(p||h){var d=lo(l),y=lo(p||h);r("Overlapping wildcard callback outputs",[e,"Output ".concat(u," (").concat(d,")"),"overlaps another output (".concat(y,")"),"used in ".concat(p?"this":"a different"," callback.")])}else a.push(l)}})),It(i).forEach((function(t){n[t]=1})),a.forEach((function(t){o.push(t)}))}(i,c,e,r,n),function(t,e,r,n,o){var i=Kn(t[0].id).matchKeys;t.forEach((function(e,r){r&&!se(Kn(e.id).matchKeys,i)&&o("Mismatched `MATCH` wildcards across `Output`s",[n,"Output ".concat(r," (").concat(lo(e),")"),"does not have MATCH wildcards on the same keys as","Output 0 (".concat(lo(t[0]),")."),"MATCH wildcards must be on the same keys for all Outputs.","ALL wildcards need not match, only MATCH."])})),[[e,"Input"],[r,"State"]].forEach((function(e){var r=An(e,2),a=r[0],u=r[1];a.forEach((function(e,r){var a=Kn(e.id),c=a.matchKeys,s=a.allsmallerKeys,f=c.concat(s),l=Te(f,i);l.length&&(l.sort(),o("`Input` / `State` wildcards not in `Output`s",[n,"".concat(u," ").concat(r," (").concat(lo(e),")"),"has MATCH or ALLSMALLER on key(s) ".concat(l.join(", ")),"where Output 0 (".concat(lo(t[0]),")"),"does not have a MATCH wildcard. Inputs and State do not","need every MATCH from the Output(s), but they cannot have","extras beyond the Output(s)."]))}))}))}(i,o,a,c,e)}))}(i,(function(t,r){a=!0,e(t,r)}));var u={},c={},s={},f={},l={MultiGraph:r,outputMap:u,inputMap:c,outputPatterns:s,inputPatterns:f,callbacks:i};if(a)return l;function p(t,e){var r=[{}];return Ke((function(t,o){var i=n[o].vals,a=i.indexOf(e[o]),u=[t];t&&t.wild&&(u=t===Cn?a>0?i.slice(0,a):[]:-1===a||t===In?i:[e[o]]),r=$t($t([Kt(o)],u),r)}),t),r}i.forEach((function(t){var e=t.outputs,r=t.inputs;e.concat(r).forEach((function(t){var e=t.id;"object"==typeof e&&Ke((function(t,e){n[e]||(n[e]={exact:[],expand:0});var r=n[e];t&&t.wild?t.expand&&(r.expand+=1):-1===r.exact.indexOf(t)&&r.exact.push(t)}),e)}))})),Ke((function(t){var e,r=t.exact,n=t.expand,o=r.slice().sort(Fn);if(n)for(var i=0;i<n;i++)r.length?(o.splice(0,0,[(e=o[0],pn()(e)?e-1:0)]),o.push(Hn(o[o.length-1]))):o.push(i);else r.length||o.push(0);t.vals=o}),n);var h="__output",d=[],y=[],v=[];function g(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];r.addNode(t),r.addDependency(t,e),n&&(y[y.length-1].push(t),v[v.length-1].push(e))}return i.forEach((function(t){var e=t.outputs,n=t.inputs;function o(t,e){r.addNode(e),n.forEach((function(r){var n=r.id,o=r.property;"object"==typeof n?p(n,t).forEach((function(t){g(lo({id:t,property:o}),e)})):g(lo(r),e)}))}y.push([]),v.push([]);var i=Kn(e[0].id).matchKeys,a=$e((function(t){return!Qn(t.id)}),e),l=xr({matchKeys:i,firstSingleOutput:a,outputs:e},t);e.forEach((function(t){var e=t.id,r=t.property,i=function(t,e){var r=t.id,n=t.property;return e.some((function(e){var o=e.id,i=e.property;if(n!==i||typeof r!=typeof o)return!1;if("string"==typeof r){if(r===o)return!0}else if(Yn(e,[t]))return!0;return!1}))}(t,n);if("object"==typeof e)p(e,{}).forEach((function(t){var e={id:t,property:r},n=lo(e);i&&(d.push(e),n+=h),o(t,n)})),zn(s,e,r,l);else{var a=lo(t);i&&(d.push(t),a+=h),o({},a),$n(u,e,r,l)}})),n.forEach((function(t){var e=t.id,r=t.property;"object"==typeof e?zn(f,e,r,l):$n(c,e,r,l)}))})),d.forEach((function(t){for(var e=lo(t),r=e.concat(h),n=0;n<y.length;n++)y[n].some((function(t){return t===e}))&&(v[n].some((function(t){return t===r}))||v[n].forEach((function(t){g(r,t,!1)})))})),l}function Kn(t){var e=[],r=[];return"object"==typeof t&&(Ke((function(t,n){t===Rn?e.push(n):t===Cn&&r.push(n)}),t),e.sort(),r.sort()),{matchKeys:e,allsmallerKeys:r}}function Vn(t,e,r,n,o,i){for(var a=0;a<t.length;a++){var u=e[a],c=r[a];if(c.wild){if(n&&c!==In){var s=n.indexOf(t[a]),f=i[s];if(c===Cn&&f===Cn)throw new Error("invalid wildcard id pair: "+JSON.stringify({keys:t,patternVals:r,vals:e,refKeys:n,refPatternVals:i,refVals:o}));if(Fn(u,o[s])!==(c===Cn?-1:f===Cn?1:0))return!1}}else if(u!==c)return!1}return!0}function Zn(t,e){for(var r=[],n=0;n<t.length;n++)t[n]===Rn&&r.push(e[n]);return r.length?JSON.stringify(r):""}function Qn(t){var e=t.id;return"object"==typeof e&&Ht((function(t){return t.multi}),Jt(e))}function Xn(t,e,r,n){var o,i,a="";if("string"==typeof r){var u=(t.outputMap[r]||{})[n];u&&(i=u[0],o=Oo())}else{var c=Object.keys(r).sort(),s=Ur(c,r),f=c.join(","),l=(t.outputPatterns[f]||{})[n];if(l)for(var p=0;p<l.length;p++){var h=l[p].values;if(Vn(c,s,h)){i=l[p].callbacks[0],o=Oo(c,s,h),a=Zn(h,s);break}}}return!!o&&mo(i,o,a)}function to(t,e,r,n){var o=Object.keys(e.id).sort(),i=Ur(o,e.id),a={};r.forEach((function(e){var r=e.id,u=Ur(o,r),c=mo(t,Oo(o,u,i),Zn(i,u)),s=c.resolvedId;a[s]||(n.push(c),a[s]=!0)}))}function eo(t,e,r){return function(n){var o=n.matchKeys,i=n.firstSingleOutput,a=n.outputs;if(o.length){var u=a[i];if(u)to(n,u,t(e)(u),r);else{var c={};a.forEach((function(i){var a=t(e)(i).filter((function(t){var e=JSON.stringify(Ur(o,t.id));return!c[e]&&(c[e]=1,!0)}));to(n,i,a,r)}))}}else{var s=mo(n,t,"");ze(s.getOutputs(e)).length&&r.push(s)}}}function ro(t){return function(t){if(Array.isArray(t))return co(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||uo(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function no(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function oo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?no(Object(r),!0).forEach((function(e){io(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):no(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function io(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ao(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||uo(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uo(t,e){if(t){if("string"==typeof t)return co(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?co(t,e):void 0}}function co(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var so=1,fo=Pr(Math.max),lo=function(t){var e=t.id,r=t.property;return"".concat(Bn(e),".").concat(r)};function po(t,e,r,n,o){var i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],a=[],u=lo({id:r,property:n});if("string"==typeof r){var c=(t.inputMap[r]||{})[n];if(!c)return[];c.forEach(eo(Oo(),e,a))}else{var s=Object.keys(r).sort(),f=Ur(s,r),l=s.join(","),p=(t.inputPatterns[l]||{})[n];if(!p)return[];p.forEach((function(t){Vn(s,f,t.values)&&t.callbacks.forEach(eo(Oo(s,f,t.values),e,a))}))}return a.forEach((function(r){r.changedPropIds[u]=o||2,i&&(r.priority=ho(t,e,r))})),a}function ho(t,e,r){for(var n=[r],o={},i={},a=[];n.length;){n=ge((function(t){var e=i[t.resolvedId];return i[t.resolvedId]=!0,e}),n);var u=ge((function(t){return!o[lo(t)]}),ze(Ct((function(t){return ze(t.getOutputs(e))}),n)));u.forEach((function(t){return o[lo(t)]=!0})),(n=ze(Ct((function(r){var n=r.id,o=r.property;return po(t,e,n,o,so,!1)}),u))).length&&a.push(n.length)}return a.unshift(a.length),Ct((function(t){return Math.min(t,35).toString(36)}),a).join("")}var yo=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;if(!e.length)return[];var n=Ct(lo,Gt((function(e,r){return _e(e,ze(r.getOutputs(t)))}),[],r)),o={};return Ye((function(t){return o[t]=!0}),n),ge((function(e){return lt((function(t){return!o[lo(t)]}),Te(ze(e.getInputs(t)),ze(e.getOutputs(t))))}),e)},vo=function(t,e,r,n){for(var o=[],i=function(t,e,r,n){var o=n.outputsOnly,i=n.removedArrayInputsOnly,a=n.newPaths,u=n.chunkPath,c={},s=[];function f(t){if(t){var e=c[t.resolvedId];if(void 0!==e){var r=s[e];r.changedPropIds=fo(r.changedPropIds,t.changedPropIds),t.initialCall&&(r.initialCall=!0)}else c[t.resolvedId]=s.length,s.push(t)}}function l(r,n,c){if(n)for(var s in n){var l=Xn(t,0,r,s);l&&(l.callback.prevent_initial_call||(l.initialCall=!0,f(l)))}if(!o&&c){var p=i?(y=Bn(r),function(t){return t.getInputs(e).some((function(e){return!(!Array.isArray(e)||!e.some((function(t){return Bn(t.id)===y}))||(ze(t.getOutputs(a)).length&&(t.initialCall=!0,t.changedPropIds={},f(t)),0))}))}):f,h=p;for(var d in u&&(h=function(t){lt($r(u),Ut("path",ze(t.getOutputs(e))))||p(t)}),c)po(t,e,r,d,so).forEach(h)}var y}return wn(r,(function(e){var r=dr(["props","id"],e);if(r)if("string"!=typeof r||i){var n=Object.keys(r).sort().join(",");l(r,!i&&t.outputPatterns[n],t.inputPatterns[n])}else l(r,t.outputMap[r],t.inputMap[r])})),Ct((function(r){return Sn(Sn({},r),{},{priority:ho(t,e,r)})}),s)}(t,e,r,n);;){var a=ao(Lr((function(t){var r=t.callback.inputs,n=t.getInputs;return lt(Qn,r)||!sr(Te(Ct(lo,ze(n(e))),o))}),i),2),u=a[0],c=a[1];if(!c.length)break;i=u,o=_e(o,Ct(lo,ze(Ct((function(t){return(0,t.getOutputs)(e)}),c))))}var s=Math.random().toString(16);return Ct((function(t){return oo(oo({},t),{},{executionGroup:s})}),i)},go=function(t){var e=t.anyVals,r=t.callback,n=r.inputs,o=r.outputs,i=r.state;return _e(Ct(lo,[].concat(ro(n),ro(o),ro(i))),Array.isArray(e)?e:""===e?[]:[e]).join(",")};function bo(t,e,r,n){return ze(Ct((function(e){return po(r,n,t,e)}),It(e)))}var mo=function(t,e,r){return{callback:t,anyVals:r,resolvedId:t.output+r,getOutputs:function(r){return t.outputs.map(e(r))},getInputs:function(r){return t.inputs.map(e(r))},getState:function(r){return t.state.map(e(r))},changedPropIds:{},initialCall:!1}};function wo(t,e){var r=ao(Lr((function(t){var r=t.getOutputs,n=t.callback.outputs;return ze(r(e)).length===n.length}),t),2)[1],n=ao(Lr((function(t){var r=t.getOutputs;return!ze(r(e)).length}),r),2)[1];return{added:Ct((function(t){return Kt("changedPropIds",Nr((function(t,r){return En(e,qn(r).id)}),t.changedPropIds),t)}),n),removed:r}}function Oo(t,e,r){return function(n){return function(o){var i=o.id,a=o.property;if("string"==typeof i){var u=En(n,i);return u?[{id:i,property:a,path:u}]:[]}var c=Object.keys(i).sort(),s=Ur(c,i),f=c.join(","),l=n.objs[f];if(!l)return[];var p=[];return l.forEach((function(n){var o=n.values,i=n.path;Vn(c,o,s,t,e,r)&&p.push({id:Kr(c,o),property:a,path:i})})),p}}}var _o={ON_PROP_CHANGE:1,SET_REQUEST_QUEUE:1,SET_GRAPHS:1,SET_PATHS:1,SET_LAYOUT:1,SET_APP_LIFECYCLE:1,SET_CONFIG:1,ADD_HTTP_HEADERS:1,ON_ERROR:1,SET_HOOKS:1},Eo=function(t){if(_o[t])return t;throw new Error("".concat(t," is not defined."))};function jo(t){var e={STARTED:"STARTED",HYDRATED:"HYDRATED",DESTROYED:"DESTROYED"};if(e[t])return e[t];throw new Error("".concat(t," is not a valid app state."))}var xo,So,Po=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:jo("STARTED"),e=arguments.length>1?arguments[1]:void 0;return e.type===Eo("SET_APP_LIFECYCLE")?jo(e.payload):t};function Ao(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ko(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ao(Object(r),!0).forEach((function(e){To(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ao(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function To(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(t){t.AddBlocked="Callbacks.AddBlocked",t.AddExecuted="Callbacks.AddExecuted",t.AddExecuting="Callbacks.AddExecuting",t.AddPrioritized="Callbacks.AddPrioritized",t.AddRequested="Callbacks.AddRequested",t.AddStored="Callbacks.AddStored",t.AddWatched="Callbacks.AddWatched",t.RemoveBlocked="Callbacks.RemoveBlocked",t.RemoveExecuted="Callbacks.RemoveExecuted",t.RemoveExecuting="Callbacks.RemoveExecuting",t.RemovePrioritized="Callbacks.RemovePrioritized",t.RemoveRequested="Callbacks.RemoveRequested",t.RemoveStored="Callbacks.RemoveStored",t.RemoveWatched="Callbacks.RemoveWatched"}(xo||(xo={})),function(t){t.AddCompleted="Callbacks.Completed",t.Aggregate="Callbacks.Aggregate"}(So||(So={}));var Lo={blocked:[],executed:[],executing:[],prioritized:[],requested:[],stored:[],watched:[],completed:0},Io={[xo.AddBlocked]:_e,[xo.AddExecuted]:_e,[xo.AddExecuting]:_e,[xo.AddPrioritized]:_e,[xo.AddRequested]:_e,[xo.AddStored]:_e,[xo.AddWatched]:_e,[xo.RemoveBlocked]:Te,[xo.RemoveExecuted]:Te,[xo.RemoveExecuting]:Te,[xo.RemovePrioritized]:Te,[xo.RemoveRequested]:Te,[xo.RemoveStored]:Te,[xo.RemoveWatched]:Te},Ro={[xo.AddBlocked]:"blocked",[xo.AddExecuted]:"executed",[xo.AddExecuting]:"executing",[xo.AddPrioritized]:"prioritized",[xo.AddRequested]:"requested",[xo.AddStored]:"stored",[xo.AddWatched]:"watched",[xo.RemoveBlocked]:"blocked",[xo.RemoveExecuted]:"executed",[xo.RemoveExecuting]:"executing",[xo.RemovePrioritized]:"prioritized",[xo.RemoveRequested]:"requested",[xo.RemoveStored]:"stored",[xo.RemoveWatched]:"watched"},Co=function(t,e){return ko(ko({},t),{},{completed:t.completed+e.payload})},No=function(t,e){var r=Io[e.type],n=Ro[e.type];return r&&n&&0!==e.payload.length?ko(ko({},t),{},{[n]:r(t[n],e.payload)}):t},Do=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Lo,e=arguments.length>1?arguments[1]:void 0;return Gt((function(t,e){return null===e?t:e.type===So.AddCompleted?Co(t,e):No(t,e)}),t,e.type===So.Aggregate?e.payload:[e])};function Mo(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1?arguments[1]:void 0;return e.type===Eo("SET_CONFIG")?(window.__dashprivate_childrenProps=Er(window.__dashprivate_childrenProps||{},e.payload.children_props),e.payload):e.type===Eo("ADD_HTTP_HEADERS")?Er(t,{fetch:{headers:e.payload}}):t}var Uo={},qo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Uo,e=arguments.length>1?arguments[1]:void 0;return"SET_GRAPHS"===e.type?e.payload:t};function Go(t){return function(t){if(Array.isArray(t))return Bo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Bo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Bo(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Fo={frontEnd:[],backEnd:[],backEndConnected:!0};function Ho(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Fo,e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"ON_ERROR":var r=t.frontEnd,n=t.backEnd,o=t.backEndConnected;return console.error(e.payload.error),"frontEnd"===e.payload.type?{frontEnd:[xr(e.payload,{timestamp:new Date})].concat(Go(r)),backEnd:n,backEndConnected:o}:"backEnd"===e.payload.type?{frontEnd:r,backEnd:[xr(e.payload,{timestamp:new Date})].concat(Go(n)),backEndConnected:o}:t;case"SET_CONNECTION_STATUS":return xr(t,{backEndConnected:e.payload});default:return t}}function $o(t){return function(t){if(Array.isArray(t))return zo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return zo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?zo(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Jo={past:[],present:{},future:[]},Yo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Jo,e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"UNDO":var r=t.past,n=t.present,o=t.future,i=r[r.length-1],a=r.slice(0,r.length-1);return{past:a,present:i,future:[n].concat($o(o))};case"REDO":var u=t.past,c=t.present,s=t.future,f=s[0],l=s.slice(1);return{past:[].concat($o(u),[c]),present:f,future:l};case"REVERT":var p=t.past,h=t.future,d=p[p.length-1],y=p.slice(0,p.length-1);return{past:y,present:d,future:$o(h)};default:return t}},Wo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null,bear:!1},e=arguments.length>1?arguments[1]:void 0;return"SET_HOOKS"===e.type?e.payload:t},Ko="JWT Expired",Vo=200,Zo=204,Qo="CLIENTSIDE_ERROR",Xo={[Vo]:"SUCCESS",[Zo]:"NO_UPDATE"},ti=["__dash_client","__dash_server","__dash_upload","__dash_download"];function ei(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var ri,ni={count:0,total:0,compute:0,network:{time:0,upload:0,download:0},resources:{},status:{latest:null},result:{}},oi={updated:[],resources:{},callbacks:{},graphLayout:null},ii=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:oi,e=arguments.length>1?arguments[1]:void 0;if("UPDATE_RESOURCE_USAGE"===e.type){var r=e.payload,n=r.id,o=r.usage,i=r.status,a=Xo[i]||i,u={updated:[n],resources:t.resources,callbacks:t.callbacks,graphLayout:t.graphLayout};u.callbacks[n]=u.callbacks[n]||Xt(ni);var c=u.callbacks[n],s=c.resources,f=u.resources;if(c.count+=1,c.status.latest=a,c.status[a]=(c.status[a]||0)+1,c.result=e.payload.result,c.inputs=e.payload.inputs,c.state=e.payload.state,o){var l=o.__dash_client,p=o.__dash_server,h=o.__dash_upload,d=o.__dash_download,y=ei(o,ti);for(var v in c.total+=l,c.compute+=p,c.network.time+=l-p,c.network.upload+=h,c.network.download+=d,y)y.hasOwnProperty(v)&&(s[v]=(s[v]||0)+y[v],f[v]=(f[v]||0)+y[v])}return u}return t},ai={id:null,props:{}},ui=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ai;return t};!function(t){t.Set="IsLoading.Set"}(ri||(ri={}));var ci,si=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=arguments.length>1?arguments[1]:void 0;return e.type===ri.Set?e.payload:t},fi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(e.type===Eo("SET_LAYOUT"))return e.payload;if(er(e.type,["UNDO_PROP_CHANGE","REDO_PROP_CHANGE",Eo("ON_PROP_CHANGE")])){var r=zt("props",e.payload.itempath),n=Yr(yr(r),t),o=xr(n,e.payload.props);return Wt(r,o,t)}return t};!function(t){t.Set="LoadingMap.Set"}(ci||(ci={}));var li={},pi=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:li,e=arguments.length>1?arguments[1]:void 0;return e.type===ci.Set?e.payload:t},hi={strs:{},objs:{}},di=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hi,e=arguments.length>1?arguments[1]:void 0;return e.type===Eo("SET_PATHS")?e.payload:t},yi=function(t,e){return Kt(t.jobId,t,e)},vi=function(t,e){return Re(t,e)},gi=function(t,e){return Wt([t,"outdated"],!0,e)};function bi(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"ADD_CALLBACK_JOB":return yi(e.payload,t);case"REMOVE_CALLBACK_JOB":return vi(e.payload.jobId,t);case"CALLBACK_JOB_OUTDATED":return gi(e.payload.jobId,t);default:return t}}function mi(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var wi=["dependenciesRequest","layoutRequest","reloadRequest","loginRequest"];function Oi(){var t={appLifecycle:Po,callbacks:Do,config:Mo,error:Ho,graphs:qo,history:Yo,hooks:Wo,profile:ii,changed:ui,isLoading:si,layout:fi,loadingMap:pi,paths:di};return Ye((function(e){var r;t[e]=(r=e,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=t;if(e.type===r){var o=e.payload,i=o.id,a={status:o.status,content:o.content};n=Array.isArray(i)?Wt(i,a,t):i?Kt(i,a,t):xr(t,a)}return n})}),wi),t.callbackJobs=bi,function(t){for(var e=Object.keys(t),r={},n=0;n<e.length;n++){var o=e[n];"function"==typeof t[o]&&(r[o]=t[o])}var i,a=Object.keys(r);try{!function(t){Object.keys(t).forEach((function(e){var r=t[e];if(void 0===r(void 0,{type:rn.INIT}))throw new Error(Xr(12));if(void 0===r(void 0,{type:rn.PROBE_UNKNOWN_ACTION()}))throw new Error(Xr(13))}))}(r)}catch(t){i=t}return function(t,e){if(void 0===t&&(t={}),i)throw i;for(var n=!1,o={},u=0;u<a.length;u++){var c=a[u],s=r[c],f=t[c],l=s(f,e);if(void 0===l)throw e&&e.type,new Error(Xr(14));o[c]=l,n=n||l!==f}return(n=n||a.length!==Object.keys(t).length)?o:t}}(t)}function _i(t,e,r){var n,o=e.graphs,i=e.paths,a=e.layout,u=t.itempath,c=t.props,s=dr(u.concat(["props"]),a)||{},f=s.id;return f&&(r&&(e.changed={id:f,props:c}),n={id:f,props:{}},It(c).forEach((function(t){po(o,i,f,t).length&&(n.props[t]=s[t])}))),n}function Ei(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function ji(t,e,r){return e&&Ei(t.prototype,e),r&&Ei(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function xi(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Si=ji((function t(e){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),xi(this,"_store",void 0),xi(this,"_unsubscribe",void 0),xi(this,"_observers",[]),xi(this,"observe",(function(t,e){if("function"==typeof t){if(!Array.isArray(e))throw new Error("inputs must be an array");return r.add(t,e),function(){return r.remove(t)}}return r.add(t.observer,t.inputs),function(){return r.remove(t.observer)}})),xi(this,"setStore",(function(t){r.__finalize__(),r.__init__(t)})),xi(this,"__finalize__",(function(){var t;return null===(t=r._unsubscribe)||void 0===t?void 0:t.call(r)})),xi(this,"__init__",(function(t){r._store=t,t&&(r._unsubscribe=t.subscribe(r.notify)),Ye((function(t){return t.lastState=null}),r._observers)})),xi(this,"add",(function(t,e){return r._observers.push({inputPaths:Ct((function(t){return t.split(".")}),e),lastState:null,observer:t,triggered:!1})})),xi(this,"notify",(function(){var t=r._store;if(t){var e=t.getState(),n=ge((function(t){return!t.triggered&&Ht((function(r){return dr(r,e)!==dr(r,t.lastState)}),t.inputPaths)}),r._observers);Ye((function(t){return t.triggered=!0}),n),Ye((function(e){e.lastState=t.getState(),e.observer(t),e.triggered=!1}),n)}})),xi(this,"remove",(function(t){return r._observers.splice(r._observers.findIndex((function(e){return t===e.observer}),r._observers),1)})),this.__init__(e)})),Pi=function(t){var e=t(),r=e.config,n=e.isLoading,o=null==r?void 0:r.update_title;o&&(n?document.title!==o&&(Ai.title=document.title,document.title=o):document.title===o?document.title=Ai.title:Ai.title=document.title)},Ai={inputs:["isLoading"],mutationObserver:void 0,observer:function(t){var e=t.getState,r=e().config;if(Ai.config!==r){var n;Ai.config=r,null===(n=Ai.mutationObserver)||void 0===n||n.disconnect(),Ai.mutationObserver=new MutationObserver((function(){return Pi(e)}));var o=document.querySelector("title");o&&Ai.mutationObserver.observe(o,{subtree:!0,childList:!0,attributes:!0,characterData:!0})}Pi(e)}},ki=Ai,Ti=r(143),Li=r.n(Ti),Ii=function(t){return"function"==typeof t},Ri=function(t){return t},Ci=function(t){return null===t};function Ni(t,e,r){void 0===e&&(e=Ri),Li()(Ii(e)||Ci(e),"Expected payloadCreator to be a function, undefined or null");var n=Ci(e)||e===Ri?Ri:function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return t instanceof Error?t:e.apply(void 0,[t].concat(n))},o=Ii(r),i=t.toString(),a=function(){var e=n.apply(void 0,arguments),i={type:t};return e instanceof Error&&(i.error=!0),void 0!==e&&(i.payload=e),o&&(i.meta=r.apply(void 0,arguments)),i};return a.toString=function(){return i},a}var Di=r(489);function Mi(){Mi=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function Ui(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var qi=Ni(Eo("ON_ERROR")),Gi=Ni(Eo("SET_APP_LIFECYCLE")),Bi=Ni(Eo("SET_CONFIG")),Fi=Ni(Eo("ADD_HTTP_HEADERS")),Hi=Ni(Eo("SET_GRAPHS")),$i=Ni(Eo("SET_HOOKS")),zi=Ni(Eo("SET_LAYOUT")),Ji=Ni(Eo("SET_PATHS")),Yi=(Ni(Eo("SET_REQUEST_QUEUE")),Ni(Eo("ON_PROP_CHANGE"))),Wi=function(t){return function(e,r){return t(qi({type:"backEnd",error:{message:e,html:r.join("\n")}}))}};var Ki=Tr(console.warn);function Vi(){try{return{"X-CSRFToken":Di.parse(document.cookie)._csrf_token}}catch(t){return Ki(t),{}}}var Zi=ta("REDO"),Qi=ta("UNDO"),Xi=ta("REVERT");function ta(t){return function(e,r){var n=r(),o=n.history,i=n.paths;e(Ni(t)());var a=("REDO"===t?o.future[0]:o.past[o.past.length-1])||{},u=a.id,c=a.props;u&&(e(Ni("UNDO_PROP_CHANGE")({itempath:En(i,u),props:c})),e(ea({id:u,props:c})))}}function ea(t){var e=t.id,r=t.props;return function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Ui(i,n,o,a,u,"next",t)}function u(t){Ui(i,n,o,a,u,"throw",t)}a(void 0)}))}}(Mi().mark((function t(n,o){var i,a,u;return Mi().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=o(),a=i.graphs,u=i.paths,n(ba(bo(e,r,a,u)));case 2:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}()}function ra(t,e,r){if(t&&"function"==typeof t.text)t.text().then((function(t){r(qi({type:"backEnd",error:{message:e,html:t}}))}));else{var n=t instanceof Error?t:{message:e,html:t};r(qi({type:"backEnd",error:n}))}}function na(t){return function(t){if(Array.isArray(t))return pa(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||la(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oa(){oa=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function ia(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function aa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ia(Object(r),!0).forEach((function(e){ua(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ia(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ua(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ca(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function sa(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ca(i,n,o,a,u,"next",t)}function u(t){ca(i,n,o,a,u,"throw",t)}a(void 0)}))}}function fa(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||la(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function la(t,e){if(t){if("string"==typeof t)return pa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?pa(t,e):void 0}}function pa(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var ha=Ni(xo.AddBlocked),da=Ni(So.AddCompleted),ya=Ni(xo.AddExecuted),va=Ni(xo.AddExecuting),ga=Ni(xo.AddPrioritized),ba=Ni(xo.AddRequested),ma=Ni(xo.AddStored),wa=Ni(xo.AddWatched),Oa=Ni(xo.RemoveExecuted),_a=Ni(xo.RemoveBlocked),Ea=Ni(xo.RemoveExecuting),ja=Ni(xo.RemovePrioritized),xa=Ni(xo.RemoveRequested),Sa=Ni(xo.RemoveStored),Pa=Ni(xo.RemoveWatched),Aa=Ni(So.Aggregate),ka=Ni("UPDATE_RESOURCE_USAGE"),Ta=Ni("ADD_CALLBACK_JOB"),La=Ni("REMOVE_CALLBACK_JOB"),Ia=Ni("CALLBACK_JOB_OUTDATED");function Ra(t,e,r,n,o){var i="";if(Qn(r))return[e,i];if(1!==e.length)if(e.length)i="Multiple objects were found for an `"+o+"` of a callback that only takes one value. The id spec is "+JSON.stringify(r.id)+(n?" with MATCH values "+n:"")+" and the property is `"+r.property+"`. The objects we found are: "+JSON.stringify(Ct(Cr(["id","property"]),e));else{var a="string"==typeof r.id;i="A nonexistent object was used in an `"+o+"` of a Dash callback. The id of this object is "+(a?"`"+r.id+"`":JSON.stringify(r.id)+(n?" with MATCH values "+n:""))+" and the property is `"+r.property+(a?"`. The string ids in the current layout are: ["+It(t.strs).join(", ")+"]":"`. The wildcard ids currently available are logged above.")}return[e[0],i]}function Ca(t,e,r,n,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a="Input"===o?r.getInputs:r.getState,u=[],c=0,s=a(t).map((function(i,a){var s=fa(Ra(t,i.map((function(t){var r=t.id,n=t.property,o=t.path;return{id:r,property:n,value:dr(o,e).props[n]}})),n[a],r.anyVals,o),2),f=s[0],l=s[1];return Qn(n[a])&&!f.length&&c++,l&&u.push(l),f}));if(u.length){if(i&&u.length+c===s.length)return null;Na(u,t)}return s}function Na(t,e){var r=t[0];throw-1!==r.indexOf("logged above")&&console.error(e.objs),new ReferenceError(r)}var Da=function(t){return Array.isArray(t)?Ut("value",t):t.value},Ma=function(t,e){return Array.isArray(t)?Wr(t,e):[[t,e]]};function Ua(t,e,r,n){return qa.apply(this,arguments)}function qa(){return qa=sa(oa().mark((function t(e,r,n,o){var i,a,u,c,s,f,l,p,h,d,y,v,g,b,m,w,O;return oa().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if((i=window.dash_clientside=window.dash_clientside||{}).no_update||(Object.defineProperty(i,"no_update",{value:{description:"Return to prevent updating an Output."},writable:!1}),Object.defineProperty(i,"PreventUpdate",{value:{description:"Throw to prevent updating all Outputs."},writable:!1})),a=o.inputs,u=o.outputs,c=o.state,s=Date.now(),f=Fa(a),l=Fa(c),p={},h=Vo,t.prev=8,v=r.namespace,g=r.function_name,b=a.map(Da),c&&(b=_e(b,c.map(Da))),i.callback_context={},i.callback_context.triggered=o.changedPropIds.map((function(t){return{prop_id:t,value:f[t]}})),i.callback_context.inputs_list=a,i.callback_context.inputs=f,i.callback_context.states_list=c,i.callback_context.states=l,m=(d=i[v])[g].apply(d,na(b)),delete i.callback_context,"function"!=typeof(null===(y=m)||void 0===y?void 0:y.then)){t.next=24;break}return t.next=23,m;case 23:m=t.sent;case 24:Ma(u,m).forEach((function(t){var e=fa(t,2),r=e[0],n=e[1];Ma(r,n).forEach((function(t){var e=fa(t,2),r=e[0],n=e[1],o=r.id,a=r.property,u=Bn(o),c=p[u]=p[u]||{};n!==i.no_update&&(c[a]=n)}))})),t.next=35;break;case 27:if(t.prev=27,t.t0=t.catch(8),t.t0!==i.PreventUpdate){t.next=33;break}h=Zo,t.next=35;break;case 33:throw h=Qo,t.t0;case 35:return t.prev=35,delete i.callback_context,w=Date.now()-s,O={__dash_server:w,__dash_client:w,__dash_upload:0,__dash_download:0},n.ui&&e(ka({id:o.output,usage:O,status:h,result:p,inputs:a,state:c})),t.finish(35);case 41:return t.abrupt("return",p);case 42:case"end":return t.stop()}}),t,null,[[8,27,35,41]])}))),qa.apply(this,arguments)}function Ga(t,e,r){zr(t).forEach((function(t){var n=fa(t,2),o=n[0],i=n[1],a=fa(o.split("."),2),u=a[0],c=a[1],s=r.strs[u];e(Yi({props:{[c]:i},itempath:s})),e(ea({id:u,props:{[c]:i}}))}))}function Ba(t,e,r,n,o,i,a,u,c){e.request_pre&&e.request_pre(n);var s,f,l,p,h=Date.now(),d=JSON.stringify(n),y=a;return new Promise((function(a,v){var g=function(y){var g=y.status;if(f){var b=u().callbackJobs[f];if(null!=b&&b.outdated)return t(La({jobId:f})),a({})}function w(e){if(r.ui){var o={__dash_server:0,__dash_client:Date.now()-h,__dash_upload:d.length,__dash_download:Number(y.headers.get("Content-Length"))};(y.headers.get("Server-Timing")||"").split(",").forEach((function(t){var e=t.split(";")[0],r=t.match(/;dur=[0-9.]+/);r&&(o[e]=Number(r[0].slice(5)))})),t(ka({id:n.output,usage:o,status:g,result:e,inputs:n.inputs,state:n.state}))}}var O=function(){f&&t(La({jobId:f})),l&&Ga(l,t,o),p&&Ga(p,t,o)};g===Vo?y.json().then((function(r){if(!s&&r.cacheKey&&(s=r.cacheKey),!f&&r.job){var u={jobId:r.job,cacheKey:r.cacheKey,cancelInputs:r.cancel,progressDefault:r.progressDefault,output:c};t(Ta(u)),f=r.job}r.progress&&Ga(r.progress,t,o),r.running&&Ga(r.running,t,o),!l&&r.runningOff&&(l=r.runningOff),!p&&r.progressDefault&&(p=r.progressDefault),i&&void 0===r.response?setTimeout(m,void 0!==i.interval?i.interval:500):(O(),function(t){var r,o=t.multi,i=t.response;if(e.request_post&&e.request_post(n,i),o)r=i;else{var u=n.output;r={[u.substr(0,u.lastIndexOf("."))]:i.props}}w(r),a(r)}(r))})):g===Zo?(O(),w({}),a({})):(O(),v(y))},b=function(){r.ui&&t(ka({id:n.output,status:"NO_RESPONSE",result:{},inputs:n.inputs,state:n.state})),v(new Error("Callback failed: the server did not respond."))},m=function(){var t,e,n;(t=Vi(),e="".concat(bn(r),"_dash-update-component"),n=function(t,r){var n="?";e.includes("?")&&(n="&"),e="".concat(e).concat(n).concat(t,"=").concat(r)},s&&n("cacheKey",s),f&&n("job",f),y&&(y.forEach((function(t){var e=fa(t,2),r=e[0],o=e[1];return n(r,o)})),y=y.filter((function(t){var e=fa(t,3);return e[0],e[1],!e[2]}))),fetch(e,Er(r.fetch,{method:"POST",headers:t,body:d}))).then(g,b)};m()}))}function Fa(t){if(!t)return{};for(var e={},r=0;r<t.length;r++){var n;if(Array.isArray(t[r]))for(var o=t[r],i=0;i<o.length;i++){var a;e["".concat(Bn(o[i].id),".").concat(o[i].property)]=null!==(a=o[i].value)&&void 0!==a?a:null}else e["".concat(Bn(t[r].id),".").concat(t[r].property)]=null!==(n=t[r].value)&&void 0!==n?n:null}return e}function Ha(t,e,r,n,o,i,a,u){var c=i.allOutputs,s=t.callback,f=s.output,l=s.inputs,p=s.state,h=s.clientside_function,d=s.long;try{var y=Ca(n,o,t,l,"Input",!0);if(null===y)return aa(aa({},t),{},{executionPromise:null});var v=[],g=[];if(c.forEach((function(e,r){var o=fa(Ra(n,Ct(Cr(["id","property"]),e),t.callback.outputs[r],t.anyVals,"Output"),2),i=o[0],a=o[1];v.push(i),a&&g.push(a)})),g.length)return ze(y).length&&Na(g,n),aa(aa({},t),{},{executionPromise:null});var b=function(){var i=sa(oa().mark((function i(){var c,s,l,g,b,m,w,O,_,E;return oa().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(i.prev=0,c={output:f,outputs:Ln(f)?v:v[0],inputs:y,changedPropIds:It(t.changedPropIds),state:t.callback.state.length?Ca(n,o,t,p,"State"):void 0},!h){i.next=13;break}return i.prev=3,i.next=6,Ua(a,h,e,c);case 6:return s=i.sent,i.abrupt("return",{data:s,payload:c});case 10:return i.prev=10,i.t0=i.catch(3),i.abrupt("return",{error:i.t0,payload:c});case 13:l=e,g=null,m=[],Jt(u().callbackJobs).forEach((function(e){t.callback.output===e.output&&(m.push(["oldJob",e.jobId,!0]),a(Ia({jobId:e.jobId}))),e.cancelInputs&&ur(e.cancelInputs,t.callback.inputs).length&&(m.push(["cancelJob",e.jobId]),e.progressDefault&&Ga(e.progressDefault,a,n))})),w=0;case 18:if(!(w<=1)){i.next=49;break}return i.prev=19,i.next=22,Ba(a,r,l,c,n,d,m.length?m:void 0,u,t.callback.output);case 22:return O=i.sent,g&&a(Fi(g)),i.abrupt("return",{data:O,payload:c});case 27:if(i.prev=27,i.t1=i.catch(19),b=i.t1,!(w<=1)||401!==i.t1.status&&400!==i.t1.status){i.next=45;break}return i.next=33,i.t1.text();case 33:if(!i.sent.includes(Ko)){i.next=45;break}if(null===r.request_refresh_jwt){i.next=45;break}return _=null,e.fetch.headers.Authorization&&(_=e.fetch.headers.Authorization.substr("Bearer ".length)),i.next=40,r.request_refresh_jwt(_);case 40:if(!(E=i.sent)){i.next=45;break}return g={Authorization:"Bearer ".concat(E)},l=Er(e,{fetch:{headers:g}}),i.abrupt("continue",46);case 45:return i.abrupt("break",49);case 46:w++,i.next=18;break;case 49:return i.abrupt("return",{error:b,payload:null});case 52:return i.prev=52,i.t2=i.catch(0),i.abrupt("return",{error:i.t2,payload:null});case 55:case"end":return i.stop()}}),i,null,[[0,52],[3,10],[19,27]])})));return function(){return i.apply(this,arguments)}}();return aa(aa({},t),{},{executionPromise:b()})}catch(e){return aa(aa({},t),{},{executionPromise:{error:e,payload:null}})}}function $a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||function(t,e){if(t){if("string"==typeof t)return za(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?za(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function za(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ja(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ya(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Wa(t,e,r){return e&&Ya(t.prototype,e),r&&Ya(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var Ka="_dash_persistence.";function Va(t){var e="string"==typeof t?new Error(t):t;return Ni("ON_ERROR")({type:"frontEnd",error:e})}function Za(t,e){var r=t+e,n=r.length;return function(e){return e===t||e.substr(0,n)===r}}var Qa=function(t){return"U"===t?void 0:JSON.parse(t||null)},Xa=function(t){return void 0===t?"U":JSON.stringify(t)},tu=function(){function t(e){Ja(this,t),this._name=e,this._storage=window[e]}return Wa(t,[{key:"hasItem",value:function(t){return null!==this._storage.getItem(Ka+t)}},{key:"getItem",value:function(t){return Qa(this._storage.getItem(Ka+t))}},{key:"_setItem",value:function(t,e){this._storage.setItem(Ka+t,Xa(e))}},{key:"setItem",value:function(t,e,r){try{this._setItem(t,e)}catch(e){r(Va("".concat(t," failed to save in ").concat(this._name,". Persisted props may be lost.")))}}},{key:"removeItem",value:function(t){this._storage.removeItem(Ka+t)}},{key:"clear",value:function(t){for(var e=this,r=Za(Ka+(t||""),t?".":""),n=[],o=0;o<this._storage.length;o++){var i=this._storage.key(o);r(i)&&n.push(i)}Ye((function(t){return e._storage.removeItem(t)}),n)}}]),t}(),eu={memory:new(function(){function t(){Ja(this,t),this._data={}}return Wa(t,[{key:"hasItem",value:function(t){return t in this._data}},{key:"getItem",value:function(t){return Qa(this._data[t])}},{key:"setItem",value:function(t,e){this._data[t]=Xa(e)}},{key:"removeItem",value:function(t){delete this._data[t]}},{key:"clear",value:function(t){var e=this;t?Ye((function(t){return delete e._data[t]}),ge(Za(t,"."),It(this._data))):this._data={}}}]),t}())},ru={local:"localStorage",session:"sessionStorage"};function nu(t,e){return eu[t]||(eu[t]=function(t,e){var r=new tu(t),n=eu.memory,o=function(){for(var t="Spam",e=2;e<16;e++)t+=t;return t}(),i=Ka+"x.x";try{return r._setItem(i,o),r.getItem(i)!==o?(e(Va("".concat(t," init failed set/get, falling back to memory"))),n):(r.removeItem(i),r)}catch(r){e(Va("".concat(t," init first try failed; clearing and retrying")))}try{if(r.clear(),r._setItem(i,o),r.getItem(i)!==o)throw new Error("nope");return r.removeItem(i),e(Va("".concat(t," init set/get succeeded after clearing!"))),r}catch(r){return e(Va("".concat(t," init still failed, falling back to memory"))),n}}(ru[t],e)),eu[t]}var ou={extract:function(t){return t},apply:function(t,e){return t}},iu=function(t,e,r){return t.persistenceTransforms&&t.persistenceTransforms[e]?r?t.persistenceTransforms[e][r]:t.persistenceTransforms[e]:ou},au=function(t,e,r){return"".concat(Bn(t),".").concat(e,".").concat(JSON.stringify(r))},uu=function(t){var e=t.props,r=t.type,n=t.namespace;if(!r||!n)return{props:e};var o=e.id,i=e.persistence,a=jn(t),u=function(t){return e[t]||(a.defaultProps||{})[t]},c=u("persisted_props"),s=u("persistence_type");return{canPersist:o&&c&&s,id:o,props:e,element:a,persistence:i,persisted_props:c,persistence_type:s}};function cu(t,e){return"Object"===Zt(t)&&t.props?fu(t,t,[],e):t}function su(t,e,r,n,o,i,a){if(e.hasItem(t)){var u=$a(e.getItem(t),2),c=u[0],s=u[1],f=a?c:s,l=a?s:c,p=$a(o.split("."),2),h=p[0],d=p[1],y=iu(r,h,d);se(f,y.extract(n[h]))?i[h]=y.apply(l,h in i?i[h]:n[h]):e.removeItem(t)}}function fu(t,e,r,n){var o=uu(e),i=o.canPersist,a=o.id,u=o.props,c=o.element,s=o.persistence,f=o.persisted_props,l=o.persistence_type,p=t;if(i&&s){var h=nu(l,n),d={};for(var y in Ye((function(t){return su(au(a,t,s),h,c,u,t,d)}),f),d)p=Fr(yr(r.concat("props",y)),d[y],p)}var v=u.children;return Array.isArray(v)?v.forEach((function(t,e){"Object"===Zt(t)&&t.props&&(p=fu(p,t,r.concat("props","children",e),n))})):"Object"===Zt(v)&&v.props&&(p=fu(p,v,r.concat("props","children"),n)),p}function lu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function pu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lu(Object(r),!0).forEach((function(e){hu(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lu(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function hu(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function du(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||function(t,e){if(t){if("string"==typeof t)return yu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?yu(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var vu={observer:function(t){var e=t.dispatch,r=t.getState,n=r().callbacks.executed;var o=[],i=[];Ye((function(t){var n,a=_e(null!==(n=t.predecessors)&&void 0!==n?n:[],[t.callback]),u=t.callback,c=u.clientside_function,s=u.output,f=t.executionResult;if(!Yt(f)){var l=f.data,p=f.error,h=f.payload;if(void 0!==l&&(Ye((function(t){var n=du(t,2),i=n[0],u=n[1],c=Gn(i),s=r(),f=s.graphs,l=s.layout,p=s.paths,h=function(t,n){var o=r(),i=o.layout,a=En(o.paths,t);if(!a)return!1;n=function(t,e,r){var n=uu(t),o=n.canPersist,i=n.id,a=n.props,u=n.persistence,c=n.persisted_props,s=n.persistence_type,f=n.element,l=function(t,r){return t in e?e[t]:r},p=l("persistence",u);if(!o||!u&&!p)return e;var h=l("persistence_type",s),d=l("persisted_props",c),y=p!==u||h!==s||d!==c,v=function(t){return!(t.split(".")[0]in e)},g={},b=a;if(y&&u){var m=nu(s,r);Ye((function(t){return su(au(i,t,u),m,f,a,t,g,!0)}),ge(v,c)),b=xr(a,g)}if(p){var w=nu(h,r);y&&Ye((function(t){return su(au(i,t,p),w,f,b,t,g)}),ge(v,d));var O=f.persistenceTransforms||{};for(var _ in e){var E=O[_];if(E)for(var j in E)w.removeItem(au(i,"".concat(_,".").concat(j),p));else w.removeItem(au(i,_,p))}}return y?xr(e,g):e}(dr(a,i),n,e);var u=cu({props:n},e).props;return e(Yi({itempath:a,props:u,source:"response"})),u}(c,u);if(o=_e(o,ze(Ct((function(t){return po(f,p,c,t,!0)}),It(u))).map((function(t){return pu(pu({},t),{},{predecessors:a})}))),Qe("children",h)){var d=h.children,y=_e(En(p,c),["props","children"]),v=dr(y,l),g=_n(d,y,p);e(Ji(g)),o=_e(o,vo(f,g,d,{chunkPath:y}).map((function(t){return pu(pu({},t),{},{predecessors:a})}))),o=_e(o,vo(f,p,v,{removedArrayInputsOnly:!0,newPaths:g,chunkPath:y}).map((function(t){return pu(pu({},t),{},{predecessors:a})})))}var b=Nr((function(t,e){return!(e in u)}),h);if(!sr(b)){var m=r(),w=m.graphs,O=m.paths;o=_e(o,bo(i,b,w,O).map((function(t){return pu(pu({},t),{},{predecessors:a})})))}}),Object.entries(l)),i.push(pu(pu({},t),{},{executionMeta:{allProps:Ct(lo,ze(t.getOutputs(r().paths))),updatedProps:ze(Ct((function(t){var e=du(t,2),r=e[0],n=e[1];return Ct((function(t){return lo({id:r,property:t})}),It(n))}),zr(l)))}}))),void 0!==p){var d=h?Ct(lo,ze([h.outputs])).join(", "):s,y="Callback error updating ".concat(d);if(c){var v=c.namespace,g=c.function_name;y+=" via clientside function ".concat(v,".").concat(g)}ra(p,y,e),i.push(pu(pu({},t),{},{executionMeta:{allProps:Ct(lo,ze(t.getOutputs(r().paths))),updatedProps:[]}}))}}}),n),e(Aa([n.length?Oa(n):null,n.length?da(n.length):null,i.length?ma(i):null,o.length?ba(o):null]))},inputs:["callbacks.executed"]},gu=vu;function bu(){bu=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function mu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function wu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?mu(Object(r),!0).forEach((function(e){Ou(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mu(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ou(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _u(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function Eu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var ju={observer:function(t){var e,r,n=t.dispatch,o=t.getState,i=o().callbacks.executing,a=(e=Lr((function(t){return t.executionPromise instanceof Promise}),i),r=2,function(t){if(Array.isArray(t))return t}(e)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,r)||function(t,e){if(t){if("string"==typeof t)return Eu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Eu(t,e):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),u=a[0],c=a[1];n(Aa([i.length?Ea(i):null,u.length?wa(u):null,c.length?ya(c.map((function(t){return Kt("executionResult",t.executionPromise,t)}))):null])),Ye(function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){_u(i,n,o,a,u,"next",t)}function u(t){_u(i,n,o,a,u,"throw",t)}a(void 0)}))}}(bu().mark((function t(e){var r,i,a,u,c,s;return bu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.executionPromise;case 2:if(r=t.sent,i=o(),a=i.callbacks.watched,u=i.appLifecycle,c=i.hooks.callback_resolved,u===jo("HYDRATED")){t.next=6;break}return t.abrupt("return");case 6:if(c&&c(e.callback,r),s=Be((function(t){return t===e||t.executionPromise===e.executionPromise}),a)){t.next=10;break}return t.abrupt("return");case 10:n(Aa([Pa([s]),ya([wu(wu({},s),{},{executionResult:r})])]));case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),u)},inputs:["callbacks.executing"]},xu=ju;function Su(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Pu=function(t){var e,r;return(e=Array()).concat.apply(e,function(t){if(Array.isArray(t))return Su(t)}(r=Jt(Ar(["stored","completed"],t)))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(t,e){if(t){if("string"==typeof t)return Su(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Su(t,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())},Au=Ni(ri.Set),ku={observer:function(t){var e=t.dispatch,r=(0,t.getState)(),n=r.callbacks,o=r.isLoading,i=Pu(n),a=Boolean(i.length);o!==a&&e(Au(a))},inputs:["callbacks"]},Tu=ku,Lu=Ni(ci.Set);function Iu(t){return function(t){if(Array.isArray(t))return Ru(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Ru(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ru(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ru(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Cu={observer:function(t){var e=t.dispatch,r=(0,t.getState)(),n=r.callbacks,o=n.executing,i=n.watched,a=n.executed,u=r.loadingMap,c=r.paths,s=ze(Ct((function(t){return t.getOutputs(c)}),[].concat(Iu(o),Iu(i),Iu(a)))),f=sr(s)?null:Gt((function(t,e){var r=e.id,n=e.property,o=e.path,i=t,a={id:r,property:n};return i.__dashprivate__idprops__=i.__dashprivate__idprops__||[],i.__dashprivate__idprops__.push(a),o.forEach((function(t,e){var r;(i=i[t]=null!==(r=i[t])&&void 0!==r?r:"children"===t&&"number"==typeof o[e+1]?[]:{}).__dashprivate__idprops__=i.__dashprivate__idprops__||[],i.__dashprivate__idprops__.push(a)})),i.__dashprivate__idprop__=i.__dashprivate__idprop__||a,t}),{},s);se(f,u)||e(Lu(f))},inputs:["callbacks.executing","callbacks.watched","callbacks.executed"]},Nu=Cu,Du=r(800),Mu=function(t,e,r){if(!r.length)return!0;var n=[],o=e.events,i=new Promise((function(t){o.once("rendered",t)}));return r.forEach((function(r){var o=En(e,r);if(o){var a=dr(o,t);if(a){var u=jn(a),c=(0,Du.isReady)(u);c&&"function"==typeof c.then&&n.push(Promise.race([c,i.then((function(){return document.getElementById(Bn(r))&&c}))]))}}})),!n.length||Promise.all(n)};function Uu(){Uu=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function qu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Gu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qu(Object(r),!0).forEach((function(e){Bu(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qu(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Bu(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fu(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function Hu(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Fu(i,n,o,a,u,"next",t)}function u(t){Fu(i,n,o,a,u,"throw",t)}a(void 0)}))}}function $u(t){return function(t){if(Array.isArray(t))return Ju(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||zu(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zu(t,e){if(t){if("string"==typeof t)return Ju(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ju(t,e):void 0}}function Ju(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Yu=function(t,e){var r,n;return(null!==(r=t.priority)&&void 0!==r?r:"")>(null!==(n=e.priority)&&void 0!==n?n:"")?-1:1},Wu=function(t,e){var r=(0,t.getOutputs)(e),n=ze(r),o=[],i={};return n.forEach((function(t){var e=t.id,r=t.property,n=Bn(e);(i[n]=i[n]||[]).push(r),o.push(lo({id:n,property:r}))})),{allOutputs:r,allPropIds:o}},Ku=function(t,e){return ar(Ut("id",[].concat($u(ze(t.getInputs(e))),$u(ze(t.getState(e))))))},Vu={observer:function(){var t=Hu(Uu().mark((function t(e){var r,n,o,i,a,u,c,s,f,l,p,h,d,y,v,g,b,m,w,O,_;return Uu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.dispatch,n=e.getState,o=n(),i=o.callbacks,a=i.executing,u=i.watched,c=o.config,s=o.hooks,f=o.layout,l=o.paths,p=o.appLifecycle,h=n(),d=h.callbacks.prioritized,p===jo("HYDRATED")){t.next=5;break}return t.abrupt("return");case 5:y=Math.max(0,12-a.length-u.length),d=Hr(Yu,d),v=Lr((function(t){return!0===Mu(f,l,Ku(t,l))}),d),j=2,g=function(t){if(Array.isArray(t))return t}(E=v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(E,j)||zu(E,j)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=g[0],m=g[1],w=b.slice(0,y),O=m.slice(0,y-w.length),w.length&&r(Aa([ja(w),va(Ct((function(t){return Ha(t,c,s,l,f,Wu(t,l),r,n)}),w))])),O.length&&(_=Ct((function(t){return Gu(Gu(Gu({},t),Wu(t,l)),{},{isReady:Mu(f,l,Ku(t,l))})}),O),r(Aa([ja(O),ha(_)])),Ye(function(){var t=Hu(Uu().mark((function t(e){var o,i,a;return Uu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.isReady;case 2:if(o=n(),i=o.callbacks.blocked,Be((function(t){return t===e||t.isReady===e.isReady}),i)){t.next=6;break}return t.abrupt("return");case 6:a=Ha(e,c,s,l,f,e,r,n),r(Aa([_a([e]),va([a])]));case 8:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),_));case 12:case"end":return t.stop()}var E,j}),t)})));return function(e){return t.apply(this,arguments)}}(),inputs:["callbacks.prioritized","callbacks.completed"]},Zu=Vu;function Qu(){Qu=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function Xu(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var tc=function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Xu(i,n,o,a,u,"next",t)}function u(t){Xu(i,n,o,a,u,"throw",t)}a(void 0)}))}}(Qu().mark((function t(e){var r,n;return Qu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=new Promise((function(t){return r=t})),setTimeout(r,e),t.abrupt("return",n);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();function ec(){ec=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function rc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function nc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rc(Object(r),!0).forEach((function(e){oc(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function oc(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ic(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var ac={observer:function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ic(i,n,o,a,u,"next",t)}function u(t){ic(i,n,o,a,u,"throw",t)}a(void 0)}))}}(ec().mark((function t(e){var r,n,o,i,a,u,c,s,f,l,p,h,d,y,v,g,b,m,w,O,_,E,j,x,S,P,A,k,T,L,I,R,C,N,D,M,U,q,G,B,F,H,$,z,J,Y;return ec().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.dispatch,n=e.getState,t.next=3,tc(0);case 3:if(o=n(),i=o.callbacks,a=o.callbacks,u=a.prioritized,c=a.blocked,s=a.executing,f=a.watched,l=a.stored,p=o.paths,h=n(),d=h.callbacks.requested,y=d.slice(0),v=Pu(i),g=ge((function(t){var e;return er(t.callback,null!==(e=t.predecessors)&&void 0!==e?e:[])}),d),d=Te(d,g),b=[],m=[],Ye((function(t){if(1===t.length)m.push(t[0]);else{var e=t.find((function(t){return t.initialCall}));e&&b.push(e);var r=t.filter((function(t){return t!==e}));1===r.length?m.push(r[0]):(b=_e(b,r),m.push(jr({changedPropIds:Gt(Pr(Math.max),{},Ut("changedPropIds",r)),executionGroup:ge((function(t){return Boolean(t)}),Ut("executionGroup",r)).slice(-1)[0]},r.slice(-1)[0])))}}),Jt(Ve(go,d))),w=ze(Ct((function(t){return t.slice(0,-1)}),Jt(Ve(go,_e(u,d=m))))),O=ze(Ct((function(t){return t.slice(0,-1)}),Jt(Ve(go,_e(c,d))))),_=ze(Ct((function(t){return t.slice(0,-1)}),Jt(Ve(go,_e(s,d))))),E=ze(Ct((function(t){return t.slice(0,-1)}),Jt(Ve(go,_e(f,d))))),j=wo(d,p),x=j.added,S=j.removed,P=wo(u,p),A=P.added,k=P.removed,T=wo(c,p),L=T.added,I=T.removed,R=wo(s,p),C=R.added,N=R.removed,D=wo(f,p),M=D.added,U=D.removed,d=_e(Te(d,S),x),q=yo(p,d,v),G=[],B=[],!q.length&&d.length&&d.length===v.length)for(F=d.slice(0),H=function(){var t=F[0];q.push(t),F=F.slice(1),F=yo(p,F,q);var e=Te(F,F),r=ge((function(e){return!e.predecessors||!er(t.callback,e.predecessors)}),e);G=_e(G,r),B=_e(B,r.map((function(e){var r;return nc(nc({},e),{},{predecessors:_e(null!==(r=e.predecessors)&&void 0!==r?r:[],[t.callback])})})))};F.length;)H();d=_e(Te(d,G),B),$=Ve((function(t){return t.executionGroup}),ge((function(t){return!Yt(t.executionGroup)}),l)),z=ge((function(t){if(!t.executionGroup||!$[t.executionGroup]||!$[t.executionGroup].length)return!1;var e=Ct(lo,ze(t.getInputs(p))),r=ze(Ct((function(t){return t.executionMeta.allProps}),$[t.executionGroup])),n=ze(Ct((function(t){return t.executionMeta.updatedProps}),$[t.executionGroup]));return sr(ur(e,n))&&sr(Te(e,r))&&!lt(Qn,t.callback.inputs)}),q),d=Te(d,z),q=Te(q,z),d=Te(d,q),J=Te(d,y),Y=Te(y,d),r(Aa([J.length?ba(J):null,Y.length?xa(Y):null,w.length?ja(w):null,O.length?_a(O):null,_.length?Ea(_):null,E.length?Pa(E):null,k.length?ja(k):null,A.length?ga(A):null,I.length?_a(I):null,L.length?ha(L):null,N.length?Ea(N):null,C.length?va(C):null,U.length?Pa(U):null,M.length?wa(M):null,q.length?ga(q):null]));case 36:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),inputs:["callbacks.requested","callbacks.completed"]},uc=ac;function cc(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(t,e)||function(t,e){if(t){if("string"==typeof t)return sc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?sc(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var fc={observer:function(t){var e=t.dispatch,r=t.getState,n=r().callbacks,o=Pu(n),i=r().callbacks.stored,a=cc(Lr((function(t){return Yt(t.executionGroup)}),i),2),u=a[0],c=a[1],s=Ve((function(t){return t.executionGroup}),c),f=Ve((function(t){return t.executionGroup}),ge((function(t){return!Yt(t.executionGroup)}),o)),l=Gt((function(t,e){var r=cc(e,2),n=r[0],o=r[1];return f[n]?t:_e(t,o)}),[],zr(s));e(Aa([u.length?Sa(u):null,l.length?Sa(l):null]))},inputs:["callbacks.stored","callbacks.completed"]},lc=fc;function pc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function hc(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var dc=function(){function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),hc(this,"__store",void 0),hc(this,"storeObserver",new Si),hc(this,"setObservers",Tr((function(){var t=e.storeObserver.observe;t(ki),t(Tu),t(Nu),t(uc),t(Zu),t(xu),t(gu),t(lc)}))),hc(this,"createAppStore",(function(t,r){e.__store=on(t,r),e.storeObserver.setStore(e.__store),e.setObservers()})),hc(this,"initializeStore",(function(t){if(e.__store&&!t)return e.__store;var r=function(){return function(t){return function(e,r){var n=e||{},o=n.history,i=n.config,a=n.hooks,u=e;return"RELOAD"===r.type?u={history:o,config:i,hooks:a}:"SET_CONFIG"===r.type&&(u={hooks:a}),t(u,r)}}((t=Oi(),function(e,r){var n=r.type,o=r.payload;if("ON_PROP_CHANGE"===n){var i=_i(o,e,!0);i&&!sr(i.props)&&(e.history.present=i)}var a,u=t(e,r);if("ON_PROP_CHANGE"===n&&"response"!==o.source){var c=_i(o,u);c&&!sr(c.props)&&(u.history={past:[].concat((a=u.history.past,function(t){if(Array.isArray(t))return mi(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return mi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?mi(t,e):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[e.history.present]),present:c,future:[]})}return u}));var t}();return e.createAppStore(r,function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(t){return function(){var r=t.apply(void 0,arguments),n=function(){throw new Error(Xr(15))},o={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=e.map((function(t){return t(o)}));return n=an.apply(void 0,i)(r.dispatch),Qr(Qr({},r),{},{dispatch:n})}}}(sn)),t||(window.store=e.__store),e.__store})),this.__store=this.initializeStore()}var e,r;return e=t,(r=[{key:"store",get:function(){return this.__store}}])&&pc(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}(),yc=["String","Number","Null","Boolean"],vc=function(t){return er(Zt(t),yc)};function gc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function bc(t,e){return bc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},bc(t,e)}function mc(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function wc(t){return wc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},wc(t)}var Oc=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&bc(t,e)}(u,t);var e,r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=wc(o);if(i){var r=wc(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return mc(this,t)});function u(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),(e=a.call(this,t)).state={myID:t.componentId,oldChildren:null,hasError:!1},e}return e=u,r=[{key:"componentDidCatch",value:function(t,e){var r=this.props.dispatch;r(qi({myID:this.state.myID,type:"frontEnd",error:t,info:e})),r(Xi)}},{key:"componentDidUpdate",value:function(t,e){var r=t.children;this.state.hasError||r===e.oldChildren||r===this.props.children||this.setState({oldChildren:r})}},{key:"render",value:function(){var t=this.state,e=t.hasError,r=t.oldChildren;return e?r:this.props.children}}],n=[{key:"getDerivedStateFromError",value:function(t){return{hasError:!0}}}],r&&gc(e.prototype,r),n&&gc(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(t.Component);Oc.propTypes={children:u().object,componentId:u().string,error:u().object,dispatch:u().func};var _c=Oc,Ec=r(414),jc=r.n(Ec),xc=!1;function Sc(t,e,r){var n;if(!r)return xc;var o=dr(e,r);if(!o)return xc;var i=o.__dashprivate__idprop__;if(i)return{is_loading:!0,prop_name:i.property,component_name:Bn(i.id)};var a,u=null===(n=o.__dashprivate__idprops__)||void 0===n?void 0:n[0];return u&&(Ac(a=t),jn(a)._dashprivate_isLoadingComponent)?{is_loading:!0,prop_name:u.property,component_name:Bn(u.id)}:xc}var Pc=function(t,e){var r,n;return(null!==(r=e&&(null===(n=dr(t,e))||void 0===n?void 0:n.__dashprivate__idprops__))&&void 0!==r?r:[]).map((function(t){var e=t.id,r=t.property;return"".concat(e,".").concat(r)})).join(",")};function Ac(t){if("Array"===Zt(t))throw new Error("The children property of a component is a list of lists, instead of just a list. Check the component that has the following contents, and remove one of the levels of nesting: \n"+JSON.stringify(t,null,2));if("Object"===Zt(t)&&!(Qe("namespace",t)&&Qe("type",t)&&Qe("props",t)))throw new Error("An object was provided as `children` instead of a component, string, or number (or list of those). Check the children property that looks something like:\n"+JSON.stringify(t,null,2))}function kc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Tc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?kc(Object(r),!0).forEach((function(e){Lc(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Lc(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ic(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Rc(t,e){return Rc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Rc(t,e)}function Cc(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Nc(t)}function Nc(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Dc(t){return Dc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Dc(t)}function Mc(){return Mc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mc.apply(this,arguments)}function Uc(t){return function(t){if(Array.isArray(t))return qc(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return qc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?qc(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Gc={is_loading:!1};function Bc(t){var e=t.element,r=t.extraProps,n=t.props,o=t.children,i=t.type,a=function(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=[];for(var a in t)if(t.hasOwnProperty(a)){var u=void 0;try{"function"!=typeof t[a]?(u=Error((n||"React class")+": "+r+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof t[a]+"`.")).name="Invariant Violation":u=t[a](e,a,n,r,null,jc())}catch(t){u=t}if(!u||u instanceof Error||i.push((n||"React class")+": type specification of "+r+" `"+a+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof u+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),u instanceof Error){var c=o&&o()||"";i.push("Failed "+r+" type: "+u.message+c)}}return i.join("\n\n")}(e.propTypes,n,"component prop",e);return a&&function(t,e,r){var n,o=t.split("`");if(er("is marked as required",t)){var i=o[1];n="".concat(i," in ").concat(r),e.id&&(n+=' with ID "'.concat(e.id,'"')),n+=" is required but it was not provided."}else if(er("Bad object",t))n=t.split("supplied to ")[0]+"supplied to ".concat(r)+".\nBad"+t.split(".\nBad")[1];else{if(!er("Invalid ",t)||!er(" supplied to ",t))throw new Error(t);var a=o[1];if(n="Invalid argument `".concat(a,"` passed into ").concat(r),e.id&&(n+=' with ID "'.concat(e.id,'"')),n+=".",er(", expected ",t)){var u=t.split(", expected ")[1];n+="\nExpected ".concat(u)}if(er(" of type `",t)){var c=t.split(" of type `")[1].split("`")[0];n+="\nWas supplied type `".concat(c,"`.")}if(Qe(a,e)){var s=JSON.stringify(e[a],null,2);s&&(er("\n",s)?n+="\nValue provided: \n".concat(s):n+="\nValue provided: ".concat(s))}}throw new Error(n)}(a,n,i),Fc(e,n,r,o)}function Fc(t,r,n,o){var i=xr(r,n);return Array.isArray(o)?e().createElement.apply(e(),[t,i].concat(Uc(o))):e().createElement(t,i,o)}function Hc(t){return"Object"===Zt(t)&&Qe("type",t)&&Qe("namespace",t)&&Qe("props",t)}Bc.propTypes={children:u().any,element:u().any,layout:u().any,props:u().any,extraProps:u().any,id:u().string};var $c=(0,t.memo)((function(t){return e().createElement(us.Consumer,null,(function(r){return e().createElement(zc,Mc({},r.fn(),t,{_dashprivate_path:JSON.parse(t._dashprivate_path)}))}))})),zc=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Rc(t,e)}(u,t);var r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Dc(o);if(i){var r=Dc(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return Cc(this,t)});function u(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),(e=a.call(this,t)).setProps=e.setProps.bind(Nc(e)),e}return r=u,(n=[{key:"createContainer",value:function(t,r,n){return vc(r)?r:e().createElement($c,{key:r&&r.props&&Bn(r.props.id),_dashprivate_error:t._dashprivate_error,_dashprivate_layout:r,_dashprivate_loadingState:Sc(r,n,t._dashprivate_loadingMap),_dashprivate_loadingStateHash:Pc(n,t._dashprivate_loadingMap),_dashprivate_path:JSON.stringify(n)})}},{key:"setProps",value:function(t){var e=this.props,r=e._dashprivate_graphs,n=e._dashprivate_dispatch,o=e._dashprivate_path,i=e._dashprivate_layout,a=this.getLayoutProps(),u=a.id,c=Nr((function(t,e){return!se(t,a[e])}),t);if(!sr(c)){var s=function(t,e,r){if(!(t&&r&&e.length))return[];if("string"==typeof t){var n=r.inputMap[t];return n?e.filter((function(t){return n[t]})):[]}var o=Object.keys(t).sort(),i=Ur(o,t),a=o.join(","),u=r.inputPatterns[a];return u?e.filter((function(t){var e=u[t];return e&&e.some((function(t){return Vn(o,i,t.values)}))})):[]}(u,It(c),r);!function(t,e,r){var n=uu(t),o=n.canPersist,i=n.id,a=n.props,u=n.element,c=n.persistence,s=n.persisted_props,f=n.persistence_type;o&&c&&Ye((function(t){var n=$a(t.split("."),2),o=n[0],s=n[1];if(void 0!==e[o]){var l=nu(f,r),p=iu(u,o,s).extract,h=au(i,t,c),d=p(a[o]),y=p(e[o]);if(d!==y){l.hasItem(h)&&(d=l.getItem(h)[1]);var v=void 0===d?[y]:[y,d];l.setItem(h,v,r)}}}),s)}(i,t,n),s.length&&n(ea({id:u,props:Cr(s,c)})),n(Yi({props:c,itempath:o}))}}},{key:"getChildren",value:function(t,e){var r=this;return Yt(t)?null:Array.isArray(t)?rt(Ct)((function(t,n){return r.createContainer(r.props,t,_e(e,["props","children",n]))}),t):this.createContainer(this.props,t,_e(e,["props","children"]))}},{key:"wrapChildrenProp",value:function(t,e){var r=this;return Array.isArray(t)?t.map((function(t,n){return Hc(t)?r.createContainer(r.props,t,_e(r.props._dashprivate_path,["props"].concat(Uc(e),[n]))):t})):Hc(t)?this.createContainer(this.props,t,_e(this.props._dashprivate_path,["props"].concat(Uc(e)))):t}},{key:"getComponent",value:function(t,r,n,o){var i=this,a=this.props,u=a._dashprivate_config,c=a._dashprivate_dispatch,s=a._dashprivate_error;if(sr(t))return null;if(vc(t))return t;Ac(t);for(var f=jn(t),l=Rr([],["children_props",t.namespace,t.type],u),p=Re("children",t.props),h=0;h<l.length;h++){var d=l[h];if(d.includes(".")){var y=d.split("."),v=void 0,g=void 0;if(d.includes("[]")){var b=function(){var t=[],e=[],r=!1;return y.forEach((function(n){r?e.push(n):n.includes("[]")?(r=!0,t.push(n.replace("[]",""))):t.push(n)})),void 0!==(v=dr(t,p))&&v.length&&dr(e,v[0])?(g=v.map((function(r,n){var o=_e(t,_e([n],e));return Wt(e,i.wrapChildrenProp(dr(e,r),o),r)})),void(y=t)):"continue"}();if("continue"===b)continue}else{if(void 0===(v=dr(y,p)))continue;g=this.wrapChildrenProp(v,y)}p=Wt(y,g,p)}else{var m=p[d];void 0!==m&&(p=Kt(d,this.wrapChildrenProp(m,[d]),p))}}"Object"===Zt(p.id)&&(p.id=Bn(p.id));var w={loading_state:n||Gc,setProps:o};return e().createElement(_c,{componentType:t.type,componentId:p.id,key:p.id,dispatch:c,error:s},u.props_check?e().createElement(Bc,{children:r,element:f,props:p,extraProps:w,type:t.type}):Fc(f,p,w,r))}},{key:"getLayoutProps",value:function(){return Mr({},"props",this.props._dashprivate_layout)}},{key:"render",value:function(){var t=this.props,e=t._dashprivate_layout,r=t._dashprivate_loadingState,n=t._dashprivate_path,o=this.getLayoutProps(),i=this.getChildren(o.children,n);return this.getComponent(e,i,r,this.setProps)}}])&&Ic(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),u}(t.Component);$c.propTypes={_dashprivate_error:u().any,_dashprivate_layout:u().object,_dashprivate_loadingState:u().oneOfType([u().object,u().bool]),_dashprivate_loadingStateHash:u().string,_dashprivate_path:u().string},zc.propTypes=Tc(Tc({},$c.propTypes),{},{_dashprivate_config:u().object,_dashprivate_dispatch:u().func,_dashprivate_graphs:u().any,_dashprivate_loadingMap:u().any,_dashprivate_path:u().array});var Jc=$c;function Yc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Wc(t,e){return Wc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Wc(t,e)}function Kc(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Vc(t){return Vc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Vc(t)}var Zc=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Wc(t,e)}(u,t);var r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Vc(o);if(i){var r=Vc(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return Kc(this,t)});function u(t){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),a.call(this,t)}return r=u,(n=[{key:"render",value:function(){return e().createElement("div",{id:"_dash-app-content"},this.props.children)}}])&&Yc(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),u}(t.Component);Zc.propTypes={children:u().object};var Qc=Zc;function Xc(){Xc=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function ts(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var es=Tr(console.warn),rs={GET:function(t,e){return fetch(t,Er(e,{method:"GET",headers:Vi()}))},POST:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return fetch(t,Er(e,{method:"POST",headers:Vi(),body:r?JSON.stringify(r):null}))}};function ns(t,e,r,n,o){return function(){var i=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ts(i,n,o,a,u,"next",t)}function u(t){ts(i,n,o,a,u,"throw",t)}a(void 0)}))}}(Xc().mark((function i(a,u){var c,s,f,l,p,h,d,y,v,g,b;return Xc().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:h=function(t){u().error.backEndConnected!==t&&a({type:"SET_CONNECTION_STATUS",payload:t})},c=u(),s=c.config,f=c.hooks,l=null,p="".concat(bn(s)).concat(t),a({type:r,payload:{id:n,status:"loading"}}),i.prev=5,y=0;case 7:if(!(y<=1)){i.next=36;break}return i.prev=8,i.next=11,rs[e](p,s.fetch,o);case 11:d=i.sent,i.next=19;break;case 14:return i.prev=14,i.t0=i.catch(8),console.log("fetch error",d),h(!1),i.abrupt("return");case 19:if(401!==d.status&&400!==d.status){i.next=32;break}if(!f.request_refresh_jwt){i.next=32;break}return i.next=23,d.text();case 23:if(!i.sent.includes(Ko)){i.next=32;break}return i.next=27,f.request_refresh_jwt(s.fetch.headers.Authorization.substr("Bearer ".length));case 27:if(!(v=i.sent)){i.next=32;break}return l={Authorization:"Bearer ".concat(v)},s=Er(s,{fetch:{headers:l}}),i.abrupt("continue",33);case 32:return i.abrupt("break",36);case 33:y++,i.next=7;break;case 36:if(g=d.headers.get("content-type"),l&&a(Fi(l)),h(!0),!g||-1===g.indexOf("application/json")){i.next=41;break}return i.abrupt("return",d.json().then((function(t){return a({type:r,payload:{status:d.status,content:t,id:n}}),t})));case 41:return es("Response is missing header: content-type: application/json"),i.abrupt("return",a({type:r,payload:{id:n,status:d.status}}));case 45:i.prev=45,i.t1=i.catch(5),b="Error from API call: "+t,ra(i.t1,b,a);case 49:case"end":return i.stop()}}),i,null,[[5,45],[8,14]])})));return function(t,e){return i.apply(this,arguments)}}()}function os(){os=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o=e&&e.prototype instanceof l?e:l,i=Object.create(o.prototype),a=new E(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=w(a,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(t,e,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===f)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var f={};function l(){}function p(){}function h(){}var d={};u(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(j([])));v&&v!==e&&r.call(v,o)&&(d=v);var g=h.prototype=l.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function m(t,e){function n(o,i,a,u){var c=s(t[o],t,i);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(l).then((function(t){f.value=t,a(f)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var o;this._invoke=function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,u(t,a,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},b(m.prototype),u(m.prototype,i,(function(){return this})),t.AsyncIterator=m,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new m(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,a,"Generator"),u(g,o,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=j,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function is(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function as(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var us=(0,t.createContext)({}),cs=function(r){var n,o,i=r.appLifecycle,a=r.config,u=r.dependenciesRequest,c=r.error,s=r.layoutRequest,f=r.layout,l=r.loadingMap,p=(n=(0,t.useState)(!1),o=2,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(n,o)||function(t,e){if(t){if("string"==typeof t)return as(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?as(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),h=p[0],d=p[1],y=(0,t.useRef)(null);y.current||(y.current=new On);var v=(0,t.useRef)(!1),g=(0,t.useRef)({});g.current=r;var b,m=(0,t.useRef)({fn:function(){return{_dashprivate_config:g.current.config,_dashprivate_dispatch:g.current.dispatch,_dashprivate_graphs:g.current.graphs,_dashprivate_loadingMap:g.current.loadingMap}}});return(0,t.useEffect)(ss.bind(null,r,y,d)),(0,t.useEffect)((function(){v.current&&function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){is(i,n,o,a,u,"next",t)}function u(t){is(i,n,o,a,u,"throw",t)}a(void 0)}))}}(os().mark((function t(){return os().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return v.current=!1,t.next=3,tc(0);case 3:y.current.emit("rendered");case 4:case"end":return t.stop()}}),t)})))()})),s.status&&!er(s.status,[Vo,"loading"])?b=e().createElement("div",{className:"_dash-error"},"Error loading layout"):h||u.status&&!er(u.status,[Vo,"loading"])?b=e().createElement("div",{className:"_dash-error"},"Error loading dependencies"):i===jo("HYDRATED")?(v.current=!0,b=e().createElement(us.Provider,{value:m.current},e().createElement(Jc,{_dashprivate_error:c,_dashprivate_layout:f,_dashprivate_loadingState:Sc(f,[],l),_dashprivate_loadingStateHash:Pc([],l),_dashprivate_path:JSON.stringify([])}))):b=e().createElement("div",{className:"_dash-loading"},"Loading..."),a&&!0===a.ui?e().createElement(Qc,null,b):b};function ss(t,e,r){var n=t.appLifecycle,o=t.dependenciesRequest,i=t.dispatch,a=t.error,u=t.graphs,c=t.layout,s=t.layoutRequest;if(sr(s))i(ns("_dash-layout","GET","layoutRequest"));else if(s.status===Vo&&sr(c)){var f=cu(s.content,i);i(Ji(_n(f,[],null,e.current))),i(zi(f))}if(sr(o)?i(ns("_dash-dependencies","GET","dependenciesRequest")):o.status===Vo&&sr(u)&&i(Hi(Wn(o.content,Wi(i)))),o.status===Vo&&!sr(u)&&s.status===Vo&&!sr(c)&&n===jo("STARTED")){var l=!1;try{i((Wi(i),function(t,e){!function(t,e){var r,n,o=t.config,i=t.graphs,a=t.layout,u=t.paths,c=!o.suppress_callback_exceptions;c&&o.validation_layout?(r=o.validation_layout,n=_n(r,[],null,u.events)):(r=a,n=u);var s=i.outputMap,f=i.inputMap,l=i.outputPatterns,p=i.inputPatterns;function h(t){return"This ID was used in the callback(s) for Output(s):\n  "+t.map((function(t){return t.outputs.map(lo).join(", ")})).join("\n  ")}function d(t,r,n){e("ID not found in layout",["Attempting to connect a callback ".concat(r," item to component:"),'  "'.concat(Bn(t),'"'),"but no components with that id exist in the layout.","","If you are assigning callbacks to components that are","generated by other callbacks (and therefore not in the","initial layout), you can suppress this exception by setting","`suppress_callback_exceptions=True`.",h(n)])}function y(t,n,o,i,a){var u=dr(n,r),c=jn(u);if(c&&c.propTypes&&!c.propTypes[o]){for(var s in c.propTypes){var f=s.length-1;if("*"===s.charAt(f)&&o.substr(0,f)===s.substr(0,f))return}var l=u.type,p=u.namespace;e("Invalid prop for this component",['Property "'.concat(o,'" was used with component ID:'),"  ".concat(JSON.stringify(t)),"in one of the ".concat(i," items of a callback."),"This ID is assigned to a ".concat(p,".").concat(l," component"),"in the layout, which does not support this property.",h(a)])}}function v(t,e,r,o){Oo()(n)({id:t,property:e}).forEach((function(t){y(t.id,t.path,e,r,o)}))}var g={};function b(t){var e=t.state,r=t.output;if(!g[r]){g[r]=1;var o="State";e.forEach((function(e){var r=e.id,i=e.property;if("string"==typeof r){var a=En(n,r);a?y(r,a,i,o,[t]):c&&d(r,o,[t])}else ur([Rn,Cn],Jt(r)).length||v(r,i,o,[t])}))}}function m(t,e,r){for(var o in t){var i=t[o],a=En(n,o);if(a)for(var u in i){var s=i[u];y(o,a,u,e,s),r&&s.forEach(b)}else c&&d(o,e,ze(Jt(i)))}}function w(t,e,r){for(var n in t){var o=t[n],i=function(t){o[t].forEach((function(n){var o=n.keys,i=n.values,a=n.callbacks;v(Kr(o,i),t,e,a),r&&a.forEach(b)}))};for(var a in o)i(a)}}m(s,"Output",!0),m(f,"Input"),w(l,"Output",!0),w(p,"Input")}(e(),Wi(t)),function(t,e){var r=e(),n=r.graphs,o=r.paths,i=r.layout;try{n.MultiGraph.overallOrder()}catch(e){t(qi({type:"backEnd",error:{message:"Circular Dependencies",html:e.toString()}}))}t(ba(vo(n,o,i,{outputsOnly:!0})))}(t,e),t(Gi(jo("HYDRATED")))}))}catch(t){a.frontEnd.length||a.backEnd.length||i(qi({type:"backEnd",error:t})),l=!0}finally{r(l)}}}cs.propTypes={appLifecycle:u().oneOf([jo("STARTED"),jo("HYDRATED"),jo("DESTROYED")]),dispatch:u().func,dependenciesRequest:u().object,graphs:u().object,layoutRequest:u().object,layout:u().object,loadingMap:u().any,history:u().any,error:u().object,config:u().object};var fs=z((function(t){return{appLifecycle:t.appLifecycle,dependenciesRequest:t.dependenciesRequest,layoutRequest:t.layoutRequest,layout:t.layout,loadingMap:t.loadingMap,graphs:t.graphs,history:t.history,error:t.error,config:t.config}}),(function(t){return{dispatch:t}}))(cs);function ls(t){return t.isLoading?e().createElement("div",{className:"_dash-loading-callback"}):null}ls.propTypes={isLoading:u().bool.isRequired};var ps=z((function(t){return{isLoading:t.isLoading}}))(ls),hs=r(379),ds=r.n(hs),ys=r(795),vs=r.n(ys),gs=r(569),bs=r.n(gs),ms=r(565),ws=r.n(ms),Os=r(216),_s=r.n(Os),Es=r(589),js=r.n(Es),xs=r(789),Ss={};function Ps(t){var r=t.dispatch,n=t.history,o=e().createElement("span",{key:"undoLink",className:"_dash-undo-redo-link",onClick:function(){return r(Qi)}},e().createElement("div",{className:"_dash-icon-undo"},"↺"),e().createElement("div",{className:"_dash-undo-redo-label"},"undo")),i=e().createElement("span",{key:"redoLink",className:"_dash-undo-redo-link",onClick:function(){return r(Zi)}},e().createElement("div",{className:"_dash-icon-redo"},"↻"),e().createElement("div",{className:"_dash-undo-redo-label"},"redo"));return e().createElement("div",{className:"_dash-undo-redo"},e().createElement("div",null,n.past.length>0?o:null,n.future.length>0?i:null))}Ss.styleTagTransform=js(),Ss.setAttributes=ws(),Ss.insert=bs().bind(null,"head"),Ss.domAPI=vs(),Ss.insertStyleElement=_s(),ds()(xs.Z,Ss),xs.Z&&xs.Z.locals&&xs.Z.locals,Ps.propTypes={history:u().object,dispatch:u().func};var As=z((function(t){return{history:t.history}}),(function(t){return{dispatch:t}}))(Ps);function ks(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ts(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Ls(t,e){return Ls=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ls(t,e)}function Is(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Rs(t)}function Rs(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Cs(t){return Cs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Cs(t)}var Ns=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ls(t,e)}(u,t);var e,r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Cs(o);if(i){var r=Cs(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return Is(this,t)});function u(t){var e;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),e=a.call(this,t),t.config.hot_reload){var r=t.config.hot_reload,n=r.interval,o=r.max_retry;e.state={interval:n,disabled:!1,intervalId:null,packages:null,max_retry:o}}else e.state={disabled:!0};return e._retry=0,e._head=document.querySelector("head"),e.clearInterval=e.clearInterval.bind(Rs(e)),e}return e=u,r=[{key:"clearInterval",value:function(){window.clearInterval(this.state.intervalId),this.setState({intervalId:null})}},{key:"componentDidUpdate",value:function(t,e){var r=this.state.reloadRequest,n=this.props.dispatch;if(r&&Qe("reloadRequest",e))if(200===r.status&&dr(["content","reloadHash"],r)!==dr(["reloadRequest","content","reloadHash"],e))if(!r.content.hard&&se(r.content.packages.length,Rr([],["reloadRequest","content","packages"],e).length)&&se(Hr(te(vr),r.content.packages),Hr(te(vr),Rr([],["reloadRequest","content","packages"],e))))n({type:"RELOAD"});else{var o,i=!1,a=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return ks(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ks(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(r.content.files);try{for(a.s();!(o=a.n()).done;){var u=o.value;if(!u.is_css){i=!1;break}i=!0;for(var c=[],s=document.evaluate('//link[contains(@href, "'.concat(u.url,'")]'),this._head),f=s.iterateNext();f;)c.push(f),f=s.iterateNext();if(Ye((function(t){return t.setAttribute("disabled","disabled")}),c),u.modified>0){var l=document.createElement("link");l.href="".concat(u.url,"?m=").concat(u.modified),l.type="text/css",l.rel="stylesheet",this._head.appendChild(l)}}}catch(t){a.e(t)}finally{a.f()}i||window.location.reload()}else null!==this.state.intervalId&&500===r.status&&(this._retry>this.state.max_retry&&(this.clearInterval(),window.alert("Hot reloading is disabled after failing ".concat(this._retry," times. ")+"Please check your application for errors, then refresh the page.")),this._retry++)}},{key:"componentDidMount",value:function(){var t=this.props,e=t.dispatch,r=t.reloadRequest,n=this.state,o=n.disabled,i=n.interval;if(!o&&!this.state.intervalId){var a=window.setInterval((function(){"loading"!==r.status&&e(ns("_reload-hash","GET","reloadRequest"))}),i);this.setState({intervalId:a})}}},{key:"componentWillUnmount",value:function(){!this.state.disabled&&this.state.intervalId&&this.clearInterval()}},{key:"render",value:function(){return null}}],n=[{key:"getDerivedStateFromProps",value:function(t){return sr(t.reloadRequest)||"loading"===t.reloadRequest.status?null:{reloadRequest:t.reloadRequest}}}],r&&Ts(e.prototype,r),n&&Ts(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(e().Component);Ns.defaultProps={},Ns.propTypes={id:u().string,config:u().object,reloadRequest:u().object,dispatch:u().func,interval:u().number};var Ds=z((function(t){return{config:t.config,reloadRequest:t.reloadRequest}}),(function(t){return{dispatch:t}}))(Ns);function Ms(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Us(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ms(Object(r),!0).forEach((function(e){qs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ms(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qs(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Gs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Bs(t,e){return Bs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Bs(t,e)}function Fs(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function Hs(t){return Hs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Hs(t)}var $s=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Bs(t,e)}(u,t);var r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Hs(o);if(i){var r=Hs(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return Fs(this,t)});function u(t){var e;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),e=a.call(this,t),null!==t.hooks.request_pre||null!==t.hooks.request_post||null!==t.hooks.callback_resolved||null!==t.hooks.request_refresh_jwt){var r=t.hooks;r.request_refresh_jwt&&(r=Us(Us({},r),{},{request_refresh_jwt:br(tr,r.request_refresh_jwt)})),t.dispatch($i(r))}return e}return r=u,n=[{key:"UNSAFE_componentWillMount",value:function(){var t,e=this.props.dispatch,r=(t=document.getElementById("_dash-config"),JSON.parse(null!=t&&t.textContent?null==t?void 0:t.textContent:"{}"));r.fetch={credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}},e(Bi(r))}},{key:"render",value:function(){var t=this.props.config;if("Null"===Zt(t))return e().createElement("div",{className:"_dash-loading"},"Loading...");var r=t.show_undo_redo;return e().createElement(e().Fragment,null,r?e().createElement(As,null):null,e().createElement(fs,null),e().createElement(ps,null),e().createElement(Ds,null))}}],n&&Gs(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),u}(e().Component);$s.propTypes={hooks:u().object,dispatch:u().func,config:u().object};var zs=z((function(t){return{history:t.history,config:t.config}}),(function(t){return{dispatch:t}}))($s);function Js(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ys=function(r){var n,o,i=r.hooks,a=(n=(0,t.useState)((function(){return new dc})),o=1,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(n,o)||function(t,e){if(t){if("string"==typeof t)return Js(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Js(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0].store;return e().createElement(J,{store:a},e().createElement(zs,{hooks:i}))};Ys.propTypes={hooks:u().shape({request_pre:u().func,request_post:u().func,callback_resolved:u().func,request_refresh_jwt:u().func})},Ys.defaultProps={hooks:{request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null}};var Ws=Ys;function Ks(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Vs(t,e,r){return e&&Ks(t.prototype,e),r&&Ks(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var Zs=Vs((function t(r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),i().render(e().createElement(Ws,{hooks:r}),document.getElementById("react-entry-point"))}));window.DashRenderer=Zs}(),window.dash_renderer=n}();