{"name": "dash-html-components", "version": "2.0.6", "description": "Vanilla HTML components for Dash", "main": "lib/index.js", "repository": {"type": "git", "url": "https://github.com/plotly/dash.git"}, "license": "MIT", "bugs": {"url": "https://github.com/plotly/dash/issues"}, "homepage": "https://github.com/plotly/dash", "scripts": {"clean": "rm -rf src/* && mkdir -p src/components", "prebuild": "cd scripts && sh generate-all.sh && cd -", "lint": "eslint src scripts", "build:js": "webpack --mode production", "build:backends": "dash-generate-components ./src/components dash_html_components -p package-info.json && cp dash_html_components_base/** dash_html_components && dash-generate-components ./src/components dash_html_components -p package-info.json --r-prefix 'html' --r-suggests 'dash,dashCoreComponents' --jl-prefix 'html' && black dash_html_components", "build": "npm run build:js && npm run build:backends", "postbuild": "es-check es5 dash_html_components/*.js", "build:watch": "watch 'npm run build' src", "test:py": "pytest --nopercyfinalize --headless tests/test_dash_html_components.py tests/test_integration.py", "test": "run-s -c test:py lint"}, "author": "<PERSON> <<EMAIL>>", "maintainer": "<PERSON> <<EMAIL>>", "dependencies": {"prop-types": "^15.8.1", "ramda": "^0.28.0", "react": "^17.0.2", "react-dom": "^17.0.2"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-react": "^7.18.6", "babel-loader": "^8.2.5", "cheerio": "^0.22.0", "cross-env": "^7.0.3", "es-check": "^7.0.1", "eslint": "^8.26.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.10", "npm-run-all": "^4.1.5", "react-docgen": "^5.4.3", "request": "^2.88.2", "string": "^3.3.3", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "files": ["/dash_html_components/*{.js,.map}"], "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "browserslist": ["last 7 years and not dead"]}