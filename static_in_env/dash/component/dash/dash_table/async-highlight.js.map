{"version": 3, "file": "async-highlight.js", "mappings": "8JAGA,E,mICQIA,EAAU,CAAC,EAEfA,EAAQC,kBAAoB,IAC5BD,EAAQE,cAAgB,IAElBF,EAAQG,OAAS,SAAc,KAAM,QAE3CH,EAAQI,OAAS,IACjBJ,EAAQK,mBAAqB,IAEhB,IAAI,IAASL,GAKJ,KAAW,YAAiB,WC1BlD,IA6CMM,EAAO,CACX,IACA,OACA,UACA,UACA,QACA,QACA,IACA,aACA,OACA,SACA,SACA,UACA,OACA,OACA,KACA,MACA,UACA,MACA,MACA,KACA,KACA,KACA,WACA,aACA,SACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,SACA,SACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,OACA,OACA,MACA,SACA,KACA,IACA,IACA,QACA,OACA,UACA,OACA,SACA,UACA,MACA,QACA,QACA,KACA,WACA,QACA,KACA,QACA,OACA,KACA,KACA,MACA,SAGIC,EAAiB,CACrB,YACA,cACA,eACA,QACA,cACA,cACA,sBACA,gBACA,eACA,eACA,gBACA,OACA,SACA,QACA,kBACA,aACA,cACA,iBACA,kBACA,UACA,uBACA,mBACA,yBACA,+BACA,aACA,OACA,YACA,SACA,QAEA,YACA,YACA,aACA,cAIIC,EAAiB,CACrB,SACA,WACA,QACA,UACA,UACA,UACA,UACA,MACA,WACA,OACA,QACA,UACA,QACA,cACA,gBACA,aACA,SACA,QACA,gBACA,eACA,MACA,OACA,eACA,QACA,gBACA,WACA,UACA,KACA,OACA,aACA,eACA,OACA,OACA,aACA,MACA,YACA,UACA,iBACA,eACA,mBACA,cACA,aACA,eACA,WACA,eACA,OACA,oBACA,YACA,aACA,WACA,QACA,OACA,QACA,SACA,gBACA,eACA,QACA,UACA,SAIIC,EAAkB,CACtB,QACA,WACA,SACA,MACA,aACA,eACA,aACA,gBACA,SACA,OACA,cACA,YACA,UACA,kBAGIC,EAAa,CACjB,gBACA,cACA,aACA,MACA,YACA,kBACA,sBACA,qBACA,sBACA,4BACA,iBACA,uBACA,4BACA,sBACA,aACA,wBACA,wBACA,kBACA,mBACA,mBACA,oBACA,sBACA,oBACA,kBACA,aACA,SACA,eACA,qBACA,mBACA,yBACA,yBACA,yBACA,qBACA,2BACA,2BACA,2BACA,qBACA,qBACA,gBACA,sBACA,4BACA,6BACA,sBACA,sBACA,kBACA,eACA,eACA,sBACA,sBACA,qBACA,sBACA,qBACA,gBACA,sBACA,oBACA,0BACA,0BACA,0BACA,sBACA,4BACA,4BACA,4BACA,sBACA,sBACA,cACA,oBACA,oBACA,oBACA,gBACA,eACA,qBACA,qBACA,qBACA,iBACA,eACA,aACA,mBACA,yBACA,0BACA,mBACA,mBACA,eACA,SACA,uBACA,aACA,aACA,cACA,eACA,eACA,eACA,cACA,QACA,OACA,YACA,YACA,QACA,eACA,cACA,aACA,cACA,oBACA,oBACA,oBACA,cACA,eACA,UACA,UACA,UACA,qBACA,oBACA,gBACA,MACA,YACA,aACA,SACA,YACA,UACA,cACA,SACA,OACA,aACA,iBACA,YACA,YACA,cACA,YACA,QACA,OACA,OACA,eACA,cACA,wBACA,eACA,yBACA,YACA,mBACA,iBACA,eACA,aACA,iBACA,eACA,oBACA,0BACA,yBACA,uBACA,wBACA,0BACA,cACA,MACA,6BACA,OACA,YACA,oBACA,iBACA,iBACA,cACA,kBACA,oBACA,WACA,WACA,eACA,iBACA,gBACA,sBACA,wBACA,qBACA,sBACA,SACA,UACA,OACA,oBACA,kBACA,mBACA,WACA,cACA,YACA,kBACA,OACA,iBACA,aACA,cACA,aACA,mBACA,sBACA,kBACA,SACA,eACA,mBACA,qBACA,gBACA,gBACA,oBACA,sBACA,cACA,eACA,aACA,QACA,OACA,cACA,mBACA,qBACA,qBACA,oBACA,qBACA,oBACA,YACA,iBACA,aACA,YACA,cACA,gBACA,cACA,YACA,YACA,iBACA,aACA,kBACA,YACA,iBACA,aACA,kBACA,YACA,iBACA,WACA,YACA,WACA,YACA,SACA,OACA,SACA,aACA,kBACA,UACA,QACA,UACA,UACA,gBACA,iBACA,gBACA,gBACA,WACA,gBACA,aACA,aACA,UACA,gBACA,oBACA,sBACA,iBACA,iBACA,qBACA,uBACA,eACA,gBACA,cACA,mBACA,oBACA,oBACA,QACA,cACA,eACA,cACA,qBACA,iBACA,WACA,SACA,SACA,OACA,aACA,cACA,QACA,UACA,gBACA,sBACA,0BACA,4BACA,uBACA,uBACA,2BACA,6BACA,qBACA,sBACA,oBACA,iBACA,uBACA,2BACA,6BACA,wBACA,wBACA,4BACA,8BACA,sBACA,uBACA,qBACA,oBACA,mBACA,mBACA,kBACA,mBACA,kBACA,wBACA,eACA,gBACA,QACA,WACA,MACA,WACA,eACA,aACA,iBACA,kBACA,uBACA,kBACA,wBACA,uBACA,wBACA,gBACA,sBACA,yBACA,sBACA,cACA,eACA,mBACA,gBACA,iBACA,cACA,iBACA,0BACA,MACA,YACA,gBACA,mBACA,kBACA,aACA,mBACA,sBACA,sBACA,6BACA,eACA,iBACA,aACA,gBACA,iBACA,eACA,cACA,cACA,aACA,eACA,eACA,cACA,SACA,QACA,cACA,aACA,eACA,YACA,eACA,WAGAC,U,0GCnlBF,SAASC,EAAOC,GACd,OAAO,IAAIC,OAAOD,EAAME,QAAQ,wBAAyB,QAAS,IACpE,CAMA,SAASC,EAAOC,GACd,OAAKA,EACa,iBAAPA,EAAwBA,EAE5BA,EAAGD,OAHM,IAIlB,CAMA,SAASE,EAAUD,GACjB,OAAOE,EAAO,MAAOF,EAAI,IAC3B,CAMA,SAASE,IAAgB,2BAANC,EAAI,yBAAJA,EAAI,gBACrB,IAAMC,EAASD,EAAKE,KAAI,SAACC,GAAC,OAAKP,EAAOO,EAAE,IAAEC,KAAK,IAC/C,OAAOH,CACT,CAMA,SAASI,EAAqBL,GAC5B,IAAMM,EAAON,EAAKA,EAAKO,OAAS,GAEhC,MAAoB,iBAATD,GAAqBA,EAAKE,cAAgBC,QACnDT,EAAKU,OAAOV,EAAKO,OAAS,EAAG,GACtBD,GAEA,CAAC,CAEZ,CAWA,SAASK,IAAgB,2BAANX,EAAI,yBAAJA,EAAI,gBAErB,IAAMM,EAAOD,EAAqBL,GAC5BC,EAAS,KACVK,EAAKM,QAAU,GAAK,MACrBZ,EAAKE,KAAI,SAACC,GAAC,OAAKP,EAAOO,EAAE,IAAEC,KAAK,KAAO,IAC3C,OAAOH,CACT,CCnEA,IAAMY,EAAW,2BACXC,EAAW,CACf,KACA,KACA,KACA,KACA,MACA,QACA,UACA,MACA,MACA,WACA,KACA,SACA,OACA,OACA,QACA,QACA,aACA,OACA,QACA,OACA,UACA,MACA,SACA,WACA,SACA,SACA,MACA,QACA,QACA,QAIA,WACA,QACA,QACA,SACA,SACA,OACA,SACA,WAEIC,EAAW,CACf,OACA,QACA,OACA,YACA,MACA,YAIIC,EAAQ,CAEZ,SACA,WACA,UACA,SAEA,OACA,OACA,SACA,SAEA,SACA,SAEA,QACA,eACA,eACA,YACA,aACA,oBACA,aACA,aACA,cACA,cACA,gBACA,iBAEA,MACA,MACA,UACA,UAEA,cACA,oBACA,UACA,WACA,OAEA,UACA,YACA,oBACA,gBAEA,UACA,QAEA,OAEA,eAGIC,EAAc,CAClB,QACA,YACA,gBACA,aACA,iBACA,cACA,YACA,YAGIC,EAAmB,CACvB,cACA,aACA,gBACA,eAEA,UACA,UAEA,OACA,WACA,QACA,aACA,WACA,YACA,qBACA,YACA,qBACA,SACA,YAGIC,EAAqB,CACzB,YACA,OACA,QACA,UACA,SACA,WACA,eACA,SACA,UAGIC,EAAY,GAAGrB,OACnBmB,EACAF,EACAC,GCrIFI,EAAAA,iBAA6B,QCZ7B,SAAcC,GACZ,IAAMC,EAAQD,EAAKC,MACbC,EAAM,CAAC,EACPC,EAAa,CACjBC,MAAO,OACPC,IAAK,KACLC,SAAU,CACR,OACA,CACEF,MAAO,KACPE,SAAU,CAAEJ,MAIlBf,OAAOoB,OAAOL,EAAK,CACjBM,UAAW,WACXC,SAAU,CACR,CAAEL,MAAOH,EAAMxB,OAAO,qBAEpB,wBAEF0B,KAIJ,IAAMO,EAAQ,CACZF,UAAW,QACXJ,MAAO,OACPC,IAAK,KACLC,SAAU,CAAEN,EAAKW,mBAEbC,EAAW,CACfR,MAAO,iBACPS,OAAQ,CAAEP,SAAU,CAClBN,EAAKc,kBAAkB,CACrBV,MAAO,QACPC,IAAK,QACLG,UAAW,cAIXO,EAAe,CACnBP,UAAW,SACXJ,MAAO,IACPC,IAAK,IACLC,SAAU,CACRN,EAAKW,iBACLT,EACAQ,IAGJA,EAAMJ,SAASU,KAAKD,GACpB,IAUME,EAAa,CACjBb,MAAO,SACPC,IAAK,OACLC,SAAU,CACR,CACEF,MAAO,gBACPI,UAAW,UAEbR,EAAKkB,YACLhB,IAcEiB,EAAgBnB,EAAKoB,QAAQ,CACjCC,OAAQ,IAAF,OAZe,CACrB,OACA,OACA,MACA,KACA,MACA,MACA,OACA,OACA,QAG2BvC,KAAK,KAAI,KACpCwC,UAAW,KAEPC,EAAW,CACff,UAAW,WACXJ,MAAO,4BACPoB,aAAa,EACblB,SAAU,CAAEN,EAAKyB,QAAQzB,EAAK0B,WAAY,CAAEtB,MAAO,gBACnDkB,UAAW,GA0Pb,MAAO,CACLK,KAAM,OACNC,QAAS,CAAE,MACXC,SAAU,CACRC,SAAU,wBACVC,QA5Pa,CACf,KACA,OACA,OACA,OACA,KACA,MACA,QACA,KACA,KACA,OACA,OACA,OACA,YAgPEC,QA7Oa,CACf,OACA,SA4OEC,SAAU,GAAF,OArOY,CACtB,QACA,KACA,WACA,OACA,OACA,OACA,SACA,UACA,OACA,MACA,WACA,SACA,QACA,OACA,QACA,OACA,QACA,SAGqB,CACrB,QACA,OACA,UACA,SACA,UACA,UACA,OACA,SACA,OACA,MACA,QACA,SACA,UACA,SACA,OACA,YACA,SACA,OACA,UACA,SACA,WA6LqB,CAEjB,MACA,SA7LgB,CACpB,WACA,KACA,UACA,MACA,MACA,QACA,QACA,gBACA,WACA,UACA,eACA,YACA,aACA,YACA,WACA,UACA,aACA,OACA,UACA,SACA,SACA,SACA,UACA,KACA,KACA,QACA,YACA,SACA,QACA,UACA,UACA,OACA,OACA,QACA,MACA,SACA,OACA,QACA,QACA,SACA,SACA,QACA,SACA,SACA,OACA,UACA,SACA,aACA,SACA,UACA,WACA,QACA,OACA,SACA,QACA,QACA,WACA,UACA,OACA,MACA,WACA,aACA,QACA,OACA,cACA,UACA,SACA,QAGqB,CACrB,QACA,QACA,QACA,QACA,KACA,KACA,KACA,MACA,YACA,KACA,KACA,QACA,SACA,QACA,SACA,KACA,WACA,KACA,QACA,QACA,OACA,QACA,WACA,OACA,QACA,SACA,SACA,MACA,QACA,OACA,SACA,MACA,SACA,MACA,OACA,OACA,OACA,SACA,KACA,SACA,KACA,QACA,MACA,KACA,UACA,YACA,YACA,YACA,YACA,OACA,OACA,QACA,MACA,MACA,OACA,KACA,QACA,WACA,OACA,KACA,OACA,WACA,SACA,OACA,UACA,KACA,OACA,MACA,OACA,SAEA,SACA,SACA,KACA,OACA,UACA,OACA,QACA,QACA,UACA,QACA,WACA,SACA,MACA,WACA,SACA,MACA,QACA,OACA,SACA,OACA,MACA,OACA,UAEA,MACA,QACA,SACA,SACA,QACA,MACA,SACA,SAoBA3B,SAAU,CACRa,EACAnB,EAAKoB,UACLG,EACAN,EACAjB,EAAKkC,kBACLtB,EAxPc,CAAEuB,MAAO,kBA0PvBpB,EA7TkB,CACpBP,UAAW,GACXJ,MAAO,OAGW,CAClBI,UAAW,SACXJ,MAAO,IACPC,IAAK,KAwTHH,GAGN,ID1WAH,EAAAA,iBAA6B,UEb7B,SAAgBC,GACd,IA6IMR,EAAW,CACfuC,QAjGsB,CACtB,WACA,KACA,OACA,QACA,OACA,QACA,QACA,QACA,WACA,KACA,OACA,QACA,WACA,SACA,UACA,QACA,MACA,UACA,OACA,KACA,WACA,KACA,YACA,WACA,KACA,OACA,YACA,MACA,WACA,MACA,WACA,SACA,UACA,YACA,SACA,WACA,SACA,MACA,SACA,SACA,SACA,SACA,aACA,SACA,SACA,SACA,OACA,QACA,MACA,SACA,YACA,SACA,QACA,UACA,OACA,WACA,SAwCyBtD,OAtCC,CAC1B,MACA,QACA,MACA,YACA,QACA,QACA,KACA,aACA,SACA,OACA,MACA,SACA,QACA,OACA,OACA,OACA,MACA,SACA,MACA,UACA,KACA,KACA,UACA,UACA,SACA,SACA,MACA,YACA,UACA,MACA,OACA,QACA,OACA,UAKAwD,SA/IwB,CACxB,OACA,OACA,OACA,UACA,WACA,SACA,UACA,OACA,QACA,MACA,OACA,OACA,QACA,SACA,QACA,QACA,SACA,QACA,OACA,UA4HAD,QAzGuB,CACvB,UACA,QACA,OACA,SAuGIN,EAAa1B,EAAKyB,QAAQzB,EAAK0B,WAAY,CAAEtB,MAAO,uBACpDgC,EAAU,CACd5B,UAAW,SACXC,SAAU,CACR,CAAEL,MAAO,iBACT,CAAEA,MAAO,mEACT,CAAEA,MAAO,wFAEXkB,UAAW,GAEPe,EAAkB,CACtB7B,UAAW,SACXJ,MAAO,KACPC,IAAK,IACLC,SAAU,CAAE,CAAEF,MAAO,QAEjBkC,EAAwBtC,EAAKyB,QAAQY,EAAiB,CAAEE,QAAS,OACjE7B,EAAQ,CACZF,UAAW,QACXJ,MAAO,KACPC,IAAK,KACLwB,SAAUrC,GAENgD,EAAcxC,EAAKyB,QAAQf,EAAO,CAAE6B,QAAS,OAC7CE,EAAsB,CAC1BjC,UAAW,SACXJ,MAAO,MACPC,IAAK,IACLkC,QAAS,KACTjC,SAAU,CACR,CAAEF,MAAO,QACT,CAAEA,MAAO,QACTJ,EAAKW,iBACL6B,IAGEE,EAA+B,CACnClC,UAAW,SACXJ,MAAO,OACPC,IAAK,IACLC,SAAU,CACR,CAAEF,MAAO,QACT,CAAEA,MAAO,QACT,CAAEA,MAAO,MACTM,IAGEiC,EAAqC3C,EAAKyB,QAAQiB,EAA8B,CACpFH,QAAS,KACTjC,SAAU,CACR,CAAEF,MAAO,QACT,CAAEA,MAAO,QACT,CAAEA,MAAO,MACToC,KAGJ9B,EAAMJ,SAAW,CACfoC,EACAD,EACAJ,EACArC,EAAK4C,iBACL5C,EAAK6C,kBACLT,EACApC,EAAK8C,sBAEPN,EAAYlC,SAAW,CACrBqC,EACAF,EACAH,EACAtC,EAAK4C,iBACL5C,EAAK6C,kBACLT,EACApC,EAAKyB,QAAQzB,EAAK8C,qBAAsB,CAAEP,QAAS,QAErD,IAAMQ,EAAS,CAAEtC,SAAU,CACzBiC,EACAD,EACAJ,EACArC,EAAK4C,iBACL5C,EAAK6C,oBAGDG,EAAmB,CACvB5C,MAAO,IACPC,IAAK,IACLC,SAAU,CACR,CAAE2C,cAAe,UACjBvB,IAGEwB,EAAgBlD,EAAKT,SAAW,KAAOS,EAAKT,SAAW,aAAeS,EAAKT,SAAW,iBACtF4D,EAAgB,CAGpB/C,MAAO,IAAMJ,EAAKT,SAClB+B,UAAW,GAGb,MAAO,CACLK,KAAM,KACNC,QAAS,CACP,KACA,MAEFC,SAAUrC,EACV+C,QAAS,KACTjC,SAAU,CACRN,EAAKoD,QACH,MACA,IACA,CACE5B,aAAa,EACblB,SAAU,CACR,CACEE,UAAW,SACXC,SAAU,CACR,CACEL,MAAO,MACPkB,UAAW,GAEb,CAAElB,MAAO,kBACT,CACEA,MAAO,MACPC,IAAK,UAOjBL,EAAKqD,oBACLrD,EAAK8C,qBACL,CACEtC,UAAW,OACXJ,MAAO,IACPC,IAAK,IACLwB,SAAU,CAAEE,QAAS,wFAEvBgB,EACAX,EACA,CACEa,cAAe,kBACf3B,UAAW,EACXjB,IAAK,QACLkC,QAAS,UACTjC,SAAU,CACR,CAAE2C,cAAe,eACjBvB,EACAsB,EACAhD,EAAKqD,oBACLrD,EAAK8C,uBAGT,CACEG,cAAe,YACf3B,UAAW,EACXjB,IAAK,QACLkC,QAAS,SACTjC,SAAU,CACRoB,EACA1B,EAAKqD,oBACLrD,EAAK8C,uBAGT,CACEG,cAAe,SACf3B,UAAW,EACXjB,IAAK,QACLkC,QAAS,SACTjC,SAAU,CACRoB,EACAsB,EACAhD,EAAKqD,oBACLrD,EAAK8C,uBAGT,CAEEtC,UAAW,OACXJ,MAAO,oBACPkD,cAAc,EACdjD,IAAK,MACLkD,YAAY,EACZjD,SAAU,CACR,CACEE,UAAW,SACXJ,MAAO,IACPC,IAAK,OAIX,CAGE4C,cAAe,8BACf3B,UAAW,GAEb,CACEd,UAAW,WACXJ,MAAO,IAAM8C,EAAgB,SAAWlD,EAAKT,SAAW,wBACxDiC,aAAa,EACbnB,IAAK,WACLkD,YAAY,EACZ1B,SAAUrC,EACVc,SAAU,CAER,CACE2C,cA3UiB,CACzB,SACA,UACA,YACA,SACA,WACA,YACA,WACA,QACA,SACA,WACA,SACA,UACA,MACA,SACA,WA4T0CnE,KAAK,KACvCwC,UAAW,GAEb,CACElB,MAAOJ,EAAKT,SAAW,wBACvBiC,aAAa,EACblB,SAAU,CACRN,EAAK0B,WACLsB,GAEF1B,UAAW,GAEb,CAAEa,MAAO,QACT,CACE3B,UAAW,SACXJ,MAAO,KACPC,IAAK,KACLiD,cAAc,EACdC,YAAY,EACZ1B,SAAUrC,EACV8B,UAAW,EACXhB,SAAU,CACRyC,EACAX,EACApC,EAAK8C,uBAGT9C,EAAKqD,oBACLrD,EAAK8C,uBAGTK,GAGN,IFtXApD,EAAAA,iBAA6B,OHykB7B,SAAaC,GACX,IAAMC,EAAQD,EAAKC,MACbuD,EAlmBM,SAACxD,GACb,MAAO,CACLyD,UAAW,CACTC,MAAO,OACPtD,MAAO,cAETuD,cAAe3D,EAAK8C,qBACpBc,SAAU,CACRF,MAAO,SACPtD,MAAO,mDAETyD,kBAAmB,CACjBrD,UAAW,WACXJ,MAAO,gBAET0D,wBAAyB,CACvBJ,MAAO,gBACPtD,MAAO,KACPC,IAAK,KACLkC,QAAS,IACTjC,SAAU,CACRN,EAAK4C,iBACL5C,EAAK6C,oBAGTkB,gBAAiB,CACfL,MAAO,SACPtD,MAAOJ,EAAKgE,UAALhE,kGASPsB,UAAW,GAEb2C,aAAc,CACZzD,UAAW,OACXJ,MAAO,4BAGb,CAujBgB8D,CAAMlE,GAKdmE,EAAU,CACdnE,EAAK4C,iBACL5C,EAAK6C,mBAGP,MAAO,CACLlB,KAAM,MACNyC,kBAAkB,EAClB7B,QAAS,UACTV,SAAU,CAAEwC,iBAAkB,WAC9BC,iBAAkB,CAGhBD,iBAAkB,gBACpB/D,SAAU,CACRkD,EAAMG,cAnBY,CAAEvD,MAAO,gCAuB3BoD,EAAMO,gBACN,CACEvD,UAAW,cACXJ,MAAO,kBACPkB,UAAW,GAEb,CACEd,UAAW,iBACXJ,MAAO,6BACPkB,UAAW,GAEbkC,EAAMM,wBACN,CACEtD,UAAW,kBACXC,SAAU,CACR,CAAEL,MAAO,KAAOtC,EAAegB,KAAK,KAAO,KAC3C,CAAEsB,MAAO,SAAWrC,EAAgBe,KAAK,KAAO,OASpD0E,EAAMS,aACN,CACEzD,UAAW,YACXJ,MAAO,OAASpC,EAAWc,KAAK,KAAO,QAGzC,CACEsB,MAAO,IACPC,IAAK,QACLC,SAAU,CACRkD,EAAMG,cACNH,EAAMI,SACNJ,EAAMC,UACND,EAAMO,iBAAe,OAClBI,EAAO,CAIV,CACE/D,MAAO,mBACPC,IAAK,KACLiB,UAAW,EACXO,SAAU,CAAEI,SAAU,gBACtB3B,SAAU,GAAF,OACH6D,EAAO,CACV,CACE3D,UAAW,SAGXJ,MAAO,OACPmE,gBAAgB,EAChBhB,YAAY,MAIlBC,EAAMK,qBAGV,CACEzD,MAAOH,EAAMzB,UAAU,KACvB6B,IAAK,OACLiB,UAAW,EACXiB,QAAS,IACTjC,SAAU,CACR,CACEE,UAAW,UACXJ,MA5Fa,qBA8Ff,CACEA,MAAO,KACPmE,gBAAgB,EAChBhB,YAAY,EACZjC,UAAW,EACXO,SAAU,CACRC,SAAU,UACVC,QAtGS,kBAuGTyC,UAAW3G,EAAeiB,KAAK,MAEjCwB,SAAU,CACR,CACEF,MAAO,eACPI,UAAW,cACZ,OACE2D,EAAO,CACVX,EAAMO,qBAKd,CACEvD,UAAW,eACXJ,MAAO,OAASxC,EAAKkB,KAAK,KAAO,SAIzC,IGtsBAiB,EAAAA,iBAA6B,UFsD7B,SAAgBC,GACd,IAmEMyE,EAAoB,CAExBf,MAAO,UACPvB,MAAO,sCAyCHuC,EAAc,CAElB,OACA,OACA,QACA,OACA,QACA,QACA,QACA,SACA,SACA,MACA,OACA,QACA,SACA,YACA,aACA,UACA,QACA,SACA,UACA,SACA,OACA,SACA,OACA,SAEA,SACA,UACA,OACA,QACA,MACA,QACA,MACA,QACA,YACA,MACA,SACA,UAEA,UA4CIC,EAAe,CACnB5C,QApMe,CACf,WACA,MACA,KACA,SACA,OACA,QACA,QACA,UACA,WACA,KACA,OACA,WACA,SACA,OACA,OACA,MACA,YACA,SAEA,UACA,QACA,MACA,MACA,WACA,SACA,KACA,KACA,UACA,SACA,YACA,WACA,OACA,MACA,QACA,SACA,SACA,UACA,YACA,MAGA,KACA,OACA,KACA,WACA,UACA,SACA,MACA,SACA,SACA,SACA,OACA,KAEA,MACA,OACA,SACA,MACA,MACA,OACA,OACA,QACA,OACA,SAqIAC,QA5Ge,CACf,OACA,QACA,OACA,OACA,OACA,KACA,QACA,WACA,YACA,MACA,QAkGAC,SA5Ce,CAKf,MACA,MACA,QACA,UACA,OACA,eACA,MACA,MACA,OACA,SACA,SACA,YACA,SACA,UACA,aACA,YACA,KACA,MACA,MACA,SACA,OACA,QACA,MACA,QACA,WACA,SACA,UACA,UACA,UACA,WACA,UACA,WACA,WACA,aAOA,oBAhG0B,CAC1B,WACA,uBACA,oBAsGImB,EAAU,CACd3C,SAAU,CALVT,EAAKoD,QAAQ,aAAc,OAAQ,CACjC9C,SAAU,CAAC,UAMXN,EAAKqD,sBAOHuB,EAAoB,CACxBlB,MAAO,WACPtD,MAAO,KACPC,IAAK,MAIDwE,EAA+B,WAC/BC,EAAsB,CAC1BpB,MAAO,SACPjD,SAAU,CAER,CAAE0B,MAAO1D,EAAOoG,EAA8B,YAE9C,CAAE1C,MAAO1D,EAAOoG,EAA8B7E,EAAK+E,uBAErDzD,UAAW,GAGP0D,EAAmB,SAAH,GAA8B,IAE9CC,EAEFA,EAJ4C,EAAZC,aAIb,kBAEA,iBACrB,I,EAAMC,EAAiBC,MAAMC,KAAKJ,GAC5BK,EAAmB7G,EAAM,cAAC,KAAG,O,+CAAK0G,EAAevG,IAAIV,K,gkBAAO,CAAE,OAE9DqH,EAA0BlG,EAAOiG,EAAkB,MAEnDE,EAAqC/G,EAAO8G,EAAyB/G,EAAU+G,IAC/EE,EAAuBpG,EAC3BZ,EAAO+G,EAAoCD,EAAyB,KACpE9G,EAAO6G,EAAkB,MAE3B,MAAO,CACL5B,MAAO,WACPvB,MAAO9C,EAELoG,EAGA,OACA,MACA,KACA,KACA,MACA,MACFnE,UAAW,EAEf,EAEMoE,EAAWV,EAAiB,CAAEE,cAAc,IAE5CS,EAAyBX,EAAiB,CAAEE,cAAc,IAE1DU,EAAyB,SAASC,EAAQC,GAC9C,MAAO,CACL1F,MAAO3B,EACLoH,EACArH,EACEC,EACE,MACAY,EACE,KACA,IACA,KACA,IACA,KACA,KACA,UAER0G,WAAYD,EAMZzF,IAAK7B,EACHa,EACE,KACA,MACJiC,UAAW,EAEXO,SAAU7B,EAAKyB,QAAQkD,EAAc,CAAEqB,KAAMtB,IAC7CpE,SAAU,CACR8C,EACA0B,EACA9E,EAAKyB,QAAQmD,EAAmB,CAAElB,MAAO,OACzCiC,GAGN,EAEMM,EAAkBL,EAAuB,IAAK,YAC9CM,EAAsCN,EAAuB,SAAU,WAGvEO,EAAmB,CACvB/F,MAAO,CACL,UACA,OACA,MAzGkB,oBA4GpB2F,WAAY,CACV,EAAG,UACH,EAAG,eAEL1F,IAAK7B,EAAU,UACfqD,SAAU8C,EACVrE,SAAU,CACR8C,EACApD,EAAKyB,QAAQmD,EAAmB,CAAElB,MAAO,OACzCoB,EACA,CAEEpB,MAAO,WACPvB,MAAO,OAET8D,IAIEG,EAAyB,CAE7B1C,MAAO,yBAEPvB,MAAO,wBAGHkE,EAAe,CAEnBjG,MAAO,CACL,OACA3B,EAAO,IAAKY,EAAM,aAzRQ,CAC5B,KACA,OACA,QACA,OACA,SACA,QACA,IACA,IACA,IACA,OACA,OACA,OACA,UA6QE,MAEF0G,WAAY,CAAE,EAAG,QACjB1F,IAAK7B,EAAU,SAKX8H,EAAS,CACb7F,SAAU,CACRT,EAAKuG,mBACLvG,EAAKwG,gBAQHC,EAAgB,CACpB/C,MAAO,SACPtD,MAAO,IACPC,IAAK,IACLC,SAAU,CACRN,EAAKW,mBAIH0B,EAAkB,CACtBqB,MAAO,SACPtD,MAAO,KACPC,IAAK,IACLC,SAAU,CACR,CACE6B,MAAO,MAETnC,EAAKW,mBAIH+F,EAAuB,CAC3BhD,MAAO,SACPtD,MAAO,MACPC,IAAK,MACLiB,UAAW,GAEPZ,EAAQ,CACZgD,MAAO,QACPtD,MAAO,KACPC,IAAK,KACLwB,SAAU8C,GAGNlC,EAAsB,CAC1BiB,MAAO,SACPtD,MAAO,MACPC,IAAK,IACLC,SAAU,CACR,CACE6B,MAAO,QAET,CACEA,MAAO,QAETnC,EAAKW,iBACLD,IAIEgC,EAA+B,CACnCgB,MAAO,SACPtD,MAAO,aACPC,IAAK,IACLC,SAAU,CACR,CACE6B,MAAO,QAET,CACEA,MAAO,QAET,CACEA,MAAO,MAETnC,EAAKW,iBACLD,IAIEiG,EAAoC,CACxCjD,MAAO,SACPtD,MAAO,QACPC,IAAK,MACLC,SAAU,CACR,CACE6B,MAAO,QAET,CACEA,MAAO,QAETzB,GAEFY,UAAW,GAGPsF,EAAe,CACnBlD,MAAO,SACPvB,MAAO1D,EACL,IACAY,EACE,SACA,8DAEF,MAkCJ,OA5BAqB,EAAMJ,SAAW,CACfoC,EACAD,EACAJ,EACAoE,EACAG,EACAnC,EACArB,EACAwB,EACAqB,EACAG,EACAC,EACAC,EACAxB,EACAY,GAcK,CACL/D,KAAM,KACNC,QAAS,CACP,KACA,MAEFC,SAAU8C,EACVpC,QAAS,OACT+B,iBAAkB,CAChB,yBAA0B,WAE5BhE,SAAU,CACRmE,EAxBW,CACbhE,SAAU,CACRkG,EACAjE,EACAD,EACAiE,EACArE,EACAoE,EACAG,IAkBAxD,EACAwB,EACAuB,EACA,CAGEzC,MAAO,OACPtD,MAAO,MACPC,IAAK,MACLiB,UAAW,EACXhB,SAAU,CACRsE,EAEA8B,EACArE,EACAoE,EACAG,EACAN,IAGJJ,EACAD,EACAG,EACAC,EACAC,EACAxB,EACAY,GAGN,IEtlBA3F,EAAAA,iBAA6B,QGjB7B,SAAcC,GACZ,IACM6G,EAAU,oBAEVC,EAAS,CACbtG,UAAW,YACXJ,MALYJ,EAAKC,MAKJxB,OAAO,IAHF,wBAGoB,cACtCoC,OAAQ,CAAEP,SAAU,CAClB,CACEE,UAAW,cACXJ,MAAO,KACPkB,UAAW,EACXT,OAAQ,CACNR,IAAK,IACLiB,UAAW,OAKbyF,EAAmB,CACvBD,EACA,CACE1G,MAAO,SACPS,OAAQ,CACNmG,YAAa,GACbzC,gBAAgB,KAKtB,MAAO,CACL5C,KAAM,OACNC,QAAS,CAAE,SACXW,QAAS,KACTjC,SAAU,CAER,CACEF,MAAO,OAASyG,EAAU,WAC1BxG,IAAK,IACLC,SAAU,CACR,CACEE,UAAW,OACXJ,MAAOyG,GAET,CACErG,UAAW,SACXJ,MAAO,iBAGXS,OAAQ,CACNR,IAAK,OACLkC,QAAS,KACTjC,SAAUyG,IAId,CACE3G,MAAO,oBAAsByG,EAAU,KACvCxG,IAAK,IACLC,SAAU,CACR,CACEE,UAAW,SACXJ,MAAO,IACPC,IAAK,IACLiD,cAAc,EACdC,YAAY,GAEd,CACE/C,UAAW,OACXJ,MAAOyG,GAET,CACErG,UAAW,UACXJ,MAAO,WAGXS,OAAQ,CACNR,IAAK,OACLkC,QAAS,KACTjC,SAAUyG,IAId/G,EAAKyB,QAAQqF,EAAQ,CAAExF,UAAW,KAGxC,IHpEAvB,EAAAA,iBAA6B,cD2I7B,SAAoBC,GAClB,IAAMC,EAAQD,EAAKC,MAcbgH,EAAa1H,EAOb2H,EAAU,CACd9G,MAAO,sBACPC,IAAK,4BAKL8G,kBAAmB,SAAChF,EAAOiF,GACzB,IA6BIC,EA7BEC,EAAkBnF,EAAM,GAAGlD,OAASkD,EAAMoF,MAC1CC,EAAWrF,EAAMsF,MAAMH,GAKd,MAAbE,GAGa,MAAbA,GAOe,MAAbA,IArCc,SAACrF,EAAO,GAAc,IAAZuF,EAAK,EAALA,MACxBC,EAAM,KAAOxF,EAAM,GAAGyF,MAAM,GAElC,OAAgB,IADJzF,EAAMsF,MAAMI,QAAQF,EAAKD,EAEvC,CAoCWI,CAAc3F,EAAO,CAAEuF,MAAOJ,KACjCF,EAASW,gBAYRV,EAFclF,EAAMsF,MAAMO,UAAUV,GAErBnF,MAAM,oBACR,IAAZkF,EAAEE,OACJH,EAASW,eAxBXX,EAASW,aA6Bb,GAEIE,EAAa,CACjBnG,SAAUvC,EACVwC,QAASvC,EACTwC,QAASvC,EACTwC,SAAUnC,EACV,oBAAqBD,GAIjBqI,EAAgB,kBAChBC,EAAO,OAAH,OAAUD,EAAa,KAG3BE,EAAiB,sCACjB9B,EAAS,CACb9F,UAAW,SACXC,SAAU,CAER,CAAEL,MAAO,eAAQgI,EAAc,cAAMD,EAAI,oBAAYA,EAAI,0BAC1CD,EAAa,SAC5B,CAAE9H,MAAO,OAAF,OAASgI,EAAc,iBAASD,EAAI,uBAAeA,EAAI,SAG9D,CAAE/H,MAAO,8BAGT,CAAEA,MAAO,4CACT,CAAEA,MAAO,gCACT,CAAEA,MAAO,gCAIT,CAAEA,MAAO,oBAEXkB,UAAW,GAGPZ,EAAQ,CACZF,UAAW,QACXJ,MAAO,SACPC,IAAK,MACLwB,SAAUoG,EACV3H,SAAU,IAEN+H,EAAgB,CACpBjI,MAAO,QACPC,IAAK,GACLQ,OAAQ,CACNR,IAAK,IACLiI,WAAW,EACXhI,SAAU,CACRN,EAAKW,iBACLD,GAEFsG,YAAa,QAGXuB,EAAe,CACnBnI,MAAO,OACPC,IAAK,GACLQ,OAAQ,CACNR,IAAK,IACLiI,WAAW,EACXhI,SAAU,CACRN,EAAKW,iBACLD,GAEFsG,YAAa,QAGXwB,EAAkB,CACtBhI,UAAW,SACXJ,MAAO,IACPC,IAAK,IACLC,SAAU,CACRN,EAAKW,iBACLD,IA0CE0C,EAAU,CACd5C,UAAW,UACXC,SAAU,CAzCUT,EAAKoD,QACzB,eACA,OACA,CACE9B,UAAW,EACXhB,SAAU,CACR,CACEF,MAAO,iBACPkB,UAAW,EACXhB,SAAU,CACR,CACEE,UAAW,SACXJ,MAAO,cAET,CACEI,UAAW,OACXJ,MAAO,MACPC,IAAK,MACLkD,YAAY,EACZD,cAAc,EACdhC,UAAW,GAEb,CACEd,UAAW,WACXJ,MAAO6G,EAAa,gBACpBwB,YAAY,EACZnH,UAAW,GAIb,CACElB,MAAO,cACPkB,UAAW,QAWnBtB,EAAK8C,qBACL9C,EAAKqD,sBAGHqF,EAAkB,CACtB1I,EAAK4C,iBACL5C,EAAK6C,kBACLwF,EACAE,EACAC,EACAlC,GAKF5F,EAAMJ,SAAWoI,EACdjK,OAAO,CAGN2B,MAAO,KACPC,IAAK,KACLwB,SAAUoG,EACV3H,SAAU,CACR,QACA7B,OAAOiK,KAEb,IA4HgBC,EA5HVC,EAAqB,GAAGnK,OAAO2E,EAAS1C,EAAMJ,UAC9CuI,EAAkBD,EAAmBnK,OAAO,CAEhD,CACE2B,MAAO,KACPC,IAAK,KACLwB,SAAUoG,EACV3H,SAAU,CAAC,QAAQ7B,OAAOmK,MAGxBE,EAAS,CACbtI,UAAW,SACXJ,MAAO,KACPC,IAAK,KACLiD,cAAc,EACdC,YAAY,EACZ1B,SAAUoG,EACV3H,SAAUuI,GAINE,EAAmB,CACvBtI,SAAU,CAER,CACE0B,MAAO,CACL,QACA,MACA8E,EACA,MACA,UACA,MACAhH,EAAMxB,OAAOwI,EAAY,IAAKhH,EAAMxB,OAAO,KAAMwI,GAAa,OAEhEvD,MAAO,CACL,EAAG,UACH,EAAG,cACH,EAAG,UACH,EAAG,0BAIP,CACEvB,MAAO,CACL,QACA,MACA8E,GAEFvD,MAAO,CACL,EAAG,UACH,EAAG,kBAOLsF,EAAkB,CACtB1H,UAAW,EACXa,MACAlC,EAAMZ,OAEJ,SAEA,iCAEA,6CAEA,oDAMFmB,UAAW,cACXqB,SAAU,CACRoH,EAAG,GAAF,OAEIvJ,EACAC,KAYHuJ,EAAsB,CAC1BzI,SAAU,CACR,CACE0B,MAAO,CACL,WACA,MACA8E,EACA,cAIJ,CACE9E,MAAO,CACL,WACA,eAIN3B,UAAW,CACT,EAAG,UACH,EAAG,kBAEL2I,MAAO,WACP7I,SAAU,CAAEwI,GACZvG,QAAS,KAaL6G,EAAgB,CACpBjH,MAAOlC,EAAMxB,OACX,MANYkK,EAOL,GAAD,OACD/I,EAAgB,CACnB,UARGK,EAAMxB,OAAO,MAAOkK,EAAK7J,KAAK,KAAM,MAUzCmI,EAAYhH,EAAMzB,UAAU,OAC9BgC,UAAW,iBACXc,UAAW,GAGP+H,EAAkB,CACtBjJ,MAAOH,EAAMxB,OAAO,KAAMwB,EAAMzB,UAC9ByB,EAAMxB,OAAOwI,EAAY,wBAE3B5G,IAAK4G,EACL3D,cAAc,EACdzB,SAAU,YACVrB,UAAW,WACXc,UAAW,GAGPgI,EAAmB,CACvBnH,MAAO,CACL,UACA,MACA8E,EACA,UAEFzG,UAAW,CACT,EAAG,UACH,EAAG,kBAELF,SAAU,CACR,CACEF,MAAO,QAET0I,IAIES,EAAkB,2DAMbvJ,EAAK+E,oBAAsB,UAEhCyE,EAAoB,CACxBrH,MAAO,CACL,gBAAiB,MACjB8E,EAAY,MACZ,OACA,cACAhH,EAAMzB,UAAU+K,IAElB1H,SAAU,QACVrB,UAAW,CACT,EAAG,UACH,EAAG,kBAELF,SAAU,CACRwI,IAIJ,MAAO,CACLnH,KAAM,aACNC,QAAS,CAAC,KAAM,MAAO,MAAO,OAC9BC,SAAUoG,EAEVwB,QAAS,CAAEZ,kBAAiBG,mBAC5BzG,QAAS,eACTjC,SAAU,CACRN,EAAKoB,QAAQ,CACX+H,MAAO,UACP9H,OAAQ,OACRC,UAAW,IA3HE,CACjB6H,MAAO,aACP3I,UAAW,OACXc,UAAW,GACXlB,MAAO,gCA0HLJ,EAAK4C,iBACL5C,EAAK6C,kBACLwF,EACAE,EACAC,EACApF,EACAkD,EACA0C,EACA,CACExI,UAAW,OACXJ,MAAO6G,EAAahH,EAAMzB,UAAU,KACpC8C,UAAW,GAEbkI,EACA,CACEpJ,MAAO,IAAMJ,EAAK0J,eAAiB,kCACnC7H,SAAU,oBACVP,UAAW,EACXhB,SAAU,CACR8C,EACApD,EAAK2J,YACL,CACEnJ,UAAW,WAIXJ,MAAOmJ,EACP/H,aAAa,EACbnB,IAAK,SACLC,SAAU,CACR,CACEE,UAAW,SACXC,SAAU,CACR,CACEL,MAAOJ,EAAK+E,oBACZzD,UAAW,GAEb,CACEd,UAAW,KACXJ,MAAO,UACPwJ,MAAM,GAER,CACExJ,MAAO,KACPC,IAAK,KACLiD,cAAc,EACdC,YAAY,EACZ1B,SAAUoG,EACV3H,SAAUuI,OAMpB,CACEzI,MAAO,IACPkB,UAAW,GAEb,CACEa,MAAO,MACPb,UAAW,GAEb,CACEb,SAAU,CACR,CAAEL,MA5dL,KA4d4BC,IA3d9B,OA4dK,CAAE8B,MAzdW,6BA0db,CACE/B,MAAO8G,EAAQ9G,MAGf,WAAY8G,EAAQC,kBACpB9G,IAAK6G,EAAQ7G,MAGjB2G,YAAa,MACb1G,SAAU,CACR,CACEF,MAAO8G,EAAQ9G,MACfC,IAAK6G,EAAQ7G,IACbuJ,MAAM,EACNtJ,SAAU,CAAC,aAMrB4I,EACA,CAGEjG,cAAe,6BAEjB,CAIE7C,MAAO,kBAAoBJ,EAAK+E,oBAAzB,gEAQPvD,aAAY,EACZ2H,MAAO,WACP7I,SAAU,CACRwI,EACA9I,EAAKyB,QAAQzB,EAAK0B,WAAY,CAAEtB,MAAO6G,EAAYzG,UAAW,qBAIlE,CACE2B,MAAO,SACPb,UAAW,GAEb+H,EAIA,CACElH,MAAO,MAAQ8E,EACf3F,UAAW,GAEb,CACEa,MAAO,CAAE,0BACT3B,UAAW,CAAE,EAAG,kBAChBF,SAAU,CAAEwI,IAEdM,EA7NwB,CAC1B9H,UAAW,EACXa,MAAO,sBACP3B,UAAW,qBA4NTuI,EACAO,EACA,CACEnH,MAAO,WAIf,ICjsBApC,EAAAA,iBAA6B,QInB7B,SAAcC,GACZ,IAUMP,EAAW,CACf,OACA,QACA,QAOIoK,EAAgB,CACpBnG,MAAO,UACPT,cAAexD,EAASX,KAAK,MAG/B,MAAO,CACL6C,KAAM,OACNE,SAAS,CACPG,QAASvC,GAEXa,SAAU,CA9BM,CAChBE,UAAW,OACXJ,MAAO,8BACPkB,UAAW,MAEO,CAClBa,MAAO,YACP3B,UAAW,cACXc,UAAW,GAyBTtB,EAAK6C,kBACLgH,EACA7J,EAAKwG,cACLxG,EAAKqD,oBACLrD,EAAK8C,sBAEPP,QAAS,MAEb,IJtBAxC,EAAAA,iBAA6B,SKpB7B,SAAeC,GAOb,IAAM8J,EAAmB,uDAoTnBtK,EAAW,CACfsC,SAAUgI,EACV/H,QA1SmB,CACnB,aACA,QACA,QACA,QACA,QACA,QACA,WACA,KACA,OACA,SACA,MACA,SACA,QACA,UACA,MACA,WACA,SACA,KACA,SACA,KACA,MACA,MACA,QACA,QACA,SACA,QACA,SACA,OACA,MACA,QACA,QACA,SA2QAC,QAzPmB,CACnB,OACA,SACA,aACA,aACA,MACA,MACA,QACA,QACA,QACA,gBACA,YACA,YACA,MACA,QACA,QACA,QACA,eACA,YACA,YACA,gBACA,eACA,uBACA,qBACA,cACA,UACA,YACA,UACA,QACA,KACA,UACA,UACA,KACA,SACA,QACA,SACA,OACA,QACA,IACA,KAmNAC,SAjMoB,CACpB,gBACA,kBACA,eACA,eACA,kBACA,gBACA,qBACA,iBACA,gBACA,cACA,iBACA,oBACA,mBACA,iBACA,MACA,gBACA,QACA,iBACA,WACA,SACA,WACA,YACA,SACA,YACA,OACA,cACA,oBACA,iBACA,mBACA,QACA,UACA,SACA,UACA,OACA,OACA,YACA,QACA,YACA,MACA,QACA,UACA,aACA,aACA,aACA,qBACA,YACA,aACA,SACA,UACA,WACA,UACA,SACA,QACA,aACA,SACA,aACA,UACA,QACA,WACA,WACA,WACA,aACA,cACA,gBACA,cACA,OACA,oBACA,OACA,cACA,cACA,WACA,OACA,iBACA,YACA,qBACA,OACA,UACA,UACA,UACA,WACA,YACA,OACA,KACA,WACA,YACA,WACA,SACA,iBACA,cACA,aACA,eACA,YACA,MACA,SACA,QACA,QACA,QACA,OACA,UACA,qBACA,wBACA,aACA,WACA,WACA,iBACA,gBACA,YACA,OACA,SACA,SACA,cACA,UACA,mBACA,SACA,SACA,aACA,UACA,SACA,eACA,mBACA,gBACA,OACA,mBACA,oBACA,OACA,yBACA,MACA,YACA,WACA,QACA,sBACA,OACA,gBACA,MACA,QACA,aACA,eACA,oBACA,MACA,SACA,OACA,qBACA,YACA,eACA,eACA,gBACA,kBACA,gBACA,SACA,mBACA,WACA,YACA,qBACA,SACA,cACA,OACA,sBACA,OACA,cACA,QACA,QACA,OACA,YACA,UACA,OACA,UACA,SACA,SACA,SACA,QACA,mBACA,oBACA,gBACA,gBACA,QACA,WACA,YACA,WACA,MACA,SACA,aACA,WACA,SACA,gBACA,cACA,YAWI8H,EAAU,CACdlI,SAAUrC,EACV+C,QAAS,OAsBLyH,EAAgB,CACpBxJ,UAAW,QACXJ,MAAO,OACPC,IAAK,KACLwB,SAAUrC,GAGNyK,EAAwB,CAC5BzJ,UAAW,WACXJ,MAAO,MAAQ0J,GAIX/G,EAAS,CACbvC,UAAW,SACXF,SAAU,CACRN,EAAKW,iBACLqJ,EACAC,GAEFxJ,SAAU,CACR,CACEL,MAAO,SACPC,IAAK,SACLiB,UAAW,IAEb,CACElB,MAAO,OACPC,IAAK,UAKL6J,EAAU,CACd1J,UAAW,SACXF,SAAU,CACRN,EAAKW,iBACLqJ,EACAC,GAEF7J,MAAO,IACPC,IAAK,KAGD8J,EAAY,CAChB3J,UAAW,OACXJ,MAAO,IAAM0J,GAoCf,OAlBAC,EAAQpI,KAAO,QACfoI,EAAQzJ,SAAW,CAnFJ,CACbE,UAAW,SAQXJ,MAAO,qIACPkB,UAAW,GAGA,CACXd,UAAW,SACXJ,MAAO,8BAuEP2C,EACAmH,EACAC,EArBc,CACd3J,UAAW,UACXC,SAAU,CACR,CACEL,MAAO,KACPC,IAAK,KACLiB,UAAW,IAEb,CACElB,MAAO,IACPC,IAAK,OAaTL,EAAKkC,kBACL,CACE1B,UAAW,UACXJ,MACE,+DAEJ,CAAEA,MAAO,OAEX4J,EAAc1J,SAAWyJ,EAAQzJ,SAE1ByJ,CACT,ILzZAhK,EAAAA,iBAA6B,YMrB7B,SAAkBC,GAChB,IACMoK,EAAc,CAClBhK,MAAO,gBACPC,IAAK,IACL2G,YAAa,MACb1F,UAAW,GA8DP+I,EAAO,CACX5J,SAAU,CAGR,CACEL,MAAO,iBACPkB,UAAW,GAGb,CACElB,MAAO,gEACPkB,UAAW,GAEb,CACElB,MAjFQJ,EAAKC,MAiFAxB,OAAO,YAfP,0BAegC,cAC7C6C,UAAW,GAGb,CACElB,MAAO,wBACPkB,UAAW,GAGb,CACElB,MAAO,iBACPkB,UAAW,IAGfE,aAAa,EACblB,SAAU,CACR,CAEE6B,MAAO,YACT,CACE3B,UAAW,SACXc,UAAW,EACXlB,MAAO,MACPC,IAAK,MACLiD,cAAc,EACdgF,WAAW,GAEb,CACE9H,UAAW,OACXc,UAAW,EACXlB,MAAO,SACPC,IAAK,MACLiD,cAAc,EACdC,YAAY,GAEd,CACE/C,UAAW,SACXc,UAAW,EACXlB,MAAO,SACPC,IAAK,MACLiD,cAAc,EACdC,YAAY,KAIZ+G,EAAO,CACX9J,UAAW,SACXF,SAAU,GACVG,SAAU,CACR,CACEL,MAAO,OACPC,IAAK,QAEP,CACED,MAAO,QACPC,IAAK,WAILkK,EAAS,CACb/J,UAAW,WACXF,SAAU,GACVG,SAAU,CACR,CACEL,MAAO,WACPC,IAAK,MAEP,CACED,MAAO,SACPC,IAAK,IACLiB,UAAW,KAQXkJ,EAAsBxK,EAAKyB,QAAQ6I,EAAM,CAAEhK,SAAU,KACrDmK,EAAsBzK,EAAKyB,QAAQ8I,EAAQ,CAAEjK,SAAU,KAC7DgK,EAAKhK,SAASU,KAAKyJ,GACnBF,EAAOjK,SAASU,KAAKwJ,GAErB,IAAIE,EAAc,CAChBN,EACAC,GA2CF,MAxCA,CACEC,EACAC,EACAC,EACAC,GACAE,SAAQ,SAAAtD,GACRA,EAAE/G,SAAW+G,EAAE/G,SAAS7B,OAAOiM,EACjC,IAiCO,CACL/I,KAAM,WACNC,QAAS,CACP,KACA,SACA,OAEFtB,SAAU,CApCG,CACbE,UAAW,UACXC,SAAU,CACR,CACEL,MAAO,UACPC,IAAK,IACLC,SARNoK,EAAcA,EAAYjM,OAAO6L,EAAMC,IAUnC,CACEnK,MAAO,uBACPE,SAAU,CACR,CAAEF,MAAO,WACT,CACEA,MAAO,IACPC,IAAK,MACLC,SAAUoK,OAuBhBN,EAjLS,CACX5J,UAAW,SACXJ,MAAO,mCACPC,IAAK,OACLkD,YAAY,GA+KV+G,EACAC,EAnBe,CACjB/J,UAAW,QACXJ,MAAO,SACPE,SAAUoK,EACVrK,IAAK,KAnMM,CACXG,UAAW,OACXC,SAAU,CAER,CAAEL,MAAO,iCACT,CAAEA,MAAO,iCAET,CACEA,MAAO,MACPC,IAAK,aAEP,CACED,MAAO,MACPC,IAAK,aAEP,CAAED,MAAO,SACT,CACEA,MAAO,kBAGPE,SAAU,CACR,CACEF,MAAO,cACPC,IAAK,WAGTiB,UAAW,KA9BO,CACtBlB,MAAO,cACPC,IAAK,KAwNHgK,EAlLmB,CACrBjK,MAAO,eACPoB,aAAa,EACblB,SAAU,CACR,CACEE,UAAW,SACXJ,MAAO,KACPC,IAAK,KACLiD,cAAc,EACdC,YAAY,GAEd,CACE/C,UAAW,OACXJ,MAAO,OACPC,IAAK,IACLiD,cAAc,MAuKtB,INhNAvD,EAAAA,iBAA6B,UOlB7B,SAAgBC,GACd,IACM4K,EAAY,CAChBtJ,UAAW,EACXhB,SAAU,CAAE,CAAEF,MAHK,eAMrB,MAAO,CACLuB,KAAM,SACNE,SAAU,CACRE,QACE,qLAEFE,SACE,swCAiBJM,QAAS,0BACTjC,SAAU,CACR,CACEE,UAAW,WACXyC,cAAe,WACf5C,IAAK,IACLC,SAAU,CACRN,EAAK6K,sBACL,CACErK,UAAW,SACXC,SAAU,CACR,CACEL,MAAO,MACPC,IAAK,OAEP,CACED,MAAO,MACPC,IAAK,WAMf,CACEG,UAAW,WACXJ,MAAO,aACPkB,UAAW,EACXT,OAAQ+J,GAEV,CACExK,MAAO,iCACPkB,UAAW,GAEb,CACEd,UAAW,SACXJ,MAAOJ,EAAK8K,YACZxJ,UAAW,EACXT,OAAQ+J,GAEV,CACEpK,UAAW,SACXJ,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE,CAAEF,MAAO,QAEvB,CACEA,MAAO,WACPkB,UAAW,EACXT,OAAQ+J,GAEV,CACEpK,UAAW,SACXJ,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE,CAAEF,MAAO,OACrBS,OAAQ+J,GAEV5K,EAAKoD,QAAQ,iBAAkB,kBAC/BpD,EAAKoD,QAAQ,IAAK,MAGxB,IPzEArD,EAAAA,iBAA6B,aQxB7B,SAAmBC,GACjB,MAAO,CACL2B,KAAM,aACNC,QAAS,CACP,OACA,OAEFmJ,mBAAmB,EAEvB,IRgBAhL,EAAAA,iBAA6B,USzB7B,SAAgBC,GACd,IAAMC,EAAQD,EAAKC,MACbV,EAAW,o0iBACXyL,EAAiB,CACrB,MACA,KACA,SACA,QACA,QACA,QACA,OACA,QACA,WACA,MACA,MACA,OACA,OACA,SACA,UACA,MACA,OACA,SACA,KACA,SACA,KACA,KACA,SACA,QACA,cACA,MACA,KACA,OACA,QACA,SACA,MACA,QACA,OACA,SAuGIxL,EAAW,CACfsC,SAAU,sBACVC,QAASiJ,EACT/I,SAvGgB,CAChB,aACA,MACA,MACA,MACA,QACA,MACA,OACA,aACA,YACA,QACA,WACA,MACA,cACA,UACA,UACA,UACA,OACA,MACA,SACA,YACA,OACA,OACA,SACA,QACA,SACA,YACA,UACA,UACA,UACA,OACA,OACA,MACA,KACA,QACA,MACA,aACA,aACA,OACA,MACA,OACA,SACA,MACA,MACA,aACA,MACA,OACA,SACA,MACA,OACA,MACA,MACA,QACA,WACA,QACA,OACA,WACA,QACA,MACA,UACA,QACA,SACA,eACA,MACA,MACA,QACA,QACA,OACA,OACA,OAmCAD,QAhCe,CACf,YACA,WACA,QACA,OACA,iBACA,QA2BAgE,KArBY,CACZ,MACA,WACA,YACA,OACA,OACA,UACA,UACA,WACA,WACA,MACA,QACA,OACA,UAWIiF,EAAS,CACbzK,UAAW,OACXJ,MAAO,kBAGHM,EAAQ,CACZF,UAAW,QACXJ,MAAO,KACPC,IAAK,KACLwB,SAAUrC,EACV+C,QAAS,KAGL2I,EAAkB,CACtB9K,MAAO,OACPkB,UAAW,GAGPyB,EAAS,CACbvC,UAAW,SACXF,SAAU,CAAEN,EAAKW,kBACjBF,SAAU,CACR,CACEL,MAAO,yCACPC,IAAK,MACLC,SAAU,CACRN,EAAKW,iBACLsK,GAEF3J,UAAW,IAEb,CACElB,MAAO,yCACPC,IAAK,MACLC,SAAU,CACRN,EAAKW,iBACLsK,GAEF3J,UAAW,IAEb,CACElB,MAAO,8BACPC,IAAK,MACLC,SAAU,CACRN,EAAKW,iBACLsK,EACAC,EACAxK,IAGJ,CACEN,MAAO,8BACPC,IAAK,MACLC,SAAU,CACRN,EAAKW,iBACLsK,EACAC,EACAxK,IAGJ,CACEN,MAAO,eACPC,IAAK,IACLiB,UAAW,IAEb,CACElB,MAAO,eACPC,IAAK,IACLiB,UAAW,IAEb,CACElB,MAAO,4BACPC,IAAK,KAEP,CACED,MAAO,4BACPC,IAAK,KAEP,CACED,MAAO,4BACPC,IAAK,IACLC,SAAU,CACRN,EAAKW,iBACLuK,EACAxK,IAGJ,CACEN,MAAO,4BACPC,IAAK,IACLC,SAAU,CACRN,EAAKW,iBACLuK,EACAxK,IAGJV,EAAK4C,iBACL5C,EAAK6C,oBAKHsI,EAAY,kBACZC,EAAa,QAAH,OAAWD,EAAS,kBAAUA,EAAS,iBAASA,EAAS,QAMnE3M,EAAY,OAAH,OAAUwM,EAAelM,KAAK,MACvCwH,EAAS,CACb9F,UAAW,SACXc,UAAW,EACXb,SAAU,CAWR,CACEL,MAAO,QAAF,OAAU+K,EAAS,cAAMC,EAAU,uBAAeD,EAAS,oBAAY3M,EAAS,MAEvF,CACE4B,MAAO,IAAF,OAAMgL,EAAU,WASvB,CACEhL,MAAO,0CAAF,OAA4C5B,EAAS,MAE5D,CACE4B,MAAO,4BAAF,OAA8B5B,EAAS,MAE9C,CACE4B,MAAO,6BAAF,OAA+B5B,EAAS,MAE/C,CACE4B,MAAO,mCAAF,OAAqC5B,EAAS,MAKrD,CACE4B,MAAO,OAAF,OAAS+K,EAAS,mBAAW3M,EAAS,QAI3C6M,EAAe,CACnB7K,UAAW,UACXJ,MAAOH,EAAMzB,UAAU,WACvB6B,IAAK,IACLwB,SAAUrC,EACVc,SAAU,CACR,CACEF,MAAO,WAGT,CACEA,MAAO,IACPC,IAAK,OACLkE,gBAAgB,KAIhBuE,EAAS,CACbtI,UAAW,SACXC,SAAU,CAER,CACED,UAAW,GACXJ,MAAO,UACPwJ,MAAM,GAER,CACExJ,MAAO,KACPC,IAAK,KACLiD,cAAc,EACdC,YAAY,EACZ1B,SAAUrC,EACVc,SAAU,CACR,OACA2K,EACA3E,EACAvD,EACA/C,EAAKkC,sBAWb,OANAxB,EAAMJ,SAAW,CACfyC,EACAuD,EACA2E,GAGK,CACLtJ,KAAM,SACNC,QAAS,CACP,KACA,MACA,WAEF0J,cAAc,EACdzJ,SAAUrC,EACV+C,QAAS,iBACTjC,SAAU,CACR2K,EACA3E,EACA,CAEElG,MAAO,YAET,CAGE6C,cAAe,KACf3B,UAAW,GAEbyB,EACAsI,EACArL,EAAKkC,kBACL,CACEC,MAAO,CACL,QAAS,MACT5C,GAEFmE,MAAO,CACL,EAAG,UACH,EAAG,kBAELpD,SAAU,CAAEwI,IAEd,CACErI,SAAU,CACR,CACE0B,MAAO,CACL,UAAW,MACX5C,EAAU,MACV,QAASA,EAAS,UAGtB,CACE4C,MAAO,CACL,UAAW,MACX5C,KAINmE,MAAO,CACL,EAAG,UACH,EAAG,cACH,EAAG,0BAGP,CACElD,UAAW,OACXJ,MAAO,WACPC,IAAK,UACLC,SAAU,CACRgG,EACAwC,EACA/F,KAKV,IT9YAhD,EAAAA,iBAA6B,KUvB7B,SAAWC,GACT,IAAMC,EAAQD,EAAKC,MAObV,EAAW,uDACXgM,EAAkBtL,EAAMZ,OAE5B,gDAEA,0CAEA,iDAEImM,EAAe,mEACfC,EAAiBxL,EAAMZ,OAC3B,OACA,OACA,OACA,QACA,KACA,KAGF,MAAO,CACLsC,KAAM,IAENE,SAAU,CACRC,SAAUvC,EACVwC,QACE,kDACFC,QACE,wFAEFC,SAEE,khCAuBJ3B,SAAU,CAERN,EAAKoD,QACH,KACA,IACA,CAAE9C,SAAU,CACV,CAMEoD,MAAO,SACPvB,MAAO,YACPtB,OAAQ,CACNR,IAAKJ,EAAMzB,UAAUyB,EAAMZ,OAEzB,yBAEA,cAEFoJ,YAAY,IAGhB,CAGE/E,MAAO,SACPtD,MAAO,SACPC,IAAK,IACLC,SAAU,CACR,CACEoD,MAAO,WACPjD,SAAU,CACR,CAAE0B,MAAO5C,GACT,CAAE4C,MAAO,sBAEXsG,YAAY,KAIlB,CACE/E,MAAO,SACPvB,MAAO,cAET,CACEuB,MAAO,UACPvB,MAAO,kBAKbnC,EAAKkC,kBAEL,CACEwB,MAAO,SACPpD,SAAU,CAAEN,EAAKW,kBACjBF,SAAU,CACRT,EAAKc,kBAAkB,CACrBV,MAAO,cACPC,IAAK,YAEPL,EAAKc,kBAAkB,CACrBV,MAAO,cACPC,IAAK,YAEPL,EAAKc,kBAAkB,CACrBV,MAAO,cACPC,IAAK,YAEPL,EAAKc,kBAAkB,CACrBV,MAAO,cACPC,IAAK,YAEPL,EAAKc,kBAAkB,CACrBV,MAAO,cACPC,IAAK,YAEPL,EAAKc,kBAAkB,CACrBV,MAAO,cACPC,IAAK,YAEP,CACED,MAAO,IACPC,IAAK,IACLiB,UAAW,GAEb,CACElB,MAAO,IACPC,IAAK,IACLiB,UAAW,KAcjB,CACEA,UAAW,EACXb,SAAU,CACR,CACEiD,MAAO,CACL,EAAG,WACH,EAAG,UAELvB,MAAO,CACLqJ,EACAD,IAGJ,CACE7H,MAAO,CACL,EAAG,WACH,EAAG,UAELvB,MAAO,CACL,UACAoJ,IAGJ,CACE7H,MAAO,CACL,EAAG,cACH,EAAG,UAELvB,MAAO,CACLsJ,EACAF,IAGJ,CACE7H,MAAO,CAAE,EAAG,UACZvB,MAAO,CACL,mBACAoJ,MAOR,CAEE7H,MAAO,CAAE,EAAG,YACZvB,MAAO,CACL5C,EACA,MACA,KACA,QAIJ,CACEmE,MAAO,WACPpC,UAAW,EACXb,SAAU,CACR,CAAE0B,MAAOqJ,GACT,CAAErJ,MAAO,aAIb,CACEuB,MAAO,cACPpC,UAAW,EACXa,MAAOsJ,GAGT,CAEErL,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE,CAAEF,MAAO,UAI7B,IV5NAL,EAAAA,iBAA6B,QWzB7B,SAAcC,GACZ,IAAMC,EAAQD,EAAKC,MACbyL,EAAiB,qFAEjBC,EAAgB1L,EAAMZ,OAC1B,uBAEA,8BAGIuM,EAA+B3L,EAAMxB,OAAOkN,EAAe,YAC3DE,EAAgB,CACpB,oBAAqB,CACnB,WACA,YAEF,oBAAqB,CACnB,OACA,SAEF9J,QAAS,CACP,QACA,MACA,gBACA,cACA,cACA,QACA,QACA,QACA,OACA,QACA,UACA,KACA,OACA,QACA,MACA,MACA,SACA,MACA,KACA,KACA,UACA,SACA,OACA,MACA,KACA,OACA,UACA,SACA,QACA,SACA,OACA,QACA,SACA,QACA,OACA,QACA,SAEFE,SAAU,CACR,OACA,UAEFD,QAAS,CACP,OACA,QACA,QAGE8J,EAAY,CAChBtL,UAAW,SACXJ,MAAO,cAEH2L,EAAa,CACjB3L,MAAO,KACPC,IAAK,KAED2L,EAAgB,CACpBhM,EAAKoD,QACH,IACA,IACA,CAAE9C,SAAU,CAAEwL,KAEhB9L,EAAKoD,QACH,UACA,QACA,CACE9C,SAAU,CAAEwL,GACZxK,UAAW,KAGftB,EAAKoD,QAAQ,WAAYpD,EAAKiM,mBAE1BvL,EAAQ,CACZF,UAAW,QACXJ,MAAO,MACPC,IAAK,KACLwB,SAAUgK,GAEN9I,EAAS,CACbvC,UAAW,SACXF,SAAU,CACRN,EAAKW,iBACLD,GAEFD,SAAU,CACR,CACEL,MAAO,IACPC,IAAK,KAEP,CACED,MAAO,IACPC,IAAK,KAEP,CACED,MAAO,IACPC,IAAK,KAEP,CACED,MAAO,cACPC,IAAK,MAEP,CACED,MAAO,cACPC,IAAK,MAEP,CACED,MAAO,cACPC,IAAK,MAEP,CACED,MAAO,aACPC,IAAK,KAEP,CACED,MAAO,cACPC,IAAK,MAEP,CACED,MAAO,aACPC,IAAK,KAEP,CACED,MAAO,aACPC,IAAK,KAEP,CACED,MAAO,cACPC,IAAK,MAIP,CAAED,MAAO,mBACT,CAAEA,MAAO,6BACT,CAAEA,MAAO,mCACT,CAAEA,MAAO,2DACT,CAAEA,MAAO,2BACT,CAAEA,MAAO,aAET,CAGEA,MAAOH,EAAMxB,OACX,YACAwB,EAAMzB,UAAU,6CAElB8B,SAAU,CACRN,EAAKc,kBAAkB,CACrBV,MAAO,QACPC,IAAK,QACLC,SAAU,CACRN,EAAKW,iBACLD,SAYNwL,EAAS,kBACT5F,EAAS,CACb9F,UAAW,SACXc,UAAW,EACXb,SAAU,CAER,CAAEL,MAAO,OAAF,OAPK,oBAOW,iBAAS8L,EAAM,yBAAiBA,EAAM,eAI7D,CAAE9L,MAAO,kCACT,CAAEA,MAAO,kCACT,CAAEA,MAAO,kCACT,CAAEA,MAAO,8CAGT,CAAEA,MAAO,2BAIP0I,EAAS,CACbrI,SAAU,CACR,CACE0B,MAAO,QAET,CACE3B,UAAW,SACXJ,MAAO,KACPC,IAAK,SACLiD,cAAc,EACdmF,YAAY,EACZ5G,SAAUgK,KA4DVM,EAAwB,CAC5BpJ,EAxDuB,CACvBtC,SAAU,CACR,CACE0B,MAAO,CACL,WACAyJ,EACA,UACAA,IAGJ,CACEzJ,MAAO,CACL,WACAyJ,KAINlI,MAAO,CACL,EAAG,cACH,EAAG,yBAEL7B,SAAUgK,GAuBY,CACtBvK,UAAW,EACXa,MAAO,CACLyJ,EACA,aAEFlI,MAAO,CACL,EAAG,gBA3BqB,CAC1BpC,UAAW,EACXa,MAAO,sBACP3B,UAAW,qBAGa,CACxB2B,MAAO,CACL,MAAO,MACPuJ,GAEFhI,MAAO,CACL,EAAG,UACH,EAAG,kBAELpD,SAAU,CACRwI,IAqBF,CAEE1I,MAAOJ,EAAKT,SAAW,MACzB,CACEiB,UAAW,SACXJ,MAAOJ,EAAK+E,oBAAsB,YAClCzD,UAAW,GAEb,CACEd,UAAW,SACXJ,MAAO,WACPE,SAAU,CACRyC,EACA,CAAE3C,MAAOsL,IAEXpK,UAAW,GAEbgF,EACA,CAGE9F,UAAW,WACXJ,MAAO,8DAET,CACEI,UAAW,SACXJ,MAAO,KACPC,IAAK,KACLiD,cAAc,EACdC,YAAY,EACZjC,UAAW,EACXO,SAAUgK,GAEZ,CACEzL,MAAO,IAAMJ,EAAK0J,eAAiB,eACnC7H,SAAU,SACVvB,SAAU,CACR,CACEE,UAAW,SACXF,SAAU,CACRN,EAAKW,iBACLD,GAEF6B,QAAS,KACT9B,SAAU,CACR,CACEL,MAAO,IACPC,IAAK,WAEP,CACED,MAAO,OACPC,IAAK,YAEP,CACED,MAAO,QACPC,IAAK,aAEP,CACED,MAAO,MACPC,IAAK,WAEP,CACED,MAAO,QACPC,IAAK,gBAIX5B,OAAOsN,EAAYC,GACrB1K,UAAW,IAEb7C,OAAOsN,EAAYC,GAErBtL,EAAMJ,SAAW6L,EACjBrD,EAAOxI,SAAW6L,EAIlB,IAKMC,EAAc,CAClB,CACEhM,MAAO,SACPS,OAAQ,CACNR,IAAK,IACLC,SAAU6L,IAGd,CACE3L,UAAW,cACXJ,MAAO,iGACPS,OAAQ,CACNR,IAAK,IACLwB,SAAUgK,EACVvL,SAAU6L,KAOhB,OAFAH,EAAcK,QAAQN,GAEf,CACLpK,KAAM,OACNC,QAAS,CACP,KACA,UACA,UACA,OACA,OAEFC,SAAUgK,EACVtJ,QAAS,OACTjC,SAAU,CAAEN,EAAKoB,QAAQ,CAAEC,OAAQ,UAChC5C,OAAO2N,GACP3N,OAAOuN,GACPvN,OAAO0N,GAEd,IXtXApM,EAAAA,iBAA6B,SY1B7B,SAAeC,GACb,MAAO,CACL2B,KAAM,gBACNC,QAAS,CACP,UACA,gBAEFtB,SAAU,CACR,CACEE,UAAW,cAIXJ,MAAO,qCACPS,OAAQ,CACNR,IAAK,gBACL2G,YAAa,UAKvB,IZMAjH,EAAAA,iBAA6B,Oad7B,SAAaC,GACX,IAAMC,EAAQD,EAAKC,MACbqM,EAAetM,EAAKoD,QAAQ,KAAM,KAiBlC3D,EAAW,CACf,OACA,QAGA,WAUIC,EAAQ,CACZ,SACA,SACA,OACA,UACA,OACA,YACA,OACA,OACA,MACA,WACA,UACA,QACA,MACA,UACA,WACA,QACA,QACA,WACA,UACA,OACA,MACA,WACA,OACA,YACA,UACA,UACA,aAmYI6M,EAAqB,CACzB,MACA,OACA,YACA,OACA,OACA,MACA,OACA,OACA,UACA,WACA,OACA,MACA,OACA,QACA,YACA,aACA,YACA,aACA,QACA,UACA,MACA,UACA,cACA,QACA,aACA,gBACA,cACA,cACA,iBACA,aACA,aACA,uBACA,aACA,MACA,aACA,OACA,UACA,KACA,MACA,QACA,QACA,MACA,MACA,MACA,YACA,QACA,SACA,eACA,kBACA,kBACA,WACA,iBACA,QACA,OACA,YACA,YACA,aACA,iBACA,UACA,aACA,WACA,WACA,WACA,aACA,MACA,OACA,OACA,aACA,cACA,YACA,kBACA,MACA,MACA,OACA,YACA,kBACA,QACA,OACA,aACA,SACA,QACA,WACA,UACA,WACA,gBAwBIC,EAAS,CACb,eACA,cACA,cACA,cACA,WACA,cACA,iBACA,gBACA,cACA,gBACA,gBACA,eACA,cACA,aACA,cACA,iBAGIC,EAAYF,EAEZ/M,EAAW,UAtfM,CACrB,MACA,OACA,MACA,WACA,QACA,MACA,MACA,MACA,QACA,YACA,wBACA,KACA,aACA,OACA,aACA,KACA,OACA,SACA,gBACA,MACA,QACA,cACA,kBACA,UACA,SACA,SACA,OACA,UACA,OACA,KACA,OACA,SACA,cACA,WACA,OACA,OACA,OACA,UACA,OACA,cACA,YACA,mBACA,QACA,aACA,OACA,QACA,WACA,UACA,UACA,SACA,SACA,YACA,UACA,aACA,WACA,UACA,OACA,OACA,gBACA,MACA,OACA,QACA,YACA,aACA,SACA,QACA,OACA,YACA,UACA,kBACA,eACA,kCACA,eACA,eACA,cACA,iBACA,eACA,oBACA,eACA,eACA,mCACA,eACA,SACA,QACA,OACA,MACA,aACA,MACA,UACA,WACA,UACA,UACA,SACA,SACA,aACA,QACA,WACA,gBACA,aACA,WACA,SACA,OACA,UACA,OACA,UACA,OACA,QACA,MACA,YACA,gBACA,WACA,SACA,SACA,QACA,SACA,OACA,UACA,SACA,MACA,WACA,UACA,QACA,QACA,SACA,cACA,QACA,QACA,MACA,UACA,YACA,OACA,OACA,OACA,WACA,SACA,MACA,SACA,QACA,QACA,WACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,UACA,QACA,QACA,cACA,SACA,MACA,UACA,YACA,eACA,WACA,OACA,KACA,OACA,aACA,gBACA,cACA,cACA,iBACA,aACA,aACA,uBACA,aACA,MACA,WACA,QACA,aACA,UACA,OACA,UACA,OACA,OACA,aACA,UACA,KACA,QACA,YACA,iBACA,MACA,QACA,QACA,QACA,eACA,kBACA,UACA,MACA,SACA,QACA,SACA,MACA,SACA,MACA,WACA,SACA,QACA,WACA,WACA,UACA,QACA,QACA,MACA,KACA,OACA,YACA,MACA,YACA,QACA,OACA,SACA,UACA,eACA,oBACA,KACA,SACA,MACA,OACA,KACA,MACA,OACA,OACA,KACA,QACA,MACA,QACA,OACA,WACA,UACA,YACA,YACA,UACA,MACA,UACA,eACA,kBACA,kBACA,SACA,UACA,WACA,iBACA,QACA,WACA,YACA,UACA,UACA,YACA,MACA,QACA,OACA,QACA,OACA,YACA,MACA,aACA,cACA,YACA,YACA,aACA,iBACA,UACA,aACA,WACA,WACA,WACA,UACA,SACA,SACA,UACA,SACA,QACA,WACA,SACA,MACA,aACA,OACA,UACA,YACA,QACA,SACA,SACA,SACA,OACA,SACA,YACA,eACA,MACA,OACA,UACA,MACA,OACA,OACA,WACA,OACA,WACA,eACA,MACA,eACA,WACA,aACA,OACA,QACA,SACA,aACA,cACA,cACA,SACA,YACA,kBACA,WACA,MACA,YACA,SACA,cACA,cACA,QACA,cACA,MACA,OACA,OACA,OACA,YACA,gBACA,kBACA,KACA,WACA,YACA,kBACA,cACA,QACA,UACA,OACA,aACA,OACA,WACA,UACA,QACA,SACA,UACA,SACA,SACA,QACA,OACA,QACA,QACA,SACA,WACA,UACA,WACA,YACA,UACA,UACA,aACA,OACA,WACA,QACA,eACA,SACA,OACA,SACA,UACA,QA1XyB,CACzB,MACA,MACA,YACA,OACA,QACA,QACA,OACA,SA6fAkN,QAAO,SAAC3K,GACR,OAAQwK,EAAmBI,SAAS5K,EACtC,IAaMqH,EAAgB,CACpBhJ,MAAOH,EAAMxB,OAAO,KAAMwB,EAAMZ,OAAM,MAAZY,EAAgBwM,GAAY,SACtDnL,UAAW,EACXO,SAAU,CAAEI,SAAUwK,IAoBxB,MAAO,CACL9K,KAAM,MACNyC,kBAAkB,EAElB7B,QAAS,WACTV,SAAU,CACRC,SAAU,YACVC,QAvBJ,SAAyB4G,GAEjB,6DAAJ,CAAC,EADHiE,EAAU,EAAVA,WAAYC,EAAI,EAAJA,KAENC,EAAYD,EAElB,OADAD,EAAaA,GAAc,GACpBjE,EAAK/J,KAAI,SAACmO,GACf,OAAIA,EAAK5K,MAAM,WAAayK,EAAWD,SAASI,GACvCA,EACED,EAAUC,GACZ,GAAP,OAAUA,EAAI,MAEPA,CAEX,GACF,CAUMC,CAAgBxN,EAAU,CAAEqN,KAAM,SAAChO,GAAC,OAAKA,EAAEI,OAAS,CAAC,IACvD+C,QAASvC,EACTuG,KAAMtG,EACNuC,SA7F4B,CAC9B,kBACA,eACA,kCACA,eACA,eACA,iBACA,mCACA,eACA,eACA,cACA,cACA,eACA,YACA,oBACA,mBAgFA3B,SAAU,CACR,CACEF,MAAOH,EAAMZ,OAAM,MAAZY,EAAgBuM,GACvBlL,UAAW,EACXO,SAAU,CACRC,SAAU,UACVC,QAASvC,EAASf,OAAO+N,GACzBxK,QAASvC,EACTuG,KAAMtG,IAGV,CACEc,UAAW,OACXJ,MAAOH,EAAMZ,OAAM,MAAZY,EA1mBY,CACvB,mBACA,eACA,gBACA,sBAwmBEmJ,EA9Da,CACf5I,UAAW,WACXJ,MAAO,cAxkBM,CACbI,UAAW,SACXC,SAAU,CACR,CACEL,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE,CAAEF,MAAO,UAID,CACxBA,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE,CAAEF,MAAO,QA2nBnBJ,EAAKwG,cACLxG,EAAK8C,qBACLwJ,EA/Da,CACf9L,UAAW,WACXJ,MAAO,gDACPkB,UAAW,IAgEf,IbloBAvB,EAAAA,iBAA6B,Oc7B7B,SAAaC,GACX,IAAMC,EAAQD,EAAKC,MAQbgN,EAAchN,EAAMxB,OAAO,20PAAawB,EAAMiN,SAAS,u1PAAqB,s1PAE5EC,EAAe,CACnB3M,UAAW,SACXJ,MAAO,oCAEHgN,EAAoB,CACxBhN,MAAO,KACPE,SAAU,CACR,CACEE,UAAW,UACXJ,MAAO,sBACPmC,QAAS,QAIT8K,EAAwBrN,EAAKyB,QAAQ2L,EAAmB,CAC5DhN,MAAO,KACPC,IAAK,OAEDiN,EAAwBtN,EAAKyB,QAAQzB,EAAK4C,iBAAkB,CAAEpC,UAAW,WACzE+M,EAAyBvN,EAAKyB,QAAQzB,EAAK6C,kBAAmB,CAAErC,UAAW,WAC3EgN,EAAgB,CACpBjJ,gBAAgB,EAChBhC,QAAS,IACTjB,UAAW,EACXhB,SAAU,CACR,CACEE,UAAW,OACXJ,MA5Be,q1PA6BfkB,UAAW,GAEb,CACElB,MAAO,OACPkB,UAAW,EACXhB,SAAU,CACR,CACEE,UAAW,SACXiI,YAAY,EACZhI,SAAU,CACR,CACEL,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE6M,IAEd,CACE/M,MAAO,IACPC,IAAK,IACLC,SAAU,CAAE6M,IAEd,CAAE/M,MAAO,sBAOrB,MAAO,CACLuB,KAAM,YACNC,QAAS,CACP,OACA,QACA,MACA,OACA,MACA,MACA,MACA,QACA,MACA,OAEFwC,kBAAkB,EAClBkH,cAAc,EACdhL,SAAU,CACR,CACEE,UAAW,OACXJ,MAAO,UACPC,IAAK,IACLiB,UAAW,GACXhB,SAAU,CACR8M,EACAG,EACAD,EACAD,EACA,CACEjN,MAAO,KACPC,IAAK,KACLC,SAAU,CACR,CACEE,UAAW,OACXJ,MAAO,UACPC,IAAK,IACLC,SAAU,CACR8M,EACAC,EACAE,EACAD,QAOZtN,EAAKoD,QACH,OACA,MACA,CAAE9B,UAAW,KAEf,CACElB,MAAO,cACPC,IAAK,QACLiB,UAAW,IAEb6L,EAEA,CACE3M,UAAW,OACXH,IAAK,MACLI,SAAU,CACR,CACEL,MAAO,SACPkB,UAAW,GACXhB,SAAU,CACRiN,IAGJ,CACEnN,MAAO,uBAKb,CACEI,UAAW,MAMXJ,MAAO,iBACPC,IAAK,IACLwB,SAAU,CAAEF,KAAM,SAClBrB,SAAU,CAAEkN,GACZ3M,OAAQ,CACNR,IAAK,YACLiI,WAAW,EACXtB,YAAa,CACX,MACA,SAIN,CACExG,UAAW,MAEXJ,MAAO,kBACPC,IAAK,IACLwB,SAAU,CAAEF,KAAM,UAClBrB,SAAU,CAAEkN,GACZ3M,OAAQ,CACNR,IAAK,aACLiI,WAAW,EACXtB,YAAa,CACX,aACA,aACA,SAKN,CACExG,UAAW,MACXJ,MAAO,WAGT,CACEI,UAAW,MACXJ,MAAOH,EAAMxB,OACX,IACAwB,EAAMzB,UAAUyB,EAAMxB,OACpBwO,EAIAhN,EAAMZ,OAAO,MAAO,IAAK,SAG7BgB,IAAK,OACLC,SAAU,CACR,CACEE,UAAW,OACXJ,MAAO6M,EACP3L,UAAW,EACXT,OAAQ2M,KAKd,CACEhN,UAAW,MACXJ,MAAOH,EAAMxB,OACX,MACAwB,EAAMzB,UAAUyB,EAAMxB,OACpBwO,EAAa,OAGjB3M,SAAU,CACR,CACEE,UAAW,OACXJ,MAAO6M,EACP3L,UAAW,GAEb,CACElB,MAAO,IACPkB,UAAW,EACXmH,YAAY,MAMxB,IdxMA1I,EAAAA,iBAA6B,Qe7B7B,SAAcC,GACZ,IAAMP,EAAW,yBAGXgO,EAAiB,8BA8BjB1K,EAAS,CACbvC,UAAW,SACXc,UAAW,EACXb,SAAU,CACR,CACEL,MAAO,IACPC,IAAK,KAEP,CACED,MAAO,IACPC,IAAK,KAEP,CAAED,MAAO,QAEXE,SAAU,CACRN,EAAKW,iBA5BkB,CACzBH,UAAW,oBACXC,SAAU,CACR,CACEL,MAAO,OACPC,IAAK,QAEP,CACED,MAAO,MACPC,IAAK,UA0BLqN,EAAmB1N,EAAKyB,QAAQsB,EAAQ,CAAEtC,SAAU,CACxD,CACEL,MAAO,IACPC,IAAK,KAEP,CACED,MAAO,IACPC,IAAK,KAEP,CAAED,MAAO,mBAYLuN,EAAkB,CACtBtN,IAAK,IACLkE,gBAAgB,EAChBhB,YAAY,EACZ1B,SAAUpC,EACV6B,UAAW,GAEPsM,EAAS,CACbxN,MAAO,KACPC,IAAK,KACLC,SAAU,CAAEqN,GACZpL,QAAS,MACTjB,UAAW,GAEPuM,EAAQ,CACZzN,MAAO,MACPC,IAAK,MACLC,SAAU,CAAEqN,GACZpL,QAAS,MACTjB,UAAW,GAGP4C,EAAQ,CAzFF,CACV1D,UAAW,OACXC,SAAU,CACR,CAAEL,MAAO,gCACT,CACEA,MAAO,kCACT,CACEA,MAAO,oCAoFX,CACEI,UAAW,OACXJ,MAAO,YACPkB,UAAW,IAEb,CAKEd,UAAW,SACXJ,MAAO,iEAET,CACEA,MAAO,WACPC,IAAK,UACL2G,YAAa,OACb1D,cAAc,EACdC,YAAY,EACZjC,UAAW,GAEb,CACEd,UAAW,OACXJ,MAAO,SAAWqN,GAGpB,CACEjN,UAAW,OACXJ,MAAO,KAAOqN,EAAiB,KAEjC,CACEjN,UAAW,OACXJ,MAAO,IAAMqN,GAEf,CACEjN,UAAW,OACXJ,MAAO,KAAOqN,GAEhB,CACEjN,UAAW,OACXJ,MAAO,IAAMJ,EAAK+E,oBAAsB,KAE1C,CACEvE,UAAW,OACXJ,MAAO,MAAQJ,EAAK+E,oBAAsB,KAE5C,CACEvE,UAAW,SAEXJ,MAAO,aACPkB,UAAW,GAEbtB,EAAKkC,kBACL,CACEe,cAAexD,EACfoC,SAAU,CAAEG,QAASvC,IApFP,CAChBe,UAAW,SACXJ,MAAO,iIAuFP,CACEI,UAAW,SACXJ,MAAOJ,EAAK8K,YAAc,MAC1BxJ,UAAW,GAEbsM,EACAC,EACA9K,GAGI+K,EAAc,GAAH,OAAQ5J,GAKzB,OAJA4J,EAAYC,MACZD,EAAY9M,KAAK0M,GACjBC,EAAgBrN,SAAWwN,EAEpB,CACLnM,KAAM,OACNyC,kBAAkB,EAClBxC,QAAS,CAAE,OACXtB,SAAU4D,EAEd,IfxJA,O,qEgBpCI8J,E,MAA0B,GAA4B,KAE1DA,EAAwBhN,KAAK,CAACiN,EAAOC,GAAI,yyCAA0yC,GAAG,CAAC,QAAU,EAAE,QAAU,CAAC,2DAA2D,MAAQ,GAAG,SAAW,wXAAwX,eAAiB,CAAC,0yCAA0yC,WAAa,MAEhoG,K,gCCDAD,EAAOxE,QAAU,SAAU0E,GACzB,IAAIxF,EAAO,GA6FX,OA3FAA,EAAKyF,SAAW,WACd,OAAOC,KAAKzP,KAAI,SAAUmO,GACxB,IAAIuB,EAAU,GACVC,OAA+B,IAAZxB,EAAK,GA4B5B,OA1BIA,EAAK,KACPuB,GAAW,cAAc7P,OAAOsO,EAAK,GAAI,QAGvCA,EAAK,KACPuB,GAAW,UAAU7P,OAAOsO,EAAK,GAAI,OAGnCwB,IACFD,GAAW,SAAS7P,OAAOsO,EAAK,GAAG9N,OAAS,EAAI,IAAIR,OAAOsO,EAAK,IAAM,GAAI,OAG5EuB,GAAWH,EAAuBpB,GAE9BwB,IACFD,GAAW,KAGTvB,EAAK,KACPuB,GAAW,KAGTvB,EAAK,KACPuB,GAAW,KAGNA,CACT,IAAGxP,KAAK,GACV,EAGA6J,EAAK6F,EAAI,SAAWC,EAASC,EAAOC,EAAQC,EAAUC,GAC7B,iBAAZJ,IACTA,EAAU,CAAC,CAAC,KAAMA,OAASK,KAG7B,IAAIC,EAAyB,CAAC,EAE9B,GAAIJ,EACF,IAAK,IAAIK,EAAI,EAAGA,EAAIX,KAAKpP,OAAQ+P,IAAK,CACpC,IAAId,EAAKG,KAAKW,GAAG,GAEP,MAANd,IACFa,EAAuBb,IAAM,EAEjC,CAGF,IAAK,IAAIe,EAAK,EAAGA,EAAKR,EAAQxP,OAAQgQ,IAAM,CAC1C,IAAIlC,EAAO,GAAGtO,OAAOgQ,EAAQQ,IAEzBN,GAAUI,EAAuBhC,EAAK,WAIrB,IAAV8B,SACc,IAAZ9B,EAAK,KAGdA,EAAK,GAAK,SAAStO,OAAOsO,EAAK,GAAG9N,OAAS,EAAI,IAAIR,OAAOsO,EAAK,IAAM,GAAI,MAAMtO,OAAOsO,EAAK,GAAI,MAF/FA,EAAK,GAAK8B,GAOVH,IACG3B,EAAK,IAGRA,EAAK,GAAK,UAAUtO,OAAOsO,EAAK,GAAI,MAAMtO,OAAOsO,EAAK,GAAI,KAC1DA,EAAK,GAAK2B,GAHV3B,EAAK,GAAK2B,GAOVE,IACG7B,EAAK,IAGRA,EAAK,GAAK,cAActO,OAAOsO,EAAK,GAAI,OAAOtO,OAAOsO,EAAK,GAAI,KAC/DA,EAAK,GAAK6B,GAHV7B,EAAK,GAAK,GAAGtO,OAAOmQ,IAOxBjG,EAAK3H,KAAK+L,GACZ,CACF,EAEOpE,CACT,C,gCCnGAsF,EAAOxE,QAAU,SAAUsD,GACzB,IAAIuB,EAAUvB,EAAK,GACfmC,EAAanC,EAAK,GAEtB,IAAKmC,EACH,OAAOZ,EAGT,GAAoB,mBAATa,KAAqB,CAC9B,IAAIC,EAASD,KAAKE,SAASC,mBAAmBC,KAAKC,UAAUN,MACzDO,EAAO,+DAA+DhR,OAAO2Q,GAC7EM,EAAgB,OAAOjR,OAAOgR,EAAM,OACpCE,EAAaT,EAAWU,QAAQhR,KAAI,SAAUN,GAChD,MAAO,iBAAiBG,OAAOyQ,EAAWW,YAAc,IAAIpR,OAAOH,EAAQ,MAC7E,IACA,MAAO,CAACgQ,GAAS7P,OAAOkR,GAAYlR,OAAO,CAACiR,IAAgB5Q,KAAK,KACnE,CAEA,MAAO,CAACwP,GAASxP,KAAK,KACxB,C,gCCnBA,IAAIgR,EAAc,GAElB,SAASC,EAAqBC,GAG5B,IAFA,IAAIC,GAAU,EAELzB,EAAI,EAAGA,EAAIsB,EAAY7Q,OAAQuP,IACtC,GAAIsB,EAAYtB,GAAGwB,aAAeA,EAAY,CAC5CC,EAASzB,EACT,KACF,CAGF,OAAOyB,CACT,CAEA,SAASC,EAAavH,EAAMrL,GAI1B,IAHA,IAAI6S,EAAa,CAAC,EACdC,EAAc,GAET5B,EAAI,EAAGA,EAAI7F,EAAK1J,OAAQuP,IAAK,CACpC,IAAIzB,EAAOpE,EAAK6F,GACZN,EAAK5Q,EAAQ+S,KAAOtD,EAAK,GAAKzP,EAAQ+S,KAAOtD,EAAK,GAClDuD,EAAQH,EAAWjC,IAAO,EAC1B8B,EAAa,GAAGvR,OAAOyP,EAAI,KAAKzP,OAAO6R,GAC3CH,EAAWjC,GAAMoC,EAAQ,EACzB,IAAIC,EAAoBR,EAAqBC,GACzCQ,EAAM,CACRC,IAAK1D,EAAK,GACV2B,MAAO3B,EAAK,GACZ2D,UAAW3D,EAAK,GAChB6B,SAAU7B,EAAK,GACf8B,MAAO9B,EAAK,IAGd,IAA2B,IAAvBwD,EACFT,EAAYS,GAAmBI,aAC/Bb,EAAYS,GAAmBK,QAAQJ,OAClC,CACL,IAAII,EAAUC,EAAgBL,EAAKlT,GACnCA,EAAQwT,QAAUtC,EAClBsB,EAAY1Q,OAAOoP,EAAG,EAAG,CACvBwB,WAAYA,EACZY,QAASA,EACTD,WAAY,GAEhB,CAEAP,EAAYpP,KAAKgP,EACnB,CAEA,OAAOI,CACT,CAEA,SAASS,EAAgBL,EAAKlT,GAC5B,IAAIyT,EAAMzT,EAAQI,OAAOJ,GAezB,OAdAyT,EAAIC,OAAOR,GAEG,SAAiBS,GAC7B,GAAIA,EAAQ,CACV,GAAIA,EAAOR,MAAQD,EAAIC,KAAOQ,EAAOvC,QAAU8B,EAAI9B,OAASuC,EAAOP,YAAcF,EAAIE,WAAaO,EAAOrC,WAAa4B,EAAI5B,UAAYqC,EAAOpC,QAAU2B,EAAI3B,MACzJ,OAGFkC,EAAIC,OAAOR,EAAMS,EACnB,MACEF,EAAIG,QAER,CAGF,CAEAjD,EAAOxE,QAAU,SAAUd,EAAMrL,GAG/B,IAAI6T,EAAkBjB,EADtBvH,EAAOA,GAAQ,GADfrL,EAAUA,GAAW,CAAC,GAGtB,OAAO,SAAgB8T,GACrBA,EAAUA,GAAW,GAErB,IAAK,IAAI5C,EAAI,EAAGA,EAAI2C,EAAgBlS,OAAQuP,IAAK,CAC/C,IACIjH,EAAQwI,EADKoB,EAAgB3C,IAEjCsB,EAAYvI,GAAOoJ,YACrB,CAIA,IAFA,IAAIU,EAAqBnB,EAAakB,EAAS9T,GAEtCgU,EAAK,EAAGA,EAAKH,EAAgBlS,OAAQqS,IAAM,CAClD,IAEIC,EAASxB,EAFKoB,EAAgBG,IAIK,IAAnCxB,EAAYyB,GAAQZ,aACtBb,EAAYyB,GAAQX,UAEpBd,EAAY1Q,OAAOmS,EAAQ,GAE/B,CAEAJ,EAAkBE,CACpB,CACF,C,+BCrGA,IAAIG,EAAO,CAAC,EAoCZvD,EAAOxE,QAVP,SAA0BhM,EAAQgU,GAChC,IAAIC,EAxBN,SAAmBA,GACjB,QAA4B,IAAjBF,EAAKE,GAAyB,CACvC,IAAIC,EAAcC,SAASC,cAAcH,GAEzC,GAAII,OAAOC,mBAAqBJ,aAAuBG,OAAOC,kBAC5D,IAGEJ,EAAcA,EAAYK,gBAAgBC,IAI5C,CAHE,MAAOC,GAEPP,EAAc,IAChB,CAGFH,EAAKE,GAAUC,CACjB,CAEA,OAAOH,EAAKE,EACd,CAKeS,CAAU1U,GAEvB,IAAKiU,EACH,MAAM,IAAIU,MAAM,2GAGlBV,EAAOW,YAAYZ,EACrB,C,gCC1BAxD,EAAOxE,QAPP,SAA4BnM,GAC1B,IAAIgV,EAAUV,SAASW,cAAc,SAGrC,OAFAjV,EAAQE,cAAc8U,EAAShV,EAAQkV,YACvClV,EAAQG,OAAO6U,EAAShV,EAAQA,SACzBgV,CACT,C,oCCGArE,EAAOxE,QARP,SAAwCgJ,GACtC,IAAIC,EAAmD,KAEnDA,GACFD,EAAaE,aAAa,QAASD,EAEvC,C,gCC4DAzE,EAAOxE,QAZP,SAAgBnM,GACd,IAAImV,EAAenV,EAAQK,mBAAmBL,GAC9C,MAAO,CACL0T,OAAQ,SAAgBR,IAzD5B,SAAeiC,EAAcnV,EAASkT,GACpC,IAAIC,EAAM,GAEND,EAAI5B,WACN6B,GAAO,cAAchS,OAAO+R,EAAI5B,SAAU,QAGxC4B,EAAI9B,QACN+B,GAAO,UAAUhS,OAAO+R,EAAI9B,MAAO,OAGrC,IAAIH,OAAiC,IAAdiC,EAAI3B,MAEvBN,IACFkC,GAAO,SAAShS,OAAO+R,EAAI3B,MAAM5P,OAAS,EAAI,IAAIR,OAAO+R,EAAI3B,OAAS,GAAI,OAG5E4B,GAAOD,EAAIC,IAEPlC,IACFkC,GAAO,KAGLD,EAAI9B,QACN+B,GAAO,KAGLD,EAAI5B,WACN6B,GAAO,KAGT,IAAIC,EAAYF,EAAIE,UAEhBA,GAA6B,oBAATvB,OACtBsB,GAAO,uDAAuDhS,OAAO0Q,KAAKE,SAASC,mBAAmBC,KAAKC,UAAUkB,MAAe,QAMtIpT,EAAQC,kBAAkBkT,EAAKgC,EAAcnV,EAAQA,QACvD,CAiBMsV,CAAMH,EAAcnV,EAASkT,EAC/B,EACAU,OAAQ,YAjBZ,SAA4BuB,GAE1B,GAAgC,OAA5BA,EAAaI,WACf,OAAO,EAGTJ,EAAaI,WAAWC,YAAYL,EACtC,CAWMM,CAAmBN,EACrB,EAEJ,C,gCCpDAxE,EAAOxE,QAZP,SAA2BgH,EAAKgC,GAC9B,GAAIA,EAAaO,WACfP,EAAaO,WAAWC,QAAUxC,MAC7B,CACL,KAAOgC,EAAaS,YAClBT,EAAaK,YAAYL,EAAaS,YAGxCT,EAAaJ,YAAYT,SAASuB,eAAe1C,GACnD,CACF,C,olHCbA,IAAI2C,EAAgB,CAAC3J,QAAS,CAAC,GAE/B,SAAS4J,EAAW7C,GAuBhB,OAtBIA,aAAe8C,IACf9C,EAAI+C,MAAQ/C,EAAIgD,OAAShD,EAAIiD,IAAM,WAC/B,MAAM,IAAIrB,MAAM,mBACpB,EACO5B,aAAekD,MACtBlD,EAAImD,IAAMnD,EAAI+C,MAAQ/C,EAAIgD,OAAS,WAC/B,MAAM,IAAIpB,MAAM,mBACpB,GAIJjT,OAAOyU,OAAOpD,GAEdrR,OAAO0U,oBAAoBrD,GAAK7F,SAAQ,SAAUhJ,GAC9C,IAAImS,EAAOtD,EAAI7O,GAGI,iBAARmS,GAAqB3U,OAAO4U,SAASD,IAC5CT,EAAWS,EAEnB,IAEOtD,CACX,CAEA4C,EAAc3J,QAAU4J,EACxBD,EAAc3J,QAAQuK,QAAUX,EAIhC,IAEMY,EAAQ,WAIZ,WAAYC,GAAM,eAEEpF,IAAdoF,EAAKzE,OAAoByE,EAAKzE,KAAO,CAAC,GAE1CpB,KAAKoB,KAAOyE,EAAKzE,KACjBpB,KAAK8F,gBAAiB,CACxB,CAIC,OAJA,8BAED,WACE9F,KAAK8F,gBAAiB,CACxB,KAAC,EAdW,GAqBd,SAASC,EAAWjW,GAClB,OAAOA,EACJE,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,UACdA,QAAQ,KAAM,SACnB,CAUA,SAASgW,EAAUC,GAEjB,IAAMrE,EAAS9Q,OAAOoV,OAAO,MAE7B,IAAK,IAAMC,KAAOF,EAChBrE,EAAOuE,GAAOF,EAASE,GACxB,2BAN6BC,EAAO,iCAAPA,EAAO,kBAYrC,OALAA,EAAQ9J,SAAQ,SAAS6F,GACvB,IAAK,IAAMgE,KAAOhE,EAChBP,EAAOuE,GAAOhE,EAAIgE,EAEtB,IACO,CACT,CAcA,IAMME,EAAoB,SAACC,GAGzB,QAASA,EAAKjR,OAAUiR,EAAKC,aAAeD,EAAKE,QACnD,EAmBMC,EAAY,WAOhB,WAAYC,EAAWzX,GAAS,UAC9B+Q,KAAK2G,OAAS,GACd3G,KAAK4G,YAAc3X,EAAQ2X,YAC3BF,EAAUG,KAAK7G,KACjB,CAmDC,OAjDD,0BAIA,SAAQ8G,GACN9G,KAAK2G,QAAUZ,EAAWe,EAC5B,GAEA,sBAIA,SAASR,GACP,GAAKD,EAAkBC,GAAvB,CAEA,IAAInU,EAEFA,EADEmU,EAAKC,YACK,YAAH,OAAeD,EAAKE,UA1CX,SAAClT,EAAM,GAAe,IAAbkE,EAAM,EAANA,OAC/B,GAAIlE,EAAKgL,SAAS,KAAM,CACtB,IAAMyI,EAASzT,EAAK0T,MAAM,KAC1B,MAAO,CAAC,GAAD,OACFxP,GAAM,OAAGuP,EAAOE,UAAO,SACtBF,EAAOxW,KAAI,SAACC,EAAG2P,GAAC,gBAAQ3P,GAAC,OAAG,IAAI0W,OAAO/G,EAAI,GAAE,MACjD1P,KAAK,IACT,CACA,MAAO,GAAP,OAAU+G,GAAM,OAAGlE,EACrB,CAmCkB6T,CAAgBb,EAAKjR,MAAO,CAAEmC,OAAQwI,KAAK4G,cAEzD5G,KAAKoH,KAAKjV,EAR0B,CAStC,GAEA,uBAIA,SAAUmU,GACHD,EAAkBC,KAEvBtG,KAAK2G,QAzEU,UA0EjB,GAEA,mBAGA,WACE,OAAO3G,KAAK2G,MACd,GAIA,kBAIA,SAAKxU,GACH6N,KAAK2G,QAAU,gBAAJ,OAAoBxU,EAAS,KAC1C,KAAC,EA9De,GAuEZkV,EAAU,WAAe,IAAd1W,EAAO,UAAH,6CAAG,CAAC,EAEjBiR,EAAS,CAAE0F,SAAU,IAE3B,OADAxW,OAAOoB,OAAO0P,EAAQjR,GACfiR,CACT,EA6GM2F,EAAgB,8BAIpB,WAAYtY,GAAS,MAEI,OAFJ,WACnB,gBACKA,QAAUA,EAAQ,CACzB,CA0CC,OAxCD,6BAIA,SAAW6X,EAAMzR,GACF,KAATyR,IAEJ9G,KAAKwH,SAASnS,GACd2K,KAAKyH,QAAQX,GACb9G,KAAK0H,YACP,GAEA,qBAGA,SAAQZ,GACO,KAATA,GAEJ9G,KAAKsF,IAAIwB,EACX,GAEA,4BAIA,SAAea,EAASrU,GAEtB,IAAMgT,EAAOqB,EAAQC,KACrBtB,EAAKC,aAAc,EACnBD,EAAKE,SAAWlT,EAChB0M,KAAKsF,IAAIgB,EACX,GAAC,oBAED,WAEE,OADiB,IAAIG,EAAazG,KAAMA,KAAK/Q,SAC7Ba,OAClB,GAAC,sBAED,WACE,OAAO,CACT,KAAC,EAjDmB,CA3GP,WACb,aAAc,UAEZkQ,KAAK6H,SAAWR,IAChBrH,KAAK8H,MAAQ,CAAC9H,KAAK6H,SACrB,CA+EC,OA/EA,oBAED,WACE,OAAO7H,KAAK8H,MAAM9H,KAAK8H,MAAMlX,OAAS,EACxC,GAAC,gBAED,WAAa,OAAOoP,KAAK6H,QAAU,GAEnC,iBACA,SAAIvB,GACFtG,KAAK+H,IAAIT,SAAS3U,KAAK2T,EACzB,GAEA,sBACA,SAASjR,GAEP,IAAMiR,EAAOe,EAAQ,CAAEhS,UACvB2K,KAAKsF,IAAIgB,GACTtG,KAAK8H,MAAMnV,KAAK2T,EAClB,GAAC,uBAED,WACE,GAAItG,KAAK8H,MAAMlX,OAAS,EACtB,OAAOoP,KAAK8H,MAAMpI,KAItB,GAAC,2BAED,WACE,KAAOM,KAAK0H,cACd,GAAC,oBAED,WACE,OAAOxG,KAAKC,UAAUnB,KAAK6H,SAAU,KAAM,EAC7C,GAEA,kBAIA,SAAKG,GAEH,OAAOhI,KAAKnP,YAAYoX,MAAMD,EAAShI,KAAK6H,SAG9C,IAEA,oBAIA,SAAaG,EAAS1B,GAAM,WAQ1B,MAPoB,iBAATA,EACT0B,EAAQP,QAAQnB,GACPA,EAAKgB,WACdU,EAAQR,SAASlB,GACjBA,EAAKgB,SAAShL,SAAQ,SAAC4L,GAAK,OAAK,EAAKD,MAAMD,EAASE,EAAM,IAC3DF,EAAQN,UAAUpB,IAEb0B,CACT,GAEA,uBAGA,SAAiB1B,GACK,iBAATA,GACNA,EAAKgB,WAENhB,EAAKgB,SAASa,OAAM,SAAAC,GAAE,MAAkB,iBAAPA,CAAe,IAGlD9B,EAAKgB,SAAW,CAAChB,EAAKgB,SAAS7W,KAAK,KAEpC6V,EAAKgB,SAAShL,SAAQ,SAAC4L,GACrBG,EAAUC,UAAUJ,EACtB,IAEJ,KAAC,EApFY,IAwKf,SAASjY,EAAOC,GACd,OAAKA,EACa,iBAAPA,EAAwBA,EAE5BA,EAAGD,OAHM,IAIlB,CAMA,SAASE,EAAUD,GACjB,OAAOE,EAAO,MAAOF,EAAI,IAC3B,CAMA,SAASqY,EAAiBrY,GACxB,OAAOE,EAAO,MAAOF,EAAI,KAC3B,CAMA,SAAS2O,EAAS3O,GAChB,OAAOE,EAAO,MAAOF,EAAI,KAC3B,CAMA,SAASE,IAAgB,2BAANC,EAAI,yBAAJA,EAAI,gBACrB,IAAMC,EAASD,EAAKE,KAAI,SAACC,GAAC,OAAKP,EAAOO,EAAE,IAAEC,KAAK,IAC/C,OAAOH,CACT,CAMA,SAASI,EAAqBL,GAC5B,IAAMM,EAAON,EAAKA,EAAKO,OAAS,GAEhC,MAAoB,iBAATD,GAAqBA,EAAKE,cAAgBC,QACnDT,EAAKU,OAAOV,EAAKO,OAAS,EAAG,GACtBD,GAEA,CAAC,CAEZ,CAWA,SAASK,IAAgB,2BAANX,EAAI,yBAAJA,EAAI,gBAErB,IAAMM,EAAOD,EAAqBL,GAC5BC,EAAS,KACVK,EAAKM,QAAU,GAAK,MACrBZ,EAAKE,KAAI,SAACC,GAAC,OAAKP,EAAOO,EAAE,IAAEC,KAAK,KAAO,IAC3C,OAAOH,CACT,CAMA,SAASkY,EAAiBtY,GACxB,OAAQ,IAAIH,OAAOG,EAAG6P,WAAa,KAAM0I,KAAK,IAAI7X,OAAS,CAC7D,CAmBA,IAAM8X,EAAa,iDAanB,SAASC,EAAuBC,EAAS,GAAc,IAAZC,EAAQ,EAARA,SACrCC,EAAc,EAElB,OAAOF,EAAQrY,KAAI,SAACqB,GAMlB,IAJA,IAAMmX,EADND,GAAe,EAEX5Y,EAAKD,EAAO2B,GACZoX,EAAM,GAEH9Y,EAAGU,OAAS,GAAG,CACpB,IAAMkD,EAAQ4U,EAAWD,KAAKvY,GAC9B,IAAK4D,EAAO,CACVkV,GAAO9Y,EACP,KACF,CACA8Y,GAAO9Y,EAAGyJ,UAAU,EAAG7F,EAAMoF,OAC7BhJ,EAAKA,EAAGyJ,UAAU7F,EAAMoF,MAAQpF,EAAM,GAAGlD,QACrB,OAAhBkD,EAAM,GAAG,IAAeA,EAAM,GAEhCkV,GAAO,KAAOC,OAAOC,OAAOpV,EAAM,IAAMiV,IAExCC,GAAOlV,EAAM,GACI,MAAbA,EAAM,IACRgV,IAGN,CACA,OAAOE,CACT,IAAGzY,KAAI,SAAAL,GAAE,iBAAQA,EAAE,QAAKO,KAAKoY,EAC/B,CAMA,IACM3X,EAAW,eACXwF,EAAsB,gBACtBf,EAAY,oBACZ8G,EAAc,yEACd0M,EAAmB,eA4BnB7W,EAAmB,CACvBP,MAAO,eAAgBkB,UAAW,GAE9BsB,EAAmB,CACvBc,MAAO,SACPtD,MAAO,IACPC,IAAK,IACLkC,QAAS,MACTjC,SAAU,CAACK,IAEPkC,EAAoB,CACxBa,MAAO,SACPtD,MAAO,IACPC,IAAK,IACLkC,QAAS,MACTjC,SAAU,CAACK,IAaPyC,EAAU,SAAShD,EAAOC,GAAuB,IAAlBoX,EAAc,UAAH,6CAAG,CAAC,EAC5CvD,EAAOG,EACX,CACE3Q,MAAO,UACPtD,QACAC,MACAC,SAAU,IAEZmX,GAEFvD,EAAK5T,SAASU,KAAK,CACjB0C,MAAO,SAGPtD,MAAO,mDACPC,IAAK,2CACLiD,cAAc,EACdhC,UAAW,IAEb,IAAMoW,EAAerY,EAEnB,IACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KAEA,iCACA,qBACA,qBA4BF,OAzBA6U,EAAK5T,SAASU,KACZ,CAgBEZ,MAAO3B,EACL,OACA,IACAiZ,EACA,uBACA,UAGCxD,CACT,EACM7Q,EAAsBD,EAAQ,KAAM,KACpCN,EAAuBM,EAAQ,OAAQ,QACvClB,EAAoBkB,EAAQ,IAAK,KACjClC,EAAc,CAClBwC,MAAO,SACPtD,MAAO4D,EACP1C,UAAW,GAEPkF,EAAgB,CACpB9C,MAAO,SACPtD,MAAO0K,EACPxJ,UAAW,GAEPiF,EAAqB,CACzB7C,MAAO,SACPtD,MAAOoX,EACPlW,UAAW,GAEPqI,EAAc,CAOlBvJ,MAAO,kBACPE,SAAU,CAAC,CACToD,MAAO,SACPtD,MAAO,KACPC,IAAK,aACLkC,QAAS,KACTjC,SAAU,CACRK,EACA,CACEP,MAAO,KACPC,IAAK,KACLiB,UAAW,EACXhB,SAAU,CAACK,QAKbe,EAAa,CACjBgC,MAAO,QACPtD,MAAOb,EACP+B,UAAW,GAEPuJ,EAAwB,CAC5BnH,MAAO,QACPtD,MAAO2E,EACPzD,UAAW,GAyBT4C,EAAqB/E,OAAOyU,OAAO,CACnC+D,UAAW,KACX1L,iBA3MqB,OA4MrB1M,SAAUA,EACVwF,oBAAqBA,EACrBf,UAAWA,EACX8G,YAAaA,EACb0M,iBAAkBA,EAClB9N,eA3MmB,+IA4MnBtI,QAvMY,WAAe,IAAdpC,EAAO,UAAH,6CAAG,CAAC,EACjB4Y,EAAe,YAQrB,OAPI5Y,EAAKqC,SACPrC,EAAKoB,MAAQ3B,EACXmZ,EACA,OACA5Y,EAAKqC,OACL,SAEGgT,EAAU,CACf3Q,MAAO,OACPtD,MAAOwX,EACPvX,IAAK,IACLiB,UAAW,EAEX,WAAY,SAAC+F,EAAGwQ,GACE,IAAZxQ,EAAEE,OAAasQ,EAAK9P,aAC1B,GACC/I,EACL,EAqLI2B,iBAAkBA,EAClBiC,iBAAkBA,EAClBC,kBAAmBA,EACnBiV,mBApKuB,CACzB1X,MAAO,8IAoKLgD,QAASA,EACTC,oBAAqBA,EACrBP,qBAAsBA,EACtBZ,kBAAmBA,EACnBhB,YAAaA,EACbsF,cAAeA,EACfD,mBAAoBA,EACpBoD,YAAaA,EACbjI,WAAYA,EACZmJ,sBAAuBA,EACvBkN,aA/CiB,CAEnB3X,MAAO,uBACPkB,UAAW,GA6CTR,kBAnCsB,SAASoT,GACjC,OAAO/U,OAAOoB,OAAO2T,EACnB,CAEE,WAAY,SAAC7M,EAAGwQ,GAAWA,EAAKpI,KAAKuI,YAAc3Q,EAAE,EAAI,EAEzD,SAAU,SAACA,EAAGwQ,GAAeA,EAAKpI,KAAKuI,cAAgB3Q,EAAE,IAAIwQ,EAAK9P,aAAe,GAEvF,IA2DA,SAASkQ,GAAsB9V,EAAOiF,GAErB,MADAjF,EAAMsF,MAAMtF,EAAMoF,MAAQ,IAEvCH,EAASW,aAEb,CAMA,SAASmQ,GAAehE,EAAMiE,QAELrJ,IAAnBoF,EAAK1T,YACP0T,EAAKxQ,MAAQwQ,EAAK1T,iBACX0T,EAAK1T,UAEhB,CAMA,SAASyC,GAAciR,EAAMkE,GACtBA,GACAlE,EAAKjR,gBAOViR,EAAK9T,MAAQ,OAAS8T,EAAKjR,cAAcoS,MAAM,KAAKvW,KAAK,KAAO,sBAChEoV,EAAKmE,cAAgBJ,GACrB/D,EAAKrS,SAAWqS,EAAKrS,UAAYqS,EAAKjR,qBAC/BiR,EAAKjR,mBAKW6L,IAAnBoF,EAAK5S,YAAyB4S,EAAK5S,UAAY,GACrD,CAMA,SAASgX,GAAepE,EAAMiE,GACvB/S,MAAMmT,QAAQrE,EAAK3R,WAExB2R,EAAK3R,QAAUlD,EAAM,eAAI6U,EAAK3R,UAChC,CAMA,SAASiW,GAAatE,EAAMiE,GAC1B,GAAKjE,EAAK/R,MAAV,CACA,GAAI+R,EAAK9T,OAAS8T,EAAK7T,IAAK,MAAM,IAAI+R,MAAM,4CAE5C8B,EAAK9T,MAAQ8T,EAAK/R,aACX+R,EAAK/R,KAJW,CAKzB,CAMA,SAASsW,GAAiBvE,EAAMiE,QAEPrJ,IAAnBoF,EAAK5S,YAAyB4S,EAAK5S,UAAY,EACrD,CAIA,IAAMoX,GAAiB,SAACxE,EAAMkE,GAC5B,GAAKlE,EAAKyE,YAAV,CAGA,GAAIzE,EAAKrT,OAAQ,MAAM,IAAIuR,MAAM,0CAEjC,IAAMwG,EAAezZ,OAAOoB,OAAO,CAAC,EAAG2T,GACvC/U,OAAO0Z,KAAK3E,GAAMvJ,SAAQ,SAAC6J,UAAiBN,EAAKM,EAAM,IAEvDN,EAAKrS,SAAW+W,EAAa/W,SAC7BqS,EAAK9T,MAAQ3B,EAAOma,EAAaD,YAAana,EAAUoa,EAAaxY,QACrE8T,EAAKrT,OAAS,CACZS,UAAW,EACXhB,SAAU,CACRnB,OAAOoB,OAAOqY,EAAc,CAAEnQ,YAAY,MAG9CyL,EAAK5S,UAAY,SAEVsX,EAAaD,WAlBS,CAmB/B,EAGMG,GAAkB,CACtB,KACA,MACA,MACA,KACA,MACA,KACA,KACA,OACA,SACA,OACA,SAGIC,GAAwB,UAQ9B,SAASC,GAAgBC,EAAaC,GAAoD,IAAnCC,EAAY,UAAH,6CAAGJ,GAE3DK,EAAmBja,OAAOoV,OAAO,MAiBvC,MAb2B,iBAAhB0E,EACTI,EAAYF,EAAWF,EAAY5D,MAAM,MAChCjQ,MAAMmT,QAAQU,GACvBI,EAAYF,EAAWF,GAEvB9Z,OAAO0Z,KAAKI,GAAatO,SAAQ,SAASwO,GAExCha,OAAOoB,OACL6Y,EACAJ,GAAgBC,EAAYE,GAAYD,EAAiBC,GAE7D,IAEKC,EAYP,SAASC,EAAYF,EAAWG,GAC1BJ,IACFI,EAAcA,EAAY1a,KAAI,SAAAC,GAAC,OAAIA,EAAE0a,aAAa,KAEpDD,EAAY3O,SAAQ,SAAS5I,GAC3B,IAAMyX,EAAOzX,EAAQsT,MAAM,KAC3B+D,EAAiBI,EAAK,IAAM,CAACL,EAAWM,GAAgBD,EAAK,GAAIA,EAAK,IACxE,GACF,CACF,CAUA,SAASC,GAAgB1X,EAAS2X,GAGhC,OAAIA,EACKnC,OAAOmC,GAUlB,SAAuB3X,GACrB,OAAO+W,GAAgBnM,SAAS5K,EAAQwX,cAC1C,CATSI,CAAc5X,GAAW,EAAI,CACtC,CAoBA,IAAM6X,GAAmB,CAAC,EAKpBC,GAAQ,SAACC,GACbC,QAAQF,MAAMC,EAChB,EAMME,GAAO,SAACF,GAAqB,6BAATpb,EAAI,iCAAJA,EAAI,mBAC5B,EAAAqb,SAAQE,IAAG,yBAAUH,IAAO,OAAOpb,GACrC,EAMMwb,GAAa,SAACC,EAASL,GACvBF,GAAiB,GAAD,OAAIO,EAAO,YAAIL,MAEnCC,QAAQE,IAAI,oBAAD,OAAqBE,EAAO,aAAKL,IAC5CF,GAAiB,GAAD,OAAIO,EAAO,YAAIL,KAAa,EAC9C,EAQMM,GAAkB,IAAIhI,MA8B5B,SAASiI,GAAgBnG,EAAMoG,EAAS,GAQtC,IAR+C,IAAP9F,EAAG,EAAHA,IACpC4C,EAAS,EACPmD,EAAarG,EAAKM,GAElBgG,EAAO,CAAC,EAERC,EAAY,CAAC,EAEVjM,EAAI,EAAGA,GAAK8L,EAAQrb,OAAQuP,IACnCiM,EAAUjM,EAAI4I,GAAUmD,EAAW/L,GACnCgM,EAAKhM,EAAI4I,IAAU,EACnBA,GAAUP,EAAiByD,EAAQ9L,EAAI,IAIzC0F,EAAKM,GAAOiG,EACZvG,EAAKM,GAAKkG,MAAQF,EAClBtG,EAAKM,GAAKmG,QAAS,CACrB,CA+DA,SAASC,GAAW1G,IAVpB,SAAoBA,GACdA,EAAKxQ,OAA+B,iBAAfwQ,EAAKxQ,OAAqC,OAAfwQ,EAAKxQ,QACvDwQ,EAAKnO,WAAamO,EAAKxQ,aAChBwQ,EAAKxQ,MAEhB,CAMEmX,CAAW3G,GAEoB,iBAApBA,EAAKnO,aACdmO,EAAKnO,WAAa,CAAE+U,MAAO5G,EAAKnO,aAEL,iBAAlBmO,EAAK6G,WACd7G,EAAK6G,SAAW,CAAED,MAAO5G,EAAK6G,WAjElC,SAAyB7G,GACvB,GAAK9O,MAAMmT,QAAQrE,EAAK9T,OAAxB,CAEA,GAAI8T,EAAKtK,MAAQsK,EAAK5Q,cAAgB4Q,EAAK1S,YAEzC,MADAqY,GAAM,sEACAO,GAGR,GAA+B,iBAApBlG,EAAKnO,YAA+C,OAApBmO,EAAKnO,WAE9C,MADA8T,GAAM,6BACAO,GAGRC,GAAgBnG,EAAMA,EAAK9T,MAAO,CAAEoU,IAAK,eACzCN,EAAK9T,MAAQ4W,EAAuB9C,EAAK9T,MAAO,CAAE8W,SAAU,IAbtB,CAcxC,CAqDE8D,CAAgB9G,GAhDlB,SAAuBA,GACrB,GAAK9O,MAAMmT,QAAQrE,EAAK7T,KAAxB,CAEA,GAAI6T,EAAKtK,MAAQsK,EAAK3Q,YAAc2Q,EAAK5L,UAEvC,MADAuR,GAAM,gEACAO,GAGR,GAA6B,iBAAlBlG,EAAK6G,UAA2C,OAAlB7G,EAAK6G,SAE5C,MADAlB,GAAM,2BACAO,GAGRC,GAAgBnG,EAAMA,EAAK7T,IAAK,CAAEmU,IAAK,aACvCN,EAAK7T,IAAM2W,EAAuB9C,EAAK7T,IAAK,CAAE6W,SAAU,IAbpB,CActC,CAkCE+D,CAAc/G,EAChB,CAoBA,SAASgH,GAAgBrG,GAOvB,SAASsG,EAAOhd,EAAOid,GACrB,OAAO,IAAIhd,OACTE,EAAOH,GACP,KACG0W,EAASzQ,iBAAmB,IAAM,KAClCyQ,EAASvJ,aAAe,IAAM,KAC9B8P,EAAS,IAAM,IAEtB,CAEA,IAaMC,EAAU,WACd,aAAc,UACZhN,KAAKiN,aAAe,CAAC,EAErBjN,KAAKiM,QAAU,GACfjM,KAAKkN,QAAU,EACflN,KAAKmN,SAAW,CAClB,CAqCC,OAnCD,0BACA,SAAQjd,EAAIS,GACVA,EAAKwc,SAAWnN,KAAKmN,WAErBnN,KAAKiN,aAAajN,KAAKkN,SAAWvc,EAClCqP,KAAKiM,QAAQtZ,KAAK,CAAChC,EAAMT,IACzB8P,KAAKkN,SAAW1E,EAAiBtY,GAAM,CACzC,GAAC,qBAED,WAC8B,IAAxB8P,KAAKiM,QAAQrb,SAGfoP,KAAKyI,KAAO,kBAAM,IAAI,GAExB,IAAM2E,EAAcpN,KAAKiM,QAAQ1b,KAAI,SAAA6X,GAAE,OAAIA,EAAG,EAAE,IAChDpI,KAAKqN,UAAYP,EAAOnE,EAAuByE,EAAa,CAAEvE,SAAU,OAAQ,GAChF7I,KAAKsN,UAAY,CACnB,GAEA,kBACA,SAAKC,GACHvN,KAAKqN,UAAUC,UAAYtN,KAAKsN,UAChC,IAAMxZ,EAAQkM,KAAKqN,UAAU5E,KAAK8E,GAClC,IAAKzZ,EAAS,OAAO,KAGrB,IAAMqM,EAAIrM,EAAM0Z,WAAU,SAACpF,EAAIjI,GAAC,OAAKA,EAAI,QAAYM,IAAP2H,CAAgB,IAExDqF,EAAYzN,KAAKiN,aAAa9M,GAKpC,OAFArM,EAAM/C,OAAO,EAAGoP,GAETrP,OAAOoB,OAAO4B,EAAO2Z,EAC9B,KAAC,EA5Ca,GA8EVC,EAAmB,WACvB,aAAc,UAEZ1N,KAAK2N,MAAQ,GAEb3N,KAAK4N,aAAe,GACpB5N,KAAKiC,MAAQ,EAEbjC,KAAKsN,UAAY,EACjBtN,KAAK6N,WAAa,CACpB,CAiFC,OA/ED,6BACA,SAAW3U,GACT,GAAI8G,KAAK4N,aAAa1U,GAAQ,OAAO8G,KAAK4N,aAAa1U,GAEvD,IAAM4U,EAAU,IAAId,EAIpB,OAHAhN,KAAK2N,MAAMpU,MAAML,GAAOoD,SAAQ,yBAAEpM,EAAE,KAAES,EAAI,YAAMmd,EAAQC,QAAQ7d,EAAIS,EAAK,IACzEmd,EAAQE,UACRhO,KAAK4N,aAAa1U,GAAS4U,EACpBA,CACT,GAAC,wCAED,WACE,OAA2B,IAApB9N,KAAK6N,UACd,GAAC,yBAED,WACE7N,KAAK6N,WAAa,CACpB,GAEA,qBACA,SAAQ3d,EAAIS,GACVqP,KAAK2N,MAAMhb,KAAK,CAACzC,EAAIS,IACH,UAAdA,EAAKgH,MAAkBqI,KAAKiC,OAClC,GAEA,kBACA,SAAKsL,GACH,IAAMvU,EAAIgH,KAAKiO,WAAWjO,KAAK6N,YAC/B7U,EAAEsU,UAAYtN,KAAKsN,UACnB,IAAI1L,EAAS5I,EAAEyP,KAAK8E,GAiCpB,GAAIvN,KAAKkO,6BACP,GAAItM,GAAUA,EAAO1I,QAAU8G,KAAKsN,eAAkB,CACpD,IAAMa,EAAKnO,KAAKiO,WAAW,GAC3BE,EAAGb,UAAYtN,KAAKsN,UAAY,EAChC1L,EAASuM,EAAG1F,KAAK8E,EACnB,CAWF,OARI3L,IACF5B,KAAK6N,YAAcjM,EAAOuL,SAAW,EACjCnN,KAAK6N,aAAe7N,KAAKiC,OAE3BjC,KAAKoO,eAIFxM,CACT,KAAC,EA3FsB,GAsOzB,GAHK4E,EAAS6H,qBAAoB7H,EAAS6H,mBAAqB,IAG5D7H,EAASvU,UAAYuU,EAASvU,SAASqM,SAAS,QAClD,MAAM,IAAIyF,MAAM,6FAMlB,OAFAyC,EAASvQ,iBAAmB+P,EAAUQ,EAASvQ,kBAAoB,CAAC,GAhFpE,SAASqY,EAAYzI,EAAMkE,GAAQ,MAC3BwE,EAAmC1I,EACzC,GAAIA,EAAK2I,WAAY,OAAOD,EAE5B,CACE1E,GAGAM,GACAoC,GACAlC,IACA/N,SAAQ,SAAAmS,GAAG,OAAIA,EAAI5I,EAAMkE,EAAO,IAElCvD,EAAS6H,mBAAmB/R,SAAQ,SAAAmS,GAAG,OAAIA,EAAI5I,EAAMkE,EAAO,IAG5DlE,EAAKmE,cAAgB,KAErB,CACEpV,GAGAqV,GAEAG,IACA9N,SAAQ,SAAAmS,GAAG,OAAIA,EAAI5I,EAAMkE,EAAO,IAElClE,EAAK2I,YAAa,EAElB,IAAIE,EAAiB,KAwCrB,MAvC6B,iBAAlB7I,EAAKrS,UAAyBqS,EAAKrS,SAASC,WAIrDoS,EAAKrS,SAAW1C,OAAOoB,OAAO,CAAC,EAAG2T,EAAKrS,UACvCkb,EAAiB7I,EAAKrS,SAASC,gBACxBoS,EAAKrS,SAASC,UAEvBib,EAAiBA,GAAkB,MAE/B7I,EAAKrS,WACPqS,EAAKrS,SAAWmX,GAAgB9E,EAAKrS,SAAUgT,EAASzQ,mBAG1DwY,EAAMI,iBAAmB7B,EAAO4B,GAAgB,GAE5C3E,IACGlE,EAAK9T,QAAO8T,EAAK9T,MAAQ,SAC9Bwc,EAAMK,QAAU9B,EAAOyB,EAAMxc,OACxB8T,EAAK7T,KAAQ6T,EAAK3P,iBAAgB2P,EAAK7T,IAAM,SAC9C6T,EAAK7T,MAAKuc,EAAMM,MAAQ/B,EAAOyB,EAAMvc,MACzCuc,EAAMO,cAAgB7e,EAAOse,EAAMvc,MAAQ,GACvC6T,EAAK3P,gBAAkB6T,EAAO+E,gBAChCP,EAAMO,gBAAkBjJ,EAAK7T,IAAM,IAAM,IAAM+X,EAAO+E,gBAGtDjJ,EAAK3R,UAASqa,EAAMQ,UAAYjC,EAAuCjH,EAAK3R,UAC3E2R,EAAK5T,WAAU4T,EAAK5T,SAAW,IAEpC4T,EAAK5T,UAAW,MAAG7B,OAAM,UAAIyV,EAAK5T,SAAS1B,KAAI,SAASye,GACtD,OAoDN,SAA2BnJ,GAUzB,OATIA,EAAKzT,WAAayT,EAAKoJ,iBACzBpJ,EAAKoJ,eAAiBpJ,EAAKzT,SAAS7B,KAAI,SAAS2e,GAC/C,OAAOlJ,EAAUH,EAAM,CAAEzT,SAAU,MAAQ8c,EAC7C,KAMErJ,EAAKoJ,eACApJ,EAAKoJ,eAOVE,GAAmBtJ,GACdG,EAAUH,EAAM,CAAErT,OAAQqT,EAAKrT,OAASwT,EAAUH,EAAKrT,QAAU,OAGtE1B,OAAO4U,SAASG,GACXG,EAAUH,GAIZA,CACT,CAhFauJ,CAAwB,SAANJ,EAAenJ,EAAOmJ,EACjD,MACAnJ,EAAK5T,SAASqK,SAAQ,SAAS0S,GAAKV,EAA+BU,EAAIT,EAAQ,IAE3E1I,EAAKrT,QACP8b,EAAYzI,EAAKrT,OAAQuX,GAG3BwE,EAAMT,QA1HR,SAAwBjI,GACtB,IAAMwJ,EAAK,IAAI3B,EAWf,OATA7H,EAAK5T,SAASqK,SAAQ,SAAAgT,GAAI,OAAID,EAAGtB,QAAQuB,EAAKvd,MAAO,CAAEwd,KAAMD,EAAM3X,KAAM,SAAU,IAE/EkO,EAAKiJ,eACPO,EAAGtB,QAAQlI,EAAKiJ,cAAe,CAAEnX,KAAM,QAErCkO,EAAK3R,SACPmb,EAAGtB,QAAQlI,EAAK3R,QAAS,CAAEyD,KAAM,YAG5B0X,CACT,CA6GkBG,CAAejB,GACxBA,CACT,CAYOD,CAA+B9H,EACxC,CAaA,SAAS2I,GAAmBtJ,GAC1B,QAAKA,IAEEA,EAAK3P,gBAAkBiZ,GAAmBtJ,EAAKrT,QACxD,CA0CA,IAEMid,GAAkB,8BACtB,WAAYC,EAAQC,GAAM,MAGP,OAHO,WACxB,cAAMD,IACDpc,KAAO,qBACZ,EAAKqc,KAAOA,EAAK,CACnB,CAAC,YALqB,CAKrB,EAL8B5L,QAmC3BlU,GAASkW,EACT3S,GAAU4S,EACV4J,GAAWC,OAAO,WAk8BpBC,GA37BS,SAASne,GAGpB,IAAMoe,EAAYjf,OAAOoV,OAAO,MAE1B3S,EAAUzC,OAAOoV,OAAO,MAExB8J,EAAU,GAIZC,GAAY,EACVC,EAAqB,sFAErBC,EAAqB,CAAEzT,mBAAmB,EAAMpJ,KAAM,aAAcrB,SAAU,IAKhFhD,EAAU,CACZmhB,qBAAqB,EACrBC,oBAAoB,EACpBC,cAAe,qBACfC,iBAAkB,8BAClB3J,YAAa,QACb4J,YAAa,WACbT,UAAW,KAGXU,UAAWlJ,GASb,SAASmJ,EAAmBC,GAC1B,OAAO1hB,EAAQqhB,cAAcM,KAAKD,EACpC,CA+CA,SAASb,EAAUe,EAAoBC,EAAeC,GACpD,IAAIC,EAAO,GACPL,EAAe,GACU,iBAAlBG,GACTE,EAAOH,EACPE,EAAiBD,EAAcC,eAC/BJ,EAAeG,EAActK,WAG7BqF,GAAW,SAAU,uDACrBA,GAAW,SAAU,yGACrB8E,EAAeE,EACfG,EAAOF,QAKcrQ,IAAnBsQ,IAAgCA,GAAiB,GAGrD,IAAME,EAAU,CACdD,OACAxK,SAAUmK,GAIZO,EAAK,mBAAoBD,GAIzB,IAAMrP,EAASqP,EAAQrP,OACnBqP,EAAQrP,OACRuP,EAAWF,EAAQzK,SAAUyK,EAAQD,KAAMD,GAM/C,OAJAnP,EAAOoP,KAAOC,EAAQD,KAEtBE,EAAK,kBAAmBtP,GAEjBA,CACT,CAWA,SAASuP,EAAWR,EAAcS,EAAiBL,EAAgBM,GACjE,IAAMC,EAAcxgB,OAAOoV,OAAO,MAYlC,SAASqL,IACP,GAAKxJ,EAAIvU,SAAT,CAKA,IAAI8Z,EAAY,EAChBvF,EAAI4G,iBAAiBrB,UAAY,EAIjC,IAHA,IAZyBkE,EAYrB1d,EAAQiU,EAAI4G,iBAAiBlG,KAAKgJ,GAClCC,EAAM,GAEH5d,GAAO,CACZ4d,GAAOD,EAAW9X,UAAU2T,EAAWxZ,EAAMoF,OAC7C,IAAMyY,EAAOnL,EAASzQ,iBAAmBjC,EAAM,GAAGoX,cAAgBpX,EAAM,GAClEsN,GAlBiBoQ,EAkBOG,EAAL5J,EAjBfvU,SAASge,IAkBnB,GAAIpQ,EAAM,CACR,QAAiCA,EAAI,GAA9BwQ,EAAI,KAAEC,EAAgB,KAM7B,GALAlK,EAAQF,QAAQiK,GAChBA,EAAM,GAENJ,EAAYK,IAASL,EAAYK,IAAS,GAAK,EAC3CL,EAAYK,IAjLD,IAiL4B1e,GAAa4e,GACpDD,EAAKE,WAAW,KAGlBJ,GAAO5d,EAAM,OACR,CACL,IAAMie,EAAWvL,EAASvQ,iBAAiB2b,IAASA,EACpDjK,EAAQqK,WAAWle,EAAM,GAAIie,EAC/B,CACF,MACEL,GAAO5d,EAAM,GAEfwZ,EAAYvF,EAAI4G,iBAAiBrB,UACjCxZ,EAAQiU,EAAI4G,iBAAiBlG,KAAKgJ,EACpC,CACAC,GAAOD,EAAW9X,UAAU2T,GAC5B3F,EAAQF,QAAQiK,EAjChB,MAFE/J,EAAQF,QAAQgK,EAoCpB,CA4BA,SAASQ,IACgB,MAAnBlK,EAAIpP,YA3BV,WACE,GAAmB,KAAf8Y,EAAJ,CAEA,IAAI7P,EAAS,KAEb,GAA+B,iBAApBmG,EAAIpP,YAA0B,CACvC,IAAKoX,EAAUhI,EAAIpP,aAEjB,YADAgP,EAAQF,QAAQgK,GAGlB7P,EAASuP,EAAWpJ,EAAIpP,YAAa8Y,GAAY,EAAMS,EAAcnK,EAAIpP,cACzEuZ,EAAcnK,EAAIpP,aAA4CiJ,EAAOuQ,IACvE,MACEvQ,EAASwQ,EAAcX,EAAY1J,EAAIpP,YAAY/H,OAASmX,EAAIpP,YAAc,MAO5EoP,EAAI9U,UAAY,IAClBA,GAAa2O,EAAO3O,WAEtB0U,EAAQ0K,eAAezQ,EAAO0Q,SAAU1Q,EAAO4E,SAtBlB,CAuB/B,CAII+L,GAEAhB,IAEFE,EAAa,EACf,CAMA,SAASe,EAAend,EAAOvB,GAG7B,IAFA,IAAIqM,EAAI,EACFsS,EAAM3e,EAAMlD,OAAS,EACpBuP,GAAKsS,GACV,GAAKpd,EAAMgX,MAAMlM,GAAjB,CACA,IAAMuS,EAAQlM,EAASvQ,iBAAiBZ,EAAM8K,KAAO9K,EAAM8K,GACrD2G,EAAOhT,EAAMqM,GACfuS,EACF/K,EAAQqK,WAAWlL,EAAM4L,IAEzBjB,EAAa3K,EACbyK,IACAE,EAAa,IAEftR,GAVsC,MAAfA,GAY3B,CAMA,SAASwS,EAAa9M,EAAM/R,GAiB1B,OAhBI+R,EAAKxQ,OAA+B,iBAAfwQ,EAAKxQ,OAC5BsS,EAAQH,SAAShB,EAASvQ,iBAAiB4P,EAAKxQ,QAAUwQ,EAAKxQ,OAE7DwQ,EAAKnO,aAEHmO,EAAKnO,WAAW+U,OAClB9E,EAAQqK,WAAWP,EAAYjL,EAASvQ,iBAAiB4P,EAAKnO,WAAW+U,QAAU5G,EAAKnO,WAAW+U,OACnGgF,EAAa,IACJ5L,EAAKnO,WAAW4U,SAEzBkG,EAAe3M,EAAKnO,WAAY5D,GAChC2d,EAAa,KAIjB1J,EAAMjX,OAAOoV,OAAOL,EAAM,CAAEkE,OAAQ,CAAEja,MAAOiY,IAE/C,CAQA,SAAS6K,EAAU/M,EAAM/R,EAAO+e,GAC9B,IAAIC,EA/4CV,SAAoB5iB,EAAI6iB,GACtB,IAAMjf,EAAQ5D,GAAMA,EAAGuY,KAAKsK,GAC5B,OAAOjf,GAAyB,IAAhBA,EAAMoF,KACxB,CA44CoB4Y,CAAWjM,EAAKgJ,MAAOgE,GAErC,GAAIC,EAAS,CACX,GAAIjN,EAAK,UAAW,CAClB,IAAM2D,EAAO,IAAI5D,EAASC,GAC1BA,EAAK,UAAU/R,EAAO0V,GAClBA,EAAK1D,iBAAgBgN,GAAU,EACrC,CAEA,GAAIA,EAAS,CACX,KAAOjN,EAAKzL,YAAcyL,EAAKkE,QAC7BlE,EAAOA,EAAKkE,OAEd,OAAOlE,CACT,CACF,CAGA,GAAIA,EAAK3P,eACP,OAAO0c,EAAU/M,EAAKkE,OAAQjW,EAAO+e,EAEzC,CAOA,SAASG,EAASD,GAChB,OAA+B,IAA3BhL,EAAI+F,QAAQD,YAGd4D,GAAcsB,EAAO,GACd,IAIPE,GAA2B,EACpB,EAEX,CAyCA,SAASC,EAAWpf,GAClB,IAAMif,EAASjf,EAAM,GACf+e,EAAqBzB,EAAgBzX,UAAU7F,EAAMoF,OAErDia,EAAUP,EAAU7K,EAAKjU,EAAO+e,GACtC,IAAKM,EAAW,OAAOvD,GAEvB,IAAMwD,EAASrL,EACXA,EAAI2E,UAAY3E,EAAI2E,SAASD,OAC/BwF,IACAtK,EAAQqK,WAAWe,EAAQhL,EAAI2E,SAASD,QAC/B1E,EAAI2E,UAAY3E,EAAI2E,SAASJ,QACtC2F,IACAO,EAAezK,EAAI2E,SAAU5Y,IACpBsf,EAAO7X,KAChBkW,GAAcsB,GAERK,EAAOnZ,WAAamZ,EAAOle,aAC/Buc,GAAcsB,GAEhBd,IACImB,EAAOle,aACTuc,EAAasB,IAGjB,GACMhL,EAAI1S,OACNsS,EAAQD,YAELK,EAAIxM,MAASwM,EAAIpP,cACpB1F,GAAa8U,EAAI9U,WAEnB8U,EAAMA,EAAIgC,aACHhC,IAAQoL,EAAQpJ,QAIzB,OAHIoJ,EAAQ3gB,QACVmgB,EAAaQ,EAAQ3gB,OAAQsB,GAExBsf,EAAOnZ,UAAY,EAAI8Y,EAAOniB,MACvC,CAaA,IAAIyiB,EAAY,CAAC,EAQjB,SAASC,EAAcC,EAAiBzf,GACtC,IAAMif,EAASjf,GAASA,EAAM,GAK9B,GAFA2d,GAAc8B,EAEA,MAAVR,EAEF,OADAd,IACO,EAOT,GAAuB,UAAnBoB,EAAU1b,MAAmC,QAAf7D,EAAM6D,MAAkB0b,EAAUna,QAAUpF,EAAMoF,OAAoB,KAAX6Z,EAAe,CAG1G,GADAtB,GAAcL,EAAgB7X,MAAMzF,EAAMoF,MAAOpF,EAAMoF,MAAQ,IAC1D+W,EAAW,CAEd,IAAMuD,EAAM,IAAIzP,MAAM,wBAAD,OAAyB4M,EAAY,MAG1D,MAFA6C,EAAI7C,aAAeA,EACnB6C,EAAIC,QAAUJ,EAAU9D,KAClBiE,CACR,CACA,OAAO,CACT,CAGA,GAFAH,EAAYvf,EAEO,UAAfA,EAAM6D,KACR,OA1HJ,SAAsB7D,GAOpB,IANA,IAAMif,EAASjf,EAAM,GACf4f,EAAU5f,EAAMyb,KAEhB/F,EAAO,IAAI5D,EAAS8N,GAG1B,MADwB,CAACA,EAAQ1J,cAAe0J,EAAQ,aACxB,eAAE,CAA7B,IAAMC,EAAE,KACX,GAAKA,IACLA,EAAG7f,EAAO0V,GACNA,EAAK1D,gBAAgB,OAAOkN,EAASD,EAC3C,CAcA,OAZIW,EAAQnY,KACVkW,GAAcsB,GAEVW,EAAQze,eACVwc,GAAcsB,GAEhBd,IACKyB,EAAQvgB,aAAgBugB,EAAQze,eACnCwc,EAAasB,IAGjBJ,EAAae,EAAS5f,GACf4f,EAAQvgB,YAAc,EAAI4f,EAAOniB,MAC1C,CAgGWgjB,CAAa9f,GACf,GAAmB,YAAfA,EAAM6D,OAAuBoZ,EAAgB,CAGtD,IAAMyC,EAAM,IAAIzP,MAAM,mBAAqBgP,EAAS,gBAAkBhL,EAAI1S,OAAS,aAAe,KAElG,MADAme,EAAI3N,KAAOkC,EACLyL,CACR,CAAO,GAAmB,QAAf1f,EAAM6D,KAAgB,CAC/B,IAAMkc,EAAYX,EAAWpf,GAC7B,GAAI+f,IAAcjE,GAChB,OAAOiE,CAEX,CAKA,GAAmB,YAAf/f,EAAM6D,MAAiC,KAAXob,EAE9B,OAAO,EAOT,GAAIe,EAAa,KAAUA,EAA2B,EAAdhgB,EAAMoF,MAE5C,MADY,IAAI6K,MAAM,6DAaxB,OADA0N,GAAcsB,EACPA,EAAOniB,MAChB,CAEA,IAAM4V,EAAWuN,EAAYpD,GAC7B,IAAKnK,EAEH,MADAgF,GAAM0E,EAAmBlgB,QAAQ,KAAM2gB,IACjC,IAAI5M,MAAM,sBAAwB4M,EAAe,KAGzD,IAAMqD,EAAKnH,GAAgBrG,GACvB5E,EAAS,GAETmG,EAAMsJ,GAAgB2C,EAEpB9B,EAAgB,CAAC,EACjBvK,EAAU,IAAI1Y,EAAQwhB,UAAUxhB,IAxGtC,WAEE,IADA,IAAMqL,EAAO,GACJ2Z,EAAUlM,EAAKkM,IAAYzN,EAAUyN,EAAUA,EAAQlK,OAC1DkK,EAAQ5e,OACViF,EAAK0D,QAAQiW,EAAQ5e,OAGzBiF,EAAKgC,SAAQ,SAAAoC,GAAI,OAAIiJ,EAAQH,SAAS9I,EAAK,GAC7C,CAiGAwV,GACA,IAAIzC,EAAa,GACbxe,EAAY,EACZiG,EAAQ,EACR4a,EAAa,EACbb,GAA2B,EAE/B,IAGE,IAFAlL,EAAI+F,QAAQM,gBAEH,CACP0F,IACIb,EAGFA,GAA2B,EAE3BlL,EAAI+F,QAAQM,cAEdrG,EAAI+F,QAAQR,UAAYpU,EAExB,IAAMpF,EAAQiU,EAAI+F,QAAQrF,KAAK2I,GAG/B,IAAKtd,EAAO,MAEZ,IACMqgB,EAAiBb,EADHlC,EAAgBzX,UAAUT,EAAOpF,EAAMoF,OACTpF,GAClDoF,EAAQpF,EAAMoF,MAAQib,CACxB,CAMA,OALAb,EAAclC,EAAgBzX,UAAUT,IACxCyO,EAAQyM,gBACRzM,EAAQ0M,WACRzS,EAAS+F,EAAQ2M,SAEV,CACL9N,SAAUmK,EACV7gB,MAAO8R,EACP3O,UAAWA,EACXiB,SAAS,EACToe,SAAU3K,EACVwK,KAAMpK,EA+BV,CA7BE,MAAOyL,GACP,GAAIA,EAAI/H,SAAW+H,EAAI/H,QAAQnN,SAAS,WACtC,MAAO,CACLkI,SAAUmK,EACV7gB,MAAOD,GAAOuhB,GACdld,SAAS,EACTjB,UAAW,EACXshB,WAAY,CACV9I,QAAS+H,EAAI/H,QACbvS,MAAOA,EACP+X,QAASG,EAAgB7X,MAAML,EAAQ,IAAKA,EAAQ,KACpD2M,KAAM2N,EAAI3N,KACV2O,YAAa5S,GAEf0Q,SAAU3K,GAEP,GAAIsI,EACT,MAAO,CACLzJ,SAAUmK,EACV7gB,MAAOD,GAAOuhB,GACdld,SAAS,EACTjB,UAAW,EACXwhB,YAAajB,EACblB,SAAU3K,EACVwK,KAAMpK,GAGR,MAAMyL,CAEV,CACF,CAmCA,SAASpB,EAAcpB,EAAM0D,GAC3BA,EAAiBA,GAAkBzlB,EAAQ8gB,WAAajf,OAAO0Z,KAAKuF,GACpE,IAAM4E,EA5BR,SAAiC3D,GAC/B,IAAMpP,EAAS,CACb9R,MAAOD,GAAOmhB,GACd9c,SAAS,EACTjB,UAAW,EACXkf,KAAMhC,EACNmC,SAAU,IAAIrjB,EAAQwhB,UAAUxhB,IAGlC,OADA2S,EAAO0Q,SAAS7K,QAAQuJ,GACjBpP,CACT,CAkBoBgT,CAAwB5D,GAEpC6D,EAAUH,EAAerW,OAAO0V,GAAa1V,OAAOyW,GAAevkB,KAAI,SAAA+C,GAAI,OAC/E6d,EAAW7d,EAAM0d,GAAM,EAAM,IAE/B6D,EAAQ7W,QAAQ2W,GAEhB,IAqBA,IArBeE,EAAQE,MAAK,SAACC,EAAGC,GAE9B,GAAID,EAAE/hB,YAAcgiB,EAAEhiB,UAAW,OAAOgiB,EAAEhiB,UAAY+hB,EAAE/hB,UAIxD,GAAI+hB,EAAExO,UAAYyO,EAAEzO,SAAU,CAC5B,GAAIuN,EAAYiB,EAAExO,UAAU0O,aAAeD,EAAEzO,SAC3C,OAAO,EACF,GAAIuN,EAAYkB,EAAEzO,UAAU0O,aAAeF,EAAExO,SAClD,OAAQ,CAEZ,CAMA,OAAO,CACT,IAEiC,GAA1B2O,EAAI,KAAEC,EAAU,KAGjBxT,EAASuT,EAGf,OAFAvT,EAAOwT,WAAaA,EAEbxT,CACT,CAqBA,SAASyT,EAAiBpR,GAExB,IACMuC,EApnBR,SAAuB8O,GACrB,IAAIC,EAAUD,EAAMnjB,UAAY,IAEhCojB,GAAWD,EAAM9Q,WAAa8Q,EAAM9Q,WAAWrS,UAAY,GAG3D,IAAM2B,EAAQ7E,EAAQshB,iBAAiB9H,KAAK8M,GAC5C,GAAIzhB,EAAO,CACT,IAAM0S,EAAWuN,EAAYjgB,EAAM,IAKnC,OAJK0S,IACHmF,GAAKuE,EAAmBlgB,QAAQ,KAAM8D,EAAM,KAC5C6X,GAAK,oDAAqD2J,IAErD9O,EAAW1S,EAAM,GAAK,cAC/B,CAEA,OAAOyhB,EACJvO,MAAM,OACNwO,MAAK,SAACC,GAAM,OAAK/E,EAAmB+E,IAAW1B,EAAY0B,EAAO,GACvE,CAimBmBC,CAAczR,GAE/B,IAAIyM,EAAmBlK,GAAvB,CAUA,GARA0K,EAAK,0BACH,CAAE9I,GAAInE,EAASuC,SAAUA,IAOvBvC,EAAQqD,SAAS1W,OAAS,IACvB3B,EAAQmhB,sBACX1E,QAAQC,KAAK,iGACbD,QAAQC,KAAK,6DACbD,QAAQC,KAAK,oCACbD,QAAQC,KAAK1H,IAEXhV,EAAQohB,oBAKV,MAJY,IAAIZ,GACd,mDACAxL,EAAQ0R,WAOd,IAAM7O,EADC7C,EACW2R,YACZhU,EAAS4E,EAAWsJ,EAAUhJ,EAAM,CAAEN,WAAUuK,gBAAgB,IAAUqB,EAActL,GAE9F7C,EAAQ0R,UAAY/T,EAAO9R,MA/C7B,SAAyBmU,EAAS4R,EAAaC,GAC7C,IAAMtP,EAAYqP,GAAetiB,EAAQsiB,IAAiBC,EAE1D7R,EAAQ8R,UAAUzQ,IAAI,QACtBrB,EAAQ8R,UAAUzQ,IAAI,YAAD,OAAakB,GACpC,CA2CEwP,CAAgB/R,EAASuC,EAAU5E,EAAO4E,UAC1CvC,EAAQrC,OAAS,CACf4E,SAAU5E,EAAO4E,SAEjBtW,GAAI0R,EAAO3O,UACXA,UAAW2O,EAAO3O,WAEhB2O,EAAOwT,aACTnR,EAAQmR,WAAa,CACnB5O,SAAU5E,EAAOwT,WAAW5O,SAC5BvT,UAAW2O,EAAOwT,WAAWniB,YAIjCie,EAAK,yBAA0B,CAAE9I,GAAInE,EAASrC,SAAQkF,QA7Cd,CA8C1C,CAuBA,IAAImP,GAAiB,EAKrB,SAASC,IAEqB,YAAxB3S,SAAS4S,WAKE5S,SAAS6S,iBAAiBnnB,EAAQuhB,aAC1ClU,QAAQ+Y,GALbY,GAAiB,CAMrB,CAmEA,SAASlC,EAAYzgB,GAEnB,OADAA,GAAQA,GAAQ,IAAI4X,cACb6E,EAAUzc,IAASyc,EAAUxc,EAAQD,GAC9C,CAOA,SAAS+iB,EAAgBC,EAAW,GAAkB,IAAhB3F,EAAY,EAAZA,aACX,iBAAd2F,IACTA,EAAY,CAACA,IAEfA,EAAUha,SAAQ,SAAAia,GAAWhjB,EAAQgjB,EAAMrL,eAAiByF,CAAc,GAC5E,CAMA,SAASmE,EAAcxhB,GACrB,IAAMkjB,EAAOzC,EAAYzgB,GACzB,OAAOkjB,IAASA,EAAK9Z,iBACvB,CAsCA,SAASwU,EAAKuF,EAAOpmB,GACnB,IAAMsjB,EAAK8C,EACXzG,EAAQ1T,SAAQ,SAASoa,GACnBA,EAAO/C,IACT+C,EAAO/C,GAAItjB,EAEf,GACF,CA8CA,IAAK,IAAM8V,IA9KW,oBAAX1C,QAA0BA,OAAOkT,kBAC1ClT,OAAOkT,iBAAiB,oBAP1B,WAEMV,GAAgBC,GACtB,IAIoD,GA6IpDplB,OAAOoB,OAAOP,EAAM,CAClBme,YACAsC,gBACA8D,eACAb,mBAEAuB,eAdF,SAAiCxO,GAI/B,OAHAyD,GAAW,SAAU,oDACrBA,GAAW,SAAU,oCAEdwJ,EAAiBjN,EAC1B,EAUEyO,UA3LF,SAAmBC,GACjB7nB,EAAUmE,GAAQnE,EAAS6nB,EAC7B,EA0LEC,iBAvLuB,WACvBb,IACArK,GAAW,SAAU,0DACvB,EAqLEmL,uBAlLF,WACEd,IACArK,GAAW,SAAU,gEACvB,EAgLEoL,iBA9IF,SAA0BtG,EAAcuG,GACtC,IAAIV,EAAO,KACX,IACEA,EAAOU,EAAmBvlB,EAU5B,CATE,MAAOwlB,GAGP,GAFA3L,GAAM,wDAAwDxb,QAAQ,KAAM2gB,KAEvEV,EAAa,MAAMkH,EAAkB3L,GAAM2L,GAKhDX,EAAOrG,CACT,CAEKqG,EAAKljB,OAAMkjB,EAAKljB,KAAOqd,GAC5BZ,EAAUY,GAAgB6F,EAC1BA,EAAKY,cAAgBF,EAAmBG,KAAK,KAAM1lB,GAE/C6kB,EAAKjjB,SACP8iB,EAAgBG,EAAKjjB,QAAS,CAAEod,gBAEpC,EAyHE2G,mBAlHF,SAA4B3G,UACnBZ,EAAUY,GACjB,IAAK,IAAL,MAAoB7f,OAAO0Z,KAAKjX,GAAQ,eAAE,CAArC,IAAMgjB,EAAK,KACVhjB,EAAQgjB,KAAW5F,UACdpd,EAAQgjB,EAEnB,CACF,EA4GEgB,cAvGF,WACE,OAAOzmB,OAAO0Z,KAAKuF,EACrB,EAsGEgE,cACAsC,kBACAvB,gBACA1hB,WACAokB,UAhDF,SAAmBd,IArBnB,SAA0BA,GAEpBA,EAAO,2BAA6BA,EAAO,6BAC7CA,EAAO,2BAA6B,SAACtV,GACnCsV,EAAO,yBACL5lB,OAAOoB,OAAO,CAAEojB,MAAOlU,EAAKgH,IAAMhH,GAEtC,GAEEsV,EAAO,0BAA4BA,EAAO,4BAC5CA,EAAO,0BAA4B,SAACtV,GAClCsV,EAAO,wBACL5lB,OAAOoB,OAAO,CAAEojB,MAAOlU,EAAKgH,IAAMhH,GAEtC,EAEJ,CAMEqW,CAAiBf,GACjB1G,EAAQrd,KAAK+jB,EACf,IAgDA/kB,EAAK+lB,UAAY,WAAazH,GAAY,CAAO,EACjDte,EAAKgmB,SAAW,WAAa1H,GAAY,CAAM,EAC/Cte,EAAKimB,cAh9BO,SAk9BZjmB,EAAKC,MAAQ,CACXxB,OAAQA,EACRD,UAAWA,EACXa,OAAQA,EACR6N,SAAUA,EACV0J,iBAAkBA,GAGF1S,EAEU,iBAAfA,EAAMsQ,IAEfpB,EAAc3J,QAAQvF,EAAMsQ,IAOhC,OAFArV,OAAOoB,OAAOP,EAAMkE,GAEblE,CACT,CAGgBkmB,CAAK,CAAC,GAEtBjY,EAAOxE,QAAU0U,GACjBA,GAAUgI,YAAchI,GACxBA,GAAUnK,QAAUmK,E", "sources": ["webpack://dash_table/./node_modules/highlight.js/es/core.js", "webpack://dash_table/./node_modules/highlight.js/styles/github.css?d6b7", "webpack://dash_table/./node_modules/highlight.js/es/languages/css.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/fsharp.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/javascript.js", "webpack://dash_table/./src/third-party/highlight.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/bash.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/csharp.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/http.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/json.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/julia.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/markdown.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/matlab.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/plaintext.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/python.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/r.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/ruby.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/shell.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/sql.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/xml.js", "webpack://dash_table/./node_modules/highlight.js/es/languages/yaml.js", "webpack://dash_table/./node_modules/highlight.js/styles/github.css", "webpack://dash_table/./node_modules/css-loader/dist/runtime/api.js", "webpack://dash_table/./node_modules/css-loader/dist/runtime/sourceMaps.js", "webpack://dash_table/./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js", "webpack://dash_table/./node_modules/style-loader/dist/runtime/insertBySelector.js", "webpack://dash_table/./node_modules/style-loader/dist/runtime/insertStyleElement.js", "webpack://dash_table/./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js", "webpack://dash_table/./node_modules/style-loader/dist/runtime/styleDomAPI.js", "webpack://dash_table/./node_modules/style-loader/dist/runtime/styleTagTransform.js", "webpack://dash_table/./node_modules/highlight.js/lib/core.js"], "sourcesContent": ["// https://nodejs.org/api/packages.html#packages_writing_dual_packages_while_avoiding_or_minimizing_hazards\nimport HighlightJS from '../lib/core.js';\nexport { HighlightJS };\nexport default HighlightJS;\n", "\n      import API from \"!../../style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../css-loader/dist/cjs.js!./github.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../css-loader/dist/cjs.js!./github.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'all',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-variant',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inline-size',\n  'isolation',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-break',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'row-gap',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n/*\nLanguage: CSS\nCategory: common, css, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/CSS\n*/\n\n/** @type LanguageFn */\nfunction css(hljs) {\n  const regex = hljs.regex;\n  const modes = MODES(hljs);\n  const VENDOR_PREFIX = { begin: /-(webkit|moz|ms|o)-(?=[a-z])/ };\n  const AT_MODIFIERS = \"and or not only\";\n  const AT_PROPERTY_RE = /@-?\\w[\\w]*(-\\w+)*/; // @-webkit-keyframes\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const STRINGS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ];\n\n  return {\n    name: 'CSS',\n    case_insensitive: true,\n    illegal: /[=|'\\$]/,\n    keywords: { keyframePosition: \"from to\" },\n    classNameAliases: {\n      // for visual continuity with `tag {}` and because we\n      // don't have a great class for this?\n      keyframePosition: \"selector-tag\" },\n    contains: [\n      modes.BLOCK_COMMENT,\n      VENDOR_PREFIX,\n      // to recognize keyframe 40% etc which are outside the scope of our\n      // attribute value mode\n      modes.CSS_NUMBER_MODE,\n      {\n        className: 'selector-id',\n        begin: /#[A-Za-z0-9_-]+/,\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.' + IDENT_RE,\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        variants: [\n          { begin: ':(' + PSEUDO_CLASSES.join('|') + ')' },\n          { begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')' }\n        ]\n      },\n      // we may actually need this (12/2020)\n      // { // pseudo-selector params\n      //   begin: /\\(/,\n      //   end: /\\)/,\n      //   contains: [ hljs.CSS_NUMBER_MODE ]\n      // },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      // attribute values\n      {\n        begin: /:/,\n        end: /[;}{]/,\n        contains: [\n          modes.BLOCK_COMMENT,\n          modes.HEXCOLOR,\n          modes.IMPORTANT,\n          modes.CSS_NUMBER_MODE,\n          ...STRINGS,\n          // needed to highlight these as strings and to avoid issues with\n          // illegal characters that might be inside urls that would tigger the\n          // languages illegal stack\n          {\n            begin: /(url|data-uri)\\(/,\n            end: /\\)/,\n            relevance: 0, // from keywords\n            keywords: { built_in: \"url data-uri\" },\n            contains: [\n              ...STRINGS,\n              {\n                className: \"string\",\n                // any character other than `)` as in `url()` will be the start\n                // of a string, which ends with `)` (from the parent mode)\n                begin: /[^)]/,\n                endsWithParent: true,\n                excludeEnd: true\n              }\n            ]\n          },\n          modes.FUNCTION_DISPATCH\n        ]\n      },\n      {\n        begin: regex.lookahead(/@/),\n        end: '[{;]',\n        relevance: 0,\n        illegal: /:/, // break on Less variables @var: ...\n        contains: [\n          {\n            className: 'keyword',\n            begin: AT_PROPERTY_RE\n          },\n          {\n            begin: /\\s/,\n            endsWithParent: true,\n            excludeEnd: true,\n            relevance: 0,\n            keywords: {\n              $pattern: /[a-z-]+/,\n              keyword: AT_MODIFIERS,\n              attribute: MEDIA_FEATURES.join(\" \")\n            },\n            contains: [\n              {\n                begin: /[a-z-]+(?=:)/,\n                className: \"attribute\"\n              },\n              ...STRINGS,\n              modes.CSS_NUMBER_MODE\n            ]\n          }\n        ]\n      },\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b'\n      }\n    ]\n  };\n}\n\nexport { css as default };\n", "/**\n * @param {string} value\n * @returns {RegExp}\n * */\nfunction escape(value) {\n  return new RegExp(value.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), 'm');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: F#\nAuthor: Jonas Follesø <<EMAIL>>\nContributors: Troy Kershaw <<EMAIL>>, Henrik Feldt <<EMAIL>>, Melvyn Laïly <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/dotnet/fsharp/\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction fsharp(hljs) {\n  const KEYWORDS = [\n    \"abstract\",\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"base\",\n    \"begin\",\n    \"class\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"done\",\n    \"downcast\",\n    \"downto\",\n    \"elif\",\n    \"else\",\n    \"end\",\n    \"exception\",\n    \"extern\",\n    // \"false\", // literal\n    \"finally\",\n    \"fixed\",\n    \"for\",\n    \"fun\",\n    \"function\",\n    \"global\",\n    \"if\",\n    \"in\",\n    \"inherit\",\n    \"inline\",\n    \"interface\",\n    \"internal\",\n    \"lazy\",\n    \"let\",\n    \"match\",\n    \"member\",\n    \"module\",\n    \"mutable\",\n    \"namespace\",\n    \"new\",\n    // \"not\", // built_in\n    // \"null\", // literal\n    \"of\",\n    \"open\",\n    \"or\",\n    \"override\",\n    \"private\",\n    \"public\",\n    \"rec\",\n    \"return\",\n    \"static\",\n    \"struct\",\n    \"then\",\n    \"to\",\n    // \"true\", // literal\n    \"try\",\n    \"type\",\n    \"upcast\",\n    \"use\",\n    \"val\",\n    \"void\",\n    \"when\",\n    \"while\",\n    \"with\",\n    \"yield\"\n  ];\n\n  const BANG_KEYWORD_MODE = {\n    // monad builder keywords (matches before non-bang keywords)\n    scope: 'keyword',\n    match: /\\b(yield|return|let|do|match|use)!/\n  };\n\n  const PREPROCESSOR_KEYWORDS = [\n    \"if\",\n    \"else\",\n    \"endif\",\n    \"line\",\n    \"nowarn\",\n    \"light\",\n    \"r\",\n    \"i\",\n    \"I\",\n    \"load\",\n    \"time\",\n    \"help\",\n    \"quit\"\n  ];\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\",\n    \"Some\",\n    \"None\",\n    \"Ok\",\n    \"Error\",\n    \"infinity\",\n    \"infinityf\",\n    \"nan\",\n    \"nanf\"\n  ];\n\n  const SPECIAL_IDENTIFIERS = [\n    \"__LINE__\",\n    \"__SOURCE_DIRECTORY__\",\n    \"__SOURCE_FILE__\"\n  ];\n\n  // Since it's possible to re-bind/shadow names (e.g. let char = 'c'),\n  // these builtin types should only be matched when a type name is expected.\n  const KNOWN_TYPES = [\n    // basic types\n    \"bool\",\n    \"byte\",\n    \"sbyte\",\n    \"int8\",\n    \"int16\",\n    \"int32\",\n    \"uint8\",\n    \"uint16\",\n    \"uint32\",\n    \"int\",\n    \"uint\",\n    \"int64\",\n    \"uint64\",\n    \"nativeint\",\n    \"unativeint\",\n    \"decimal\",\n    \"float\",\n    \"double\",\n    \"float32\",\n    \"single\",\n    \"char\",\n    \"string\",\n    \"unit\",\n    \"bigint\",\n    // other native types or lowercase aliases\n    \"option\",\n    \"voption\",\n    \"list\",\n    \"array\",\n    \"seq\",\n    \"byref\",\n    \"exn\",\n    \"inref\",\n    \"nativeptr\",\n    \"obj\",\n    \"outref\",\n    \"voidptr\",\n    // other important FSharp types\n    \"Result\"\n  ];\n\n  const BUILTINS = [\n    // Somewhat arbitrary list of builtin functions and values.\n    // Most of them are declared in Microsoft.FSharp.Core\n    // I tried to stay relevant by adding only the most idiomatic\n    // and most used symbols that are not already declared as types.\n    \"not\",\n    \"ref\",\n    \"raise\",\n    \"reraise\",\n    \"dict\",\n    \"readOnlyDict\",\n    \"set\",\n    \"get\",\n    \"enum\",\n    \"sizeof\",\n    \"typeof\",\n    \"typedefof\",\n    \"nameof\",\n    \"nullArg\",\n    \"invalidArg\",\n    \"invalidOp\",\n    \"id\",\n    \"fst\",\n    \"snd\",\n    \"ignore\",\n    \"lock\",\n    \"using\",\n    \"box\",\n    \"unbox\",\n    \"tryUnbox\",\n    \"printf\",\n    \"printfn\",\n    \"sprintf\",\n    \"eprintf\",\n    \"eprintfn\",\n    \"fprintf\",\n    \"fprintfn\",\n    \"failwith\",\n    \"failwithf\"\n  ];\n\n  const ALL_KEYWORDS = {\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILTINS,\n    'variable.constant': SPECIAL_IDENTIFIERS\n  };\n\n  // (* potentially multi-line Meta Language style comment *)\n  const ML_COMMENT =\n    hljs.COMMENT(/\\(\\*(?!\\))/, /\\*\\)/, {\n      contains: [\"self\"]\n    });\n  // Either a multi-line (* Meta Language style comment *) or a single line // C style comment.\n  const COMMENT = {\n    variants: [\n      ML_COMMENT,\n      hljs.C_LINE_COMMENT_MODE,\n    ]\n  };\n\n  // Most identifiers can contain apostrophes\n  const IDENTIFIER_RE = /[a-zA-Z_](\\w|')*/;\n\n  const QUOTED_IDENTIFIER = {\n    scope: 'variable',\n    begin: /``/,\n    end: /``/\n  };\n\n  // 'a or ^a where a can be a ``quoted identifier``\n  const BEGIN_GENERIC_TYPE_SYMBOL_RE = /\\B('|\\^)/;\n  const GENERIC_TYPE_SYMBOL = {\n    scope: 'symbol',\n    variants: [\n      // the type name is a quoted identifier:\n      { match: concat(BEGIN_GENERIC_TYPE_SYMBOL_RE, /``.*?``/) },\n      // the type name is a normal identifier (we don't use IDENTIFIER_RE because there cannot be another apostrophe here):\n      { match: concat(BEGIN_GENERIC_TYPE_SYMBOL_RE, hljs.UNDERSCORE_IDENT_RE) }\n    ],\n    relevance: 0\n  };\n\n  const makeOperatorMode = function({ includeEqual }) {\n    // List or symbolic operator characters from the FSharp Spec 4.1, minus the dot, and with `?` added, used for nullable operators.\n    let allOperatorChars;\n    if (includeEqual)\n      allOperatorChars = \"!%&*+-/<=>@^|~?\";\n    else\n      allOperatorChars = \"!%&*+-/<>@^|~?\";\n    const OPERATOR_CHARS = Array.from(allOperatorChars);\n    const OPERATOR_CHAR_RE = concat('[', ...OPERATOR_CHARS.map(escape), ']');\n    // The lone dot operator is special. It cannot be redefined, and we don't want to highlight it. It can be used as part of a multi-chars operator though.\n    const OPERATOR_CHAR_OR_DOT_RE = either(OPERATOR_CHAR_RE, /\\./);\n    // When a dot is present, it must be followed by another operator char:\n    const OPERATOR_FIRST_CHAR_OF_MULTIPLE_RE = concat(OPERATOR_CHAR_OR_DOT_RE, lookahead(OPERATOR_CHAR_OR_DOT_RE));\n    const SYMBOLIC_OPERATOR_RE = either(\n      concat(OPERATOR_FIRST_CHAR_OF_MULTIPLE_RE, OPERATOR_CHAR_OR_DOT_RE, '*'), // Matches at least 2 chars operators\n      concat(OPERATOR_CHAR_RE, '+'), // Matches at least one char operators\n    );\n    return {\n      scope: 'operator',\n      match: either(\n        // symbolic operators:\n        SYMBOLIC_OPERATOR_RE,\n        // other symbolic keywords:\n        // Type casting and conversion operators:\n        /:\\?>/,\n        /:\\?/,\n        /:>/,\n        /:=/, // Reference cell assignment\n        /::?/, // : or ::\n        /\\$/), // A single $ can be used as an operator\n      relevance: 0\n    };\n  };\n\n  const OPERATOR = makeOperatorMode({ includeEqual: true });\n  // This variant is used when matching '=' should end a parent mode:\n  const OPERATOR_WITHOUT_EQUAL = makeOperatorMode({ includeEqual: false });\n\n  const makeTypeAnnotationMode = function(prefix, prefixScope) {\n    return {\n      begin: concat( // a type annotation is a\n        prefix,            // should be a colon or the 'of' keyword\n        lookahead(   // that has to be followed by\n          concat(\n            /\\s*/,         // optional space\n            either(  // then either of:\n              /\\w/,        // word\n              /'/,         // generic type name\n              /\\^/,        // generic type name\n              /#/,         // flexible type name\n              /``/,        // quoted type name\n              /\\(/,        // parens type expression\n              /{\\|/,       // anonymous type annotation\n      )))),\n      beginScope: prefixScope,\n      // BUG: because ending with \\n is necessary for some cases, multi-line type annotations are not properly supported.\n      // Examples where \\n is required at the end:\n      // - abstract member definitions in classes: abstract Property : int * string\n      // - return type annotations: let f f' = f' () : returnTypeAnnotation\n      // - record fields definitions: { A : int \\n B : string }\n      end: lookahead(\n        either(\n          /\\n/,\n          /=/)),\n      relevance: 0,\n      // we need the known types, and we need the type constraint keywords and literals. e.g.: when 'a : null\n      keywords: hljs.inherit(ALL_KEYWORDS, { type: KNOWN_TYPES }),\n      contains: [\n        COMMENT,\n        GENERIC_TYPE_SYMBOL,\n        hljs.inherit(QUOTED_IDENTIFIER, { scope: null }), // match to avoid strange patterns inside that may break the parsing\n        OPERATOR_WITHOUT_EQUAL\n      ]\n    };\n  };\n\n  const TYPE_ANNOTATION = makeTypeAnnotationMode(/:/, 'operator');\n  const DISCRIMINATED_UNION_TYPE_ANNOTATION = makeTypeAnnotationMode(/\\bof\\b/, 'keyword');\n\n  // type MyType<'a> = ...\n  const TYPE_DECLARATION = {\n    begin: [\n      /(^|\\s+)/, // prevents matching the following: `match s.stype with`\n      /type/,\n      /\\s+/,\n      IDENTIFIER_RE\n    ],\n    beginScope: {\n      2: 'keyword',\n      4: 'title.class'\n    },\n    end: lookahead(/\\(|=|$/),\n    keywords: ALL_KEYWORDS, // match keywords in type constraints. e.g.: when 'a : null\n    contains: [\n      COMMENT,\n      hljs.inherit(QUOTED_IDENTIFIER, { scope: null }), // match to avoid strange patterns inside that may break the parsing\n      GENERIC_TYPE_SYMBOL,\n      {\n        // For visual consistency, highlight type brackets as operators.\n        scope: 'operator',\n        match: /<|>/\n      },\n      TYPE_ANNOTATION // generic types can have constraints, which are type annotations. e.g. type MyType<'T when 'T : delegate<obj * string>> =\n    ]\n  };\n\n  const COMPUTATION_EXPRESSION = {\n    // computation expressions:\n    scope: 'computation-expression',\n    // BUG: might conflict with record deconstruction. e.g. let f { Name = name } = name // will highlight f\n    match: /\\b[_a-z]\\w*(?=\\s*\\{)/\n  };\n\n  const PREPROCESSOR = {\n    // preprocessor directives and fsi commands:\n    begin: [\n      /^\\s*/,\n      concat(/#/, either(...PREPROCESSOR_KEYWORDS)),\n      /\\b/\n    ],\n    beginScope: { 2: 'meta' },\n    end: lookahead(/\\s|$/)\n  };\n\n  // TODO: this definition is missing support for type suffixes and octal notation.\n  // BUG: range operator without any space is wrongly interpreted as a single number (e.g. 1..10 )\n  const NUMBER = {\n    variants: [\n      hljs.BINARY_NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n\n  // All the following string definitions are potentially multi-line.\n  // BUG: these definitions are missing support for byte strings (suffixed with B)\n\n  // \"...\"\n  const QUOTED_STRING = {\n    scope: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE\n    ]\n  };\n  // @\"...\"\n  const VERBATIM_STRING = {\n    scope: 'string',\n    begin: /@\"/,\n    end: /\"/,\n    contains: [\n      {\n        match: /\"\"/ // escaped \"\n      },\n      hljs.BACKSLASH_ESCAPE\n    ]\n  };\n  // \"\"\"...\"\"\"\n  const TRIPLE_QUOTED_STRING = {\n    scope: 'string',\n    begin: /\"\"\"/,\n    end: /\"\"\"/,\n    relevance: 2\n  };\n  const SUBST = {\n    scope: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: ALL_KEYWORDS\n  };\n  // $\"...{1+1}...\"\n  const INTERPOLATED_STRING = {\n    scope: 'string',\n    begin: /\\$\"/,\n    end: /\"/,\n    contains: [\n      {\n        match: /\\{\\{/ // escaped {\n      },\n      {\n        match: /\\}\\}/ // escaped }\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  // $@\"...{1+1}...\"\n  const INTERPOLATED_VERBATIM_STRING = {\n    scope: 'string',\n    begin: /(\\$@|@\\$)\"/,\n    end: /\"/,\n    contains: [\n      {\n        match: /\\{\\{/ // escaped {\n      },\n      {\n        match: /\\}\\}/ // escaped }\n      },\n      {\n        match: /\"\"/\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  // $\"\"\"...{1+1}...\"\"\"\n  const INTERPOLATED_TRIPLE_QUOTED_STRING = {\n    scope: 'string',\n    begin: /\\$\"\"\"/,\n    end: /\"\"\"/,\n    contains: [\n      {\n        match: /\\{\\{/ // escaped {\n      },\n      {\n        match: /\\}\\}/ // escaped }\n      },\n      SUBST\n    ],\n    relevance: 2\n  };\n  // '.'\n  const CHAR_LITERAL = {\n    scope: 'string',\n    match: concat(\n      /'/,\n      either(\n        /[^\\\\']/, // either a single non escaped char...\n        /\\\\(?:.|\\d{3}|x[a-fA-F\\d]{2}|u[a-fA-F\\d]{4}|U[a-fA-F\\d]{8})/ // ...or an escape sequence\n      ),\n      /'/\n    )\n  };\n  // F# allows a lot of things inside string placeholders.\n  // Things that don't currently seem allowed by the compiler: types definition, attributes usage.\n  // (Strictly speaking, some of the followings are only allowed inside triple quoted interpolated strings...)\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    QUOTED_STRING,\n    CHAR_LITERAL,\n    BANG_KEYWORD_MODE,\n    COMMENT,\n    QUOTED_IDENTIFIER,\n    TYPE_ANNOTATION,\n    COMPUTATION_EXPRESSION,\n    PREPROCESSOR,\n    NUMBER,\n    GENERIC_TYPE_SYMBOL,\n    OPERATOR\n  ];\n  const STRING = {\n    variants: [\n      INTERPOLATED_TRIPLE_QUOTED_STRING,\n      INTERPOLATED_VERBATIM_STRING,\n      INTERPOLATED_STRING,\n      TRIPLE_QUOTED_STRING,\n      VERBATIM_STRING,\n      QUOTED_STRING,\n      CHAR_LITERAL\n    ]\n  };\n\n  return {\n    name: 'F#',\n    aliases: [\n      'fs',\n      'f#'\n    ],\n    keywords: ALL_KEYWORDS,\n    illegal: /\\/\\*/,\n    classNameAliases: {\n      'computation-expression': 'keyword'\n    },\n    contains: [\n      BANG_KEYWORD_MODE,\n      STRING,\n      COMMENT,\n      QUOTED_IDENTIFIER,\n      TYPE_DECLARATION,\n      {\n        // e.g. [<Attributes(\"\")>] or [<``module``: MyCustomAttributeThatWorksOnModules>]\n        // or [<Sealed; NoEquality; NoComparison; CompiledName(\"FSharpAsync`1\")>]\n        scope: 'meta',\n        begin: /\\[</,\n        end: />\\]/,\n        relevance: 2,\n        contains: [\n          QUOTED_IDENTIFIER,\n          // can contain any constant value\n          TRIPLE_QUOTED_STRING,\n          VERBATIM_STRING,\n          QUOTED_STRING,\n          CHAR_LITERAL,\n          NUMBER\n        ]\n      },\n      DISCRIMINATED_UNION_TYPE_ANNOTATION,\n      TYPE_ANNOTATION,\n      COMPUTATION_EXPRESSION,\n      PREPROCESSOR,\n      NUMBER,\n      GENERIC_TYPE_SYMBOL,\n      OPERATOR\n    ]\n  };\n}\n\nexport { fsharp as default };\n", "const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\") {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    TEMPLATE_STRING,\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\"\n      ]),\n      IDENT_RE$1, regex.lookahead(/\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'Javascript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        className: 'attr',\n        begin: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\nexport { javascript as default };\n", "\nimport highlightjs from 'highlight.js/lib/core';\nimport 'highlight.js/styles/github.css';\nimport bash from 'highlight.js/lib/languages/bash';\nimport csharp from 'highlight.js/lib/languages/csharp';\nimport css from 'highlight.js/lib/languages/css';\nimport fsharp from 'highlight.js/lib/languages/fsharp';\nimport http from 'highlight.js/lib/languages/http';\nimport javascript from 'highlight.js/lib/languages/javascript';\nimport json from 'highlight.js/lib/languages/json';\nimport julia from 'highlight.js/lib/languages/julia';\nimport markdown from 'highlight.js/lib/languages/markdown';\nimport matlab from 'highlight.js/lib/languages/matlab';\nimport plaintext from 'highlight.js/lib/languages/plaintext';\nimport python from 'highlight.js/lib/languages/python';\nimport r from 'highlight.js/lib/languages/r';\nimport ruby from 'highlight.js/lib/languages/ruby';\nimport shell from 'highlight.js/lib/languages/shell';\nimport sql from 'highlight.js/lib/languages/sql';\nimport xml from 'highlight.js/lib/languages/xml';\nimport yaml from 'highlight.js/lib/languages/yaml';\nhighlightjs.registerLanguage('bash', bash);\nhighlightjs.registerLanguage('csharp', csharp);\nhighlightjs.registerLanguage('css', css);\nhighlightjs.registerLanguage('fsharp', fsharp);\nhighlightjs.registerLanguage('http', http);\nhighlightjs.registerLanguage('javascript', javascript);\nhighlightjs.registerLanguage('json', json);\nhighlightjs.registerLanguage('julia', julia);\nhighlightjs.registerLanguage('markdown', markdown);\nhighlightjs.registerLanguage('matlab', matlab);\nhighlightjs.registerLanguage('plaintext', plaintext);\nhighlightjs.registerLanguage('python', python);\nhighlightjs.registerLanguage('r', r);\nhighlightjs.registerLanguage('ruby', ruby);\nhighlightjs.registerLanguage('shell', shell);\nhighlightjs.registerLanguage('sql', sql);\nhighlightjs.registerLanguage('xml', xml);\nhighlightjs.registerLanguage('yaml', yaml);\nexport default highlightjs;\n", "/*\nLanguage: Bash\nAuthor: vah <vah<PERSON><EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const regex = hljs.regex;\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR, {\n    className: 'variable',\n    variants: [\n      { begin: regex.concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: { contains: [\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(\\w+)/,\n        end: /(\\w+)/,\n        className: 'string'\n      })\n    ] }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    className: '',\n    begin: /\\\\\"/\n\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      {\n        begin: /\\d+#[0-9a-f]+/,\n        className: \"number\"\n      },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [ hljs.inherit(hljs.TITLE_MODE, { begin: /\\w[\\w\\d_]*/ }) ],\n    relevance: 0\n  };\n\n  const KEYWORDS = [\n    \"if\",\n    \"then\",\n    \"else\",\n    \"elif\",\n    \"fi\",\n    \"for\",\n    \"while\",\n    \"in\",\n    \"do\",\n    \"done\",\n    \"case\",\n    \"esac\",\n    \"function\"\n  ];\n\n  const LITERALS = [\n    \"true\",\n    \"false\"\n  ];\n\n  // to consume paths to prevent keyword matches inside them\n  const PATH_MODE = { match: /(\\/[a-z._-]+)+/ };\n\n  // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n  const SHELL_BUILT_INS = [\n    \"break\",\n    \"cd\",\n    \"continue\",\n    \"eval\",\n    \"exec\",\n    \"exit\",\n    \"export\",\n    \"getopts\",\n    \"hash\",\n    \"pwd\",\n    \"readonly\",\n    \"return\",\n    \"shift\",\n    \"test\",\n    \"times\",\n    \"trap\",\n    \"umask\",\n    \"unset\"\n  ];\n\n  const BASH_BUILT_INS = [\n    \"alias\",\n    \"bind\",\n    \"builtin\",\n    \"caller\",\n    \"command\",\n    \"declare\",\n    \"echo\",\n    \"enable\",\n    \"help\",\n    \"let\",\n    \"local\",\n    \"logout\",\n    \"mapfile\",\n    \"printf\",\n    \"read\",\n    \"readarray\",\n    \"source\",\n    \"type\",\n    \"typeset\",\n    \"ulimit\",\n    \"unalias\"\n  ];\n\n  const ZSH_BUILT_INS = [\n    \"autoload\",\n    \"bg\",\n    \"bindkey\",\n    \"bye\",\n    \"cap\",\n    \"chdir\",\n    \"clone\",\n    \"comparguments\",\n    \"compcall\",\n    \"compctl\",\n    \"compdescribe\",\n    \"compfiles\",\n    \"compgroups\",\n    \"compquote\",\n    \"comptags\",\n    \"comptry\",\n    \"compvalues\",\n    \"dirs\",\n    \"disable\",\n    \"disown\",\n    \"echotc\",\n    \"echoti\",\n    \"emulate\",\n    \"fc\",\n    \"fg\",\n    \"float\",\n    \"functions\",\n    \"getcap\",\n    \"getln\",\n    \"history\",\n    \"integer\",\n    \"jobs\",\n    \"kill\",\n    \"limit\",\n    \"log\",\n    \"noglob\",\n    \"popd\",\n    \"print\",\n    \"pushd\",\n    \"pushln\",\n    \"rehash\",\n    \"sched\",\n    \"setcap\",\n    \"setopt\",\n    \"stat\",\n    \"suspend\",\n    \"ttyctl\",\n    \"unfunction\",\n    \"unhash\",\n    \"unlimit\",\n    \"unsetopt\",\n    \"vared\",\n    \"wait\",\n    \"whence\",\n    \"where\",\n    \"which\",\n    \"zcompile\",\n    \"zformat\",\n    \"zftp\",\n    \"zle\",\n    \"zmodload\",\n    \"zparseopts\",\n    \"zprof\",\n    \"zpty\",\n    \"zregexparse\",\n    \"zsocket\",\n    \"zstyle\",\n    \"ztcp\"\n  ];\n\n  const GNU_CORE_UTILS = [\n    \"chcon\",\n    \"chgrp\",\n    \"chown\",\n    \"chmod\",\n    \"cp\",\n    \"dd\",\n    \"df\",\n    \"dir\",\n    \"dircolors\",\n    \"ln\",\n    \"ls\",\n    \"mkdir\",\n    \"mkfifo\",\n    \"mknod\",\n    \"mktemp\",\n    \"mv\",\n    \"realpath\",\n    \"rm\",\n    \"rmdir\",\n    \"shred\",\n    \"sync\",\n    \"touch\",\n    \"truncate\",\n    \"vdir\",\n    \"b2sum\",\n    \"base32\",\n    \"base64\",\n    \"cat\",\n    \"cksum\",\n    \"comm\",\n    \"csplit\",\n    \"cut\",\n    \"expand\",\n    \"fmt\",\n    \"fold\",\n    \"head\",\n    \"join\",\n    \"md5sum\",\n    \"nl\",\n    \"numfmt\",\n    \"od\",\n    \"paste\",\n    \"ptx\",\n    \"pr\",\n    \"sha1sum\",\n    \"sha224sum\",\n    \"sha256sum\",\n    \"sha384sum\",\n    \"sha512sum\",\n    \"shuf\",\n    \"sort\",\n    \"split\",\n    \"sum\",\n    \"tac\",\n    \"tail\",\n    \"tr\",\n    \"tsort\",\n    \"unexpand\",\n    \"uniq\",\n    \"wc\",\n    \"arch\",\n    \"basename\",\n    \"chroot\",\n    \"date\",\n    \"dirname\",\n    \"du\",\n    \"echo\",\n    \"env\",\n    \"expr\",\n    \"factor\",\n    // \"false\", // keyword literal already\n    \"groups\",\n    \"hostid\",\n    \"id\",\n    \"link\",\n    \"logname\",\n    \"nice\",\n    \"nohup\",\n    \"nproc\",\n    \"pathchk\",\n    \"pinky\",\n    \"printenv\",\n    \"printf\",\n    \"pwd\",\n    \"readlink\",\n    \"runcon\",\n    \"seq\",\n    \"sleep\",\n    \"stat\",\n    \"stdbuf\",\n    \"stty\",\n    \"tee\",\n    \"test\",\n    \"timeout\",\n    // \"true\", // keyword literal already\n    \"tty\",\n    \"uname\",\n    \"unlink\",\n    \"uptime\",\n    \"users\",\n    \"who\",\n    \"whoami\",\n    \"yes\"\n  ];\n\n  return {\n    name: 'Bash',\n    aliases: [ 'sh' ],\n    keywords: {\n      $pattern: /\\b[a-z][a-z0-9._-]+\\b/,\n      keyword: KEYWORDS,\n      literal: LITERALS,\n      built_in: [\n        ...SHELL_BUILT_INS,\n        ...BASH_BUILT_INS,\n        // Shell modifiers\n        \"set\",\n        \"shopt\",\n        ...ZSH_BUILT_INS,\n        ...GNU_CORE_UTILS\n      ]\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      hljs.HASH_COMMENT_MODE,\n      HERE_DOC,\n      PATH_MODE,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      VAR\n    ]\n  };\n}\n\nexport { bash as default };\n", "/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'scoped',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'equals',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'remove',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, { begin: '[a-zA-Z](\\\\.?\\\\w)*' });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [ { begin: '\"\"' } ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, { illegal: /\\n/ });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, { illegal: /\\n/ });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, { illegal: /\\n/ })\n  ];\n  const STRING = { variants: [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ] };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      { beginKeywords: \"in out\" },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                { begin: '<!--|-->' },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: { keyword: 'if else elif endif define undef warning error line region endregion pragma checksum' }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          { beginKeywords: \"where class\" },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[(?=[\\\\w])',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          { match: /\\(\\)/ },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nexport { csharp as default };\n", "/*\nLanguage: HTTP\nDescription: HTTP request and response headers with automatic body highlighting\nAuthor: <PERSON> <<EMAIL>>\nCategory: protocols, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/Overview\n*/\n\nfunction http(hljs) {\n  const regex = hljs.regex;\n  const VERSION = 'HTTP/(2|1\\\\.[01])';\n  const HEADER_NAME = /[A-Za-z][A-Za-z0-9-]*/;\n  const HEADER = {\n    className: 'attribute',\n    begin: regex.concat('^', HEADER_NAME, '(?=\\\\:\\\\s)'),\n    starts: { contains: [\n      {\n        className: \"punctuation\",\n        begin: /: /,\n        relevance: 0,\n        starts: {\n          end: '$',\n          relevance: 0\n        }\n      }\n    ] }\n  };\n  const HEADERS_AND_BODY = [\n    HEADER,\n    {\n      begin: '\\\\n\\\\n',\n      starts: {\n        subLanguage: [],\n        endsWithParent: true\n      }\n    }\n  ];\n\n  return {\n    name: 'HTTP',\n    aliases: [ 'https' ],\n    illegal: /\\S/,\n    contains: [\n      // response\n      {\n        begin: '^(?=' + VER<PERSON>ON + \" \\\\d{3})\",\n        end: /$/,\n        contains: [\n          {\n            className: \"meta\",\n            begin: VERSION\n          },\n          {\n            className: 'number',\n            begin: '\\\\b\\\\d{3}\\\\b'\n          }\n        ],\n        starts: {\n          end: /\\b\\B/,\n          illegal: /\\S/,\n          contains: HEADERS_AND_BODY\n        }\n      },\n      // request\n      {\n        begin: '(?=^[A-Z]+ (.*?) ' + VERSION + '$)',\n        end: /$/,\n        contains: [\n          {\n            className: 'string',\n            begin: ' ',\n            end: ' ',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: \"meta\",\n            begin: VERSION\n          },\n          {\n            className: 'keyword',\n            begin: '[A-Z]+'\n          }\n        ],\n        starts: {\n          end: /\\b\\B/,\n          illegal: /\\S/,\n          contains: HEADERS_AND_BODY\n        }\n      },\n      // to allow headers to work even without a preamble\n      hljs.inherit(HEADER, { relevance: 0 })\n    ]\n  };\n}\n\nexport { http as default };\n", "/*\nLanguage: JSON\nDescription: J<PERSON><PERSON> (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols, web\n*/\n\nfunction json(hljs) {\n  const ATTRIBUTE = {\n    className: 'attr',\n    begin: /\"(\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    relevance: 1.01\n  };\n  const PUNCTUATION = {\n    match: /[{}[\\],:]/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  // NOTE: normally we would rely on `keywords` for this but using a mode here allows us\n  // - to use the very tight `illegal: \\S` rule later to flag any other character\n  // - as illegal indicating that despite looking like JSON we do not truly have\n  // - JSON and thus improve false-positively greatly since J<PERSON><PERSON> will try and claim\n  // - all sorts of JSON looking stuff\n  const LITERALS_MODE = {\n    scope: \"literal\",\n    beginKeywords: LITERALS.join(\" \"),\n  };\n\n  return {\n    name: '<PERSON><PERSON><PERSON>',\n    keywords:{\n      literal: LITERALS,\n    },\n    contains: [\n      ATTRIBUTE,\n      PUNCTUATION,\n      hljs.QUOTE_STRING_MODE,\n      LITERALS_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ],\n    illegal: '\\\\S'\n  };\n}\n\nexport { json as default };\n", "/*\nLanguage: Julia\nDescription: Julia is a high-level, high-performance, dynamic programming language.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <ekrefred<PERSON>@gmail.com>\nWebsite: https://julialang.org\n*/\n\nfunction julia(hljs) {\n  // Since there are numerous special names in <PERSON>, it is too much trouble\n  // to maintain them by hand. Hence these names (i.e. keywords, literals and\n  // built-ins) are automatically generated from Julia 1.5.2 itself through\n  // the following scripts for each.\n\n  // ref: https://docs.julialang.org/en/v1/manual/variables/#Allowed-Variable-Names\n  const VARIABLE_NAME_RE = '[A-Za-z_\\\\u00A1-\\\\uFFFF][A-Za-z_0-9\\\\u00A1-\\\\uFFFF]*';\n\n  // # keyword generator, multi-word keywords handled manually below (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[\"in\", \"isa\", \"where\"]\n  // for kw in collect(x.keyword for x in REPLCompletions.complete_keyword(\"\"))\n  //     if !(contains(kw, \" \") || kw == \"struct\")\n  //         push!(res, kw)\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  const KEYWORD_LIST = [\n    'baremodule',\n    'begin',\n    'break',\n    'catch',\n    'ccall',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'elseif',\n    'end',\n    'export',\n    'false',\n    'finally',\n    'for',\n    'function',\n    'global',\n    'if',\n    'import',\n    'in',\n    'isa',\n    'let',\n    'local',\n    'macro',\n    'module',\n    'quote',\n    'return',\n    'true',\n    'try',\n    'using',\n    'where',\n    'while',\n  ];\n\n  // # literal generator (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[\"true\", \"false\"]\n  // for compl in filter!(x -> isa(x, REPLCompletions.ModuleCompletion) && (x.parent === Base || x.parent === Core),\n  //                     REPLCompletions.completions(\"\", 0)[1])\n  //     try\n  //         v = eval(Symbol(compl.mod))\n  //         if !(v isa Function || v isa Type || v isa TypeVar || v isa Module || v isa Colon)\n  //             push!(res, compl.mod)\n  //         end\n  //     catch e\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  const LITERAL_LIST = [\n    'ARGS',\n    'C_NULL',\n    'DEPOT_PATH',\n    'ENDIAN_BOM',\n    'ENV',\n    'Inf',\n    'Inf16',\n    'Inf32',\n    'Inf64',\n    'InsertionSort',\n    'LOAD_PATH',\n    'MergeSort',\n    'NaN',\n    'NaN16',\n    'NaN32',\n    'NaN64',\n    'PROGRAM_FILE',\n    'QuickSort',\n    'RoundDown',\n    'RoundFromZero',\n    'RoundNearest',\n    'RoundNearestTiesAway',\n    'RoundNearestTiesUp',\n    'RoundToZero',\n    'RoundUp',\n    'VERSION|0',\n    'devnull',\n    'false',\n    'im',\n    'missing',\n    'nothing',\n    'pi',\n    'stderr',\n    'stdin',\n    'stdout',\n    'true',\n    'undef',\n    'π',\n    'ℯ',\n  ];\n\n  // # built_in generator (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[]\n  // for compl in filter!(x -> isa(x, REPLCompletions.ModuleCompletion) && (x.parent === Base || x.parent === Core),\n  //                     REPLCompletions.completions(\"\", 0)[1])\n  //     try\n  //         v = eval(Symbol(compl.mod))\n  //         if (v isa Type || v isa TypeVar) && (compl.mod != \"=>\")\n  //             push!(res, compl.mod)\n  //         end\n  //     catch e\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  const BUILT_IN_LIST = [\n    'AbstractArray',\n    'AbstractChannel',\n    'AbstractChar',\n    'AbstractDict',\n    'AbstractDisplay',\n    'AbstractFloat',\n    'AbstractIrrational',\n    'AbstractMatrix',\n    'AbstractRange',\n    'AbstractSet',\n    'AbstractString',\n    'AbstractUnitRange',\n    'AbstractVecOrMat',\n    'AbstractVector',\n    'Any',\n    'ArgumentError',\n    'Array',\n    'AssertionError',\n    'BigFloat',\n    'BigInt',\n    'BitArray',\n    'BitMatrix',\n    'BitSet',\n    'BitVector',\n    'Bool',\n    'BoundsError',\n    'CapturedException',\n    'CartesianIndex',\n    'CartesianIndices',\n    'Cchar',\n    'Cdouble',\n    'Cfloat',\n    'Channel',\n    'Char',\n    'Cint',\n    'Cintmax_t',\n    'Clong',\n    'Clonglong',\n    'Cmd',\n    'Colon',\n    'Complex',\n    'ComplexF16',\n    'ComplexF32',\n    'ComplexF64',\n    'CompositeException',\n    'Condition',\n    'Cptrdiff_t',\n    'Cshort',\n    'Csize_t',\n    'Cssize_t',\n    'Cstring',\n    'Cuchar',\n    'Cuint',\n    'Cuintmax_t',\n    'Culong',\n    'Culonglong',\n    'Cushort',\n    'Cvoid',\n    'Cwchar_t',\n    'Cwstring',\n    'DataType',\n    'DenseArray',\n    'DenseMatrix',\n    'DenseVecOrMat',\n    'DenseVector',\n    'Dict',\n    'DimensionMismatch',\n    'Dims',\n    'DivideError',\n    'DomainError',\n    'EOFError',\n    'Enum',\n    'ErrorException',\n    'Exception',\n    'ExponentialBackOff',\n    'Expr',\n    'Float16',\n    'Float32',\n    'Float64',\n    'Function',\n    'GlobalRef',\n    'HTML',\n    'IO',\n    'IOBuffer',\n    'IOContext',\n    'IOStream',\n    'IdDict',\n    'IndexCartesian',\n    'IndexLinear',\n    'IndexStyle',\n    'InexactError',\n    'InitError',\n    'Int',\n    'Int128',\n    'Int16',\n    'Int32',\n    'Int64',\n    'Int8',\n    'Integer',\n    'InterruptException',\n    'InvalidStateException',\n    'Irrational',\n    'KeyError',\n    'LinRange',\n    'LineNumberNode',\n    'LinearIndices',\n    'LoadError',\n    'MIME',\n    'Matrix',\n    'Method',\n    'MethodError',\n    'Missing',\n    'MissingException',\n    'Module',\n    'NTuple',\n    'NamedTuple',\n    'Nothing',\n    'Number',\n    'OrdinalRange',\n    'OutOfMemoryError',\n    'OverflowError',\n    'Pair',\n    'PartialQuickSort',\n    'PermutedDimsArray',\n    'Pipe',\n    'ProcessFailedException',\n    'Ptr',\n    'QuoteNode',\n    'Rational',\n    'RawFD',\n    'ReadOnlyMemoryError',\n    'Real',\n    'ReentrantLock',\n    'Ref',\n    'Regex',\n    'RegexMatch',\n    'RoundingMode',\n    'SegmentationFault',\n    'Set',\n    'Signed',\n    'Some',\n    'StackOverflowError',\n    'StepRange',\n    'StepRangeLen',\n    'StridedArray',\n    'StridedMatrix',\n    'StridedVecOrMat',\n    'StridedVector',\n    'String',\n    'StringIndexError',\n    'SubArray',\n    'SubString',\n    'SubstitutionString',\n    'Symbol',\n    'SystemError',\n    'Task',\n    'TaskFailedException',\n    'Text',\n    'TextDisplay',\n    'Timer',\n    'Tuple',\n    'Type',\n    'TypeError',\n    'TypeVar',\n    'UInt',\n    'UInt128',\n    'UInt16',\n    'UInt32',\n    'UInt64',\n    'UInt8',\n    'UndefInitializer',\n    'UndefKeywordError',\n    'UndefRefError',\n    'UndefVarError',\n    'Union',\n    'UnionAll',\n    'UnitRange',\n    'Unsigned',\n    'Val',\n    'Vararg',\n    'VecElement',\n    'VecOrMat',\n    'Vector',\n    'VersionNumber',\n    'WeakKeyDict',\n    'WeakRef',\n  ];\n\n  const KEYWORDS = {\n    $pattern: VARIABLE_NAME_RE,\n    keyword: KEYWORD_LIST,\n    literal: LITERAL_LIST,\n    built_in: BUILT_IN_LIST,\n  };\n\n  // placeholder for recursive self-reference\n  const DEFAULT = {\n    keywords: KEYWORDS,\n    illegal: /<\\//\n  };\n\n  // ref: https://docs.julialang.org/en/v1/manual/integers-and-floating-point-numbers/\n  const NUMBER = {\n    className: 'number',\n    // supported numeric literals:\n    //  * binary literal (e.g. 0x10)\n    //  * octal literal (e.g. 0o76543210)\n    //  * hexadecimal literal (e.g. 0xfedcba876543210)\n    //  * hexadecimal floating point literal (e.g. 0x1p0, 0x1.2p2)\n    //  * decimal literal (e.g. 9876543210, 100_000_000)\n    //  * floating pointe literal (e.g. 1.2, 1.2f, .2, 1., 1.2e10, 1.2e-10)\n    begin: /(\\b0x[\\d_]*(\\.[\\d_]*)?|0x\\.\\d[\\d_]*)p[-+]?\\d+|\\b0[box][a-fA-F0-9][a-fA-F0-9_]*|(\\b\\d[\\d_]*(\\.[\\d_]*)?|\\.\\d[\\d_]*)([eEfF][-+]?\\d+)?/,\n    relevance: 0\n  };\n\n  const CHAR = {\n    className: 'string',\n    begin: /'(.|\\\\[xXuU][a-zA-Z0-9]+)'/\n  };\n\n  const INTERPOLATION = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS\n  };\n\n  const INTERPOLATED_VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + VARIABLE_NAME_RE\n  };\n\n  // TODO: neatly escape normal code in string literal\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      INTERPOLATION,\n      INTERPOLATED_VARIABLE\n    ],\n    variants: [\n      {\n        begin: /\\w*\"\"\"/,\n        end: /\"\"\"\\w*/,\n        relevance: 10\n      },\n      {\n        begin: /\\w*\"/,\n        end: /\"\\w*/\n      }\n    ]\n  };\n\n  const COMMAND = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      INTERPOLATION,\n      INTERPOLATED_VARIABLE\n    ],\n    begin: '`',\n    end: '`'\n  };\n\n  const MACROCALL = {\n    className: 'meta',\n    begin: '@' + VARIABLE_NAME_RE\n  };\n\n  const COMMENT = {\n    className: 'comment',\n    variants: [\n      {\n        begin: '#=',\n        end: '=#',\n        relevance: 10\n      },\n      {\n        begin: '#',\n        end: '$'\n      }\n    ]\n  };\n\n  DEFAULT.name = 'Julia';\n  DEFAULT.contains = [\n    NUMBER,\n    CHAR,\n    STRING,\n    COMMAND,\n    MACROCALL,\n    COMMENT,\n    hljs.HASH_COMMENT_MODE,\n    {\n      className: 'keyword',\n      begin:\n        '\\\\b(((abstract|primitive)\\\\s+)type|(mutable\\\\s+)?struct)\\\\b'\n    },\n    { begin: /<:/ } // relevance booster\n  ];\n  INTERPOLATION.contains = DEFAULT.contains;\n\n  return DEFAULT;\n}\n\nexport { julia as default };\n", "/*\nLanguage: Markdown\nRequires: xml.js\nAuthor: <PERSON> <john.cre<PERSON><PERSON>@gmail.com>\nWebsite: https://daringfireball.net/projects/markdown/\nCategory: common, markup\n*/\n\nfunction markdown(hljs) {\n  const regex = hljs.regex;\n  const INLINE_HTML = {\n    begin: /<\\/?[A-Za-z_]/,\n    end: '>',\n    subLanguage: 'xml',\n    relevance: 0\n  };\n  const HORIZONTAL_RULE = {\n    begin: '^[-\\\\*]{3,}',\n    end: '$'\n  };\n  const CODE = {\n    className: 'code',\n    variants: [\n      // TODO: fix to allow these to work with sublanguage also\n      { begin: '(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*' },\n      { begin: '(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*' },\n      // needed to allow markdown as a sublanguage to work\n      {\n        begin: '```',\n        end: '```+[ ]*$'\n      },\n      {\n        begin: '~~~',\n        end: '~~~+[ ]*$'\n      },\n      { begin: '`.+?`' },\n      {\n        begin: '(?=^( {4}|\\\\t))',\n        // use contains to gobble up multiple lines to allow the block to be whatever size\n        // but only have a single open/close tag vs one per line\n        contains: [\n          {\n            begin: '^( {4}|\\\\t)',\n            end: '(\\\\n)$'\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n  const LIST = {\n    className: 'bullet',\n    begin: '^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)',\n    end: '\\\\s+',\n    excludeEnd: true\n  };\n  const LINK_REFERENCE = {\n    begin: /^\\[[^\\n]+\\]:/,\n    returnBegin: true,\n    contains: [\n      {\n        className: 'symbol',\n        begin: /\\[/,\n        end: /\\]/,\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'link',\n        begin: /:\\s*/,\n        end: /$/,\n        excludeBegin: true\n      }\n    ]\n  };\n  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;\n  const LINK = {\n    variants: [\n      // too much like nested array access in so many languages\n      // to have any real relevance\n      {\n        begin: /\\[.+?\\]\\[.*?\\]/,\n        relevance: 0\n      },\n      // popular internet URLs\n      {\n        begin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\n        relevance: 2\n      },\n      {\n        begin: regex.concat(/\\[.+?\\]\\(/, URL_SCHEME, /:\\/\\/.*?\\)/),\n        relevance: 2\n      },\n      // relative urls\n      {\n        begin: /\\[.+?\\]\\([./?&#].*?\\)/,\n        relevance: 1\n      },\n      // whatever else, lower relevance (might not be a link at all)\n      {\n        begin: /\\[.*?\\]\\(.*?\\)/,\n        relevance: 0\n      }\n    ],\n    returnBegin: true,\n    contains: [\n      {\n        // empty strings for alt or link text\n        match: /\\[(?=\\])/ },\n      {\n        className: 'string',\n        relevance: 0,\n        begin: '\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        returnEnd: true\n      },\n      {\n        className: 'link',\n        relevance: 0,\n        begin: '\\\\]\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'symbol',\n        relevance: 0,\n        begin: '\\\\]\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n  const BOLD = {\n    className: 'strong',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /_{2}/,\n        end: /_{2}/\n      },\n      {\n        begin: /\\*{2}/,\n        end: /\\*{2}/\n      }\n    ]\n  };\n  const ITALIC = {\n    className: 'emphasis',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /\\*(?!\\*)/,\n        end: /\\*/\n      },\n      {\n        begin: /_(?!_)/,\n        end: /_/,\n        relevance: 0\n      }\n    ]\n  };\n\n  // 3 level deep nesting is not allowed because it would create confusion\n  // in cases like `***testing***` because where we don't know if the last\n  // `***` is starting a new bold/italic or finishing the last one\n  const BOLD_WITHOUT_ITALIC = hljs.inherit(BOLD, { contains: [] });\n  const ITALIC_WITHOUT_BOLD = hljs.inherit(ITALIC, { contains: [] });\n  BOLD.contains.push(ITALIC_WITHOUT_BOLD);\n  ITALIC.contains.push(BOLD_WITHOUT_ITALIC);\n\n  let CONTAINABLE = [\n    INLINE_HTML,\n    LINK\n  ];\n\n  [\n    BOLD,\n    ITALIC,\n    BOLD_WITHOUT_ITALIC,\n    ITALIC_WITHOUT_BOLD\n  ].forEach(m => {\n    m.contains = m.contains.concat(CONTAINABLE);\n  });\n\n  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);\n\n  const HEADER = {\n    className: 'section',\n    variants: [\n      {\n        begin: '^#{1,6}',\n        end: '$',\n        contains: CONTAINABLE\n      },\n      {\n        begin: '(?=^.+?\\\\n[=-]{2,}$)',\n        contains: [\n          { begin: '^[=-]*$' },\n          {\n            begin: '^',\n            end: \"\\\\n\",\n            contains: CONTAINABLE\n          }\n        ]\n      }\n    ]\n  };\n\n  const BLOCKQUOTE = {\n    className: 'quote',\n    begin: '^>\\\\s+',\n    contains: CONTAINABLE,\n    end: '$'\n  };\n\n  return {\n    name: 'Markdown',\n    aliases: [\n      'md',\n      'mkdown',\n      'mkd'\n    ],\n    contains: [\n      HEADER,\n      INLINE_HTML,\n      LIST,\n      BOLD,\n      ITALIC,\n      BLOCKQUOTE,\n      CODE,\n      HORIZONTAL_RULE,\n      LINK,\n      LINK_REFERENCE\n    ]\n  };\n}\n\nexport { markdown as default };\n", "/*\nLanguage: Matlab\nAuthor: <PERSON> <bar<PERSON><PERSON><PERSON><PERSON>@gmail.com>\nContributors: <PERSON> <ni<PERSON><PERSON><PERSON>@ya.ru>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.mathworks.com/products/matlab.html\nCategory: scientific\n*/\n\n/*\n  Formal syntax is not published, helpful link:\n  https://github.com/kornilova-l/matlab-IntelliJ-plugin/blob/master/src/main/grammar/Matlab.bnf\n*/\nfunction matlab(hljs) {\n  const TRANSPOSE_RE = '(\\'|\\\\.\\')+';\n  const TRANSPOSE = {\n    relevance: 0,\n    contains: [ { begin: TRANSPOSE_RE } ]\n  };\n\n  return {\n    name: 'Matlab',\n    keywords: {\n      keyword:\n        'arguments break case catch classdef continue else elseif end enumeration events for function '\n        + 'global if methods otherwise parfor persistent properties return spmd switch try while',\n      built_in:\n        'sin sind sinh asin asind asinh cos cosd cosh acos acosd acosh tan tand tanh atan '\n        + 'atand atan2 atanh sec secd sech asec asecd asech csc cscd csch acsc acscd acsch cot '\n        + 'cotd coth acot acotd acoth hypot exp expm1 log log1p log10 log2 pow2 realpow reallog '\n        + 'realsqrt sqrt nthroot nextpow2 abs angle complex conj imag real unwrap isreal '\n        + 'cplxpair fix floor ceil round mod rem sign airy besselj bessely besselh besseli '\n        + 'besselk beta betainc betaln ellipj ellipke erf erfc erfcx erfinv expint gamma '\n        + 'gammainc gammaln psi legendre cross dot factor isprime primes gcd lcm rat rats perms '\n        + 'nchoosek factorial cart2sph cart2pol pol2cart sph2cart hsv2rgb rgb2hsv zeros ones '\n        + 'eye repmat rand randn linspace logspace freqspace meshgrid accumarray size length '\n        + 'ndims numel disp isempty isequal isequalwithequalnans cat reshape diag blkdiag tril '\n        + 'triu fliplr flipud flipdim rot90 find sub2ind ind2sub bsxfun ndgrid permute ipermute '\n        + 'shiftdim circshift squeeze isscalar isvector ans eps realmax realmin pi i|0 inf nan '\n        + 'isnan isinf isfinite j|0 why compan gallery hadamard hankel hilb invhilb magic pascal '\n        + 'rosser toeplitz vander wilkinson max min nanmax nanmin mean nanmean type table '\n        + 'readtable writetable sortrows sort figure plot plot3 scatter scatter3 cellfun '\n        + 'legend intersect ismember procrustes hold num2cell '\n    },\n    illegal: '(//|\"|#|/\\\\*|\\\\s+/\\\\w+)',\n    contains: [\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '$',\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'params',\n            variants: [\n              {\n                begin: '\\\\(',\n                end: '\\\\)'\n              },\n              {\n                begin: '\\\\[',\n                end: '\\\\]'\n              }\n            ]\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        begin: /true|false/,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        begin: '[a-zA-Z][a-zA-Z_0-9]*' + TRANSPOSE_RE,\n        relevance: 0\n      },\n      {\n        className: 'number',\n        begin: hljs.C_NUMBER_RE,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '\\'',\n        contains: [ { begin: '\\'\\'' } ]\n      },\n      {\n        begin: /\\]|\\}|\\)/,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        contains: [ { begin: '\"\"' } ],\n        starts: TRANSPOSE\n      },\n      hljs.COMMENT('^\\\\s*%\\\\{\\\\s*$', '^\\\\s*%\\\\}\\\\s*$'),\n      hljs.COMMENT('%', '$')\n    ]\n  };\n}\n\nexport { matlab as default };\n", "/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: [\n      'text',\n      'txt'\n    ],\n    disableAutodetect: true\n  };\n}\n\nexport { plaintext as default };\n", "/*\nLanguage: Python\nDescription: Python is an interpreted, object-oriented, high-level programming language with dynamic semantics.\nWebsite: https://www.python.org\nCategory: common\n*/\n\nfunction python(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[\\p{XID_Start}_]\\p{XID_Continue}*/u;\n  const RESERVED_WORDS = [\n    'and',\n    'as',\n    'assert',\n    'async',\n    'await',\n    'break',\n    'case',\n    'class',\n    'continue',\n    'def',\n    'del',\n    'elif',\n    'else',\n    'except',\n    'finally',\n    'for',\n    'from',\n    'global',\n    'if',\n    'import',\n    'in',\n    'is',\n    'lambda',\n    'match',\n    'nonlocal|10',\n    'not',\n    'or',\n    'pass',\n    'raise',\n    'return',\n    'try',\n    'while',\n    'with',\n    'yield'\n  ];\n\n  const BUILT_INS = [\n    '__import__',\n    'abs',\n    'all',\n    'any',\n    'ascii',\n    'bin',\n    'bool',\n    'breakpoint',\n    'bytearray',\n    'bytes',\n    'callable',\n    'chr',\n    'classmethod',\n    'compile',\n    'complex',\n    'delattr',\n    'dict',\n    'dir',\n    'divmod',\n    'enumerate',\n    'eval',\n    'exec',\n    'filter',\n    'float',\n    'format',\n    'frozenset',\n    'getattr',\n    'globals',\n    'hasattr',\n    'hash',\n    'help',\n    'hex',\n    'id',\n    'input',\n    'int',\n    'isinstance',\n    'issubclass',\n    'iter',\n    'len',\n    'list',\n    'locals',\n    'map',\n    'max',\n    'memoryview',\n    'min',\n    'next',\n    'object',\n    'oct',\n    'open',\n    'ord',\n    'pow',\n    'print',\n    'property',\n    'range',\n    'repr',\n    'reversed',\n    'round',\n    'set',\n    'setattr',\n    'slice',\n    'sorted',\n    'staticmethod',\n    'str',\n    'sum',\n    'super',\n    'tuple',\n    'type',\n    'vars',\n    'zip'\n  ];\n\n  const LITERALS = [\n    '__debug__',\n    'Ellipsis',\n    'False',\n    'None',\n    'NotImplemented',\n    'True'\n  ];\n\n  // https://docs.python.org/3/library/typing.html\n  // TODO: Could these be supplemented by a CamelCase matcher in certain\n  // contexts, leaving these remaining only for relevance hinting?\n  const TYPES = [\n    \"Any\",\n    \"Callable\",\n    \"Coroutine\",\n    \"Dict\",\n    \"List\",\n    \"Literal\",\n    \"Generic\",\n    \"Optional\",\n    \"Sequence\",\n    \"Set\",\n    \"Tuple\",\n    \"Type\",\n    \"Union\"\n  ];\n\n  const KEYWORDS = {\n    $pattern: /[A-Za-z]\\w+|__\\w+__/,\n    keyword: RESERVED_WORDS,\n    built_in: BUILT_INS,\n    literal: LITERALS,\n    type: TYPES\n  };\n\n  const PROMPT = {\n    className: 'meta',\n    begin: /^(>>>|\\.\\.\\.) /\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS,\n    illegal: /#/\n  };\n\n  const LITERAL_BRACKET = {\n    begin: /\\{\\{/,\n    relevance: 0\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([uU]|[rR])'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[rR])\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])'/,\n        end: /'/\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])\"/,\n        end: /\"/\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'/,\n        end: /'/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"/,\n        end: /\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#numeric-literals\n  const digitpart = '[0-9](_?[0-9])*';\n  const pointfloat = `(\\\\b(${digitpart}))?\\\\.(${digitpart})|\\\\b(${digitpart})\\\\.`;\n  // Whitespace after a number (or any lexical token) is needed only if its absence\n  // would change the tokenization\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#whitespace-between-tokens\n  // We deviate slightly, requiring a word boundary or a keyword\n  // to avoid accidentally recognizing *prefixes* (e.g., `0` in `0x41` or `08` or `0__1`)\n  const lookahead = `\\\\b|${RESERVED_WORDS.join('|')}`;\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // exponentfloat, pointfloat\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#floating-point-literals\n      // optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      // Note: no leading \\b because floats can start with a decimal point\n      // and we don't want to mishandle e.g. `fn(.5)`,\n      // no trailing \\b for pointfloat because it can end with a decimal point\n      // and we don't want to mishandle e.g. `0..hex()`; this should be safe\n      // because both MUST contain a decimal point and so cannot be confused with\n      // the interior part of an identifier\n      {\n        begin: `(\\\\b(${digitpart})|(${pointfloat}))[eE][+-]?(${digitpart})[jJ]?(?=${lookahead})`\n      },\n      {\n        begin: `(${pointfloat})[jJ]?`\n      },\n\n      // decinteger, bininteger, octinteger, hexinteger\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#integer-literals\n      // optionally \"long\" in Python 2\n      // https://docs.python.org/2.7/reference/lexical_analysis.html#integer-and-long-integer-literals\n      // decinteger is optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[bB](_?[01])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${lookahead})`\n      },\n\n      // imagnumber (digitpart-based)\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b(${digitpart})[jJ](?=${lookahead})`\n      }\n    ]\n  };\n  const COMMENT_TYPE = {\n    className: \"comment\",\n    begin: regex.lookahead(/# type:/),\n    end: /$/,\n    keywords: KEYWORDS,\n    contains: [\n      { // prevent keywords from coloring `type`\n        begin: /# type:/\n      },\n      // comment within a datatype comment includes no keywords\n      {\n        begin: /#/,\n        end: /\\b\\B/,\n        endsWithParent: true\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    variants: [\n      // Exclude params in functions without params\n      {\n        className: \"\",\n        begin: /\\(\\s*\\)/,\n        skip: true\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          'self',\n          PROMPT,\n          NUMBER,\n          STRING,\n          hljs.HASH_COMMENT_MODE\n        ]\n      }\n    ]\n  };\n  SUBST.contains = [\n    STRING,\n    NUMBER,\n    PROMPT\n  ];\n\n  return {\n    name: 'Python',\n    aliases: [\n      'py',\n      'gyp',\n      'ipython'\n    ],\n    unicodeRegex: true,\n    keywords: KEYWORDS,\n    illegal: /(<\\/|->|\\?)|=>/,\n    contains: [\n      PROMPT,\n      NUMBER,\n      {\n        // very common convention\n        begin: /\\bself\\b/\n      },\n      {\n        // eat \"if\" prior to string so that it won't accidentally be\n        // labeled as an f-string\n        beginKeywords: \"if\",\n        relevance: 0\n      },\n      STRING,\n      COMMENT_TYPE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        match: [\n          /\\bdef/, /\\s+/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.function\"\n        },\n        contains: [ PARAMS ]\n      },\n      {\n        variants: [\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE, /\\s*/,\n              /\\(\\s*/, IDENT_RE,/\\s*\\)/\n            ],\n          },\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE\n            ],\n          }\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          6: \"title.class.inherited\",\n        }\n      },\n      {\n        className: 'meta',\n        begin: /^[\\t ]*@/,\n        end: /(?=#)|$/,\n        contains: [\n          NUMBER,\n          PARAMS,\n          STRING\n        ]\n      }\n    ]\n  };\n}\n\nexport { python as default };\n", "/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  const regex = hljs.regex;\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const NUMBER_TYPES_RE = regex.either(\n    // Special case: only hexadecimal binary powers can contain fractions\n    /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/,\n    // Hexadecimal numbers without fraction and optional binary power\n    /0[xX][0-9a-fA-F]+(?:[pP][+-]?\\d+)?[Li]?/,\n    // Decimal numbers\n    /(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?[Li]?/\n  );\n  const OPERATORS_RE = /[=!<>:]=|\\|\\||&&|:::?|<-|<<-|->>|->|\\|>|[-+*\\/?!$&|:<=>@^~]|\\*\\*/;\n  const PUNCTUATION_RE = regex.either(\n    /[()]/,\n    /[{}]/,\n    /\\[\\[/,\n    /[[\\]]/,\n    /\\\\/,\n    /,/\n  );\n\n  return {\n    name: 'R',\n\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword:\n        'function if in break next repeat else for while',\n      literal:\n        'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 '\n        + 'NA_character_|10 NA_complex_|10',\n      built_in:\n        // Builtin constants\n        'LETTERS letters month.abb month.name pi T F '\n        // Primitive functions\n        // These are all the functions in `base` that are implemented as a\n        // `.Primitive`, minus those functions that are also keywords.\n        + 'abs acos acosh all any anyNA Arg as.call as.character '\n        + 'as.complex as.double as.environment as.integer as.logical '\n        + 'as.null.default as.numeric as.raw asin asinh atan atanh attr '\n        + 'attributes baseenv browser c call ceiling class Conj cos cosh '\n        + 'cospi cummax cummin cumprod cumsum digamma dim dimnames '\n        + 'emptyenv exp expression floor forceAndCall gamma gc.time '\n        + 'globalenv Im interactive invisible is.array is.atomic is.call '\n        + 'is.character is.complex is.double is.environment is.expression '\n        + 'is.finite is.function is.infinite is.integer is.language '\n        + 'is.list is.logical is.matrix is.na is.name is.nan is.null '\n        + 'is.numeric is.object is.pairlist is.raw is.recursive is.single '\n        + 'is.symbol lazyLoadDBfetch length lgamma list log max min '\n        + 'missing Mod names nargs nzchar oldClass on.exit pos.to.env '\n        + 'proc.time prod quote range Re rep retracemem return round '\n        + 'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt '\n        + 'standardGeneric substitute sum switch tan tanh tanpi tracemem '\n        + 'trigamma trunc unclass untracemem UseMethod xtfrm',\n    },\n\n    contains: [\n      // Roxygen comments\n      hljs.COMMENT(\n        /#'/,\n        /$/,\n        { contains: [\n          {\n            // Handle `@examples` separately to cause all subsequent code\n            // until the next `@`-tag on its own line to be kept as-is,\n            // preventing highlighting. This code is example R code, so nested\n            // doctags shouldn’t be treated as such. See\n            // `test/markup/r/roxygen.txt` for an example.\n            scope: 'doctag',\n            match: /@examples/,\n            starts: {\n              end: regex.lookahead(regex.either(\n                // end if another doc comment\n                /\\n^#'\\s*(?=@[a-zA-Z]+)/,\n                // or a line with no comment\n                /\\n^(?!#')/\n              )),\n              endsParent: true\n            }\n          },\n          {\n            // Handle `@param` to highlight the parameter name following\n            // after.\n            scope: 'doctag',\n            begin: '@param',\n            end: /$/,\n            contains: [\n              {\n                scope: 'variable',\n                variants: [\n                  { match: IDENT_RE },\n                  { match: /`(?:\\\\.|[^`\\\\])+`/ }\n                ],\n                endsParent: true\n              }\n            ]\n          },\n          {\n            scope: 'doctag',\n            match: /@[a-zA-Z]+/\n          },\n          {\n            scope: 'keyword',\n            match: /\\\\[a-zA-Z]+/\n          }\n        ] }\n      ),\n\n      hljs.HASH_COMMENT_MODE,\n\n      {\n        scope: 'string',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        variants: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\(/,\n            end: /\\)(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\{/,\n            end: /\\}(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\[/,\n            end: /\\](-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\(/,\n            end: /\\)(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\{/,\n            end: /\\}(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\[/,\n            end: /\\](-*)'/\n          }),\n          {\n            begin: '\"',\n            end: '\"',\n            relevance: 0\n          },\n          {\n            begin: \"'\",\n            end: \"'\",\n            relevance: 0\n          }\n        ],\n      },\n\n      // Matching numbers immediately following punctuation and operators is\n      // tricky since we need to look at the character ahead of a number to\n      // ensure the number is not part of an identifier, and we cannot use\n      // negative look-behind assertions. So instead we explicitly handle all\n      // possible combinations of (operator|punctuation), number.\n      // TODO: replace with negative look-behind when available\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n      // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n      {\n        relevance: 0,\n        variants: [\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              OPERATORS_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              /%[^%]*%/,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'punctuation',\n              2: 'number'\n            },\n            match: [\n              PUNCTUATION_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: { 2: 'number' },\n            match: [\n              /[^a-zA-Z0-9._]|^/, // not part of an identifier, or start of document\n              NUMBER_TYPES_RE\n            ]\n          }\n        ]\n      },\n\n      // Operators/punctuation when they're not directly followed by numbers\n      {\n        // Relevance boost for the most common assignment form.\n        scope: { 3: 'operator' },\n        match: [\n          IDENT_RE,\n          /\\s+/,\n          /<-/,\n          /\\s+/\n        ]\n      },\n\n      {\n        scope: 'operator',\n        relevance: 0,\n        variants: [\n          { match: OPERATORS_RE },\n          { match: /%[^%]*%/ }\n        ]\n      },\n\n      {\n        scope: 'punctuation',\n        relevance: 0,\n        match: PUNCTUATION_RE\n      },\n\n      {\n        // Escaped identifier\n        begin: '`',\n        end: '`',\n        contains: [ { begin: /\\\\./ } ]\n      }\n    ]\n  };\n}\n\nexport { r as default };\n", "/*\nLanguage: Ruby\nDescription: Ruby is a dynamic, open source programming language with a focus on simplicity and productivity.\nWebsite: https://www.ruby-lang.org/\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction ruby(hljs) {\n  const regex = hljs.regex;\n  const RUBY_METHOD_RE = '([a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?)';\n  // TODO: move concepts like CAMEL_CASE into `modes.js`\n  const CLASS_NAME_RE = regex.either(\n    /\\b([A-Z]+[a-z0-9]+)+/,\n    // ends in caps\n    /\\b([A-Z]+[a-z0-9]+)+[A-Z]+/,\n  )\n  ;\n  const CLASS_NAME_WITH_NAMESPACE_RE = regex.concat(CLASS_NAME_RE, /(::\\w+)*/);\n  const RUBY_KEYWORDS = {\n    \"variable.constant\": [\n      \"__FILE__\",\n      \"__LINE__\"\n    ],\n    \"variable.language\": [\n      \"self\",\n      \"super\",\n    ],\n    keyword: [\n      \"alias\",\n      \"and\",\n      \"attr_accessor\",\n      \"attr_reader\",\n      \"attr_writer\",\n      \"begin\",\n      \"BEGIN\",\n      \"break\",\n      \"case\",\n      \"class\",\n      \"defined\",\n      \"do\",\n      \"else\",\n      \"elsif\",\n      \"end\",\n      \"END\",\n      \"ensure\",\n      \"for\",\n      \"if\",\n      \"in\",\n      \"include\",\n      \"module\",\n      \"next\",\n      \"not\",\n      \"or\",\n      \"redo\",\n      \"require\",\n      \"rescue\",\n      \"retry\",\n      \"return\",\n      \"then\",\n      \"undef\",\n      \"unless\",\n      \"until\",\n      \"when\",\n      \"while\",\n      \"yield\",\n    ],\n    built_in: [\n      \"proc\",\n      \"lambda\"\n    ],\n    literal: [\n      \"true\",\n      \"false\",\n      \"nil\"\n    ]\n  };\n  const YARDOCTAG = {\n    className: 'doctag',\n    begin: '@[A-Za-z]+'\n  };\n  const IRB_OBJECT = {\n    begin: '#<',\n    end: '>'\n  };\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      '#',\n      '$',\n      { contains: [ YARDOCTAG ] }\n    ),\n    hljs.COMMENT(\n      '^=begin',\n      '^=end',\n      {\n        contains: [ YARDOCTAG ],\n        relevance: 10\n      }\n    ),\n    hljs.COMMENT('^__END__', hljs.MATCH_NOTHING_RE)\n  ];\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: RUBY_KEYWORDS\n  };\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /`/,\n        end: /`/\n      },\n      {\n        begin: /%[qQwWx]?\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /%[qQwWx]?\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /%[qQwWx]?\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /%[qQwWx]?</,\n        end: />/\n      },\n      {\n        begin: /%[qQwWx]?\\//,\n        end: /\\//\n      },\n      {\n        begin: /%[qQwWx]?%/,\n        end: /%/\n      },\n      {\n        begin: /%[qQwWx]?-/,\n        end: /-/\n      },\n      {\n        begin: /%[qQwWx]?\\|/,\n        end: /\\|/\n      },\n      // in the following expressions, \\B in the beginning suppresses recognition of ?-sequences\n      // where ? is the last character of a preceding identifier, as in: `func?4`\n      { begin: /\\B\\?(\\\\\\d{1,3})/ },\n      { begin: /\\B\\?(\\\\x[A-Fa-f0-9]{1,2})/ },\n      { begin: /\\B\\?(\\\\u\\{?[A-Fa-f0-9]{1,6}\\}?)/ },\n      { begin: /\\B\\?(\\\\M-\\\\C-|\\\\M-\\\\c|\\\\c\\\\M-|\\\\M-|\\\\C-\\\\M-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\(c|C-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\?\\S/ },\n      // heredocs\n      {\n        // this guard makes sure that we have an entire heredoc and not a false\n        // positive (auto-detect, etc.)\n        begin: regex.concat(\n          /<<[-~]?'?/,\n          regex.lookahead(/(\\w+)(?=\\W)[^\\n]*\\n(?:[^\\n]*\\n)*?\\s*\\1\\b/)\n        ),\n        contains: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /(\\w+)/,\n            end: /(\\w+)/,\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ]\n          })\n        ]\n      }\n    ]\n  };\n\n  // Ruby syntax is underdocumented, but this grammar seems to be accurate\n  // as of version 2.7.2 (confirmed with (irb and `Ripper.sexp(...)`)\n  // https://docs.ruby-lang.org/en/2.7.0/doc/syntax/literals_rdoc.html#label-Numbers\n  const decimal = '[1-9](_?[0-9])*|0';\n  const digits = '[0-9](_?[0-9])*';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal integer/float, optionally exponential or rational, optionally imaginary\n      { begin: `\\\\b(${decimal})(\\\\.(${digits}))?([eE][+-]?(${digits})|r)?i?\\\\b` },\n\n      // explicit decimal/binary/octal/hexadecimal integer,\n      // optionally rational and/or imaginary\n      { begin: \"\\\\b0[dD][0-9](_?[0-9])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\\\b\" },\n\n      // 0-prefixed implicit octal integer, optionally rational and/or imaginary\n      { begin: \"\\\\b0(_?[0-7])+r?i?\\\\b\" }\n    ]\n  };\n\n  const PARAMS = {\n    variants: [\n      {\n        match: /\\(\\)/,\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /(?=\\))/,\n        excludeBegin: true,\n        endsParent: true,\n        keywords: RUBY_KEYWORDS,\n      }\n    ]\n  };\n\n  const CLASS_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /class\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE,\n          /\\s+<\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      },\n      {\n        match: [\n          /class\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      }\n    ],\n    scope: {\n      2: \"title.class\",\n      4: \"title.class.inherited\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  const METHOD_DEFINITION = {\n    match: [\n      /def/, /\\s+/,\n      RUBY_METHOD_RE\n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  const OBJECT_CREATION = {\n    relevance: 0,\n    match: [\n      CLASS_NAME_WITH_NAMESPACE_RE,\n      /\\.new[ (]/\n    ],\n    scope: {\n      1: \"title.class\"\n    }\n  };\n\n  const RUBY_DEFAULT_CONTAINS = [\n    STRING,\n    CLASS_DEFINITION,\n    OBJECT_CREATION,\n    UPPER_CASE_CONSTANT,\n    METHOD_DEFINITION,\n    {\n      // swallow namespace qualifiers before symbols\n      begin: hljs.IDENT_RE + '::' },\n    {\n      className: 'symbol',\n      begin: hljs.UNDERSCORE_IDENT_RE + '(!|\\\\?)?:',\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ':(?!\\\\s)',\n      contains: [\n        STRING,\n        { begin: RUBY_METHOD_RE }\n      ],\n      relevance: 0\n    },\n    NUMBER,\n    {\n      // negative-look forward attempts to prevent false matches like:\n      // @ident@ or $ident$ that might indicate this is not ruby at all\n      className: \"variable\",\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))(?=[^@$?])' + `(?![A-Za-z])(?![@$?'])`\n    },\n    {\n      className: 'params',\n      begin: /\\|/,\n      end: /\\|/,\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0, // this could be a lot of things (in other languages) other than params\n      keywords: RUBY_KEYWORDS\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + '|unless)\\\\s*',\n      keywords: 'unless',\n      contains: [\n        {\n          className: 'regexp',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          illegal: /\\n/,\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: /%r\\{/,\n              end: /\\}[a-z]*/\n            },\n            {\n              begin: '%r\\\\(',\n              end: '\\\\)[a-z]*'\n            },\n            {\n              begin: '%r!',\n              end: '![a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ].concat(IRB_OBJECT, COMMENT_MODES),\n      relevance: 0\n    }\n  ].concat(IRB_OBJECT, COMMENT_MODES);\n\n  SUBST.contains = RUBY_DEFAULT_CONTAINS;\n  PARAMS.contains = RUBY_DEFAULT_CONTAINS;\n\n  // >>\n  // ?>\n  const SIMPLE_PROMPT = \"[>?]>\";\n  // irb(main):001:0>\n  const DEFAULT_PROMPT = \"[\\\\w#]+\\\\(\\\\w+\\\\):\\\\d+:\\\\d+[>*]\";\n  const RVM_PROMPT = \"(\\\\w+-)?\\\\d+\\\\.\\\\d+\\\\.\\\\d+(p\\\\d+)?[^\\\\d][^>]+>\";\n\n  const IRB_DEFAULT = [\n    {\n      begin: /^\\s*=>/,\n      starts: {\n        end: '$',\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    },\n    {\n      className: 'meta.prompt',\n      begin: '^(' + SIMPLE_PROMPT + \"|\" + DEFAULT_PROMPT + '|' + RVM_PROMPT + ')(?=[ ])',\n      starts: {\n        end: '$',\n        keywords: RUBY_KEYWORDS,\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    }\n  ];\n\n  COMMENT_MODES.unshift(IRB_OBJECT);\n\n  return {\n    name: 'Ruby',\n    aliases: [\n      'rb',\n      'gemspec',\n      'podspec',\n      'thor',\n      'irb'\n    ],\n    keywords: RUBY_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [ hljs.SHEBANG({ binary: \"ruby\" }) ]\n      .concat(IRB_DEFAULT)\n      .concat(COMMENT_MODES)\n      .concat(RUBY_DEFAULT_CONTAINS)\n  };\n}\n\nexport { ruby as default };\n", "/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON>e <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [\n      'console',\n      'shellsession'\n    ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nexport { shell as default };\n", "/*\n Language: SQL\n Website: https://en.wikipedia.org/wiki/SQL\n Category: common, database\n */\n\n/*\n\nGoals:\n\nSQL is intended to highlight basic/common SQL keywords and expressions\n\n- If pretty much every single SQL server includes supports, then it's a canidate.\n- It is NOT intended to include tons of vendor specific keywords (Oracle, MySQL,\n  PostgreSQL) although the list of data types is purposely a bit more expansive.\n- For more specific SQL grammars please see:\n  - PostgreSQL and PL/pgSQL - core\n  - T-SQL - https://github.com/highlightjs/highlightjs-tsql\n  - sql_more (core)\n\n */\n\nfunction sql(hljs) {\n  const regex = hljs.regex;\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [ { begin: /''/ } ]\n      }\n    ]\n  };\n  const QUOTED_IDENTIFIER = {\n    begin: /\"/,\n    end: /\"/,\n    contains: [ { begin: /\"\"/ } ]\n  };\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    // Not sure it's correct to call NULL literal, and clauses like IS [NOT] NULL look strange that way.\n    // \"null\",\n    \"unknown\"\n  ];\n\n  const MULTI_WORD_TYPES = [\n    \"double precision\",\n    \"large object\",\n    \"with timezone\",\n    \"without timezone\"\n  ];\n\n  const TYPES = [\n    'bigint',\n    'binary',\n    'blob',\n    'boolean',\n    'char',\n    'character',\n    'clob',\n    'date',\n    'dec',\n    'decfloat',\n    'decimal',\n    'float',\n    'int',\n    'integer',\n    'interval',\n    'nchar',\n    'nclob',\n    'national',\n    'numeric',\n    'real',\n    'row',\n    'smallint',\n    'time',\n    'timestamp',\n    'varchar',\n    'varying', // modifier (character varying)\n    'varbinary'\n  ];\n\n  const NON_RESERVED_WORDS = [\n    \"add\",\n    \"asc\",\n    \"collation\",\n    \"desc\",\n    \"final\",\n    \"first\",\n    \"last\",\n    \"view\"\n  ];\n\n  // https://jakewheat.github.io/sql-overview/sql-2016-foundation-grammar.html#reserved-word\n  const RESERVED_WORDS = [\n    \"abs\",\n    \"acos\",\n    \"all\",\n    \"allocate\",\n    \"alter\",\n    \"and\",\n    \"any\",\n    \"are\",\n    \"array\",\n    \"array_agg\",\n    \"array_max_cardinality\",\n    \"as\",\n    \"asensitive\",\n    \"asin\",\n    \"asymmetric\",\n    \"at\",\n    \"atan\",\n    \"atomic\",\n    \"authorization\",\n    \"avg\",\n    \"begin\",\n    \"begin_frame\",\n    \"begin_partition\",\n    \"between\",\n    \"bigint\",\n    \"binary\",\n    \"blob\",\n    \"boolean\",\n    \"both\",\n    \"by\",\n    \"call\",\n    \"called\",\n    \"cardinality\",\n    \"cascaded\",\n    \"case\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"char\",\n    \"char_length\",\n    \"character\",\n    \"character_length\",\n    \"check\",\n    \"classifier\",\n    \"clob\",\n    \"close\",\n    \"coalesce\",\n    \"collate\",\n    \"collect\",\n    \"column\",\n    \"commit\",\n    \"condition\",\n    \"connect\",\n    \"constraint\",\n    \"contains\",\n    \"convert\",\n    \"copy\",\n    \"corr\",\n    \"corresponding\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"create\",\n    \"cross\",\n    \"cube\",\n    \"cume_dist\",\n    \"current\",\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_row\",\n    \"current_schema\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_path\",\n    \"current_role\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"cursor\",\n    \"cycle\",\n    \"date\",\n    \"day\",\n    \"deallocate\",\n    \"dec\",\n    \"decimal\",\n    \"decfloat\",\n    \"declare\",\n    \"default\",\n    \"define\",\n    \"delete\",\n    \"dense_rank\",\n    \"deref\",\n    \"describe\",\n    \"deterministic\",\n    \"disconnect\",\n    \"distinct\",\n    \"double\",\n    \"drop\",\n    \"dynamic\",\n    \"each\",\n    \"element\",\n    \"else\",\n    \"empty\",\n    \"end\",\n    \"end_frame\",\n    \"end_partition\",\n    \"end-exec\",\n    \"equals\",\n    \"escape\",\n    \"every\",\n    \"except\",\n    \"exec\",\n    \"execute\",\n    \"exists\",\n    \"exp\",\n    \"external\",\n    \"extract\",\n    \"false\",\n    \"fetch\",\n    \"filter\",\n    \"first_value\",\n    \"float\",\n    \"floor\",\n    \"for\",\n    \"foreign\",\n    \"frame_row\",\n    \"free\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"fusion\",\n    \"get\",\n    \"global\",\n    \"grant\",\n    \"group\",\n    \"grouping\",\n    \"groups\",\n    \"having\",\n    \"hold\",\n    \"hour\",\n    \"identity\",\n    \"in\",\n    \"indicator\",\n    \"initial\",\n    \"inner\",\n    \"inout\",\n    \"insensitive\",\n    \"insert\",\n    \"int\",\n    \"integer\",\n    \"intersect\",\n    \"intersection\",\n    \"interval\",\n    \"into\",\n    \"is\",\n    \"join\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"language\",\n    \"large\",\n    \"last_value\",\n    \"lateral\",\n    \"lead\",\n    \"leading\",\n    \"left\",\n    \"like\",\n    \"like_regex\",\n    \"listagg\",\n    \"ln\",\n    \"local\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"match\",\n    \"match_number\",\n    \"match_recognize\",\n    \"matches\",\n    \"max\",\n    \"member\",\n    \"merge\",\n    \"method\",\n    \"min\",\n    \"minute\",\n    \"mod\",\n    \"modifies\",\n    \"module\",\n    \"month\",\n    \"multiset\",\n    \"national\",\n    \"natural\",\n    \"nchar\",\n    \"nclob\",\n    \"new\",\n    \"no\",\n    \"none\",\n    \"normalize\",\n    \"not\",\n    \"nth_value\",\n    \"ntile\",\n    \"null\",\n    \"nullif\",\n    \"numeric\",\n    \"octet_length\",\n    \"occurrences_regex\",\n    \"of\",\n    \"offset\",\n    \"old\",\n    \"omit\",\n    \"on\",\n    \"one\",\n    \"only\",\n    \"open\",\n    \"or\",\n    \"order\",\n    \"out\",\n    \"outer\",\n    \"over\",\n    \"overlaps\",\n    \"overlay\",\n    \"parameter\",\n    \"partition\",\n    \"pattern\",\n    \"per\",\n    \"percent\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"period\",\n    \"portion\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"precedes\",\n    \"precision\",\n    \"prepare\",\n    \"primary\",\n    \"procedure\",\n    \"ptf\",\n    \"range\",\n    \"rank\",\n    \"reads\",\n    \"real\",\n    \"recursive\",\n    \"ref\",\n    \"references\",\n    \"referencing\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"release\",\n    \"result\",\n    \"return\",\n    \"returns\",\n    \"revoke\",\n    \"right\",\n    \"rollback\",\n    \"rollup\",\n    \"row\",\n    \"row_number\",\n    \"rows\",\n    \"running\",\n    \"savepoint\",\n    \"scope\",\n    \"scroll\",\n    \"search\",\n    \"second\",\n    \"seek\",\n    \"select\",\n    \"sensitive\",\n    \"session_user\",\n    \"set\",\n    \"show\",\n    \"similar\",\n    \"sin\",\n    \"sinh\",\n    \"skip\",\n    \"smallint\",\n    \"some\",\n    \"specific\",\n    \"specifictype\",\n    \"sql\",\n    \"sqlexception\",\n    \"sqlstate\",\n    \"sqlwarning\",\n    \"sqrt\",\n    \"start\",\n    \"static\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"submultiset\",\n    \"subset\",\n    \"substring\",\n    \"substring_regex\",\n    \"succeeds\",\n    \"sum\",\n    \"symmetric\",\n    \"system\",\n    \"system_time\",\n    \"system_user\",\n    \"table\",\n    \"tablesample\",\n    \"tan\",\n    \"tanh\",\n    \"then\",\n    \"time\",\n    \"timestamp\",\n    \"timezone_hour\",\n    \"timezone_minute\",\n    \"to\",\n    \"trailing\",\n    \"translate\",\n    \"translate_regex\",\n    \"translation\",\n    \"treat\",\n    \"trigger\",\n    \"trim\",\n    \"trim_array\",\n    \"true\",\n    \"truncate\",\n    \"uescape\",\n    \"union\",\n    \"unique\",\n    \"unknown\",\n    \"unnest\",\n    \"update\",\n    \"upper\",\n    \"user\",\n    \"using\",\n    \"value\",\n    \"values\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"varbinary\",\n    \"varchar\",\n    \"varying\",\n    \"versioning\",\n    \"when\",\n    \"whenever\",\n    \"where\",\n    \"width_bucket\",\n    \"window\",\n    \"with\",\n    \"within\",\n    \"without\",\n    \"year\",\n  ];\n\n  // these are reserved words we have identified to be functions\n  // and should only be highlighted in a dispatch-like context\n  // ie, array_agg(...), etc.\n  const RESERVED_FUNCTIONS = [\n    \"abs\",\n    \"acos\",\n    \"array_agg\",\n    \"asin\",\n    \"atan\",\n    \"avg\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"coalesce\",\n    \"corr\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"dense_rank\",\n    \"deref\",\n    \"element\",\n    \"exp\",\n    \"extract\",\n    \"first_value\",\n    \"floor\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"listagg\",\n    \"ln\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"max\",\n    \"min\",\n    \"mod\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"rank\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"row_number\",\n    \"sin\",\n    \"sinh\",\n    \"sqrt\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"substring\",\n    \"substring_regex\",\n    \"sum\",\n    \"tan\",\n    \"tanh\",\n    \"translate\",\n    \"translate_regex\",\n    \"treat\",\n    \"trim\",\n    \"trim_array\",\n    \"unnest\",\n    \"upper\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"width_bucket\",\n  ];\n\n  // these functions can\n  const POSSIBLE_WITHOUT_PARENS = [\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"session_user\",\n    \"system_time\",\n    \"system_user\",\n    \"current_time\",\n    \"localtime\",\n    \"current_timestamp\",\n    \"localtimestamp\"\n  ];\n\n  // those exist to boost relevance making these very\n  // \"SQL like\" keyword combos worth +1 extra relevance\n  const COMBOS = [\n    \"create table\",\n    \"insert into\",\n    \"primary key\",\n    \"foreign key\",\n    \"not null\",\n    \"alter table\",\n    \"add constraint\",\n    \"grouping sets\",\n    \"on overflow\",\n    \"character set\",\n    \"respect nulls\",\n    \"ignore nulls\",\n    \"nulls first\",\n    \"nulls last\",\n    \"depth first\",\n    \"breadth first\"\n  ];\n\n  const FUNCTIONS = RESERVED_FUNCTIONS;\n\n  const KEYWORDS = [\n    ...RESERVED_WORDS,\n    ...NON_RESERVED_WORDS\n  ].filter((keyword) => {\n    return !RESERVED_FUNCTIONS.includes(keyword);\n  });\n\n  const VARIABLE = {\n    className: \"variable\",\n    begin: /@[a-z0-9]+/,\n  };\n\n  const OPERATOR = {\n    className: \"operator\",\n    begin: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\n    relevance: 0,\n  };\n\n  const FUNCTION_CALL = {\n    begin: regex.concat(/\\b/, regex.either(...FUNCTIONS), /\\s*\\(/),\n    relevance: 0,\n    keywords: { built_in: FUNCTIONS }\n  };\n\n  // keywords with less than 3 letters are reduced in relevancy\n  function reduceRelevancy(list, {\n    exceptions, when\n  } = {}) {\n    const qualifyFn = when;\n    exceptions = exceptions || [];\n    return list.map((item) => {\n      if (item.match(/\\|\\d+$/) || exceptions.includes(item)) {\n        return item;\n      } else if (qualifyFn(item)) {\n        return `${item}|0`;\n      } else {\n        return item;\n      }\n    });\n  }\n\n  return {\n    name: 'SQL',\n    case_insensitive: true,\n    // does not include {} or HTML tags `</`\n    illegal: /[{}]|<\\//,\n    keywords: {\n      $pattern: /\\b[\\w\\.]+/,\n      keyword:\n        reduceRelevancy(KEYWORDS, { when: (x) => x.length < 3 }),\n      literal: LITERALS,\n      type: TYPES,\n      built_in: POSSIBLE_WITHOUT_PARENS\n    },\n    contains: [\n      {\n        begin: regex.either(...COMBOS),\n        relevance: 0,\n        keywords: {\n          $pattern: /[\\w\\.]+/,\n          keyword: KEYWORDS.concat(COMBOS),\n          literal: LITERALS,\n          type: TYPES\n        },\n      },\n      {\n        className: \"type\",\n        begin: regex.either(...MULTI_WORD_TYPES)\n      },\n      FUNCTION_CALL,\n      VARIABLE,\n      STRING,\n      QUOTED_IDENTIFIER,\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      OPERATOR\n    ]\n  };\n}\n\nexport { sql as default };\n", "/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common, web\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  const regex = hljs.regex;\n  // XML names can have the following additional letters: https://www.w3.org/TR/xml/#NT-NameChar\n  // OTHER_NAME_CHARS = /[:\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]/;\n  // Element names start with NAME_START_CHAR followed by optional other Unicode letters, ASCII digits, hyphens, underscores, and periods\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);;\n  // const XML_IDENT_RE = /[A-Z_a-z:\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]+/;\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);\n  // however, to cater for performance and more Unicode support rely simply on the Unicode letter class\n  const TAG_NAME_RE = regex.concat(/[\\p{L}_]/u, regex.optional(/[\\p{L}0-9_.-]*:/u), /[\\p{L}0-9_.-]*/u);\n  const XML_IDENT_RE = /[\\p{L}0-9._:-]+/u;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [\n      {\n        className: 'keyword',\n        begin: /#?[a-z_][a-z1-9_-]+/,\n        illegal: /\\n/\n      }\n    ]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, { className: 'string' });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: XML_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /=\\s*/,\n        relevance: 0,\n        contains: [\n          {\n            className: 'string',\n            endsParent: true,\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [ XML_ENTITIES ]\n              },\n              { begin: /[^\\s\"'=<>`]+/ }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: [\n      'html',\n      'xhtml',\n      'rss',\n      'atom',\n      'xjb',\n      'xsd',\n      'xsl',\n      'plist',\n      'wsf',\n      'svg'\n    ],\n    case_insensitive: true,\n    unicodeRegex: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: /<![a-z]/,\n        end: />/,\n        relevance: 10,\n        contains: [\n          XML_META_KEYWORDS,\n          QUOTE_META_STRING_MODE,\n          APOS_META_STRING_MODE,\n          XML_META_PAR_KEYWORDS,\n          {\n            begin: /\\[/,\n            end: /\\]/,\n            contains: [\n              {\n                className: 'meta',\n                begin: /<![a-z]/,\n                end: />/,\n                contains: [\n                  XML_META_KEYWORDS,\n                  XML_META_PAR_KEYWORDS,\n                  QUOTE_META_STRING_MODE,\n                  APOS_META_STRING_MODE\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      hljs.COMMENT(\n        /<!--/,\n        /-->/,\n        { relevance: 10 }\n      ),\n      {\n        begin: /<!\\[CDATA\\[/,\n        end: /\\]\\]>/,\n        relevance: 10\n      },\n      XML_ENTITIES,\n      // xml processing instructions\n      {\n        className: 'meta',\n        end: /\\?>/,\n        variants: [\n          {\n            begin: /<\\?xml/,\n            relevance: 10,\n            contains: [\n              QUOTE_META_STRING_MODE\n            ]\n          },\n          {\n            begin: /<\\?[a-z][a-z0-9]+/,\n          }\n        ]\n\n      },\n      {\n        className: 'tag',\n        /*\n        The lookahead pattern (?=...) ensures that 'begin' only matches\n        '<style' as a single word, followed by a whitespace or an\n        ending bracket.\n        */\n        begin: /<style(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'style' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/style>/,\n          returnEnd: true,\n          subLanguage: [\n            'css',\n            'xml'\n          ]\n        }\n      },\n      {\n        className: 'tag',\n        // See the comment in the <style tag about the lookahead pattern\n        begin: /<script(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'script' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/script>/,\n          returnEnd: true,\n          subLanguage: [\n            'javascript',\n            'handlebars',\n            'xml'\n          ]\n        }\n      },\n      // we need this for now for jSX\n      {\n        className: 'tag',\n        begin: /<>|<\\/>/\n      },\n      // open tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /</,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE,\n            // <tag/>\n            // <tag>\n            // <tag ...\n            regex.either(/\\/>/, />/, /\\s/)\n          ))\n        ),\n        end: /\\/?>/,\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0,\n            starts: TAG_INTERNALS\n          }\n        ]\n      },\n      // close tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /<\\//,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE, />/\n          ))\n        ),\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0\n          },\n          {\n            begin: />/,\n            relevance: 0,\n            endsParent: true\n          }\n        ]\n      }\n    ]\n  };\n}\n\nexport { xml as default };\n", "/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  const LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  const URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  const KEY = {\n    className: 'attr',\n    variants: [\n      { begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)' },\n      { // double quoted keys\n        begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)' },\n      { // single quoted keys\n        begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)' }\n    ]\n  };\n\n  const TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { // jinja templates Ansible\n        begin: /\\{\\{/,\n        end: /\\}\\}/\n      },\n      { // Ruby i18n\n        begin: /%\\{/,\n        end: /\\}/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  const CONTAINER_STRING = hljs.inherit(STRING, { variants: [\n    {\n      begin: /'/,\n      end: /'/\n    },\n    {\n      begin: /\"/,\n      end: /\"/\n    },\n    { begin: /[^\\s,{}[\\]]+/ }\n  ] });\n\n  const DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  const TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  const FRACTION_RE = '(\\\\.[0-9]*)?';\n  const ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  const TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  const MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    STRING\n  ];\n\n  const VALUE_MODES = [ ...MODES ];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nexport { yaml as default };\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}/*!\\n  Theme: GitHub\\n  Description: Light theme as seen on github.com\\n  Author: github.com\\n  Maintainer: @Hirse\\n  Updated: 2021-05-15\\n\\n  Outdated base version: https://github.com/primer/github-syntax-light\\n  Current colors taken from GitHub's CSS\\n*/.hljs{color:#24292e;background:#fff}.hljs-doctag,.hljs-keyword,.hljs-meta .hljs-keyword,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language_{color:#d73a49}.hljs-title,.hljs-title.class_,.hljs-title.class_.inherited__,.hljs-title.function_{color:#6f42c1}.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id,.hljs-variable{color:#005cc5}.hljs-meta .hljs-string,.hljs-regexp,.hljs-string{color:#032f62}.hljs-built_in,.hljs-symbol{color:#e36209}.hljs-code,.hljs-comment,.hljs-formula{color:#6a737d}.hljs-name,.hljs-quote,.hljs-selector-pseudo,.hljs-selector-tag{color:#22863a}.hljs-subst{color:#24292e}.hljs-section{color:#005cc5;font-weight:700}.hljs-bullet{color:#735c0f}.hljs-emphasis{color:#24292e;font-style:italic}.hljs-strong{color:#24292e;font-weight:700}.hljs-addition{color:#22863a;background-color:#f0fff4}.hljs-deletion{color:#b31d28;background-color:#ffeef0}\", \"\",{\"version\":3,\"sources\":[\"webpack://./node_modules/highlight.js/styles/github.css\"],\"names\":[],\"mappings\":\"AAAA,cAAc,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,eAAe,CAAC;;;;;;;;;CASjF,CAAC,MAAM,aAAa,CAAC,eAAe,CAAC,mIAAmI,aAAa,CAAC,oFAAoF,aAAa,CAAC,0JAA0J,aAAa,CAAC,kDAAkD,aAAa,CAAC,4BAA4B,aAAa,CAAC,uCAAuC,aAAa,CAAC,gEAAgE,aAAa,CAAC,YAAY,aAAa,CAAC,cAAc,aAAa,CAAC,eAAe,CAAC,aAAa,aAAa,CAAC,eAAe,aAAa,CAAC,iBAAiB,CAAC,aAAa,aAAa,CAAC,eAAe,CAAC,eAAe,aAAa,CAAC,wBAAwB,CAAC,eAAe,aAAa,CAAC,wBAAwB\",\"sourcesContent\":[\"pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}/*!\\n  Theme: GitHub\\n  Description: Light theme as seen on github.com\\n  Author: github.com\\n  Maintainer: @Hirse\\n  Updated: 2021-05-15\\n\\n  Outdated base version: https://github.com/primer/github-syntax-light\\n  Current colors taken from GitHub's CSS\\n*/.hljs{color:#24292e;background:#fff}.hljs-doctag,.hljs-keyword,.hljs-meta .hljs-keyword,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language_{color:#d73a49}.hljs-title,.hljs-title.class_,.hljs-title.class_.inherited__,.hljs-title.function_{color:#6f42c1}.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id,.hljs-variable{color:#005cc5}.hljs-meta .hljs-string,.hljs-regexp,.hljs-string{color:#032f62}.hljs-built_in,.hljs-symbol{color:#e36209}.hljs-code,.hljs-comment,.hljs-formula{color:#6a737d}.hljs-name,.hljs-quote,.hljs-selector-pseudo,.hljs-selector-tag{color:#22863a}.hljs-subst{color:#24292e}.hljs-section{color:#005cc5;font-weight:700}.hljs-bullet{color:#735c0f}.hljs-emphasis{color:#24292e;font-style:italic}.hljs-strong{color:#24292e;font-weight:700}.hljs-addition{color:#22863a;background-color:#f0fff4}.hljs-deletion{color:#b31d28;background-color:#ffeef0}\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "\"use strict\";\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = []; // return the list of modules as css string\n\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n\n      content += cssWithMappingToString(item);\n\n      if (needLayer) {\n        content += \"}\";\n      }\n\n      if (item[2]) {\n        content += \"}\";\n      }\n\n      if (item[4]) {\n        content += \"}\";\n      }\n\n      return content;\n    }).join(\"\");\n  }; // import a list of modules into the list\n\n\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n\n    var alreadyImportedModules = {};\n\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n\n      list.push(item);\n    }\n  };\n\n  return list;\n};", "\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n\n  if (!cssMapping) {\n    return content;\n  }\n\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    var sourceURLs = cssMapping.sources.map(function (source) {\n      return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || \"\").concat(source, \" */\");\n    });\n    return [content].concat(sourceURLs).concat([sourceMapping]).join(\"\\n\");\n  }\n\n  return [content].join(\"\\n\");\n};", "\"use strict\";\n\nvar stylesInDOM = [];\n\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n\n  return result;\n}\n\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n\n    identifiers.push(identifier);\n  }\n\n  return identifiers;\n}\n\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n\n  return updater;\n}\n\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n\n    var newLastIdentifiers = modulesToDom(newList, options);\n\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n\n      var _index = getIndexByIdentifier(_identifier);\n\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n\n    lastIdentifiers = newLastIdentifiers;\n  };\n};", "\"use strict\";\n\nvar memo = {};\n/* istanbul ignore next  */\n\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target); // Special case to return head of iframe instead of iframe itself\n\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n\n    memo[target] = styleTarget;\n  }\n\n  return memo[target];\n}\n/* istanbul ignore next  */\n\n\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n\n  target.appendChild(style);\n}\n\nmodule.exports = insertBySelector;", "\"use strict\";\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\n\nmodule.exports = insertStyleElement;", "\"use strict\";\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce = typeof __webpack_nonce__ !== \"undefined\" ? __webpack_nonce__ : null;\n\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\n\nmodule.exports = setAttributesWithoutAttributes;", "\"use strict\";\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n\n  var needLayer = typeof obj.layer !== \"undefined\";\n\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n\n  css += obj.css;\n\n  if (needLayer) {\n    css += \"}\";\n  }\n\n  if (obj.media) {\n    css += \"}\";\n  }\n\n  if (obj.supports) {\n    css += \"}\";\n  }\n\n  var sourceMap = obj.sourceMap;\n\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  } // For old IE\n\n  /* istanbul ignore if  */\n\n\n  options.styleTagTransform(css, styleElement, options.options);\n}\n\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n\n  styleElement.parentNode.removeChild(styleElement);\n}\n/* istanbul ignore next  */\n\n\nfunction domAPI(options) {\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\n\nmodule.exports = domAPI;", "\"use strict\";\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\n\nmodule.exports = styleTagTransform;", "var deepFreezeEs6 = {exports: {}};\n\nfunction deepFreeze(obj) {\n    if (obj instanceof Map) {\n        obj.clear = obj.delete = obj.set = function () {\n            throw new Error('map is read-only');\n        };\n    } else if (obj instanceof Set) {\n        obj.add = obj.clear = obj.delete = function () {\n            throw new Error('set is read-only');\n        };\n    }\n\n    // Freeze self\n    Object.freeze(obj);\n\n    Object.getOwnPropertyNames(obj).forEach(function (name) {\n        var prop = obj[name];\n\n        // Freeze prop if it is an object\n        if (typeof prop == 'object' && !Object.isFrozen(prop)) {\n            deepFreeze(prop);\n        }\n    });\n\n    return obj;\n}\n\ndeepFreezeEs6.exports = deepFreeze;\ndeepFreezeEs6.exports.default = deepFreeze;\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope || (node.sublanguage && node.language);\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    let className = \"\";\n    if (node.sublanguage) {\n      className = `language-${node.language}`;\n    } else {\n      className = scopeToCSSClass(node.scope, { prefix: this.classPrefix });\n    }\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addKeyword(text, scope)\n  - addText(text)\n  - addSublanguage(emitter, subLanguageName)\n  - finalize()\n  - openNode(scope)\n  - closeNode()\n  - closeAllNodes()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   * @param {string} scope\n   */\n  addKeyword(text, scope) {\n    if (text === \"\") { return; }\n\n    this.openNode(scope);\n    this.addText(text);\n    this.closeNode();\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    node.sublanguage = true;\n    node.language = name;\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  // this outer rule makes sure we actually have a WHOLE regex and not simply\n  // an expression such as:\n  //\n  //     3 / something\n  //\n  // (which will then blow up when regex's `illegal` sees the newline)\n  begin: /(?=\\/[^/\\n]*\\/)/,\n  contains: [{\n    scope: 'regexp',\n    begin: /\\//,\n    end: /\\/[gimuy]*/,\n    illegal: /\\n/,\n    contains: [\n      BACKSLASH_ESCAPE,\n      {\n        begin: /\\[/,\n        end: /\\]/,\n        relevance: 0,\n        contains: [BACKSLASH_ESCAPE]\n      }\n    ]\n  }]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n    IDENT_RE: IDENT_RE,\n    UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n    NUMBER_RE: NUMBER_RE,\n    C_NUMBER_RE: C_NUMBER_RE,\n    BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n    RE_STARTERS_RE: RE_STARTERS_RE,\n    SHEBANG: SHEBANG,\n    BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n    APOS_STRING_MODE: APOS_STRING_MODE,\n    QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n    PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n    COMMENT: COMMENT,\n    C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n    C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n    HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n    NUMBER_MODE: NUMBER_MODE,\n    C_NUMBER_MODE: C_NUMBER_MODE,\n    BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n    REGEXP_MODE: REGEXP_MODE,\n    TITLE_MODE: TITLE_MODE,\n    UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE,\n    METHOD_GUARD: METHOD_GUARD,\n    END_SAME_AS_BEGIN: END_SAME_AS_BEGIN\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type KeywordDict */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.6.0\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitter.addKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitter.addKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitter.addKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitter.addKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      top.matcher.considerAll();\n\n      for (;;) {\n        iterations++;\n        if (resumeScanAtSamePosition) {\n          // only regexes not matched previously will now be\n          // considered for a potential match\n          resumeScanAtSamePosition = false;\n        } else {\n          top.matcher.considerAll();\n        }\n        top.matcher.lastIndex = index;\n\n        const match = top.matcher.exec(codeToHighlight);\n        // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n        if (!match) break;\n\n        const beforeMatch = codeToHighlight.substring(index, match.index);\n        const processedCount = processLexeme(beforeMatch, match);\n        index = match.index + processedCount;\n      }\n      processLexeme(codeToHighlight.substring(index));\n      emitter.closeAllNodes();\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance: relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index: index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language: language });\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreezeEs6.exports(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// export an \"instance\" of the highlighter\nvar highlight = HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n"], "names": ["options", "styleTagTransform", "setAttributes", "insert", "domAPI", "insertStyleElement", "TAGS", "MEDIA_FEATURES", "PSEUDO_CLASSES", "PSEUDO_ELEMENTS", "ATTRIBUTES", "reverse", "escape", "value", "RegExp", "replace", "source", "re", "<PERSON><PERSON><PERSON>", "concat", "args", "joined", "map", "x", "join", "stripOptionsFromArgs", "opts", "length", "constructor", "Object", "splice", "either", "capture", "IDENT_RE", "KEYWORDS", "LITERALS", "TYPES", "ERROR_TYPES", "BUILT_IN_GLOBALS", "BUILT_IN_VARIABLES", "BUILT_INS", "highlightjs", "hljs", "regex", "VAR", "BRACED_VAR", "begin", "end", "contains", "assign", "className", "variants", "SUBST", "BACKSLASH_ESCAPE", "HERE_DOC", "starts", "END_SAME_AS_BEGIN", "QUOTE_STRING", "push", "ARITHMETIC", "NUMBER_MODE", "KNOWN_SHEBANG", "SHEBANG", "binary", "relevance", "FUNCTION", "returnBegin", "inherit", "TITLE_MODE", "name", "aliases", "keywords", "$pattern", "keyword", "literal", "built_in", "HASH_COMMENT_MODE", "match", "NUMBERS", "VERBATIM_STRING", "VERBATIM_STRING_NO_LF", "illegal", "SUBST_NO_LF", "INTERPOLATED_STRING", "INTERPOLATED_VERBATIM_STRING", "INTERPOLATED_VERBATIM_STRING_NO_LF", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_BLOCK_COMMENT_MODE", "STRING", "GENERIC_MODIFIER", "beginKeywords", "TYPE_IDENT_RE", "AT_IDENTIFIER", "COMMENT", "C_LINE_COMMENT_MODE", "excludeBegin", "excludeEnd", "modes", "IMPORTANT", "scope", "BLOCK_COMMENT", "HEXCOLOR", "FUNCTION_DISPATCH", "ATTRIBUTE_SELECTOR_MODE", "CSS_NUMBER_MODE", "NUMBER_RE", "CSS_VARIABLE", "MODES", "STRINGS", "case_insensitive", "keyframePosition", "classNameAliases", "endsWithParent", "attribute", "BANG_KEYWORD_MODE", "KNOWN_TYPES", "ALL_KEYWORDS", "QUOTED_IDENTIFIER", "BEGIN_GENERIC_TYPE_SYMBOL_RE", "GENERIC_TYPE_SYMBOL", "UNDERSCORE_IDENT_RE", "makeOperatorMode", "allOperatorChars", "includeEqual", "OPERATOR_CHARS", "Array", "from", "OPERATOR_CHAR_RE", "OPERATOR_CHAR_OR_DOT_RE", "OPERATOR_FIRST_CHAR_OF_MULTIPLE_RE", "SYMBOLIC_OPERATOR_RE", "OPERATOR", "OPERATOR_WITHOUT_EQUAL", "makeTypeAnnotationMode", "prefix", "prefixScope", "beginScope", "type", "TYPE_ANNOTATION", "DISCRIMINATED_UNION_TYPE_ANNOTATION", "TYPE_DECLARATION", "COMPUTATION_EXPRESSION", "PREPROCESSOR", "NUMBER", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "QUOTED_STRING", "TRIPLE_QUOTED_STRING", "INTERPOLATED_TRIPLE_QUOTED_STRING", "CHAR_LITERAL", "VERSION", "HEADER", "HEADERS_AND_BODY", "subLanguage", "IDENT_RE$1", "XML_TAG", "isTrulyOpeningTag", "response", "m", "afterMatchIndex", "index", "nextChar", "input", "after", "tag", "slice", "indexOf", "hasClosingTag", "ignoreMatch", "substring", "KEYWORDS$1", "decimalDigits", "frac", "decimalInteger", "HTML_TEMPLATE", "returnEnd", "CSS_TEMPLATE", "TEMPLATE_STRING", "endsParent", "SUBST_INTERNALS", "list", "SUBST_AND_COMMENTS", "PARAMS_CONTAINS", "PARAMS", "CLASS_OR_EXTENDS", "CLASS_REFERENCE", "_", "FUNCTION_DEFINITION", "label", "FUNCTION_CALL", "PROPERTY_ACCESS", "GETTER_OR_SETTER", "FUNC_LEAD_IN_RE", "FUNCTION_VARIABLE", "exports", "RE_STARTERS_RE", "REGEXP_MODE", "skip", "LITERALS_MODE", "VARIABLE_NAME_RE", "DEFAULT", "INTERPOLATION", "INTERPOLATED_VARIABLE", "COMMAND", "MACROCALL", "INLINE_HTML", "LINK", "BOLD", "ITALIC", "BOLD_WITHOUT_ITALIC", "ITALIC_WITHOUT_BOLD", "CONTAINABLE", "for<PERSON>ach", "TRANSPOSE", "UNDERSCORE_TITLE_MODE", "C_NUMBER_RE", "disableAutodetect", "RESERVED_WORDS", "PROMPT", "LITERAL_BRACKET", "digitpart", "pointfloat", "COMMENT_TYPE", "unicodeRegex", "NUMBER_TYPES_RE", "OPERATORS_RE", "PUNCTUATION_RE", "RUBY_METHOD_RE", "CLASS_NAME_RE", "CLASS_NAME_WITH_NAMESPACE_RE", "RUBY_KEYWORDS", "YARDOCTAG", "IRB_OBJECT", "COMMENT_MODES", "MATCH_NOTHING_RE", "digits", "RUBY_DEFAULT_CONTAINS", "IRB_DEFAULT", "unshift", "COMMENT_MODE", "RESERVED_FUNCTIONS", "COMBOS", "FUNCTIONS", "filter", "includes", "exceptions", "when", "qualifyFn", "item", "reduceRelevancy", "TAG_NAME_RE", "optional", "XML_ENTITIES", "XML_META_KEYWORDS", "XML_META_PAR_KEYWORDS", "APOS_META_STRING_MODE", "QUOTE_META_STRING_MODE", "TAG_INTERNALS", "URI_CHARACTERS", "CONTAINER_STRING", "VALUE_CONTAINER", "OBJECT", "ARRAY", "VALUE_MODES", "pop", "___CSS_LOADER_EXPORT___", "module", "id", "cssWithMappingToString", "toString", "this", "content", "<PERSON><PERSON><PERSON>er", "i", "modules", "media", "dedupe", "supports", "layer", "undefined", "alreadyImportedModules", "k", "_k", "cssMapping", "btoa", "base64", "unescape", "encodeURIComponent", "JSON", "stringify", "data", "sourceMapping", "sourceURLs", "sources", "sourceRoot", "stylesInDOM", "getIndexByIdentifier", "identifier", "result", "modulesToDom", "idCountMap", "identifiers", "base", "count", "indexByIdentifier", "obj", "css", "sourceMap", "references", "updater", "addElementStyle", "byIndex", "api", "update", "newObj", "remove", "lastIdentifiers", "newList", "newLastIdentifiers", "_i", "_index", "memo", "style", "target", "styleTarget", "document", "querySelector", "window", "HTMLIFrameElement", "contentDocument", "head", "e", "get<PERSON><PERSON><PERSON>", "Error", "append<PERSON><PERSON><PERSON>", "element", "createElement", "attributes", "styleElement", "nonce", "setAttribute", "apply", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "removeStyleElement", "styleSheet", "cssText", "<PERSON><PERSON><PERSON><PERSON>", "createTextNode", "deepFreezeEs6", "deepFreeze", "Map", "clear", "delete", "set", "Set", "add", "freeze", "getOwnPropertyNames", "prop", "isFrozen", "default", "Response", "mode", "isMatchIgnored", "escapeHTML", "inherit$1", "original", "create", "key", "objects", "emitsWrappingTags", "node", "sublanguage", "language", "HTMLR<PERSON><PERSON>", "parseTree", "buffer", "classPrefix", "walk", "text", "pieces", "split", "shift", "repeat", "scopeToCSSClass", "span", "newNode", "children", "TokenTreeEmitter", "openNode", "addText", "closeNode", "emitter", "root", "rootNode", "stack", "top", "builder", "_walk", "child", "every", "el", "TokenTree", "_collapse", "anyNumberOfTimes", "countMatchGroups", "exec", "BACKREF_RE", "_rewriteBackreferences", "regexps", "joinWith", "numCaptures", "offset", "out", "String", "Number", "BINARY_NUMBER_RE", "modeOptions", "ENGLISH_WORD", "__proto__", "beginShebang", "resp", "PHRASAL_WORDS_MODE", "METHOD_GUARD", "_beginMatch", "skipIfHasPrecedingDot", "scopeClassName", "_parent", "parent", "__beforeBegin", "compileIllegal", "isArray", "compileMatch", "compileRelevance", "beforeMatchExt", "beforeMatch", "originalMode", "keys", "COMMON_KEYWORDS", "DEFAULT_KEYWORD_SCOPE", "compileKeywords", "rawKeywords", "caseInsensitive", "scopeName", "compiledKeywords", "compileList", "keywordList", "toLowerCase", "pair", "scoreForKeyword", "providedScore", "commonKeyword", "seenDeprecations", "error", "message", "console", "warn", "log", "deprecated", "version", "MultiClassError", "remapScopeNames", "regexes", "scopeNames", "emit", "positions", "_emit", "_multi", "MultiClass", "scopeSugar", "_wrap", "endScope", "beginMultiClass", "endMultiClass", "compileLanguage", "langRe", "global", "MultiRegex", "matchIndexes", "matchAt", "position", "terminators", "matcherRe", "lastIndex", "s", "findIndex", "matchData", "ResumableMultiRegex", "rules", "multiRegexes", "regexIndex", "matcher", "addRule", "compile", "getMatcher", "resumingScanAtSamePosition", "m2", "considerAll", "compilerExtensions", "compileMode", "cmode", "isCompiled", "ext", "keywordPattern", "keywordPatternRe", "beginRe", "endRe", "terminatorEnd", "illegalRe", "c", "cachedVariants", "variant", "dependencyOnParent", "expandOrCloneMode", "mm", "term", "rule", "buildModeRegex", "HTMLInjectionError", "reason", "html", "NO_MATCH", "Symbol", "highlight", "languages", "plugins", "SAFE_MODE", "LANGUAGE_NOT_FOUND", "PLAINTEXT_LANGUAGE", "ignoreUnescapedHTML", "throwUnescapedHTML", "noHighlightRe", "languageDetectRe", "cssSelector", "__emitter", "shouldNotHighlight", "languageName", "test", "codeOrLanguageName", "optionsOrCode", "ignoreIllegals", "code", "context", "fire", "_highlight", "codeToHighlight", "continuation", "keywordHits", "processKeywords", "matchText", "modeBuffer", "buf", "word", "kind", "keywordRelevance", "startsWith", "cssClass", "addKeyword", "processBuffer", "continuations", "_top", "highlightAuto", "addSublanguage", "_emitter", "processSubLanguage", "emitMultiClass", "max", "klass", "startNewMode", "endOfMode", "matchPlusRemainder", "matched", "lexeme", "doIgnore", "resumeScanAtSamePosition", "doEndMatch", "endMode", "origin", "lastMatch", "processLexeme", "textBeforeMatch", "err", "badRule", "newMode", "cb", "doBeginMatch", "processed", "iterations", "getLanguage", "md", "current", "processContinuations", "processedCount", "closeAllNodes", "finalize", "toHTML", "_illegalBy", "resultSoFar", "errorRaised", "languageSubset", "plaintext", "justTextHighlightResult", "results", "autoDetection", "sort", "a", "b", "supersetOf", "best", "secondBest", "highlightElement", "block", "classes", "find", "_class", "blockLanguage", "innerHTML", "textContent", "currentLang", "resultLang", "classList", "updateClassName", "wantsHighlight", "highlightAll", "readyState", "querySelectorAll", "registerAliases", "aliasList", "alias", "lang", "event", "plugin", "addEventListener", "highlightBlock", "configure", "userOptions", "initHighlighting", "initHighlightingOnLoad", "registerLanguage", "languageDefinition", "error$1", "rawDefinition", "bind", "unregisterLanguage", "listLanguages", "addPlugin", "upgradePluginAPI", "debugMode", "safeMode", "versionString", "HLJS", "HighlightJS"], "sourceRoot": ""}