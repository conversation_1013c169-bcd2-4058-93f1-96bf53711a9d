{"version": 3, "file": "bundle.js", "mappings": "gBACIA,EADAC,ECAAC,EACAC,E,wBCDwR,IAAUC,EAAjBC,OAApNC,EAAOC,SAA8NH,EAApN,EAAQ,MAAsN,SAASA,GAAG,IAAII,EAAE,CAAC,EAAE,SAASC,EAAEC,GAAG,GAAGF,EAAEE,GAAG,OAAOF,EAAEE,GAAGH,QAAQ,IAAII,EAAEH,EAAEE,GAAG,CAACE,EAAEF,EAAEG,GAAE,EAAGN,QAAQ,CAAC,GAAG,OAAOH,EAAEM,GAAGI,KAAKH,EAAEJ,QAAQI,EAAEA,EAAEJ,QAAQE,GAAGE,EAAEE,GAAE,EAAGF,EAAEJ,OAAO,CAAC,OAAOE,EAAEM,EAAEX,EAAEK,EAAEO,EAAER,EAAEC,EAAEQ,EAAE,SAASb,EAAEI,EAAEE,GAAGD,EAAEE,EAAEP,EAAEI,IAAIU,OAAOC,eAAef,EAAEI,EAAE,CAACY,YAAW,EAAGC,IAAIX,GAAG,EAAED,EAAEC,EAAE,SAASN,GAAG,oBAAoBkB,QAAQA,OAAOC,aAAaL,OAAOC,eAAef,EAAEkB,OAAOC,YAAY,CAACC,MAAM,WAAWN,OAAOC,eAAef,EAAE,aAAa,CAACoB,OAAM,GAAI,EAAEf,EAAEA,EAAE,SAASL,EAAEI,GAAG,GAAG,EAAEA,IAAIJ,EAAEK,EAAEL,IAAI,EAAEI,EAAE,OAAOJ,EAAE,GAAG,EAAEI,GAAG,iBAAiBJ,GAAGA,GAAGA,EAAEqB,WAAW,OAAOrB,EAAE,IAAIM,EAAEQ,OAAOQ,OAAO,MAAM,GAAGjB,EAAEC,EAAEA,GAAGQ,OAAOC,eAAeT,EAAE,UAAU,CAACU,YAAW,EAAGI,MAAMpB,IAAI,EAAEI,GAAG,iBAAiBJ,EAAE,IAAI,IAAIO,KAAKP,EAAEK,EAAEQ,EAAEP,EAAEC,EAAE,SAASH,GAAG,OAAOJ,EAAEI,EAAE,EAAEmB,KAAK,KAAKhB,IAAI,OAAOD,CAAC,EAAED,EAAED,EAAE,SAASJ,GAAG,IAAII,EAAEJ,GAAGA,EAAEqB,WAAW,WAAW,OAAOrB,EAAEwB,OAAO,EAAE,WAAW,OAAOxB,CAAC,EAAE,OAAOK,EAAEQ,EAAET,EAAE,IAAIA,GAAGA,CAAC,EAAEC,EAAEE,EAAE,SAASP,EAAEI,GAAG,OAAOU,OAAOW,UAAUC,eAAehB,KAAKV,EAAEI,EAAE,EAAEC,EAAEsB,EAAE,GAAGtB,EAAEA,EAAEuB,EAAE,EAAE,CAAn5B,CAAq5B,CAAC,SAASxB,EAAEC,GAAGD,EAAED,QAAQH,CAAC,EAAE,SAASA,EAAEI,EAAEC,GAAG,aAAaA,EAAEC,EAAEF,GAAGC,EAAEQ,EAAET,EAAE,kBAAiB,WAAY,OAAOyB,CAAE,IAAGxB,EAAEQ,EAAET,EAAE,yBAAwB,WAAY,OAAO0B,CAAE,IAAGzB,EAAEQ,EAAET,EAAE,WAAU,WAAY,OAAOQ,CAAE,IAAGP,EAAEQ,EAAET,EAAE,WAAU,WAAY,OAAOS,CAAE,IAAG,IAAIP,EAAED,EAAE,GAAG,SAASE,EAAEP,EAAEI,EAAEC,EAAEC,EAAEC,EAAEC,EAAEqB,GAAG,IAAI,IAAIC,EAAE9B,EAAEQ,GAAGqB,GAAGjB,EAAEkB,EAAEV,KAA+B,CAAzB,MAAMpB,GAAG,YAAYK,EAAEL,EAAE,CAAC8B,EAAEC,KAAK3B,EAAEQ,GAAGoB,QAAQC,QAAQrB,GAAGsB,KAAK5B,EAAEC,EAAE,CAAC,SAASC,EAAER,GAAG,OAAO,WAAW,IAAII,EAAE+B,KAAK9B,EAAE+B,UAAU,OAAO,IAAIJ,SAAQ,SAAU1B,EAAEE,GAAG,IAAIqB,EAAE7B,EAAEqC,MAAMjC,EAAEC,GAAG,SAASyB,EAAE9B,GAAGO,EAAEsB,EAAEvB,EAAEE,EAAEsB,EAAElB,EAAE,OAAOZ,EAAE,CAAC,SAASY,EAAEZ,GAAGO,EAAEsB,EAAEvB,EAAEE,EAAEsB,EAAElB,EAAE,QAAQZ,EAAE,CAAC8B,OAAE,EAAQ,GAAE,CAAC,CAAC,IAAID,EAAE,SAAS7B,EAAEI,GAAG,IAAIC,EAAEE,EAAE,CAAC+B,QAAQ,IAAIN,SAAQ,SAAUhC,GAAGK,EAAEL,CAAE,IAAGiB,IAAIH,OAAOR,EAAEiC,KAATzB,EAAe,WAAY,OAAOkB,QAAQC,QAAQ7B,KAAK8B,MAAK,SAAUlC,GAAG,OAAOwC,WAAWhC,EAAEiC,mBAAmBC,MAAK,SAAU1C,IAAI,OAAOyC,mBAAmBE,MAAK,SAAU3C,GAAG,OAAO,OAAOA,EAAE4C,KAAK5C,EAAE6C,MAAM,KAAK,EAAE,OAAO7C,EAAE6C,KAAK,EAAExC,GAAE,GAAI,KAAK,EAAEE,EAAE+B,SAAQ,EAAG,KAAK,EAAE,IAAI,MAAM,OAAOtC,EAAE8C,OAAQ,GAAE9C,EAAG,KAAI,GAAGA,CAAE,GAAG,KAAI,OAAOc,OAAOC,eAAef,EAAE,oCAAoC,CAACiB,IAAI,WAAW,OAAOV,EAAE+B,OAAO,IAAI/B,EAAEU,GAAG,EAAEa,EAAE,SAAS9B,EAAEI,GAAGU,OAAOC,eAAef,EAAE,oCAAoC,CAACiB,IAAI,WAAW,OAAOL,EAAER,EAAE,GAAG,EAAEQ,EAAE,SAASZ,GAAG,OAAOA,GAAGA,EAAE+C,iCAAiC,EAAE,SAASC,EAAEhD,EAAEI,GAAG,IAAI,IAAIC,EAAE,EAAEA,EAAED,EAAE6C,OAAO5C,IAAI,CAAC,IAAIC,EAAEF,EAAEC,GAAGC,EAAEU,WAAWV,EAAEU,aAAY,EAAGV,EAAE4C,cAAa,EAAG,UAAU5C,IAAIA,EAAE6C,UAAS,GAAIrC,OAAOC,eAAef,EAAEM,EAAE8C,IAAI9C,EAAE,CAAC,CAAC,IAAIsB,EAAE,6BAA6Bf,EAAE,WAAW,SAASb,KAAK,SAASA,EAAEI,GAAG,KAAKJ,aAAaI,GAAG,MAAM,IAAIiD,UAAU,oCAAoC,CAA3F,CAA6FlB,KAAKnC,EAAE,CAAC,IAAII,EAAIE,EAAE,OAAOF,EAAEJ,EAAEM,EAAE,CAAC,CAAC8C,IAAI,sBAAsBhC,MAAM,WAAWnB,OAAOqD,cAAc,IAAIC,YAAY3B,GAAG,GAAG,CAACwB,IAAI,WAAWhC,MAAM,SAASpB,GAAG,OAAOC,OAAOuD,iBAAiB5B,EAAE5B,GAAG,WAAW,OAAOC,OAAOwD,oBAAoB7B,EAAE5B,EAAE,CAAC,IAAO,MAAOgD,EAAE5C,EAAEqB,UAAX,MAAwBnB,GAAG0C,EAAE5C,EAAEE,GAAGQ,OAAOC,eAAeX,EAAE,YAAY,CAAC+C,UAAS,IAAKnD,CAAC,CAAlc,EAAqc,I,oCCAriG,IAAI0D,E,iFACJ,SAAWA,GACPA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAiB,KAAI,GAAK,MACxC,CAHD,CAGGA,IAAeA,EAAa,CAAC,IAChC,ICLIC,EDKJ,KCJA,SAAWA,GACPA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAe,KAAI,GAAK,OACjCA,EAASA,EAAkB,QAAI,GAAK,UACpCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAgB,MAAI,GAAK,QAClCA,EAASA,EAAe,KAAI,GAAK,MACpC,CAPD,CAOGA,IAAaA,EAAW,CAAC,IAC5B,QCPMC,EAAY,GAClBA,EAAUD,EAAAA,OAAkB,QAC5BC,EAAUD,EAAAA,MAAiB,OAC3BC,EAAUD,EAAAA,SAAoB,UAC9BC,EAAUD,EAAAA,OAAkB,QAC5BC,EAAUD,EAAAA,OAAkB,QAC5BC,EAAUD,EAAAA,MAAiB,OAC3BC,EAAUF,EAAAA,OAAoB,QAC9BE,EAAUF,EAAAA,MAAmB,QAC7B,IAAIG,EAAaF,EAAAA,KACbG,EAAeJ,EAAAA,KAGnB,SAASK,EAAMC,EAAOC,GAClB,GAAID,EAAQC,EACR,OAAO,WAAQ,EAEnB,IAAIC,EAEJ,OAAQF,GACJ,KAAKL,EAAAA,MACL,KAAKA,EAAAA,KACDO,EAAKjE,OAAOkE,QAAQC,IAEpB,MACJ,KAAKV,EAAAA,MACL,KAAKC,EAAAA,QACDO,EAAKjE,OAAOkE,QAAQE,KAEpB,MACJ,KAAKV,EAAAA,MACL,KAAKA,EAAAA,MACDO,EAAKjE,OAAOkE,QAAQG,MAEpB,MACJ,QACI,MAAM,IAAIC,MAAM,eAAD,OAAgBP,IAEvC,IAAMQ,EAAS,GAAH,OAA4C,GAAE,YAAIZ,EAAUI,GAAOS,cAAa,KAKxF,OAAOP,EAAG3C,KAAKtB,OAAOkE,QAASK,EAEvC,CACA,IAAME,EAAS,CACXC,cAAcX,GACVF,EAAeE,CACnB,EACAY,YAAYZ,GACRH,EAAaG,CACjB,GAEJlD,OAAO+D,iBAAiBH,EAAQ,CAC5BI,MAAO,CACH7D,IAAK,WACD,OAAO8C,EAAMJ,EAAAA,MAAgBE,EACjC,EACAX,cAAc,EACdlC,YAAY,GAEhB+D,KAAM,CACF9D,IAAK,WACD,OAAO8C,EAAMJ,EAAAA,KAAeE,EAChC,EACAX,cAAc,EACdlC,YAAY,GAEhBgE,QAAS,CACL/D,IAAK,WACD,OAAO8C,EAAMJ,EAAAA,QAAkBE,EACnC,EACAX,cAAc,EACdlC,YAAY,GAEhBsD,MAAO,CACHrD,IAAK,WACD,OAAO8C,EAAMJ,EAAAA,MAAgBE,EACjC,EACAX,cAAc,EACdlC,YAAY,GAEhBiE,MAAO,CACHhE,IAAK,WACD,OAAO8C,EAAMJ,EAAAA,MAAgBE,EACjC,EACAX,cAAc,EACdlC,YAAY,GAEhBkE,MAAO,CACHjE,IAAK,WACD,OAAO8C,EAAML,EAAAA,MAAkBI,EACnC,EACAZ,cAAc,EACdlC,YAAY,KAGpBF,OAAOqE,OAAOT,GACd,O,wPCpGA,I,MAAMU,EAAS,MACTC,EAAY,QACGC,EAAa,yB,4FAAA,S,QAsD7B,O,EAtD6B,E,EAAA,qBAmB9B,SAAcC,GAA6B,IAAzBC,EAAS,UAAH,6CAAG,GAAIC,EAAO,UAAH,6CAAG,IAClC,GAAKH,EAAcI,UAAnB,CAGA,IAAMC,EAAU,IAAIC,KAAKA,KAAKC,MAAQT,GAAQU,cAC9CC,SAASC,OAAS,GAAH,OAAMT,EAAE,qBAAaI,EAAO,mBAAWH,EAAM,iBAASC,EAFrE,CAGJ,GAAC,iBACD,SAAWF,GACP,GAAKA,EAAGtC,QAGHqC,EAAcI,UAWnB,OARAH,EAAKA,EAAGU,eACQF,SAASC,OAAOE,MAAM,KAAKC,KAAI,SAAAH,GAC3C,IAAMI,EAAYJ,EAAOE,MAAM,KAC/B,MAAO,CACHX,GAAIa,EAAU,GAAGC,OACjBjF,MAAOgF,EAAU,GAEzB,IACgBE,MAAK,SAAAN,GAAM,OAAIT,IAAOS,EAAOT,GAAGgB,mBAAmB,KAC/D,CAAC,GAAGnF,KACZ,GAAC,iBACD,SAAWmE,EAAInE,GAAgC,IAAzBoE,EAAS,UAAH,6CAAG,GAAIC,EAAO,UAAH,6CAAG,IACtC,GAAKH,EAAcI,UAAnB,CAGA,IAAMC,EAAU,IAAIC,KAAKA,KAAKC,MAAQR,GAAWS,cAC3CU,EAAQ,GAAH,OAAMjB,EAAE,YAAInE,EAAK,oBAAYuE,EAAO,mBAAWH,EAAM,iBAASC,GACrEH,EAAcrE,IAAIsE,IAClBD,EAAcmB,OAAOlB,EAAIC,EAAQC,GAErCM,SAASC,OAASQ,CANlB,CAOJ,IAtD8B,M,cAAA,M,6DAsD7B,EAtD6B,G,EAAblB,E,EAAa,U,EAKboB,EAAAA,KAAO,WACpB,IAEIX,SAASC,OAAS,eAClB,IAAMW,GAAkD,IAA5CZ,SAASC,OAAOY,QAAQ,eAIpC,OAFAb,SAASC,OACL,sDACGW,CAIX,CAFA,MAAO3G,GACH,OAAO,CACX,CACJ,I,6YCnBJ,IAAM6G,EAAa,aACbC,EAAW,WACIC,EAAW,yB,4FAAA,S,QA6B3B,O,EA7B2B,E,EAAA,yBAK5B,WACI,MAAwB,oBAARC,KACZA,IAAIvF,WACJuF,IAAIvF,UAAUwF,aACd,IAAID,IAAI/G,OAAOiH,SAASC,MAAMC,cAAiB,CAAEnG,IAAK,kBAAM,IAAI,EACxE,GAAC,sBACD,WACI,IAAMiE,EAAQ/C,KAAKiF,aAAanG,IAAI4F,IAAevB,EAAcrE,IAAI4F,GACrE,OAAO3B,GACDxB,EAAAA,GAAWwB,IACXxB,EAAAA,GAAAA,IACV,GAAC,oBACD,WACI,IAAMU,EAAMjC,KAAKiF,aAAanG,IAAI6F,IAAaxB,EAAcrE,IAAI6F,GACjE,OAAO1C,GAAMT,EAAAA,GAASS,IAAyBT,EAAAA,GAAAA,KACnD,GAAC,uBACD,WACI,MAAO,mBACX,GAAC,sBACD,WACI,OAAOoD,EAAYM,WACvB,GAAC,gCACD,WACI,OAAON,EAAYO,qBACvB,IA7B4B,M,cAAA,M,6DA6B3B,EA7B2B,GA6B3B,EA7BgBP,EAAW,wBACGQ,QAAkB,QAAX,EAACtH,OAAOuH,WAAG,OAAU,QAAV,EAAV,EAAYC,gBAAQ,WAAV,EAAV,SAAuB,iBAAkB,qBAAmB,EADlFV,EAAW,cAEPA,EAAYO,sBAC3B,0BACA,oB,8OCRWI,EAAU,yB,4FAAA,S,QAa1B,O,EAb0B,E,EAAA,iBAC3B,WACI,OAAO,kCAEX,GAAC,gBACD,WACI,OAAO1F,QAAQC,QAAQhC,OAAO0H,MAC1B,8BACwFzF,MAAK,SAAA0F,GAAM,OAAIA,EAAOpG,OAAO,IAC7H,GAAC,mBACD,WACI,OAAO,qDAEX,IAb2B,M,cAAA,M,6DAa1B,EAb0B,E,++BCM/B,IASqBqG,EAAS,a,qRAAA,U,QAAA,G,EAAA,E,+YAAA,sDAOzB,O,EAPyB,G,EAAA,qBAC1B,WACI,OACI,kBAAC,EAAAC,SAAQ,CAACC,SAAU,MAChB,kBAACC,EAAkB7F,KAAK8F,OAGpC,M,oEAAC,EAPyB,CAASC,EAAAA,WASjCF,GAAgBG,EAAAA,EAAAA,gBAAeN,EAAWH,EAAAA,EAAAA,OACnCU,EAAe,CACxBC,YAAa,SACbC,aAAc,EACdC,UAAW,IACXC,IAAK,GACLC,aAAc,GACdC,cAAe,OACfC,aAAc,GACdC,YAAa,OACbC,UAAW,SACXC,QAAS,GACTC,oBAAoB,EACpBC,sBAAuB,GACvBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,+BAAgC,GAChCC,kCAAmC,GACnCC,qBAAsB,GACtBC,wBAAyB,GACzBC,wBAAyB,GACzBC,8BAA+B,GAC/BC,iCAAkC,GAClCC,SAAU,CAAC,EACXC,qBAAsB,GACtBC,cAAe,GACfC,YAAY,EACZC,eAAgB,CAAC,EACjBC,cAAe,CACXC,SAAS,EACTC,KAAM,GAEVC,WAAY,CACRF,SAAS,EACTC,KAAM,GAEVE,iBAAkB,CACdC,YAAa,SACbC,MAAM,GAEVC,QAAS,CAAC,EACVC,oBAAqB,GACrBC,aAAc,GACdC,eAAgB,CAAC,EACjBC,cAAe,IACfC,iBAAkB,IAClBC,mBAAmB,EACnBC,UAAU,EACVC,eAAgB,UAChBC,cAAe,OACfC,+BAA+B,EAC/BC,eAAgB,GAChBC,iBAAkB,GAClBC,cAAe,GACfC,iBAAkB,GAClBC,iBAAiB,EACjBC,gBAAgB,EAChBC,YAAa,CAAC,EACdC,uBAAwB,GACxBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,gBAAgB,EAChBC,gBAAiB,CACb,eACA,eACA,iBACA,eACA,mBACA,gBACA,WAEJC,iBAAkB,SAETC,EAAY,CAarB9B,KAAM+B,IAAAA,QACFA,IAAAA,SACIA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,SAQZC,QAASD,IAAAA,QACLA,IAAAA,MAAgB,CAMZzG,GAAIyG,IAAAA,OAAAA,WAMJE,KAAMF,IAAAA,UAAoB,CACtBA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UACnBG,WAsBHC,KAAMJ,IAAAA,MAAgB,CAAC,MAAO,UAAW,OAAQ,aAMjDK,aAAcL,IAAAA,MAAgB,CAAC,QAAS,WAAY,aAkBpDM,WAAYN,IAAAA,UAAoB,CAC5BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAoBtBO,UAAWP,IAAAA,UAAoB,CAC3BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAkBtBQ,UAAWR,IAAAA,UAAoB,CAC3BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAWtBnB,SAAUmB,IAAAA,KAiBVS,SAAUT,IAAAA,UAAoB,CAC1BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAkBtBU,UAAWV,IAAAA,UAAoB,CAC3BA,IAAAA,MAAgB,CAAC,QAAS,SAC1BA,IAAAA,KACAA,IAAAA,QAAkBA,IAAAA,QAStBlC,eAAgBkC,IAAAA,MAAgB,CAI5BW,KAAMX,IAAAA,MAAgB,CAAC,YAAa,gBAIpCY,iBAAkBZ,IAAAA,SAQtBa,OAAQb,IAAAA,MAAgB,CAKpBc,OAAQd,IAAAA,MAAgB,CAMpBe,OAAQf,IAAAA,QAAkBA,IAAAA,QAI1BgB,QAAShB,IAAAA,OAITiB,MAAOjB,IAAAA,OAKPkB,SAAUlB,IAAAA,QAAkBA,IAAAA,QAI5BmB,SAAUnB,IAAAA,QAAkBA,IAAAA,QAI5BoB,QAASpB,IAAAA,OAITqB,iBAAkBrB,IAAAA,OAMtBsB,MAAOtB,IAAAA,IAKPxH,OAAQwH,IAAAA,OAIRuB,UAAWvB,IAAAA,SAKfwB,UAAWxB,IAAAA,MAAgB,CAOvByB,OAAQzB,IAAAA,MAAgB,CAAC,SAAU,OAAQ,aAO3C0B,QAAS1B,IAAAA,MAAgB,CAAC,SAAU,UAAW,aAOnDrD,aAAcqD,IAAAA,QACVA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,QAOR2B,WAAY3B,IAAAA,MAAgB,CAIxB4B,WAAY5B,IAAAA,KAIZxK,QAASwK,IAAAA,IAOT6B,SAAU7B,IAAAA,UActBnB,SAAUmB,IAAAA,KAiBVjC,cAAeiC,IAAAA,UAAoB,CAC/BA,IAAAA,MAAgB,CAIZ/B,KAAM+B,IAAAA,MAAgB,CAAC,IACvBhC,QAASgC,IAAAA,MAAgB,EAAC,MAE9BA,IAAAA,MAAgB,CAIZ/B,KAAM+B,IAAAA,OACNhC,QAASgC,IAAAA,MAAgB,EAAC,IAAOG,eAgBzCjC,WAAY8B,IAAAA,UAAoB,CAC5BA,IAAAA,MAAgB,CAIZ/B,KAAM+B,IAAAA,MAAgB,CAAC,IACvBhC,QAASgC,IAAAA,MAAgB,EAAC,MAE9BA,IAAAA,MAAgB,CAIZ/B,KAAM+B,IAAAA,OACNhC,QAASgC,IAAAA,MAAgB,EAAC,IAAOG,eAezCvB,kBAAmBoB,IAAAA,MAAgB,CAAC,SAAU,SAAS,IAKvDX,gBAAiBW,IAAAA,KAWjBV,eAAgBU,IAAAA,MAAgB,CAAC,SAAU,SAAS,IAKpD8B,cAAe9B,IAAAA,KAKf+B,YAAa/B,IAAAA,MAAgB,CACzBgC,IAAKhC,IAAAA,OACLiC,OAAQjC,IAAAA,OACRkC,OAAQlC,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/CmC,UAAWnC,IAAAA,SASff,eAAgBe,IAAAA,QACZA,IAAAA,MAAgB,CACZgC,IAAKhC,IAAAA,OACLiC,OAAQjC,IAAAA,OACRkC,OAAQlC,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/CmC,UAAWnC,IAAAA,UAQnBb,cAAea,IAAAA,QAAkBA,IAAAA,QAMjCd,iBAAkBc,IAAAA,QAAkBA,IAAAA,QAMpCZ,iBAAkBY,IAAAA,QACdA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAS3CoC,WAAYpC,IAAAA,MAAgB,CACxBgC,IAAKhC,IAAAA,OACLiC,OAAQjC,IAAAA,OACRkC,OAAQlC,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/CmC,UAAWnC,IAAAA,SASfqC,SAAUrC,IAAAA,MAAgB,CACtBgC,IAAKhC,IAAAA,OACLiC,OAAQjC,IAAAA,OACRkC,OAAQlC,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAC/CmC,UAAWnC,IAAAA,SAUfsC,cAAetC,IAAAA,QAAkBA,IAAAA,QAKjCuC,eAAgBvC,IAAAA,QAAkBA,IAAAA,QAIlCwC,WAAYxC,IAAAA,KAOZyC,wBAAyBzC,IAAAA,KAOzB0C,eAAgB1C,IAAAA,OAMhBhB,8BAA+BgB,IAAAA,KAM/BlB,eAAgBkB,IAAAA,MAAgB,CAAC,MAAO,YAKxCjB,cAAeiB,IAAAA,MAAgB,CAAC,MAAO,OAAQ,SAU/C2C,eAAgB3C,IAAAA,MAAgB,CAAC,OAAQ,MAAO,QAAS,YAqBzD3D,YAAa2D,IAAAA,MAAgB,CAAC,SAAU,SAAU,SAMlD1D,aAAc0D,IAAAA,OAOd4C,WAAY5C,IAAAA,OAKZzD,UAAWyD,IAAAA,OAMXvD,aAAcuD,IAAAA,OAYdtD,cAAesD,IAAAA,UAAoB,CAC/BA,IAAAA,MAAgB,CAAC,SAAU,SAAU,SACrCA,IAAAA,MAAgB,CACZI,KAAMJ,IAAAA,MAAgB,CAAC,SAAU,WAAWG,WAC5C0C,SAAU7C,IAAAA,MAAgB,CAAC,MAAO,WAU1ClC,eAAgBkC,IAAAA,MAAgB,CAI5BW,KAAMX,IAAAA,MAAgB,CAAC,YAAa,gBAIpCY,iBAAkBZ,IAAAA,SAgBtBpD,YAAaoD,IAAAA,MAAgB,CAAC,SAAU,SAAU,SAUlDnD,UAAWmD,IAAAA,MAAgB,CAAC,SAAU,UAYtClD,QAASkD,IAAAA,QACLA,IAAAA,MAAgB,CACZmC,UAAWnC,IAAAA,OAAAA,WACX8C,UAAW9C,IAAAA,MAAgB,CAAC,MAAO,SAASG,cASpDxD,aAAcqD,IAAAA,QACVA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,QASRtC,SAAUsC,IAAAA,SACNA,IAAAA,MAAgB,CACZO,UAAWP,IAAAA,KACX+C,QAAS/C,IAAAA,QACLA,IAAAA,MAAgB,CACZgD,MAAOhD,IAAAA,OAAAA,WACP5K,MAAO4K,IAAAA,UAAoB,CACvBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,OACDG,cAETA,cAUVxC,qBAAsBqC,IAAAA,QAClBA,IAAAA,MAAgB,CACZO,UAAWP,IAAAA,KACXiD,GAAIjD,IAAAA,MAAgB,CAChBmC,UAAWnC,IAAAA,OACXvD,aAAcuD,IAAAA,SAElB+C,QAAS/C,IAAAA,QACLA,IAAAA,MAAgB,CACZgD,MAAOhD,IAAAA,OAAAA,WACP5K,MAAO4K,IAAAA,UAAoB,CACvBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,OACDG,cAETA,cAQVvC,cAAeoC,IAAAA,QACXA,IAAAA,SACIA,IAAAA,MAAgB,CACZO,UAAWP,IAAAA,KACX+C,QAAS/C,IAAAA,QACLA,IAAAA,MAAgB,CACZgD,MAAOhD,IAAAA,OAAAA,WACP5K,MAAO4K,IAAAA,UAAoB,CACvBA,IAAAA,OACAA,IAAAA,OACAA,IAAAA,OACDG,cAETA,eASd7B,QAAS0B,IAAAA,SACLA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,MAAgB,CAOZkD,MAAOlD,IAAAA,OAOPmD,SAAUnD,IAAAA,OAMVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAM/BoD,SAAUpD,IAAAA,MAAgB,CAAC,OAAQ,OAAQ,WAO3C5K,MAAO4K,IAAAA,OAAAA,gBAenBzB,oBAAqByB,IAAAA,QACjBA,IAAAA,MAAgB,CAOZkD,MAAOlD,IAAAA,OAOPmD,SAAUnD,IAAAA,OAOViD,GAAIjD,IAAAA,MAAgB,CAIhBmC,UAAWnC,IAAAA,OAIXvD,aAAcuD,IAAAA,OAIdqD,UAAWrD,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,MAAgB,CAAC,MAAO,aAE7BG,WAMHC,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAI/B5K,MAAO4K,IAAAA,OAAAA,cASfxB,aAAcwB,IAAAA,QACVA,IAAAA,SACIA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,MAAgB,CAOZkD,MAAOlD,IAAAA,OAUPmD,SAAUnD,IAAAA,OAOVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAI/B5K,MAAO4K,IAAAA,OAAAA,iBAevBvB,eAAgBuB,IAAAA,SACZA,IAAAA,UAAoB,CAChBA,IAAAA,OACAA,IAAAA,MAAgB,CAOZkD,MAAOlD,IAAAA,OAUPmD,SAAUnD,IAAAA,OAOVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAI/B5K,MAAO4K,IAAAA,OAAAA,aAEXA,IAAAA,QACIA,IAAAA,UAAoB,CAChBA,IAAAA,MAAgB,CAAC,OACjBA,IAAAA,OACAA,IAAAA,MAAgB,CACZkD,MAAOlD,IAAAA,OACPmD,SAAUnD,IAAAA,OACVI,KAAMJ,IAAAA,MAAgB,CAAC,OAAQ,aAC/B5K,MAAO4K,IAAAA,OAAAA,mBAY3BtB,cAAesB,IAAAA,OAOfrB,iBAAkBqB,IAAAA,OAMlBsD,cAAetD,IAAAA,MAAgB,CAM3Be,OAAQf,IAAAA,QAAkBA,IAAAA,QAI1BgB,QAAShB,IAAAA,OAITiB,MAAOjB,IAAAA,OAIPkB,SAAUlB,IAAAA,QAAkBA,IAAAA,QAI5BmB,SAAUnB,IAAAA,QAAkBA,IAAAA,QAI5BoB,QAASpB,IAAAA,OAITqB,iBAAkBrB,IAAAA,OAMtBjD,mBAAoBiD,IAAAA,KAMpBnC,WAAYmC,IAAAA,KAIZ7B,iBAAkB6B,IAAAA,MAAgB,CAM9B5B,YAAa4B,IAAAA,UAAoB,CAC7BA,IAAAA,OACAA,IAAAA,MAAgB,CAAC,SAAU,UAAW,QAAS,WAOnD3B,KAAM2B,IAAAA,OAYVxD,IAAKwD,IAAAA,QACDA,IAAAA,MAAgB,CACZuD,SAAUvD,IAAAA,OAAAA,WACVwD,KAAMxD,IAAAA,OAAAA,cAQdT,YAAaS,IAAAA,OAMbyD,WAAYzD,IAAAA,OAKZ0D,WAAY1D,IAAAA,OAMZ2D,aAAc3D,IAAAA,OAKd4D,aAAc5D,IAAAA,OAKdR,uBAAwBQ,IAAAA,QACpBA,IAAAA,MAAgB,CACZiD,GAAIjD,IAAAA,MAAgB,CAChBmC,UAAWnC,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtB6D,YAAa7D,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,kBAShBP,uBAAwBO,IAAAA,QACpBA,IAAAA,MAAgB,CACZiD,GAAIjD,IAAAA,MAAgB,CAChBmC,UAAWnC,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtB6D,YAAa7D,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,aAEJvD,aAAcuD,IAAAA,OACd8D,MAAO9D,IAAAA,MAAgB,CAAC,SAAU,aAClCqD,UAAWrD,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,MAAgB,CAAC,MAAO,SACxBA,IAAAA,QAAkBA,IAAAA,UAEtB+D,gBAAiB/D,IAAAA,UAQ7BN,yBAA0BM,IAAAA,QACtBA,IAAAA,MAAgB,CACZiD,GAAIjD,IAAAA,MAAgB,CAChBmC,UAAWnC,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtB6D,YAAa7D,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,aAEJ+D,gBAAiB/D,IAAAA,UAQ7BL,yBAA0BK,IAAAA,QACtBA,IAAAA,MAAgB,CACZiD,GAAIjD,IAAAA,MAAgB,CAChBmC,UAAWnC,IAAAA,UAAoB,CAC3BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,UAEtB6D,YAAa7D,IAAAA,MAAgB,CACzB,MACA,UACA,OACA,aAEJgE,aAAchE,IAAAA,UAAoB,CAC9BA,IAAAA,OACAA,IAAAA,QAAkBA,IAAAA,QAClBA,IAAAA,MAAgB,CAAC,MAAO,WAE5B+D,gBAAiB/D,IAAAA,UAW7BJ,eAAgBI,IAAAA,KA0BhBiE,+BAAgCjE,IAAAA,OAMhChD,sBAAuBgD,IAAAA,QAAkBA,IAAAA,QAOzC/C,yBAA0B+C,IAAAA,QAAkBA,IAAAA,QAO5C9C,yBAA0B8C,IAAAA,QACtBA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAM3CkE,kCAAmClE,IAAAA,QAAkBA,IAAAA,QAKrD7C,+BAAgC6C,IAAAA,QAAkBA,IAAAA,QAKlD5C,kCAAmC4C,IAAAA,QAC/BA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAO3C3C,qBAAsB2C,IAAAA,QAAkBA,IAAAA,QAOxC1C,wBAAyB0C,IAAAA,QAAkBA,IAAAA,QAO3CzC,wBAAyByC,IAAAA,QACrBA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAM3CxC,8BAA+BwC,IAAAA,QAAkBA,IAAAA,QAMjDvC,iCAAkCuC,IAAAA,QAC9BA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAK3CzG,GAAIyG,IAAAA,OAIJmE,SAAUnE,IAAAA,KAIVoE,cAAepE,IAAAA,MAAgB,CAI3BqE,WAAYrE,IAAAA,KAIZsE,UAAWtE,IAAAA,OAIXuE,eAAgBvE,IAAAA,SAUpBwE,YAAaxE,IAAAA,UAAoB,CAC7BA,IAAAA,KACAA,IAAAA,OACAA,IAAAA,SAMJH,gBAAiBG,IAAAA,QACbA,IAAAA,MAAgB,CACZ,eACA,OACA,eACA,iBACA,eACA,mBACA,gBACA,aASRF,iBAAkBE,IAAAA,MAAgB,CAAC,QAAS,UAAW,YAE3DnE,EAAU4I,sBAAwB,CAC9BxE,QAAS,CACLC,KAAM,CACFwE,QAAS,SAAAC,GAAS,OAAIjK,EAAAA,IAAQ,OAAQiK,EAAU,EAChDtO,MAAO,SAACuO,EAAaD,GAAS,OAC1BjK,EAAAA,IAAUA,EAAAA,IAAQ,QAASkK,EAAaD,EAAU,KAIlE9I,EAAUO,aAAeA,EACzBP,EAAUkE,UAAYA,C,uBCx7CpB,IAAS8E,SAYQ,IAAV,EAAAC,EAAwB,EAAAA,EAAS3O,KARxCjC,EAAOC,QAQuC,SAAS0Q,GAExD,GAAIA,EAAKrJ,KAAOqJ,EAAKrJ,IAAIuJ,OACxB,OAAOF,EAAKrJ,IAAIuJ,OAIjB,IAAIC,EAAY,SAAS5P,GACxB,GAAwB,GAApBgB,UAAUa,OACb,MAAM,IAAII,UAAU,sCAQrB,IANA,IAGI4N,EAHAC,EAASC,OAAO/P,GAChB6B,EAASiO,EAAOjO,OAChBmO,GAAS,EAETxJ,EAAS,GACTyJ,EAAgBH,EAAOI,WAAW,KAC7BF,EAAQnO,GAOA,IANhBgO,EAAWC,EAAOI,WAAWF,IA2B5BxJ,GAbCqJ,GAAY,GAAUA,GAAY,IAAuB,KAAZA,GAGpC,GAATG,GAAcH,GAAY,IAAUA,GAAY,IAIvC,GAATG,GACAH,GAAY,IAAUA,GAAY,IACjB,IAAjBI,EAIS,KAAOJ,EAASM,SAAS,IAAM,IAOhC,GAATH,GACU,GAAVnO,GACY,IAAZgO,KAWAA,GAAY,KACA,IAAZA,GACY,IAAZA,GACAA,GAAY,IAAUA,GAAY,IAClCA,GAAY,IAAUA,GAAY,IAClCA,GAAY,IAAUA,GAAY,KAdxB,KAAOC,EAAOM,OAAOJ,GAiBrBF,EAAOM,OAAOJ,GAhDxBxJ,GAAU,IAyDZ,OAAOA,CACR,EAOA,OALKiJ,EAAKrJ,MACTqJ,EAAKrJ,IAAM,CAAC,GAGbqJ,EAAKrJ,IAAIuJ,OAASC,EACXA,CAER,CApGmBS,CAAQZ,E,gCCL3B3Q,EAAOC,QAAUF,OAAkB,S,gCCAnCC,EAAOC,QAAUF,OAAc,K,gCCA/BC,EAAOC,QAAUF,OAAiB,Q,oCCWnB,SAAS,EAAQyR,EAAMC,GAGpC,IAAIC,EADJD,EAAOA,GAAQ,GAEf,IAAIE,GAHJH,EAAOA,GAAQ,IAGCzO,OACZ6O,EAAOH,EAAK1O,OACZ2E,EAAS,GAGb,IAFAgK,EAAM,EAECA,EAAMC,GACXjK,EAAOA,EAAO3E,QAAUyO,EAAKE,GAC7BA,GAAO,EAKT,IAFAA,EAAM,EAECA,EAAME,GACXlK,EAAOA,EAAO3E,QAAU0O,EAAKC,GAC7BA,GAAO,EAGT,OAAOhK,CACT,C,+7CCjCe,SAAS,EAAOxH,EAAG8D,GAEhC,OAAQ9D,GACN,KAAK,EACH,OAAO,WACL,OAAO8D,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,GACf,OAAO7N,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,GACnB,OAAO9N,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,GACvB,OAAO/N,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,GAC3B,OAAOhO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,EAAIC,GAC/B,OAAOjO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACnC,OAAOlO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACvC,OAAOnO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAC3C,OAAOpO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,EACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAC/C,OAAOrO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,KAAK,GACH,OAAO,SAAU2P,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACnD,OAAOtO,EAAG7B,MAAMF,KAAMC,UACxB,EAEF,QACE,MAAM,IAAImC,MAAM,+EAEtB,C,wBChDe,SAASkO,EAAQxP,EAAQyP,EAAUxO,GAChD,OAAO,WAML,IALA,IAAIyO,EAAW,GACXC,EAAU,EACVC,EAAO5P,EACP6P,EAAc,EAEXA,EAAcJ,EAASzP,QAAU2P,EAAUxQ,UAAUa,QAAQ,CAClE,IAAI2E,EAEAkL,EAAcJ,EAASzP,WAAY,EAAA8P,EAAA,GAAeL,EAASI,KAAiBF,GAAWxQ,UAAUa,QACnG2E,EAAS8K,EAASI,IAElBlL,EAASxF,UAAUwQ,GACnBA,GAAW,GAGbD,EAASG,GAAelL,GAEnB,EAAAmL,EAAA,GAAenL,KAClBiL,GAAQ,GAGVC,GAAe,CACjB,CAEA,OAAOD,GAAQ,EAAI3O,EAAG7B,MAAMF,KAAMwQ,GAAY,EAAOE,EAAMJ,EAAQxP,EAAQ0P,EAAUzO,GACvF,CACF,CCMA,IAUA,GARA,QAAQ,SAAgBjB,EAAQiB,GAC9B,OAAe,IAAXjB,GACK,OAAQiB,GAGV,EAAOjB,EAAQwP,EAAQxP,EAAQ,GAAIiB,GAC5C,IC3BI8O,GAEJ,QAAQ,SAAkB9O,GACxB,OAAO,EAAOA,EAAGjB,QAAQ,WACvB,IAAI2O,EAAM,EACNqB,EAAS7Q,UAAU,GACnB8Q,EAAO9Q,UAAUA,UAAUa,OAAS,GACpCkQ,EAAOC,MAAM3R,UAAU4R,MAAM3S,KAAK0B,UAAW,GAQjD,OANA+Q,EAAK,GAAK,WACR,IAAIvL,EAASqL,EAAO5Q,MAAMF,KAAM,EAAQC,UAAW,CAACwP,EAAKsB,KAEzD,OADAtB,GAAO,EACAhK,CACT,EAEO1D,EAAG7B,MAAMF,KAAMgR,EACxB,GACF,IAEA,ICnCA,EAAeC,MAAME,SAAW,SAAkBC,GAChD,OAAc,MAAPA,GAAeA,EAAItQ,QAAU,GAA6C,mBAAxCnC,OAAOW,UAAU8P,SAAS7Q,KAAK6S,EAC1E,ECde,SAAS,EAAeC,GACrC,OAAc,MAAPA,GAAmD,mBAA7BA,EAAI,oBACnC,CCgBe,SAASC,EAAcC,EAAaC,EAAmBzP,GACpE,OAAO,WACL,GAAyB,IAArB9B,UAAUa,OACZ,OAAOiB,IAGT,IAAIsP,EAAMpR,UAAUA,UAAUa,OAAS,GAEvC,IAAK,EAASuQ,GAAM,CAGlB,IAFA,IAAI5B,EAAM,EAEHA,EAAM8B,EAAYzQ,QAAQ,CAC/B,GAAqC,mBAA1BuQ,EAAIE,EAAY9B,IACzB,OAAO4B,EAAIE,EAAY9B,IAAMvP,MAAMmR,EAAKJ,MAAM3R,UAAU4R,MAAM3S,KAAK0B,UAAW,GAAI,IAGpFwP,GAAO,CACT,CAEA,GAAI,EAAe4B,GAAM,CACvB,IAAII,EAAaD,EAAkBtR,MAAM,KAAM+Q,MAAM3R,UAAU4R,MAAM3S,KAAK0B,UAAW,GAAI,IACzF,OAAOwR,EAAWJ,EACpB,CACF,CAEA,OAAOtP,EAAG7B,MAAMF,KAAMC,UACxB,CACF,CC7Ce,SAASyR,EAASC,GAC/B,OAAOA,GAAKA,EAAE,wBAA0BA,EAAI,CAC1C,qBAAsBA,EACtB,wBAAwB,EAE5B,CCLA,OACEC,KAAM,WACJ,OAAO5R,KAAK6R,GAAG,sBACjB,EACApM,OAAQ,SAAUA,GAChB,OAAOzF,KAAK6R,GAAG,uBAAuBpM,EACxC,GCFEqM,EAEJ,WACE,SAASA,EAAKjR,EAAGgR,GACf7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,EACTb,KAAK+R,KAAM,CACb,CAqBA,OAnBAD,EAAKxS,UAAU,qBAAuB0S,EAAQJ,KAE9CE,EAAKxS,UAAU,uBAAyB,SAAUmG,GAKhD,OAJIzF,KAAK+R,MACPtM,EAASzF,KAAK6R,GAAG,qBAAqBpM,GAAQ,IAGzCzF,KAAK6R,GAAG,uBAAuBpM,EACxC,EAEAqM,EAAKxS,UAAU,qBAAuB,SAAUmG,EAAQwM,GAMtD,OALKjS,KAAKa,EAAEoR,KACVjS,KAAK+R,KAAM,EACXtM,EAASiM,EAAS1R,KAAK6R,GAAG,qBAAqBpM,GAAQ,KAGlDA,CACT,EAEOqM,CACT,CA3BA,GAmCA,GAJA,QAAQ,SAAejR,EAAGgR,GACxB,OAAO,IAAIC,EAAKjR,EAAGgR,EACrB,ICOA,GAhBA,OAEAP,EAAc,CAAC,OAAQ,GAAO,SAAavP,EAAIgP,GAG7C,IAFA,IAAItB,EAAM,EAEHA,EAAMsB,EAAKjQ,QAAQ,CACxB,IAAKiB,EAAGgP,EAAKtB,IACX,OAAO,EAGTA,GAAO,CACT,CAEA,OAAO,CACT,KC5Ce,SAAS,EAAK1N,EAAImQ,GAK/B,IAJA,IAAIzC,EAAM,EACN0C,EAAMD,EAAQpR,OACd2E,EAASwL,MAAMkB,GAEZ1C,EAAM0C,GACX1M,EAAOgK,GAAO1N,EAAGmQ,EAAQzC,IACzBA,GAAO,EAGT,OAAOhK,CACT,CCXe,SAAS,EAAUkM,GAChC,MAA6C,oBAAtChT,OAAOW,UAAU8P,SAAS7Q,KAAKoT,EACxC,CCoBA,IA8BA,GA5BA,QAAQ,SAAqBA,GAC3B,QAAI,EAASA,MAIRA,GAIY,iBAANA,IAIP,EAAUA,KAIG,IAAbA,EAAE7Q,QAIF6Q,EAAE7Q,OAAS,GACN6Q,EAAEpS,eAAe,IAAMoS,EAAEpS,eAAeoS,EAAE7Q,OAAS,GAI9D,IClDIsR,EAEJ,WACE,SAASA,EAAMrQ,GACb/B,KAAKa,EAAIkB,CACX,CAcA,OAZAqQ,EAAM9S,UAAU,qBAAuB,WACrC,MAAM,IAAI8C,MAAM,gCAClB,EAEAgQ,EAAM9S,UAAU,uBAAyB,SAAU+S,GACjD,OAAOA,CACT,EAEAD,EAAM9S,UAAU,qBAAuB,SAAU+S,EAAKV,GACpD,OAAO3R,KAAKa,EAAEwR,EAAKV,EACrB,EAEOS,CACT,CAlBA,GCuBIhT,GAEJ,QAAQ,SAAc2C,EAAIuQ,GACxB,OAAO,EAAOvQ,EAAGjB,QAAQ,WACvB,OAAOiB,EAAG7B,MAAMoS,EAASrS,UAC3B,GACF,IAEA,ICXA,SAASsS,EAAgBV,EAAIQ,EAAKG,GAGhC,IAFA,IAAIC,EAAOD,EAAK9R,QAER+R,EAAK7S,MAAM,CAGjB,IAFAyS,EAAMR,EAAG,qBAAqBQ,EAAKI,EAAKxT,SAE7BoT,EAAI,wBAAyB,CACtCA,EAAMA,EAAI,sBACV,KACF,CAEAI,EAAOD,EAAK9R,MACd,CAEA,OAAOmR,EAAG,uBAAuBQ,EACnC,CAEA,SAASK,EAAcb,EAAIQ,EAAKhB,EAAKsB,GACnC,OAAOd,EAAG,uBAAuBR,EAAIsB,GAAY,EAAKd,EAAG,qBAAsBA,GAAKQ,GACtF,CAEA,IAAIO,EAAgC,oBAAX7T,OAAyBA,OAAO8T,SAAW,aACrD,SAAS,EAAQ9Q,EAAIsQ,EAAKtB,GAKvC,GAJkB,mBAAPhP,IACTA,EFxBW,SAAgBA,GAC7B,OAAO,IAAIqQ,EAAMrQ,EACnB,CEsBS,CAAOA,IAGV,EAAagP,GACf,OA9CJ,SAAsBc,EAAIQ,EAAKtB,GAI7B,IAHA,IAAItB,EAAM,EACN0C,EAAMpB,EAAKjQ,OAER2O,EAAM0C,GAAK,CAGhB,IAFAE,EAAMR,EAAG,qBAAqBQ,EAAKtB,EAAKtB,MAE7B4C,EAAI,wBAAyB,CACtCA,EAAMA,EAAI,sBACV,KACF,CAEA5C,GAAO,CACT,CAEA,OAAOoC,EAAG,uBAAuBQ,EACnC,CA8BWS,CAAa/Q,EAAIsQ,EAAKtB,GAG/B,GAA2C,mBAAhCA,EAAK,uBACd,OAAO2B,EAAc3Q,EAAIsQ,EAAKtB,EAAM,uBAGtC,GAAyB,MAArBA,EAAK6B,GACP,OAAOL,EAAgBxQ,EAAIsQ,EAAKtB,EAAK6B,MAGvC,GAAyB,mBAAd7B,EAAKrQ,KACd,OAAO6R,EAAgBxQ,EAAIsQ,EAAKtB,GAGlC,GAA2B,mBAAhBA,EAAKgC,OACd,OAAOL,EAAc3Q,EAAIsQ,EAAKtB,EAAM,UAGtC,MAAM,IAAI7P,UAAU,yCACtB,CCnEA,IAAI8R,EAEJ,WACE,SAASA,EAAKnS,EAAGgR,GACf7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,CACX,CASA,OAPAmS,EAAK1T,UAAU,qBAAuB0S,EAAQJ,KAC9CoB,EAAK1T,UAAU,uBAAyB0S,EAAQvM,OAEhDuN,EAAK1T,UAAU,qBAAuB,SAAUmG,EAAQwM,GACtD,OAAOjS,KAAK6R,GAAG,qBAAqBpM,EAAQzF,KAAKa,EAAEoR,GACrD,EAEOe,CACT,CAdA,GAsBA,GAJA,QAAQ,SAAenS,EAAGgR,GACxB,OAAO,IAAImB,EAAKnS,EAAGgR,EACrB,ICzBe,SAAS,EAAKoB,EAAM5B,GACjC,OAAO1S,OAAOW,UAAUC,eAAehB,KAAK8S,EAAK4B,EACnD,CCDA,IAAI,EAAWtU,OAAOW,UAAU8P,SAE5B,EAEJ,WACE,MAAoC,uBAA7B,EAAS7Q,KAAK0B,WAAsC,SAAsB0R,GAC/E,MAA4B,uBAArB,EAASpT,KAAKoT,EACvB,EAAI,SAAsBA,GACxB,OAAO,EAAK,SAAUA,EACxB,CACF,CANA,GAQA,ICTIuB,GAEJ,CACE9D,SAAU,MACV+D,qBAAqB,YACnBC,EAAqB,CAAC,cAAe,UAAW,gBAAiB,WAAY,uBAAwB,iBAAkB,kBAEvHC,EAEJ,WAGE,OAAOpT,UAAUkT,qBAAqB,SACxC,CAJA,GAMIG,EAAW,SAAkBvC,EAAMwC,GAGrC,IAFA,IAAI9D,EAAM,EAEHA,EAAMsB,EAAKjQ,QAAQ,CACxB,GAAIiQ,EAAKtB,KAAS8D,EAChB,OAAO,EAGT9D,GAAO,CACT,CAEA,OAAO,CACT,EA2DA,EAtCkC,mBAAhB9Q,OAAO6U,MAAwBH,GAMjD,QAAQ,SAAchC,GACpB,GAAI1S,OAAO0S,KAASA,EAClB,MAAO,GAGT,IAAI4B,EAAMQ,EACNC,EAAK,GAELC,EAAkBN,GAAkB,EAAahC,GAErD,IAAK4B,KAAQ5B,GACP,EAAK4B,EAAM5B,IAAUsC,GAA4B,WAATV,IAC1CS,EAAGA,EAAG5S,QAAUmS,GAIpB,GAAIC,EAGF,IAFAO,EAAOL,EAAmBtS,OAAS,EAE5B2S,GAAQ,GAGT,EAFJR,EAAOG,EAAmBK,GAEXpC,KAASiC,EAASI,EAAIT,KACnCS,EAAGA,EAAG5S,QAAUmS,GAGlBQ,GAAQ,EAIZ,OAAOC,CACT,KAnCA,QAAQ,SAAcrC,GACpB,OAAO1S,OAAO0S,KAASA,EAAM,GAAK1S,OAAO6U,KAAKnC,EAChD,ICbI,GAEJ,OAEAC,EAAc,CAAC,mBAAoB,OAAQ,GAAO,SAAavP,EAAImQ,GACjE,OAAQvT,OAAOW,UAAU8P,SAAS7Q,KAAK2T,IACrC,IAAK,oBACH,OAAO,EAAOA,EAAQpR,QAAQ,WAC5B,OAAOiB,EAAGxD,KAAKyB,KAAMkS,EAAQhS,MAAMF,KAAMC,WAC3C,IAEF,IAAK,kBACH,OAAO,GAAQ,SAAUoS,EAAKpR,GAE5B,OADAoR,EAAIpR,GAAOc,EAAGmQ,EAAQjR,IACfoR,CACT,GAAG,CAAC,EAAG,EAAKH,IAEd,QACE,OAAO,EAAKnQ,EAAImQ,GAEtB,KAEA,ICzDA,EAAe0B,OAAOC,WAAa,SAAoB5V,GACrD,OAAOA,GAAK,IAAMA,CACpB,EC0BA,GALA,QAAQ,SAAa6V,EAAQ/C,GAC3B,IAAItB,EAAMqE,EAAS,EAAI/C,EAAKjQ,OAASgT,EAASA,EAC9C,OAAO,EAAU/C,GAAQA,EAAK1B,OAAOI,GAAOsB,EAAKtB,EACnD,ICCA,GARA,QAAQ,SAAcjQ,EAAG6R,GACvB,GAAW,MAAPA,EAIJ,OAAO,EAAW7R,GAAK,EAAIA,EAAG6R,GAAOA,EAAI7R,EAC3C,ICIA,GAJA,QAAQ,SAAeA,EAAGuR,GACxB,OAAO,EAAI,EAAKvR,GAAIuR,EACtB,I,UCkBA,GAFA,OAAQ,GC/CJgD,EAEJ,WACE,SAASA,EAAKlT,EAAGgR,GACf7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,EACTb,KAAKgU,KAAM,CACb,CAqBA,OAnBAD,EAAKzU,UAAU,qBAAuB0S,EAAQJ,KAE9CmC,EAAKzU,UAAU,uBAAyB,SAAUmG,GAKhD,OAJKzF,KAAKgU,MACRvO,EAASzF,KAAK6R,GAAG,qBAAqBpM,GAAQ,IAGzCzF,KAAK6R,GAAG,uBAAuBpM,EACxC,EAEAsO,EAAKzU,UAAU,qBAAuB,SAAUmG,EAAQwM,GAMtD,OALIjS,KAAKa,EAAEoR,KACTjS,KAAKgU,KAAM,EACXvO,EAASiM,EAAS1R,KAAK6R,GAAG,qBAAqBpM,GAAQ,KAGlDA,CACT,EAEOsO,CACT,CA3BA,GAmCA,GAJA,QAAQ,SAAelT,EAAGgR,GACxB,OAAO,IAAIkC,EAAKlT,EAAGgR,EACrB,ICQA,GAhBA,OAEAP,EAAc,CAAC,OAAQ,GAAO,SAAavP,EAAIgP,GAG7C,IAFA,IAAItB,EAAM,EAEHA,EAAMsB,EAAKjQ,QAAQ,CACxB,GAAIiB,EAAGgP,EAAKtB,IACV,OAAO,EAGTA,GAAO,CACT,CAEA,OAAO,CACT,KChBA,GAJA,QAAQ,SAAgBwE,EAAIlD,GAC1B,OAAO,EAAQA,EAAM,CAACkD,GACxB,ICSA,GAdA,QAAQ,SAAgB5C,GAMtB,IALA,IAAIvL,EAAQ,EAAKuL,GACbc,EAAMrM,EAAMhF,OACZoT,EAAO,GACPzE,EAAM,EAEHA,EAAM0C,GACX+B,EAAKzE,GAAO4B,EAAIvL,EAAM2J,IACtBA,GAAO,EAGT,OAAOyE,CACT,ICTA,IAJA,QAAQ,SAAevC,GACrB,OAAY,MAALA,CACT,ICwBA,IAfA,QAAQ,SAASwC,EAAU7Q,EAAM8N,EAAKC,GACpC,GAAoB,IAAhB/N,EAAKxC,OACP,OAAOsQ,EAGT,IAAI3B,EAAMnM,EAAK,GAEf,GAAIA,EAAKxC,OAAS,EAAG,CACnB,IAAIsT,GAAW,GAAM/C,IAAQ,EAAK5B,EAAK4B,GAAOA,EAAI5B,GAAO,EAAWnM,EAAK,IAAM,GAAK,CAAC,EACrF8N,EAAM+C,EAAUlD,MAAM3R,UAAU4R,MAAM3S,KAAK+E,EAAM,GAAI8N,EAAKgD,EAC5D,CAEA,OC7Ba,SAAgBnB,EAAM7B,EAAKC,GACxC,GAAI,EAAW4B,IAAS,EAAS5B,GAAM,CACrC,IAAIgD,EAAM,GAAGC,OAAOjD,GAEpB,OADAgD,EAAIpB,GAAQ7B,EACLiD,CACT,CAEA,IAAI5O,EAAS,CAAC,EAEd,IAAK,IAAIjG,KAAK6R,EACZ5L,EAAOjG,GAAK6R,EAAI7R,GAIlB,OADAiG,EAAOwN,GAAQ7B,EACR3L,CACT,CDcS,CAAOgK,EAAK2B,EAAKC,EAC1B,IEfA,IAJA,QAAQ,SAAe4B,EAAM7B,EAAKC,GAChC,OAAO,GAAU,CAAC4B,GAAO7B,EAAKC,EAChC,IClBA,IAJA,QAAQ,SAAiBxQ,EAAGgR,GAC1B,OAAO,EAAIhR,ECWE,SAAegR,GAC5B,IAAI0C,EAdkB,SAAU1C,GAChC,MAAO,CACL,oBAAqBG,EAAQJ,KAC7B,sBAAuB,SAAUnM,GAC/B,OAAOoM,EAAG,uBAAuBpM,EACnC,EACA,oBAAqB,SAAUA,EAAQwM,GACrC,IAAIzN,EAAMqN,EAAG,qBAAqBpM,EAAQwM,GAC1C,OAAOzN,EAAI,wBCZR,CACL,qBDWqDA,ECVrD,wBAAwB,GDUoCA,CAC5D,EAEJ,CAGYgQ,CAAkB3C,GAC5B,MAAO,CACL,oBAAqBG,EAAQJ,KAC7B,sBAAuB,SAAUnM,GAC/B,OAAO8O,EAAI,uBAAuB9O,EACpC,EACA,oBAAqB,SAAUA,EAAQwM,GACrC,OAAQ,EAAaA,GAAyC,EAAQsC,EAAK9O,EAAQwM,GAArD,EAAQsC,EAAK9O,EAAQ,CAACwM,GACtD,EAEJ,CDtBgB,CAASJ,GACzB,IGsCA,IAZA,OAEAP,EAAc,CAAC,qBAAsB,SAAU,IAAS,SAAevP,EAAI0S,GACzE,MAAqB,mBAAVA,EACF,SAAU9C,GACf,OAAO5P,EAAG0S,EAAM9C,GAAT5P,CAAa4P,EACtB,IAGe,EClCV,SAAeZ,GAMpB,IALA,IAAI9R,EAAOyV,EAAMC,EACblP,EAAS,GACTgK,EAAM,EACNmF,EAAO7D,EAAKjQ,OAET2O,EAAMmF,GAAM,CACjB,GAAI,EAAa7D,EAAKtB,IAKpB,IAHAkF,EAAI,EACJD,GAFAzV,EAAuC8R,EAAKtB,IAE/B3O,OAEN6T,EAAID,GACTjP,EAAOA,EAAO3E,QAAU7B,EAAM0V,GAC9BA,GAAK,OAGPlP,EAAOA,EAAO3E,QAAUiQ,EAAKtB,GAG/BA,GAAO,CACT,CAEA,OAAOhK,CACT,GDUwB,EAAI1D,EAAI0S,GAClC,KEXA,IAJA,QAAQ,SAAcrD,GACpB,OAAe,OAARA,EAAe,YAAiByD,IAARzD,EAAoB,YAAczS,OAAOW,UAAU8P,SAAS7Q,KAAK6S,GAAKF,MAAM,GAAI,EACjH,IClBe,SAAS,GAAOjS,EAAO6V,EAASC,EAAOC,GACpD,ICdmCC,EDc/BC,EAAO,SAAcC,GAIvB,IAHA,IAAIhD,EAAM2C,EAAQhU,OACd2O,EAAM,EAEHA,EAAM0C,GAAK,CAChB,GAAIlT,IAAU6V,EAAQrF,GACpB,OAAOsF,EAAMtF,GAGfA,GAAO,CACT,CAKA,IAAK,IAAIxO,KAHT6T,EAAQrF,GAAOxQ,EACf8V,EAAMtF,GAAO0F,EAEGlW,EACVA,EAAMM,eAAe0B,KACvBkU,EAAYlU,GAAO+T,EAAO,GAAO/V,EAAMgC,GAAM6T,EAASC,GAAO,GAAQ9V,EAAMgC,IAI/E,OAAOkU,CACT,EAEA,OAAQ,GAAKlW,IACX,IAAK,SACH,OAAOiW,EAAKvW,OAAOQ,OAAOR,OAAOyW,eAAenW,KAElD,IAAK,QACH,OAAOiW,EAAK,IAEd,IAAK,OACH,OAAO,IAAIzR,KAAKxE,EAAMoW,WAExB,IAAK,SACH,OCjD+BJ,EDiDXhW,EChDjB,IAAIqW,OAAOL,EAAQM,QAASN,EAAQO,OAAS,IAAM,KAAOP,EAAQQ,WAAa,IAAM,KAAOR,EAAQS,UAAY,IAAM,KAAOT,EAAQU,OAAS,IAAM,KAAOV,EAAQW,QAAU,IAAM,KDkDxL,IAAK,YACL,IAAK,aACL,IAAK,oBACL,IAAK,aACL,IAAK,cACL,IAAK,aACL,IAAK,cACL,IAAK,eACL,IAAK,eACL,IAAK,gBACL,IAAK,iBACH,OAAO3W,EAAMiS,QAEf,QACE,OAAOjS,EAEb,CErCA,IAMA,IAJA,QAAQ,SAAeA,GACrB,OAAgB,MAATA,GAAwC,mBAAhBA,EAAM4W,MAAuB5W,EAAM4W,QAAU,GAAO5W,EAAO,GAAI,IAAI,EACpG,ICDA,IANA,QAAQ,SAAoB6W,GAC1B,OAAO,SAAUnW,EAAGoW,GAClB,OAAOD,EAAKnW,EAAGoW,IAAM,EAAID,EAAKC,EAAGpW,GAAK,EAAI,CAC5C,CACF,IC/Be,SAASqW,GAAMnV,EAAG8N,GAC/B,OAAO,WACL,OAAOA,EAAEpQ,KAAKyB,KAAMa,EAAEX,MAAMF,KAAMC,WACpC,CACF,CCQe,SAAS,GAAgBgW,EAAYlU,GAClD,OAAO,WACL,IAAIjB,EAASb,UAAUa,OAEvB,GAAe,IAAXA,EACF,OAAOiB,IAGT,IAAIsP,EAAMpR,UAAUa,EAAS,GAC7B,OAAO,EAASuQ,IAAmC,mBAApBA,EAAI4E,GAA6BlU,EAAG7B,MAAMF,KAAMC,WAAaoR,EAAI4E,GAAY/V,MAAMmR,EAAKJ,MAAM3R,UAAU4R,MAAM3S,KAAK0B,UAAW,EAAGa,EAAS,GAC3K,CACF,CCIA,IAQA,IANA,OAEA,GAAgB,SAAS,SAAeoV,EAAWC,EAASpF,GAC1D,OAAOE,MAAM3R,UAAU4R,MAAM3S,KAAKwS,EAAMmF,EAAWC,EACrD,KCMA,IANA,OAEA,GAAgB,OAEhB,GAAM,EAAGC,OCRM,SAASC,KACtB,GAAyB,IAArBpW,UAAUa,OACZ,MAAM,IAAIsB,MAAM,uCAGlB,OAAO,EAAOnC,UAAU,GAAGa,OAAQ,EAAOkV,GAAO/V,UAAU,GAAI,GAAKA,YACtE,CCRA,IAMA,IAJA,QAAQ,SAAiB8Q,GACvB,OAAO,EAAUA,GAAQA,EAAKhN,MAAM,IAAIuS,UAAUC,KAAK,IAAMtF,MAAM3R,UAAU4R,MAAM3S,KAAKwS,EAAM,GAAGuF,SACnG,ICHe,SAASE,KACtB,GAAyB,IAArBvW,UAAUa,OACZ,MAAM,IAAIsB,MAAM,0CAGlB,OAAOiU,GAAKnW,MAAMF,KAAM,GAAQC,WAClC,CClCe,SAAS,GAAY0R,GAClC,IAAI1H,EAAOtL,OAAOW,UAAU8P,SAAS7Q,KAAKoT,GAC1C,MAAgB,sBAAT1H,GAAyC,2BAATA,GAA8C,+BAATA,GAAkD,oCAATA,CACvH,CCHe,SAASwM,GAAmBjE,GAIzC,IAHA,IACI9R,EADAqQ,EAAO,KAGFrQ,EAAO8R,EAAK9R,QAAQd,MAC3BmR,EAAK2F,KAAKhW,EAAKzB,OAGjB,OAAO8R,CACT,CCTe,SAAS4F,GAAcb,EAAMnE,EAAGZ,GAI7C,IAHA,IAAItB,EAAM,EACN0C,EAAMpB,EAAKjQ,OAER2O,EAAM0C,GAAK,CAChB,GAAI2D,EAAKnE,EAAGZ,EAAKtB,IACf,OAAO,EAGTA,GAAO,CACT,CAEA,OAAO,CACT,CCAA,OAAoC,mBAAd9Q,OAAOiY,GAAoBjY,OAAOiY,GAZxD,SAAmBjX,EAAGoW,GAEpB,OAAIpW,IAAMoW,EAGK,IAANpW,GAAW,EAAIA,GAAM,EAAIoW,EAGzBpW,GAAMA,GAAKoW,GAAMA,CAE5B,ECOA,SAASc,GAAmBC,EAAWC,EAAWC,EAAQC,GACxD,IAAItX,EAAI8W,GAAmBK,GAI3B,SAASI,EAAGC,EAAIC,GACd,OAAOC,GAAQF,EAAIC,EAAIJ,EAAO9F,QAAS+F,EAAO/F,QAChD,CAGA,OAAQyF,IAAc,SAAUZ,EAAGuB,GACjC,OAAQX,GAAcO,EAAII,EAAOvB,EACnC,GATQU,GAAmBM,GASrBpX,EACR,CAEe,SAAS0X,GAAQ1X,EAAGoW,EAAGiB,EAAQC,GAC5C,GAAI,GAAUtX,EAAGoW,GACf,OAAO,EAGT,ICtCoClV,EAEhC0W,EDoCAC,EAAQ,GAAK7X,GAEjB,GAAI6X,IAAU,GAAKzB,GACjB,OAAO,EAGT,GAAwC,mBAA7BpW,EAAE,wBAA6E,mBAA7BoW,EAAE,uBAC7D,MAA2C,mBAA7BpW,EAAE,wBAAyCA,EAAE,uBAAuBoW,IAA0C,mBAA7BA,EAAE,wBAAyCA,EAAE,uBAAuBpW,GAGrK,GAAwB,mBAAbA,EAAE8X,QAA6C,mBAAb1B,EAAE0B,OAC7C,MAA2B,mBAAb9X,EAAE8X,QAAyB9X,EAAE8X,OAAO1B,IAA0B,mBAAbA,EAAE0B,QAAyB1B,EAAE0B,OAAO9X,GAGrG,OAAQ6X,GACN,IAAK,YACL,IAAK,QACL,IAAK,SACH,GAA6B,mBAAlB7X,EAAEmF,aAA+D,aCxD5CjE,EDwDyBlB,EAAEmF,YCrD/C,OADZyS,EAAQvI,OAAOnO,GAAG0W,MAAM,oBACL,GAAKA,EAAM,IDsD5B,OAAO5X,IAAMoW,EAGf,MAEF,IAAK,UACL,IAAK,SACL,IAAK,SACH,UAAapW,UAAaoW,IAAK,GAAUpW,EAAE0V,UAAWU,EAAEV,WACtD,OAAO,EAGT,MAEF,IAAK,OACH,IAAK,GAAU1V,EAAE0V,UAAWU,EAAEV,WAC5B,OAAO,EAGT,MAEF,IAAK,QACH,OAAO1V,EAAEoK,OAASgM,EAAEhM,MAAQpK,EAAE+X,UAAY3B,EAAE2B,QAE9C,IAAK,SACH,GAAM/X,EAAE4V,SAAWQ,EAAER,QAAU5V,EAAE6V,SAAWO,EAAEP,QAAU7V,EAAE8V,aAAeM,EAAEN,YAAc9V,EAAE+V,YAAcK,EAAEL,WAAa/V,EAAEgW,SAAWI,EAAEJ,QAAUhW,EAAEiW,UAAYG,EAAEH,QAC/J,OAAO,EAQb,IAFA,IAAInG,EAAMuH,EAAOlW,OAAS,EAEnB2O,GAAO,GAAG,CACf,GAAIuH,EAAOvH,KAAS9P,EAClB,OAAOsX,EAAOxH,KAASsG,EAGzBtG,GAAO,CACT,CAEA,OAAQ+H,GACN,IAAK,MACH,OAAI7X,EAAEgY,OAAS5B,EAAE4B,MAIVd,GAAmBlX,EAAEiY,UAAW7B,EAAE6B,UAAWZ,EAAO1C,OAAO,CAAC3U,IAAKsX,EAAO3C,OAAO,CAACyB,KAEzF,IAAK,MACH,OAAIpW,EAAEgY,OAAS5B,EAAE4B,MAIVd,GAAmBlX,EAAEkY,SAAU9B,EAAE8B,SAAUb,EAAO1C,OAAO,CAAC3U,IAAKsX,EAAO3C,OAAO,CAACyB,KAEvF,IAAK,YACL,IAAK,QACL,IAAK,SACL,IAAK,UACL,IAAK,SACL,IAAK,SACL,IAAK,OACL,IAAK,QACL,IAAK,SACL,IAAK,YACL,IAAK,aACL,IAAK,oBACL,IAAK,aACL,IAAK,cACL,IAAK,aACL,IAAK,cACL,IAAK,eACL,IAAK,eACL,IAAK,cACH,MAEF,QAEE,OAAO,EAGX,IAAI+B,EAAQ,EAAKnY,GAEjB,GAAImY,EAAMhX,SAAW,EAAKiV,GAAGjV,OAC3B,OAAO,EAGT,IAAIiX,EAAiBf,EAAO1C,OAAO,CAAC3U,IAChCqY,EAAiBf,EAAO3C,OAAO,CAACyB,IAGpC,IAFAtG,EAAMqI,EAAMhX,OAAS,EAEd2O,GAAO,GAAG,CACf,IAAIxO,EAAM6W,EAAMrI,GAEhB,IAAM,EAAKxO,EAAK8U,KAAMsB,GAAQtB,EAAE9U,GAAMtB,EAAEsB,GAAM8W,EAAgBC,GAC5D,OAAO,EAGTvI,GAAO,CACT,CAEA,OAAO,CACT,CErIA,IAMA,IAJA,QAAQ,SAAgB9P,EAAGoW,GACzB,OAAOsB,GAAQ1X,EAAGoW,EAAG,GAAI,GAC3B,IC/Be,SAASkC,GAASlH,EAAMpR,EAAG8P,GACxC,IAAIyI,EAAK3E,EAET,GAA4B,mBAAjBxC,EAAKtM,QACd,cAAe9E,GACb,IAAK,SACH,GAAU,IAANA,EAAS,CAIX,IAFAuY,EAAM,EAAIvY,EAEH8P,EAAMsB,EAAKjQ,QAAQ,CAGxB,GAAa,KAFbyS,EAAOxC,EAAKtB,KAEM,EAAI8D,IAAS2E,EAC7B,OAAOzI,EAGTA,GAAO,CACT,CAEA,OAAQ,CACV,CAAO,GAAI9P,GAAMA,EAAG,CAElB,KAAO8P,EAAMsB,EAAKjQ,QAAQ,CAGxB,GAAoB,iBAFpByS,EAAOxC,EAAKtB,KAEoB8D,GAASA,EACvC,OAAO9D,EAGTA,GAAO,CACT,CAEA,OAAQ,CACV,CAGA,OAAOsB,EAAKtM,QAAQ9E,EAAG8P,GAGzB,IAAK,SACL,IAAK,UACL,IAAK,WACL,IAAK,YACH,OAAOsB,EAAKtM,QAAQ9E,EAAG8P,GAEzB,IAAK,SACH,GAAU,OAAN9P,EAEF,OAAOoR,EAAKtM,QAAQ9E,EAAG8P,GAO/B,KAAOA,EAAMsB,EAAKjQ,QAAQ,CACxB,GAAI,GAAOiQ,EAAKtB,GAAM9P,GACpB,OAAO8P,EAGTA,GAAO,CACT,CAEA,OAAQ,CACV,CCjEe,SAAS0I,GAAUxY,EAAGoR,GACnC,OAAOkH,GAASlH,EAAMpR,EAAG,IAAM,CACjC,CCHe,SAASyY,GAAO3Y,GAG7B,MAAO,IAFOA,EAAE4Y,QAAQ,MAAO,QAAQA,QAAQ,QAAS,OACvDA,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OACzGA,QAAQ,KAAM,OAAS,GAC9C,CCDA,IAAIC,GAAM,SAAara,GACrB,OAAQA,EAAI,GAAK,IAAM,IAAMA,CAC/B,EAQA,GANyD,mBAA/BwF,KAAKnE,UAAUiZ,YAA6B,SAAsB7Z,GAC1F,OAAOA,EAAE6Z,aACX,EAAI,SAAsB7Z,GACxB,OAAOA,EAAE8Z,iBAAmB,IAAMF,GAAI5Z,EAAE+Z,cAAgB,GAAK,IAAMH,GAAI5Z,EAAEga,cAAgB,IAAMJ,GAAI5Z,EAAEia,eAAiB,IAAML,GAAI5Z,EAAEka,iBAAmB,IAAMN,GAAI5Z,EAAEma,iBAAmB,KAAOna,EAAEoa,qBAAuB,KAAMC,QAAQ,GAAG7H,MAAM,EAAG,GAAK,GACrP,ECXe,SAAS8H,GAAQjX,EAAIgP,GAKlC,IAJA,IAAItB,EAAM,EACN0C,EAAMpB,EAAKjQ,OACX2E,EAAS,GAENgK,EAAM0C,GACPpQ,EAAGgP,EAAKtB,MACVhK,EAAOA,EAAO3E,QAAUiQ,EAAKtB,IAG/BA,GAAO,EAGT,OAAOhK,CACT,CCXA,IAAIwT,GAEJ,WACE,SAASA,EAAQpY,EAAGgR,GAClB7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,CACX,CASA,OAPAoY,EAAQ3Z,UAAU,qBAAuB0S,EAAQJ,KACjDqH,EAAQ3Z,UAAU,uBAAyB0S,EAAQvM,OAEnDwT,EAAQ3Z,UAAU,qBAAuB,SAAUmG,EAAQwM,GACzD,OAAOjS,KAAKa,EAAEoR,GAASjS,KAAK6R,GAAG,qBAAqBpM,EAAQwM,GAASxM,CACvE,EAEOwT,CACT,CAdA,GAsBA,IAJA,QAAQ,SAAkBpY,EAAGgR,GAC3B,OAAO,IAAIoH,GAAQpY,EAAGgR,EACxB,ICyBA,IAbA,OAEAP,EAAc,CAAC,sBAAuB,UAAW,IAAU,SAAUwE,EAAMoD,GACzE,OCxCgCvH,EDwCfuH,ECvC4B,oBAAtCva,OAAOW,UAAU8P,SAAS7Q,KAAKoT,GDuCP,GAAQ,SAAUU,EAAKpR,GAKpD,OAJI6U,EAAKoD,EAAWjY,MAClBoR,EAAIpR,GAAOiY,EAAWjY,IAGjBoR,CACT,GAAG,CAAC,EAAG,EAAK6G,IACZF,GAAQlD,EAAMoD,GC/CD,IAAmBvH,CDgDlC,KEpBIwH,IAEJ,QAAQ,SAAgBrD,EAAMoD,GAC5B,OAAO,IC/B2BrY,ED+BRiV,EC9BnB,WACL,OAAQjV,EAAEX,MAAMF,KAAMC,UACxB,GD4BiCiZ,GC/BpB,IAAqBrY,CDgCpC,IAEA,ME5Be,SAAS,GAAU8Q,EAAGyH,GACnC,IAAIC,EAAQ,SAAeC,GACzB,IAAIC,EAAKH,EAAK9E,OAAO,CAAC3C,IACtB,OAAOwG,GAAUmB,EAAGC,GAAM,aAAe,GAAUD,EAAGC,EACxD,EAGIC,EAAW,SAAUnI,EAAKmC,GAC5B,OAAO,GAAK,SAAUiG,GACpB,OAAOrB,GAAOqB,GAAK,KAAOJ,EAAMhI,EAAIoI,GACtC,GAAGjG,EAAKtC,QAAQwI,OAClB,EAEA,OAAQ/a,OAAOW,UAAU8P,SAAS7Q,KAAKoT,IACrC,IAAK,qBACH,MAAO,qCAAuC,EAAK0H,EAAO1H,GAAG4E,KAAK,MAAQ,KAE5E,IAAK,iBACH,MAAO,IAAM,EAAK8C,EAAO1H,GAAG2C,OAAOkF,EAAS7H,EAAG,IAAO,SAAU8H,GAC9D,MAAO,QAAQE,KAAKF,EACtB,GAAG,EAAK9H,MAAM4E,KAAK,MAAQ,IAE7B,IAAK,mBACH,MAAoB,iBAAN5E,EAAiB,eAAiB0H,EAAM1H,EAAE0D,WAAa,IAAM1D,EAAEvC,WAE/E,IAAK,gBACH,MAAO,aAAewK,MAAMjI,EAAE0D,WAAagE,EAAMQ,KAAOzB,GAAO,GAAazG,KAAO,IAErF,IAAK,gBACH,MAAO,OAET,IAAK,kBACH,MAAoB,iBAANA,EAAiB,cAAgB0H,EAAM1H,EAAE0D,WAAa,IAAM,EAAI1D,IAAM,IAAY,KAAOA,EAAEvC,SAAS,IAEpH,IAAK,kBACH,MAAoB,iBAANuC,EAAiB,cAAgB0H,EAAM1H,EAAE0D,WAAa,IAAM+C,GAAOzG,GAEnF,IAAK,qBACH,MAAO,YAET,QACE,GAA0B,mBAAfA,EAAEvC,SAAyB,CACpC,IAAI0K,EAAOnI,EAAEvC,WAEb,GAAa,oBAAT0K,EACF,OAAOA,CAEX,CAEA,MAAO,IAAMN,EAAS7H,EAAG,EAAKA,IAAI4E,KAAK,MAAQ,IAErD,CClBA,IAMA,IAJA,QAAQ,SAAkBnF,GACxB,OAAO,GAAUA,EAAK,GACxB,ICqBA,IA5BA,QAAQ,SAAgBzR,EAAGoW,GACzB,GAAI,EAASpW,GAAI,CACf,GAAI,EAASoW,GACX,OAAOpW,EAAE2U,OAAOyB,GAGlB,MAAM,IAAI7U,UAAU,GAAS6U,GAAK,mBACpC,CAEA,GAAI,EAAUpW,GAAI,CAChB,GAAI,EAAUoW,GACZ,OAAOpW,EAAIoW,EAGb,MAAM,IAAI7U,UAAU,GAAS6U,GAAK,mBACpC,CAEA,GAAS,MAALpW,GAAa,GAAYA,EAAE,wBAC7B,OAAOA,EAAE,uBAAuBoW,GAGlC,GAAS,MAALpW,GAAa,GAAYA,EAAE2U,QAC7B,OAAO3U,EAAE2U,OAAOyB,GAGlB,MAAM,IAAI7U,UAAU,GAASvB,GAAK,kEACpC,ICrCA,IAJA,QAAQ,SAAaA,EAAGoW,GACtB,OAAOA,EAAIpW,EAAIoW,EAAIpW,CACrB,ICiBA,SAASoa,GAASxG,EAAMyG,EAAWC,GACjC,IACIC,EADAjQ,SAAcsJ,EAGlB,OAAQtJ,GACN,IAAK,SACL,IAAK,SAEH,OAAa,IAATsJ,GAAc,EAAIA,IAAS,MACzB0G,EAAIE,OAAO,QAGTH,IACFC,EAAIE,OAAO,OAAQ,IAGd,GAKY,OAAnBF,EAAIG,WACFJ,GACFE,EAAWD,EAAIG,WAAWzC,KAE1BsC,EAAIG,WAAWC,IAAI9G,GAET0G,EAAIG,WAAWzC,OACNuC,GAEZD,EAAIG,WAAWE,IAAI/G,GAGtBtJ,KAAQgQ,EAAIE,OAOP5G,KAAQ0G,EAAIE,OAAOlQ,KAGxB+P,IACFC,EAAIE,OAAOlQ,GAAMsJ,IAAQ,IAGpB,IAbHyG,IACFC,EAAIE,OAAOlQ,GAAQ,CAAC,EACpBgQ,EAAIE,OAAOlQ,GAAMsJ,IAAQ,IAGpB,GAYb,IAAK,UAGH,GAAItJ,KAAQgQ,EAAIE,OAAQ,CACtB,IAAII,EAAOhH,EAAO,EAAI,EAEtB,QAAI0G,EAAIE,OAAOlQ,GAAMsQ,KAGfP,IACFC,EAAIE,OAAOlQ,GAAMsQ,IAAQ,IAGpB,EAEX,CAKE,OAJIP,IACFC,EAAIE,OAAOlQ,GAAQsJ,EAAO,EAAC,GAAO,GAAQ,EAAC,GAAM,KAG5C,EAGX,IAAK,WAEH,OAAuB,OAAnB0G,EAAIG,WACFJ,GACFE,EAAWD,EAAIG,WAAWzC,KAE1BsC,EAAIG,WAAWC,IAAI9G,GAET0G,EAAIG,WAAWzC,OACNuC,GAEZD,EAAIG,WAAWE,IAAI/G,GAGtBtJ,KAAQgQ,EAAIE,SAQbhC,GAAU5E,EAAM0G,EAAIE,OAAOlQ,MAC1B+P,GACFC,EAAIE,OAAOlQ,GAAMyM,KAAKnD,IAGjB,IAZHyG,IACFC,EAAIE,OAAOlQ,GAAQ,CAACsJ,KAGf,GAcb,IAAK,YACH,QAAI0G,EAAIE,OAAOlQ,KAGT+P,IACFC,EAAIE,OAAOlQ,IAAQ,IAGd,GAGX,IAAK,SACH,GAAa,OAATsJ,EACF,QAAK0G,EAAIE,OAAa,OAChBH,IACFC,EAAIE,OAAa,MAAI,IAGhB,GAQb,QAKE,OAFAlQ,EAAOtL,OAAOW,UAAU8P,SAAS7Q,KAAKgV,MAExB0G,EAAIE,SASbhC,GAAU5E,EAAM0G,EAAIE,OAAOlQ,MAC1B+P,GACFC,EAAIE,OAAOlQ,GAAMyM,KAAKnD,IAGjB,IAbHyG,IACFC,EAAIE,OAAOlQ,GAAQ,CAACsJ,KAGf,GAcf,CAGA,OApMA,WACE,SAASiH,IAEPxa,KAAKoa,WAA4B,mBAARK,IAAqB,IAAIA,IAAQ,KAC1Dza,KAAKma,OAAS,CAAC,CACjB,CA4BA,OAtBAK,EAAKlb,UAAU+a,IAAM,SAAU9G,GAC7B,OAAQwG,GAASxG,GAAM,EAAMvT,KAC/B,EAMAwa,EAAKlb,UAAUgb,IAAM,SAAU/G,GAC7B,OAAOwG,GAASxG,GAAM,EAAOvT,KAC/B,EAYOwa,CACT,CAlCA,GCqBA,GADA,GAAK,GCpBDE,GAEJ,WACE,SAASA,EAAM7Z,EAAGgR,GAChB7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,EACTb,KAAK2a,OAAQ,CACf,CAqBA,OAnBAD,EAAMpb,UAAU,qBAAuB0S,EAAQJ,KAE/C8I,EAAMpb,UAAU,uBAAyB,SAAUmG,GAKjD,OAJKzF,KAAK2a,QACRlV,EAASzF,KAAK6R,GAAG,qBAAqBpM,OAAQ,IAGzCzF,KAAK6R,GAAG,uBAAuBpM,EACxC,EAEAiV,EAAMpb,UAAU,qBAAuB,SAAUmG,EAAQwM,GAMvD,OALIjS,KAAKa,EAAEoR,KACTjS,KAAK2a,OAAQ,EACblV,EAASiM,EAAS1R,KAAK6R,GAAG,qBAAqBpM,EAAQwM,KAGlDxM,CACT,EAEOiV,CACT,CA3BA,GAmCA,IAJA,QAAQ,SAAgB7Z,EAAGgR,GACzB,OAAO,IAAI6I,GAAM7Z,EAAGgR,EACtB,ICMA,IAfA,OAEAP,EAAc,CAAC,QAAS,IAAQ,SAAcvP,EAAIgP,GAIhD,IAHA,IAAItB,EAAM,EACN0C,EAAMpB,EAAKjQ,OAER2O,EAAM0C,GAAK,CAChB,GAAIpQ,EAAGgP,EAAKtB,IACV,OAAOsB,EAAKtB,GAGdA,GAAO,CACT,CACF,KCvCImL,GAEJ,WACE,SAASA,EAAW/Z,EAAGgR,GACrB7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,EACTb,KAAKyP,KAAO,EACZzP,KAAK2a,OAAQ,CACf,CAuBA,OArBAC,EAAWtb,UAAU,qBAAuB0S,EAAQJ,KAEpDgJ,EAAWtb,UAAU,uBAAyB,SAAUmG,GAKtD,OAJKzF,KAAK2a,QACRlV,EAASzF,KAAK6R,GAAG,qBAAqBpM,GAAS,IAG1CzF,KAAK6R,GAAG,uBAAuBpM,EACxC,EAEAmV,EAAWtb,UAAU,qBAAuB,SAAUmG,EAAQwM,GAQ5D,OAPAjS,KAAKyP,KAAO,EAERzP,KAAKa,EAAEoR,KACTjS,KAAK2a,OAAQ,EACblV,EAASiM,EAAS1R,KAAK6R,GAAG,qBAAqBpM,EAAQzF,KAAKyP,OAGvDhK,CACT,EAEOmV,CACT,CA9BA,GAsCA,IAJA,QAAQ,SAAqB/Z,EAAGgR,GAC9B,OAAO,IAAI+I,GAAW/Z,EAAGgR,EAC3B,ICGA,IAjBA,OAEAP,EAAc,GAAI,IAAa,SAAmBvP,EAAIgP,GAIpD,IAHA,IAAItB,EAAM,EACN0C,EAAMpB,EAAKjQ,OAER2O,EAAM0C,GAAK,CAChB,GAAIpQ,EAAGgP,EAAKtB,IACV,OAAOA,EAGTA,GAAO,CACT,CAEA,OAAQ,CACV,KCxCIoL,GAEJ,WACE,SAASA,EAAUha,EAAGgR,GACpB7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,CACX,CAgBA,OAdAga,EAAUvb,UAAU,qBAAuB0S,EAAQJ,KAEnDiJ,EAAUvb,UAAU,uBAAyB,SAAUmG,GACrD,OAAOzF,KAAK6R,GAAG,uBAAuB7R,KAAK6R,GAAG,qBAAqBpM,EAAQzF,KAAK8a,MAClF,EAEAD,EAAUvb,UAAU,qBAAuB,SAAUmG,EAAQwM,GAK3D,OAJIjS,KAAKa,EAAEoR,KACTjS,KAAK8a,KAAO7I,GAGPxM,CACT,EAEOoV,CACT,CArBA,GA6BA,IAJA,QAAQ,SAAoBha,EAAGgR,GAC7B,OAAO,IAAIgJ,GAAUha,EAAGgR,EAC1B,ICUA,IAdA,OAEAP,EAAc,GAAI,IAAY,SAAkBvP,EAAIgP,GAGlD,IAFA,IAAItB,EAAMsB,EAAKjQ,OAAS,EAEjB2O,GAAO,GAAG,CACf,GAAI1N,EAAGgP,EAAKtB,IACV,OAAOsB,EAAKtB,GAGdA,GAAO,CACT,CACF,KCaA,IAdA,OAEA,GAAgB,WAAW,SAAiB1N,EAAIgP,GAI9C,IAHA,IAAIoB,EAAMpB,EAAKjQ,OACX2O,EAAM,EAEHA,EAAM0C,GACXpQ,EAAGgP,EAAKtB,IACRA,GAAO,EAGT,OAAOsB,CACT,KCJA,IApBA,QAAQ,SAAiBgK,EAAO1J,GAC9B,GAAqB,IAAjB0J,EAAMja,QAAgB,GAAMuQ,GAC9B,OAAO,EAMT,IAHA,IAAID,EAAMC,EACN5B,EAAM,EAEHA,EAAMsL,EAAMja,QAAQ,CACzB,GAAK,GAAMsQ,KAAQ,EAAK2J,EAAMtL,GAAM2B,GAIlC,OAAO,EAHPA,EAAMA,EAAI2J,EAAMtL,IAChBA,GAAO,CAIX,CAEA,OAAO,CACT,ICZA,IAJA,QAAQ,SAAawD,EAAM5B,GACzB,OAAO,GAAQ,CAAC4B,GAAO5B,EACzB,ICLA,GADA,EAAI,GCGA2J,IAEJ,QAAQ,SAAgBC,EAAWC,EAAQC,GACzC,OAAO,EAAOC,KAAKC,IAAIJ,EAAUna,OAAQoa,EAAOpa,OAAQqa,EAAQra,SAAS,WACvE,OAAOma,EAAU/a,MAAMF,KAAMC,WAAaib,EAAOhb,MAAMF,KAAMC,WAAakb,EAAQjb,MAAMF,KAAMC,UAChG,GACF,IAEA,MCPA,IAFA,OAAQkY,ICER,IAJA,QAAQ,SAAiBmD,EAAQ/B,GAC/B,MAA6B,mBAAfA,EAAG9U,SAA2B,EAAS8U,GAA2BtB,GAASsB,EAAI+B,EAAQ,GAA1C/B,EAAG9U,QAAQ6W,EACxE,ICAA,IALA,QAAQ,SAAmB7L,EAAK8L,EAAMxK,GAEpC,OADAtB,EAAMA,EAAMsB,EAAKjQ,QAAU2O,GAAO,EAAIA,EAAMsB,EAAKjQ,OAC1C,GAAGwT,OAAOrD,MAAM3R,UAAU4R,MAAM3S,KAAKwS,EAAM,EAAGtB,GAAM8L,EAAMtK,MAAM3R,UAAU4R,MAAM3S,KAAKwS,EAAMtB,GACpG,ICFI+L,IAEJ,QAAQ,SAAczZ,GACpB,OAAO,EAAOA,EAAGjB,QAAQ,SAAUnB,EAAGoW,GACpC,IAAI/E,EAAOC,MAAM3R,UAAU4R,MAAM3S,KAAK0B,UAAW,GAGjD,OAFA+Q,EAAK,GAAK+E,EACV/E,EAAK,GAAKrR,EACHoC,EAAG7B,MAAMF,KAAMgR,EACxB,GACF,IAEA,MClCe,SAASyK,GAAU9J,GAChC,OAAOA,CACT,CCoBA,IAIA,IAFA,OAAQ8J,ICpBJC,GAEJ,WACE,SAASA,EAAQ7a,EAAGgR,GAClB7R,KAAK6R,GAAKA,EACV7R,KAAKa,EAAIA,EACTb,KAAKia,IAAM,IAAI,EACjB,CASA,OAPAyB,EAAQpc,UAAU,qBAAuB0S,EAAQJ,KACjD8J,EAAQpc,UAAU,uBAAyB0S,EAAQvM,OAEnDiW,EAAQpc,UAAU,qBAAuB,SAAUmG,EAAQwM,GACzD,OAAOjS,KAAKia,IAAII,IAAIra,KAAKa,EAAEoR,IAAUjS,KAAK6R,GAAG,qBAAqBpM,EAAQwM,GAASxM,CACrF,EAEOiW,CACT,CAfA,GAuBA,IAJA,QAAQ,SAAkB7a,EAAGgR,GAC3B,OAAO,IAAI6J,GAAQ7a,EAAGgR,EACxB,ICJA,ICIA,OAEAP,EAAc,GAAI,IAAU,SAAUvP,EAAIgP,GAMxC,IALA,IAGI4K,EAAapI,EAHb0G,EAAM,IAAI,GACVxU,EAAS,GACTgK,EAAM,EAGHA,EAAMsB,EAAKjQ,QAEhB6a,EAAc5Z,EADdwR,EAAOxC,EAAKtB,IAGRwK,EAAII,IAAIsB,IACVlW,EAAOiR,KAAKnD,GAGd9D,GAAO,EAGT,OAAOhK,CACT,IDzBA,CAAO,IEiBP,IAdA,QAAQ,SAAsBmW,EAAOC,GACnC,IAAIC,EAAYC,EAUhB,OARIH,EAAM9a,OAAS+a,EAAM/a,QACvBgb,EAAaF,EACbG,EAAeF,IAEfC,EAAaD,EACbE,EAAeH,GAGV,GAAK5C,GAAQ,GAAKb,GAAL,CAAgB2D,GAAaC,GACnD,ICTA,GAAwC,mBAAlBpd,OAAOqd,OAAwBrd,OAAOqd,OA1B5D,SAAuBV,GACrB,GAAc,MAAVA,EACF,MAAM,IAAIpa,UAAU,8CAOtB,IAJA,IAAI+a,EAAStd,OAAO2c,GAChB7L,EAAM,EACN3O,EAASb,UAAUa,OAEhB2O,EAAM3O,GAAQ,CACnB,IAAIyU,EAAStV,UAAUwP,GAEvB,GAAc,MAAV8F,EACF,IAAK,IAAI2G,KAAW3G,EACd,EAAK2G,EAAS3G,KAChB0G,EAAOC,GAAW3G,EAAO2G,IAK/BzM,GAAO,CACT,CAEA,OAAOwM,CACT,ECMA,IAJA,QAAQ,SAAYE,EAAM/K,GACxB,OAAOA,aAAe+K,GAAe,MAAP/K,IAAgBA,EAAItM,cAAgBqX,GAAsB,WAAdA,EAAKpS,MAAoC,iBAARqH,EAC7G,ICMA,IAXA,QAAQ,SAAgBC,GACtB,IAAI4B,EACAS,EAAK,GAET,IAAKT,KAAQ5B,EACXqC,EAAGA,EAAG5S,QAAUmS,EAGlB,OAAOS,CACT,ICIA,IAVA,QAAQ,SAAc0I,EAAQC,GAC5B,OAAO,SAAUC,GACf,OAAO,SAAUhB,GACf,OAAO,GAAI,SAAUiB,GACnB,OAAOF,EAAOE,EAAOjB,EACvB,GAAGgB,EAAYF,EAAOd,IACxB,CACF,CACF,ICQA,IApBA,QAAQ,SAAekB,EAAYnL,GACjC,OAAOmL,EAAWxY,KAAI,SAAUyY,GAK9B,IAJA,IAEIjd,EAFA4R,EAAMC,EACN5B,EAAM,EAGHA,EAAMgN,EAAM3b,QAAQ,CACzB,GAAW,MAAPsQ,EACF,OAGF5R,EAAIid,EAAMhN,GACV2B,EAAM,EAAW5R,GAAK,EAAIA,EAAG4R,GAAOA,EAAI5R,GACxCiQ,GAAO,CACT,CAEA,OAAO2B,CACT,GACF,ICbA,IAJA,QAAQ,SAAcsL,EAAQrL,GAC5B,OAAO,GAAM,CAACqL,GAASrL,GAAK,EAC9B,ICQA,IAJA,QAAQ,SAAkB7R,GACxB,OAAO,GAAK,GAAKA,GAAI,GAAUA,GACjC,ICZA,GADA,GCCA,QAAQ,SAAaG,EAAGoW,GACtB,OAAOnC,OAAOjU,GAAKiU,OAAOmC,EAC5B,IDHY,GEQZ,IAJA,QAAQ,SAAkBhF,GACxB,OAAO,SAAoB,KAAM,CAAC,CAAC,GAAGuD,OAAOvD,GAC/C,ICMA,IAJA,QAAQ,SAAoBzS,EAAGH,GAC7B,OAAO,GAAc,CAAC,EAAGG,EAAGH,EAC9B,ICLA,IAJA,QAAQ,SAAawB,EAAGoW,GACtB,OAAOA,EAAIpW,EAAIoW,EAAIpW,CACrB,ICiBA,IApBA,QAAQ,SAAcgd,EAAOtL,GAM3B,IALA,IAAI5L,EAAS,CAAC,EACVwJ,EAAQ,CAAC,EACTQ,EAAM,EACN0C,EAAMwK,EAAM7b,OAET2O,EAAM0C,GACXlD,EAAM0N,EAAMlN,IAAQ,EACpBA,GAAO,EAGT,IAAK,IAAIwD,KAAQ5B,EACVpC,EAAM1P,eAAe0T,KACxBxN,EAAOwN,GAAQ5B,EAAI4B,IAIvB,OAAOxN,CACT,IChBImX,IAEJ,QAAQ,SAAc7a,GACpB,IACI0D,EADAoX,GAAS,EAEb,OAAO,EAAO9a,EAAGjB,QAAQ,WACvB,OAAI+b,EACKpX,GAGToX,GAAS,EACTpX,EAAS1D,EAAG7B,MAAMF,KAAMC,WAE1B,GACF,IAEA,MCDA,IAfA,QAAQ,SAAc0c,EAAOtL,GAI3B,IAHA,IAAI5L,EAAS,CAAC,EACVgK,EAAM,EAEHA,EAAMkN,EAAM7b,QACb6b,EAAMlN,KAAQ4B,IAChB5L,EAAOkX,EAAMlN,IAAQ4B,EAAIsL,EAAMlN,KAGjCA,GAAO,EAGT,OAAOhK,CACT,ICHA,IANA,QAAQ,SAAeqX,EAAIzL,GACzB,OAAOyL,EAAG9Y,KAAI,SAAUxE,GACtB,OAAO,GAAK,CAACA,GAAI6R,EACnB,GACF,IC9Be,SAAS0L,GAAUpL,GAChC,MAA6C,oBAAtChT,OAAOW,UAAU8P,SAAS7Q,KAAKoT,EACxC,CCiBA,IAkBA,IAhBA,QAAQ,SAAeqL,EAAMC,GAC3B,IAAMF,GAAUC,KAASD,GAAUE,GACjC,MAAM,IAAI/b,UAAU,2CAMtB,IAHA,IAAIuE,EAAS,GACTxH,EAAI+e,EAED/e,EAAIgf,GACTxX,EAAOiR,KAAKzY,GACZA,GAAK,EAGP,OAAOwH,CACT,ICOA,GANA6K,EAAQ,EAAG,IAAI,SAAsBwF,EAAM/T,EAAIpC,EAAGoR,GAChD,OAAO,GAAQ,SAAUsB,EAAKV,GAC5B,OAAOmE,EAAKzD,EAAKV,GAAK5P,EAAGsQ,EAAKV,GAAKD,EAASW,EAC9C,GAAG1S,EAAGoR,EACR,I,WCXA,IANA,QAAQ,SAAgBK,GACtB,OAAO,WACL,OAAOA,CACT,CACF,ICmBA,IAnBA,QAAQ,SAAerP,EAAI9D,GACzB,IAEI8S,EAFAoB,EAAMyB,OAAO3V,GACbwR,EAAM,EAGV,GAAI0C,EAAM,GAAKyH,MAAMzH,GACnB,MAAM,IAAI+K,WAAW,mCAKvB,IAFAnM,EAAO,IAAIE,MAAMkB,GAEV1C,EAAM0C,GACXpB,EAAKtB,GAAO1N,EAAG0N,GACfA,GAAO,EAGT,OAAOsB,CACT,ICXA,IAJA,QAAQ,SAAgB9R,EAAOhB,GAC7B,OAAO,GAAM,GAAOgB,GAAQhB,EAC9B,IC5BIkf,GAAW,SAAUxL,GACvB,MAAO,CACL1S,MAAO0S,EACP3N,IAAK,SAAUnD,GACb,OAAOsc,GAAStc,EAAE8Q,GACpB,EAEJ,EAoCA,IATA,QAAQ,SAAcyL,EAAMvc,EAAG8Q,GAI7B,OAAOyL,GAAK,SAAU9D,GACpB,OAAO6D,GAAStc,EAAEyY,GACpB,GAFO8D,CAEJzL,GAAG1S,KACR,ICZA,IAJA,QAAQ,SAAame,EAAMC,EAAG1L,GAC5B,OAAO,GAAKyL,EAAM,GAAOC,GAAI1L,EAC/B,ICqBA,IAdA,QAAQ,SAAkB2L,EAAKvM,GAC7B,OAAOE,MAAM3R,UAAU4R,MAAM3S,KAAKwS,EAAM,GAAG2I,MAAK,SAAU/Z,EAAGoW,GAI3D,IAHA,IAAItQ,EAAS,EACTpH,EAAI,EAEU,IAAXoH,GAAgBpH,EAAIif,EAAIxc,QAC7B2E,EAAS6X,EAAIjf,GAAGsB,EAAGoW,GACnB1X,GAAK,EAGP,OAAOoH,CACT,GACF,ICdA,IAZA,QAAQ,SAAiB4L,GACvB,IAAIkM,EAAQ,GAEZ,IAAK,IAAItK,KAAQ5B,EACX,EAAK4B,EAAM5B,KACbkM,EAAMA,EAAMzc,QAAU,CAACmS,EAAM5B,EAAI4B,KAIrC,OAAOsK,CACT,ICkBA,IAvBA,QAAQ,SAAmBC,GAIzB,IAHA,IAAInf,EAAI,EACJoH,EAAS,GAENpH,EAAImf,EAAU1c,QAAQ,CAI3B,IAHA,IAAI2c,EAAYD,EAAUnf,GACtBsW,EAAI,EAEDA,EAAI8I,EAAU3c,aACM,IAAd2E,EAAOkP,KAChBlP,EAAOkP,GAAK,IAGdlP,EAAOkP,GAAG+B,KAAK+G,EAAU9I,IACzBA,GAAK,EAGPtW,GAAK,CACP,CAEA,OAAOoH,CACT,ICrBA,ICzB0BuJ,OAAO1P,UAAU4E,MDqB3C,OAEAsS,GAAQ,GAAM,KEHd,GADA,GAAMiF,ICQN,IAJA,QAAQ,SAAUlC,EAAIxI,GACpB,OAAO,GAAO,GAAKoH,GAAL,CAAgBoB,GAAKxI,EACrC,ICgBA,IAtBA,QAAQ,SAAepR,EAAGoW,GAQxB,IANA,IAEIpB,EAFAlF,EAAM,EACNmF,EAAOjV,EAAEmB,OAET4T,EAAOqB,EAAEjV,OACT2E,EAAS,GAENgK,EAAMmF,GAAM,CAGjB,IAFAD,EAAI,EAEGA,EAAID,GACTjP,EAAOA,EAAO3E,QAAU,CAACnB,EAAE8P,GAAMsG,EAAEpB,IACnCA,GAAK,EAGPlF,GAAO,CACT,CAEA,OAAOhK,CACT,ICNA,IAbA,QAAQ,SAAa9F,EAAGoW,GAKtB,IAJA,IAAI2H,EAAK,GACLjO,EAAM,EACN0C,EAAMiJ,KAAKuC,IAAIhe,EAAEmB,OAAQiV,EAAEjV,QAExB2O,EAAM0C,GACXuL,EAAGjO,GAAO,CAAC9P,EAAE8P,GAAMsG,EAAEtG,IACrBA,GAAO,EAGT,OAAOiO,CACT,ICOA,IAbA,QAAQ,SAAiB3b,EAAIpC,EAAGoW,GAK9B,IAJA,IAAI2H,EAAK,GACLjO,EAAM,EACN0C,EAAMiJ,KAAKuC,IAAIhe,EAAEmB,OAAQiV,EAAEjV,QAExB2O,EAAM0C,GACXuL,EAAGjO,GAAO1N,EAAGpC,EAAE8P,GAAMsG,EAAEtG,IACvBA,GAAO,EAGT,OAAOiO,CACT,G,kFC7Be,SAASE,EAAQ7b,GAC9B,OAAO,SAAS8b,EAAGle,GACjB,OAAyB,IAArBM,UAAUa,SAAgB,OAAenB,GACpCke,EAEA9b,EAAG7B,MAAMF,KAAMC,UAE1B,CACF,C,4FCPe,SAAS6d,EAAQ/b,GAC9B,OAAO,SAASgc,EAAGpe,EAAGoW,GACpB,OAAQ9V,UAAUa,QAChB,KAAK,EACH,OAAOid,EAET,KAAK,EACH,OAAO,OAAepe,GAAKoe,GAAK,QAAQ,SAAU3G,GAChD,OAAOrV,EAAGpC,EAAGyX,EACf,IAEF,QACE,OAAO,OAAezX,KAAM,OAAeoW,GAAKgI,GAAK,OAAepe,IAAK,QAAQ,SAAUwX,GACzF,OAAOpV,EAAGoV,EAAIpB,EAChB,KAAK,OAAeA,IAAK,QAAQ,SAAUqB,GACzC,OAAOrV,EAAGpC,EAAGyX,EACf,IAAKrV,EAAGpC,EAAGoW,GAEjB,CACF,C,sGClBe,SAASiI,EAAQjc,GAC9B,OAAO,SAASkc,EAAGte,EAAGoW,EAAGtX,GACvB,OAAQwB,UAAUa,QAChB,KAAK,EACH,OAAOmd,EAET,KAAK,EACH,OAAO,OAAete,GAAKse,GAAK,QAAQ,SAAU7G,EAAI8G,GACpD,OAAOnc,EAAGpC,EAAGyX,EAAI8G,EACnB,IAEF,KAAK,EACH,OAAO,OAAeve,KAAM,OAAeoW,GAAKkI,GAAK,OAAete,IAAK,QAAQ,SAAUwX,EAAI+G,GAC7F,OAAOnc,EAAGoV,EAAIpB,EAAGmI,EACnB,KAAK,OAAenI,IAAK,QAAQ,SAAUqB,EAAI8G,GAC7C,OAAOnc,EAAGpC,EAAGyX,EAAI8G,EACnB,KAAK,QAAQ,SAAUA,GACrB,OAAOnc,EAAGpC,EAAGoW,EAAGmI,EAClB,IAEF,QACE,OAAO,OAAeve,KAAM,OAAeoW,KAAM,OAAetX,GAAKwf,GAAK,OAAete,KAAM,OAAeoW,IAAK,QAAQ,SAAUoB,EAAIC,GACvI,OAAOrV,EAAGoV,EAAIC,EAAI3Y,EACpB,KAAK,OAAekB,KAAM,OAAelB,IAAK,QAAQ,SAAU0Y,EAAI+G,GAClE,OAAOnc,EAAGoV,EAAIpB,EAAGmI,EACnB,KAAK,OAAenI,KAAM,OAAetX,IAAK,QAAQ,SAAU2Y,EAAI8G,GAClE,OAAOnc,EAAGpC,EAAGyX,EAAI8G,EACnB,KAAK,OAAeve,IAAK,QAAQ,SAAUwX,GACzC,OAAOpV,EAAGoV,EAAIpB,EAAGtX,EACnB,KAAK,OAAesX,IAAK,QAAQ,SAAUqB,GACzC,OAAOrV,EAAGpC,EAAGyX,EAAI3Y,EACnB,KAAK,OAAeA,IAAK,QAAQ,SAAUyf,GACzC,OAAOnc,EAAGpC,EAAGoW,EAAGmI,EAClB,IAAKnc,EAAGpC,EAAGoW,EAAGtX,GAEpB,CACF,C,oCChDe,SAASmS,EAAejR,GACrC,OAAY,MAALA,GAA0B,iBAANA,IAAoD,IAAlCA,EAAE,2BACjD,C,uECoBIwe,GAEJ,E,QAAA,IAAQ,SAAgBC,EAAOC,EAAOtN,GACpC,IAAItL,EAASwL,MAAM3R,UAAU4R,MAAM3S,KAAKwS,EAAM,GAE9C,OADAtL,EAAO6Y,OAAOF,EAAOC,GACd5Y,CACT,IAEA,K,GC7BI8Y,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB5J,IAAjB6J,EACH,OAAOA,EAAa1gB,QAGrB,IAAID,EAASwgB,EAAyBE,GAAY,CACjDrb,GAAIqb,EAEJzgB,QAAS,CAAC,GAOX,OAHA2gB,EAAoBF,GAAUlgB,KAAKR,EAAOC,QAASD,EAAQA,EAAOC,QAASwgB,GAGpEzgB,EAAOC,OACf,CAGAwgB,EAAoBhgB,EAAImgB,ECxBxBH,EAAoBvgB,EAAI,SAASF,GAChC,IAAIqe,EAASre,GAAUA,EAAOmB,WAC7B,WAAa,OAAOnB,EAAgB,OAAG,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAygB,EAAoB9f,EAAE0d,EAAQ,CAAEzc,EAAGyc,IAC5BA,CACR,EvJPI1e,EAAWiB,OAAOyW,eAAiB,SAAS/D,GAAO,OAAO1S,OAAOyW,eAAe/D,EAAM,EAAI,SAASA,GAAO,OAAOA,EAAIuN,SAAW,EAQpIJ,EAAoBtgB,EAAI,SAASe,EAAO4f,GAEvC,GADU,EAAPA,IAAU5f,EAAQe,KAAKf,IAChB,EAAP4f,EAAU,OAAO5f,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP4f,GAAa5f,EAAMC,WAAY,OAAOD,EAC1C,GAAW,GAAP4f,GAAoC,mBAAf5f,EAAMc,KAAqB,OAAOd,CAC5D,CACA,IAAI6f,EAAKngB,OAAOQ,OAAO,MACvBqf,EAAoBrgB,EAAE2gB,GACtB,IAAIC,EAAM,CAAC,EACXthB,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAIshB,EAAiB,EAAPH,GAAY5f,EAAyB,iBAAX+f,KAAyBvhB,EAAegH,QAAQua,GAAUA,EAAUthB,EAASshB,GACxHrgB,OAAOsgB,oBAAoBD,GAASE,SAAQ,SAASje,GAAO8d,EAAI9d,GAAO,WAAa,OAAOhC,EAAMgC,EAAM,CAAG,IAI3G,OAFA8d,EAAa,QAAI,WAAa,OAAO9f,CAAO,EAC5Cuf,EAAoB9f,EAAEogB,EAAIC,GACnBD,CACR,EwJxBAN,EAAoB9f,EAAI,SAASV,EAASmhB,GACzC,IAAI,IAAIle,KAAOke,EACXX,EAAoBpgB,EAAE+gB,EAAYle,KAASud,EAAoBpgB,EAAEJ,EAASiD,IAC5EtC,OAAOC,eAAeZ,EAASiD,EAAK,CAAEpC,YAAY,EAAMC,IAAKqgB,EAAWle,IAG3E,ECPAud,EAAoB3d,EAAI,CAAC,EAGzB2d,EAAoB3gB,EAAI,SAASuhB,GAChC,OAAOvf,QAAQkS,IAAIpT,OAAO6U,KAAKgL,EAAoB3d,GAAGkS,QAAO,SAASsM,EAAUpe,GAE/E,OADAud,EAAoB3d,EAAEI,GAAKme,EAASC,GAC7BA,CACR,GAAG,IACJ,ECPAb,EAAoB9e,EAAI,SAAS0f,GAEhC,MAAY,CAAC,IAAM,cAAc,IAAM,eAAe,IAAM,mBAAmBA,GAAW,KAC3F,ECJAZ,EAAoB7P,EAAI,WACvB,GAA0B,iBAAf2Q,WAAyB,OAAOA,WAC3C,IACC,OAAOtf,MAAQ,IAAIuf,SAAS,cAAb,EAGhB,CAFE,MAAO1hB,GACR,GAAsB,iBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB0gB,EAAoBpgB,EAAI,SAASiT,EAAK4B,GAAQ,OAAOtU,OAAOW,UAAUC,eAAehB,KAAK8S,EAAK4B,EAAO,E3JAlGtV,EAAa,CAAC,EACdC,EAAoB,cAExB4gB,EAAoBlgB,EAAI,SAASkhB,EAAK5f,EAAMqB,EAAKme,GAChD,GAAGzhB,EAAW6hB,GAAQ7hB,EAAW6hB,GAAK9I,KAAK9W,OAA3C,CACA,IAAI6f,EAAQC,EACZ,QAAW7K,IAAR5T,EAEF,IADA,IAAI0e,EAAU/b,SAASgc,qBAAqB,UACpCvhB,EAAI,EAAGA,EAAIshB,EAAQ7e,OAAQzC,IAAK,CACvC,IAAIoB,EAAIkgB,EAAQthB,GAChB,GAAGoB,EAAEogB,aAAa,QAAUL,GAAO/f,EAAEogB,aAAa,iBAAmBjiB,EAAoBqD,EAAK,CAAEwe,EAAShgB,EAAG,KAAO,CACpH,CAEGggB,IACHC,GAAa,GACbD,EAAS7b,SAASkc,cAAc,WAEzBC,QAAU,QACjBN,EAAOO,QAAU,IACbxB,EAAoByB,IACvBR,EAAOS,aAAa,QAAS1B,EAAoByB,IAElDR,EAAOS,aAAa,eAAgBtiB,EAAoBqD,GACxDwe,EAAOU,IAAMX,GAEd7hB,EAAW6hB,GAAO,CAAC5f,GACnB,IAAIwgB,EAAmB,SAAS3f,EAAM4f,GAErCZ,EAAOa,QAAUb,EAAOc,OAAS,KACjCC,aAAaR,GACb,IAAIS,EAAU9iB,EAAW6hB,GAIzB,UAHO7hB,EAAW6hB,GAClBC,EAAOiB,YAAcjB,EAAOiB,WAAWC,YAAYlB,GACnDgB,GAAWA,EAAQvB,SAAQ,SAASnd,GAAM,OAAOA,EAAGse,EAAQ,IACzD5f,EAAM,OAAOA,EAAK4f,EACtB,EAEIL,EAAU3f,WAAW+f,EAAiBhhB,KAAK,UAAMyV,EAAW,CAAE5K,KAAM,UAAWqR,OAAQmE,IAAW,MACtGA,EAAOa,QAAUF,EAAiBhhB,KAAK,KAAMqgB,EAAOa,SACpDb,EAAOc,OAASH,EAAiBhhB,KAAK,KAAMqgB,EAAOc,QACnDb,GAAc9b,SAASgd,KAAKC,YAAYpB,EApCkB,CAqC3D,E4JxCAjB,EAAoBrgB,EAAI,SAASH,GACX,oBAAXe,QAA0BA,OAAOC,aAC1CL,OAAOC,eAAeZ,EAASe,OAAOC,YAAa,CAAEC,MAAO,WAE7DN,OAAOC,eAAeZ,EAAS,aAAc,CAAEiB,OAAO,GACvD,E,WCNA,IAAI6hB,EACAtC,EAAoB7P,EAAEoS,gBAAeD,EAAYtC,EAAoB7P,EAAE5J,SAAW,IACtF,IAAInB,EAAW4a,EAAoB7P,EAAE/K,SACrC,IAAKkd,GAAald,IACbA,EAASod,gBACZF,EAAYld,EAASod,cAAcb,MAC/BW,GAAW,CACf,IAAInB,EAAU/b,EAASgc,qBAAqB,UACzCD,EAAQ7e,SAAQggB,EAAYnB,EAAQA,EAAQ7e,OAAS,GAAGqf,IAC5D,CAID,IAAKW,EAAW,MAAM,IAAI1e,MAAM,yDAChC0e,EAAYA,EAAUzI,QAAQ,OAAQ,IAAIA,QAAQ,QAAS,IAAIA,QAAQ,YAAa,KACpFmG,EAAoBhf,EAAIshB,C,ICfxB,IA4BYtB,EA5BRyB,EAAmB,WACnB,IAAIxB,EAAS7b,SAASod,cACtB,IAAKvB,EAAQ,CAOT,IAHA,IAAIyB,EAActd,SAASgc,qBAAqB,UAC5CD,EAAU,GAELthB,EAAI,EAAGA,EAAI6iB,EAAYpgB,OAAQzC,IACpCshB,EAAQjJ,KAAKwK,EAAY7iB,IAI7BohB,GADAE,EAAUA,EAAQwB,QAAO,SAAS1hB,GAAK,OAAQA,EAAE2hB,QAAU3hB,EAAE4hB,OAAS5hB,EAAE6hB,WAAa,KACpEpQ,OAAO,GAAG,EAC/B,CAEA,OAAOuO,CACX,EAkBA,GAZA9gB,OAAOC,eAAe4f,EAAqB,IAAK,CAC5C1f,KAGQ0gB,EAFSyB,IAEId,IAAIpc,MAAM,KAAKmN,MAAM,GAAI,GAAGqF,KAAK,KAAO,IAElD,WACH,OAAOiJ,CACX,KAIsB,oBAAnB+B,eAAgC,CACvC,IAAIC,EAAqBD,eACzBA,eAAiB,SAASnC,GACtB,IAnBqBK,EAoBjBgC,GApBiBhC,EAmBRwB,IAlBV,6BAA6BtH,KAAK8F,EAAOU,MAqBxCA,EAAMqB,EAAmBpC,GAE7B,IAAIqC,EACA,OAAOtB,EAGX,IAAIuB,EAAevB,EAAIpc,MAAM,KACzB4d,EAAgBD,EAAaxQ,OAAO,GAAG,GAAGnN,MAAM,KAKpD,OAHA4d,EAAcrD,OAAO,EAAG,EAAG,qBAC3BoD,EAAapD,QAAQ,EAAG,EAAGqD,EAAcpL,KAAK,MAEvCmL,EAAanL,KAAK,IAC7B,CACJ,E,WCnDA,IAAIqL,EAAkB,CACrB,IAAK,GAGNpD,EAAoB3d,EAAE8T,EAAI,SAASyK,EAASC,GAE1C,IAAIwC,EAAqBrD,EAAoBpgB,EAAEwjB,EAAiBxC,GAAWwC,EAAgBxC,QAAWvK,EACtG,GAA0B,IAAvBgN,EAGF,GAAGA,EACFxC,EAAS3I,KAAKmL,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIjiB,SAAQ,SAASC,EAASqZ,GAAU0I,EAAqBD,EAAgBxC,GAAW,CAACtf,EAASqZ,EAAS,IACzHkG,EAAS3I,KAAKmL,EAAmB,GAAKC,GAGtC,IAAItC,EAAMhB,EAAoBhf,EAAIgf,EAAoB9e,EAAE0f,GAEpDjd,EAAQ,IAAIC,MAgBhBoc,EAAoBlgB,EAAEkhB,GAfH,SAASa,GAC3B,GAAG7B,EAAoBpgB,EAAEwjB,EAAiBxC,KAEf,KAD1ByC,EAAqBD,EAAgBxC,MACRwC,EAAgBxC,QAAWvK,GACrDgN,GAAoB,CACtB,IAAIE,EAAY1B,IAAyB,SAAfA,EAAMpW,KAAkB,UAAYoW,EAAMpW,MAChE+X,EAAU3B,GAASA,EAAM/E,QAAU+E,EAAM/E,OAAO6E,IACpDhe,EAAMuV,QAAU,iBAAmB0H,EAAU,cAAgB2C,EAAY,KAAOC,EAAU,IAC1F7f,EAAM4H,KAAO,iBACb5H,EAAM8H,KAAO8X,EACb5f,EAAM8f,QAAUD,EAChBH,EAAmB,GAAG1f,EACvB,CAEF,GACyC,SAAWid,EAASA,EAE/D,CAEH,EAaA,IAAI8C,EAAuB,SAASC,EAA4Bra,GAC/D,IAKI2W,EAAUW,EALVgD,EAAWta,EAAK,GAChBua,EAAcva,EAAK,GACnBwa,EAAUxa,EAAK,GAGIzJ,EAAI,EAC3B,GAAG+jB,EAASG,MAAK,SAASnf,GAAM,OAA+B,IAAxBwe,EAAgBxe,EAAW,IAAI,CACrE,IAAIqb,KAAY4D,EACZ7D,EAAoBpgB,EAAEikB,EAAa5D,KACrCD,EAAoBhgB,EAAEigB,GAAY4D,EAAY5D,IAG7C6D,GAAsBA,EAAQ9D,EAClC,CAEA,IADG2D,GAA4BA,EAA2Bra,GACrDzJ,EAAI+jB,EAASthB,OAAQzC,IACzB+gB,EAAUgD,EAAS/jB,GAChBmgB,EAAoBpgB,EAAEwjB,EAAiBxC,IAAYwC,EAAgBxC,IACrEwC,EAAgBxC,GAAS,KAE1BwC,EAAgBxC,GAAW,CAG7B,EAEIoD,EAAqBC,KAA6B,uBAAIA,KAA6B,wBAAK,GAC5FD,EAAmBtD,QAAQgD,EAAqB9iB,KAAK,KAAM,IAC3DojB,EAAmB9L,KAAOwL,EAAqB9iB,KAAK,KAAMojB,EAAmB9L,KAAKtX,KAAKojB,G,ICvFvFhE,EAAoByB,QAAKpL,E,8HCIzB6N,EAAAA,GAAAA,cAAqB9d,EAAAA,EAAAA,YACrB8d,EAAAA,GAAAA,YAAmB9d,EAAAA,EAAAA,S", "sources": ["webpack://dash_table/webpack/runtime/create fake namespace object", "webpack://dash_table/webpack/runtime/load script", "webpack://dash_table/./node_modules/@plotly/dash-component-plugins/dist/index.js", "webpack://dash_table/./src/core/Logger/DebugLevel.ts", "webpack://dash_table/./src/core/Logger/LogLevel.ts", "webpack://dash_table/./src/core/Logger/index.ts", "webpack://dash_table/./src/core/storage/Cookie.ts", "webpack://dash_table/./src/core/environment/index.ts", "webpack://dash_table/./src/dash-table/LazyLoader.ts", "webpack://dash_table/./src/dash-table/dash/DataTable.js", "webpack://dash_table/./node_modules/css.escape/css.escape.js", "webpack://dash_table/external window \"PropTypes\"", "webpack://dash_table/external window \"React\"", "webpack://dash_table/external window \"ReactDOM\"", "webpack://dash_table/./node_modules/ramda/es/internal/_concat.js", "webpack://dash_table/./node_modules/ramda/es/internal/_arity.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curryN.js", "webpack://dash_table/./node_modules/ramda/es/curryN.js", "webpack://dash_table/./node_modules/ramda/es/addIndex.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isArray.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isTransformer.js", "webpack://dash_table/./node_modules/ramda/es/internal/_dispatchable.js", "webpack://dash_table/./node_modules/ramda/es/internal/_reduced.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xfBase.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xall.js", "webpack://dash_table/./node_modules/ramda/es/all.js", "webpack://dash_table/./node_modules/ramda/es/internal/_map.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isString.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isArrayLike.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xwrap.js", "webpack://dash_table/./node_modules/ramda/es/bind.js", "webpack://dash_table/./node_modules/ramda/es/internal/_reduce.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xmap.js", "webpack://dash_table/./node_modules/ramda/es/internal/_has.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isArguments.js", "webpack://dash_table/./node_modules/ramda/es/keys.js", "webpack://dash_table/./node_modules/ramda/es/map.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isInteger.js", "webpack://dash_table/./node_modules/ramda/es/nth.js", "webpack://dash_table/./node_modules/ramda/es/prop.js", "webpack://dash_table/./node_modules/ramda/es/pluck.js", "webpack://dash_table/./node_modules/ramda/es/reduce.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xany.js", "webpack://dash_table/./node_modules/ramda/es/any.js", "webpack://dash_table/./node_modules/ramda/es/append.js", "webpack://dash_table/./node_modules/ramda/es/values.js", "webpack://dash_table/./node_modules/ramda/es/isNil.js", "webpack://dash_table/./node_modules/ramda/es/assocPath.js", "webpack://dash_table/./node_modules/ramda/es/internal/_assoc.js", "webpack://dash_table/./node_modules/ramda/es/assoc.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xchain.js", "webpack://dash_table/./node_modules/ramda/es/internal/_flatCat.js", "webpack://dash_table/./node_modules/ramda/es/internal/_forceReduced.js", "webpack://dash_table/./node_modules/ramda/es/chain.js", "webpack://dash_table/./node_modules/ramda/es/internal/_makeFlat.js", "webpack://dash_table/./node_modules/ramda/es/type.js", "webpack://dash_table/./node_modules/ramda/es/internal/_clone.js", "webpack://dash_table/./node_modules/ramda/es/internal/_cloneRegExp.js", "webpack://dash_table/./node_modules/ramda/es/clone.js", "webpack://dash_table/./node_modules/ramda/es/comparator.js", "webpack://dash_table/./node_modules/ramda/es/internal/_pipe.js", "webpack://dash_table/./node_modules/ramda/es/internal/_checkForMethod.js", "webpack://dash_table/./node_modules/ramda/es/slice.js", "webpack://dash_table/./node_modules/ramda/es/tail.js", "webpack://dash_table/./node_modules/ramda/es/pipe.js", "webpack://dash_table/./node_modules/ramda/es/reverse.js", "webpack://dash_table/./node_modules/ramda/es/compose.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isFunction.js", "webpack://dash_table/./node_modules/ramda/es/internal/_arrayFromIterator.js", "webpack://dash_table/./node_modules/ramda/es/internal/_includesWith.js", "webpack://dash_table/./node_modules/ramda/es/internal/_objectIs.js", "webpack://dash_table/./node_modules/ramda/es/internal/_equals.js", "webpack://dash_table/./node_modules/ramda/es/internal/_functionName.js", "webpack://dash_table/./node_modules/ramda/es/equals.js", "webpack://dash_table/./node_modules/ramda/es/internal/_indexOf.js", "webpack://dash_table/./node_modules/ramda/es/internal/_includes.js", "webpack://dash_table/./node_modules/ramda/es/internal/_quote.js", "webpack://dash_table/./node_modules/ramda/es/internal/_toISOString.js", "webpack://dash_table/./node_modules/ramda/es/internal/_filter.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xfilter.js", "webpack://dash_table/./node_modules/ramda/es/filter.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isObject.js", "webpack://dash_table/./node_modules/ramda/es/reject.js", "webpack://dash_table/./node_modules/ramda/es/internal/_complement.js", "webpack://dash_table/./node_modules/ramda/es/internal/_toString.js", "webpack://dash_table/./node_modules/ramda/es/toString.js", "webpack://dash_table/./node_modules/ramda/es/concat.js", "webpack://dash_table/./node_modules/ramda/es/max.js", "webpack://dash_table/./node_modules/ramda/es/internal/_Set.js", "webpack://dash_table/./node_modules/ramda/es/last.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xfind.js", "webpack://dash_table/./node_modules/ramda/es/find.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xfindIndex.js", "webpack://dash_table/./node_modules/ramda/es/findIndex.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xfindLast.js", "webpack://dash_table/./node_modules/ramda/es/findLast.js", "webpack://dash_table/./node_modules/ramda/es/forEach.js", "webpack://dash_table/./node_modules/ramda/es/hasPath.js", "webpack://dash_table/./node_modules/ramda/es/has.js", "webpack://dash_table/./node_modules/ramda/es/head.js", "webpack://dash_table/./node_modules/ramda/es/ifElse.js", "webpack://dash_table/./node_modules/ramda/es/includes.js", "webpack://dash_table/./node_modules/ramda/es/indexOf.js", "webpack://dash_table/./node_modules/ramda/es/insertAll.js", "webpack://dash_table/./node_modules/ramda/es/flip.js", "webpack://dash_table/./node_modules/ramda/es/internal/_identity.js", "webpack://dash_table/./node_modules/ramda/es/identity.js", "webpack://dash_table/./node_modules/ramda/es/internal/_xuniqBy.js", "webpack://dash_table/./node_modules/ramda/es/uniq.js", "webpack://dash_table/./node_modules/ramda/es/uniqBy.js", "webpack://dash_table/./node_modules/ramda/es/intersection.js", "webpack://dash_table/./node_modules/ramda/es/internal/_objectAssign.js", "webpack://dash_table/./node_modules/ramda/es/is.js", "webpack://dash_table/./node_modules/ramda/es/keysIn.js", "webpack://dash_table/./node_modules/ramda/es/lens.js", "webpack://dash_table/./node_modules/ramda/es/paths.js", "webpack://dash_table/./node_modules/ramda/es/path.js", "webpack://dash_table/./node_modules/ramda/es/lensPath.js", "webpack://dash_table/./node_modules/ramda/es/sum.js", "webpack://dash_table/./node_modules/ramda/es/add.js", "webpack://dash_table/./node_modules/ramda/es/mergeAll.js", "webpack://dash_table/./node_modules/ramda/es/mergeRight.js", "webpack://dash_table/./node_modules/ramda/es/min.js", "webpack://dash_table/./node_modules/ramda/es/omit.js", "webpack://dash_table/./node_modules/ramda/es/once.js", "webpack://dash_table/./node_modules/ramda/es/pick.js", "webpack://dash_table/./node_modules/ramda/es/props.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isNumber.js", "webpack://dash_table/./node_modules/ramda/es/range.js", "webpack://dash_table/./node_modules/ramda/es/reduceWhile.js", "webpack://dash_table/./node_modules/ramda/es/always.js", "webpack://dash_table/./node_modules/ramda/es/times.js", "webpack://dash_table/./node_modules/ramda/es/repeat.js", "webpack://dash_table/./node_modules/ramda/es/over.js", "webpack://dash_table/./node_modules/ramda/es/set.js", "webpack://dash_table/./node_modules/ramda/es/sortWith.js", "webpack://dash_table/./node_modules/ramda/es/toPairs.js", "webpack://dash_table/./node_modules/ramda/es/transpose.js", "webpack://dash_table/./node_modules/ramda/es/union.js", "webpack://dash_table/./node_modules/ramda/es/trim.js", "webpack://dash_table/./node_modules/ramda/es/unnest.js", "webpack://dash_table/./node_modules/ramda/es/without.js", "webpack://dash_table/./node_modules/ramda/es/xprod.js", "webpack://dash_table/./node_modules/ramda/es/zip.js", "webpack://dash_table/./node_modules/ramda/es/zipWith.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curry1.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curry2.js", "webpack://dash_table/./node_modules/ramda/es/internal/_curry3.js", "webpack://dash_table/./node_modules/ramda/es/internal/_isPlaceholder.js", "webpack://dash_table/./node_modules/ramda/es/remove.js", "webpack://dash_table/webpack/bootstrap", "webpack://dash_table/webpack/runtime/compat get default export", "webpack://dash_table/webpack/runtime/define property getters", "webpack://dash_table/webpack/runtime/ensure chunk", "webpack://dash_table/webpack/runtime/get javascript chunk filename", "webpack://dash_table/webpack/runtime/global", "webpack://dash_table/webpack/runtime/hasOwnProperty shorthand", "webpack://dash_table/webpack/runtime/make namespace object", "webpack://dash_table/webpack/runtime/publicPath", "webpack://dash_table/webpack/runtime/compat", "webpack://dash_table/webpack/runtime/jsonp chunk loading", "webpack://dash_table/webpack/runtime/nonce", "webpack://dash_table/./src/dash-table/index.ts"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? function(obj) { return Object.getPrototypeOf(obj); } : function(obj) { return obj.__proto__; };\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach(function(key) { def[key] = function() { return value[key]; }; });\n\t}\n\tdef['default'] = function() { return value; };\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"dash_table:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t}\n\t;\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "!function(e,n){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=n(require(\"react\")):\"function\"==typeof define&&define.amd?define([\"react\"],n):\"object\"==typeof exports?exports[\"dash-component-plugins\"]=n(require(\"react\")):e[\"dash-component-plugins\"]=n(e.React)}(window,(function(e){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&\"object\"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,\"default\",{enumerable:!0,value:e}),2&n&&\"string\"!=typeof e)for(var o in e)t.d(r,o,function(n){return e[n]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,\"a\",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p=\"\",t(t.s=1)}([function(n,t){n.exports=e},function(e,n,t){\"use strict\";t.r(n),t.d(n,\"asyncDecorator\",(function(){return u})),t.d(n,\"inheritAsyncDecorator\",(function(){return a})),t.d(n,\"isReady\",(function(){return c})),t.d(n,\"History\",(function(){return d}));var r=t(0);function o(e,n,t,r,o,i,u){try{var a=e[i](u),c=a.value}catch(e){return void t(e)}a.done?n(c):Promise.resolve(c).then(r,o)}function i(e){return function(){var n=this,t=arguments;return new Promise((function(r,i){var u=e.apply(n,t);function a(e){o(u,r,i,a,c,\"next\",e)}function c(e){o(u,r,i,a,c,\"throw\",e)}a(void 0)}))}}var u=function(e,n){var t,o={isReady:new Promise((function(e){t=e})),get:Object(r.lazy)((function(){return Promise.resolve(n()).then((function(e){return setTimeout(i(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t(!0);case 2:o.isReady=!0;case 3:case\"end\":return e.stop()}}),e)}))),0),e}))}))};return Object.defineProperty(e,\"_dashprivate_isLazyComponentReady\",{get:function(){return o.isReady}}),o.get},a=function(e,n){Object.defineProperty(e,\"_dashprivate_isLazyComponentReady\",{get:function(){return c(n)}})},c=function(e){return e&&e._dashprivate_isLazyComponentReady};function f(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var s=\"_dashprivate_historychange\",d=function(){function e(){!function(e,n){if(!(e instanceof n))throw new TypeError(\"Cannot call a class as a function\")}(this,e)}var n,t,r;return n=e,r=[{key:\"dispatchChangeEvent\",value:function(){window.dispatchEvent(new CustomEvent(s))}},{key:\"onChange\",value:function(e){return window.addEventListener(s,e),function(){return window.removeEventListener(s,e)}}}],(t=null)&&f(n.prototype,t),r&&f(n,r),Object.defineProperty(n,\"prototype\",{writable:!1}),e}()}])}));", "var DebugLevel;\n(function (DebugLevel) {\n    DebugLevel[DebugLevel[\"DEBUG\"] = 6] = \"DEBUG\";\n    DebugLevel[DebugLevel[\"NONE\"] = 7] = \"NONE\";\n})(DebugLevel || (DebugLevel = {}));\nexport default DebugLevel;\n", "var LogLevel;\n(function (LogLevel) {\n    LogLevel[LogLevel[\"TRACE\"] = 0] = \"TRACE\";\n    LogLevel[LogLevel[\"INFO\"] = 1] = \"INFO\";\n    LogLevel[LogLevel[\"WARNING\"] = 2] = \"WARNING\";\n    LogLevel[LogLevel[\"ERROR\"] = 3] = \"ERROR\";\n    LogLevel[LogLevel[\"FATAL\"] = 4] = \"FATAL\";\n    LogLevel[LogLevel[\"NONE\"] = 5] = \"NONE\";\n})(LogLevel || (LogLevel = {}));\nexport default LogLevel;\n", "import DebugLevel from './DebugLevel';\nimport LogLevel from './LogLevel';\nconst LogString = [];\nLogString[LogLevel.TRACE] = 'trace';\nLogString[LogLevel.INFO] = 'info';\nLogString[LogLevel.WARNING] = 'warning';\nLogString[LogLevel.ERROR] = 'error';\nLogString[LogLevel.FATAL] = 'fatal';\nLogString[LogLevel.NONE] = 'none';\nLogString[DebugLevel.DEBUG] = 'debug';\nLogString[DebugLevel.NONE] = 'trace';\nlet __logLevel = LogLevel.NONE;\nlet __debugLevel = DebugLevel.NONE;\nlet __highlightPrefix;\n__highlightPrefix = false;\nfunction logFn(level, currentLevel) {\n    if (level < currentLevel) {\n        return () => { };\n    }\n    let fn;\n    let fnStyle = '';\n    switch (level) {\n        case LogLevel.TRACE:\n        case LogLevel.INFO:\n            fn = window.console.log;\n            fnStyle = 'color: white; background-color: #3166A2;';\n            break;\n        case DebugLevel.DEBUG:\n        case LogLevel.WARNING:\n            fn = window.console.warn;\n            fnStyle = 'color: white; background-color: #E9B606;';\n            break;\n        case LogLevel.ERROR:\n        case LogLevel.FATAL:\n            fn = window.console.error;\n            fnStyle = 'color: white; background-color: #FF0000;';\n            break;\n        default:\n            throw new Error(`Unknown log ${level}`);\n    }\n    const prefix = `${fnStyle && __highlightPrefix ? '%c' : ''}[${LogString[level].toUpperCase()}]`;\n    if (fnStyle && __highlightPrefix) {\n        return fn.bind(window.console, prefix, fnStyle);\n    }\n    else {\n        return fn.bind(window.console, prefix);\n    }\n}\nconst logger = {\n    setDebugLevel(level) {\n        __debugLevel = level;\n    },\n    setLogLevel(level) {\n        __logLevel = level;\n    }\n};\nObject.defineProperties(logger, {\n    trace: {\n        get: () => {\n            return logFn(LogLevel.TRACE, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    info: {\n        get: () => {\n            return logFn(LogLevel.INFO, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    warning: {\n        get: () => {\n            return logFn(LogLevel.WARNING, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    error: {\n        get: () => {\n            return logFn(LogLevel.ERROR, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    fatal: {\n        get: () => {\n            return logFn(LogLevel.FATAL, __logLevel);\n        },\n        configurable: false,\n        enumerable: false\n    },\n    debug: {\n        get: () => {\n            return logFn(DebugLevel.DEBUG, __debugLevel);\n        },\n        configurable: false,\n        enumerable: false\n    }\n});\nObject.freeze(logger);\nexport default logger;\nexport { DebugLevel, LogLevel };\n", "import * as R from 'ramda';\nconst __1day = 86400 * 1000;\nconst __20years = 86400 * 1000 * 365 * 20;\nexport default class CookieStorage {\n    // From https://github.com/Modernizr/Modernizr/blob/f4d3aa0b3c9eeb7338e8d89ed77929a8e969c502/feature-detects/cookies.js#L1\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    static enabled = R.once(() => {\n        try {\n            // Create cookie\n            document.cookie = 'cookietest=1';\n            const ret = document.cookie.indexOf('cookietest=') !== -1;\n            // Delete cookie\n            document.cookie =\n                'cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n            return ret;\n        }\n        catch (e) {\n            return false;\n        }\n    });\n    static delete(id, domain = '', path = '/') {\n        if (!CookieStorage.enabled()) {\n            return;\n        }\n        const expires = new Date(Date.now() - __1day).toUTCString();\n        document.cookie = `${id}=;expires=${expires};domain=${domain};path=${path}`;\n    }\n    static get(id) {\n        if (!id.length) {\n            return;\n        }\n        if (!CookieStorage.enabled()) {\n            return;\n        }\n        id = id.toLowerCase();\n        const cookies = document.cookie.split(';').map(cookie => {\n            const fragments = cookie.split('=');\n            return {\n                id: fragments[0].trim(),\n                value: fragments[1]\n            };\n        });\n        return (cookies.find(cookie => id === cookie.id.toLocaleLowerCase()) ||\n            {}).value;\n    }\n    static set(id, value, domain = '', path = '/') {\n        if (!CookieStorage.enabled()) {\n            return;\n        }\n        const expires = new Date(Date.now() + __20years).toUTCString();\n        const entry = `${id}=${value};expires=${expires};domain=${domain};path=${path}`;\n        if (CookieStorage.get(id)) {\n            CookieStorage.delete(id, domain, path);\n        }\n        document.cookie = entry;\n    }\n}\n", "import CookieStorage from 'core/storage/Cookie';\nimport { DebugLevel, LogLevel } from 'core/Logger';\nconst DASH_DEBUG = 'dash_debug';\nconst DASH_LOG = 'dash_log';\nexport default class Environment {\n    static _supportsCssVariables = Boolean(window.CSS?.supports?.('.some-selector', 'var(--some-var)'));\n    static _activeEdge = Environment._supportsCssVariables\n        ? '1px solid var(--accent)'\n        : '1px solid hotpink';\n    static get searchParams() {\n        return ((typeof URL !== 'undefined' &&\n            URL.prototype &&\n            URL.prototype.constructor &&\n            new URL(window.location.href).searchParams) || { get: () => null });\n    }\n    static get debugLevel() {\n        const debug = this.searchParams.get(DASH_DEBUG) || CookieStorage.get(DASH_DEBUG);\n        return debug\n            ? DebugLevel[debug] || DebugLevel.NONE\n            : DebugLevel.NONE;\n    }\n    static get logLevel() {\n        const log = this.searchParams.get(DASH_LOG) || CookieStorage.get(DASH_LOG);\n        return log ? LogLevel[log] || LogLevel.ERROR : LogLevel.ERROR;\n    }\n    static get defaultEdge() {\n        return '1px solid #d3d3d3';\n    }\n    static get activeEdge() {\n        return Environment._activeEdge;\n    }\n    static get supportsCssVariables() {\n        return Environment._supportsCssVariables;\n    }\n}\n", "export default class LazyLoader {\n    static get xlsx() {\n        return import(\n        /* webpackChunkName: \"export\", webpackMode: \"lazy\" */ 'xlsx');\n    }\n    static get hljs() {\n        return Promise.resolve(window.hljs ||\n            import(\n            /* webpackChunkName: \"highlight\", webpackMode: \"lazy\" */ '../third-party/highlight.js').then(result => result.default));\n    }\n    static table() {\n        return import(\n        /* webpackChunkName: \"table\", webpackMode: \"lazy\" */ 'dash-table/dash/fragments/DataTable');\n    }\n}\n", "\nimport * as R from 'ramda';\nimport React, {Component, Suspense} from 'react';\nimport PropTypes from 'prop-types';\nimport {asyncDecorator} from '@plotly/dash-component-plugins';\nimport Lazy<PERSON>oader from 'dash-table/LazyLoader';\n/**\n * Dash DataTable is an interactive table component designed for\n * viewing, editing, and exploring large datasets.\n * DataTable is rendered with standard, semantic HTML <table/> markup,\n * which makes it accessible, responsive, and easy to style. This\n * component was written from scratch in React.js specifically for the\n * Dash community. Its API was designed to be ergonomic and its behavior\n * is completely customizable through its properties.\n */\nexport default class DataTable extends Component {\n    render() {\n        return (\n            <Suspense fallback={null}>\n                <RealDataTable {...this.props} />\n            </Suspense>\n        );\n    }\n}\nconst RealDataTable = asyncDecorator(DataTable, LazyLoader.table);\nexport const defaultProps = {\n    page_action: 'native',\n    page_current: 0,\n    page_size: 250,\n    css: [],\n    filter_query: '',\n    filter_action: 'none',\n    sort_as_null: [],\n    sort_action: 'none',\n    sort_mode: 'single',\n    sort_by: [],\n    style_as_list_view: false,\n    derived_viewport_data: [],\n    derived_viewport_indices: [],\n    derived_viewport_row_ids: [],\n    derived_viewport_selected_rows: [],\n    derived_viewport_selected_row_ids: [],\n    derived_virtual_data: [],\n    derived_virtual_indices: [],\n    derived_virtual_row_ids: [],\n    derived_virtual_selected_rows: [],\n    derived_virtual_selected_row_ids: [],\n    dropdown: {},\n    dropdown_conditional: [],\n    dropdown_data: [],\n    fill_width: true,\n    filter_options: {},\n    fixed_columns: {\n        headers: false,\n        data: 0\n    },\n    fixed_rows: {\n        headers: false,\n        data: 0\n    },\n    markdown_options: {\n        link_target: '_blank',\n        html: false\n    },\n    tooltip: {},\n    tooltip_conditional: [],\n    tooltip_data: [],\n    tooltip_header: {},\n    tooltip_delay: 350,\n    tooltip_duration: 2000,\n    column_selectable: false,\n    editable: false,\n    export_columns: 'visible',\n    export_format: 'none',\n    include_headers_on_copy_paste: false,\n    selected_cells: [],\n    selected_columns: [],\n    selected_rows: [],\n    selected_row_ids: [],\n    cell_selectable: true,\n    row_selectable: false,\n    style_table: {},\n    style_cell_conditional: [],\n    style_data_conditional: [],\n    style_filter_conditional: [],\n    style_header_conditional: [],\n    virtualization: false,\n    persisted_props: [\n        'columns.name',\n        'filter_query',\n        'hidden_columns',\n        'page_current',\n        'selected_columns',\n        'selected_rows',\n        'sort_by'\n    ],\n    persistence_type: 'local'\n};\nexport const propTypes = {\n    /**\n     * The contents of the table.\n     * The keys of each item in data should match the column IDs.\n     * Each item can also have an 'id' key, whose value is its row ID. If there\n     * is a column with ID='id' this will display the row ID, otherwise it is\n     * just used to reference the row for selections, filtering, etc.\n     * Example:\n     * [\n     *      {'column-1': 4.5, 'column-2': 'montreal', 'column-3': 'canada'},\n     *      {'column-1': 8, 'column-2': 'boston', 'column-3': 'america'}\n     * ]\n     */\n    data: PropTypes.arrayOf(\n        PropTypes.objectOf(\n            PropTypes.oneOfType([\n                PropTypes.string,\n                PropTypes.number,\n                PropTypes.bool\n            ])\n        )\n    ),\n    /**\n     * Columns describes various aspects about each individual column.\n     * `name` and `id` are the only required parameters.\n     */\n    columns: PropTypes.arrayOf(\n        PropTypes.exact({\n            /**\n             * The `id` of the column.\n             * The column `id` is used to match cells in data with particular columns.\n             * The `id` is not visible in the table.\n             */\n            id: PropTypes.string.isRequired,\n            /**\n             * The `name` of the column, as it appears in the column header.\n             * If `name` is a list of strings, then the columns\n             * will render with multiple headers rows.\n             */\n            name: PropTypes.oneOfType([\n                PropTypes.string,\n                PropTypes.arrayOf(PropTypes.string)\n            ]).isRequired,\n            /**\n             * The data-type provides support for per column typing and allows for data\n             * validation and coercion.\n             * 'numeric': represents both floats and ints.\n             * 'text': represents a string.\n             * 'datetime': a string representing a date or date-time, in the form:\n             *   'YYYY-MM-DD HH:MM:SS.ssssss' or some truncation thereof. Years must\n             *   have 4 digits, unless you use `validation.allow_YY: true`. Also\n             *   accepts 'T' or 't' between date and time, and allows timezone info\n             *   at the end. To convert these strings to Python `datetime` objects,\n             *   use `dateutil.parser.isoparse`. In R use `parse_iso_8601` from the\n             *   `parsedate` library.\n             *   WARNING: these parsers do not work with 2-digit years, if you use\n             *   `validation.allow_YY: true` and do not coerce to 4-digit years.\n             *   And parsers that do work with 2-digit years may make a different\n             *   guess about the century than we make on the front end.\n             * 'any': represents any type of data.\n             * Defaults to 'any' if undefined.\n             *\n             *\n             */\n            type: PropTypes.oneOf(['any', 'numeric', 'text', 'datetime']),\n            /**\n             * The `presentation` to use to display data. Markdown can be used on\n             * columns with type 'text'.  See 'dropdown' for more info.\n             * Defaults to 'input' for ['datetime', 'numeric', 'text', 'any'].\n             */\n            presentation: PropTypes.oneOf(['input', 'dropdown', 'markdown']),\n            /**\n             * If true, the user can select the column by clicking on the checkbox or radio button\n             * in the column. If there are multiple header rows, true will display the input\n             * on each row.\n             * If `last`, the input will only appear on the last header row. If `first` it will only\n             * appear on the first header row. These are respectively shortcut equivalents to\n             * `[false, ..., false, true]` and `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose which column header\n             * row to display the input in by supplying an array of booleans.\n             * For example, `[true, false]` will display the `selectable` input on the first row,\n             * but now on the second row.\n             * If the `selectable` input appears on a merged columns, then clicking on that input\n             * will select *all* of the merged columns associated with it.\n             * The table-level prop `column_selectable` is used to determine the type of column\n             * selection to use.\n             *\n             */\n            selectable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * If true, the user can clear the column by clicking on the `clear`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `clear` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `clear` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `clear` action button\n             * on the first row, but not the second row.\n             * If the `clear` action button appears on a merged column, then clicking\n             * on that button will clear *all* of the merged columns associated with it.\n             * Unlike `column.deletable`, this action does not remove the column(s)\n             * from the table. It only removed the associated entries from `data`.\n             */\n            clearable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * If true, the user can remove the column by clicking on the `delete`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `delete` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `delete` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `delete` action button\n             * on the first row, but not the second row.\n             * If the `delete` action button appears on a merged column, then clicking\n             * on that button will remove *all* of the merged columns associated with it.\n             */\n            deletable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * There are two `editable` flags in the table.\n             * This is the  column-level editable flag and there is\n             * also the table-level `editable` flag.\n             * These flags determine whether the contents of the table\n             * are editable or not.\n             * If the column-level `editable` flag is set it overrides\n             * the table-level `editable` flag for that column.\n             */\n            editable: PropTypes.bool,\n            /**\n             * If true, the user can hide the column by clicking on the `hide`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `hide` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `hide` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `hide` action button\n             * on the first row, but not the second row.\n             * If the `hide` action button appears on a merged column, then clicking\n             * on that button will hide *all* of the merged columns associated with it.\n             */\n            hideable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * If true, the user can rename the column by clicking on the `rename`\n             * action button on the column. If there are multiple header rows, true\n             * will display the action button on each row.\n             * If `last`, the `rename` action button will only appear on the last header\n             * row. If `first` it will only appear on the first header row. These\n             * are respectively shortcut equivalents to `[false, ..., false, true]` and\n             * `[true, false, ..., false]`.\n             * If there are merged, multi-header columns then you can choose\n             * which column header row to display the `rename` action button in by\n             * supplying an array of booleans.\n             * For example, `[true, false]` will display the `rename` action button\n             * on the first row, but not the second row.\n             * If the `rename` action button appears on a merged column, then clicking\n             * on that button will rename *all* of the merged columns associated with it.\n             */\n            renamable: PropTypes.oneOfType([\n                PropTypes.oneOf(['first', 'last']),\n                PropTypes.bool,\n                PropTypes.arrayOf(PropTypes.bool)\n            ]),\n            /**\n             * There are two `filter_options` props in the table.\n             * This is the column-level filter_options prop and there is\n             * also the table-level `filter_options` prop.\n             * If the column-level `filter_options` prop is set it overrides\n             * the table-level `filter_options` prop for that column.\n             */\n            filter_options: PropTypes.shape({\n                /**\n                 * (default: 'sensitive') Determine whether the applicable filter relational operators will default to `sensitive` or `insensitive` comparison.\n                 */\n                case: PropTypes.oneOf(['sensitive', 'insensitive']),\n                /**\n                 * (default: 'filter data...') The filter cell placeholder text.\n                 */\n                placeholder_text: PropTypes.string\n            }),\n            /**\n             * The formatting applied to the column's data.\n             * This prop is derived from the [d3-format](https://github.com/d3/d3-format) library specification. Apart from\n             * being structured slightly differently (under a single prop), the usage is the same.\n             * See also dash_table.FormatTemplate.  It contains helper functions for typical number formats.\n             */\n            format: PropTypes.exact({\n                /**\n                 * Represents localization specific formatting information.\n                 * When left unspecified, will use the default value provided by d3-format.\n                 */\n                locale: PropTypes.exact({\n                    /**\n                     * (default: ['$', '']).  A list of two strings representing the\n                     *  prefix and suffix symbols. Typically used for currency, and implemented using d3's\n                     *  currency format, but you can use this for other symbols such as measurement units\n                     */\n                    symbol: PropTypes.arrayOf(PropTypes.string),\n                    /**\n                     * (default: '.').  The string used for the decimal separator\n                     */\n                    decimal: PropTypes.string,\n                    /**\n                     * (default: ',').  The string used for the groups separator\n                     */\n                    group: PropTypes.string,\n                    /**\n                     * (default: [3]).  A list of integers representing the grouping pattern. The default is\n                     * 3 for thousands.\n                     */\n                    grouping: PropTypes.arrayOf(PropTypes.number),\n                    /**\n                     *  A list of ten strings used as replacements for numbers 0-9\n                     */\n                    numerals: PropTypes.arrayOf(PropTypes.string),\n                    /**\n                     * (default: '%').  The string used for the percentage symbol\n                     */\n                    percent: PropTypes.string,\n                    /**\n                     * (default: True). Separates integers with 4-digits or less\n                     */\n                    separate_4digits: PropTypes.bool\n                }),\n                /**\n                 * A value that will be used in place of the nully value during formatting.\n                 *   If the value type matches the column type, it will be formatted normally.\n                 */\n                nully: PropTypes.any,\n                /**\n                 * A number representing the SI unit to use during formatting.\n                 *   See `dash_table.Format.Prefix` enumeration for the list of valid values\n                 */\n                prefix: PropTypes.number,\n                /**\n                 *  (default: '').  Represents the d3 rules to apply when formatting the number.\n                 */\n                specifier: PropTypes.string\n            }),\n            /**\n             * The `on_change` behavior of the column for user-initiated modifications.\n             */\n            on_change: PropTypes.exact({\n                /**\n                 * (default 'coerce'):  'none': do not validate data;\n                 *  'coerce': check if the data corresponds to the destination type and\n                 *  attempts to coerce it into the destination type if not;\n                 *  'validate': check if the data corresponds to the destination type (no coercion).\n                 */\n                action: PropTypes.oneOf(['coerce', 'none', 'validate']),\n                /**\n                 *  (default 'reject'):  What to do with the value if the action fails.\n                 *  'accept': use the invalid value;\n                 *  'default': replace the provided value with `validation.default`;\n                 *  'reject': do not modify the existing value.\n                 */\n                failure: PropTypes.oneOf(['accept', 'default', 'reject'])\n            }),\n            /**\n             * An array of string, number and boolean values that are treated as `null`\n             * (i.e. ignored and always displayed last) when sorting.\n             * This value overrides the table-level `sort_as_null`.\n             */\n            sort_as_null: PropTypes.arrayOf(\n                PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.number,\n                    PropTypes.bool\n                ])\n            ),\n            /**\n             * The `validation` options for user input processing that can accept, reject or apply a\n             * default value.\n             */\n            validation: PropTypes.exact({\n                /**\n                 * Allow the use of nully values. (undefined, null, NaN) (default: False)\n                 */\n                allow_null: PropTypes.bool,\n                /**\n                 * The default value to apply with on_change.failure = 'default'. (default: None)\n                 */\n                default: PropTypes.any,\n                /**\n                 * This is for `datetime` columns only.  Allow 2-digit years (default: False).\n                 *   If True, we interpret years as ranging from now-70 to now+29 - in 2019\n                 *   this is 1949 to 2048 but in 2020 it will be different. If used with\n                 *   `action: 'coerce'`, will convert user input to a 4-digit year.\n                 */\n                allow_YY: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * If True, then the data in all of the cells is editable.\n     * When `editable` is True, particular columns can be made\n     * uneditable by setting `editable` to `False` inside the `columns`\n     * property.\n     * If False, then the data in all of the cells is uneditable.\n     * When `editable` is False, particular columns can be made\n     * editable by setting `editable` to `True` inside the `columns`\n     * property.\n     */\n    editable: PropTypes.bool,\n    /**\n     * `fixed_columns` will \"fix\" the set of columns so that\n     * they remain visible when scrolling horizontally across\n     * the unfixed columns. `fixed_columns` fixes columns\n     * from left-to-right.\n     * If `headers` is False, no columns are fixed.\n     * If `headers` is True, all operation columns (see `row_deletable` and `row_selectable`)\n     * are fixed. Additional data columns can be fixed by\n     * assigning a number to `data`.\n     *\n     * Note that fixing columns introduces some changes to the\n     * underlying markup of the table and may impact the\n     * way that your columns are rendered or sized.\n     * View the documentation examples to learn more.\n     *\n     */\n    fixed_columns: PropTypes.oneOfType([\n        PropTypes.exact({\n            /**\n             * Example `{'headers':False, 'data':0}` No columns are fixed (the default)\n             */\n            data: PropTypes.oneOf([0]),\n            headers: PropTypes.oneOf([false])\n        }),\n        PropTypes.exact({\n            /**\n             * Example `{'headers':True, 'data':1}` one column is fixed.\n             */\n            data: PropTypes.number,\n            headers: PropTypes.oneOf([true]).isRequired\n        })\n    ]),\n    /**\n     * `fixed_rows` will \"fix\" the set of rows so that\n     * they remain visible when scrolling vertically down\n     * the table. `fixed_rows` fixes rows\n     * from top-to-bottom, starting from the headers.\n     * If `headers` is False, no rows are fixed.\n     * If `headers` is True, all header and filter rows (see `filter_action`) are\n     * fixed. Additional data rows can be fixed by assigning\n     * a number to `data`.  Note that fixing rows introduces some changes to the\n     * underlying markup of the table and may impact the\n     * way that your columns are rendered or sized.\n     * View the documentation examples to learn more.\n     */\n    fixed_rows: PropTypes.oneOfType([\n        PropTypes.exact({\n            /**\n             * Example `{'headers':False, 'data':0}` No rows are fixed (the default)\n             */\n            data: PropTypes.oneOf([0]),\n            headers: PropTypes.oneOf([false])\n        }),\n        PropTypes.exact({\n            /**\n             * Example `{'headers':True, 'data':1}` one row is fixed.\n             */\n            data: PropTypes.number,\n            headers: PropTypes.oneOf([true]).isRequired\n        })\n    ]),\n    /**\n     * If `single`, then the user can select a single column or group\n     * of merged columns via the radio button that will appear in the\n     * header rows.\n     * If `multi`, then the user can select multiple columns or groups\n     * of merged columns via the checkbox that will appear in the header\n     * rows.\n     * If false, then the user will not be able to select columns and no\n     * input will appear in the header rows.\n     * When a column is selected, its id will be contained in `selected_columns`\n     * and `derived_viewport_selected_columns`.\n     */\n    column_selectable: PropTypes.oneOf(['single', 'multi', false]),\n    /**\n     * If True (default), then it is possible to click and navigate\n     * table cells.\n     */\n    cell_selectable: PropTypes.bool,\n    /**\n     * If `single`, then the user can select a single row\n     * via a radio button that will appear next to each row.\n     * If `multi`, then the user can select multiple rows\n     * via a checkbox that will appear next to each row.\n     * If false, then the user will not be able to select rows\n     * and no additional UI elements will appear.\n     * When a row is selected, its index will be contained\n     * in `selected_rows`.\n     */\n    row_selectable: PropTypes.oneOf(['single', 'multi', false]),\n    /**\n     * If True, then a `x` will appear next to each `row`\n     * and the user can delete the row.\n     */\n    row_deletable: PropTypes.bool,\n    /**\n     * The row and column indices and IDs of the currently active cell.\n     * `row_id` is only returned if the data rows have an `id` key.\n     */\n    active_cell: PropTypes.exact({\n        row: PropTypes.number,\n        column: PropTypes.number,\n        row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        column_id: PropTypes.string\n    }),\n    /**\n     * `selected_cells` represents the set of cells that are selected,\n     * as an array of objects, each item similar to `active_cell`.\n     * Multiple cells can be selected by holding down shift and\n     * clicking on a different cell or holding down shift and navigating\n     * with the arrow keys.\n     */\n    selected_cells: PropTypes.arrayOf(\n        PropTypes.exact({\n            row: PropTypes.number,\n            column: PropTypes.number,\n            row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n            column_id: PropTypes.string\n        })\n    ),\n    /**\n     * `selected_rows` contains the indices of rows that\n     * are selected via the UI elements that appear when\n     * `row_selectable` is `'single'` or `'multi'`.\n     */\n    selected_rows: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `selected_columns` contains the ids of columns that\n     * are selected via the UI elements that appear when\n     * `column_selectable` is `'single' or 'multi'`.\n     */\n    selected_columns: PropTypes.arrayOf(PropTypes.string),\n    /**\n     * `selected_row_ids` contains the ids of rows that\n     * are selected via the UI elements that appear when\n     * `row_selectable` is `'single'` or `'multi'`.\n     */\n    selected_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * When selecting multiple cells\n     * (via clicking on a cell and then shift-clicking on another cell),\n     * `start_cell` represents the [row, column] coordinates of the cell\n     * in one of the corners of the region.\n     * `end_cell` represents the coordinates of the other corner.\n     */\n    start_cell: PropTypes.exact({\n        row: PropTypes.number,\n        column: PropTypes.number,\n        row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        column_id: PropTypes.string\n    }),\n    /**\n     * When selecting multiple cells\n     * (via clicking on a cell and then shift-clicking on another cell),\n     * `end_cell` represents the row / column coordinates and IDs of the cell\n     * in one of the corners of the region.\n     * `start_cell` represents the coordinates of the other corner.\n     */\n    end_cell: PropTypes.exact({\n        row: PropTypes.number,\n        column: PropTypes.number,\n        row_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n        column_id: PropTypes.string\n    }),\n    /**\n     * The previous state of `data`. `data_previous`\n     * has the same structure as `data` and it will be updated\n     * whenever `data` changes, either through a callback or\n     * by editing the table.\n     * This is a read-only property: setting this property will not\n     * have any impact on the table.\n     */\n    data_previous: PropTypes.arrayOf(PropTypes.object),\n    /**\n     * List of columns ids of the columns that are currently hidden.\n     * See the associated nested prop `columns.hideable`.\n     */\n    hidden_columns: PropTypes.arrayOf(PropTypes.string),\n    /**\n     * If True, then the `active_cell` is in a focused state.\n     */\n    is_focused: PropTypes.bool,\n    /**\n     * If True, then column headers that have neighbors with duplicate names\n     * will be merged into a single cell.\n     * This will be applied for single column headers and multi-column\n     * headers.\n     */\n    merge_duplicate_headers: PropTypes.bool,\n    /**\n     * The unix timestamp when the data was last edited.\n     * Use this property with other timestamp properties\n     * (such as `n_clicks_timestamp` in `dash_html_components`)\n     * to determine which property has changed within a callback.\n     */\n    data_timestamp: PropTypes.number,\n    /**\n     * If true, headers are included when copying from the table to different\n     * tabs and elsewhere. Note that headers are ignored when copying from the table onto itself and\n     * between two tables within the same tab.\n     */\n    include_headers_on_copy_paste: PropTypes.bool,\n    /**\n     * Denotes the columns that will be used in the export data file.\n     * If `all`, all columns will be used (visible + hidden). If `visible`,\n     * only the visible columns will be used. Defaults to `visible`.\n     */\n    export_columns: PropTypes.oneOf(['all', 'visible']),\n    /**\n     * Denotes the type of the export data file,\n     * Defaults to `'none'`\n     */\n    export_format: PropTypes.oneOf(['csv', 'xlsx', 'none']),\n    /**\n     * Denotes the format of the headers in the export data file.\n     * If `'none'`, there will be no header. If `'display'`, then the header\n     * of the data file will be be how it is currently displayed. Note that\n     * `'display'` is only supported for `'xlsx'` export_format and will behave\n     * like `'names'` for `'csv'` export format. If `'ids'` or `'names'`,\n     * then the headers of data file will be the column id or the column\n     * names, respectively\n     */\n    export_headers: PropTypes.oneOf(['none', 'ids', 'names', 'display']),\n    /**\n     * `page_action` refers to a mode of the table where\n     * not all of the rows are displayed at once: only a subset\n     * are displayed (a \"page\") and the next subset of rows\n     * can viewed by clicking \"Next\" or \"Previous\" buttons\n     * at the bottom of the page.\n     * Pagination is used to improve performance: instead of\n     * rendering all of the rows at once (which can be expensive),\n     * we only display a subset of them.\n     * With pagination, we can either page through data that exists\n     * in the table (e.g. page through `10,000` rows in `data` `100` rows at a time)\n     * or we can update the data on-the-fly with callbacks\n     * when the user clicks on the \"Previous\" or \"Next\" buttons.\n     * These modes can be toggled with this `page_action` parameter:\n     * `'native'`: all data is passed to the table up-front, paging logic is\n     * handled by the table;\n     * `'custom'`: data is passed to the table one page at a time, paging logic\n     * is handled via callbacks;\n     * `'none'`: disables paging, render all of the data at once.\n     */\n    page_action: PropTypes.oneOf(['custom', 'native', 'none']),\n    /**\n     * `page_current` represents which page the user is on.\n     * Use this property to index through data in your callbacks with\n     * backend paging.\n     */\n    page_current: PropTypes.number,\n    /**\n     * `page_count` represents the number of the pages in the\n     * paginated table. This is really only useful when performing\n     * backend pagination, since the front end is able to use the\n     * full size of the table to calculate the number of pages.\n     */\n    page_count: PropTypes.number,\n    /**\n     * `page_size` represents the number of rows that will be\n     * displayed on a particular page when `page_action` is `'custom'` or `'native'`\n     */\n    page_size: PropTypes.number,\n    /**\n     * If `filter_action` is enabled, then the current filtering\n     * string is represented in this `filter_query`\n     * property.\n     */\n    filter_query: PropTypes.string,\n    /**\n     * The `filter_action` property controls the behavior of the `filtering` UI.\n     * If `'none'`, then the filtering UI is not displayed.\n     * If `'native'`, then the filtering UI is displayed and the filtering\n     * logic is handled by the table. That is, it is performed on the data\n     * that exists in the `data` property.\n     * If `'custom'`, then the filtering UI is displayed but it is the\n     * responsibility of the developer to program the filtering\n     * through a callback (where `filter_query` or `derived_filter_query_structure` would be the input\n     * and `data` would be the output).\n     */\n    filter_action: PropTypes.oneOfType([\n        PropTypes.oneOf(['custom', 'native', 'none']),\n        PropTypes.shape({\n            type: PropTypes.oneOf(['custom', 'native']).isRequired,\n            operator: PropTypes.oneOf(['and', 'or'])\n        })\n    ]),\n    /**\n     * There are two `filter_options` props in the table.\n     * This is the table-level filter_options prop and there is\n     * also the column-level `filter_options` prop.\n     * If the column-level `filter_options` prop is set it overrides\n     * the table-level `filter_options` prop for that column.\n     */\n    filter_options: PropTypes.shape({\n        /**\n         * (default: 'sensitive') Determine whether the applicable filter relational operators will default to `sensitive` or `insensitive` comparison.\n         */\n        case: PropTypes.oneOf(['sensitive', 'insensitive']),\n        /**\n         * (default: 'filter data...') The filter cell placeholder text.\n         */\n        placeholder_text: PropTypes.string\n    }),\n    /**\n     * The `sort_action` property enables data to be\n     * sorted on a per-column basis.\n     * If `'none'`, then the sorting UI is not displayed.\n     * If `'native'`, then the sorting UI is displayed and the sorting\n     * logic is handled by the table. That is, it is performed on the data\n     * that exists in the `data` property.\n     * If `'custom'`, the the sorting UI is displayed but it is the\n     * responsibility of the developer to program the sorting\n     * through a callback (where `sort_by` would be the input and `data`\n     * would be the output).\n     * Clicking on the sort arrows will update the\n     * `sort_by` property.\n     */\n    sort_action: PropTypes.oneOf(['custom', 'native', 'none']),\n    /**\n     * Sorting can be performed across multiple columns\n     * (e.g. sort by country, sort within each country,\n     *  sort by year) or by a single column.\n     * NOTE - With multi-column sort, it's currently\n     * not possible to determine the order in which\n     * the columns were sorted through the UI.\n     * See [https://github.com/plotly/dash-table/issues/170](https://github.com/plotly/dash-table/issues/170)\n     */\n    sort_mode: PropTypes.oneOf(['single', 'multi']),\n    /**\n     * `sort_by` describes the current state\n     * of the sorting UI.\n     * That is, if the user clicked on the sort arrow\n     * of a column, then this property will be updated\n     * with the column ID and the direction\n     * (`asc` or `desc`) of the sort.\n     * For multi-column sorting, this will be a list of\n     * sorting parameters, in the order in which they were\n     * clicked.\n     */\n    sort_by: PropTypes.arrayOf(\n        PropTypes.exact({\n            column_id: PropTypes.string.isRequired,\n            direction: PropTypes.oneOf(['asc', 'desc']).isRequired\n        })\n    ),\n    /**\n     * An array of string, number and boolean values that are treated as `None`\n     * (i.e. ignored and always displayed last) when sorting.\n     * This value will be used by columns without `sort_as_null`.\n     * Defaults to `[]`.\n     */\n    sort_as_null: PropTypes.arrayOf(\n        PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.number,\n            PropTypes.bool\n        ])\n    ),\n    /**\n     * `dropdown` specifies dropdown options for different columns.\n     * Each entry refers to the column ID.\n     * The `clearable` property defines whether the value can be deleted.\n     * The `options` property refers to the `options` of the dropdown.\n     */\n    dropdown: PropTypes.objectOf(\n        PropTypes.exact({\n            clearable: PropTypes.bool,\n            options: PropTypes.arrayOf(\n                PropTypes.exact({\n                    label: PropTypes.string.isRequired,\n                    value: PropTypes.oneOfType([\n                        PropTypes.number,\n                        PropTypes.string,\n                        PropTypes.bool\n                    ]).isRequired\n                })\n            ).isRequired\n        })\n    ),\n    /**\n     * `dropdown_conditional` specifies dropdown options in various columns and cells.\n     * This property allows you to specify different dropdowns\n     * depending on certain conditions. For example, you may\n     * render different \"city\" dropdowns in a row depending on the\n     * current value in the \"state\" column.\n     */\n    dropdown_conditional: PropTypes.arrayOf(\n        PropTypes.exact({\n            clearable: PropTypes.bool,\n            if: PropTypes.exact({\n                column_id: PropTypes.string,\n                filter_query: PropTypes.string\n            }),\n            options: PropTypes.arrayOf(\n                PropTypes.exact({\n                    label: PropTypes.string.isRequired,\n                    value: PropTypes.oneOfType([\n                        PropTypes.number,\n                        PropTypes.string,\n                        PropTypes.bool\n                    ]).isRequired\n                })\n            ).isRequired\n        })\n    ),\n    /**\n     * `dropdown_data` specifies dropdown options on a row-by-row, column-by-column basis.\n     * Each item in the array corresponds to the corresponding dropdowns for the `data` item\n     * at the same index. Each entry in the item refers to the Column ID.\n     */\n    dropdown_data: PropTypes.arrayOf(\n        PropTypes.objectOf(\n            PropTypes.exact({\n                clearable: PropTypes.bool,\n                options: PropTypes.arrayOf(\n                    PropTypes.exact({\n                        label: PropTypes.string.isRequired,\n                        value: PropTypes.oneOfType([\n                            PropTypes.number,\n                            PropTypes.string,\n                            PropTypes.bool\n                        ]).isRequired\n                    })\n                ).isRequired\n            })\n        )\n    ),\n    /**\n     * `tooltip` is the column based tooltip configuration applied to all rows. The key is the column\n     *  id and the value is a tooltip configuration.\n     * Example: {i: {'value': i, 'use_with: 'both'} for i in df.columns}\n     */\n    tooltip: PropTypes.objectOf(\n        PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.exact({\n                /**\n                 * Represents the delay in milliseconds before\n                 * the tooltip is shown when hovering a cell. This overrides\n                 * the table's `tooltip_delay` property. If set to `None`,\n                 * the tooltip will be shown immediately.\n                 */\n                delay: PropTypes.number,\n                /**\n                 * represents the duration in milliseconds\n                 * during which the tooltip is shown when hovering a cell.\n                 * This overrides the table's `tooltip_duration` property.\n                 * If set to `None`, the tooltip will not disappear.\n                 */\n                duration: PropTypes.number,\n                /**\n                 * refers to the type of tooltip syntax used\n                 * for the tooltip generation. Can either be `markdown`\n                 * or `text`. Defaults to `text`.\n                 */\n                type: PropTypes.oneOf(['text', 'markdown']),\n                /**\n                 * Refers to whether the tooltip will be shown\n                 * only on data or headers. Can be `both`, `data`, `header`.\n                 * Defaults to `both`.\n                 */\n                use_with: PropTypes.oneOf(['both', 'data', 'header']),\n                /**\n                 * refers to the syntax-based content of\n                 * the tooltip. This value is required. Alternatively, the value of the\n                 * property can also be  a plain string. The `text` syntax will be used in\n                 * that case.\n                 */\n                value: PropTypes.string.isRequired\n            })\n        ])\n    ),\n    /**\n     * `tooltip_conditional` represents the tooltip shown\n     * for different columns and cells.\n     * This property allows you to specify different tooltips\n     * depending on certain conditions. For example, you may have\n     * different tooltips in the same column based on the value\n     * of a certain data property.\n     * Priority is from first to last defined conditional tooltip\n     * in the list. Higher priority (more specific) conditional\n     * tooltips should be put at the beginning of the list.\n     */\n    tooltip_conditional: PropTypes.arrayOf(\n        PropTypes.exact({\n            /**\n             * The `delay` represents the delay in milliseconds before\n             * the tooltip is shown when hovering a cell. This overrides\n             * the table's `tooltip_delay` property. If set to `None`,\n             * the tooltip will be shown immediately.\n             */\n            delay: PropTypes.number,\n            /**\n             * The `duration` represents the duration in milliseconds\n             * during which the tooltip is shown when hovering a cell.\n             * This overrides the table's `tooltip_duration` property.\n             * If set to `None`, the tooltip will not disappear.\n             */\n            duration: PropTypes.number,\n            /**\n             * The `if` refers to the condition that needs to be fulfilled\n             * in order for the associated tooltip configuration to be\n             * used. If multiple conditions are defined, all conditions\n             * must be met for the tooltip to be used by a cell.\n             */\n            if: PropTypes.exact({\n                /**\n                 * `column_id` refers to the column ID that must be matched.\n                 */\n                column_id: PropTypes.string,\n                /**\n                 * `filter_query` refers to the query that must evaluate to True.\n                 */\n                filter_query: PropTypes.string,\n                /**\n                 * `row_index` refers to the index of the row in the source `data`.\n                 */\n                row_index: PropTypes.oneOfType([\n                    PropTypes.number,\n                    PropTypes.oneOf(['odd', 'even'])\n                ])\n            }).isRequired,\n            /**\n             * The `type` refers to the type of tooltip syntax used\n             * for the tooltip generation. Can either be `markdown`\n             * or `text`. Defaults to `text`.\n             */\n            type: PropTypes.oneOf(['text', 'markdown']),\n            /**\n             * The `value` refers to the syntax-based content of the tooltip. This value is required.\n             */\n            value: PropTypes.string.isRequired\n        })\n    ),\n    /**\n     * `tooltip_data` represents the tooltip shown\n     * for different columns and cells.\n     * A list of dicts for which each key is\n     * a column id and the value is a tooltip configuration.\n     */\n    tooltip_data: PropTypes.arrayOf(\n        PropTypes.objectOf(\n            PropTypes.oneOfType([\n                PropTypes.string,\n                PropTypes.exact({\n                    /**\n                     * The `delay` represents the delay in milliseconds before\n                     * the tooltip is shown when hovering a cell. This overrides\n                     * the table's `tooltip_delay` property. If set to `None`,\n                     * the tooltip will be shown immediately.\n                     */\n                    delay: PropTypes.number,\n                    /**\n                     * The `duration` represents the duration in milliseconds\n                     * during which the tooltip is shown when hovering a cell.\n                     * This overrides the table's `tooltip_duration` property.\n                     * If set to `None`, the tooltip will not disappear.\n                     * Alternatively, the value of the property can also be\n                     * a plain string. The `text` syntax will be used in\n                     * that case.\n                     */\n                    duration: PropTypes.number,\n                    /**\n                     * For each tooltip configuration,\n                     * The `type` refers to the type of tooltip syntax used\n                     * for the tooltip generation. Can either be `markdown`\n                     * or `text`. Defaults to `text`.\n                     */\n                    type: PropTypes.oneOf(['text', 'markdown']),\n                    /**\n                     * The `value` refers to the syntax-based content of the tooltip. This value is required.\n                     */\n                    value: PropTypes.string.isRequired\n                })\n            ])\n        )\n    ),\n    /**\n     * `tooltip_header` represents the tooltip shown\n     * for each header column and optionally each header row.\n     * Example to show long column names in a tooltip: {i: i for i in df.columns}.\n     * Example to show different column names in a tooltip: {'Rep': 'Republican', 'Dem': 'Democrat'}.\n     * If the table has multiple rows of headers, then use a list as the value of the\n     * `tooltip_header` items.\n     *\n     *\n     */\n    tooltip_header: PropTypes.objectOf(\n        PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.exact({\n                /**\n                 * The `delay` represents the delay in milliseconds before\n                 * the tooltip is shown when hovering a cell. This overrides\n                 * the table's `tooltip_delay` property. If set to `None`,\n                 * the tooltip will be shown immediately.\n                 */\n                delay: PropTypes.number,\n                /**\n                 * The `duration` represents the duration in milliseconds\n                 * during which the tooltip is shown when hovering a cell.\n                 * This overrides the table's `tooltip_duration` property.\n                 * If set to `None`, the tooltip will not disappear.\n                 * Alternatively, the value of the property can also be\n                 * a plain string. The `text` syntax will be used in\n                 * that case.\n                 */\n                duration: PropTypes.number,\n                /**\n                 * For each tooltip configuration,\n                 * The `type` refers to the type of tooltip syntax used\n                 * for the tooltip generation. Can either be `markdown`\n                 * or `text`. Defaults to `text`.\n                 */\n                type: PropTypes.oneOf(['text', 'markdown']),\n                /**\n                 * The `value` refers to the syntax-based content of the tooltip. This value is required.\n                 */\n                value: PropTypes.string.isRequired\n            }),\n            PropTypes.arrayOf(\n                PropTypes.oneOfType([\n                    PropTypes.oneOf([null]),\n                    PropTypes.string,\n                    PropTypes.exact({\n                        delay: PropTypes.number,\n                        duration: PropTypes.number,\n                        type: PropTypes.oneOf(['text', 'markdown']),\n                        value: PropTypes.string.isRequired\n                    })\n                ])\n            )\n        ])\n    ),\n    /**\n     * `tooltip_delay` represents the table-wide delay in milliseconds before\n     * the tooltip is shown when hovering a cell. If set to `None`, the tooltip\n     * will be shown immediately.\n     * Defaults to 350.\n     */\n    tooltip_delay: PropTypes.number,\n    /**\n     * `tooltip_duration` represents the table-wide duration in milliseconds\n     * during which the tooltip will be displayed when hovering a cell. If\n     * set to `None`, the tooltip will not disappear.\n     * Defaults to 2000.\n     */\n    tooltip_duration: PropTypes.number,\n    /**\n     * The localization specific formatting information applied to all columns in the table.\n     * This prop is derived from the [d3.formatLocale](https://github.com/d3/d3-format#formatLocale) data structure specification.\n     * When left unspecified, each individual nested prop will default to a pre-determined value.\n     */\n    locale_format: PropTypes.exact({\n        /**\n         *   (default: ['$', '']). A  list of two strings representing the\n         *   prefix and suffix symbols. Typically used for currency, and implemented using d3's\n         *   currency format, but you can use this for other symbols such as measurement units.\n         */\n        symbol: PropTypes.arrayOf(PropTypes.string),\n        /**\n         * (default: '.'). The string used for the decimal separator.\n         */\n        decimal: PropTypes.string,\n        /**\n         * (default: ','). The string used for the groups separator.\n         */\n        group: PropTypes.string,\n        /**\n         * (default: [3]). A  list of integers representing the grouping pattern.\n         */\n        grouping: PropTypes.arrayOf(PropTypes.number),\n        /**\n         * A list of ten strings used as replacements for numbers 0-9.\n         */\n        numerals: PropTypes.arrayOf(PropTypes.string),\n        /**\n         * (default: '%'). The string used for the percentage symbol.\n         */\n        percent: PropTypes.string,\n        /**\n         * (default: True). Separate integers with 4-digits or less.\n         */\n        separate_4digits: PropTypes.bool\n    }),\n    /**\n     * If True, then the table will be styled like a list view\n     * and not have borders between the columns.\n     */\n    style_as_list_view: PropTypes.bool,\n    /**\n     * `fill_width` toggles between a set of CSS for two common behaviors:\n     * True: The table container's width will grow to fill the available space;\n     * False: The table container's width will equal the width of its content.\n     */\n    fill_width: PropTypes.bool,\n    /**\n     * The `markdown_options` property allows customization of the markdown cells behavior.\n     */\n    markdown_options: PropTypes.exact({\n        /**\n         * (default: '_blank').  The link's behavior (_blank opens the link in a\n         * new tab, _parent opens the link in the parent frame, _self opens the link in the\n         * current tab, and _top opens the link in the top frame) or a string\n         */\n        link_target: PropTypes.oneOfType([\n            PropTypes.string,\n            PropTypes.oneOf(['_blank', '_parent', '_self', '_top'])\n        ]),\n        /**\n         * (default: False)  If True, html may be used in markdown cells\n         * Be careful enabling html if the content being rendered can come\n         * from an untrusted user, as this may create an XSS vulnerability.\n         */\n        html: PropTypes.bool\n    }),\n    /**\n     * The `css` property is a way to embed CSS selectors and rules\n     * onto the page.\n     * We recommend starting with the `style_*` properties\n     * before using this `css` property.\n     * Example:\n     * [\n     *     {\"selector\": \".dash-spreadsheet\", \"rule\": 'font-family: \"monospace\"'}\n     * ]\n     */\n    css: PropTypes.arrayOf(\n        PropTypes.exact({\n            selector: PropTypes.string.isRequired,\n            rule: PropTypes.string.isRequired\n        })\n    ),\n    /**\n     * CSS styles to be applied to the outer `table` container.\n     * This is commonly used for setting properties like the\n     * width or the height of the table.\n     */\n    style_table: PropTypes.object,\n    /**\n     * CSS styles to be applied to each individual cell of the table.\n     * This includes the header cells, the `data` cells, and the filter\n     * cells.\n     */\n    style_cell: PropTypes.object,\n    /**\n     * CSS styles to be applied to each individual data cell.\n     * That is, unlike `style_cell`, it excludes the header and filter cells.\n     */\n    style_data: PropTypes.object,\n    /**\n     * CSS styles to be applied to the filter cells.\n     * Note that this may change in the future as we build out a\n     * more complex filtering UI.\n     */\n    style_filter: PropTypes.object,\n    /**\n     * CSS styles to be applied to each individual header cell.\n     * That is, unlike `style_cell`, it excludes the `data` and filter cells.\n     */\n    style_header: PropTypes.object,\n    /**\n     * Conditional CSS styles for the cells.\n     * This can be used to apply styles to cells on a per-column basis.\n     */\n    style_cell_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ])\n            })\n        })\n    ),\n    /**\n     * Conditional CSS styles for the data cells.\n     * This can be used to apply styles to data cells on a per-column basis.\n     */\n    style_data_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ]),\n                filter_query: PropTypes.string,\n                state: PropTypes.oneOf(['active', 'selected']),\n                row_index: PropTypes.oneOfType([\n                    PropTypes.number,\n                    PropTypes.oneOf(['odd', 'even']),\n                    PropTypes.arrayOf(PropTypes.number)\n                ]),\n                column_editable: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * Conditional CSS styles for the filter cells.\n     * This can be used to apply styles to filter cells on a per-column basis.\n     */\n    style_filter_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ]),\n                column_editable: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * Conditional CSS styles for the header cells.\n     * This can be used to apply styles to header cells on a per-column basis.\n     */\n    style_header_conditional: PropTypes.arrayOf(\n        PropTypes.shape({\n            if: PropTypes.exact({\n                column_id: PropTypes.oneOfType([\n                    PropTypes.string,\n                    PropTypes.arrayOf(PropTypes.string)\n                ]),\n                column_type: PropTypes.oneOf([\n                    'any',\n                    'numeric',\n                    'text',\n                    'datetime'\n                ]),\n                header_index: PropTypes.oneOfType([\n                    PropTypes.number,\n                    PropTypes.arrayOf(PropTypes.number),\n                    PropTypes.oneOf(['odd', 'even'])\n                ]),\n                column_editable: PropTypes.bool\n            })\n        })\n    ),\n    /**\n     * This property tells the table to use virtualization when rendering.\n     * Assumptions are that:\n     * the width of the columns is fixed;\n     * the height of the rows is always the same; and\n     * runtime styling changes will not affect width and height vs. first rendering\n     */\n    virtualization: PropTypes.bool,\n    /**\n     * This property represents the current structure of\n     * `filter_query` as a tree structure. Each node of the\n     * query structure has:\n     * type (string; required):\n     *   'open-block',\n     *   'logical-operator',\n     *   'relational-operator',\n     *   'unary-operator', or\n     *   'expression';\n     * subType (string; optional):\n     *   'open-block': '()',\n     *   'logical-operator': '&&', '||',\n     *   'relational-operator': '=', '>=', '>', '<=', '<', '!=', 'contains',\n     *   'unary-operator': '!', 'is bool', 'is even', 'is nil', 'is num', 'is object', 'is odd', 'is prime', 'is str',\n     *   'expression': 'value', 'field';\n     * value (any):\n     *   'expression, value': passed value,\n     *   'expression, field': the field/prop name.\n     * block (nested query structure; optional).\n     * left (nested query structure; optional).\n     * right (nested query structure; optional).\n     * If the query is invalid or empty, the `derived_filter_query_structure` will\n     * be `None`.\n     */\n    derived_filter_query_structure: PropTypes.object,\n    /**\n     * This property represents the current state of `data`\n     * on the current page. This property will be updated\n     * on paging, sorting, and filtering.\n     */\n    derived_viewport_data: PropTypes.arrayOf(PropTypes.object),\n    /**\n     * `derived_viewport_indices` indicates the order in which the original\n     * rows appear after being filtered, sorted, and/or paged.\n     * `derived_viewport_indices` contains indices for the current page,\n     * while `derived_virtual_indices` contains indices across all pages.\n     */\n    derived_viewport_indices: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_viewport_row_ids` lists row IDs in the order they appear\n     * after being filtered, sorted, and/or paged.\n     * `derived_viewport_row_ids` contains IDs for the current page,\n     * while `derived_virtual_row_ids` contains IDs across all pages.\n     */\n    derived_viewport_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * `derived_viewport_selected_columns` contains the ids of the\n     * `selected_columns` that are not currently hidden.\n     */\n    derived_viewport_selected_columns: PropTypes.arrayOf(PropTypes.string),\n    /**\n     * `derived_viewport_selected_rows` represents the indices of the\n     * `selected_rows` from the perspective of the `derived_viewport_indices`.\n     */\n    derived_viewport_selected_rows: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_viewport_selected_row_ids` represents the IDs of the\n     * `selected_rows` on the currently visible page.\n     */\n    derived_viewport_selected_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * This property represents the visible state of `data`\n     * across all pages after the front-end sorting and filtering\n     * as been applied.\n     */\n    derived_virtual_data: PropTypes.arrayOf(PropTypes.object),\n    /**\n     * `derived_virtual_indices` indicates the order in which the original\n     * rows appear after being filtered and sorted.\n     * `derived_viewport_indices` contains indices for the current page,\n     * while `derived_virtual_indices` contains indices across all pages.\n     */\n    derived_virtual_indices: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_virtual_row_ids` indicates the row IDs in the order in which\n     * they appear after being filtered and sorted.\n     * `derived_viewport_row_ids` contains IDs for the current page,\n     * while `derived_virtual_row_ids` contains IDs across all pages.\n     */\n    derived_virtual_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * `derived_virtual_selected_rows` represents the indices of the\n     *  `selected_rows` from the perspective of the `derived_virtual_indices`.\n     */\n    derived_virtual_selected_rows: PropTypes.arrayOf(PropTypes.number),\n    /**\n     * `derived_virtual_selected_row_ids` represents the IDs of the\n     * `selected_rows` as they appear after filtering and sorting,\n     * across all pages.\n     */\n    derived_virtual_selected_row_ids: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n    ),\n    /**\n     * The ID of the table.\n     */\n    id: PropTypes.string,\n    /**\n     * Dash-assigned callback that gets fired when the user makes changes.\n     */\n    setProps: PropTypes.func,\n    /**\n     * Object that holds the loading state object coming from dash-renderer\n     */\n    loading_state: PropTypes.shape({\n        /**\n         * Determines if the component is loading or not\n         */\n        is_loading: PropTypes.bool,\n        /**\n         * Holds which property is loading\n         */\n        prop_name: PropTypes.string,\n        /**\n         * Holds the name of the component that is loading\n         */\n        component_name: PropTypes.string\n    }),\n    /**\n     * Used to allow user interactions in this component to be persisted when\n     * the component - or the page - is refreshed. If `persisted` is truthy and\n     * hasn't changed from its previous value, any `persisted_props` that the\n     * user has changed while using the app will keep those changes, as long as\n     * the new prop value also matches what was given originally.\n     * Used in conjunction with `persistence_type` and `persisted_props`.\n     */\n    persistence: PropTypes.oneOfType([\n        PropTypes.bool,\n        PropTypes.string,\n        PropTypes.number\n    ]),\n    /**\n     * Properties whose user interactions will persist after refreshing the\n     * component or the page.\n     */\n    persisted_props: PropTypes.arrayOf(\n        PropTypes.oneOf([\n            'columns.name',\n            'data',\n            'filter_query',\n            'hidden_columns',\n            'page_current',\n            'selected_columns',\n            'selected_rows',\n            'sort_by'\n        ])\n    ),\n    /**\n     * Where persisted user changes will be stored:\n     * memory: only kept in memory, reset on page refresh.\n     * local: window.localStorage, data is kept after the browser quit.\n     * session: window.sessionStorage, data is cleared once the browser quit.\n     */\n    persistence_type: PropTypes.oneOf(['local', 'session', 'memory'])\n};\nDataTable.persistenceTransforms = {\n    columns: {\n        name: {\n            extract: propValue => R.pluck('name', propValue),\n            apply: (storedValue, propValue) =>\n                R.zipWith(R.assoc('name'), storedValue, propValue)\n        }\n    }\n};\nDataTable.defaultProps = defaultProps;\nDataTable.propTypes = propTypes;\n", "/*! https://mths.be/cssescape v1.5.1 by @mathias | MIT license */\n;(function(root, factory) {\n\t// https://github.com/umdjs/umd/blob/master/returnExports.js\n\tif (typeof exports == 'object') {\n\t\t// For Node.js.\n\t\tmodule.exports = factory(root);\n\t} else if (typeof define == 'function' && define.amd) {\n\t\t// For AMD. Register as an anonymous module.\n\t\tdefine([], factory.bind(root, root));\n\t} else {\n\t\t// For browser globals (not exposing the function separately).\n\t\tfactory(root);\n\t}\n}(typeof global != 'undefined' ? global : this, function(root) {\n\n\tif (root.CSS && root.CSS.escape) {\n\t\treturn root.CSS.escape;\n\t}\n\n\t// https://drafts.csswg.org/cssom/#serialize-an-identifier\n\tvar cssEscape = function(value) {\n\t\tif (arguments.length == 0) {\n\t\t\tthrow new TypeError('`CSS.escape` requires an argument.');\n\t\t}\n\t\tvar string = String(value);\n\t\tvar length = string.length;\n\t\tvar index = -1;\n\t\tvar codeUnit;\n\t\tvar result = '';\n\t\tvar firstCodeUnit = string.charCodeAt(0);\n\t\twhile (++index < length) {\n\t\t\tcodeUnit = string.charCodeAt(index);\n\t\t\t// Note: there’s no need to special-case astral symbols, surrogate\n\t\t\t// pairs, or lone surrogates.\n\n\t\t\t// If the character is NULL (U+0000), then the REPLACEMENT CHARACTER\n\t\t\t// (U+FFFD).\n\t\t\tif (codeUnit == 0x0000) {\n\t\t\t\tresult += '\\uFFFD';\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t// If the character is in the range [\\1-\\1F] (U+0001 to U+001F) or is\n\t\t\t\t// U+007F, […]\n\t\t\t\t(codeUnit >= 0x0001 && codeUnit <= 0x001F) || codeUnit == 0x007F ||\n\t\t\t\t// If the character is the first character and is in the range [0-9]\n\t\t\t\t// (U+0030 to U+0039), […]\n\t\t\t\t(index == 0 && codeUnit >= 0x0030 && codeUnit <= 0x0039) ||\n\t\t\t\t// If the character is the second character and is in the range [0-9]\n\t\t\t\t// (U+0030 to U+0039) and the first character is a `-` (U+002D), […]\n\t\t\t\t(\n\t\t\t\t\tindex == 1 &&\n\t\t\t\t\tcodeUnit >= 0x0030 && codeUnit <= 0x0039 &&\n\t\t\t\t\tfirstCodeUnit == 0x002D\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\t// https://drafts.csswg.org/cssom/#escape-a-character-as-code-point\n\t\t\t\tresult += '\\\\' + codeUnit.toString(16) + ' ';\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t// If the character is the first character and is a `-` (U+002D), and\n\t\t\t\t// there is no second character, […]\n\t\t\t\tindex == 0 &&\n\t\t\t\tlength == 1 &&\n\t\t\t\tcodeUnit == 0x002D\n\t\t\t) {\n\t\t\t\tresult += '\\\\' + string.charAt(index);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If the character is not handled by one of the above rules and is\n\t\t\t// greater than or equal to U+0080, is `-` (U+002D) or `_` (U+005F), or\n\t\t\t// is in one of the ranges [0-9] (U+0030 to U+0039), [A-Z] (U+0041 to\n\t\t\t// U+005A), or [a-z] (U+0061 to U+007A), […]\n\t\t\tif (\n\t\t\t\tcodeUnit >= 0x0080 ||\n\t\t\t\tcodeUnit == 0x002D ||\n\t\t\t\tcodeUnit == 0x005F ||\n\t\t\t\tcodeUnit >= 0x0030 && codeUnit <= 0x0039 ||\n\t\t\t\tcodeUnit >= 0x0041 && codeUnit <= 0x005A ||\n\t\t\t\tcodeUnit >= 0x0061 && codeUnit <= 0x007A\n\t\t\t) {\n\t\t\t\t// the character itself\n\t\t\t\tresult += string.charAt(index);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// Otherwise, the escaped character.\n\t\t\t// https://drafts.csswg.org/cssom/#escape-a-character\n\t\t\tresult += '\\\\' + string.charAt(index);\n\n\t\t}\n\t\treturn result;\n\t};\n\n\tif (!root.CSS) {\n\t\troot.CSS = {};\n\t}\n\n\troot.CSS.escape = cssEscape;\n\treturn cssEscape;\n\n}));\n", "module.exports = window[\"PropTypes\"];", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "/**\n * Private `concat` function to merge two array-like objects.\n *\n * @private\n * @param {Array|Arguments} [set1=[]] An array-like object.\n * @param {Array|Arguments} [set2=[]] An array-like object.\n * @return {Array} A new, merged array.\n * @example\n *\n *      _concat([4, 5, 6], [1, 2, 3]); //=> [4, 5, 6, 1, 2, 3]\n */\nexport default function _concat(set1, set2) {\n  set1 = set1 || [];\n  set2 = set2 || [];\n  var idx;\n  var len1 = set1.length;\n  var len2 = set2.length;\n  var result = [];\n  idx = 0;\n\n  while (idx < len1) {\n    result[result.length] = set1[idx];\n    idx += 1;\n  }\n\n  idx = 0;\n\n  while (idx < len2) {\n    result[result.length] = set2[idx];\n    idx += 1;\n  }\n\n  return result;\n}", "export default function _arity(n, fn) {\n  /* eslint-disable no-unused-vars */\n  switch (n) {\n    case 0:\n      return function () {\n        return fn.apply(this, arguments);\n      };\n\n    case 1:\n      return function (a0) {\n        return fn.apply(this, arguments);\n      };\n\n    case 2:\n      return function (a0, a1) {\n        return fn.apply(this, arguments);\n      };\n\n    case 3:\n      return function (a0, a1, a2) {\n        return fn.apply(this, arguments);\n      };\n\n    case 4:\n      return function (a0, a1, a2, a3) {\n        return fn.apply(this, arguments);\n      };\n\n    case 5:\n      return function (a0, a1, a2, a3, a4) {\n        return fn.apply(this, arguments);\n      };\n\n    case 6:\n      return function (a0, a1, a2, a3, a4, a5) {\n        return fn.apply(this, arguments);\n      };\n\n    case 7:\n      return function (a0, a1, a2, a3, a4, a5, a6) {\n        return fn.apply(this, arguments);\n      };\n\n    case 8:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7) {\n        return fn.apply(this, arguments);\n      };\n\n    case 9:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n        return fn.apply(this, arguments);\n      };\n\n    case 10:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n        return fn.apply(this, arguments);\n      };\n\n    default:\n      throw new Error('First argument to _arity must be a non-negative integer no greater than ten');\n  }\n}", "import _arity from \"./_arity.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Internal curryN function.\n *\n * @private\n * @category Function\n * @param {Number} length The arity of the curried function.\n * @param {Array} received An array of arguments received thus far.\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curryN(length, received, fn) {\n  return function () {\n    var combined = [];\n    var argsIdx = 0;\n    var left = length;\n    var combinedIdx = 0;\n\n    while (combinedIdx < received.length || argsIdx < arguments.length) {\n      var result;\n\n      if (combinedIdx < received.length && (!_isPlaceholder(received[combinedIdx]) || argsIdx >= arguments.length)) {\n        result = received[combinedIdx];\n      } else {\n        result = arguments[argsIdx];\n        argsIdx += 1;\n      }\n\n      combined[combinedIdx] = result;\n\n      if (!_isPlaceholder(result)) {\n        left -= 1;\n      }\n\n      combinedIdx += 1;\n    }\n\n    return left <= 0 ? fn.apply(this, combined) : _arity(left, _curryN(length, combined, fn));\n  };\n}", "import _arity from \"./internal/_arity.js\";\nimport _curry1 from \"./internal/_curry1.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _curryN from \"./internal/_curryN.js\";\n/**\n * Returns a curried equivalent of the provided function, with the specified\n * arity. The curried function has two unusual capabilities. First, its\n * arguments needn't be provided one at a time. If `g` is `R.curryN(3, f)`, the\n * following are equivalent:\n *\n *   - `g(1)(2)(3)`\n *   - `g(1)(2, 3)`\n *   - `g(1, 2)(3)`\n *   - `g(1, 2, 3)`\n *\n * Secondly, the special placeholder value [`R.__`](#__) may be used to specify\n * \"gaps\", allowing partial application of any combination of arguments,\n * regardless of their positions. If `g` is as above and `_` is [`R.__`](#__),\n * the following are equivalent:\n *\n *   - `g(1, 2, 3)`\n *   - `g(_, 2, 3)(1)`\n *   - `g(_, _, 3)(1)(2)`\n *   - `g(_, _, 3)(1, 2)`\n *   - `g(_, 2)(1)(3)`\n *   - `g(_, 2)(1, 3)`\n *   - `g(_, 2)(_, 3)(1)`\n *\n * @func\n * @memberOf R\n * @since v0.5.0\n * @category Function\n * @sig Number -> (* -> a) -> (* -> a)\n * @param {Number} length The arity for the returned function.\n * @param {Function} fn The function to curry.\n * @return {Function} A new, curried function.\n * @see R.curry\n * @example\n *\n *      const sumArgs = (...args) => R.sum(args);\n *\n *      const curriedAddFourNumbers = R.curryN(4, sumArgs);\n *      const f = curriedAddFourNumbers(1, 2);\n *      const g = f(3);\n *      g(4); //=> 10\n */\n\nvar curryN =\n/*#__PURE__*/\n_curry2(function curryN(length, fn) {\n  if (length === 1) {\n    return _curry1(fn);\n  }\n\n  return _arity(length, _curryN(length, [], fn));\n});\n\nexport default curryN;", "import _concat from \"./internal/_concat.js\";\nimport _curry1 from \"./internal/_curry1.js\";\nimport curryN from \"./curryN.js\";\n/**\n * Creates a new list iteration function from an existing one by adding two new\n * parameters to its callback function: the current index, and the entire list.\n *\n * This would turn, for instance, [`R.map`](#map) function into one that\n * more closely resembles `Array.prototype.map`. Note that this will only work\n * for functions in which the iteration callback function is the first\n * parameter, and where the list is the last parameter. (This latter might be\n * unimportant if the list parameter is not used.)\n *\n * @func\n * @memberOf R\n * @since v0.15.0\n * @category Function\n * @category List\n * @sig (((a ...) -> b) ... -> [a] -> *) -> (((a ..., Int, [a]) -> b) ... -> [a] -> *)\n * @param {Function} fn A list iteration function that does not pass index or list to its callback\n * @return {Function} An altered list iteration function that passes (item, index, list) to its callback\n * @example\n *\n *      const mapIndexed = R.addIndex(R.map);\n *      mapIndexed((val, idx) => idx + '-' + val, ['f', 'o', 'o', 'b', 'a', 'r']);\n *      //=> ['0-f', '1-o', '2-o', '3-b', '4-a', '5-r']\n */\n\nvar addIndex =\n/*#__PURE__*/\n_curry1(function addIndex(fn) {\n  return curryN(fn.length, function () {\n    var idx = 0;\n    var origFn = arguments[0];\n    var list = arguments[arguments.length - 1];\n    var args = Array.prototype.slice.call(arguments, 0);\n\n    args[0] = function () {\n      var result = origFn.apply(this, _concat(arguments, [idx, list]));\n      idx += 1;\n      return result;\n    };\n\n    return fn.apply(this, args);\n  });\n});\n\nexport default addIndex;", "/**\n * Tests whether or not an object is an array.\n *\n * @private\n * @param {*} val The object to test.\n * @return {Boolean} `true` if `val` is an array, `false` otherwise.\n * @example\n *\n *      _isArray([]); //=> true\n *      _isArray(null); //=> false\n *      _isArray({}); //=> false\n */\nexport default Array.isArray || function _isArray(val) {\n  return val != null && val.length >= 0 && Object.prototype.toString.call(val) === '[object Array]';\n};", "export default function _isTransformer(obj) {\n  return obj != null && typeof obj['@@transducer/step'] === 'function';\n}", "import _isArray from \"./_isArray.js\";\nimport _isTransformer from \"./_isTransformer.js\";\n/**\n * Returns a function that dispatches with different strategies based on the\n * object in list position (last argument). If it is an array, executes [fn].\n * Otherwise, if it has a function with one of the given method names, it will\n * execute that function (functor case). Otherwise, if it is a transformer,\n * uses transducer created by [transducerC<PERSON>] to return a new transformer\n * (transducer case).\n * Otherwise, it will default to executing [fn].\n *\n * @private\n * @param {Array} methodNames properties to check for a custom implementation\n * @param {Function} transducerCreator transducer factory if object is transformer\n * @param {Function} fn default ramda implementation\n * @return {Function} A function that dispatches on object in list position\n */\n\nexport default function _dispatchable(methodNames, transducerCreator, fn) {\n  return function () {\n    if (arguments.length === 0) {\n      return fn();\n    }\n\n    var obj = arguments[arguments.length - 1];\n\n    if (!_isArray(obj)) {\n      var idx = 0;\n\n      while (idx < methodNames.length) {\n        if (typeof obj[methodNames[idx]] === 'function') {\n          return obj[methodNames[idx]].apply(obj, Array.prototype.slice.call(arguments, 0, -1));\n        }\n\n        idx += 1;\n      }\n\n      if (_isTransformer(obj)) {\n        var transducer = transducerCreator.apply(null, Array.prototype.slice.call(arguments, 0, -1));\n        return transducer(obj);\n      }\n    }\n\n    return fn.apply(this, arguments);\n  };\n}", "export default function _reduced(x) {\n  return x && x['@@transducer/reduced'] ? x : {\n    '@@transducer/value': x,\n    '@@transducer/reduced': true\n  };\n}", "export default {\n  init: function () {\n    return this.xf['@@transducer/init']();\n  },\n  result: function (result) {\n    return this.xf['@@transducer/result'](result);\n  }\n};", "import _curry2 from \"./_curry2.js\";\nimport _reduced from \"./_reduced.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XAll =\n/*#__PURE__*/\nfunction () {\n  function XAll(f, xf) {\n    this.xf = xf;\n    this.f = f;\n    this.all = true;\n  }\n\n  XAll.prototype['@@transducer/init'] = _xfBase.init;\n\n  XAll.prototype['@@transducer/result'] = function (result) {\n    if (this.all) {\n      result = this.xf['@@transducer/step'](result, true);\n    }\n\n    return this.xf['@@transducer/result'](result);\n  };\n\n  XAll.prototype['@@transducer/step'] = function (result, input) {\n    if (!this.f(input)) {\n      this.all = false;\n      result = _reduced(this.xf['@@transducer/step'](result, false));\n    }\n\n    return result;\n  };\n\n  return XAll;\n}();\n\nvar _xall =\n/*#__PURE__*/\n_curry2(function _xall(f, xf) {\n  return new XAll(f, xf);\n});\n\nexport default _xall;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _xall from \"./internal/_xall.js\";\n/**\n * Returns `true` if all elements of the list match the predicate, `false` if\n * there are any that don't.\n *\n * Dispatches to the `all` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig (a -> Boolean) -> [a] -> Boolean\n * @param {Function} fn The predicate function.\n * @param {Array} list The array to consider.\n * @return {<PERSON>olean} `true` if the predicate is satisfied by every element, `false`\n *         otherwise.\n * @see R.any, R.none, R.transduce\n * @example\n *\n *      const equals3 = R.equals(3);\n *      R.all(equals3)([3, 3, 3, 3]); //=> true\n *      R.all(equals3)([3, 3, 1, 3]); //=> false\n */\n\nvar all =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['all'], _xall, function all(fn, list) {\n  var idx = 0;\n\n  while (idx < list.length) {\n    if (!fn(list[idx])) {\n      return false;\n    }\n\n    idx += 1;\n  }\n\n  return true;\n}));\n\nexport default all;", "export default function _map(fn, functor) {\n  var idx = 0;\n  var len = functor.length;\n  var result = Array(len);\n\n  while (idx < len) {\n    result[idx] = fn(functor[idx]);\n    idx += 1;\n  }\n\n  return result;\n}", "export default function _isString(x) {\n  return Object.prototype.toString.call(x) === '[object String]';\n}", "import _curry1 from \"./_curry1.js\";\nimport _isArray from \"./_isArray.js\";\nimport _isString from \"./_isString.js\";\n/**\n * Tests whether or not an object is similar to an array.\n *\n * @private\n * @category Type\n * @category List\n * @sig * -> Boolean\n * @param {*} x The object to test.\n * @return {Boolean} `true` if `x` has a numeric length property and extreme indices defined; `false` otherwise.\n * @example\n *\n *      _isArrayLike([]); //=> true\n *      _isArrayLike(true); //=> false\n *      _isArrayLike({}); //=> false\n *      _isArrayLike({length: 10}); //=> false\n *      _isArrayLike({0: 'zero', 9: 'nine', length: 10}); //=> true\n *      _isArrayLike({nodeType: 1, length: 1}) // => false\n */\n\nvar _isArrayLike =\n/*#__PURE__*/\n_curry1(function isArrayLike(x) {\n  if (_isArray(x)) {\n    return true;\n  }\n\n  if (!x) {\n    return false;\n  }\n\n  if (typeof x !== 'object') {\n    return false;\n  }\n\n  if (_isString(x)) {\n    return false;\n  }\n\n  if (x.length === 0) {\n    return true;\n  }\n\n  if (x.length > 0) {\n    return x.hasOwnProperty(0) && x.hasOwnProperty(x.length - 1);\n  }\n\n  return false;\n});\n\nexport default _isArrayLike;", "var XWrap =\n/*#__PURE__*/\nfunction () {\n  function XWrap(fn) {\n    this.f = fn;\n  }\n\n  XWrap.prototype['@@transducer/init'] = function () {\n    throw new Error('init not implemented on XWrap');\n  };\n\n  XWrap.prototype['@@transducer/result'] = function (acc) {\n    return acc;\n  };\n\n  XWrap.prototype['@@transducer/step'] = function (acc, x) {\n    return this.f(acc, x);\n  };\n\n  return XWrap;\n}();\n\nexport default function _xwrap(fn) {\n  return new XWrap(fn);\n}", "import _arity from \"./internal/_arity.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Creates a function that is bound to a context.\n * Note: `R.bind` does not provide the additional argument-binding capabilities of\n * [Function.prototype.bind](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/bind).\n *\n * @func\n * @memberOf R\n * @since v0.6.0\n * @category Function\n * @category Object\n * @sig (* -> *) -> {*} -> (* -> *)\n * @param {Function} fn The function to bind to context\n * @param {Object} thisObj The context to bind `fn` to\n * @return {Function} A function that will execute in the context of `thisObj`.\n * @see R.partial\n * @example\n *\n *      const log = R.bind(console.log, console);\n *      R.pipe(R.assoc('a', 2), R.tap(log), R.assoc('a', 3))({a: 1}); //=> {a: 3}\n *      // logs {a: 2}\n * @symb R.bind(f, o)(a, b) = f.call(o, a, b)\n */\n\nvar bind =\n/*#__PURE__*/\n_curry2(function bind(fn, thisObj) {\n  return _arity(fn.length, function () {\n    return fn.apply(thisObj, arguments);\n  });\n});\n\nexport default bind;", "import _isArrayLike from \"./_isArrayLike.js\";\nimport _xwrap from \"./_xwrap.js\";\nimport bind from \"../bind.js\";\n\nfunction _arrayReduce(xf, acc, list) {\n  var idx = 0;\n  var len = list.length;\n\n  while (idx < len) {\n    acc = xf['@@transducer/step'](acc, list[idx]);\n\n    if (acc && acc['@@transducer/reduced']) {\n      acc = acc['@@transducer/value'];\n      break;\n    }\n\n    idx += 1;\n  }\n\n  return xf['@@transducer/result'](acc);\n}\n\nfunction _iterableReduce(xf, acc, iter) {\n  var step = iter.next();\n\n  while (!step.done) {\n    acc = xf['@@transducer/step'](acc, step.value);\n\n    if (acc && acc['@@transducer/reduced']) {\n      acc = acc['@@transducer/value'];\n      break;\n    }\n\n    step = iter.next();\n  }\n\n  return xf['@@transducer/result'](acc);\n}\n\nfunction _methodReduce(xf, acc, obj, methodName) {\n  return xf['@@transducer/result'](obj[methodName](bind(xf['@@transducer/step'], xf), acc));\n}\n\nvar symIterator = typeof Symbol !== 'undefined' ? Symbol.iterator : '@@iterator';\nexport default function _reduce(fn, acc, list) {\n  if (typeof fn === 'function') {\n    fn = _xwrap(fn);\n  }\n\n  if (_isArrayLike(list)) {\n    return _arrayReduce(fn, acc, list);\n  }\n\n  if (typeof list['fantasy-land/reduce'] === 'function') {\n    return _methodReduce(fn, acc, list, 'fantasy-land/reduce');\n  }\n\n  if (list[symIterator] != null) {\n    return _iterableReduce(fn, acc, list[symIterator]());\n  }\n\n  if (typeof list.next === 'function') {\n    return _iterableReduce(fn, acc, list);\n  }\n\n  if (typeof list.reduce === 'function') {\n    return _methodReduce(fn, acc, list, 'reduce');\n  }\n\n  throw new TypeError('reduce: list must be array or iterable');\n}", "import _curry2 from \"./_curry2.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XMap =\n/*#__PURE__*/\nfunction () {\n  function XMap(f, xf) {\n    this.xf = xf;\n    this.f = f;\n  }\n\n  XMap.prototype['@@transducer/init'] = _xfBase.init;\n  XMap.prototype['@@transducer/result'] = _xfBase.result;\n\n  XMap.prototype['@@transducer/step'] = function (result, input) {\n    return this.xf['@@transducer/step'](result, this.f(input));\n  };\n\n  return XMap;\n}();\n\nvar _xmap =\n/*#__PURE__*/\n_curry2(function _xmap(f, xf) {\n  return new XMap(f, xf);\n});\n\nexport default _xmap;", "export default function _has(prop, obj) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}", "import _has from \"./_has.js\";\nvar toString = Object.prototype.toString;\n\nvar _isArguments =\n/*#__PURE__*/\nfunction () {\n  return toString.call(arguments) === '[object Arguments]' ? function _isArguments(x) {\n    return toString.call(x) === '[object Arguments]';\n  } : function _isArguments(x) {\n    return _has('callee', x);\n  };\n}();\n\nexport default _isArguments;", "import _curry1 from \"./internal/_curry1.js\";\nimport _has from \"./internal/_has.js\";\nimport _isArguments from \"./internal/_isArguments.js\"; // cover IE < 9 keys issues\n\nvar hasEnumBug = !\n/*#__PURE__*/\n{\n  toString: null\n}.propertyIsEnumerable('toString');\nvar nonEnumerableProps = ['constructor', 'valueOf', 'isPrototypeOf', 'toString', 'propertyIsEnumerable', 'hasOwnProperty', 'toLocaleString']; // Safari bug\n\nvar hasArgsEnumBug =\n/*#__PURE__*/\nfunction () {\n  'use strict';\n\n  return arguments.propertyIsEnumerable('length');\n}();\n\nvar contains = function contains(list, item) {\n  var idx = 0;\n\n  while (idx < list.length) {\n    if (list[idx] === item) {\n      return true;\n    }\n\n    idx += 1;\n  }\n\n  return false;\n};\n/**\n * Returns a list containing the names of all the enumerable own properties of\n * the supplied object.\n * Note that the order of the output array is not guaranteed to be consistent\n * across different JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {k: v} -> [k]\n * @param {Object} obj The object to extract properties from\n * @return {Array} An array of the object's own properties.\n * @see R.keysIn, R.values, R.toPairs\n * @example\n *\n *      R.keys({a: 1, b: 2, c: 3}); //=> ['a', 'b', 'c']\n */\n\n\nvar keys = typeof Object.keys === 'function' && !hasArgsEnumBug ?\n/*#__PURE__*/\n_curry1(function keys(obj) {\n  return Object(obj) !== obj ? [] : Object.keys(obj);\n}) :\n/*#__PURE__*/\n_curry1(function keys(obj) {\n  if (Object(obj) !== obj) {\n    return [];\n  }\n\n  var prop, nIdx;\n  var ks = [];\n\n  var checkArgsLength = hasArgsEnumBug && _isArguments(obj);\n\n  for (prop in obj) {\n    if (_has(prop, obj) && (!checkArgsLength || prop !== 'length')) {\n      ks[ks.length] = prop;\n    }\n  }\n\n  if (hasEnumBug) {\n    nIdx = nonEnumerableProps.length - 1;\n\n    while (nIdx >= 0) {\n      prop = nonEnumerableProps[nIdx];\n\n      if (_has(prop, obj) && !contains(ks, prop)) {\n        ks[ks.length] = prop;\n      }\n\n      nIdx -= 1;\n    }\n  }\n\n  return ks;\n});\nexport default keys;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _map from \"./internal/_map.js\";\nimport _reduce from \"./internal/_reduce.js\";\nimport _xmap from \"./internal/_xmap.js\";\nimport curryN from \"./curryN.js\";\nimport keys from \"./keys.js\";\n/**\n * Takes a function and\n * a [functor](https://github.com/fantasyland/fantasy-land#functor),\n * applies the function to each of the functor's values, and returns\n * a functor of the same shape.\n *\n * Ramda provides suitable `map` implementations for `Array` and `Object`,\n * so this function may be applied to `[1, 2, 3]` or `{x: 1, y: 2, z: 3}`.\n *\n * Dispatches to the `map` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * Also treats functions as functors and will compose them together.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Functor f => (a -> b) -> f a -> f b\n * @param {Function} fn The function to be called on every element of the input `list`.\n * @param {Array} list The list to be iterated over.\n * @return {Array} The new list.\n * @see R.transduce, R.addIndex, R.pluck, R.project\n * @example\n *\n *      const double = x => x * 2;\n *\n *      R.map(double, [1, 2, 3]); //=> [2, 4, 6]\n *\n *      R.map(double, {x: 1, y: 2, z: 3}); //=> {x: 2, y: 4, z: 6}\n * @symb R.map(f, [a, b]) = [f(a), f(b)]\n * @symb R.map(f, { x: a, y: b }) = { x: f(a), y: f(b) }\n * @symb R.map(f, functor_o) = functor_o.map(f)\n */\n\nvar map =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['fantasy-land/map', 'map'], _xmap, function map(fn, functor) {\n  switch (Object.prototype.toString.call(functor)) {\n    case '[object Function]':\n      return curryN(functor.length, function () {\n        return fn.call(this, functor.apply(this, arguments));\n      });\n\n    case '[object Object]':\n      return _reduce(function (acc, key) {\n        acc[key] = fn(functor[key]);\n        return acc;\n      }, {}, keys(functor));\n\n    default:\n      return _map(fn, functor);\n  }\n}));\n\nexport default map;", "/**\n * Determine if the passed argument is an integer.\n *\n * @private\n * @param {*} n\n * @category Type\n * @return {Boolean}\n */\nexport default Number.isInteger || function _isInteger(n) {\n  return n << 0 === n;\n};", "import _curry2 from \"./internal/_curry2.js\";\nimport _isString from \"./internal/_isString.js\";\n/**\n * Returns the nth element of the given list or string. If n is negative the\n * element at index length + n is returned.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Number -> [a] -> a | Undefined\n * @sig Number -> String -> String\n * @param {Number} offset\n * @param {*} list\n * @return {*}\n * @example\n *\n *      const list = ['foo', 'bar', 'baz', 'quux'];\n *      R.nth(1, list); //=> 'bar'\n *      R.nth(-1, list); //=> 'quux'\n *      R.nth(-99, list); //=> undefined\n *\n *      R.nth(2, 'abc'); //=> 'c'\n *      R.nth(3, 'abc'); //=> ''\n * @symb R.nth(-1, [a, b, c]) = c\n * @symb R.nth(0, [a, b, c]) = a\n * @symb R.nth(1, [a, b, c]) = b\n */\n\nvar nth =\n/*#__PURE__*/\n_curry2(function nth(offset, list) {\n  var idx = offset < 0 ? list.length + offset : offset;\n  return _isString(list) ? list.charAt(idx) : list[idx];\n});\n\nexport default nth;", "import _curry2 from \"./internal/_curry2.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport nth from \"./nth.js\";\n/**\n * Returns a function that when supplied an object returns the indicated\n * property of that object, if it exists.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig Idx -> {s: a} -> a | Undefined\n * @param {String|Number} p The property name or array index\n * @param {Object} obj The object to query\n * @return {*} The value at `obj.p`.\n * @see R.path, R.props, R.pluck, R.project, R.nth\n * @example\n *\n *      R.prop('x', {x: 100}); //=> 100\n *      R.prop('x', {}); //=> undefined\n *      R.prop(0, [100]); //=> 100\n *      R.compose(R.inc, R.prop('x'))({ x: 3 }) //=> 4\n */\n\nvar prop =\n/*#__PURE__*/\n_curry2(function prop(p, obj) {\n  if (obj == null) {\n    return;\n  }\n\n  return _isInteger(p) ? nth(p, obj) : obj[p];\n});\n\nexport default prop;", "import _curry2 from \"./internal/_curry2.js\";\nimport map from \"./map.js\";\nimport prop from \"./prop.js\";\n/**\n * Returns a new list by plucking the same named property off all objects in\n * the list supplied.\n *\n * `pluck` will work on\n * any [functor](https://github.com/fantasyland/fantasy-land#functor) in\n * addition to arrays, as it is equivalent to `R.map(R.prop(k), f)`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Functor f => k -> f {k: v} -> f v\n * @param {Number|String} key The key name to pluck off of each object.\n * @param {Array} f The array or functor to consider.\n * @return {Array} The list of values for the given key.\n * @see R.project, R.prop, R.props\n * @example\n *\n *      var getAges = R.pluck('age');\n *      getAges([{name: 'fred', age: 29}, {name: 'wilma', age: 27}]); //=> [29, 27]\n *\n *      <PERSON><PERSON>pluck(0, [[1, 2], [3, 4]]);               //=> [1, 3]\n *      <PERSON>.pluck('val', {a: {val: 3}, b: {val: 5}}); //=> {a: 3, b: 5}\n * @symb R.pluck('x', [{x: 1, y: 2}, {x: 3, y: 4}, {x: 5, y: 6}]) = [1, 3, 5]\n * @symb R.pluck(0, [[1, 2], [3, 4], [5, 6]]) = [1, 3, 5]\n */\n\nvar pluck =\n/*#__PURE__*/\n_curry2(function pluck(p, list) {\n  return map(prop(p), list);\n});\n\nexport default pluck;", "import _curry3 from \"./internal/_curry3.js\";\nimport _reduce from \"./internal/_reduce.js\";\n/**\n * Returns a single item by iterating through the list, successively calling\n * the iterator function and passing it an accumulator value and the current\n * value from the array, and then passing the result to the next call.\n *\n * The iterator function receives two values: *(acc, value)*. It may use\n * [`R.reduced`](#reduced) to shortcut the iteration.\n *\n * The arguments' order of [`reduceRight`](#reduceRight)'s iterator function\n * is *(value, acc)*.\n *\n * Note: `R.reduce` does not skip deleted or unassigned indices (sparse\n * arrays), unlike the native `Array.prototype.reduce` method. For more details\n * on this behavior, see:\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce#Description\n *\n * Dispatches to the `reduce` method of the third argument, if present. When\n * doing so, it is up to the user to handle the [`R.reduced`](#reduced)\n * shortcuting, as this is not implemented by `reduce`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig ((a, b) -> a) -> a -> [b] -> a\n * @param {Function} fn The iterator function. Receives two values, the accumulator and the\n *        current element from the array.\n * @param {*} acc The accumulator value.\n * @param {Array} list The list to iterate over.\n * @return {*} The final, accumulated value.\n * @see R.reduced, R.addIndex, R.reduceRight\n * @example\n *\n *      R.reduce(R.subtract, 0, [1, 2, 3, 4]) // => ((((0 - 1) - 2) - 3) - 4) = -10\n *      //          -               -10\n *      //         / \\              / \\\n *      //        -   4           -6   4\n *      //       / \\              / \\\n *      //      -   3   ==>     -3   3\n *      //     / \\              / \\\n *      //    -   2           -1   2\n *      //   / \\              / \\\n *      //  0   1            0   1\n *\n * @symb R.reduce(f, a, [b, c, d]) = f(f(f(a, b), c), d)\n */\n\nvar reduce =\n/*#__PURE__*/\n_curry3(_reduce);\n\nexport default reduce;", "import _curry2 from \"./_curry2.js\";\nimport _reduced from \"./_reduced.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XAny =\n/*#__PURE__*/\nfunction () {\n  function XAny(f, xf) {\n    this.xf = xf;\n    this.f = f;\n    this.any = false;\n  }\n\n  XAny.prototype['@@transducer/init'] = _xfBase.init;\n\n  XAny.prototype['@@transducer/result'] = function (result) {\n    if (!this.any) {\n      result = this.xf['@@transducer/step'](result, false);\n    }\n\n    return this.xf['@@transducer/result'](result);\n  };\n\n  XAny.prototype['@@transducer/step'] = function (result, input) {\n    if (this.f(input)) {\n      this.any = true;\n      result = _reduced(this.xf['@@transducer/step'](result, true));\n    }\n\n    return result;\n  };\n\n  return XAny;\n}();\n\nvar _xany =\n/*#__PURE__*/\n_curry2(function _xany(f, xf) {\n  return new XAny(f, xf);\n});\n\nexport default _xany;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _xany from \"./internal/_xany.js\";\n/**\n * Returns `true` if at least one of the elements of the list match the predicate,\n * `false` otherwise.\n *\n * Dispatches to the `any` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig (a -> Boolean) -> [a] -> Boolean\n * @param {Function} fn The predicate function.\n * @param {Array} list The array to consider.\n * @return {Boolean} `true` if the predicate is satisfied by at least one element, `false`\n *         otherwise.\n * @see R.all, R.none, R.transduce\n * @example\n *\n *      const lessThan0 = R.flip(R.lt)(0);\n *      const lessThan2 = R.flip(R.lt)(2);\n *      R.any(lessThan0)([1, 2]); //=> false\n *      R.any(lessThan2)([1, 2]); //=> true\n */\n\nvar any =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['any'], _xany, function any(fn, list) {\n  var idx = 0;\n\n  while (idx < list.length) {\n    if (fn(list[idx])) {\n      return true;\n    }\n\n    idx += 1;\n  }\n\n  return false;\n}));\n\nexport default any;", "import _concat from \"./internal/_concat.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns a new list containing the contents of the given list, followed by\n * the given element.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig a -> [a] -> [a]\n * @param {*} el The element to add to the end of the new list.\n * @param {Array} list The list of elements to add a new item to.\n *        list.\n * @return {Array} A new list containing the elements of the old list followed by `el`.\n * @see R.prepend\n * @example\n *\n *      R.append('tests', ['write', 'more']); //=> ['write', 'more', 'tests']\n *      R.append('tests', []); //=> ['tests']\n *      R.append(['tests'], ['write', 'more']); //=> ['write', 'more', ['tests']]\n */\n\nvar append =\n/*#__PURE__*/\n_curry2(function append(el, list) {\n  return _concat(list, [el]);\n});\n\nexport default append;", "import _curry1 from \"./internal/_curry1.js\";\nimport keys from \"./keys.js\";\n/**\n * Returns a list of all the enumerable own properties of the supplied object.\n * Note that the order of the output array is not guaranteed across different\n * JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {k: v} -> [v]\n * @param {Object} obj The object to extract values from\n * @return {Array} An array of the values of the object's own properties.\n * @see R.valuesIn, R.keys, R.toPairs\n * @example\n *\n *      R.values({a: 1, b: 2, c: 3}); //=> [1, 2, 3]\n */\n\nvar values =\n/*#__PURE__*/\n_curry1(function values(obj) {\n  var props = keys(obj);\n  var len = props.length;\n  var vals = [];\n  var idx = 0;\n\n  while (idx < len) {\n    vals[idx] = obj[props[idx]];\n    idx += 1;\n  }\n\n  return vals;\n});\n\nexport default values;", "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Checks if the input value is `null` or `undefined`.\n *\n * @func\n * @memberOf R\n * @since v0.9.0\n * @category Type\n * @sig * -> Boolean\n * @param {*} x The value to test.\n * @return {<PERSON><PERSON>an} `true` if `x` is `undefined` or `null`, otherwise `false`.\n * @example\n *\n *      R.isNil(null); //=> true\n *      R.isNil(undefined); //=> true\n *      R.isNil(0); //=> false\n *      R.isNil([]); //=> false\n */\n\nvar isNil =\n/*#__PURE__*/\n_curry1(function isNil(x) {\n  return x == null;\n});\n\nexport default isNil;", "import _curry3 from \"./internal/_curry3.js\";\nimport _has from \"./internal/_has.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport _assoc from \"./internal/_assoc.js\";\nimport isNil from \"./isNil.js\";\n/**\n * Makes a shallow clone of an object, setting or overriding the nodes required\n * to create the given path, and placing the specific value at the tail end of\n * that path. Note that this copies and flattens prototype properties onto the\n * new object as well. All non-primitive properties are copied by reference.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> a -> {a} -> {a}\n * @param {Array} path the path to set\n * @param {*} val The new value\n * @param {Object} obj The object to clone\n * @return {Object} A new object equivalent to the original except along the specified path.\n * @see R.dissocPath\n * @example\n *\n *      R.assocPath(['a', 'b', 'c'], 42, {a: {b: {c: 0}}}); //=> {a: {b: {c: 42}}}\n *\n *      // Any missing or non-object keys in path will be overridden\n *      R.assocPath(['a', 'b', 'c'], 42, {a: 5}); //=> {a: {b: {c: 42}}}\n */\n\nvar assocPath =\n/*#__PURE__*/\n_curry3(function assocPath(path, val, obj) {\n  if (path.length === 0) {\n    return val;\n  }\n\n  var idx = path[0];\n\n  if (path.length > 1) {\n    var nextObj = !isNil(obj) && _has(idx, obj) ? obj[idx] : _isInteger(path[1]) ? [] : {};\n    val = assocPath(Array.prototype.slice.call(path, 1), val, nextObj);\n  }\n\n  return _assoc(idx, val, obj);\n});\n\nexport default assocPath;", "import _isArray from \"./_isArray.js\";\nimport _isInteger from \"./_isInteger.js\";\n/**\n * Makes a shallow clone of an object, setting or overriding the specified\n * property with the given value. Note that this copies and flattens prototype\n * properties onto the new object as well. All non-primitive properties are\n * copied by reference.\n *\n * @private\n * @param {String|Number} prop The property name to set\n * @param {*} val The new value\n * @param {Object|Array} obj The object to clone\n * @return {Object|Array} A new object equivalent to the original except for the changed property.\n */\n\nexport default function _assoc(prop, val, obj) {\n  if (_isInteger(prop) && _isArray(obj)) {\n    var arr = [].concat(obj);\n    arr[prop] = val;\n    return arr;\n  }\n\n  var result = {};\n\n  for (var p in obj) {\n    result[p] = obj[p];\n  }\n\n  result[prop] = val;\n  return result;\n}", "import _curry3 from \"./internal/_curry3.js\";\nimport assocPath from \"./assocPath.js\";\n/**\n * Makes a shallow clone of an object, setting or overriding the specified\n * property with the given value. Note that this copies and flattens prototype\n * properties onto the new object as well. All non-primitive properties are\n * copied by reference.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Object\n * @typedefn Idx = String | Int\n * @sig Idx -> a -> {k: v} -> {k: v}\n * @param {String|Number} prop The property name to set\n * @param {*} val The new value\n * @param {Object} obj The object to clone\n * @return {Object} A new object equivalent to the original except for the changed property.\n * @see <PERSON>.dissoc, <PERSON>.pick\n * @example\n *\n *      R.assoc('c', 3, {a: 1, b: 2}); //=> {a: 1, b: 2, c: 3}\n */\n\nvar assoc =\n/*#__PURE__*/\n_curry3(function assoc(prop, val, obj) {\n  return assocPath([prop], val, obj);\n});\n\nexport default assoc;", "import _curry2 from \"./_curry2.js\";\nimport _flatCat from \"./_flatCat.js\";\nimport map from \"../map.js\";\n\nvar _xchain =\n/*#__PURE__*/\n_curry2(function _xchain(f, xf) {\n  return map(f, _flatCat(xf));\n});\n\nexport default _xchain;", "import _forceReduced from \"./_forceReduced.js\";\nimport _isArrayLike from \"./_isArrayLike.js\";\nimport _reduce from \"./_reduce.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar preservingReduced = function (xf) {\n  return {\n    '@@transducer/init': _xfBase.init,\n    '@@transducer/result': function (result) {\n      return xf['@@transducer/result'](result);\n    },\n    '@@transducer/step': function (result, input) {\n      var ret = xf['@@transducer/step'](result, input);\n      return ret['@@transducer/reduced'] ? _forceReduced(ret) : ret;\n    }\n  };\n};\n\nvar _flatCat = function _xcat(xf) {\n  var rxf = preservingReduced(xf);\n  return {\n    '@@transducer/init': _xfBase.init,\n    '@@transducer/result': function (result) {\n      return rxf['@@transducer/result'](result);\n    },\n    '@@transducer/step': function (result, input) {\n      return !_isArrayLike(input) ? _reduce(rxf, result, [input]) : _reduce(rxf, result, input);\n    }\n  };\n};\n\nexport default _flatCat;", "export default function _forceReduced(x) {\n  return {\n    '@@transducer/value': x,\n    '@@transducer/reduced': true\n  };\n}", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _makeFlat from \"./internal/_makeFlat.js\";\nimport _xchain from \"./internal/_xchain.js\";\nimport map from \"./map.js\";\n/**\n * `chain` maps a function over a list and concatenates the results. `chain`\n * is also known as `flatMap` in some libraries.\n *\n * Dispatches to the `chain` method of the second argument, if present,\n * according to the [FantasyLand Chain spec](https://github.com/fantasyland/fantasy-land#chain).\n *\n * If second argument is a function, `chain(f, g)(x)` is equivalent to `f(g(x), x)`.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category List\n * @sig Chain m => (a -> m b) -> m a -> m b\n * @param {Function} fn The function to map with\n * @param {Array} list The list to map over\n * @return {Array} The result of flat-mapping `list` with `fn`\n * @example\n *\n *      const duplicate = n => [n, n];\n *      R.chain(duplicate, [1, 2, 3]); //=> [1, 1, 2, 2, 3, 3]\n *\n *      R.chain(R.append, R.head)([1, 2, 3]); //=> [1, 2, 3, 1]\n */\n\nvar chain =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['fantasy-land/chain', 'chain'], _xchain, function chain(fn, monad) {\n  if (typeof monad === 'function') {\n    return function (x) {\n      return fn(monad(x))(x);\n    };\n  }\n\n  return _makeFlat(false)(map(fn, monad));\n}));\n\nexport default chain;", "import _isArrayLike from \"./_isArrayLike.js\";\n/**\n * `_makeFlat` is a helper function that returns a one-level or fully recursive\n * function based on the flag passed in.\n *\n * @private\n */\n\nexport default function _makeFlat(recursive) {\n  return function flatt(list) {\n    var value, jlen, j;\n    var result = [];\n    var idx = 0;\n    var ilen = list.length;\n\n    while (idx < ilen) {\n      if (_isArrayLike(list[idx])) {\n        value = recursive ? flatt(list[idx]) : list[idx];\n        j = 0;\n        jlen = value.length;\n\n        while (j < jlen) {\n          result[result.length] = value[j];\n          j += 1;\n        }\n      } else {\n        result[result.length] = list[idx];\n      }\n\n      idx += 1;\n    }\n\n    return result;\n  };\n}", "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Gives a single-word string description of the (native) type of a value,\n * returning such answers as 'Object', 'Number', 'Array', or 'Null'. Does not\n * attempt to distinguish user Object types any further, reporting them all as\n * 'Object'.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Type\n * @sig (* -> {*}) -> String\n * @param {*} val The value to test\n * @return {String}\n * @example\n *\n *      R.type({}); //=> \"Object\"\n *      R.type(1); //=> \"Number\"\n *      R.type(false); //=> \"Boolean\"\n *      R.type('s'); //=> \"String\"\n *      R.type(null); //=> \"Null\"\n *      R.type([]); //=> \"Array\"\n *      R.type(/[A-z]/); //=> \"RegExp\"\n *      R.type(() => {}); //=> \"Function\"\n *      R.type(undefined); //=> \"Undefined\"\n */\n\nvar type =\n/*#__PURE__*/\n_curry1(function type(val) {\n  return val === null ? 'Null' : val === undefined ? 'Undefined' : Object.prototype.toString.call(val).slice(8, -1);\n});\n\nexport default type;", "import _cloneRegExp from \"./_cloneRegExp.js\";\nimport type from \"../type.js\";\n/**\n * Copies an object.\n *\n * @private\n * @param {*} value The value to be copied\n * @param {Array} refFrom Array containing the source references\n * @param {Array} refTo Array containing the copied source references\n * @param {Boolean} deep Whether or not to perform deep cloning.\n * @return {*} The copied value.\n */\n\nexport default function _clone(value, refFrom, refTo, deep) {\n  var copy = function copy(copiedValue) {\n    var len = refFrom.length;\n    var idx = 0;\n\n    while (idx < len) {\n      if (value === refFrom[idx]) {\n        return refTo[idx];\n      }\n\n      idx += 1;\n    }\n\n    refFrom[idx] = value;\n    refTo[idx] = copiedValue;\n\n    for (var key in value) {\n      if (value.hasOwnProperty(key)) {\n        copiedValue[key] = deep ? _clone(value[key], refFrom, refTo, true) : value[key];\n      }\n    }\n\n    return copiedValue;\n  };\n\n  switch (type(value)) {\n    case 'Object':\n      return copy(Object.create(Object.getPrototypeOf(value)));\n\n    case 'Array':\n      return copy([]);\n\n    case 'Date':\n      return new Date(value.valueOf());\n\n    case 'RegExp':\n      return _cloneRegExp(value);\n\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'BigInt64Array':\n    case 'BigUint64Array':\n      return value.slice();\n\n    default:\n      return value;\n  }\n}", "export default function _cloneRegExp(pattern) {\n  return new RegExp(pattern.source, (pattern.global ? 'g' : '') + (pattern.ignoreCase ? 'i' : '') + (pattern.multiline ? 'm' : '') + (pattern.sticky ? 'y' : '') + (pattern.unicode ? 'u' : ''));\n}", "import _clone from \"./internal/_clone.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n/**\n * Creates a deep copy of the source that can be used in place of the source\n * object without retaining any references to it.\n * The source object may contain (nested) `Array`s and `Object`s,\n * `Number`s, `String`s, `Boolean`s and `Date`s.\n * `Function`s are assigned by reference rather than copied.\n *\n * Dispatches to a `clone` method if present.\n *\n * Note that if the source object has multiple nodes that share a reference,\n * the returned object will have the same structure, but the references will\n * be pointed to the location within the cloned value.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {*} -> {*}\n * @param {*} value The object or array to clone\n * @return {*} A deeply cloned copy of `val`\n * @example\n *\n *      const objects = [{}, {}, {}];\n *      const objectsClone = R.clone(objects);\n *      objects === objectsClone; //=> false\n *      objects[0] === objectsClone[0]; //=> false\n */\n\nvar clone =\n/*#__PURE__*/\n_curry1(function clone(value) {\n  return value != null && typeof value.clone === 'function' ? value.clone() : _clone(value, [], [], true);\n});\n\nexport default clone;", "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Makes a comparator function out of a function that reports whether the first\n * element is less than the second.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig ((a, b) -> Boolean) -> ((a, b) -> Number)\n * @param {Function} pred A predicate function of arity two which will return `true` if the first argument\n * is less than the second, `false` otherwise\n * @return {Function} A Function :: a -> b -> Int that returns `-1` if a < b, `1` if b < a, otherwise `0`\n * @example\n *\n *      const byAge = R.comparator((a, b) => a.age < b.age);\n *      const people = [\n *        { name: '<PERSON>', age: 70 },\n *        { name: '<PERSON>', age: 78 },\n *        { name: '<PERSON>', age: 62 },\n *      ];\n *      const peopleByIncreasingAge = R.sort(byAge, people);\n *        //=> [{ name: '<PERSON>', age: 62 },{ name: '<PERSON>', age: 70 }, { name: '<PERSON>', age: 78 }]\n */\n\nvar comparator =\n/*#__PURE__*/\n_curry1(function comparator(pred) {\n  return function (a, b) {\n    return pred(a, b) ? -1 : pred(b, a) ? 1 : 0;\n  };\n});\n\nexport default comparator;", "export default function _pipe(f, g) {\n  return function () {\n    return g.call(this, f.apply(this, arguments));\n  };\n}", "import _isArray from \"./_isArray.js\";\n/**\n * This checks whether a function has a [methodname] function. If it isn't an\n * array it will execute that function otherwise it will default to the ramda\n * implementation.\n *\n * @private\n * @param {Function} fn ramda implementation\n * @param {String} methodname property to check for a custom implementation\n * @return {Object} Whatever the return value of the method is.\n */\n\nexport default function _checkForMethod(methodname, fn) {\n  return function () {\n    var length = arguments.length;\n\n    if (length === 0) {\n      return fn();\n    }\n\n    var obj = arguments[length - 1];\n    return _isArray(obj) || typeof obj[methodname] !== 'function' ? fn.apply(this, arguments) : obj[methodname].apply(obj, Array.prototype.slice.call(arguments, 0, length - 1));\n  };\n}", "import _checkForMethod from \"./internal/_checkForMethod.js\";\nimport _curry3 from \"./internal/_curry3.js\";\n/**\n * Returns the elements of the given list or string (or object with a `slice`\n * method) from `fromIndex` (inclusive) to `toIndex` (exclusive).\n *\n * Dispatches to the `slice` method of the third argument, if present.\n *\n * @func\n * @memberOf R\n * @since v0.1.4\n * @category List\n * @sig Number -> Number -> [a] -> [a]\n * @sig Number -> Number -> String -> String\n * @param {Number} fromIndex The start index (inclusive).\n * @param {Number} toIndex The end index (exclusive).\n * @param {*} list\n * @return {*}\n * @example\n *\n *      R.slice(1, 3, ['a', 'b', 'c', 'd']);        //=> ['b', 'c']\n *      R.slice(1, Infinity, ['a', 'b', 'c', 'd']); //=> ['b', 'c', 'd']\n *      R.slice(0, -1, ['a', 'b', 'c', 'd']);       //=> ['a', 'b', 'c']\n *      R.slice(-3, -1, ['a', 'b', 'c', 'd']);      //=> ['b', 'c']\n *      R.slice(0, 3, 'ramda');                     //=> 'ram'\n */\n\nvar slice =\n/*#__PURE__*/\n_curry3(\n/*#__PURE__*/\n_checkForMethod('slice', function slice(fromIndex, toIndex, list) {\n  return Array.prototype.slice.call(list, fromIndex, toIndex);\n}));\n\nexport default slice;", "import _checkForMethod from \"./internal/_checkForMethod.js\";\nimport _curry1 from \"./internal/_curry1.js\";\nimport slice from \"./slice.js\";\n/**\n * Returns all but the first element of the given list or string (or object\n * with a `tail` method).\n *\n * Dispatches to the `slice` method of the first argument, if present.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> [a]\n * @sig String -> String\n * @param {*} list\n * @return {*}\n * @see R.head, R.init, R.last\n * @example\n *\n *      R.tail([1, 2, 3]);  //=> [2, 3]\n *      R.tail([1, 2]);     //=> [2]\n *      R.tail([1]);        //=> []\n *      R.tail([]);         //=> []\n *\n *      R.tail('abc');  //=> 'bc'\n *      R.tail('ab');   //=> 'b'\n *      R.tail('a');    //=> ''\n *      R.tail('');     //=> ''\n */\n\nvar tail =\n/*#__PURE__*/\n_curry1(\n/*#__PURE__*/\n_checkForMethod('tail',\n/*#__PURE__*/\nslice(1, Infinity)));\n\nexport default tail;", "import _arity from \"./internal/_arity.js\";\nimport _pipe from \"./internal/_pipe.js\";\nimport reduce from \"./reduce.js\";\nimport tail from \"./tail.js\";\n/**\n * Performs left-to-right function composition. The first argument may have\n * any arity; the remaining arguments must be unary.\n *\n * In some libraries this function is named `sequence`.\n *\n * **Note:** The result of pipe is not automatically curried.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig (((a, b, ..., n) -> o), (o -> p), ..., (x -> y), (y -> z)) -> ((a, b, ..., n) -> z)\n * @param {...Function} functions\n * @return {Function}\n * @see R.compose\n * @example\n *\n *      const f = R.pipe(Math.pow, R.negate, R.inc);\n *\n *      f(3, 4); // -(3^4) + 1\n * @symb R.pipe(f, g, h)(a, b) = h(g(f(a, b)))\n * @symb R.pipe(f, g, h)(a)(b) = h(g(f(a)))(b)\n */\n\nexport default function pipe() {\n  if (arguments.length === 0) {\n    throw new Error('pipe requires at least one argument');\n  }\n\n  return _arity(arguments[0].length, reduce(_pipe, arguments[0], tail(arguments)));\n}", "import _curry1 from \"./internal/_curry1.js\";\nimport _isString from \"./internal/_isString.js\";\n/**\n * Returns a new list or string with the elements or characters in reverse\n * order.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> [a]\n * @sig String -> String\n * @param {Array|String} list\n * @return {Array|String}\n * @example\n *\n *      R.reverse([1, 2, 3]);  //=> [3, 2, 1]\n *      R.reverse([1, 2]);     //=> [2, 1]\n *      R.reverse([1]);        //=> [1]\n *      R.reverse([]);         //=> []\n *\n *      R.reverse('abc');      //=> 'cba'\n *      R.reverse('ab');       //=> 'ba'\n *      R.reverse('a');        //=> 'a'\n *      R.reverse('');         //=> ''\n */\n\nvar reverse =\n/*#__PURE__*/\n_curry1(function reverse(list) {\n  return _isString(list) ? list.split('').reverse().join('') : Array.prototype.slice.call(list, 0).reverse();\n});\n\nexport default reverse;", "import pipe from \"./pipe.js\";\nimport reverse from \"./reverse.js\";\n/**\n * Performs right-to-left function composition. The last argument may have\n * any arity; the remaining arguments must be unary.\n *\n * **Note:** The result of compose is not automatically curried.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig ((y -> z), (x -> y), ..., (o -> p), ((a, b, ..., n) -> o)) -> ((a, b, ..., n) -> z)\n * @param {...Function} ...functions The functions to compose\n * @return {Function}\n * @see R.pipe\n * @example\n *\n *      const classyGreeting = (firstName, lastName) => \"The name's \" + lastName + \", \" + firstName + \" \" + lastName\n *      const yellGreeting = R.compose(R.toUpper, classyGreeting);\n *      yellGreeting('James', 'Bond'); //=> \"THE NAME'S BOND, JAMES BOND\"\n *\n *      R.compose(Math.abs, R.add(1), <PERSON><PERSON>multiply(2))(-4) //=> 7\n *\n * @symb R.compose(f, g, h)(a, b) = f(g(h(a, b)))\n * @symb R.compose(f, g, h)(a)(b) = f(g(h(a)))(b)\n */\n\nexport default function compose() {\n  if (arguments.length === 0) {\n    throw new Error('compose requires at least one argument');\n  }\n\n  return pipe.apply(this, reverse(arguments));\n}", "export default function _isFunction(x) {\n  var type = Object.prototype.toString.call(x);\n  return type === '[object Function]' || type === '[object AsyncFunction]' || type === '[object GeneratorFunction]' || type === '[object AsyncGeneratorFunction]';\n}", "export default function _arrayFromIterator(iter) {\n  var list = [];\n  var next;\n\n  while (!(next = iter.next()).done) {\n    list.push(next.value);\n  }\n\n  return list;\n}", "export default function _includesWith(pred, x, list) {\n  var idx = 0;\n  var len = list.length;\n\n  while (idx < len) {\n    if (pred(x, list[idx])) {\n      return true;\n    }\n\n    idx += 1;\n  }\n\n  return false;\n}", "// Based on https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction _objectIs(a, b) {\n  // SameValue algorithm\n  if (a === b) {\n    // Steps 1-5, 7-10\n    // Steps 6.b-6.e: +0 != -0\n    return a !== 0 || 1 / a === 1 / b;\n  } else {\n    // Step 6.a: NaN == NaN\n    return a !== a && b !== b;\n  }\n}\n\nexport default typeof Object.is === 'function' ? Object.is : _objectIs;", "import _arrayFromIterator from \"./_arrayFromIterator.js\";\nimport _includesWith from \"./_includesWith.js\";\nimport _functionName from \"./_functionName.js\";\nimport _has from \"./_has.js\";\nimport _objectIs from \"./_objectIs.js\";\nimport keys from \"../keys.js\";\nimport type from \"../type.js\";\n/**\n * private _uniqContentEquals function.\n * That function is checking equality of 2 iterator contents with 2 assumptions\n * - iterators lengths are the same\n * - iterators values are unique\n *\n * false-positive result will be returned for comparison of, e.g.\n * - [1,2,3] and [1,2,3,4]\n * - [1,1,1] and [1,2,3]\n * */\n\nfunction _uniqContentEquals(aIterator, bIterator, stackA, stackB) {\n  var a = _arrayFromIterator(aIterator);\n\n  var b = _arrayFromIterator(bIterator);\n\n  function eq(_a, _b) {\n    return _equals(_a, _b, stackA.slice(), stackB.slice());\n  } // if *a* array contains any element that is not included in *b*\n\n\n  return !_includesWith(function (b, aItem) {\n    return !_includesWith(eq, aItem, b);\n  }, b, a);\n}\n\nexport default function _equals(a, b, stackA, stackB) {\n  if (_objectIs(a, b)) {\n    return true;\n  }\n\n  var typeA = type(a);\n\n  if (typeA !== type(b)) {\n    return false;\n  }\n\n  if (typeof a['fantasy-land/equals'] === 'function' || typeof b['fantasy-land/equals'] === 'function') {\n    return typeof a['fantasy-land/equals'] === 'function' && a['fantasy-land/equals'](b) && typeof b['fantasy-land/equals'] === 'function' && b['fantasy-land/equals'](a);\n  }\n\n  if (typeof a.equals === 'function' || typeof b.equals === 'function') {\n    return typeof a.equals === 'function' && a.equals(b) && typeof b.equals === 'function' && b.equals(a);\n  }\n\n  switch (typeA) {\n    case 'Arguments':\n    case 'Array':\n    case 'Object':\n      if (typeof a.constructor === 'function' && _functionName(a.constructor) === 'Promise') {\n        return a === b;\n      }\n\n      break;\n\n    case 'Boolean':\n    case 'Number':\n    case 'String':\n      if (!(typeof a === typeof b && _objectIs(a.valueOf(), b.valueOf()))) {\n        return false;\n      }\n\n      break;\n\n    case 'Date':\n      if (!_objectIs(a.valueOf(), b.valueOf())) {\n        return false;\n      }\n\n      break;\n\n    case 'Error':\n      return a.name === b.name && a.message === b.message;\n\n    case 'RegExp':\n      if (!(a.source === b.source && a.global === b.global && a.ignoreCase === b.ignoreCase && a.multiline === b.multiline && a.sticky === b.sticky && a.unicode === b.unicode)) {\n        return false;\n      }\n\n      break;\n  }\n\n  var idx = stackA.length - 1;\n\n  while (idx >= 0) {\n    if (stackA[idx] === a) {\n      return stackB[idx] === b;\n    }\n\n    idx -= 1;\n  }\n\n  switch (typeA) {\n    case 'Map':\n      if (a.size !== b.size) {\n        return false;\n      }\n\n      return _uniqContentEquals(a.entries(), b.entries(), stackA.concat([a]), stackB.concat([b]));\n\n    case 'Set':\n      if (a.size !== b.size) {\n        return false;\n      }\n\n      return _uniqContentEquals(a.values(), b.values(), stackA.concat([a]), stackB.concat([b]));\n\n    case 'Arguments':\n    case 'Array':\n    case 'Object':\n    case 'Boolean':\n    case 'Number':\n    case 'String':\n    case 'Date':\n    case 'Error':\n    case 'RegExp':\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'ArrayBuffer':\n      break;\n\n    default:\n      // Values of other types are only equal if identical.\n      return false;\n  }\n\n  var keysA = keys(a);\n\n  if (keysA.length !== keys(b).length) {\n    return false;\n  }\n\n  var extendedStackA = stackA.concat([a]);\n  var extendedStackB = stackB.concat([b]);\n  idx = keysA.length - 1;\n\n  while (idx >= 0) {\n    var key = keysA[idx];\n\n    if (!(_has(key, b) && _equals(b[key], a[key], extendedStackA, extendedStackB))) {\n      return false;\n    }\n\n    idx -= 1;\n  }\n\n  return true;\n}", "export default function _functionName(f) {\n  // String(x => x) evaluates to \"x => x\", so the pattern may not match.\n  var match = String(f).match(/^function (\\w*)/);\n  return match == null ? '' : match[1];\n}", "import _curry2 from \"./internal/_curry2.js\";\nimport _equals from \"./internal/_equals.js\";\n/**\n * Returns `true` if its arguments are equivalent, `false` otherwise. Handles\n * cyclical data structures.\n *\n * Dispatches symmetrically to the `equals` methods of both arguments, if\n * present.\n *\n * @func\n * @memberOf R\n * @since v0.15.0\n * @category Relation\n * @sig a -> b -> Boolean\n * @param {*} a\n * @param {*} b\n * @return {Boolean}\n * @example\n *\n *      R.equals(1, 1); //=> true\n *      R.equals(1, '1'); //=> false\n *      R.equals([1, 2, 3], [1, 2, 3]); //=> true\n *\n *      const a = {}; a.v = a;\n *      const b = {}; b.v = b;\n *      R.equals(a, b); //=> true\n */\n\nvar equals =\n/*#__PURE__*/\n_curry2(function equals(a, b) {\n  return _equals(a, b, [], []);\n});\n\nexport default equals;", "import equals from \"../equals.js\";\nexport default function _indexOf(list, a, idx) {\n  var inf, item; // Array.prototype.indexOf doesn't exist below IE9\n\n  if (typeof list.indexOf === 'function') {\n    switch (typeof a) {\n      case 'number':\n        if (a === 0) {\n          // manually crawl the list to distinguish between +0 and -0\n          inf = 1 / a;\n\n          while (idx < list.length) {\n            item = list[idx];\n\n            if (item === 0 && 1 / item === inf) {\n              return idx;\n            }\n\n            idx += 1;\n          }\n\n          return -1;\n        } else if (a !== a) {\n          // NaN\n          while (idx < list.length) {\n            item = list[idx];\n\n            if (typeof item === 'number' && item !== item) {\n              return idx;\n            }\n\n            idx += 1;\n          }\n\n          return -1;\n        } // non-zero numbers can utilise Set\n\n\n        return list.indexOf(a, idx);\n      // all these types can utilise Set\n\n      case 'string':\n      case 'boolean':\n      case 'function':\n      case 'undefined':\n        return list.indexOf(a, idx);\n\n      case 'object':\n        if (a === null) {\n          // null can utilise Set\n          return list.indexOf(a, idx);\n        }\n\n    }\n  } // anything else not covered above, defer to R.equals\n\n\n  while (idx < list.length) {\n    if (equals(list[idx], a)) {\n      return idx;\n    }\n\n    idx += 1;\n  }\n\n  return -1;\n}", "import _indexOf from \"./_indexOf.js\";\nexport default function _includes(a, list) {\n  return _indexOf(list, a, 0) >= 0;\n}", "export default function _quote(s) {\n  var escaped = s.replace(/\\\\/g, '\\\\\\\\').replace(/[\\b]/g, '\\\\b') // \\b matches word boundary; [\\b] matches backspace\n  .replace(/\\f/g, '\\\\f').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/\\t/g, '\\\\t').replace(/\\v/g, '\\\\v').replace(/\\0/g, '\\\\0');\n  return '\"' + escaped.replace(/\"/g, '\\\\\"') + '\"';\n}", "/**\n * Polyfill from <https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toISOString>.\n */\nvar pad = function pad(n) {\n  return (n < 10 ? '0' : '') + n;\n};\n\nvar _toISOString = typeof Date.prototype.toISOString === 'function' ? function _toISOString(d) {\n  return d.toISOString();\n} : function _toISOString(d) {\n  return d.getUTCFullYear() + '-' + pad(d.getUTCMonth() + 1) + '-' + pad(d.getUTCDate()) + 'T' + pad(d.getUTCHours()) + ':' + pad(d.getUTCMinutes()) + ':' + pad(d.getUTCSeconds()) + '.' + (d.getUTCMilliseconds() / 1000).toFixed(3).slice(2, 5) + 'Z';\n};\n\nexport default _toISOString;", "export default function _filter(fn, list) {\n  var idx = 0;\n  var len = list.length;\n  var result = [];\n\n  while (idx < len) {\n    if (fn(list[idx])) {\n      result[result.length] = list[idx];\n    }\n\n    idx += 1;\n  }\n\n  return result;\n}", "import _curry2 from \"./_curry2.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XFilter =\n/*#__PURE__*/\nfunction () {\n  function XFilter(f, xf) {\n    this.xf = xf;\n    this.f = f;\n  }\n\n  XFilter.prototype['@@transducer/init'] = _xfBase.init;\n  XFilter.prototype['@@transducer/result'] = _xfBase.result;\n\n  XFilter.prototype['@@transducer/step'] = function (result, input) {\n    return this.f(input) ? this.xf['@@transducer/step'](result, input) : result;\n  };\n\n  return XFilter;\n}();\n\nvar _xfilter =\n/*#__PURE__*/\n_curry2(function _xfilter(f, xf) {\n  return new XFilter(f, xf);\n});\n\nexport default _xfilter;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _filter from \"./internal/_filter.js\";\nimport _isObject from \"./internal/_isObject.js\";\nimport _reduce from \"./internal/_reduce.js\";\nimport _xfilter from \"./internal/_xfilter.js\";\nimport keys from \"./keys.js\";\n/**\n * Takes a predicate and a `Filterable`, and returns a new filterable of the\n * same type containing the members of the given filterable which satisfy the\n * given predicate. Filterable objects include plain objects or any object\n * that has a filter method such as `Array`.\n *\n * Dispatches to the `filter` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Filterable f => (a -> <PERSON><PERSON>an) -> f a -> f a\n * @param {Function} pred\n * @param {Array} filterable\n * @return {Array} Filterable\n * @see R.reject, <PERSON>.transduce, <PERSON>.addIndex\n * @example\n *\n *      const isEven = n => n % 2 === 0;\n *\n *      R.filter(isEven, [1, 2, 3, 4]); //=> [2, 4]\n *\n *      R.filter(isEven, {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, d: 4}\n */\n\nvar filter =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['fantasy-land/filter', 'filter'], _xfilter, function (pred, filterable) {\n  return _isObject(filterable) ? _reduce(function (acc, key) {\n    if (pred(filterable[key])) {\n      acc[key] = filterable[key];\n    }\n\n    return acc;\n  }, {}, keys(filterable)) : // else\n  _filter(pred, filterable);\n}));\n\nexport default filter;", "export default function _isObject(x) {\n  return Object.prototype.toString.call(x) === '[object Object]';\n}", "import _complement from \"./internal/_complement.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport filter from \"./filter.js\";\n/**\n * The complement of [`filter`](#filter).\n *\n * Acts as a transducer if a transformer is given in list position. Filterable\n * objects include plain objects or any object that has a filter method such\n * as `Array`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Filterable f => (a -> <PERSON><PERSON><PERSON>) -> f a -> f a\n * @param {Function} pred\n * @param {Array} filterable\n * @return {Array}\n * @see R.filter, R.transduce, R.addIndex\n * @example\n *\n *      const isOdd = (n) => n % 2 !== 0;\n *\n *      R.reject(isOdd, [1, 2, 3, 4]); //=> [2, 4]\n *\n *      R.reject(isOdd, {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, d: 4}\n */\n\nvar reject =\n/*#__PURE__*/\n_curry2(function reject(pred, filterable) {\n  return filter(_complement(pred), filterable);\n});\n\nexport default reject;", "export default function _complement(f) {\n  return function () {\n    return !f.apply(this, arguments);\n  };\n}", "import _includes from \"./_includes.js\";\nimport _map from \"./_map.js\";\nimport _quote from \"./_quote.js\";\nimport _toISOString from \"./_toISOString.js\";\nimport keys from \"../keys.js\";\nimport reject from \"../reject.js\";\nexport default function _toString(x, seen) {\n  var recur = function recur(y) {\n    var xs = seen.concat([x]);\n    return _includes(y, xs) ? '<Circular>' : _toString(y, xs);\n  }; //  mapPairs :: (Object, [String]) -> [String]\n\n\n  var mapPairs = function (obj, keys) {\n    return _map(function (k) {\n      return _quote(k) + ': ' + recur(obj[k]);\n    }, keys.slice().sort());\n  };\n\n  switch (Object.prototype.toString.call(x)) {\n    case '[object Arguments]':\n      return '(function() { return arguments; }(' + _map(recur, x).join(', ') + '))';\n\n    case '[object Array]':\n      return '[' + _map(recur, x).concat(mapPairs(x, reject(function (k) {\n        return /^\\d+$/.test(k);\n      }, keys(x)))).join(', ') + ']';\n\n    case '[object Boolean]':\n      return typeof x === 'object' ? 'new Boolean(' + recur(x.valueOf()) + ')' : x.toString();\n\n    case '[object Date]':\n      return 'new Date(' + (isNaN(x.valueOf()) ? recur(NaN) : _quote(_toISOString(x))) + ')';\n\n    case '[object Null]':\n      return 'null';\n\n    case '[object Number]':\n      return typeof x === 'object' ? 'new Number(' + recur(x.valueOf()) + ')' : 1 / x === -Infinity ? '-0' : x.toString(10);\n\n    case '[object String]':\n      return typeof x === 'object' ? 'new String(' + recur(x.valueOf()) + ')' : _quote(x);\n\n    case '[object Undefined]':\n      return 'undefined';\n\n    default:\n      if (typeof x.toString === 'function') {\n        var repr = x.toString();\n\n        if (repr !== '[object Object]') {\n          return repr;\n        }\n      }\n\n      return '{' + mapPairs(x, keys(x)).join(', ') + '}';\n  }\n}", "import _curry1 from \"./internal/_curry1.js\";\nimport _toString from \"./internal/_toString.js\";\n/**\n * Returns the string representation of the given value. `eval`'ing the output\n * should result in a value equivalent to the input value. Many of the built-in\n * `toString` methods do not satisfy this requirement.\n *\n * If the given value is an `[object Object]` with a `toString` method other\n * than `Object.prototype.toString`, this method is invoked with no arguments\n * to produce the return value. This means user-defined constructor functions\n * can provide a suitable `toString` method. For example:\n *\n *     function Point(x, y) {\n *       this.x = x;\n *       this.y = y;\n *     }\n *\n *     Point.prototype.toString = function() {\n *       return 'new Point(' + this.x + ', ' + this.y + ')';\n *     };\n *\n *     R.toString(new Point(1, 2)); //=> 'new Point(1, 2)'\n *\n * @func\n * @memberOf R\n * @since v0.14.0\n * @category String\n * @sig * -> String\n * @param {*} val\n * @return {String}\n * @example\n *\n *      R.toString(42); //=> '42'\n *      R.toString('abc'); //=> '\"abc\"'\n *      R.toString([1, 2, 3]); //=> '[1, 2, 3]'\n *      R.toString({foo: 1, bar: 2, baz: 3}); //=> '{\"bar\": 2, \"baz\": 3, \"foo\": 1}'\n *      R.toString(new Date('2001-02-03T04:05:06Z')); //=> 'new Date(\"2001-02-03T04:05:06.000Z\")'\n */\n\nvar toString =\n/*#__PURE__*/\n_curry1(function toString(val) {\n  return _toString(val, []);\n});\n\nexport default toString;", "import _curry2 from \"./internal/_curry2.js\";\nimport _isArray from \"./internal/_isArray.js\";\nimport _isFunction from \"./internal/_isFunction.js\";\nimport _isString from \"./internal/_isString.js\";\nimport toString from \"./toString.js\";\n/**\n * Returns the result of concatenating the given lists or strings.\n *\n * Note: `R.concat` expects both arguments to be of the same type,\n * unlike the native `Array.prototype.concat` method. It will throw\n * an error if you `concat` an Array with a non-Array value.\n *\n * Dispatches to the `concat` method of the first argument, if present.\n * Can also concatenate two members of a [fantasy-land\n * compatible semigroup](https://github.com/fantasyland/fantasy-land#semigroup).\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> [a] -> [a]\n * @sig String -> String -> String\n * @param {Array|String} firstList The first list\n * @param {Array|String} secondList The second list\n * @return {Array|String} A list consisting of the elements of `firstList` followed by the elements of\n * `secondList`.\n *\n * @example\n *\n *      R.concat('ABC', 'DEF'); // 'ABCDEF'\n *      R.concat([4, 5, 6], [1, 2, 3]); //=> [4, 5, 6, 1, 2, 3]\n *      R.concat([], []); //=> []\n */\n\nvar concat =\n/*#__PURE__*/\n_curry2(function concat(a, b) {\n  if (_isArray(a)) {\n    if (_isArray(b)) {\n      return a.concat(b);\n    }\n\n    throw new TypeError(toString(b) + ' is not an array');\n  }\n\n  if (_isString(a)) {\n    if (_isString(b)) {\n      return a + b;\n    }\n\n    throw new TypeError(toString(b) + ' is not a string');\n  }\n\n  if (a != null && _isFunction(a['fantasy-land/concat'])) {\n    return a['fantasy-land/concat'](b);\n  }\n\n  if (a != null && _isFunction(a.concat)) {\n    return a.concat(b);\n  }\n\n  throw new TypeError(toString(a) + ' does not have a method named \"concat\" or \"fantasy-land/concat\"');\n});\n\nexport default concat;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns the larger of its two arguments.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Relation\n * @sig Ord a => a -> a -> a\n * @param {*} a\n * @param {*} b\n * @return {*}\n * @see R.maxBy, R.min\n * @example\n *\n *      R.max(789, 123); //=> 789\n *      R.max('a', 'b'); //=> 'b'\n */\n\nvar max =\n/*#__PURE__*/\n_curry2(function max(a, b) {\n  return b > a ? b : a;\n});\n\nexport default max;", "import _includes from \"./_includes.js\";\n\nvar _Set =\n/*#__PURE__*/\nfunction () {\n  function _Set() {\n    /* globals Set */\n    this._nativeSet = typeof Set === 'function' ? new Set() : null;\n    this._items = {};\n  }\n\n  // until we figure out why jsdo<PERSON> chokes on this\n  // @param item The item to add to the Set\n  // @returns {boolean} true if the item did not exist prior, otherwise false\n  //\n  _Set.prototype.add = function (item) {\n    return !hasOrAdd(item, true, this);\n  }; //\n  // @param item The item to check for existence in the Set\n  // @returns {boolean} true if the item exists in the Set, otherwise false\n  //\n\n\n  _Set.prototype.has = function (item) {\n    return hasOrAdd(item, false, this);\n  }; //\n  // Combines the logic for checking whether an item is a member of the set and\n  // for adding a new item to the set.\n  //\n  // @param item       The item to check or add to the Set instance.\n  // @param shouldAdd  If true, the item will be added to the set if it doesn't\n  //                   already exist.\n  // @param set        The set instance to check or add to.\n  // @return {boolean} true if the item already existed, otherwise false.\n  //\n\n\n  return _Set;\n}();\n\nfunction hasOrAdd(item, shouldAdd, set) {\n  var type = typeof item;\n  var prevSize, newSize;\n\n  switch (type) {\n    case 'string':\n    case 'number':\n      // distinguish between +0 and -0\n      if (item === 0 && 1 / item === -Infinity) {\n        if (set._items['-0']) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items['-0'] = true;\n          }\n\n          return false;\n        }\n      } // these types can all utilise the native Set\n\n\n      if (set._nativeSet !== null) {\n        if (shouldAdd) {\n          prevSize = set._nativeSet.size;\n\n          set._nativeSet.add(item);\n\n          newSize = set._nativeSet.size;\n          return newSize === prevSize;\n        } else {\n          return set._nativeSet.has(item);\n        }\n      } else {\n        if (!(type in set._items)) {\n          if (shouldAdd) {\n            set._items[type] = {};\n            set._items[type][item] = true;\n          }\n\n          return false;\n        } else if (item in set._items[type]) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items[type][item] = true;\n          }\n\n          return false;\n        }\n      }\n\n    case 'boolean':\n      // set._items['boolean'] holds a two element array\n      // representing [ falseExists, trueExists ]\n      if (type in set._items) {\n        var bIdx = item ? 1 : 0;\n\n        if (set._items[type][bIdx]) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items[type][bIdx] = true;\n          }\n\n          return false;\n        }\n      } else {\n        if (shouldAdd) {\n          set._items[type] = item ? [false, true] : [true, false];\n        }\n\n        return false;\n      }\n\n    case 'function':\n      // compare functions for reference equality\n      if (set._nativeSet !== null) {\n        if (shouldAdd) {\n          prevSize = set._nativeSet.size;\n\n          set._nativeSet.add(item);\n\n          newSize = set._nativeSet.size;\n          return newSize === prevSize;\n        } else {\n          return set._nativeSet.has(item);\n        }\n      } else {\n        if (!(type in set._items)) {\n          if (shouldAdd) {\n            set._items[type] = [item];\n          }\n\n          return false;\n        }\n\n        if (!_includes(item, set._items[type])) {\n          if (shouldAdd) {\n            set._items[type].push(item);\n          }\n\n          return false;\n        }\n\n        return true;\n      }\n\n    case 'undefined':\n      if (set._items[type]) {\n        return true;\n      } else {\n        if (shouldAdd) {\n          set._items[type] = true;\n        }\n\n        return false;\n      }\n\n    case 'object':\n      if (item === null) {\n        if (!set._items['null']) {\n          if (shouldAdd) {\n            set._items['null'] = true;\n          }\n\n          return false;\n        }\n\n        return true;\n      }\n\n    /* falls through */\n\n    default:\n      // reduce the search size of heterogeneous sets by creating buckets\n      // for each type.\n      type = Object.prototype.toString.call(item);\n\n      if (!(type in set._items)) {\n        if (shouldAdd) {\n          set._items[type] = [item];\n        }\n\n        return false;\n      } // scan through all previously applied items\n\n\n      if (!_includes(item, set._items[type])) {\n        if (shouldAdd) {\n          set._items[type].push(item);\n        }\n\n        return false;\n      }\n\n      return true;\n  }\n} // A simple Set type that honours R.equals semantics\n\n\nexport default _Set;", "import nth from \"./nth.js\";\n/**\n * Returns the last element of the given list or string.\n *\n * @func\n * @memberOf R\n * @since v0.1.4\n * @category List\n * @sig [a] -> a | Undefined\n * @sig String -> String\n * @param {*} list\n * @return {*}\n * @see R.init, R.head, R.tail\n * @example\n *\n *      R.last(['fi', 'fo', 'fum']); //=> 'fum'\n *      R.last([]); //=> undefined\n *\n *      R.last('abc'); //=> 'c'\n *      R.last(''); //=> ''\n */\n\nvar last =\n/*#__PURE__*/\nnth(-1);\nexport default last;", "import _curry2 from \"./_curry2.js\";\nimport _reduced from \"./_reduced.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XFind =\n/*#__PURE__*/\nfunction () {\n  function XFind(f, xf) {\n    this.xf = xf;\n    this.f = f;\n    this.found = false;\n  }\n\n  XFind.prototype['@@transducer/init'] = _xfBase.init;\n\n  XFind.prototype['@@transducer/result'] = function (result) {\n    if (!this.found) {\n      result = this.xf['@@transducer/step'](result, void 0);\n    }\n\n    return this.xf['@@transducer/result'](result);\n  };\n\n  XFind.prototype['@@transducer/step'] = function (result, input) {\n    if (this.f(input)) {\n      this.found = true;\n      result = _reduced(this.xf['@@transducer/step'](result, input));\n    }\n\n    return result;\n  };\n\n  return XFind;\n}();\n\nvar _xfind =\n/*#__PURE__*/\n_curry2(function _xfind(f, xf) {\n  return new XFind(f, xf);\n});\n\nexport default _xfind;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _xfind from \"./internal/_xfind.js\";\n/**\n * Returns the first element of the list which matches the predicate, or\n * `undefined` if no element matches.\n *\n * Dispatches to the `find` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig (a -> Boolean) -> [a] -> a | undefined\n * @param {Function} fn The predicate function used to determine if the element is the\n *        desired one.\n * @param {Array} list The array to consider.\n * @return {Object} The element found, or `undefined`.\n * @see R.transduce\n * @example\n *\n *      const xs = [{a: 1}, {a: 2}, {a: 3}];\n *      R.find(R.propEq('a', 2))(xs); //=> {a: 2}\n *      R.find(R.propEq('a', 4))(xs); //=> undefined\n */\n\nvar find =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['find'], _xfind, function find(fn, list) {\n  var idx = 0;\n  var len = list.length;\n\n  while (idx < len) {\n    if (fn(list[idx])) {\n      return list[idx];\n    }\n\n    idx += 1;\n  }\n}));\n\nexport default find;", "import _curry2 from \"./_curry2.js\";\nimport _reduced from \"./_reduced.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XFindIndex =\n/*#__PURE__*/\nfunction () {\n  function XFindIndex(f, xf) {\n    this.xf = xf;\n    this.f = f;\n    this.idx = -1;\n    this.found = false;\n  }\n\n  XFindIndex.prototype['@@transducer/init'] = _xfBase.init;\n\n  XFindIndex.prototype['@@transducer/result'] = function (result) {\n    if (!this.found) {\n      result = this.xf['@@transducer/step'](result, -1);\n    }\n\n    return this.xf['@@transducer/result'](result);\n  };\n\n  XFindIndex.prototype['@@transducer/step'] = function (result, input) {\n    this.idx += 1;\n\n    if (this.f(input)) {\n      this.found = true;\n      result = _reduced(this.xf['@@transducer/step'](result, this.idx));\n    }\n\n    return result;\n  };\n\n  return XFindIndex;\n}();\n\nvar _xfindIndex =\n/*#__PURE__*/\n_curry2(function _xfindIndex(f, xf) {\n  return new XFindIndex(f, xf);\n});\n\nexport default _xfindIndex;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _xfindIndex from \"./internal/_xfindIndex.js\";\n/**\n * Returns the index of the first element of the list which matches the\n * predicate, or `-1` if no element matches.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.1\n * @category List\n * @sig (a -> Boolean) -> [a] -> Number\n * @param {Function} fn The predicate function used to determine if the element is the\n * desired one.\n * @param {Array} list The array to consider.\n * @return {Number} The index of the element found, or `-1`.\n * @see R.transduce, R.indexOf\n * @example\n *\n *      const xs = [{a: 1}, {a: 2}, {a: 3}];\n *      R.findIndex(R.propEq('a', 2))(xs); //=> 1\n *      R.findIndex(R.propEq('a', 4))(xs); //=> -1\n */\n\nvar findIndex =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable([], _xfindIndex, function findIndex(fn, list) {\n  var idx = 0;\n  var len = list.length;\n\n  while (idx < len) {\n    if (fn(list[idx])) {\n      return idx;\n    }\n\n    idx += 1;\n  }\n\n  return -1;\n}));\n\nexport default findIndex;", "import _curry2 from \"./_curry2.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XFindLast =\n/*#__PURE__*/\nfunction () {\n  function XFindLast(f, xf) {\n    this.xf = xf;\n    this.f = f;\n  }\n\n  XFindLast.prototype['@@transducer/init'] = _xfBase.init;\n\n  XFindLast.prototype['@@transducer/result'] = function (result) {\n    return this.xf['@@transducer/result'](this.xf['@@transducer/step'](result, this.last));\n  };\n\n  XFindLast.prototype['@@transducer/step'] = function (result, input) {\n    if (this.f(input)) {\n      this.last = input;\n    }\n\n    return result;\n  };\n\n  return XFindLast;\n}();\n\nvar _xfindLast =\n/*#__PURE__*/\n_curry2(function _xfindLast(f, xf) {\n  return new XFindLast(f, xf);\n});\n\nexport default _xfindLast;", "import _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _xfindLast from \"./internal/_xfindLast.js\";\n/**\n * Returns the last element of the list which matches the predicate, or\n * `undefined` if no element matches.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.1\n * @category List\n * @sig (a -> Boolean) -> [a] -> a | undefined\n * @param {Function} fn The predicate function used to determine if the element is the\n * desired one.\n * @param {Array} list The array to consider.\n * @return {Object} The element found, or `undefined`.\n * @see R.transduce\n * @example\n *\n *      const xs = [{a: 1, b: 0}, {a:1, b: 1}];\n *      R.findLast(R.propEq('a', 1))(xs); //=> {a: 1, b: 1}\n *      R.findLast(R.propEq('a', 4))(xs); //=> undefined\n */\n\nvar findLast =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable([], _xfindLast, function findLast(fn, list) {\n  var idx = list.length - 1;\n\n  while (idx >= 0) {\n    if (fn(list[idx])) {\n      return list[idx];\n    }\n\n    idx -= 1;\n  }\n}));\n\nexport default findLast;", "import _checkForMethod from \"./internal/_checkForMethod.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Iterate over an input `list`, calling a provided function `fn` for each\n * element in the list.\n *\n * `fn` receives one argument: *(value)*.\n *\n * Note: `R.forEach` does not skip deleted or unassigned indices (sparse\n * arrays), unlike the native `Array.prototype.forEach` method. For more\n * details on this behavior, see:\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach#Description\n *\n * Also note that, unlike `Array.prototype.forEach`, <PERSON><PERSON>'s `forEach` returns\n * the original array. In some libraries this function is named `each`.\n *\n * Dispatches to the `forEach` method of the second argument, if present.\n *\n * @func\n * @memberOf R\n * @since v0.1.1\n * @category List\n * @sig (a -> *) -> [a] -> [a]\n * @param {Function} fn The function to invoke. Receives one argument, `value`.\n * @param {Array} list The list to iterate over.\n * @return {Array} The original list.\n * @see R.addIndex\n * @example\n *\n *      const printXPlusFive = x => console.log(x + 5);\n *      R.forEach(printXPlusFive, [1, 2, 3]); //=> [1, 2, 3]\n *      // logs 6\n *      // logs 7\n *      // logs 8\n * @symb R.forEach(f, [a, b, c]) = [a, b, c]\n */\n\nvar forEach =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_checkForMethod('forEach', function forEach(fn, list) {\n  var len = list.length;\n  var idx = 0;\n\n  while (idx < len) {\n    fn(list[idx]);\n    idx += 1;\n  }\n\n  return list;\n}));\n\nexport default forEach;", "import _curry2 from \"./internal/_curry2.js\";\nimport _has from \"./internal/_has.js\";\nimport isNil from \"./isNil.js\";\n/**\n * Returns whether or not a path exists in an object. Only the object's\n * own properties are checked.\n *\n * @func\n * @memberOf R\n * @since v0.26.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> {a} -> Boolean\n * @param {Array} path The path to use.\n * @param {Object} obj The object to check the path in.\n * @return {Boolean} Whether the path exists.\n * @see R.has\n * @example\n *\n *      R.hasPath(['a', 'b'], {a: {b: 2}});         // => true\n *      R.hasPath(['a', 'b'], {a: {b: undefined}}); // => true\n *      R.hasPath(['a', 'b'], {a: {c: 2}});         // => false\n *      R.hasPath(['a', 'b'], {});                  // => false\n */\n\nvar hasPath =\n/*#__PURE__*/\n_curry2(function hasPath(_path, obj) {\n  if (_path.length === 0 || isNil(obj)) {\n    return false;\n  }\n\n  var val = obj;\n  var idx = 0;\n\n  while (idx < _path.length) {\n    if (!isNil(val) && _has(_path[idx], val)) {\n      val = val[_path[idx]];\n      idx += 1;\n    } else {\n      return false;\n    }\n  }\n\n  return true;\n});\n\nexport default hasPath;", "import _curry2 from \"./internal/_curry2.js\";\nimport hasPath from \"./hasPath.js\";\n/**\n * Returns whether or not an object has an own property with the specified name\n *\n * @func\n * @memberOf R\n * @since v0.7.0\n * @category Object\n * @sig s -> {s: x} -> Boolean\n * @param {String} prop The name of the property to check for.\n * @param {Object} obj The object to query.\n * @return {Boolean} Whether the property exists.\n * @example\n *\n *      const hasName = R.has('name');\n *      hasName({name: 'alice'});   //=> true\n *      hasName({name: 'bob'});     //=> true\n *      hasName({});                //=> false\n *\n *      const point = {x: 0, y: 0};\n *      const pointHas = R.has(R.__, point);\n *      pointHas('x');  //=> true\n *      pointHas('y');  //=> true\n *      pointHas('z');  //=> false\n */\n\nvar has =\n/*#__PURE__*/\n_curry2(function has(prop, obj) {\n  return hasPath([prop], obj);\n});\n\nexport default has;", "import nth from \"./nth.js\";\n/**\n * Returns the first element of the given list or string. In some libraries\n * this function is named `first`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> a | Undefined\n * @sig String -> String\n * @param {Array|String} list\n * @return {*}\n * @see R.tail, R.init, R.last\n * @example\n *\n *      R.head(['fi', 'fo', 'fum']); //=> 'fi'\n *      R.head([]); //=> undefined\n *\n *      R.head('abc'); //=> 'a'\n *      R.head(''); //=> ''\n */\n\nvar head =\n/*#__PURE__*/\nnth(0);\nexport default head;", "import _curry3 from \"./internal/_curry3.js\";\nimport curryN from \"./curryN.js\";\n/**\n * Creates a function that will process either the `onTrue` or the `onFalse`\n * function depending upon the result of the `condition` predicate.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Logic\n * @sig (*... -> <PERSON><PERSON><PERSON>) -> (*... -> *) -> (*... -> *) -> (*... -> *)\n * @param {Function} condition A predicate function\n * @param {Function} onTrue A function to invoke when the `condition` evaluates to a truthy value.\n * @param {Function} onFalse A function to invoke when the `condition` evaluates to a falsy value.\n * @return {Function} A new function that will process either the `onTrue` or the `onFalse`\n *                    function depending upon the result of the `condition` predicate.\n * @see R.unless, R.when, R.cond\n * @example\n *\n *      const incCount = R.ifElse(\n *        R.has('count'),\n *        <PERSON>.over(<PERSON><PERSON>('count'), <PERSON>.inc),\n *        <PERSON>.assoc('count', 1)\n *      );\n *      incCount({ count: 1 }); //=> { count: 2 }\n *      incCount({});           //=> { count: 1 }\n */\n\nvar ifElse =\n/*#__PURE__*/\n_curry3(function ifElse(condition, onTrue, onFalse) {\n  return curryN(Math.max(condition.length, onTrue.length, onFalse.length), function _ifElse() {\n    return condition.apply(this, arguments) ? onTrue.apply(this, arguments) : onFalse.apply(this, arguments);\n  });\n});\n\nexport default ifElse;", "import _includes from \"./internal/_includes.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns `true` if the specified value is equal, in [`<PERSON>.equals`](#equals)\n * terms, to at least one element of the given list; `false` otherwise.\n * Also works with strings.\n *\n * @func\n * @memberOf R\n * @since v0.26.0\n * @category List\n * @sig a -> [a] -> Boolean\n * @param {Object} a The item to compare against.\n * @param {Array} list The array to consider.\n * @return {Boolean} `true` if an equivalent item is in the list, `false` otherwise.\n * @see R.any\n * @example\n *\n *      R.includes(3, [1, 2, 3]); //=> true\n *      R.includes(4, [1, 2, 3]); //=> false\n *      R.includes({ name: '<PERSON>' }, [{ name: '<PERSON>' }]); //=> true\n *      R.includes([42], [[42]]); //=> true\n *      R.includes('ba', 'banana'); //=>true\n */\n\nvar includes =\n/*#__PURE__*/\n_curry2(_includes);\n\nexport default includes;", "import _curry2 from \"./internal/_curry2.js\";\nimport _indexOf from \"./internal/_indexOf.js\";\nimport _isArray from \"./internal/_isArray.js\";\n/**\n * Returns the position of the first occurrence of an item in an array, or -1\n * if the item is not included in the array. [`R.equals`](#equals) is used to\n * determine equality.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig a -> [a] -> Number\n * @param {*} target The item to find.\n * @param {Array} xs The array to search in.\n * @return {Number} the index of the target, or -1 if the target is not found.\n * @see R.lastIndexOf, R.findIndex\n * @example\n *\n *      R.indexOf(3, [1,2,3,4]); //=> 2\n *      R.indexOf(10, [1,2,3,4]); //=> -1\n */\n\nvar indexOf =\n/*#__PURE__*/\n_curry2(function indexOf(target, xs) {\n  return typeof xs.indexOf === 'function' && !_isArray(xs) ? xs.indexOf(target) : _indexOf(xs, target, 0);\n});\n\nexport default indexOf;", "import _curry3 from \"./internal/_curry3.js\";\n/**\n * Inserts the sub-list into the list, at the specified `index`. _Note that this is not\n * destructive_: it returns a copy of the list with the changes.\n * <small>No lists have been harmed in the application of this function.</small>\n *\n * @func\n * @memberOf R\n * @since v0.9.0\n * @category List\n * @sig Number -> [a] -> [a] -> [a]\n * @param {Number} index The position to insert the sub-list\n * @param {Array} elts The sub-list to insert into the Array\n * @param {Array} list The list to insert the sub-list into\n * @return {Array} A new Array with `elts` inserted starting at `index`.\n * @example\n *\n *      R.insertAll(2, ['x','y','z'], [1,2,3,4]); //=> [1,2,'x','y','z',3,4]\n */\n\nvar insertAll =\n/*#__PURE__*/\n_curry3(function insertAll(idx, elts, list) {\n  idx = idx < list.length && idx >= 0 ? idx : list.length;\n  return [].concat(Array.prototype.slice.call(list, 0, idx), elts, Array.prototype.slice.call(list, idx));\n});\n\nexport default insertAll;", "import _curry1 from \"./internal/_curry1.js\";\nimport curryN from \"./curryN.js\";\n/**\n * Returns a new function much like the supplied one, except that the first two\n * arguments' order is reversed.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig ((a, b, c, ...) -> z) -> (b -> a -> c -> ... -> z)\n * @param {Function} fn The function to invoke with its first two parameters reversed.\n * @return {*} The result of invoking `fn` with its first two parameters' order reversed.\n * @example\n *\n *      const mergeThree = (a, b, c) => [].concat(a, b, c);\n *\n *      mergeThree(1, 2, 3); //=> [1, 2, 3]\n *\n *      R.flip(mergeThree)(1, 2, 3); //=> [2, 1, 3]\n * @symb R.flip(f)(a, b, c) = f(b, a, c)\n */\n\nvar flip =\n/*#__PURE__*/\n_curry1(function flip(fn) {\n  return curryN(fn.length, function (a, b) {\n    var args = Array.prototype.slice.call(arguments, 0);\n    args[0] = b;\n    args[1] = a;\n    return fn.apply(this, args);\n  });\n});\n\nexport default flip;", "export default function _identity(x) {\n  return x;\n}", "import _curry1 from \"./internal/_curry1.js\";\nimport _identity from \"./internal/_identity.js\";\n/**\n * A function that does nothing but return the parameter supplied to it. Good\n * as a default or placeholder function.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig a -> a\n * @param {*} x The value to return.\n * @return {*} The input value, `x`.\n * @example\n *\n *      R.identity(1); //=> 1\n *\n *      const obj = {};\n *      R.identity(obj) === obj; //=> true\n * @symb R.identity(a) = a\n */\n\nvar identity =\n/*#__PURE__*/\n_curry1(_identity);\n\nexport default identity;", "import _curry2 from \"./_curry2.js\";\nimport _Set from \"./_Set.js\";\nimport _xfBase from \"./_xfBase.js\";\n\nvar XUniqBy =\n/*#__PURE__*/\nfunction () {\n  function XUniqBy(f, xf) {\n    this.xf = xf;\n    this.f = f;\n    this.set = new _Set();\n  }\n\n  XUniqBy.prototype['@@transducer/init'] = _xfBase.init;\n  XUniqBy.prototype['@@transducer/result'] = _xfBase.result;\n\n  XUniqBy.prototype['@@transducer/step'] = function (result, input) {\n    return this.set.add(this.f(input)) ? this.xf['@@transducer/step'](result, input) : result;\n  };\n\n  return XUniqBy;\n}();\n\nvar _xuniqBy =\n/*#__PURE__*/\n_curry2(function _xuniqBy(f, xf) {\n  return new XUniqBy(f, xf);\n});\n\nexport default _xuniqBy;", "import identity from \"./identity.js\";\nimport uniqBy from \"./uniqBy.js\";\n/**\n * Returns a new list containing only one copy of each element in the original\n * list. [`<PERSON><PERSON>equals`](#equals) is used to determine equality.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> [a]\n * @param {Array} list The array to consider.\n * @return {Array} The list of unique items.\n * @example\n *\n *      R.uniq([1, 1, 2, 1]); //=> [1, 2]\n *      R.uniq([1, '1']);     //=> [1, '1']\n *      R.uniq([[42], [42]]); //=> [[42]]\n */\n\nvar uniq =\n/*#__PURE__*/\nuniqBy(identity);\nexport default uniq;", "import _Set from \"./internal/_Set.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _xuniqBy from \"./internal/_xuniqBy.js\";\n/**\n * Returns a new list containing only one copy of each element in the original\n * list, based upon the value returned by applying the supplied function to\n * each list element. Prefers the first item if the supplied function produces\n * the same value on two items. [`R.equals`](#equals) is used for comparison.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.16.0\n * @category List\n * @sig (a -> b) -> [a] -> [a]\n * @param {Function} fn A function used to produce a value to use during comparisons.\n * @param {Array} list The array to consider.\n * @return {Array} The list of unique items.\n * @example\n *\n *      R.uniqBy(Math.abs, [-1, -5, 2, 10, 1, 2]); //=> [-1, -5, 2, 10]\n */\n\nvar uniqBy =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable([], _xuniqBy, function (fn, list) {\n  var set = new _Set();\n  var result = [];\n  var idx = 0;\n  var appliedItem, item;\n\n  while (idx < list.length) {\n    item = list[idx];\n    appliedItem = fn(item);\n\n    if (set.add(appliedItem)) {\n      result.push(item);\n    }\n\n    idx += 1;\n  }\n\n  return result;\n}));\n\nexport default uniqBy;", "import _includes from \"./internal/_includes.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _filter from \"./internal/_filter.js\";\nimport flip from \"./flip.js\";\nimport uniq from \"./uniq.js\";\n/**\n * Combines two lists into a set (i.e. no duplicates) composed of those\n * elements common to both lists.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Relation\n * @sig [*] -> [*] -> [*]\n * @param {Array} list1 The first list.\n * @param {Array} list2 The second list.\n * @return {Array} The list of elements found in both `list1` and `list2`.\n * @see R.innerJoin\n * @example\n *\n *      R.intersection([1,2,3,4], [7,6,5,4,3]); //=> [4, 3]\n */\n\nvar intersection =\n/*#__PURE__*/\n_curry2(function intersection(list1, list2) {\n  var lookupList, filteredList;\n\n  if (list1.length > list2.length) {\n    lookupList = list1;\n    filteredList = list2;\n  } else {\n    lookupList = list2;\n    filteredList = list1;\n  }\n\n  return uniq(_filter(flip(_includes)(lookupList), filteredList));\n});\n\nexport default intersection;", "import _has from \"./_has.js\"; // Based on https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n\nfunction _objectAssign(target) {\n  if (target == null) {\n    throw new TypeError('Cannot convert undefined or null to object');\n  }\n\n  var output = Object(target);\n  var idx = 1;\n  var length = arguments.length;\n\n  while (idx < length) {\n    var source = arguments[idx];\n\n    if (source != null) {\n      for (var nextKey in source) {\n        if (_has(nextKey, source)) {\n          output[nextKey] = source[nextKey];\n        }\n      }\n    }\n\n    idx += 1;\n  }\n\n  return output;\n}\n\nexport default typeof Object.assign === 'function' ? Object.assign : _objectAssign;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * See if an object (i.e. `val`) is an instance of the supplied constructor. This\n * function will check up the inheritance chain, if any.\n * If `val` was created using `Object.create`, `R.is(Object, val) === true`.\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category Type\n * @sig (* -> {*}) -> a -> Boolean\n * @param {Object} ctor A constructor\n * @param {*} val The value to test\n * @return {Boolean}\n * @example\n *\n *      R.is(Object, {}); //=> true\n *      R.is(Number, 1); //=> true\n *      R.is(Object, 1); //=> false\n *      R.is(String, 's'); //=> true\n *      R.is(String, new String('')); //=> true\n *      R.is(Object, new String('')); //=> true\n *      R.is(Object, 's'); //=> false\n *      R.is(Number, {}); //=> false\n */\n\nvar is =\n/*#__PURE__*/\n_curry2(function is(Ctor, val) {\n  return val instanceof Ctor || val != null && (val.constructor === Ctor || Ctor.name === 'Object' && typeof val === 'object');\n});\n\nexport default is;", "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Returns a list containing the names of all the properties of the supplied\n * object, including prototype properties.\n * Note that the order of the output array is not guaranteed to be consistent\n * across different JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.2.0\n * @category Object\n * @sig {k: v} -> [k]\n * @param {Object} obj The object to extract properties from\n * @return {Array} An array of the object's own and prototype properties.\n * @see R.keys, R.valuesIn\n * @example\n *\n *      const F = function() { this.x = 'X'; };\n *      F.prototype.y = 'Y';\n *      const f = new F();\n *      R.keysIn(f); //=> ['x', 'y']\n */\n\nvar keysIn =\n/*#__PURE__*/\n_curry1(function keysIn(obj) {\n  var prop;\n  var ks = [];\n\n  for (prop in obj) {\n    ks[ks.length] = prop;\n  }\n\n  return ks;\n});\n\nexport default keysIn;", "import _curry2 from \"./internal/_curry2.js\";\nimport map from \"./map.js\";\n/**\n * Returns a lens for the given getter and setter functions. The getter \"gets\"\n * the value of the focus; the setter \"sets\" the value of the focus. The setter\n * should not mutate the data structure.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Object\n * @typedefn Lens s a = Functor f => (a -> f a) -> s -> f s\n * @sig (s -> a) -> ((a, s) -> s) -> Lens s a\n * @param {Function} getter\n * @param {Function} setter\n * @return {Lens}\n * @see R.view, R.set, R.over, <PERSON>.lensIndex, R.lensProp\n * @example\n *\n *      const xLens = R.lens(R.prop('x'), R.assoc('x'));\n *\n *      R.view(xLens, {x: 1, y: 2});            //=> 1\n *      R.set(xLens, 4, {x: 1, y: 2});          //=> {x: 4, y: 2}\n *      R.over(x<PERSON><PERSON>, <PERSON><PERSON>negate, {x: 1, y: 2});  //=> {x: -1, y: 2}\n */\n\nvar lens =\n/*#__PURE__*/\n_curry2(function lens(getter, setter) {\n  return function (toFunctorFn) {\n    return function (target) {\n      return map(function (focus) {\n        return setter(focus, target);\n      }, toFunctorFn(getter(target)));\n    };\n  };\n});\n\nexport default lens;", "import _curry2 from \"./internal/_curry2.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport nth from \"./nth.js\";\n/**\n * Retrieves the values at given paths of an object.\n *\n * @func\n * @memberOf R\n * @since v0.27.1\n * @category Object\n * @typedefn Idx = [String | Int | Symbol]\n * @sig [Idx] -> {a} -> [a | Undefined]\n * @param {Array} pathsArray The array of paths to be fetched.\n * @param {Object} obj The object to retrieve the nested properties from.\n * @return {Array} A list consisting of values at paths specified by \"pathsArray\".\n * @see R.path\n * @example\n *\n *      R.paths([['a', 'b'], ['p', 0, 'q']], {a: {b: 2}, p: [{q: 3}]}); //=> [2, 3]\n *      R.paths([['a', 'b'], ['p', 'r']], {a: {b: 2}, p: [{q: 3}]}); //=> [2, undefined]\n */\n\nvar paths =\n/*#__PURE__*/\n_curry2(function paths(pathsArray, obj) {\n  return pathsArray.map(function (paths) {\n    var val = obj;\n    var idx = 0;\n    var p;\n\n    while (idx < paths.length) {\n      if (val == null) {\n        return;\n      }\n\n      p = paths[idx];\n      val = _isInteger(p) ? nth(p, val) : val[p];\n      idx += 1;\n    }\n\n    return val;\n  });\n});\n\nexport default paths;", "import _curry2 from \"./internal/_curry2.js\";\nimport paths from \"./paths.js\";\n/**\n * Retrieve the value at a given path.\n *\n * @func\n * @memberOf R\n * @since v0.2.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> {a} -> a | Undefined\n * @param {Array} path The path to use.\n * @param {Object} obj The object to retrieve the nested property from.\n * @return {*} The data at `path`.\n * @see R.prop, R.nth\n * @example\n *\n *      R.path(['a', 'b'], {a: {b: 2}}); //=> 2\n *      R.path(['a', 'b'], {c: {b: 2}}); //=> undefined\n *      R.path(['a', 'b', 0], {a: {b: [1, 2, 3]}}); //=> 1\n *      R.path(['a', 'b', -2], {a: {b: [1, 2, 3]}}); //=> 2\n */\n\nvar path =\n/*#__PURE__*/\n_curry2(function path(pathAr, obj) {\n  return paths([pathAr], obj)[0];\n});\n\nexport default path;", "import _curry1 from \"./internal/_curry1.js\";\nimport assocPath from \"./assocPath.js\";\nimport lens from \"./lens.js\";\nimport path from \"./path.js\";\n/**\n * Returns a lens whose focus is the specified path.\n *\n * @func\n * @memberOf R\n * @since v0.19.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @typedefn Lens s a = Functor f => (a -> f a) -> s -> f s\n * @sig [Idx] -> Lens s a\n * @param {Array} path The path to use.\n * @return {Lens}\n * @see R.view, R.set, R.over\n * @example\n *\n *      const xHeadYLens = R.lensPath(['x', 0, 'y']);\n *\n *      R.view(xHeadYLens, {x: [{y: 2, z: 3}, {y: 4, z: 5}]});\n *      //=> 2\n *      R.set(xHeadYLens, 1, {x: [{y: 2, z: 3}, {y: 4, z: 5}]});\n *      //=> {x: [{y: 1, z: 3}, {y: 4, z: 5}]}\n *      <PERSON>.over(x<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>.negate, {x: [{y: 2, z: 3}, {y: 4, z: 5}]});\n *      //=> {x: [{y: -2, z: 3}, {y: 4, z: 5}]}\n */\n\nvar lensPath =\n/*#__PURE__*/\n_curry1(function lensPath(p) {\n  return lens(path(p), assocPath(p));\n});\n\nexport default lensPath;", "import add from \"./add.js\";\nimport reduce from \"./reduce.js\";\n/**\n * Adds together all the elements of a list.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Math\n * @sig [Number] -> Number\n * @param {Array} list An array of numbers\n * @return {Number} The sum of all the numbers in the list.\n * @see R.reduce\n * @example\n *\n *      R.sum([2,4,6,8,100,1]); //=> 121\n */\n\nvar sum =\n/*#__PURE__*/\nreduce(add, 0);\nexport default sum;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Adds two values.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Math\n * @sig Number -> Number -> Number\n * @param {Number} a\n * @param {Number} b\n * @return {Number}\n * @see R.subtract\n * @example\n *\n *      R.add(2, 3);       //=>  5\n *      R.add(7)(10);      //=> 17\n */\n\nvar add =\n/*#__PURE__*/\n_curry2(function add(a, b) {\n  return Number(a) + Number(b);\n});\n\nexport default add;", "import _objectAssign from \"./internal/_objectAssign.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n/**\n * Creates one new object with the own properties from a list of objects.\n * If a key exists in more than one object, the value from the last\n * object it exists in will be used.\n *\n * @func\n * @memberOf R\n * @since v0.10.0\n * @category List\n * @sig [{k: v}] -> {k: v}\n * @param {Array} list An array of objects\n * @return {Object} A merged object.\n * @see R.reduce\n * @example\n *\n *      R.mergeAll([{foo:1},{bar:2},{baz:3}]); //=> {foo:1,bar:2,baz:3}\n *      R.mergeAll([{foo:1},{foo:2},{bar:2}]); //=> {foo:2,bar:2}\n * @symb R.mergeAll([{ x: 1 }, { y: 2 }, { z: 3 }]) = { x: 1, y: 2, z: 3 }\n */\n\nvar mergeAll =\n/*#__PURE__*/\n_curry1(function mergeAll(list) {\n  return _objectAssign.apply(null, [{}].concat(list));\n});\n\nexport default mergeAll;", "import _objectAssign from \"./internal/_objectAssign.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Create a new object with the own properties of the first object merged with\n * the own properties of the second object. If a key exists in both objects,\n * the value from the second object will be used.\n *\n * @func\n * @memberOf R\n * @since v0.26.0\n * @category Object\n * @sig {k: v} -> {k: v} -> {k: v}\n * @param {Object} l\n * @param {Object} r\n * @return {Object}\n * @see R.mergeLeft, R.mergeDeepRight, R.mergeWith, R.mergeWithKey\n * @example\n *\n *      R.mergeRight({ 'name': 'fred', 'age': 10 }, { 'age': 40 });\n *      //=> { 'name': 'fred', 'age': 40 }\n *\n *      const withDefaults = R.mergeRight({x: 0, y: 0});\n *      withDefaults({y: 2}); //=> {x: 0, y: 2}\n * @symb R.mergeRight(a, b) = {...a, ...b}\n */\n\nvar mergeRight =\n/*#__PURE__*/\n_curry2(function mergeRight(l, r) {\n  return _objectAssign({}, l, r);\n});\n\nexport default mergeRight;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns the smaller of its two arguments.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Relation\n * @sig Ord a => a -> a -> a\n * @param {*} a\n * @param {*} b\n * @return {*}\n * @see R.minBy, R.max\n * @example\n *\n *      R.min(789, 123); //=> 123\n *      R.min('a', 'b'); //=> 'a'\n */\n\nvar min =\n/*#__PURE__*/\n_curry2(function min(a, b) {\n  return b < a ? b : a;\n});\n\nexport default min;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns a partial copy of an object omitting the keys specified.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig [String] -> {String: *} -> {String: *}\n * @param {Array} names an array of String property names to omit from the new object\n * @param {Object} obj The object to copy from\n * @return {Object} A new object with properties from `names` not on it.\n * @see R.pick\n * @example\n *\n *      R.omit(['a', 'd'], {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, c: 3}\n */\n\nvar omit =\n/*#__PURE__*/\n_curry2(function omit(names, obj) {\n  var result = {};\n  var index = {};\n  var idx = 0;\n  var len = names.length;\n\n  while (idx < len) {\n    index[names[idx]] = 1;\n    idx += 1;\n  }\n\n  for (var prop in obj) {\n    if (!index.hasOwnProperty(prop)) {\n      result[prop] = obj[prop];\n    }\n  }\n\n  return result;\n});\n\nexport default omit;", "import _arity from \"./internal/_arity.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n/**\n * Accepts a function `fn` and returns a function that guards invocation of\n * `fn` such that `fn` can only ever be called once, no matter how many times\n * the returned function is invoked. The first value calculated is returned in\n * subsequent invocations.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig (a... -> b) -> (a... -> b)\n * @param {Function} fn The function to wrap in a call-only-once wrapper.\n * @return {Function} The wrapped function.\n * @example\n *\n *      const addOneOnce = R.once(x => x + 1);\n *      addOneOnce(10); //=> 11\n *      addOneOnce(addOneOnce(50)); //=> 11\n */\n\nvar once =\n/*#__PURE__*/\n_curry1(function once(fn) {\n  var called = false;\n  var result;\n  return _arity(fn.length, function () {\n    if (called) {\n      return result;\n    }\n\n    called = true;\n    result = fn.apply(this, arguments);\n    return result;\n  });\n});\n\nexport default once;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns a partial copy of an object containing only the keys specified. If\n * the key does not exist, the property is ignored.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig [k] -> {k: v} -> {k: v}\n * @param {Array} names an array of String property names to copy onto a new object\n * @param {Object} obj The object to copy from\n * @return {Object} A new object with only properties from `names` on it.\n * @see R.omit, R.props\n * @example\n *\n *      R.pick(['a', 'd'], {a: 1, b: 2, c: 3, d: 4}); //=> {a: 1, d: 4}\n *      R.pick(['a', 'e', 'f'], {a: 1, b: 2, c: 3, d: 4}); //=> {a: 1}\n */\n\nvar pick =\n/*#__PURE__*/\n_curry2(function pick(names, obj) {\n  var result = {};\n  var idx = 0;\n\n  while (idx < names.length) {\n    if (names[idx] in obj) {\n      result[names[idx]] = obj[names[idx]];\n    }\n\n    idx += 1;\n  }\n\n  return result;\n});\n\nexport default pick;", "import _curry2 from \"./internal/_curry2.js\";\nimport path from \"./path.js\";\n/**\n * Acts as multiple `prop`: array of keys in, array of values out. Preserves\n * order.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig [k] -> {k: v} -> [v]\n * @param {Array} ps The property names to fetch\n * @param {Object} obj The object to query\n * @return {Array} The corresponding values or partially applied function.\n * @see R.prop, R.pluck, R.project\n * @example\n *\n *      R.props(['x', 'y'], {x: 1, y: 2}); //=> [1, 2]\n *      R.props(['c', 'a', 'b'], {b: 2, a: 1}); //=> [undefined, 1, 2]\n *\n *      const fullName = R.compose(R.join(' '), R.props(['first', 'last']));\n *      fullName({last: 'Bullet-Tooth', age: 33, first: '<PERSON>'}); //=> '<PERSON>-<PERSON>'\n */\n\nvar props =\n/*#__PURE__*/\n_curry2(function props(ps, obj) {\n  return ps.map(function (p) {\n    return path([p], obj);\n  });\n});\n\nexport default props;", "export default function _isNumber(x) {\n  return Object.prototype.toString.call(x) === '[object Number]';\n}", "import _curry2 from \"./internal/_curry2.js\";\nimport _isNumber from \"./internal/_isNumber.js\";\n/**\n * Returns a list of numbers from `from` (inclusive) to `to` (exclusive).\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Number -> Number -> [Number]\n * @param {Number} from The first number in the list.\n * @param {Number} to One more than the last number in the list.\n * @return {Array} The list of numbers in the set `[a, b)`.\n * @example\n *\n *      R.range(1, 5);    //=> [1, 2, 3, 4]\n *      R.range(50, 53);  //=> [50, 51, 52]\n */\n\nvar range =\n/*#__PURE__*/\n_curry2(function range(from, to) {\n  if (!(_isNumber(from) && _isNumber(to))) {\n    throw new TypeError('Both arguments to range must be numbers');\n  }\n\n  var result = [];\n  var n = from;\n\n  while (n < to) {\n    result.push(n);\n    n += 1;\n  }\n\n  return result;\n});\n\nexport default range;", "import _curryN from \"./internal/_curryN.js\";\nimport _reduce from \"./internal/_reduce.js\";\nimport _reduced from \"./internal/_reduced.js\";\n/**\n * Like [`reduce`](#reduce), `reduceWhile` returns a single item by iterating\n * through the list, successively calling the iterator function. `reduceWhile`\n * also takes a predicate that is evaluated before each step. If the predicate\n * returns `false`, it \"short-circuits\" the iteration and returns the current\n * value of the accumulator. `reduceWhile` may alternatively be short-circuited\n * via [`reduced`](#reduced).\n *\n * @func\n * @memberOf R\n * @since v0.22.0\n * @category List\n * @sig ((a, b) -> Boolean) -> ((a, b) -> a) -> a -> [b] -> a\n * @param {Function} pred The predicate. It is passed the accumulator and the\n *        current element.\n * @param {Function} fn The iterator function. Receives two values, the\n *        accumulator and the current element.\n * @param {*} a The accumulator value.\n * @param {Array} list The list to iterate over.\n * @return {*} The final, accumulated value.\n * @see R.reduce, <PERSON>.reduced\n * @example\n *\n *      const isOdd = (acc, x) => x % 2 !== 0;\n *      const xs = [1, 3, 5, 60, 777, 800];\n *      R.reduceWhile(isOdd, R.add, 0, xs); //=> 9\n *\n *      const ys = [2, 4, 6]\n *      R.reduceWhile(isOdd, R.add, 111, ys); //=> 111\n */\n\nvar reduceWhile =\n/*#__PURE__*/\n_curryN(4, [], function _reduceWhile(pred, fn, a, list) {\n  return _reduce(function (acc, x) {\n    return pred(acc, x) ? fn(acc, x) : _reduced(acc);\n  }, a, list);\n});\n\nexport default reduceWhile;", "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Returns a function that always returns the given value. Note that for\n * non-primitives the value returned is a reference to the original value.\n *\n * This function is known as `const`, `constant`, or `K` (for K combinator) in\n * other languages and libraries.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Function\n * @sig a -> (* -> a)\n * @param {*} val The value to wrap in a function\n * @return {Function} A Function :: * -> val.\n * @example\n *\n *      const t = R.always('Tee');\n *      t(); //=> 'Tee'\n */\n\nvar always =\n/*#__PURE__*/\n_curry1(function always(val) {\n  return function () {\n    return val;\n  };\n});\n\nexport default always;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Calls an input function `n` times, returning an array containing the results\n * of those function calls.\n *\n * `fn` is passed one argument: The current value of `n`, which begins at `0`\n * and is gradually incremented to `n - 1`.\n *\n * @func\n * @memberOf R\n * @since v0.2.3\n * @category List\n * @sig (Number -> a) -> Number -> [a]\n * @param {Function} fn The function to invoke. Passed one argument, the current value of `n`.\n * @param {Number} n A value between `0` and `n - 1`. Increments after each function call.\n * @return {Array} An array containing the return values of all calls to `fn`.\n * @see R.repeat\n * @example\n *\n *      R.times(R.identity, 5); //=> [0, 1, 2, 3, 4]\n * @symb R.times(f, 0) = []\n * @symb R.times(f, 1) = [f(0)]\n * @symb R.times(f, 2) = [f(0), f(1)]\n */\n\nvar times =\n/*#__PURE__*/\n_curry2(function times(fn, n) {\n  var len = Number(n);\n  var idx = 0;\n  var list;\n\n  if (len < 0 || isNaN(len)) {\n    throw new RangeError('n must be a non-negative number');\n  }\n\n  list = new Array(len);\n\n  while (idx < len) {\n    list[idx] = fn(idx);\n    idx += 1;\n  }\n\n  return list;\n});\n\nexport default times;", "import _curry2 from \"./internal/_curry2.js\";\nimport always from \"./always.js\";\nimport times from \"./times.js\";\n/**\n * Returns a fixed list of size `n` containing a specified identical value.\n *\n * @func\n * @memberOf R\n * @since v0.1.1\n * @category List\n * @sig a -> n -> [a]\n * @param {*} value The value to repeat.\n * @param {Number} n The desired size of the output list.\n * @return {Array} A new array containing `n` `value`s.\n * @see R.times\n * @example\n *\n *      R.repeat('hi', 5); //=> ['hi', 'hi', 'hi', 'hi', 'hi']\n *\n *      const obj = {};\n *      const repeatedObjs = R.repeat(obj, 5); //=> [{}, {}, {}, {}, {}]\n *      repeatedObjs[0] === repeatedObjs[1]; //=> true\n * @symb R.repeat(a, 0) = []\n * @symb R.repeat(a, 1) = [a]\n * @symb R.repeat(a, 2) = [a, a]\n */\n\nvar repeat =\n/*#__PURE__*/\n_curry2(function repeat(value, n) {\n  return times(always(value), n);\n});\n\nexport default repeat;", "import _curry3 from \"./internal/_curry3.js\"; // `Identity` is a functor that holds a single value, where `map` simply\n// transforms the held value with the provided function.\n\nvar Identity = function (x) {\n  return {\n    value: x,\n    map: function (f) {\n      return Identity(f(x));\n    }\n  };\n};\n/**\n * Returns the result of \"setting\" the portion of the given data structure\n * focused by the given lens to the result of applying the given function to\n * the focused value.\n *\n * @func\n * @memberOf R\n * @since v0.16.0\n * @category Object\n * @typedefn Lens s a = Functor f => (a -> f a) -> s -> f s\n * @sig Lens s a -> (a -> a) -> s -> s\n * @param {Lens} lens\n * @param {*} v\n * @param {*} x\n * @return {*}\n * @see R.view, R.set, R.lens, R.lensIndex, R.lensProp, R.lensPath\n * @example\n *\n *      const headLens = R.lensIndex(0);\n *\n *      R.over(headL<PERSON>, <PERSON>.toUpper, ['foo', 'bar', 'baz']); //=> ['FOO', 'bar', 'baz']\n */\n\n\nvar over =\n/*#__PURE__*/\n_curry3(function over(lens, f, x) {\n  // The value returned by the getter function is first transformed with `f`,\n  // then set as the value of an `Identity`. This is then mapped over with the\n  // setter function of the lens.\n  return lens(function (y) {\n    return Identity(f(y));\n  })(x).value;\n});\n\nexport default over;", "import _curry3 from \"./internal/_curry3.js\";\nimport always from \"./always.js\";\nimport over from \"./over.js\";\n/**\n * Returns the result of \"setting\" the portion of the given data structure\n * focused by the given lens to the given value.\n *\n * @func\n * @memberOf R\n * @since v0.16.0\n * @category Object\n * @typedefn Lens s a = Functor f => (a -> f a) -> s -> f s\n * @sig Lens s a -> a -> s -> s\n * @param {Lens} lens\n * @param {*} v\n * @param {*} x\n * @return {*}\n * @see R.view, R.over, R.lens, R.lensIndex, R.lensProp, R.lensPath\n * @example\n *\n *      const xLens = R.lensProp('x');\n *\n *      R.set(xLens, 4, {x: 1, y: 2});  //=> {x: 4, y: 2}\n *      R.set(xLens, 8, {x: 1, y: 2});  //=> {x: 8, y: 2}\n */\n\nvar set =\n/*#__PURE__*/\n_curry3(function set(lens, v, x) {\n  return over(lens, always(v), x);\n});\n\nexport default set;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Sorts a list according to a list of comparators.\n *\n * @func\n * @memberOf R\n * @since v0.23.0\n * @category Relation\n * @sig [(a, a) -> Number] -> [a] -> [a]\n * @param {Array} functions A list of comparator functions.\n * @param {Array} list The list to sort.\n * @return {Array} A new list sorted according to the comarator functions.\n * @see R.ascend, R.descend\n * @example\n *\n *      const alice = {\n *        name: 'alice',\n *        age: 40\n *      };\n *      const bob = {\n *        name: 'bob',\n *        age: 30\n *      };\n *      const clara = {\n *        name: 'clara',\n *        age: 40\n *      };\n *      const people = [clara, bob, alice];\n *      const ageNameSort = R.sortWith([\n *        R.descend(R.prop('age')),\n *        R.ascend(R.prop('name'))\n *      ]);\n *      ageNameSort(people); //=> [alice, clara, bob]\n */\n\nvar sortWith =\n/*#__PURE__*/\n_curry2(function sortWith(fns, list) {\n  return Array.prototype.slice.call(list, 0).sort(function (a, b) {\n    var result = 0;\n    var i = 0;\n\n    while (result === 0 && i < fns.length) {\n      result = fns[i](a, b);\n      i += 1;\n    }\n\n    return result;\n  });\n});\n\nexport default sortWith;", "import _curry1 from \"./internal/_curry1.js\";\nimport _has from \"./internal/_has.js\";\n/**\n * Converts an object into an array of key, value arrays. Only the object's\n * own properties are used.\n * Note that the order of the output array is not guaranteed to be consistent\n * across different JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.4.0\n * @category Object\n * @sig {String: *} -> [[String,*]]\n * @param {Object} obj The object to extract from\n * @return {Array} An array of key, value arrays from the object's own properties.\n * @see R.fromPairs, R.keys, R.values\n * @example\n *\n *      R.toPairs({a: 1, b: 2, c: 3}); //=> [['a', 1], ['b', 2], ['c', 3]]\n */\n\nvar toPairs =\n/*#__PURE__*/\n_curry1(function toPairs(obj) {\n  var pairs = [];\n\n  for (var prop in obj) {\n    if (_has(prop, obj)) {\n      pairs[pairs.length] = [prop, obj[prop]];\n    }\n  }\n\n  return pairs;\n});\n\nexport default toPairs;", "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Transposes the rows and columns of a 2D list.\n * When passed a list of `n` lists of length `x`,\n * returns a list of `x` lists of length `n`.\n *\n *\n * @func\n * @memberOf R\n * @since v0.19.0\n * @category List\n * @sig [[a]] -> [[a]]\n * @param {Array} list A 2D list\n * @return {Array} A 2D list\n * @example\n *\n *      R.transpose([[1, 'a'], [2, 'b'], [3, 'c']]) //=> [[1, 2, 3], ['a', 'b', 'c']]\n *      R.transpose([[1, 2, 3], ['a', 'b', 'c']]) //=> [[1, 'a'], [2, 'b'], [3, 'c']]\n *\n *      // If some of the rows are shorter than the following rows, their elements are skipped:\n *      R.transpose([[10, 11], [20], [], [30, 31, 32]]) //=> [[10, 20, 30], [11, 31], [32]]\n * @symb R.transpose([[a], [b], [c]]) = [a, b, c]\n * @symb R.transpose([[a, b], [c, d]]) = [[a, c], [b, d]]\n * @symb R.transpose([[a, b], [c]]) = [[a, c], [b]]\n */\n\nvar transpose =\n/*#__PURE__*/\n_curry1(function transpose(outerlist) {\n  var i = 0;\n  var result = [];\n\n  while (i < outerlist.length) {\n    var innerlist = outerlist[i];\n    var j = 0;\n\n    while (j < innerlist.length) {\n      if (typeof result[j] === 'undefined') {\n        result[j] = [];\n      }\n\n      result[j].push(innerlist[j]);\n      j += 1;\n    }\n\n    i += 1;\n  }\n\n  return result;\n});\n\nexport default transpose;", "import _concat from \"./internal/_concat.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport compose from \"./compose.js\";\nimport uniq from \"./uniq.js\";\n/**\n * Combines two lists into a set (i.e. no duplicates) composed of the elements\n * of each list.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Relation\n * @sig [*] -> [*] -> [*]\n * @param {Array} as The first list.\n * @param {Array} bs The second list.\n * @return {Array} The first and second lists concatenated, with\n *         duplicates removed.\n * @example\n *\n *      R.union([1, 2, 3], [2, 3, 4]); //=> [1, 2, 3, 4]\n */\n\nvar union =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\ncompose(uniq, _concat));\n\nexport default union;", "import _curry1 from \"./internal/_curry1.js\";\nvar ws = '\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u2000\\u2001\\u2002\\u2003' + '\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028' + '\\u2029\\uFEFF';\nvar zeroWidth = '\\u200b';\nvar hasProtoTrim = typeof String.prototype.trim === 'function';\n/**\n * Removes (strips) whitespace from both ends of the string.\n *\n * @func\n * @memberOf R\n * @since v0.6.0\n * @category String\n * @sig String -> String\n * @param {String} str The string to trim.\n * @return {String} Trimmed version of `str`.\n * @example\n *\n *      R.trim('   xyz  '); //=> 'xyz'\n *      R.map(R.trim, R.split(',', 'x, y, z')); //=> ['x', 'y', 'z']\n */\n\nvar trim = !hasProtoTrim ||\n/*#__PURE__*/\nws.trim() || !\n/*#__PURE__*/\nzeroWidth.trim() ?\n/*#__PURE__*/\n_curry1(function trim(str) {\n  var beginRx = new RegExp('^[' + ws + '][' + ws + ']*');\n  var endRx = new RegExp('[' + ws + '][' + ws + ']*$');\n  return str.replace(beginRx, '').replace(endRx, '');\n}) :\n/*#__PURE__*/\n_curry1(function trim(str) {\n  return str.trim();\n});\nexport default trim;", "import _identity from \"./internal/_identity.js\";\nimport chain from \"./chain.js\";\n/**\n * Shorthand for `R.chain(R.identity)`, which removes one level of nesting from\n * any [Chain](https://github.com/fantasyland/fantasy-land#chain).\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category List\n * @sig Chain c => c (c a) -> c a\n * @param {*} list\n * @return {*}\n * @see R.flatten, R.chain\n * @example\n *\n *      R.unnest([1, [2], [[3]]]); //=> [1, 2, [3]]\n *      R.unnest([[1, 2], [3, 4], [5, 6]]); //=> [1, 2, 3, 4, 5, 6]\n */\n\nvar unnest =\n/*#__PURE__*/\nchain(_identity);\nexport default unnest;", "import _includes from \"./internal/_includes.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport flip from \"./flip.js\";\nimport reject from \"./reject.js\";\n/**\n * Returns a new list without values in the first argument.\n * [`<PERSON>.equals`](#equals) is used to determine equality.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.19.0\n * @category List\n * @sig [a] -> [a] -> [a]\n * @param {Array} list1 The values to be removed from `list2`.\n * @param {Array} list2 The array to remove values from.\n * @return {Array} The new array without values in `list1`.\n * @see R.transduce, R.difference, R.remove\n * @example\n *\n *      R.without([1, 2], [1, 2, 1, 3, 4]); //=> [3, 4]\n */\n\nvar without =\n/*#__PURE__*/\n_curry2(function (xs, list) {\n  return reject(flip(_includes)(xs), list);\n});\n\nexport default without;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Creates a new list out of the two supplied by creating each possible pair\n * from the lists.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> [b] -> [[a,b]]\n * @param {Array} as The first list.\n * @param {Array} bs The second list.\n * @return {Array} The list made by combining each possible pair from\n *         `as` and `bs` into pairs (`[a, b]`).\n * @example\n *\n *      R.xprod([1, 2], ['a', 'b']); //=> [[1, 'a'], [1, 'b'], [2, 'a'], [2, 'b']]\n * @symb R.xprod([a, b], [c, d]) = [[a, c], [a, d], [b, c], [b, d]]\n */\n\nvar xprod =\n/*#__PURE__*/\n_curry2(function xprod(a, b) {\n  // = xprodWith(prepend); (takes about 3 times as long...)\n  var idx = 0;\n  var ilen = a.length;\n  var j;\n  var jlen = b.length;\n  var result = [];\n\n  while (idx < ilen) {\n    j = 0;\n\n    while (j < jlen) {\n      result[result.length] = [a[idx], b[j]];\n      j += 1;\n    }\n\n    idx += 1;\n  }\n\n  return result;\n});\n\nexport default xprod;", "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Creates a new list out of the two supplied by pairing up equally-positioned\n * items from both lists. The returned list is truncated to the length of the\n * shorter of the two input lists.\n * Note: `zip` is equivalent to `zipWith(function(a, b) { return [a, b] })`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig [a] -> [b] -> [[a,b]]\n * @param {Array} list1 The first array to consider.\n * @param {Array} list2 The second array to consider.\n * @return {Array} The list made by pairing up same-indexed elements of `list1` and `list2`.\n * @example\n *\n *      R.zip([1, 2, 3], ['a', 'b', 'c']); //=> [[1, 'a'], [2, 'b'], [3, 'c']]\n * @symb R.zip([a, b, c], [d, e, f]) = [[a, d], [b, e], [c, f]]\n */\n\nvar zip =\n/*#__PURE__*/\n_curry2(function zip(a, b) {\n  var rv = [];\n  var idx = 0;\n  var len = Math.min(a.length, b.length);\n\n  while (idx < len) {\n    rv[idx] = [a[idx], b[idx]];\n    idx += 1;\n  }\n\n  return rv;\n});\n\nexport default zip;", "import _curry3 from \"./internal/_curry3.js\";\n/**\n * Creates a new list out of the two supplied by applying the function to each\n * equally-positioned pair in the lists. The returned list is truncated to the\n * length of the shorter of the two input lists.\n *\n * @function\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig ((a, b) -> c) -> [a] -> [b] -> [c]\n * @param {Function} fn The function used to combine the two elements into one value.\n * @param {Array} list1 The first array to consider.\n * @param {Array} list2 The second array to consider.\n * @return {Array} The list made by combining same-indexed elements of `list1` and `list2`\n *         using `fn`.\n * @example\n *\n *      const f = (x, y) => {\n *        // ...\n *      };\n *      R.zipWith(f, [1, 2, 3], ['a', 'b', 'c']);\n *      //=> [f(1, 'a'), f(2, 'b'), f(3, 'c')]\n * @symb R.zipWith(fn, [a, b, c], [d, e, f]) = [fn(a, d), fn(b, e), fn(c, f)]\n */\n\nvar zipWith =\n/*#__PURE__*/\n_curry3(function zipWith(fn, a, b) {\n  var rv = [];\n  var idx = 0;\n  var len = Math.min(a.length, b.length);\n\n  while (idx < len) {\n    rv[idx] = fn(a[idx], b[idx]);\n    idx += 1;\n  }\n\n  return rv;\n});\n\nexport default zipWith;", "import _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal one-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry1(fn) {\n  return function f1(a) {\n    if (arguments.length === 0 || _isPlaceholder(a)) {\n      return f1;\n    } else {\n      return fn.apply(this, arguments);\n    }\n  };\n}", "import _curry1 from \"./_curry1.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal two-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry2(fn) {\n  return function f2(a, b) {\n    switch (arguments.length) {\n      case 0:\n        return f2;\n\n      case 1:\n        return _isPlaceholder(a) ? f2 : _curry1(function (_b) {\n          return fn(a, _b);\n        });\n\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f2 : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b);\n        }) : fn(a, b);\n    }\n  };\n}", "import _curry1 from \"./_curry1.js\";\nimport _curry2 from \"./_curry2.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal three-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry3(fn) {\n  return function f3(a, b, c) {\n    switch (arguments.length) {\n      case 0:\n        return f3;\n\n      case 1:\n        return _isPlaceholder(a) ? f3 : _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        });\n\n      case 2:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f3 : _isPlaceholder(a) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _curry1(function (_c) {\n          return fn(a, b, _c);\n        });\n\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) && _isPlaceholder(c) ? f3 : _isPlaceholder(a) && _isPlaceholder(b) ? _curry2(function (_a, _b) {\n          return fn(_a, _b, c);\n        }) : _isPlaceholder(a) && _isPlaceholder(c) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) && _isPlaceholder(c) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b, c);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b, c);\n        }) : _isPlaceholder(c) ? _curry1(function (_c) {\n          return fn(a, b, _c);\n        }) : fn(a, b, c);\n    }\n  };\n}", "export default function _isPlaceholder(a) {\n  return a != null && typeof a === 'object' && a['@@functional/placeholder'] === true;\n}", "import _curry3 from \"./internal/_curry3.js\";\n/**\n * Removes the sub-list of `list` starting at index `start` and containing\n * `count` elements. _Note that this is not destructive_: it returns a copy of\n * the list with the changes.\n * <small>No lists have been harmed in the application of this function.</small>\n *\n * @func\n * @memberOf R\n * @since v0.2.2\n * @category List\n * @sig Number -> Number -> [a] -> [a]\n * @param {Number} start The position to start removing elements\n * @param {Number} count The number of elements to remove\n * @param {Array} list The list to remove from\n * @return {Array} A new Array with `count` elements from `start` removed.\n * @see R.without\n * @example\n *\n *      R.remove(2, 3, [1,2,3,4,5,6,7,8]); //=> [1,2,6,7,8]\n */\n\nvar remove =\n/*#__PURE__*/\n_curry3(function remove(start, count, list) {\n  var result = Array.prototype.slice.call(list, 0);\n  result.splice(start, count);\n  return result;\n});\n\nexport default remove;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"\" + {\"108\":\"async-table\",\"471\":\"async-export\",\"790\":\"async-highlight\"}[chunkId] + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript)\n\t\tscriptUrl = document.currentScript.src\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) scriptUrl = scripts[scripts.length - 1].src\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "var getCurrentScript = function() {\n    var script = document.currentScript;\n    if (!script) {\n        /* Shim for IE11 and below */\n        /* Do not take into account async scripts and inline scripts */\n\n        var doc_scripts = document.getElementsByTagName('script');\n        var scripts = [];\n\n        for (var i = 0; i < doc_scripts.length; i++) {\n            scripts.push(doc_scripts[i]);\n        }\n\n        scripts = scripts.filter(function(s) { return !s.async && !s.text && !s.textContent; });\n        script = scripts.slice(-1)[0];\n    }\n\n    return script;\n};\n\nvar isLocalScript = function(script) {\n    return /\\/_dash-component-suites\\//.test(script.src);\n};\n\nObject.defineProperty(__webpack_require__, 'p', {\n    get: (function () {\n        var script = getCurrentScript();\n\n        var url = script.src.split('/').slice(0, -1).join('/') + '/';\n\n        return function() {\n            return url;\n        };\n    })()\n});\n\nif (typeof jsonpScriptSrc !== 'undefined') {\n    var __jsonpScriptSrc__ = jsonpScriptSrc;\n    jsonpScriptSrc = function(chunkId) {\n        var script = getCurrentScript();\n        var isLocal = isLocalScript(script);\n\n        var src = __jsonpScriptSrc__(chunkId);\n\n        if(!isLocal) {\n            return src;\n        }\n\n        var srcFragments = src.split('/');\n        var fileFragments = srcFragments.slice(-1)[0].split('.');\n\n        fileFragments.splice(1, 0, \"v5_2_0m1667486286\");\n        srcFragments.splice(-1, 1, fileFragments.join('.'))\n\n        return srcFragments.join('/');\n    };\n}\n", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t296: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkdash_table\"] = self[\"webpackChunkdash_table\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "import 'css.escape'; // polyfill\nimport Environment from 'core/environment';\nimport Logger from 'core/Logger';\nimport DataTable from 'dash-table/dash/DataTable';\nLogger.setDebugLevel(Environment.debugLevel);\nLogger.setLogLevel(Environment.logLevel);\nexport { DataTable };\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "e", "window", "module", "exports", "n", "t", "r", "o", "i", "l", "call", "m", "c", "d", "Object", "defineProperty", "enumerable", "get", "Symbol", "toStringTag", "value", "__esModule", "create", "bind", "default", "prototype", "hasOwnProperty", "p", "s", "u", "a", "done", "Promise", "resolve", "then", "this", "arguments", "apply", "isReady", "lazy", "setTimeout", "regeneratorRuntime", "mark", "wrap", "prev", "next", "stop", "_dashprivate_isLazyComponentReady", "f", "length", "configurable", "writable", "key", "TypeError", "dispatchEvent", "CustomEvent", "addEventListener", "removeEventListener", "DebugLevel", "LogLevel", "LogString", "__logLevel", "__debugLevel", "logFn", "level", "currentLevel", "fn", "console", "log", "warn", "error", "Error", "prefix", "toUpperCase", "logger", "setDebugLevel", "setLogLevel", "defineProperties", "trace", "info", "warning", "fatal", "debug", "freeze", "__1day", "__20years", "Cookie<PERSON>torage", "id", "domain", "path", "enabled", "expires", "Date", "now", "toUTCString", "document", "cookie", "toLowerCase", "split", "map", "fragments", "trim", "find", "toLocaleLowerCase", "entry", "delete", "R", "ret", "indexOf", "DASH_DEBUG", "DASH_LOG", "Environment", "URL", "constructor", "location", "href", "searchParams", "_activeEdge", "_supportsCssVariables", "Boolean", "CSS", "supports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hljs", "result", "DataTable", "Suspense", "fallback", "RealDataTable", "props", "Component", "asyncDecorator", "defaultProps", "page_action", "page_current", "page_size", "css", "filter_query", "filter_action", "sort_as_null", "sort_action", "sort_mode", "sort_by", "style_as_list_view", "derived_viewport_data", "derived_viewport_indices", "derived_viewport_row_ids", "derived_viewport_selected_rows", "derived_viewport_selected_row_ids", "derived_virtual_data", "derived_virtual_indices", "derived_virtual_row_ids", "derived_virtual_selected_rows", "derived_virtual_selected_row_ids", "dropdown", "dropdown_conditional", "dropdown_data", "fill_width", "filter_options", "fixed_columns", "headers", "data", "fixed_rows", "markdown_options", "link_target", "html", "tooltip", "tooltip_conditional", "tooltip_data", "tooltip_header", "tooltip_delay", "tooltip_duration", "column_selectable", "editable", "export_columns", "export_format", "include_headers_on_copy_paste", "selected_cells", "selected_columns", "selected_rows", "selected_row_ids", "cell_selectable", "row_selectable", "style_table", "style_cell_conditional", "style_data_conditional", "style_filter_conditional", "style_header_conditional", "virtualization", "persisted_props", "persistence_type", "propTypes", "PropTypes", "columns", "name", "isRequired", "type", "presentation", "selectable", "clearable", "deletable", "hideable", "renamable", "case", "placeholder_text", "format", "locale", "symbol", "decimal", "group", "grouping", "numerals", "percent", "separate_4digits", "nully", "specifier", "on_change", "action", "failure", "validation", "allow_null", "allow_YY", "row_deletable", "active_cell", "row", "column", "row_id", "column_id", "start_cell", "end_cell", "data_previous", "hidden_columns", "is_focused", "merge_duplicate_headers", "data_timestamp", "export_headers", "page_count", "operator", "direction", "options", "label", "if", "delay", "duration", "use_with", "row_index", "locale_format", "selector", "rule", "style_cell", "style_data", "style_filter", "style_header", "column_type", "state", "column_editable", "header_index", "derived_filter_query_structure", "derived_viewport_selected_columns", "setProps", "loading_state", "is_loading", "prop_name", "component_name", "persistence", "persistenceTransforms", "extract", "propValue", "storedValue", "root", "g", "escape", "cssEscape", "codeUnit", "string", "String", "index", "firstCodeUnit", "charCodeAt", "toString", "char<PERSON>t", "factory", "set1", "set2", "idx", "len1", "len2", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "_curryN", "received", "combined", "argsIdx", "left", "combinedIdx", "_isPlaceholder", "addIndex", "origFn", "list", "args", "Array", "slice", "isArray", "val", "obj", "_dispatchable", "methodNames", "transducerCreator", "transducer", "_reduced", "x", "init", "xf", "XAll", "all", "_xfBase", "input", "functor", "len", "XWrap", "acc", "thisObj", "_iterableReduce", "iter", "step", "_methodReduce", "methodName", "symIterator", "iterator", "_arrayReduce", "reduce", "XMap", "prop", "hasEnumBug", "propertyIsEnumerable", "nonEnumerableProps", "hasArgsEnumBug", "contains", "item", "keys", "nIdx", "ks", "check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "isInteger", "offset", "XAny", "any", "el", "vals", "assocPath", "nextObj", "arr", "concat", "rxf", "preservingReduced", "monad", "jlen", "j", "ilen", "undefined", "refFrom", "refTo", "deep", "pattern", "copy", "copiedValue", "getPrototypeOf", "valueOf", "RegExp", "source", "global", "ignoreCase", "multiline", "sticky", "unicode", "clone", "pred", "b", "_pipe", "methodname", "fromIndex", "toIndex", "Infinity", "pipe", "reverse", "join", "compose", "_arrayFromIterator", "push", "_includesWith", "is", "_uniqContentEquals", "aIterator", "bIterator", "stackA", "stackB", "eq", "_a", "_b", "_equals", "aItem", "match", "typeA", "equals", "message", "size", "entries", "values", "keysA", "extendedStackA", "extendedStackB", "_indexOf", "inf", "_includes", "_quote", "replace", "pad", "toISOString", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "toFixed", "_filter", "<PERSON><PERSON><PERSON><PERSON>", "filterable", "reject", "seen", "recur", "y", "xs", "mapPairs", "k", "sort", "test", "isNaN", "NaN", "repr", "hasOrAdd", "shouldAdd", "set", "prevSize", "_items", "_nativeSet", "add", "has", "bIdx", "_Set", "Set", "XFind", "found", "XFindIndex", "XFindLast", "last", "_path", "ifElse", "condition", "onTrue", "onFalse", "Math", "max", "target", "elts", "flip", "_identity", "XUniqBy", "appliedItem", "list1", "list2", "lookupList", "filteredList", "assign", "output", "<PERSON><PERSON><PERSON>", "Ctor", "getter", "setter", "toFunctorFn", "focus", "pathsArray", "paths", "pathAr", "names", "once", "called", "ps", "_isNumber", "from", "to", "RangeError", "Identity", "lens", "v", "fns", "pairs", "outerlist", "innerlist", "rv", "min", "_curry1", "f1", "_curry2", "f2", "_curry3", "f3", "_c", "remove", "start", "count", "splice", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "__proto__", "mode", "ns", "def", "current", "getOwnPropertyNames", "for<PERSON>ach", "definition", "chunkId", "promises", "globalThis", "Function", "url", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "scriptUrl", "importScripts", "currentScript", "getCurrentScript", "doc_scripts", "filter", "async", "text", "textContent", "jsonpScriptSrc", "__jsonpScriptSrc__", "isLocal", "srcFragments", "fileFragments", "installedChunks", "installedChunkData", "promise", "errorType", "realSrc", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "<PERSON><PERSON>"], "sourceRoot": ""}