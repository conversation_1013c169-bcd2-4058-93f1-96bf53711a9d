/*! For license information please see async-table.js.LICENSE.txt */
(self.webpackChunkdash_table=self.webpackChunkdash_table||[]).push([[108],{70:function(e,t,n){"use strict";function r(e,t){return e===t||o(Object.values(e),Object.values(t))}function o(e,t){if(!e)return!1;var n=e.length;if(n!==t.length)return!1;for(var r=0;r<n;++r)if(e[r]!==t[r])return!1;return!0}n.d(t,{A:function(){return o},X:function(){return r}})},5117:function(e,t,n){"use strict";n.d(t,{Pi:function(){return i},qe:function(){return o},yw:function(){return a}});var r=n(70);function o(e){var t,n=null;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return(0,r.A)(n,i)?t:(n=i)&&(t=e.apply(void 0,i))}}function i(e){return function(){return o(e)}}function a(e){var t,n=null,o=!0;return function(){for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];var l=(0,r.A)(n,a)?{cached:!0,first:o,result:t}:{cached:!1,first:o,result:(n=a)&&(t=e.apply(void 0,a))};return o=!1,l}}},4167:function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a,s,l,c,u,p,d,f;n.d(t,{Ap:function(){return b},CR:function(){return c},J2:function(){return f},KI:function(){return h},Pj:function(){return s},QD:function(){return a},T2:function(){return l},UT:function(){return A},h8:function(){return p},oN:function(){return u},p9:function(){return d},sg:function(){return v}}),function(e){e.Any="any",e.Numeric="numeric",e.Text="text",e.Datetime="datetime"}(a||(a={})),function(e){e.All="all",e.Visible="visible"}(s||(s={})),function(e){e.Csv="csv",e.Xlsx="xlsx",e.None="none"}(l||(l={})),function(e){e.Ids="ids",e.Names="names",e.None="none",e.Display="display"}(c||(c={})),function(e){e.Insensitive="insensitive",e.Sensitive="sensitive"}(u||(u={})),function(e){e.Single="single",e.Multi="multi"}(p||(p={})),function(e){e.Custom="custom",e.Native="native",e.None="none"}(d||(d={})),function(e){e.And="and",e.Or="or"}(f||(f={}));var h,A,b,v=o((function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),i(this,"clearable",void 0),i(this,"deletable",void 0),i(this,"editable",!1),i(this,"filter_options",void 0),i(this,"hideable",void 0),i(this,"renamable",void 0),i(this,"selectable",void 0),i(this,"sort_as_null",[]),i(this,"id",void 0),i(this,"name",[]),Object.keys(t).includes("name")&&(this.name=t.name),Object.keys(t).includes("id")&&(this.id=t.id)}));!function(e){e.Coerce="coerce",e.None="none",e.Validate="validate"}(h||(h={})),function(e){e.Default="default",e.Accept="accept",e.Reject="reject"}(A||(A={})),function(e){e.Dropdown="dropdown",e.Input="input",e.Markdown="markdown"}(b||(b={}))},335:function(e,t,n){"use strict";var r,o;n.d(t,{a:function(){return o},v:function(){return r}}),function(e){e.Text="text",e.Markdown="markdown"}(r||(r={})),function(e){e.Both="both",e.Data="data",e.Header="header"}(o||(o={}))},8821:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return of}});var r=n(9196),o=n.n(r),i=n(9972),a=n(5117),s=n(3936);function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function u(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=function(){function e(t){l(this,e),p(this,"name",void 0),p(this,"__stylesheet",void 0),this.name=t}return u(e,[{key:"rules",get:function(){var e=this.sheet;return Array.from(e.rules||e.cssRules)}},{key:"addRule",value:function(e,t){this.sheet.addRule?this.sheet.addRule(e,t):this.sheet.insertRule("".concat(e," { ").concat(t," }"),0)}},{key:"deleteRule",value:function(e){this.sheet.deleteRule(e)}},{key:"findRule",value:function(e){var t=this.rules,n=t.findIndex((function(t){return t.selectorText===e}));return-1===n?null:{rule:t[n],index:n}}},{key:"sheet",get:function(){var e;return(this.__stylesheet=this.__stylesheet||(e=document.createElement("style"),e.type="text/css",e.id=this.name,document.getElementsByTagName("head")[0].appendChild(e),e)).sheet}}]),e}(),f=function(){function e(t){l(this,e),p(this,"prefix",void 0),p(this,"stylesheet",void 0),this.prefix=t,this.stylesheet=new d("".concat(t,"-dynamic-inline.css"))}return u(e,[{key:"deleteRule",value:function(e){e="".concat(this.prefix," ").concat(e);var t=this.stylesheet.findRule(e);t&&this.stylesheet.deleteRule(t.index)}},{key:"setRule",value:function(e,t){e="".concat(this.prefix," ").concat(e);var n=this.stylesheet.findRule(e);if(n){if(n.rule.cssText===t||n.rule.cssText==="".concat(e," { ").concat(t," }"))return;this.stylesheet.deleteRule(n.index)}this.stylesheet.addRule(e,t),s.ZP.trace("stylesheet",e,t)}}]),e}(),h={MOUSE_LEFT:1,MOUSE_RIGHT:3,MOUSE_MIDDLE:2,BACKSPACE:8,COMMA:188,INSERT:45,DELETE:46,END:35,ENTER:13,ESCAPE:27,CONTROL:17,COMMAND_LEFT:91,COMMAND_RIGHT:93,COMMAND_FIREFOX:224,ALT:18,HOME:36,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,SPACE:32,SHIFT:16,CAPS_LOCK:20,TAB:9,ARROW_RIGHT:39,ARROW_LEFT:37,ARROW_UP:38,ARROW_DOWN:40,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,A:65,X:88,C:67,V:86},A=(h.ARROW_DOWN,h.ARROW_UP,h.ARROW_LEFT,h.ARROW_RIGHT,h.HOME,h.END,h.DELETE,h.BACKSPACE,h.F1,h.F2,h.F3,h.F4,h.F5,h.F6,h.F7,h.F8,h.F9,h.F10,h.F11,h.F12,h.TAB,h.PAGE_DOWN,h.PAGE_UP,h.ENTER,h.ESCAPE,h.SHIFT,h.CAPS_LOCK,h.ALT,[h.ARROW_DOWN,h.ARROW_UP,h.ARROW_LEFT,h.ARROW_RIGHT]),b=[].concat(A,[h.TAB,h.ENTER]);function v(e){return-1!==b.indexOf(e)}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var y=function(e){return Array.isArray(e.name)?e.name.length:1},m=function(e){return Math.max.apply(Math,function(e){if(Array.isArray(e))return g(e)}(t=e.map(y))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(e,t):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var t};function w(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];if(!e.name||Array.isArray(e.name)&&e.name.length<n||!r)return{groupIndexFirst:o,groupIndexLast:o};if(i)for(var a=o;a>=0;--a){var s=t[a];if(!(s.name&&Array.isArray(s.name)&&s.name.length>n&&s.name[n]===e.name[n]))break;o=a}for(var l=o,c=o;c<t.length;++c){var u=t[c];if(!(u.name&&Array.isArray(u.name)&&u.name.length>n&&u.name[n]===e.name[n]))break;l=c}return{groupIndexFirst:o,groupIndexLast:l}}function k(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=C(e,t,n,r,t.indexOf(e),o),s=a.groupIndexFirst,l=a.groupIndexLast;return i.tPi(s,l+1,i.jge("id",t))}function x(e,t,n,r,o,i){return{data:O(e,t,n,r,o,i).data}}function O(e,t,n,r,o,a){var s=k(e,n,r,o);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?w(Object(n),!0).forEach((function(t){E(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({columns:i.hXT((function(e){return-1===s.indexOf(e.id)}),t),data:i.UID(i.CEd(s),a)},B)}function S(e,t,n,r){var o=C(e,t,n,r,t.indexOf(e)),a=o.groupIndexFirst,s=o.groupIndexLast;return i.UID((function(e){return e.id}),t.slice(a,s+1))}var B={active_cell:void 0,start_cell:void 0,end_cell:void 0,selected_cells:[]};var _=n(4167),j=n(8102);function P(){P=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var o=t&&t.prototype instanceof p?t:p,i=Object.create(o.prototype),a=new C(r||[]);return i._invoke=function(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=m(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=c(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}(e,n,a),i}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u={};function p(){}function d(){}function f(){}var h={};s(h,o,(function(){return this}));var A=Object.getPrototypeOf,b=A&&A(A(k([])));b&&b!==t&&n.call(b,o)&&(h=b);var v=f.prototype=p.prototype=Object.create(h);function g(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function r(o,i,a,s){var l=c(e[o],e,i);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==typeof p&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(p).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(l.arg)}var o;this._invoke=function(e,n){function i(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(i,i):i()}}function m(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,m(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=c(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function k(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return d.prototype=f,s(v,"constructor",f),s(f,"constructor",d),d.displayName=s(f,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,s(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},g(y.prototype),s(y.prototype,i,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new y(l(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(v),s(v,a,"Generator"),s(v,o,(function(){return this})),s(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=k,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function D(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function I(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){D(i,r,o,a,s,"next",e)}function s(e){D(i,r,o,a,s,"throw",e)}a(void 0)}))}}function F(e){var t=[],n=i.h0F(i.Ed_),r=i.h0F(i.Ed_);return n((function(e,n){var o={};r((function(e,r){o[e]?r===o[e].e.c+1?o[e].e={r:n,c:r}:(t.push(o[e]),o[e]={s:{r:n,c:r},e:{r:n,c:r}}):o[e]={s:{r:n,c:r},e:{r:n,c:r}}}),e);var a=Object.values(o);t=i.zoF(t,a)}),e),i.hXT((function(e){return e.s.c!==e.e.c||e.s.r!==e.e.r}),t)}function T(e,t,n,r,o){return M.apply(this,arguments)}function M(){return(M=I(P().mark((function e(t,n,r,o,a){var s,l,c,u;return P().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j.Z.xlsx;case 2:return s=e.sent,l=s.utils.aoa_to_sheet([]),n=i.UID(i.eiS(r))(n),o===_.CR.Display||o===_.CR.Names||o===_.CR.None?(s.utils.sheet_add_json(l,t,{skipHeader:!0}),c=t.length>0?{header:r,skipHeader:!0,origin:t.length}:{skipHeader:!0},s.utils.sheet_add_json(l,n,c),o===_.CR.Display&&a&&(l["!merges"]=F(t))):o===_.CR.Ids&&s.utils.sheet_add_json(l,n,{header:r}),u=s.utils.book_new(),s.utils.book_append_sheet(u,l,"SheetJS"),e.abrupt("return",u);case 9:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e,t){return z.apply(this,arguments)}function z(){return(z=I(P().mark((function e(t,n){var r;return P().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j.Z.xlsx;case 2:r=e.sent,"xlsx"===n?r.writeFile(t,"Data.xlsx",{bookType:"xlsx",type:"buffer"}):"csv"===n&&r.writeFile(t,"Data.csv",{bookType:"csv",type:"buffer"});case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e,t){var n=function(e,t){return e.map((function(e){return e instanceof Array&&e.length<t?e.concat(Array(t-e.length).fill("")):0===t||1===t?[e]:e instanceof String||"string"==typeof e?Array(t).fill(e):e}))}(e,t);return i.p4s(n)}function L(){L=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var o=t&&t.prototype instanceof p?t:p,i=Object.create(o.prototype),a=new C(r||[]);return i._invoke=function(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=m(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=c(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}(e,n,a),i}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u={};function p(){}function d(){}function f(){}var h={};s(h,o,(function(){return this}));var A=Object.getPrototypeOf,b=A&&A(A(k([])));b&&b!==t&&n.call(b,o)&&(h=b);var v=f.prototype=p.prototype=Object.create(h);function g(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function r(o,i,a,s){var l=c(e[o],e,i);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==typeof p&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(p).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(l.arg)}var o;this._invoke=function(e,n){function i(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(i,i):i()}}function m(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,m(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=c(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function k(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return d.prototype=f,s(v,"constructor",f),s(f,"constructor",d),d.displayName=s(f,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,s(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},g(y.prototype),s(y.prototype,i,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new y(l(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(v),s(v,a,"Generator"),s(v,o,(function(){return this})),s(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=k,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function q(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}var W=o().memo((function(e){var t=e.columns,n=e.export_columns,r=e.export_format,i=e.virtual_data,a=e.export_headers,s=e.visibleColumns,l=e.merge_duplicate_headers,c=r===_.T2.Csv||r===_.T2.Xlsx,u=n===_.Pj.Visible?s:t,p=function(){var e,n=(e=L().mark((function e(){var n,o,s,c,p;return L().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=u.map((function(e){return e.id})),o=u.map((function(e){return e.name})),s=m(t),c=a!==_.CR.None?N(o,s):[],e.next=6,T(c,i.data,n,a,l);case 6:return p=e.sent,e.next=9,R(p,r);case 9:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){q(i,r,o,a,s,"next",e)}function s(e){q(i,r,o,a,s,"throw",e)}a(void 0)}))});return function(){return n.apply(this,arguments)}}();return o().createElement("div",null,c?o().createElement("button",{className:"export",onClick:p},"Export"):null)}));function U(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function V(e){var t=i.jge("row",e),n=i.jge("column",e);return{minRow:i.u4g(i.VV$,1/0,t),minCol:i.u4g(i.VV$,1/0,n),maxRow:i.u4g(i.Fp7,0,t),maxCol:i.u4g(i.Fp7,0,n)}}function H(e,t){var n=V(t),r=n.minRow,o=n.minCol,i=n.maxRow,a=n.maxCol,s=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return U(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?U(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,2),l=s[0],c=s[1],u=[l,c];return l>i&&(u[0]=r,u[1]=c+1>a?o:c+1),l<r&&(u[0]=i,u[1]=c-1<o?a:c-1),c>a&&(u[1]=o,u[0]=l+1>i?r:l+1),c<o&&(u[1]=a,u[0]=l-1<o?i:l-1),u}function Y(e,t,n,r){var o={row:e,column:t,column_id:n[t].id},i=r.data[e].id;return void 0!==i&&(o.row_id=i),o}function K(e,t,n){var r=e.minRow,o=e.maxRow,a=e.minCol,s=e.maxCol;return(0,i.UID)((function(e){return Y(e[0],e[1],t,n)}),(0,i.icZ)((0,i.w6H)(r,o+1),(0,i.w6H)(a,s+1)))}var Z=function(e){var t=document.createElement("div");t.style.position="absolute",t.style.visibility="hidden",t.style.width="100px",t.style.height="100px",t.style.overflow="scroll";var n=document.createElement("div");return n.style.width="100px",n.style.height="100px",t.appendChild(n),e.appendChild(t),new Promise((function(r){setTimeout((function(){var o=n.clientWidth-t.clientWidth;e.removeChild(t),r(o)}),0)}))};function Q(e,t,n){for(var r=e.length,o=new Array(r),i=0;i<r;++i)o[i]=n(e[i],t[i],i);return o}function $(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function G(e,t){for(var n=[],r=function(){var r;t=t.replace(/^\s+/,"");var o=n.slice(-1)[0],a=o?o.lexeme:null,s=e.filter((function(e){return e.if&&(Array.isArray(e.if)?a?e.if&&-1!==e.if.indexOf(a.type):e.if&&-1!==e.if.indexOf(void 0):e.if(n,o))})),l=i.sEJ((function(e){return e.regexp.test(t)}),s);if(!l)return{v:{lexemes:n,valid:!1,error:t}};var c=null!==(r=t.match(l.regexp))&&void 0!==r?r:[],u=c[l.regexpMatch||0],p=c[l.regexpFlags||-1];n.push({lexeme:l,flags:p,value:u}),t=t.substring(u.length)};t.length;){var o=r();if("object"==typeof o)return o.v}var a=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return $(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}([void 0,void 0].concat(n).slice(-2),2),s=a[0],l=a[1],c=!l||("function"==typeof l.lexeme.terminal?l.lexeme.terminal(n,s):l.lexeme.terminal);return{lexemes:n,valid:c}}var J=n(4490),X=n.n(J);function ee(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var te=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"set",value:function(e,t){e.clipboardData.setData("text/plain",t),e.preventDefault()}},{key:"get",value:function(e){return e.clipboardData?e.clipboardData.getData("text/plain"):void 0}}],null&&ee(t.prototype,null),n&&ee(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),ne=function(e,t){return{success:!0,value:e}};function re(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}var oe,ie=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ae(e){if(!(t=ie.exec(e)))throw new Error("invalid format: "+e);var t;return new se({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function se(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function le(e,t){var n=re(e,t);if(!n)return e+"";var r=n[0],o=n[1];return o<0?"0."+new Array(-o).join("0")+r:r.length>o+1?r.slice(0,o+1)+"."+r.slice(o+1):r+new Array(o-r.length+2).join("0")}ae.prototype=se.prototype,se.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var ce={"%":function(e,t){return(100*e).toFixed(t)},b:function(e){return Math.round(e).toString(2)},c:function(e){return e+""},d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:function(e,t){return e.toExponential(t)},f:function(e,t){return e.toFixed(t)},g:function(e,t){return e.toPrecision(t)},o:function(e){return Math.round(e).toString(8)},p:function(e,t){return le(100*e,t)},r:le,s:function(e,t){var n=re(e,t);if(!n)return e+"";var r=n[0],o=n[1],i=o-(oe=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=r.length;return i===a?r:i>a?r+new Array(i-a+1).join("0"):i>0?r.slice(0,i)+"."+r.slice(i):"0."+new Array(1-i).join("0")+re(e,Math.max(0,t+i-1))[0]},X:function(e){return Math.round(e).toString(16).toUpperCase()},x:function(e){return Math.round(e).toString(16)}};function ue(e){return e}var pe=Array.prototype.map,de=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];var fe=n(5924),he=n.n(fe),Ae=function(e,t){var n=Boolean(t&&t.validation&&t.validation.allow_null),r=be(e);return{success:r&&n,value:r?null:e}},be=function(e){return null==e||"number"==typeof e&&(isNaN(e)||!isFinite(e))},ve=["group","symbol"];function ge(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var me=function(e){var t=e.group,n=e.symbol,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,ve);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ge(Object(n),!0).forEach((function(t){ye(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ge(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({currency:n,thousands:t},i.CEd(["separate_4digits","symbol"],r))};function we(e,t){return he()(e)?{success:!0,value:+e}:Ae(e,t)}function Ee(e,t){return"number"!=typeof e||be(e)?Ae(e,t):{success:!0,value:e}}function Ce(e,t){return be(e)?Ae(e,t):"string"==typeof e?{success:!0,value:e}:{success:!0,value:JSON.stringify(e)}}function ke(e,t){return"string"==typeof e?{success:!0,value:e}:Ae(e,t)}var xe=/^\s*(-?\d{4}|\d{2})(-(\d{1,2})(-(\d{1,2})([ Tt]([01]?\d|2[0-3])(:([0-5]\d)(:([0-5]\d(\.\d+)?))?(Z|z|[+\-]\d{2}:?\d{2})?)?)?)?)?\s*$/m,Oe=(new Date).getFullYear()-70;function Se(e,t){if("string"!=typeof e)return null;var n=e.match(xe);if(!n)return null;var r=n[1],o=2===r.length;if(o&&(!t||!t.allow_YY))return null;var i=o?(Number(r)+2e3-Oe)%100+Oe:Number(r),a=i<0,s=n[3],l=Number(s||"1")-1,c=n[5],u=Number(c||1),p=n[7],d=Number(p||0),f=n[9],h=Number(f||0),A=n[11],b=new Date(Date.UTC(2e3,l,u,d,h));if(b.setUTCFullYear(i),b.getUTCMonth()!==l||b.getUTCDate()!==u)return null;var v=A?29:f?16:p?13:c?10:s?7:4;return(a?"-":"")+(b.toISOString().substr(a?3:0,17).replace("T"," ")+(A||"")).substr(0,v)}function Be(e,t){var n=Se(e,t&&t.validation);return null!==n?{success:!0,value:n}:Ae(e,t)}function _e(e,t){return"string"==typeof e&&null!==Se(e,t&&t.validation)?{success:!0,value:e.trim()}:Ae(e,t)}function je(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?je(Object(n),!0).forEach((function(t){De(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):je(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function De(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ie=function(e,t){var n=function(e,t){var n=t&&t.on_change&&t.on_change.action||_.KI.Coerce;switch(n){case _.KI.Coerce:return Pe({action:n},function(e){switch(e.type){case _.QD.Numeric:return we;case _.QD.Text:return Ce;case _.QD.Datetime:return Be;case _.QD.Any:default:return ne}}(t)(e,t));case _.KI.None:return{success:!0,value:e,action:n};case _.KI.Validate:return Pe({action:n},function(e){switch(e.type){case _.QD.Numeric:return Ee;case _.QD.Text:return ke;case _.QD.Datetime:return _e;case _.QD.Any:default:return ne}}(t)(e,t))}}(e,t);return n.success?n:function(e,t){var n=t&&t.on_change&&t.on_change.failure||_.UT.Reject;if(e.failure=n,n===_.UT.Default){var r=t&&t.validation&&t.validation.default,o=i.kKJ(r)?null:r;e.success=!0,e.value=o}else n===_.UT.Accept&&(e.success=!0);return e}(n,t)};function Fe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||Me(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Me(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function Me(e,t){if(e){if("string"==typeof e)return Re(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Re(e,t):void 0}}function Re(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ze=function(e,t,n,r,o,a){var l=!(arguments.length>6&&void 0!==arguments[6])||arguments[6],c=!(arguments.length>7&&void 0!==arguments[7])||arguments[7];c||s.ZP.debug("Clipboard -- Sorting or filtering active, do not create new rows"),l||s.ZP.debug("Clipboard -- Do not create new columns");var u=i.d9v(a),p=r.slice(0),d=o.slice(0);if(l&&e[0].length+t.column>=o.length){for(var f=[],h=function(e){f.push({id:"Column ".concat(e+1),name:"Column ".concat(e+1),type:_.QD.Any,sort_as_null:[]}),u.forEach((function(t){return t["Column ".concat(e)]=""}))},A=o.length;A<e[0].length+t.column;A++)h(A);p=i.cZv(i.cq5(i.Z$Q(o),r)+1,f,p),d=i.zoF(d,f)}var b=n[t.row];if(c&&e.length+b>=a.length){var v={};o.forEach((function(e){return v[e.id]=""})),u=i.zoF(u,i.rx1(v,e.length+b-a.length))}var g,y=n.slice(-1)[0]||0,m=n.length,w=Te(e.entries());try{for(w.s();!(g=w.n()).done;){var E,C=Fe(g.value,2),k=C[0],x=C[1],O=Te(x.entries());try{for(O.s();!(E=O.n()).done;){var S=Fe(E.value,2),B=S[0],j=S[1],P=t.row+k,D=m>P?n[P]:c?y+(P-m+1):void 0;if(void 0!==D){var I=t.column+B,F=d[I];if(F&&F.editable){var T=Ie(j,F);T.success&&(u=i.t8m(i.QMA([D,F.id]),T.value,u))}}}}catch(e){O.e(e)}finally{O.f()}}}catch(e){w.e(e)}finally{w.f()}return{data:u,columns:p}};function Ne(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Le(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var qe=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"toClipboard",value:function(t,n,r,o,a,l){var c=i.jj$(i.jge("row",n).sort((function(e,t){return e-t}))),u=i.jj$(i.jge("column",n).sort((function(e,t){return e-t}))),p=i.tPi(i.YMb(c),i.Z$Q(c)+1,a).map((function(e){return i.NQ5(u,i.NQ5(i.jge("id",o),e))})),d=X().prototype.stringify(p);if(e.lastLocalCopy=p,l){var f=N(i.jge("name",o),m(r)),h=i.UID((function(e){return i.UID((function(t){return e[t]}),u)}),f).concat(p);d=X().prototype.stringify(h),e.lastLocalCopy=h,e.localCopyWithoutHeaders=p}s.ZP.trace("TableClipboard -- set clipboard data: ",d),te.set(t,d)}},{key:"clearClipboard",value:function(){e.lastLocalCopy=[],e.localCopyWithoutHeaders=[]}},{key:"fromClipboard",value:function(t,n,r,o,i,a){var l=!(arguments.length>6&&void 0!==arguments[6])||arguments[6],c=!(arguments.length>7&&void 0!==arguments[7])||arguments[7],u=arguments.length>8?arguments[8]:void 0,p=te.get(t);if(s.ZP.trace("TableClipboard -- get clipboard data: ",p),p){var d=X().prototype.stringify(e.lastLocalCopy),f=u?e.localCopyWithoutHeaders:e.lastLocalCopy,h=d===p?f:e.parse(p);return ze(h,n,r,o,i,a,l,c)}}},{key:"parse",value:function(t){var n,r,o,i,a,s,l=0,c=[[]],u=t.split("\n");for(u.length>1&&""===u[u.length-1]&&u.pop(),c=[],n=0,r=u.length;n<r;n+=1){var p=u[n].split("\t");for(o=0,i=p.length;o<i;o+=1)c[l]||(c[l]=[]),a&&0===o?(s=c[l].length-1,c[l][s]=c[l][s]+"\n"+p[0].replace(/""/g,'"'),a&&1&e.countQuotes(p[0])&&(a=!1,c[l][s]=c[l][s].substring(0,c[l][s].length-1))):o===i-1&&0===p[o].indexOf('"')&&1&e.countQuotes(p[o])?(c[l].push(p[o].substring(1).replace(/""/g,'"')),a=!0):(c[l].push(p[o]),a=!1);a||(l+=1)}return c}},{key:"countQuotes",value:function(e){return e.split('"').length-1}}],null&&Ne(t.prototype,null),n&&Ne(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();Le(qe,"lastLocalCopy",[[]]),Le(qe,"localCopyWithoutHeaders",[[]]);var We=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t=t||function(){for(t=e;t&&"td"!==t.nodeName.toLowerCase();)t=t.parentElement;return t}(),e&&t){for(var n=e;"relative"!==getComputedStyle(n).position&&"sticky"!==getComputedStyle(n).position&&n.parentElement;)n=n.parentElement;for(var r=e;"relative"!==getComputedStyle(r).position&&r.parentElement;)r=r.parentElement;var o=n.getBoundingClientRect(),i=r.getBoundingClientRect(),a=t.getBoundingClientRect(),s=a.left-o.left+n.scrollLeft,l=a.top-o.top+n.scrollTop+a.height;e.style.width="".concat(a.width,"px"),e.style.top="".concat(l,"px"),e.style.left="".concat(s,"px"),e.style.position="absolute",a.top+a.height/2>i.bottom||a.top-a.height/2<i.top||a.left<i.left||a.left+.25*a.width>i.left+i.width?e.style.display="none":e.style.display="block"}},Ue=function(e,t,n){return"last"===n?e===t:"first"===n?0===e:"boolean"==typeof n?n:!!n&&n[e]},Ve=(0,a.Pi)((function(e,t,n){var r=function(e,t){return i.UID((function(n){return e.map((function(e){return i.kKJ(e.name)&&n===t-1?e.id:function(e,t){return Array.isArray(e.name)?e.name[t]:e.name}(e,n)}))}),i.w6H(0,t))}(t,m(e)),o=function(e,t,n){return i.UID((function(t){if(n){var r=[0],o=0;return t.forEach((function(e,n){e!==t[o]&&(r.push(n),o=n)})),r}return i.w6H(0,e.length)}),t)}(t,r,n);return i.$Re(r,o)}));function He(e){for(var t=e.length,n=new Array(t),r=0;r<t;++r)n[r]=e[r].slice(0);return n}function Ye(e,t,n){for(var r=e.length,o=t.length,i=0;i<r;++i)for(var a=0;a<o;++a)n(e[i],t[a],i,a)}function Ke(e,t,n){for(var r=e.length,o=t.length,i=new Array(r),a=0;a<r;++a){for(var s=new Array(o),l=0;l<o;++l)s[l]=n(e[a],t[l],a,l);i[a]=s}return i}function Ze(e,t,n){for(var r=e.length,o=new Array(r),i=0;i<r;++i){for(var a=e[i].length,s=new Array(a),l=0;l<a;++l)s[l]=n(e[i][l],t?t[i][l]:void 0,i,l);o[i]=s}return o}function Qe(e,t,n,r){for(var o=e.length,i=new Array(o),a=0;a<o;++a){for(var s=e[a].length,l=new Array(s),c=0;c<s;++c)l[c]=r(e[a][c],t?t[a][c]:void 0,n?n[a][c]:void 0,a,c);i[a]=l}return i}function $e(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=n.slice(0,-1);return i.u4g((function(e,t){return e.get(t)||e.set(t,new Map).get(t)}),e,o)}var Ge=function(){return function(e){var t=new Map;return{get:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=r.slice(-1)[0],s=$e.apply(void 0,[t].concat(r));return s.get(i)||s.set(i,(0,a.qe)(e)).get(i)}}}};function Je(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Xe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function et(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function tt(e,t){return tt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},tt(e,t)}function nt(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function rt(e){return rt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},rt(e)}var ot,it=/^children$/,at=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tt(e,t)}(l,e);var t,n,r,a,s=(r=l,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=rt(r);if(a){var n=rt(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return nt(this,e)});function l(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),s.call(this,e)}return t=l,n=[{key:"propsWithDefaults",get:function(){return this.props}},{key:"render",value:function(){var e=this.propsWithDefaults,t=e.attributes,n=e.className,r=e.onClick,i=e.onDoubleClick,a=e.onMouseEnter,s=e.onMouseLeave,l=e.onMouseMove,c=e.style;return o().createElement("td",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Je(Object(n),!0).forEach((function(t){Xe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Je(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({ref:"td",children:this.props.children,tabIndex:-1,className:n,onClick:r,onDoubleClick:i,onMouseEnter:a,onMouseLeave:s,onMouseMove:l,onMouseOver:l,style:c},t))}},{key:"shouldComponentUpdate",value:function(e){var t=this.props,n=this.getChildProps(t),r=this.getChildProps(e);return i.YjB((function(n){return!it.test(n)&&t[n]!==e[n]}),i.p8H(t))||i.YjB((function(e){return n[e]!==r[e]}),i.p8H(n))}},{key:"getChildProps",value:function(e){return e&&e.children&&e.children[0]&&e.children[0].props}}],n&&et(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.Component),st=function(e,t,n){return!!e&&e.row===t&&e.column===n},lt=function(e,t,n,r){var o=e(),a=o.cell_selectable,s=o.selected_cells,l=o.active_cell,c=o.setProps,u=o.viewport,p=o.virtualized,d=o.visibleColumns,f=n+p.offset.columns,h=Y(t,f,d,u);if((!l||t!==l.row||f!==l.column)&&(d[f].presentation!==_.Ap.Markdown&&r.preventDefault(),a)){var A=window.getSelection();A&&A.removeAllRanges();var b=function(e,t,n){return i.YjB((function(e){return e.row===t&&e.column===n}),e)}(s,t,f);if(!b||r.shiftKey){var v={is_focused:!1,end_cell:h};r.shiftKey&&l?v.selected_cells=K({minRow:(0,i.VV$)(t,l.row),maxRow:(0,i.Fp7)(t,l.row),minCol:(0,i.VV$)(f,l.column),maxCol:(0,i.Fp7)(f,l.column)},d,u):(v.active_cell=h,v.start_cell=h,v.selected_cells=[h]),c(v)}else c({is_focused:!1,active_cell:h})}},ct=function(e,t,n,r){var o=e(),i=o.is_focused,a=o.setProps,s=o.viewport,l=o.virtualized,c=o.visibleColumns;if(c[n].editable){var u=Y(t,n+l.offset.columns,c,s);i||(r.preventDefault(),a({selected_cells:[u],active_cell:u,start_cell:u,end_cell:u,is_focused:!0}))}},ut=function(e,t,n,r){var o=e(),a=o.data,s=o.setProps,l=o.virtualized,c=o.visibleColumns[n],u=l.indices[t-l.offset.rows];if(c.editable){var p=Ie(r,c);p.success&&s({data:(0,i.t8m)((0,i.QMA)([u,c.id]),p.value,a)})}},pt=function(e,t,n){var r=e(),o=r.setState,i=r.virtualized;o({currentTooltip:{header:!1,id:r.visibleColumns[n].id,row:i.indices[t-i.offset.rows]}})},dt=function(e,t,n){var r=e();(0,r.setState)({currentTooltip:{header:!0,id:r.visibleColumns[n].id,row:t}})},ft=function(e,t,n){(0,e().setState)({currentTooltip:void 0})},ht=function(e,t,n){var r=e(),o=r.currentTooltip,i=r.setState,a=r.virtualized,s=r.visibleColumns[n],l=a.indices[t-a.offset.rows];o&&o.id===s.id&&o.row===l&&!o.header||i({currentTooltip:{header:!1,id:s.id,row:l}})},At=function(e,t,n){var r=e(),o=r.currentTooltip,i=r.setState,a=r.visibleColumns[n];o&&o.id===a.id&&o.row===t&&o.header||i({currentTooltip:{header:!0,id:a.id,row:t}})},bt=function(e,t,n,r){var o=e(),i=o.active_cell,a=o.is_focused,s=st(i,t,n);if(!a&&s){r.preventDefault();var l=r.target;l.setSelectionRange(0,l.value?l.value.length:0)}},vt=function(e,t,n,r){r.preventDefault()};function gt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function yt(e,t,n){return t&&gt(e.prototype,t),n&&gt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function mt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e){e.Change="change",e.Click="click",e.DoubleClick="doubleclick",e.Enter="enter",e.EnterHeader="enterheader",e.Leave="leave",e.Move="move",e.MoveHeader="moveheader",e.MouseUp="mouseup",e.Paste="paste"}(ot||(ot={}));var wt=function(e){return new Et(e).get},Et=yt((function e(t){var n,r,o=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),mt(this,"propsFn",void 0),mt(this,"cache",(n=function(e,t,n){switch(e){case ot.Change:return ut.bind(void 0,o.propsFn,t,n);case ot.Click:return lt.bind(void 0,o.propsFn,t,n);case ot.DoubleClick:return ct.bind(void 0,o.propsFn,t,n);case ot.Enter:return pt.bind(void 0,o.propsFn,t,n);case ot.EnterHeader:return dt.bind(void 0,o.propsFn,t,n);case ot.Leave:return ft.bind(void 0,o.propsFn,t,n);case ot.Move:return ht.bind(void 0,o.propsFn,t,n);case ot.MoveHeader:return At.bind(void 0,o.propsFn,t,n);case ot.MouseUp:return bt.bind(void 0,o.propsFn,t,n);case ot.Paste:return vt.bind(void 0,o.propsFn,t,n);default:throw new Error("unexpected handler ".concat(e))}},r=new Map,{get:function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];var i=t.slice(-1)[0],a=$e.apply(void 0,[r].concat(t));return a.has(i)?a.get(i):a.set(i,n.apply(void 0,t)).get(i)}})),mt(this,"get",(function(e,t,n){return o.cache.get(e,t,n)})),this.propsFn=t}));function Ct(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function xt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ot=function(e){return new St(e)},St=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt(t);Ct(this,e),xt(this,"handlers",void 0),xt(this,"partialGet",(0,a.qe)((function(e,t,r){return i.h0F(i.UID)((function(t,o){return i.h0F(i.UID)((function(e,t){return n.getWrapper(!1,!1,o+r.rows,t,e)}),e)}),t)}))),xt(this,"get",(0,a.qe)((function(e,t,n,r){e=He(e);var a=r.length?r:n?[n]:[];return i.Ed_((function(r){var i=r.row,a=r.column;if(i-=t.rows,a-=t.columns,!(i<0||a<0||e.length<=i||e[i].length<=a)){var s=e[i][a],l=st(n,i+t.rows,a+t.columns);e[i][a]=o().cloneElement(s,{className:s.props.className+" cell--selected"+(l?" focused":"")})}}),a),e}))),xt(this,"wrapper",Ge()((function(e,t,n,r,i,a,s,l,c,u){return o().createElement(at,{active:e,attributes:{"data-dash-column":r,"data-dash-row":i},className:t,key:"column-".concat(n),onClick:c,onDoubleClick:u,onMouseEnter:a,onMouseLeave:s,onMouseMove:l})}))),this.handlers=r}var t,n;return t=e,n=[{key:"getWrapper",value:function(e,t,n,r,o){var i=o.presentation===_.Ap.Dropdown,a="dash-cell"+" column-".concat(r)+(e?" focused":"")+(t?" cell--selected":"")+(i?" dropdown":"");return this.wrapper.get(n,r)(e,a,r,o.id,n,this.handlers(ot.Enter,n,r),this.handlers(ot.Leave,n,r),this.handlers(ot.Move,n,r),this.handlers(ot.Click,n,r),this.handlers(ot.DoubleClick,n,r))}}],n&&kt(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Bt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _t(e,t){return _t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_t(e,t)}function jt(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Pt(e)}function Pt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dt(e){return Dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Dt(e)}function It(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ft=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_t(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Dt(r);if(i){var n=Dt(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return jt(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),It(Pt(t=a.call(this,e)),"propagateChange",(function(){t.state.value!==t.props.value&&(0,t.props.onChange)(t.state.value)})),It(Pt(t),"handleChange",(function(e){t.setState({value:e.target.value})})),It(Pt(t),"handleKeyDown",(function(e){var n=t.props.focused;n&&e.keyCode!==h.TAB&&e.keyCode!==h.ENTER||(n||v(e.keyCode))&&t.propagateChange()})),t.state={value:e.value},t}return t=s,(n=[{key:"render",value:function(){var e=this.props,t=e.className,n=e.onMouseUp,r=e.onPaste,i=e.value,a=null===this.state.value?void 0:this.state.value;return o().createElement("div",{className:"dash-input-cell-value-container dash-cell-value-container"},o().createElement("div",{className:"input-cell-value-shadow cell-value-shadow"},i),o().createElement("input",{ref:"textInput",type:"text",className:t,onBlur:this.propagateChange,onChange:this.handleChange,onKeyDown:this.handleKeyDown,onMouseUp:n,onPaste:r,value:a}))}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=e.value;this.state.value!==t&&this.setState({value:t})}},{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.refs.textInput;n&&r&&document.activeElement!==r&&(r.focus(),r.setSelectionRange(0,r.value?r.value.length:0))}}}])&&Bt(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent);function Tt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Rt(e,t){return Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Rt(e,t)}function zt(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Nt(e){return Nt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Nt(e)}var Lt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rt(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Nt(r);if(i){var n=Nt(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return zt(this,e)});function s(){return Tt(this,s),a.apply(this,arguments)}return t=s,(n=[{key:"render",value:function(){var e=this.props,t=e.className,n=e.value;return o().createElement("div",{ref:"el",className:t,tabIndex:-1},"boolean"==typeof n?n.toString():n)}},{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r,o=this.refs.el;n&&o&&document.activeElement!==o&&(null===(r=window.getSelection())||void 0===r||r.selectAllChildren(o),o.focus())}}}])&&Mt(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent),qt=n(5639),Wt=n(4184),Ut=n.n(Wt),Vt=n(9064),Ht=n.n(Vt),Yt=n(1850),Kt=n.n(Yt),Zt=function(e){var t=e.onMouseDown;return o().createElement("span",{className:"Select-arrow",onMouseDown:t})};Zt.propTypes={onMouseDown:Ht().func};var Qt=[{base:"A",letters:/[\u0041\u24B6\uFF21\u00C0\u00C1\u00C2\u1EA6\u1EA4\u1EAA\u1EA8\u00C3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\u00C4\u01DE\u1EA2\u00C5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F]/g},{base:"AA",letters:/[\uA732]/g},{base:"AE",letters:/[\u00C6\u01FC\u01E2]/g},{base:"AO",letters:/[\uA734]/g},{base:"AU",letters:/[\uA736]/g},{base:"AV",letters:/[\uA738\uA73A]/g},{base:"AY",letters:/[\uA73C]/g},{base:"B",letters:/[\u0042\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0182\u0181]/g},{base:"C",letters:/[\u0043\u24B8\uFF23\u0106\u0108\u010A\u010C\u00C7\u1E08\u0187\u023B\uA73E]/g},{base:"D",letters:/[\u0044\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018B\u018A\u0189\uA779]/g},{base:"DZ",letters:/[\u01F1\u01C4]/g},{base:"Dz",letters:/[\u01F2\u01C5]/g},{base:"E",letters:/[\u0045\u24BA\uFF25\u00C8\u00C9\u00CA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\u00CB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E]/g},{base:"F",letters:/[\u0046\u24BB\uFF26\u1E1E\u0191\uA77B]/g},{base:"G",letters:/[\u0047\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E]/g},{base:"H",letters:/[\u0048\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D]/g},{base:"I",letters:/[\u0049\u24BE\uFF29\u00CC\u00CD\u00CE\u0128\u012A\u012C\u0130\u00CF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197]/g},{base:"J",letters:/[\u004A\u24BF\uFF2A\u0134\u0248]/g},{base:"K",letters:/[\u004B\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2]/g},{base:"L",letters:/[\u004C\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780]/g},{base:"LJ",letters:/[\u01C7]/g},{base:"Lj",letters:/[\u01C8]/g},{base:"M",letters:/[\u004D\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C]/g},{base:"N",letters:/[\u004E\u24C3\uFF2E\u01F8\u0143\u00D1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u0220\u019D\uA790\uA7A4]/g},{base:"NJ",letters:/[\u01CA]/g},{base:"Nj",letters:/[\u01CB]/g},{base:"O",letters:/[\u004F\u24C4\uFF2F\u00D2\u00D3\u00D4\u1ED2\u1ED0\u1ED6\u1ED4\u00D5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\u00D6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\u00D8\u01FE\u0186\u019F\uA74A\uA74C]/g},{base:"OI",letters:/[\u01A2]/g},{base:"OO",letters:/[\uA74E]/g},{base:"OU",letters:/[\u0222]/g},{base:"P",letters:/[\u0050\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754]/g},{base:"Q",letters:/[\u0051\u24C6\uFF31\uA756\uA758\u024A]/g},{base:"R",letters:/[\u0052\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782]/g},{base:"S",letters:/[\u0053\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784]/g},{base:"T",letters:/[\u0054\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786]/g},{base:"TZ",letters:/[\uA728]/g},{base:"U",letters:/[\u0055\u24CA\uFF35\u00D9\u00DA\u00DB\u0168\u1E78\u016A\u1E7A\u016C\u00DC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244]/g},{base:"V",letters:/[\u0056\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245]/g},{base:"VY",letters:/[\uA760]/g},{base:"W",letters:/[\u0057\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72]/g},{base:"X",letters:/[\u0058\u24CD\uFF38\u1E8A\u1E8C]/g},{base:"Y",letters:/[\u0059\u24CE\uFF39\u1EF2\u00DD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE]/g},{base:"Z",letters:/[\u005A\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762]/g},{base:"a",letters:/[\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250]/g},{base:"aa",letters:/[\uA733]/g},{base:"ae",letters:/[\u00E6\u01FD\u01E3]/g},{base:"ao",letters:/[\uA735]/g},{base:"au",letters:/[\uA737]/g},{base:"av",letters:/[\uA739\uA73B]/g},{base:"ay",letters:/[\uA73D]/g},{base:"b",letters:/[\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253]/g},{base:"c",letters:/[\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184]/g},{base:"d",letters:/[\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A]/g},{base:"dz",letters:/[\u01F3\u01C6]/g},{base:"e",letters:/[\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD]/g},{base:"f",letters:/[\u0066\u24D5\uFF46\u1E1F\u0192\uA77C]/g},{base:"g",letters:/[\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F]/g},{base:"h",letters:/[\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265]/g},{base:"hv",letters:/[\u0195]/g},{base:"i",letters:/[\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131]/g},{base:"j",letters:/[\u006A\u24D9\uFF4A\u0135\u01F0\u0249]/g},{base:"k",letters:/[\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3]/g},{base:"l",letters:/[\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747]/g},{base:"lj",letters:/[\u01C9]/g},{base:"m",letters:/[\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F]/g},{base:"n",letters:/[\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5]/g},{base:"nj",letters:/[\u01CC]/g},{base:"o",letters:/[\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275]/g},{base:"oi",letters:/[\u01A3]/g},{base:"ou",letters:/[\u0223]/g},{base:"oo",letters:/[\uA74F]/g},{base:"p",letters:/[\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755]/g},{base:"q",letters:/[\u0071\u24E0\uFF51\u024B\uA757\uA759]/g},{base:"r",letters:/[\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783]/g},{base:"s",letters:/[\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B]/g},{base:"t",letters:/[\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787]/g},{base:"tz",letters:/[\uA729]/g},{base:"u",letters:/[\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289]/g},{base:"v",letters:/[\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C]/g},{base:"vy",letters:/[\uA761]/g},{base:"w",letters:/[\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73]/g},{base:"x",letters:/[\u0078\u24E7\uFF58\u1E8B\u1E8D]/g},{base:"y",letters:/[\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF]/g},{base:"z",letters:/[\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763]/g}],$t=function(e){for(var t=0;t<Qt.length;t++)e=e.replace(Qt[t].letters,Qt[t].base);return e},Gt=function(e){return null!=e&&""!==e},Jt=function(e,t,n,r){return r.ignoreAccents&&(t=$t(t)),r.ignoreCase&&(t=t.toLowerCase()),r.trimFilter&&(t=t.replace(/^\s+|\s+$/g,"")),n&&(n=n.map((function(e){return e[r.valueKey]}))),e.filter((function(e){if(n&&n.indexOf(e[r.valueKey])>-1)return!1;if(r.filterOption)return r.filterOption.call(void 0,e,t);if(!t)return!0;var o=e[r.valueKey],i=e[r.labelKey],a=Gt(o),s=Gt(i);if(!a&&!s)return!1;var l=a?String(o):null,c=s?String(i):null;return r.ignoreAccents&&(l&&"label"!==r.matchProp&&(l=$t(l)),c&&"value"!==r.matchProp&&(c=$t(c))),r.ignoreCase&&(l&&"label"!==r.matchProp&&(l=l.toLowerCase()),c&&"value"!==r.matchProp&&(c=c.toLowerCase())),"start"===r.matchPos?l&&"label"!==r.matchProp&&l.substr(0,t.length)===t||c&&"value"!==r.matchProp&&c.substr(0,t.length)===t:l&&"label"!==r.matchProp&&l.indexOf(t)>=0||c&&"value"!==r.matchProp&&c.indexOf(t)>=0}))},Xt=function(e){var t=e.focusedOption,n=e.focusOption,r=e.inputValue,i=e.instancePrefix,a=e.onFocus,s=e.onOptionRef,l=e.onSelect,c=e.optionClassName,u=e.optionComponent,p=e.optionRenderer,d=e.options,f=e.removeValue,h=e.selectValue,A=e.valueArray,b=e.valueKey,v=u;return d.map((function(e,u){var d=A&&A.some((function(t){return t[b]===e[b]})),g=e===t,y=Ut()(c,{"Select-option":!0,"is-selected":d,"is-focused":g,"is-disabled":e.disabled});return o().createElement(v,{className:y,focusOption:n,inputValue:r,instancePrefix:i,isDisabled:e.disabled,isFocused:g,isSelected:d,key:"option-"+u+"-"+e[b],onFocus:a,onSelect:l,option:e,optionIndex:u,ref:function(e){s(e,g)},removeValue:f,selectValue:h},p(e,u,r))}))};Xt.propTypes={focusOption:Ht().func,focusedOption:Ht().object,inputValue:Ht().string,instancePrefix:Ht().string,onFocus:Ht().func,onOptionRef:Ht().func,onSelect:Ht().func,optionClassName:Ht().string,optionComponent:Ht().func,optionRenderer:Ht().func,options:Ht().array,removeValue:Ht().func,selectValue:Ht().func,valueArray:Ht().array,valueKey:Ht().string};var en=function(e){e.preventDefault(),e.stopPropagation(),"A"===e.target.tagName&&"href"in e.target&&(e.target.target?window.open(e.target.href,e.target.target):window.location.href=e.target.href)},tn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nn=(function(){function e(e){this.value=e}function t(t){var n,r;function o(n,r){try{var a=t[n](r),s=a.value;s instanceof e?Promise.resolve(s.value).then((function(e){o("next",e)}),(function(e){o("throw",e)})):i(a.done?"return":"normal",a.value)}catch(e){i("throw",e)}}function i(e,t){switch(e){case"return":n.resolve({value:t,done:!0});break;case"throw":n.reject(t);break;default:n.resolve({value:t,done:!1})}(n=n.next)?o(n.key,n.arg):r=null}this._invoke=function(e,t){return new Promise((function(i,a){var s={key:e,arg:t,resolve:i,reject:a,next:null};r?r=r.next=s:(n=r=s,o(e,t))}))},"function"!=typeof t.return&&(this.return=void 0)}"function"==typeof Symbol&&Symbol.asyncIterator&&(t.prototype[Symbol.asyncIterator]=function(){return this}),t.prototype.next=function(e){return this._invoke("next",e)},t.prototype.throw=function(e){return this._invoke("throw",e)},t.prototype.return=function(e){return this._invoke("return",e)}}(),function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}),rn=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),on=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},an=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},sn=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},ln=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},cn=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},un=function(e){function t(e){nn(this,t);var n=cn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleMouseDown=n.handleMouseDown.bind(n),n.handleMouseEnter=n.handleMouseEnter.bind(n),n.handleMouseMove=n.handleMouseMove.bind(n),n.handleTouchStart=n.handleTouchStart.bind(n),n.handleTouchEnd=n.handleTouchEnd.bind(n),n.handleTouchMove=n.handleTouchMove.bind(n),n.onFocus=n.onFocus.bind(n),n}return sn(t,e),rn(t,[{key:"handleMouseDown",value:function(e){e.preventDefault(),e.stopPropagation(),this.props.onSelect(this.props.option,e)}},{key:"handleMouseEnter",value:function(e){this.onFocus(e)}},{key:"handleMouseMove",value:function(e){this.onFocus(e)}},{key:"handleTouchEnd",value:function(e){this.dragging||this.handleMouseDown(e)}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"onFocus",value:function(e){this.props.isFocused||this.props.onFocus(this.props.option,e)}},{key:"render",value:function(){var e=this.props,t=e.option,n=e.instancePrefix,r=e.optionIndex,i=Ut()(this.props.className,t.className);return t.disabled?o().createElement("div",{className:i,onMouseDown:en,onClick:en},this.props.children):o().createElement("div",{className:i,style:t.style,role:"option","aria-label":t.label,onMouseDown:this.handleMouseDown,onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove,onTouchEnd:this.handleTouchEnd,id:n+"-option-"+r,title:t.title},this.props.children)}}]),t}(o().Component);un.propTypes={children:Ht().node,className:Ht().string,instancePrefix:Ht().string.isRequired,isDisabled:Ht().bool,isFocused:Ht().bool,isSelected:Ht().bool,onFocus:Ht().func,onSelect:Ht().func,onUnfocus:Ht().func,option:Ht().object.isRequired,optionIndex:Ht().number};var pn=function(e){function t(e){nn(this,t);var n=cn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleMouseDown=n.handleMouseDown.bind(n),n.onRemove=n.onRemove.bind(n),n.handleTouchEndRemove=n.handleTouchEndRemove.bind(n),n.handleTouchMove=n.handleTouchMove.bind(n),n.handleTouchStart=n.handleTouchStart.bind(n),n}return sn(t,e),rn(t,[{key:"handleMouseDown",value:function(e){if("mousedown"!==e.type||0===e.button)return this.props.onClick?(e.stopPropagation(),void this.props.onClick(this.props.value,e)):void(this.props.value.href&&e.stopPropagation())}},{key:"onRemove",value:function(e){e.preventDefault(),e.stopPropagation(),this.props.onRemove(this.props.value)}},{key:"handleTouchEndRemove",value:function(e){this.dragging||this.onRemove(e)}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"renderRemoveIcon",value:function(){if(!this.props.disabled&&this.props.onRemove)return o().createElement("span",{className:"Select-value-icon","aria-hidden":"true",onMouseDown:this.onRemove,onTouchEnd:this.handleTouchEndRemove,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove},"×")}},{key:"renderLabel",value:function(){var e="Select-value-label";return this.props.onClick||this.props.value.href?o().createElement("a",{className:e,href:this.props.value.href,target:this.props.value.target,onMouseDown:this.handleMouseDown,onTouchEnd:this.handleMouseDown},this.props.children):o().createElement("span",{className:e,role:"option","aria-selected":"true",id:this.props.id},this.props.children)}},{key:"render",value:function(){return o().createElement("div",{className:Ut()("Select-value",this.props.value.disabled?"Select-value-disabled":"",this.props.value.className),style:this.props.value.style,title:this.props.value.title},this.renderRemoveIcon(),this.renderLabel())}}]),t}(o().Component);pn.propTypes={children:Ht().node,disabled:Ht().bool,id:Ht().string,onClick:Ht().func,onRemove:Ht().func,value:Ht().object.isRequired};var dn=function(e){return"string"==typeof e?e:null!==e&&JSON.stringify(e)||""},fn=Ht().oneOfType([Ht().string,Ht().node]),hn=Ht().oneOfType([Ht().string,Ht().number]),An=1,bn=function(e,t){var n=void 0===e?"undefined":tn(e);if("string"!==n&&"number"!==n&&"boolean"!==n)return e;var r=t.options,o=t.valueKey;if(r)for(var i=0;i<r.length;i++)if(String(r[i][o])===String(e))return r[i]},vn=function(e,t){return!e||(t?0===e.length:0===Object.keys(e).length)},gn=function(e){function t(e){nn(this,t);var n=cn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return["clearValue","focusOption","getOptionLabel","handleInputBlur","handleInputChange","handleInputFocus","handleInputValueChange","handleKeyDown","handleMenuScroll","handleMouseDown","handleMouseDownOnArrow","handleMouseDownOnMenu","handleTouchEnd","handleTouchEndClearValue","handleTouchMove","handleTouchOutside","handleTouchStart","handleValueClick","onOptionRef","removeValue","selectValue"].forEach((function(e){return n[e]=n[e].bind(n)})),n.state={inputValue:"",isFocused:!1,isOpen:!1,isPseudoFocused:!1,required:!1},n}return sn(t,e),rn(t,[{key:"componentWillMount",value:function(){this._instancePrefix="react-select-"+(this.props.instanceId||++An)+"-";var e=this.getValueArray(this.props.value);this.props.required&&this.setState({required:vn(e[0],this.props.multi)})}},{key:"componentDidMount",value:function(){void 0!==this.props.autofocus&&"undefined"!=typeof console&&console.warn("Warning: The autofocus prop has changed to autoFocus, support will be removed after react-select@1.0"),(this.props.autoFocus||this.props.autofocus)&&this.focus()}},{key:"componentWillReceiveProps",value:function(e){var t=this.getValueArray(e.value,e);e.required?this.setState({required:vn(t[0],e.multi)}):this.props.required&&this.setState({required:!1}),this.state.inputValue&&this.props.value!==e.value&&e.onSelectResetsInput&&this.setState({inputValue:this.handleInputValueChange("")})}},{key:"componentDidUpdate",value:function(e,t){if(this.menu&&this.focused&&this.state.isOpen&&!this.hasScrolledToOption){var n=(0,Yt.findDOMNode)(this.focused),r=(0,Yt.findDOMNode)(this.menu),o=r.scrollTop,i=o+r.offsetHeight,a=n.offsetTop,s=a+n.offsetHeight;(o>a||i<s)&&(r.scrollTop=n.offsetTop),this.hasScrolledToOption=!0}else this.state.isOpen||(this.hasScrolledToOption=!1);if(this._scrollToFocusedOptionOnUpdate&&this.focused&&this.menu){this._scrollToFocusedOptionOnUpdate=!1;var l=(0,Yt.findDOMNode)(this.focused),c=(0,Yt.findDOMNode)(this.menu),u=l.getBoundingClientRect(),p=c.getBoundingClientRect();u.bottom>p.bottom?c.scrollTop=l.offsetTop+l.clientHeight-c.offsetHeight:u.top<p.top&&(c.scrollTop=l.offsetTop)}if(this.props.scrollMenuIntoView&&this.menuContainer){var d=this.menuContainer.getBoundingClientRect();window.innerHeight<d.bottom+this.props.menuBuffer&&window.scrollBy(0,d.bottom+this.props.menuBuffer-window.innerHeight)}if(e.disabled!==this.props.disabled&&(this.setState({isFocused:!1}),this.closeMenu()),t.isOpen!==this.state.isOpen){this.toggleTouchOutsideEvent(this.state.isOpen);var f=this.state.isOpen?this.props.onOpen:this.props.onClose;f&&f()}}},{key:"componentWillUnmount",value:function(){this.toggleTouchOutsideEvent(!1)}},{key:"toggleTouchOutsideEvent",value:function(e){var t=e?document.addEventListener?"addEventListener":"attachEvent":document.removeEventListener?"removeEventListener":"detachEvent",n=document.addEventListener?"":"on";document[t](n+"touchstart",this.handleTouchOutside),document[t](n+"mousedown",this.handleTouchOutside)}},{key:"handleTouchOutside",value:function(e){this.wrapper&&!this.wrapper.contains(e.target)&&this.closeMenu()}},{key:"focus",value:function(){this.input&&this.input.focus()}},{key:"blurInput",value:function(){this.input&&this.input.blur()}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"handleTouchEnd",value:function(e){this.dragging||this.handleMouseDown(e)}},{key:"handleTouchEndClearValue",value:function(e){this.dragging||this.clearValue(e)}},{key:"handleMouseDown",value:function(e){if(!(this.props.disabled||"mousedown"===e.type&&0!==e.button))if("INPUT"!==e.target.tagName){if(e.preventDefault(),!this.props.searchable)return this.focus(),this.setState({isOpen:!this.state.isOpen,focusedOption:null});if(this.state.isFocused){this.focus();var t=this.input,n=!0;"function"==typeof t.getInput&&(t=t.getInput()),t.value="",this._focusAfterClear&&(n=!1,this._focusAfterClear=!1),this.setState({isOpen:n,isPseudoFocused:!1,focusedOption:null})}else this._openAfterFocus=this.props.openOnClick,this.focus(),this.setState({focusedOption:null})}else this.state.isFocused?this.state.isOpen||this.setState({isOpen:!0,isPseudoFocused:!1,focusedOption:null}):(this._openAfterFocus=this.props.openOnClick,this.focus())}},{key:"handleMouseDownOnArrow",value:function(e){this.props.disabled||"mousedown"===e.type&&0!==e.button||(this.state.isOpen?(e.stopPropagation(),e.preventDefault(),this.closeMenu()):this.setState({isOpen:!0}))}},{key:"handleMouseDownOnMenu",value:function(e){this.props.disabled||"mousedown"===e.type&&0!==e.button||(e.stopPropagation(),e.preventDefault(),this._openAfterFocus=!0,this.focus())}},{key:"closeMenu",value:function(){this.props.onCloseResetsInput?this.setState({inputValue:this.handleInputValueChange(""),isOpen:!1,isPseudoFocused:this.state.isFocused&&!this.props.multi}):this.setState({isOpen:!1,isPseudoFocused:this.state.isFocused&&!this.props.multi}),this.hasScrolledToOption=!1}},{key:"handleInputFocus",value:function(e){if(!this.props.disabled){var t=this.state.isOpen||this._openAfterFocus||this.props.openOnFocus;t=!this._focusAfterClear&&t,this.props.onFocus&&this.props.onFocus(e),this.setState({isFocused:!0,isOpen:!!t}),this._focusAfterClear=!1,this._openAfterFocus=!1}}},{key:"handleInputBlur",value:function(e){if(!this.menu||this.menu!==document.activeElement&&!this.menu.contains(document.activeElement)){this.props.onBlur&&this.props.onBlur(e);var t={isFocused:!1,isOpen:!1,isPseudoFocused:!1};this.props.onBlurResetsInput&&(t.inputValue=this.handleInputValueChange("")),this.setState(t)}else this.focus()}},{key:"handleInputChange",value:function(e){var t=e.target.value;this.state.inputValue!==e.target.value&&(t=this.handleInputValueChange(t)),this.setState({inputValue:t,isOpen:!0,isPseudoFocused:!1})}},{key:"setInputValue",value:function(e){if(this.props.onInputChange){var t=this.props.onInputChange(e);null!=t&&"object"!==(void 0===t?"undefined":tn(t))&&(e=""+t)}this.setState({inputValue:e})}},{key:"handleInputValueChange",value:function(e){if(this.props.onInputChange){var t=this.props.onInputChange(e);null!=t&&"object"!==(void 0===t?"undefined":tn(t))&&(e=""+t)}return e}},{key:"handleKeyDown",value:function(e){if(!(this.props.disabled||"function"==typeof this.props.onInputKeyDown&&(this.props.onInputKeyDown(e),e.defaultPrevented)))switch(e.keyCode){case 8:!this.state.inputValue&&this.props.backspaceRemoves&&(e.preventDefault(),this.popValue());break;case 9:if(e.shiftKey||!this.state.isOpen||!this.props.tabSelectsValue)break;e.preventDefault(),this.selectFocusedOption();break;case 13:e.preventDefault(),e.stopPropagation(),this.state.isOpen?this.selectFocusedOption():this.focusNextOption();break;case 27:e.preventDefault(),this.state.isOpen?(this.closeMenu(),e.stopPropagation()):this.props.clearable&&this.props.escapeClearsValue&&(this.clearValue(e),e.stopPropagation());break;case 32:if(this.props.searchable)break;if(e.preventDefault(),!this.state.isOpen){this.focusNextOption();break}e.stopPropagation(),this.selectFocusedOption();break;case 38:e.preventDefault(),this.focusPreviousOption();break;case 40:e.preventDefault(),this.focusNextOption();break;case 33:e.preventDefault(),this.focusPageUpOption();break;case 34:e.preventDefault(),this.focusPageDownOption();break;case 35:if(e.shiftKey)break;e.preventDefault(),this.focusEndOption();break;case 36:if(e.shiftKey)break;e.preventDefault(),this.focusStartOption();break;case 46:!this.state.inputValue&&this.props.deleteRemoves&&(e.preventDefault(),this.popValue())}}},{key:"handleValueClick",value:function(e,t){this.props.onValueClick&&this.props.onValueClick(e,t)}},{key:"handleMenuScroll",value:function(e){if(this.props.onMenuScrollToBottom){var t=e.target;t.scrollHeight>t.offsetHeight&&t.scrollHeight-t.offsetHeight-t.scrollTop<=0&&this.props.onMenuScrollToBottom()}}},{key:"getOptionLabel",value:function(e){return e[this.props.labelKey]}},{key:"getValueArray",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n="object"===(void 0===t?"undefined":tn(t))?t:this.props;if(n.multi){if("string"==typeof e&&(e=e.split(n.delimiter)),!Array.isArray(e)){if(null==e)return[];e=[e]}return e.map((function(e){return bn(e,n)})).filter((function(e){return e}))}var r=bn(e,n);return r?[r]:[]}},{key:"setValue",value:function(e){var t=this;if(this.props.autoBlur&&this.blurInput(),this.props.required){var n=vn(e,this.props.multi);this.setState({required:n})}this.props.simpleValue&&e&&(e=this.props.multi?e.map((function(e){return e[t.props.valueKey]})).join(this.props.delimiter):e[this.props.valueKey]),this.props.onChange&&this.props.onChange(e)}},{key:"selectValue",value:function(e){var t=this;this.props.closeOnSelect&&(this.hasScrolledToOption=!1);var n=this.props.onSelectResetsInput?"":this.state.inputValue;this.props.multi?this.setState({focusedIndex:null,inputValue:this.handleInputValueChange(n),isOpen:!this.props.closeOnSelect},(function(){t.getValueArray(t.props.value).some((function(n){return n[t.props.valueKey]===e[t.props.valueKey]}))?t.removeValue(e):t.addValue(e)})):this.setState({inputValue:this.handleInputValueChange(n),isOpen:!this.props.closeOnSelect,isPseudoFocused:this.state.isFocused},(function(){t.setValue(e)}))}},{key:"addValue",value:function(e){var t=this.getValueArray(this.props.value),n=this._visibleOptions.filter((function(e){return!e.disabled})),r=n.indexOf(e);this.setValue(t.concat(e)),this.props.closeOnSelect&&(n.length-1===r?this.focusOption(n[r-1]):n.length>r&&this.focusOption(n[r+1]))}},{key:"popValue",value:function(){var e=this.getValueArray(this.props.value);e.length&&!1!==e[e.length-1].clearableValue&&this.setValue(this.props.multi?e.slice(0,e.length-1):null)}},{key:"removeValue",value:function(e){var t=this,n=this.getValueArray(this.props.value);this.setValue(n.filter((function(n){return n[t.props.valueKey]!==e[t.props.valueKey]}))),this.focus()}},{key:"clearValue",value:function(e){e&&"mousedown"===e.type&&0!==e.button||(e.preventDefault(),this.setValue(this.getResetValue()),this.setState({inputValue:this.handleInputValueChange(""),isOpen:!1},this.focus),this._focusAfterClear=!0)}},{key:"getResetValue",value:function(){return void 0!==this.props.resetValue?this.props.resetValue:this.props.multi?[]:null}},{key:"focusOption",value:function(e){this.setState({focusedOption:e})}},{key:"focusNextOption",value:function(){this.focusAdjacentOption("next")}},{key:"focusPreviousOption",value:function(){this.focusAdjacentOption("previous")}},{key:"focusPageUpOption",value:function(){this.focusAdjacentOption("page_up")}},{key:"focusPageDownOption",value:function(){this.focusAdjacentOption("page_down")}},{key:"focusStartOption",value:function(){this.focusAdjacentOption("start")}},{key:"focusEndOption",value:function(){this.focusAdjacentOption("end")}},{key:"focusAdjacentOption",value:function(e){var t=this._visibleOptions.map((function(e,t){return{option:e,index:t}})).filter((function(e){return!e.option.disabled}));if(this._scrollToFocusedOptionOnUpdate=!0,!this.state.isOpen){var n={focusedOption:this._focusedOption||(t.length?t["next"===e?0:t.length-1].option:null),isOpen:!0};return this.props.onSelectResetsInput&&(n.inputValue=""),void this.setState(n)}if(t.length){for(var r=-1,o=0;o<t.length;o++)if(this._focusedOption===t[o].option){r=o;break}if("next"===e&&-1!==r)r=(r+1)%t.length;else if("previous"===e)r>0?r-=1:r=t.length-1;else if("start"===e)r=0;else if("end"===e)r=t.length-1;else if("page_up"===e){var i=r-this.props.pageSize;r=i<0?0:i}else if("page_down"===e){var a=r+this.props.pageSize;r=a>t.length-1?t.length-1:a}-1===r&&(r=0),this.setState({focusedIndex:t[r].index,focusedOption:t[r].option})}}},{key:"getFocusedOption",value:function(){return this._focusedOption}},{key:"selectFocusedOption",value:function(){if(this._focusedOption)return this.selectValue(this._focusedOption)}},{key:"renderLoading",value:function(){if(this.props.isLoading)return o().createElement("span",{className:"Select-loading-zone","aria-hidden":"true"},o().createElement("span",{className:"Select-loading"}))}},{key:"renderValue",value:function(e,t){var n=this,r=this.props.valueRenderer||this.getOptionLabel,i=this.props.valueComponent;if(!e.length){var a=function(e,t,n){var r=e.inputValue,o=e.isPseudoFocused,i=e.isFocused,a=t.onSelectResetsInput;return!r||!a&&!n&&!o&&!i}(this.state,this.props,t);return a?o().createElement("div",{className:"Select-placeholder"},this.props.placeholder):null}var s,l,c,u,p,d,f=this.props.onValueClick?this.handleValueClick:null;return this.props.multi?e.map((function(t,a){return o().createElement(i,{disabled:n.props.disabled||!1===t.clearableValue,id:n._instancePrefix+"-value-"+a,instancePrefix:n._instancePrefix,key:"value-"+a+"-"+t[n.props.valueKey],onClick:f,onRemove:n.removeValue,placeholder:n.props.placeholder,value:t,values:e},r(t,a),o().createElement("span",{className:"Select-aria-only"}," "))})):(s=this.state,l=this.props,c=s.inputValue,u=s.isPseudoFocused,p=s.isFocused,d=l.onSelectResetsInput,c&&(d||!p&&u||p&&!u)?void 0:(t&&(f=null),o().createElement(i,{disabled:this.props.disabled,id:this._instancePrefix+"-value-item",instancePrefix:this._instancePrefix,onClick:f,placeholder:this.props.placeholder,value:e[0]},r(e[0]))))}},{key:"renderInput",value:function(e,t){var n,r=this,i=Ut()("Select-input",this.props.inputProps.className),a=this.state.isOpen,s=Ut()((on(n={},this._instancePrefix+"-list",a),on(n,this._instancePrefix+"-backspace-remove-message",this.props.multi&&!this.props.disabled&&this.state.isFocused&&!this.state.inputValue),n)),l=this.state.inputValue;!l||this.props.onSelectResetsInput||this.state.isFocused||(l="");var c=an({},this.props.inputProps,{"aria-activedescendant":a?this._instancePrefix+"-option-"+t:this._instancePrefix+"-value","aria-describedby":this.props["aria-describedby"],"aria-expanded":""+a,"aria-haspopup":""+a,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-owns":s,onBlur:this.handleInputBlur,onChange:this.handleInputChange,onFocus:this.handleInputFocus,ref:function(e){return r.input=e},role:"combobox",required:this.state.required,tabIndex:this.props.tabIndex,value:l});if(this.props.inputRenderer)return this.props.inputRenderer(c);if(this.props.disabled||!this.props.searchable){var u=ln(this.props.inputProps,[]),p=Ut()(on({},this._instancePrefix+"-list",a));return o().createElement("div",an({},u,{"aria-expanded":a,"aria-owns":p,"aria-activedescendant":a?this._instancePrefix+"-option-"+t:this._instancePrefix+"-value","aria-disabled":""+this.props.disabled,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],className:i,onBlur:this.handleInputBlur,onFocus:this.handleInputFocus,ref:function(e){return r.input=e},role:"combobox",style:{border:0,width:1,display:"inline-block"},tabIndex:this.props.tabIndex||0}))}return this.props.autosize?o().createElement(qt.Z,an({id:this.props.id},c,{className:i,minWidth:"5"})):o().createElement("div",{className:i,key:"input-wrap",style:{display:"inline-block"}},o().createElement("input",an({id:this.props.id},c)))}},{key:"renderClear",value:function(){var e=this.getValueArray(this.props.value);if(this.props.clearable&&e.length&&!this.props.disabled&&!this.props.isLoading){var t=this.props.multi?this.props.clearAllText:this.props.clearValueText,n=this.props.clearRenderer();return o().createElement("span",{"aria-label":t,className:"Select-clear-zone",onMouseDown:this.clearValue,onTouchEnd:this.handleTouchEndClearValue,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,title:t},n)}}},{key:"renderArrow",value:function(){if(this.props.arrowRenderer){var e=this.handleMouseDownOnArrow,t=this.state.isOpen,n=this.props.arrowRenderer({onMouseDown:e,isOpen:t});return n?o().createElement("span",{className:"Select-arrow-zone",onMouseDown:e},n):null}}},{key:"filterOptions",value:function(e){var t=this.state.inputValue,n=this.props.options||[];return this.props.filterOptions?("function"==typeof this.props.filterOptions?this.props.filterOptions:Jt)(n,t,e,{filterOption:this.props.filterOption,ignoreAccents:this.props.ignoreAccents,ignoreCase:this.props.ignoreCase,labelKey:this.props.labelKey,matchPos:this.props.matchPos,matchProp:this.props.matchProp,trimFilter:this.props.trimFilter,valueKey:this.props.valueKey}):n}},{key:"onOptionRef",value:function(e,t){t&&(this.focused=e)}},{key:"renderMenu",value:function(e,t,n){return e&&e.length?this.props.menuRenderer({focusedOption:n,focusOption:this.focusOption,inputValue:this.state.inputValue,instancePrefix:this._instancePrefix,labelKey:this.props.labelKey,onFocus:this.focusOption,onOptionRef:this.onOptionRef,onSelect:this.selectValue,optionClassName:this.props.optionClassName,optionComponent:this.props.optionComponent,optionRenderer:this.props.optionRenderer||this.getOptionLabel,options:e,removeValue:this.removeValue,selectValue:this.selectValue,valueArray:t,valueKey:this.props.valueKey}):this.props.noResultsText?o().createElement("div",{className:"Select-noresults"},this.props.noResultsText):null}},{key:"renderHiddenField",value:function(e){var t=this;if(this.props.name){if(this.props.joinValues){var n=e.map((function(e){return dn(e[t.props.valueKey])})).join(this.props.delimiter);return o().createElement("input",{disabled:this.props.disabled,name:this.props.name,ref:function(e){return t.value=e},type:"hidden",value:n})}return e.map((function(e,n){return o().createElement("input",{disabled:t.props.disabled,key:"hidden."+n,name:t.props.name,ref:"value"+n,type:"hidden",value:dn(e[t.props.valueKey])})}))}}},{key:"getFocusableOptionIndex",value:function(e){var t=this._visibleOptions;if(!t.length)return null;var n=this.props.valueKey,r=this.state.focusedOption||e;if(r&&!r.disabled){var o=-1;if(t.some((function(e,t){var i=e[n]===r[n];return i&&(o=t),i})),-1!==o)return o}for(var i=0;i<t.length;i++)if(!t[i].disabled)return i;return null}},{key:"renderOuter",value:function(e,t,n){var r=this,i=this.renderMenu(e,t,n);return i?o().createElement("div",{ref:function(e){return r.menuContainer=e},className:"Select-menu-outer",style:this.props.menuContainerStyle},o().createElement("div",{className:"Select-menu",id:this._instancePrefix+"-list",onMouseDown:this.handleMouseDownOnMenu,onScroll:this.handleMenuScroll,ref:function(e){return r.menu=e},role:"listbox",style:this.props.menuStyle,tabIndex:-1},i)):null}},{key:"render",value:function(){var e=this,t=this.getValueArray(this.props.value),n=this._visibleOptions=this.filterOptions(this.props.multi&&this.props.removeSelected?t:null),r=this.state.isOpen;this.props.multi&&!n.length&&t.length&&!this.state.inputValue&&(r=!1);var i,a=this.getFocusableOptionIndex(t[0]);i=this._focusedOption=null!==a?n[a]:null;var s=Ut()("Select",this.props.className,{"has-value":t.length,"is-clearable":this.props.clearable,"is-disabled":this.props.disabled,"is-focused":this.state.isFocused,"is-loading":this.props.isLoading,"is-open":r,"is-pseudo-focused":this.state.isPseudoFocused,"is-searchable":this.props.searchable,"Select--multi":this.props.multi,"Select--rtl":this.props.rtl,"Select--single":!this.props.multi}),l=null;return this.props.multi&&!this.props.disabled&&t.length&&!this.state.inputValue&&this.state.isFocused&&this.props.backspaceRemoves&&(l=o().createElement("span",{id:this._instancePrefix+"-backspace-remove-message",className:"Select-aria-only","aria-live":"assertive"},this.props.backspaceToRemoveMessage.replace("{label}",t[t.length-1][this.props.labelKey]))),o().createElement("div",{ref:function(t){return e.wrapper=t},className:s,style:this.props.wrapperStyle},this.renderHiddenField(t),o().createElement("div",{ref:function(t){return e.control=t},className:"Select-control",onKeyDown:this.handleKeyDown,onMouseDown:this.handleMouseDown,onTouchEnd:this.handleTouchEnd,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,style:this.props.style},o().createElement("div",{className:"Select-multi-value-wrapper",id:this._instancePrefix+"-value"},this.renderValue(t,r),this.renderInput(t,a)),l,this.renderLoading(),this.renderClear(),this.renderArrow()),r?this.renderOuter(n,t,i):null)}}]),t}(o().Component);gn.propTypes={"aria-describedby":Ht().string,"aria-label":Ht().string,"aria-labelledby":Ht().string,arrowRenderer:Ht().func,autoBlur:Ht().bool,autoFocus:Ht().bool,autofocus:Ht().bool,autosize:Ht().bool,backspaceRemoves:Ht().bool,backspaceToRemoveMessage:Ht().string,className:Ht().string,clearAllText:fn,clearRenderer:Ht().func,clearValueText:fn,clearable:Ht().bool,closeOnSelect:Ht().bool,deleteRemoves:Ht().bool,delimiter:Ht().string,disabled:Ht().bool,escapeClearsValue:Ht().bool,filterOption:Ht().func,filterOptions:Ht().any,id:Ht().string,ignoreAccents:Ht().bool,ignoreCase:Ht().bool,inputProps:Ht().object,inputRenderer:Ht().func,instanceId:Ht().string,isLoading:Ht().bool,joinValues:Ht().bool,labelKey:Ht().string,matchPos:Ht().string,matchProp:Ht().string,menuBuffer:Ht().number,menuContainerStyle:Ht().object,menuRenderer:Ht().func,menuStyle:Ht().object,multi:Ht().bool,name:Ht().string,noResultsText:fn,onBlur:Ht().func,onBlurResetsInput:Ht().bool,onChange:Ht().func,onClose:Ht().func,onCloseResetsInput:Ht().bool,onFocus:Ht().func,onInputChange:Ht().func,onInputKeyDown:Ht().func,onMenuScrollToBottom:Ht().func,onOpen:Ht().func,onSelectResetsInput:Ht().bool,onValueClick:Ht().func,openOnClick:Ht().bool,openOnFocus:Ht().bool,optionClassName:Ht().string,optionComponent:Ht().func,optionRenderer:Ht().func,options:Ht().array,pageSize:Ht().number,placeholder:fn,removeSelected:Ht().bool,required:Ht().bool,resetValue:Ht().any,rtl:Ht().bool,scrollMenuIntoView:Ht().bool,searchable:Ht().bool,simpleValue:Ht().bool,style:Ht().object,tabIndex:hn,tabSelectsValue:Ht().bool,trimFilter:Ht().bool,value:Ht().any,valueComponent:Ht().func,valueKey:Ht().string,valueRenderer:Ht().func,wrapperStyle:Ht().object},gn.defaultProps={arrowRenderer:Zt,autosize:!0,backspaceRemoves:!0,backspaceToRemoveMessage:"Press backspace to remove {label}",clearable:!0,clearAllText:"Clear all",clearRenderer:function(){return o().createElement("span",{className:"Select-clear",dangerouslySetInnerHTML:{__html:"&times;"}})},clearValueText:"Clear value",closeOnSelect:!0,deleteRemoves:!0,delimiter:",",disabled:!1,escapeClearsValue:!0,filterOptions:Jt,ignoreAccents:!0,ignoreCase:!0,inputProps:{},isLoading:!1,joinValues:!1,labelKey:"label",matchPos:"any",matchProp:"any",menuBuffer:0,menuRenderer:Xt,multi:!1,noResultsText:"No results found",onBlurResetsInput:!0,onCloseResetsInput:!0,onSelectResetsInput:!0,openOnClick:!0,optionComponent:un,pageSize:5,placeholder:"Select...",removeSelected:!0,required:!1,rtl:!1,scrollMenuIntoView:!0,searchable:!0,simpleValue:!1,tabSelectsValue:!0,trimFilter:!0,valueComponent:pn,valueKey:"value"};var yn={autoload:Ht().bool.isRequired,cache:Ht().any,children:Ht().func.isRequired,ignoreAccents:Ht().bool,ignoreCase:Ht().bool,loadOptions:Ht().func.isRequired,loadingPlaceholder:Ht().oneOfType([Ht().string,Ht().node]),multi:Ht().bool,noResultsText:Ht().oneOfType([Ht().string,Ht().node]),onChange:Ht().func,onInputChange:Ht().func,options:Ht().array.isRequired,placeholder:Ht().oneOfType([Ht().string,Ht().node]),searchPromptText:Ht().oneOfType([Ht().string,Ht().node]),value:Ht().any},mn={},wn={autoload:!0,cache:mn,children:function(e){return o().createElement(gn,e)},ignoreAccents:!0,ignoreCase:!0,loadingPlaceholder:"Loading...",options:[],searchPromptText:"Type to search"},En=function(e){function t(e,n){nn(this,t);var r=cn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r._cache=e.cache===mn?{}:e.cache,r.state={inputValue:"",isLoading:!1,options:e.options},r.onInputChange=r.onInputChange.bind(r),r}return sn(t,e),rn(t,[{key:"componentDidMount",value:function(){this.props.autoload&&this.loadOptions("")}},{key:"componentWillReceiveProps",value:function(e){e.options!==this.props.options&&this.setState({options:e.options})}},{key:"componentWillUnmount",value:function(){this._callback=null}},{key:"loadOptions",value:function(e){var t=this,n=this.props.loadOptions,r=this._cache;if(r&&Object.prototype.hasOwnProperty.call(r,e))return this._callback=null,void this.setState({isLoading:!1,options:r[e]});var o=function n(o,i){var a=i&&i.options||[];r&&(r[e]=a),n===t._callback&&(t._callback=null,t.setState({isLoading:!1,options:a}))};this._callback=o;var i=n(e,o);i&&i.then((function(e){return o(0,e)}),(function(e){return o()})),this._callback&&!this.state.isLoading&&this.setState({isLoading:!0})}},{key:"onInputChange",value:function(e){var t=this.props,n=t.ignoreAccents,r=t.ignoreCase,o=t.onInputChange,i=e;if(o){var a=o(i);null!=a&&"object"!==(void 0===a?"undefined":tn(a))&&(i=""+a)}var s=i;return n&&(s=$t(s)),r&&(s=s.toLowerCase()),this.setState({inputValue:i}),this.loadOptions(s),i}},{key:"noResultsText",value:function(){var e=this.props,t=e.loadingPlaceholder,n=e.noResultsText,r=e.searchPromptText,o=this.state,i=o.inputValue;return o.isLoading?t:i&&n?n:r}},{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.children,r=t.loadingPlaceholder,o=t.placeholder,i=this.state,a=i.isLoading,s=i.options,l={noResultsText:this.noResultsText(),placeholder:a?r:o,options:a&&r?[]:s,ref:function(t){return e.select=t}};return n(an({},this.props,l,{isLoading:a,onInputChange:this.onInputChange}))}}]),t}(r.Component);En.propTypes=yn,En.defaultProps=wn;var Cn=function(e){function t(e,n){nn(this,t);var r=cn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r.filterOptions=r.filterOptions.bind(r),r.menuRenderer=r.menuRenderer.bind(r),r.onInputKeyDown=r.onInputKeyDown.bind(r),r.onInputChange=r.onInputChange.bind(r),r.onOptionSelect=r.onOptionSelect.bind(r),r}return sn(t,e),rn(t,[{key:"createNewOption",value:function(){var e=this.props,t=e.isValidNewOption,n=e.newOptionCreator,r=e.onNewOptionClick,o=e.options,i=void 0===o?[]:o;if(t({label:this.inputValue})){var a=n({label:this.inputValue,labelKey:this.labelKey,valueKey:this.valueKey});this.isOptionUnique({option:a,options:i})&&(r?r(a):(i.unshift(a),this.select.selectValue(a)))}}},{key:"filterOptions",value:function(){var e=this.props,t=e.filterOptions,n=e.isValidNewOption,r=e.promptTextCreator,o=e.showNewOptionAtTop,i=(arguments.length<=2?void 0:arguments[2])||[],a=t.apply(void 0,arguments)||[];if(n({label:this.inputValue})){var s=this.props.newOptionCreator,l=s({label:this.inputValue,labelKey:this.labelKey,valueKey:this.valueKey}),c=this.isOptionUnique({option:l,options:i.concat(a)});if(c){var u=r(this.inputValue);this._createPlaceholderOption=s({label:u,labelKey:this.labelKey,valueKey:this.valueKey}),o?a.unshift(this._createPlaceholderOption):a.push(this._createPlaceholderOption)}}return a}},{key:"isOptionUnique",value:function(e){var t=e.option,n=e.options,r=this.props.isOptionUnique;return n=n||this.props.options,r({labelKey:this.labelKey,option:t,options:n,valueKey:this.valueKey})}},{key:"menuRenderer",value:function(e){return(0,this.props.menuRenderer)(an({},e,{onSelect:this.onOptionSelect,selectValue:this.onOptionSelect}))}},{key:"onInputChange",value:function(e){var t=this.props.onInputChange;return this.inputValue=e,t&&(this.inputValue=t(e)),this.inputValue}},{key:"onInputKeyDown",value:function(e){var t=this.props,n=t.shouldKeyDownEventCreateNewOption,r=t.onInputKeyDown,o=this.select.getFocusedOption();o&&o===this._createPlaceholderOption&&n(e)?(this.createNewOption(),e.preventDefault()):r&&r(e)}},{key:"onOptionSelect",value:function(e){e===this._createPlaceholderOption?this.createNewOption():this.select.selectValue(e)}},{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.ref,r=ln(t,["ref"]),o=this.props.children;return o||(o=kn),o(an({},r,{allowCreate:!0,filterOptions:this.filterOptions,menuRenderer:this.menuRenderer,onInputChange:this.onInputChange,onInputKeyDown:this.onInputKeyDown,ref:function(t){e.select=t,t&&(e.labelKey=t.props.labelKey,e.valueKey=t.props.valueKey),n&&n(t)}}))}}]),t}(o().Component),kn=function(e){return o().createElement(gn,e)},xn=function(e){var t=e.option,n=e.options,r=e.labelKey,o=e.valueKey;return!n||!n.length||0===n.filter((function(e){return e[r]===t[r]||e[o]===t[o]})).length},On=function(e){return!!e.label},Sn=function(e){var t=e.label,n=e.labelKey,r={};return r[e.valueKey]=t,r[n]=t,r.className="Select-create-option-placeholder",r},Bn=function(e){return'Create option "'+e+'"'},_n=function(e){switch(e.keyCode){case 9:case 13:case 188:return!0;default:return!1}};Cn.isOptionUnique=xn,Cn.isValidNewOption=On,Cn.newOptionCreator=Sn,Cn.promptTextCreator=Bn,Cn.shouldKeyDownEventCreateNewOption=_n,Cn.defaultProps={filterOptions:Jt,isOptionUnique:xn,isValidNewOption:On,menuRenderer:Xt,newOptionCreator:Sn,promptTextCreator:Bn,shouldKeyDownEventCreateNewOption:_n,showNewOptionAtTop:!0},Cn.propTypes={children:Ht().func,filterOptions:Ht().any,isOptionUnique:Ht().func,isValidNewOption:Ht().func,menuRenderer:Ht().any,newOptionCreator:Ht().func,onInputChange:Ht().func,onInputKeyDown:Ht().func,onNewOptionClick:Ht().func,options:Ht().array,promptTextCreator:Ht().func,ref:Ht().func,shouldKeyDownEventCreateNewOption:Ht().func,showNewOptionAtTop:Ht().bool};var jn=function(e){function t(){return nn(this,t),cn(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return sn(t,e),rn(t,[{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this;return o().createElement(En,this.props,(function(t){var n=t.ref,r=ln(t,["ref"]),i=n;return o().createElement(Cn,r,(function(t){var n=t.ref,r=ln(t,["ref"]),o=n;return e.props.children(an({},r,{ref:function(t){o(t),i(t),e.select=t}}))}))}))}}]),t}(o().Component);jn.propTypes={children:Ht().func.isRequired},jn.defaultProps={children:function(e){return o().createElement(gn,e)}},gn.Async=En,gn.AsyncCreatable=jn,gn.Creatable=Cn,gn.Value=pn,gn.Option=un;var Pn=gn;function Dn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var In=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"getFirstParentOfType",value:function(e,t){t=t.toUpperCase();for(var n=e;n;){if(n.tagName.toUpperCase()===t)return n;if(null===n.parentElement)return;n=n.parentElement}}},{key:"getParentById",value:function(e,t){for(var n=e;n;){if(n.id===t)return n;if(null===n.parentElement)return;n=n.parentElement}}}],null&&Dn(t.prototype,null),n&&Dn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Fn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Mn(e,t){return Mn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Mn(e,t)}function Rn(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return zn(e)}function zn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Nn(e){return Nn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Nn(e)}function Ln(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var qn,Wn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mn(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Nn(r);if(i){var n=Nn(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Rn(this,e)});function s(){var e;Fn(this,s);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Ln(zn(e=a.call.apply(a,[this].concat(n))),"handleOpenDropdown",(function(){var t=e.refs.dropdown;We(t.wrapper.querySelector(".Select-menu-outer"))})),e}return t=s,(n=[{key:"render",value:function(){var e=this.props,t=e.clearable,n=e.dropdown,r=e.onChange,i=e.value,a=e.disabled;return o().createElement("div",{className:"dash-dropdown-cell-value-container dash-cell-value-container",onClick:this.handleClick},o().createElement("div",{className:"dropdown-cell-value-shadow cell-value-shadow"},(n&&n.find((function(e){return e.value===i}))||{label:void 0}).label),o().createElement(Pn,{ref:"dropdown",clearable:t,onChange:function(e){r(e?e.value:e)},scrollMenuIntoView:!1,onOpen:this.handleOpenDropdown,options:n,placeholder:"",value:i,disabled:a}))}},{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"handleClick",value:function(e){e.stopPropagation()}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.refs.dropdown;if(n&&r&&document.activeElement!==r){var o=In.getFirstParentOfType(r.wrapper,"td");o&&-1===o.className.indexOf("phantom-cell")&&o.focus()}}}}])&&Tn(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent),Un=function(e){return e},Vn=function(e){var t;return e.type===_.QD.Numeric&&(t=function(e){if(!e)return function(e){return e};var t=function(e){var t,n,r=void 0===e.grouping||void 0===e.thousands?ue:(t=pe.call(e.grouping,Number),n=e.thousands+"",function(e,r){for(var o=e.length,i=[],a=0,s=t[0],l=0;o>0&&s>0&&(l+s+1>r&&(s=Math.max(1,r-l)),i.push(e.substring(o-=s,o+s)),!((l+=s+1)>r));)s=t[a=(a+1)%t.length];return i.reverse().join(n)}),o=void 0===e.currency?"":e.currency[0]+"",i=void 0===e.currency?"":e.currency[1]+"",a=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?ue:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(pe.call(e.numerals,String)),l=void 0===e.percent?"%":e.percent+"",c=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ae(e)).fill,n=e.align,p=e.sign,d=e.symbol,f=e.zero,h=e.width,A=e.comma,b=e.precision,v=e.trim,g=e.type;"n"===g?(A=!0,g="g"):ce[g]||(void 0===b&&(b=12),v=!0,g="g"),(f||"0"===t&&"="===n)&&(f=!0,t="0",n="=");var y="$"===d?o:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",m="$"===d?i:/[%p]/.test(g)?l:"",w=ce[g],E=/[defgprs%]/.test(g);function C(e){var o,i,l,d=y,C=m;if("c"===g)C=w(e)+C,e="";else{var k=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),b),v&&(e=function(e){e:for(var t,n=e.length,r=1,o=-1;r<n;++r)switch(e[r]){case".":o=t=r;break;case"0":0===o&&(o=r),t=r;break;default:if(!+e[r])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),k&&0==+e&&"+"!==p&&(k=!1),d=(k?"("===p?p:c:"-"===p||"("===p?"":p)+d,C=("s"===g?de[8+oe/3]:"")+C+(k&&"("===p?")":""),E)for(o=-1,i=e.length;++o<i;)if(48>(l=e.charCodeAt(o))||l>57){C=(46===l?a+e.slice(o+1):e.slice(o))+C,e=e.slice(0,o);break}}A&&!f&&(e=r(e,1/0));var x=d.length+e.length+C.length,O=x<h?new Array(h-x+1).join(t):"";switch(A&&f&&(e=r(O+e,O.length?h-C.length:1/0),O=""),n){case"<":e=d+e+C+O;break;case"=":e=d+O+e+C;break;case"^":e=O.slice(0,x=O.length>>1)+d+e+C+O.slice(x);break;default:e=O+d+e+C}return s(e)}return b=void 0===b?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,b)):Math.max(0,Math.min(20,b)),C.toString=function(){return e+""},C}return{format:p,formatPrefix:function(e,t){var n,r=p(((e=ae(e)).type="f",e)),o=3*Math.max(-8,Math.min(8,Math.floor((n=t,((n=re(Math.abs(n)))?n[1]:NaN)/3)))),i=Math.pow(10,-o),a=de[8+o/3];return function(e){return r(i*e)+a}}}}(me(e.locale)),n=e.prefix?t.formatPrefix(e.specifier,e.prefix):t.format(e.specifier),r=e.locale.separate_4digits?e.specifier:e.specifier.replace(/,/,""),o=e.prefix?t.formatPrefix(r,e.prefix):t.format(r);return function(t){return"number"!=typeof(t=be(t)?e.nully:t)?t:Math.abs(t)<1e4?o(t):n(t)}}(e.format)),t||Un};function Hn(e){return(qn=qn||document.createElement("textarea")).innerHTML="&"+e+";",qn.value}var Yn=Object.prototype.hasOwnProperty;function Kn(e){var t=[].slice.call(arguments,1);return t.forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(n){e[n]=t[n]}))}})),e}var Zn=/\\([\\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function Qn(e){return e.indexOf("\\")<0?e:e.replace(Zn,"$1")}function $n(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||65535==(65535&e)||65534==(65535&e)||e>=0&&e<=8||11===e||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function Gn(e){if(e>65535){var t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}var Jn=/&([a-z#][a-z0-9]{1,31});/gi,Xn=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;function er(e,t){var n=0,r=Hn(t);return t!==r?r:35===t.charCodeAt(0)&&Xn.test(t)&&$n(n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10))?Gn(n):e}function tr(e){return e.indexOf("&")<0?e:e.replace(Jn,er)}var nr=/[&<>"]/,rr=/[&<>"]/g,or={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function ir(e){return or[e]}function ar(e){return nr.test(e)?e.replace(rr,ir):e}var sr={};function lr(e,t){return++t>=e.length-2?t:"paragraph_open"===e[t].type&&e[t].tight&&"inline"===e[t+1].type&&0===e[t+1].content.length&&"paragraph_close"===e[t+2].type&&e[t+2].tight?lr(e,t+2):t}sr.blockquote_open=function(){return"<blockquote>\n"},sr.blockquote_close=function(e,t){return"</blockquote>"+cr(e,t)},sr.code=function(e,t){return e[t].block?"<pre><code>"+ar(e[t].content)+"</code></pre>"+cr(e,t):"<code>"+ar(e[t].content)+"</code>"},sr.fence=function(e,t,n,r,o){var i,a,s,l,c=e[t],u="",p=n.langPrefix;if(c.params){if(a=(i=c.params.split(/\s+/g)).join(" "),s=o.rules.fence_custom,l=i[0],s&&Yn.call(s,l))return o.rules.fence_custom[i[0]](e,t,n,r,o);u=' class="'+p+ar(tr(Qn(a)))+'"'}return"<pre><code"+u+">"+(n.highlight&&n.highlight.apply(n.highlight,[c.content].concat(i))||ar(c.content))+"</code></pre>"+cr(e,t)},sr.fence_custom={},sr.heading_open=function(e,t){return"<h"+e[t].hLevel+">"},sr.heading_close=function(e,t){return"</h"+e[t].hLevel+">\n"},sr.hr=function(e,t,n){return(n.xhtmlOut?"<hr />":"<hr>")+cr(e,t)},sr.bullet_list_open=function(){return"<ul>\n"},sr.bullet_list_close=function(e,t){return"</ul>"+cr(e,t)},sr.list_item_open=function(){return"<li>"},sr.list_item_close=function(){return"</li>\n"},sr.ordered_list_open=function(e,t){var n=e[t];return"<ol"+(n.order>1?' start="'+n.order+'"':"")+">\n"},sr.ordered_list_close=function(e,t){return"</ol>"+cr(e,t)},sr.paragraph_open=function(e,t){return e[t].tight?"":"<p>"},sr.paragraph_close=function(e,t){var n=!(e[t].tight&&t&&"inline"===e[t-1].type&&!e[t-1].content);return(e[t].tight?"":"</p>")+(n?cr(e,t):"")},sr.link_open=function(e,t,n){var r=e[t].title?' title="'+ar(tr(e[t].title))+'"':"",o=n.linkTarget?' target="'+n.linkTarget+'"':"";return'<a href="'+ar(e[t].href)+'"'+r+o+">"},sr.link_close=function(){return"</a>"},sr.image=function(e,t,n){var r=' src="'+ar(e[t].src)+'"',o=e[t].title?' title="'+ar(tr(e[t].title))+'"':"";return"<img"+r+' alt="'+(e[t].alt?ar(tr(Qn(e[t].alt))):"")+'"'+o+(n.xhtmlOut?" /":"")+">"},sr.table_open=function(){return"<table>\n"},sr.table_close=function(){return"</table>\n"},sr.thead_open=function(){return"<thead>\n"},sr.thead_close=function(){return"</thead>\n"},sr.tbody_open=function(){return"<tbody>\n"},sr.tbody_close=function(){return"</tbody>\n"},sr.tr_open=function(){return"<tr>"},sr.tr_close=function(){return"</tr>\n"},sr.th_open=function(e,t){var n=e[t];return"<th"+(n.align?' style="text-align:'+n.align+'"':"")+">"},sr.th_close=function(){return"</th>"},sr.td_open=function(e,t){var n=e[t];return"<td"+(n.align?' style="text-align:'+n.align+'"':"")+">"},sr.td_close=function(){return"</td>"},sr.strong_open=function(){return"<strong>"},sr.strong_close=function(){return"</strong>"},sr.em_open=function(){return"<em>"},sr.em_close=function(){return"</em>"},sr.del_open=function(){return"<del>"},sr.del_close=function(){return"</del>"},sr.ins_open=function(){return"<ins>"},sr.ins_close=function(){return"</ins>"},sr.mark_open=function(){return"<mark>"},sr.mark_close=function(){return"</mark>"},sr.sub=function(e,t){return"<sub>"+ar(e[t].content)+"</sub>"},sr.sup=function(e,t){return"<sup>"+ar(e[t].content)+"</sup>"},sr.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},sr.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},sr.text=function(e,t){return ar(e[t].content)},sr.htmlblock=function(e,t){return e[t].content},sr.htmltag=function(e,t){return e[t].content},sr.abbr_open=function(e,t){return'<abbr title="'+ar(tr(e[t].title))+'">'},sr.abbr_close=function(){return"</abbr>"},sr.footnote_ref=function(e,t){var n=Number(e[t].id+1).toString(),r="fnref"+n;return e[t].subId>0&&(r+=":"+e[t].subId),'<sup class="footnote-ref"><a href="#fn'+n+'" id="'+r+'">['+n+"]</a></sup>"},sr.footnote_block_open=function(e,t,n){return(n.xhtmlOut?'<hr class="footnotes-sep" />\n':'<hr class="footnotes-sep">\n')+'<section class="footnotes">\n<ol class="footnotes-list">\n'},sr.footnote_block_close=function(){return"</ol>\n</section>\n"},sr.footnote_open=function(e,t){return'<li id="fn'+Number(e[t].id+1).toString()+'"  class="footnote-item">'},sr.footnote_close=function(){return"</li>\n"},sr.footnote_anchor=function(e,t){var n="fnref"+Number(e[t].id+1).toString();return e[t].subId>0&&(n+=":"+e[t].subId),' <a href="#'+n+'" class="footnote-backref">↩</a>'},sr.dl_open=function(){return"<dl>\n"},sr.dt_open=function(){return"<dt>"},sr.dd_open=function(){return"<dd>"},sr.dl_close=function(){return"</dl>\n"},sr.dt_close=function(){return"</dt>\n"},sr.dd_close=function(){return"</dd>\n"};var cr=sr.getBreak=function(e,t){return(t=lr(e,t))<e.length&&"list_item_close"===e[t].type?"":"\n"};function ur(){this.rules=Kn({},sr),this.getBreak=sr.getBreak}function pr(){this.__rules__=[],this.__cache__=null}function dr(e,t,n,r,o){this.src=e,this.env=r,this.options=n,this.parser=t,this.tokens=o,this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache=[],this.isInLabel=!1,this.linkLevel=0,this.linkContent="",this.labelUnmatchedScopes=0}function fr(e,t){var n,r,o,i=-1,a=e.posMax,s=e.pos,l=e.isInLabel;if(e.isInLabel)return-1;if(e.labelUnmatchedScopes)return e.labelUnmatchedScopes--,-1;for(e.pos=t+1,e.isInLabel=!0,n=1;e.pos<a;){if(91===(o=e.src.charCodeAt(e.pos)))n++;else if(93===o&&0==--n){r=!0;break}e.parser.skipToken(e)}return r?(i=e.pos,e.labelUnmatchedScopes=0):e.labelUnmatchedScopes=n-1,e.pos=s,e.isInLabel=l,i}function hr(e,t,n,r){var o,i,a,s,l,c;if(42!==e.charCodeAt(0))return-1;if(91!==e.charCodeAt(1))return-1;if(-1===e.indexOf("]:"))return-1;if((i=fr(o=new dr(e,t,n,r,[]),1))<0||58!==e.charCodeAt(i+1))return-1;for(s=o.posMax,a=i+2;a<s&&10!==o.src.charCodeAt(a);a++);return l=e.slice(2,i),0===(c=e.slice(i+2,a).trim()).length?-1:(r.abbreviations||(r.abbreviations={}),void 0===r.abbreviations[":"+l]&&(r.abbreviations[":"+l]=c),a)}function Ar(e){var t=tr(e);try{t=decodeURI(t)}catch(e){}return encodeURI(t)}function br(e,t){var n,r,o,i=t,a=e.posMax;if(60===e.src.charCodeAt(t)){for(t++;t<a;){if(10===(n=e.src.charCodeAt(t)))return!1;if(62===n)return o=Ar(Qn(e.src.slice(i+1,t))),!!e.parser.validateLink(o)&&(e.pos=t+1,e.linkContent=o,!0);92===n&&t+1<a?t+=2:t++}return!1}for(r=0;t<a&&32!==(n=e.src.charCodeAt(t))&&!(n<32||127===n);)if(92===n&&t+1<a)t+=2;else{if(40===n&&++r>1)break;if(41===n&&--r<0)break;t++}return i!==t&&(o=Qn(e.src.slice(i,t)),!!e.parser.validateLink(o)&&(e.linkContent=o,e.pos=t,!0))}function vr(e,t){var n,r=t,o=e.posMax,i=e.src.charCodeAt(t);if(34!==i&&39!==i&&40!==i)return!1;for(t++,40===i&&(i=41);t<o;){if((n=e.src.charCodeAt(t))===i)return e.pos=t+1,e.linkContent=Qn(e.src.slice(r+1,t)),!0;92===n&&t+1<o?t+=2:t++}return!1}function gr(e){return e.trim().replace(/\s+/g," ").toUpperCase()}function yr(e,t,n,r){var o,i,a,s,l,c,u,p,d;if(91!==e.charCodeAt(0))return-1;if(-1===e.indexOf("]:"))return-1;if((i=fr(o=new dr(e,t,n,r,[]),0))<0||58!==e.charCodeAt(i+1))return-1;for(s=o.posMax,a=i+2;a<s&&(32===(l=o.src.charCodeAt(a))||10===l);a++);if(!br(o,a))return-1;for(u=o.linkContent,c=a=o.pos,a+=1;a<s&&(32===(l=o.src.charCodeAt(a))||10===l);a++);for(a<s&&c!==a&&vr(o,a)?(p=o.linkContent,a=o.pos):(p="",a=c);a<s&&32===o.src.charCodeAt(a);)a++;return a<s&&10!==o.src.charCodeAt(a)?-1:(d=gr(e.slice(1,i)),void 0===r.references[d]&&(r.references[d]={title:p,href:u}),a)}ur.prototype.renderInline=function(e,t,n){for(var r=this.rules,o=e.length,i=0,a="";o--;)a+=r[e[i].type](e,i++,t,n,this);return a},ur.prototype.render=function(e,t,n){for(var r=this.rules,o=e.length,i=-1,a="";++i<o;)"inline"===e[i].type?a+=this.renderInline(e[i].children,t,n):a+=r[e[i].type](e,i,t,n,this);return a},pr.prototype.__find__=function(e){for(var t=this.__rules__.length,n=-1;t--;)if(this.__rules__[++n].name===e)return n;return-1},pr.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))}))}))},pr.prototype.at=function(e,t,n){var r=this.__find__(e),o=n||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__[r].fn=t,this.__rules__[r].alt=o.alt||[],this.__cache__=null},pr.prototype.before=function(e,t,n,r){var o=this.__find__(e),i=r||{};if(-1===o)throw new Error("Parser rule not found: "+e);this.__rules__.splice(o,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},pr.prototype.after=function(e,t,n,r){var o=this.__find__(e),i=r||{};if(-1===o)throw new Error("Parser rule not found: "+e);this.__rules__.splice(o+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},pr.prototype.push=function(e,t,n){var r=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null},pr.prototype.enable=function(e,t){e=Array.isArray(e)?e:[e],t&&this.__rules__.forEach((function(e){e.enabled=!1})),e.forEach((function(e){var t=this.__find__(e);if(t<0)throw new Error("Rules manager: invalid rule name "+e);this.__rules__[t].enabled=!0}),this),this.__cache__=null},pr.prototype.disable=function(e){(e=Array.isArray(e)?e:[e]).forEach((function(e){var t=this.__find__(e);if(t<0)throw new Error("Rules manager: invalid rule name "+e);this.__rules__[t].enabled=!1}),this),this.__cache__=null},pr.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},dr.prototype.pushPending=function(){this.tokens.push({type:"text",content:this.pending,level:this.pendingLevel}),this.pending=""},dr.prototype.push=function(e){this.pending&&this.pushPending(),this.tokens.push(e),this.pendingLevel=this.level},dr.prototype.cacheSet=function(e,t){for(var n=this.cache.length;n<=e;n++)this.cache.push(0);this.cache[e]=t},dr.prototype.cacheGet=function(e){return e<this.cache.length?this.cache[e]:0};var mr=" \n()[]'\".,!?-";function wr(e){return e.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1")}var Er=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,Cr=/\((c|tm|r|p)\)/gi,kr={c:"©",r:"®",p:"§",tm:"™"},xr=/['"]/,Or=/['"]/g,Sr=/[-\s()\[\]]/;function Br(e,t){return!(t<0||t>=e.length||Sr.test(e[t]))}function _r(e,t,n){return e.substr(0,t)+n+e.substr(t+1)}var jr=[["block",function(e){e.inlineMode?e.tokens.push({type:"inline",content:e.src.replace(/\n/g," ").trim(),level:0,lines:[0,1],children:[]}):e.block.parse(e.src,e.options,e.env,e.tokens)}],["abbr",function(e){var t,n,r,o,i=e.tokens;if(!e.inlineMode)for(t=1,n=i.length-1;t<n;t++)if("paragraph_open"===i[t-1].type&&"inline"===i[t].type&&"paragraph_close"===i[t+1].type){for(r=i[t].content;r.length&&!((o=hr(r,e.inline,e.options,e.env))<0);)r=r.slice(o).trim();i[t].content=r,r.length||(i[t-1].tight=!0,i[t+1].tight=!0)}}],["references",function(e){var t,n,r,o,i=e.tokens;if(e.env.references=e.env.references||{},!e.inlineMode)for(t=1,n=i.length-1;t<n;t++)if("inline"===i[t].type&&"paragraph_open"===i[t-1].type&&"paragraph_close"===i[t+1].type){for(r=i[t].content;r.length&&!((o=yr(r,e.inline,e.options,e.env))<0);)r=r.slice(o).trim();i[t].content=r,r.length||(i[t-1].tight=!0,i[t+1].tight=!0)}}],["inline",function(e){var t,n,r,o=e.tokens;for(n=0,r=o.length;n<r;n++)"inline"===(t=o[n]).type&&e.inline.parse(t.content,e.options,e.env,t.children)}],["footnote_tail",function(e){var t,n,r,o,i,a,s,l,c,u=0,p=!1,d={};if(e.env.footnotes&&(e.tokens=e.tokens.filter((function(e){return"footnote_reference_open"===e.type?(p=!0,l=[],c=e.label,!1):"footnote_reference_close"===e.type?(p=!1,d[":"+c]=l,!1):(p&&l.push(e),!p)})),e.env.footnotes.list)){for(a=e.env.footnotes.list,e.tokens.push({type:"footnote_block_open",level:u++}),t=0,n=a.length;t<n;t++){for(e.tokens.push({type:"footnote_open",id:t,level:u++}),a[t].tokens?((s=[]).push({type:"paragraph_open",tight:!1,level:u++}),s.push({type:"inline",content:"",level:u,children:a[t].tokens}),s.push({type:"paragraph_close",tight:!1,level:--u})):a[t].label&&(s=d[":"+a[t].label]),e.tokens=e.tokens.concat(s),i="paragraph_close"===e.tokens[e.tokens.length-1].type?e.tokens.pop():null,o=a[t].count>0?a[t].count:1,r=0;r<o;r++)e.tokens.push({type:"footnote_anchor",id:t,subId:r,level:u});i&&e.tokens.push(i),e.tokens.push({type:"footnote_close",level:--u})}e.tokens.push({type:"footnote_block_close",level:--u})}}],["abbr2",function(e){var t,n,r,o,i,a,s,l,c,u,p,d,f=e.tokens;if(e.env.abbreviations)for(e.env.abbrRegExp||(d="(^|["+mr.split("").map(wr).join("")+"])("+Object.keys(e.env.abbreviations).map((function(e){return e.substr(1)})).sort((function(e,t){return t.length-e.length})).map(wr).join("|")+")($|["+mr.split("").map(wr).join("")+"])",e.env.abbrRegExp=new RegExp(d,"g")),u=e.env.abbrRegExp,n=0,r=f.length;n<r;n++)if("inline"===f[n].type)for(t=(o=f[n].children).length-1;t>=0;t--)if("text"===(i=o[t]).type){for(l=0,a=i.content,u.lastIndex=0,c=i.level,s=[];p=u.exec(a);)u.lastIndex>l&&s.push({type:"text",content:a.slice(l,p.index+p[1].length),level:c}),s.push({type:"abbr_open",title:e.env.abbreviations[":"+p[2]],level:c++}),s.push({type:"text",content:p[2],level:c}),s.push({type:"abbr_close",level:--c}),l=u.lastIndex-p[3].length;s.length&&(l<a.length&&s.push({type:"text",content:a.slice(l),level:c}),f[n].children=o=[].concat(o.slice(0,t),s,o.slice(t+1)))}}],["replacements",function(e){var t,n,r,o,i,a;if(e.options.typographer)for(i=e.tokens.length-1;i>=0;i--)if("inline"===e.tokens[i].type)for(t=(o=e.tokens[i].children).length-1;t>=0;t--)"text"===(n=o[t]).type&&(r=(a=r=n.content).indexOf("(")<0?a:a.replace(Cr,(function(e,t){return kr[t.toLowerCase()]})),Er.test(r)&&(r=r.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---([^-]|$)/gm,"$1—$2").replace(/(^|\s)--(\s|$)/gm,"$1–$2").replace(/(^|[^-\s])--([^-\s]|$)/gm,"$1–$2")),n.content=r)}],["smartquotes",function(e){var t,n,r,o,i,a,s,l,c,u,p,d,f,h,A,b,v;if(e.options.typographer)for(v=[],A=e.tokens.length-1;A>=0;A--)if("inline"===e.tokens[A].type)for(b=e.tokens[A].children,v.length=0,t=0;t<b.length;t++)if("text"===(n=b[t]).type&&!xr.test(n.text)){for(s=b[t].level,f=v.length-1;f>=0&&!(v[f].level<=s);f--);v.length=f+1,i=0,a=(r=n.content).length;e:for(;i<a&&(Or.lastIndex=i,o=Or.exec(r));)if(l=!Br(r,o.index-1),i=o.index+1,h="'"===o[0],(c=!Br(r,i))||l){if(p=!c,d=!l)for(f=v.length-1;f>=0&&(u=v[f],!(v[f].level<s));f--)if(u.single===h&&v[f].level===s){u=v[f],h?(b[u.token].content=_r(b[u.token].content,u.pos,e.options.quotes[2]),n.content=_r(n.content,o.index,e.options.quotes[3])):(b[u.token].content=_r(b[u.token].content,u.pos,e.options.quotes[0]),n.content=_r(n.content,o.index,e.options.quotes[1])),v.length=f;continue e}p?v.push({token:t,pos:o.index,single:h,level:s}):d&&h&&(n.content=_r(n.content,o.index,"’"))}else h&&(n.content=_r(n.content,o.index,"’"))}}]];function Pr(){this.options={},this.ruler=new pr;for(var e=0;e<jr.length;e++)this.ruler.push(jr[e][0],jr[e][1])}function Dr(e,t,n,r,o){var i,a,s,l,c,u,p;for(this.src=e,this.parser=t,this.options=n,this.env=r,this.tokens=o,this.bMarks=[],this.eMarks=[],this.tShift=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.parentType="root",this.ddIndent=-1,this.level=0,this.result="",u=0,p=!1,s=l=u=0,c=(a=this.src).length;l<c;l++){if(i=a.charCodeAt(l),!p){if(32===i){u++;continue}p=!0}10!==i&&l!==c-1||(10!==i&&l++,this.bMarks.push(s),this.eMarks.push(l),this.tShift.push(u),p=!1,u=0,s=l+1)}this.bMarks.push(a.length),this.eMarks.push(a.length),this.tShift.push(0),this.lineMax=this.bMarks.length-1}function Ir(e,t){var n,r,o;return(r=e.bMarks[t]+e.tShift[t])>=(o=e.eMarks[t])||42!==(n=e.src.charCodeAt(r++))&&45!==n&&43!==n||r<o&&32!==e.src.charCodeAt(r)?-1:r}function Fr(e,t){var n,r=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];if(r+1>=o)return-1;if((n=e.src.charCodeAt(r++))<48||n>57)return-1;for(;;){if(r>=o)return-1;if(!((n=e.src.charCodeAt(r++))>=48&&n<=57)){if(41===n||46===n)break;return-1}}return r<o&&32!==e.src.charCodeAt(r)?-1:r}Pr.prototype.process=function(e){var t,n,r;for(t=0,n=(r=this.ruler.getRules("")).length;t<n;t++)r[t](e)},Dr.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},Dr.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},Dr.prototype.skipSpaces=function(e){for(var t=this.src.length;e<t&&32===this.src.charCodeAt(e);e++);return e},Dr.prototype.skipChars=function(e,t){for(var n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},Dr.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},Dr.prototype.getLines=function(e,t,n,r){var o,i,a,s,l,c=e;if(e>=t)return"";if(c+1===t)return i=this.bMarks[c]+Math.min(this.tShift[c],n),a=r?this.eMarks[c]+1:this.eMarks[c],this.src.slice(i,a);for(s=new Array(t-e),o=0;c<t;c++,o++)(l=this.tShift[c])>n&&(l=n),l<0&&(l=0),i=this.bMarks[c]+l,a=c+1<t||r?this.eMarks[c]+1:this.eMarks[c],s[o]=this.src.slice(i,a);return s.join("")};var Tr={};["article","aside","button","blockquote","body","canvas","caption","col","colgroup","dd","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","iframe","li","map","object","ol","output","p","pre","progress","script","section","style","table","tbody","td","textarea","tfoot","th","tr","thead","ul","video"].forEach((function(e){Tr[e]=!0}));var Mr=/^<([a-zA-Z]{1,15})[\s\/>]/,Rr=/^<\/([a-zA-Z]{1,15})[\s>]/;function zr(e,t){var n=e.bMarks[t]+e.blkIndent,r=e.eMarks[t];return e.src.substr(n,r-n)}function Nr(e,t){var n,r,o=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];return o>=i||126!==(r=e.src.charCodeAt(o++))&&58!==r||o===(n=e.skipSpaces(o))||n>=i?-1:n}var Lr=[["code",function(e,t,n){var r,o;if(e.tShift[t]-e.blkIndent<4)return!1;for(o=r=t+1;r<n;)if(e.isEmpty(r))r++;else{if(!(e.tShift[r]-e.blkIndent>=4))break;o=++r}return e.line=r,e.tokens.push({type:"code",content:e.getLines(t,o,4+e.blkIndent,!0),block:!0,lines:[t,e.line],level:e.level}),!0}],["fences",function(e,t,n,r){var o,i,a,s,l,c=!1,u=e.bMarks[t]+e.tShift[t],p=e.eMarks[t];if(u+3>p)return!1;if(126!==(o=e.src.charCodeAt(u))&&96!==o)return!1;if(l=u,(i=(u=e.skipChars(u,o))-l)<3)return!1;if((a=e.src.slice(u,p).trim()).indexOf("`")>=0)return!1;if(r)return!0;for(s=t;!(++s>=n||(u=l=e.bMarks[s]+e.tShift[s])<(p=e.eMarks[s])&&e.tShift[s]<e.blkIndent);)if(e.src.charCodeAt(u)===o&&!(e.tShift[s]-e.blkIndent>=4||(u=e.skipChars(u,o))-l<i||(u=e.skipSpaces(u))<p)){c=!0;break}return i=e.tShift[t],e.line=s+(c?1:0),e.tokens.push({type:"fence",params:a,content:e.getLines(t+1,s,i,!0),lines:[t,e.line],level:e.level}),!0},["paragraph","blockquote","list"]],["blockquote",function(e,t,n,r){var o,i,a,s,l,c,u,p,d,f,h,A=e.bMarks[t]+e.tShift[t],b=e.eMarks[t];if(A>b)return!1;if(62!==e.src.charCodeAt(A++))return!1;if(e.level>=e.options.maxNesting)return!1;if(r)return!0;for(32===e.src.charCodeAt(A)&&A++,l=e.blkIndent,e.blkIndent=0,s=[e.bMarks[t]],e.bMarks[t]=A,i=(A=A<b?e.skipSpaces(A):A)>=b,a=[e.tShift[t]],e.tShift[t]=A-e.bMarks[t],p=e.parser.ruler.getRules("blockquote"),o=t+1;o<n&&!((A=e.bMarks[o]+e.tShift[o])>=(b=e.eMarks[o]));o++)if(62!==e.src.charCodeAt(A++)){if(i)break;for(h=!1,d=0,f=p.length;d<f;d++)if(p[d](e,o,n,!0)){h=!0;break}if(h)break;s.push(e.bMarks[o]),a.push(e.tShift[o]),e.tShift[o]=-1337}else 32===e.src.charCodeAt(A)&&A++,s.push(e.bMarks[o]),e.bMarks[o]=A,i=(A=A<b?e.skipSpaces(A):A)>=b,a.push(e.tShift[o]),e.tShift[o]=A-e.bMarks[o];for(c=e.parentType,e.parentType="blockquote",e.tokens.push({type:"blockquote_open",lines:u=[t,0],level:e.level++}),e.parser.tokenize(e,t,o),e.tokens.push({type:"blockquote_close",level:--e.level}),e.parentType=c,u[1]=e.line,d=0;d<a.length;d++)e.bMarks[d+t]=s[d],e.tShift[d+t]=a[d];return e.blkIndent=l,!0},["paragraph","blockquote","list"]],["hr",function(e,t,n,r){var o,i,a,s=e.bMarks[t],l=e.eMarks[t];if((s+=e.tShift[t])>l)return!1;if(42!==(o=e.src.charCodeAt(s++))&&45!==o&&95!==o)return!1;for(i=1;s<l;){if((a=e.src.charCodeAt(s++))!==o&&32!==a)return!1;a===o&&i++}return!(i<3||(r||(e.line=t+1,e.tokens.push({type:"hr",lines:[t,e.line],level:e.level})),0))},["paragraph","blockquote","list"]],["list",function(e,t,n,r){var o,i,a,s,l,c,u,p,d,f,h,A,b,v,g,y,m,w,E,C,k,x=!0;if((p=Fr(e,t))>=0)A=!0;else{if(!((p=Ir(e,t))>=0))return!1;A=!1}if(e.level>=e.options.maxNesting)return!1;if(h=e.src.charCodeAt(p-1),r)return!0;for(v=e.tokens.length,A?(u=e.bMarks[t]+e.tShift[t],f=Number(e.src.substr(u,p-u-1)),e.tokens.push({type:"ordered_list_open",order:f,lines:y=[t,0],level:e.level++})):e.tokens.push({type:"bullet_list_open",lines:y=[t,0],level:e.level++}),o=t,g=!1,w=e.parser.ruler.getRules("list");!(!(o<n)||((d=(b=e.skipSpaces(p))>=e.eMarks[o]?1:b-p)>4&&(d=1),d<1&&(d=1),i=p-e.bMarks[o]+d,e.tokens.push({type:"list_item_open",lines:m=[t,0],level:e.level++}),s=e.blkIndent,l=e.tight,a=e.tShift[t],c=e.parentType,e.tShift[t]=b-e.bMarks[t],e.blkIndent=i,e.tight=!0,e.parentType="list",e.parser.tokenize(e,t,n,!0),e.tight&&!g||(x=!1),g=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=s,e.tShift[t]=a,e.tight=l,e.parentType=c,e.tokens.push({type:"list_item_close",level:--e.level}),o=t=e.line,m[1]=o,b=e.bMarks[t],o>=n)||e.isEmpty(o)||e.tShift[o]<e.blkIndent);){for(k=!1,E=0,C=w.length;E<C;E++)if(w[E](e,o,n,!0)){k=!0;break}if(k)break;if(A){if((p=Fr(e,o))<0)break}else if((p=Ir(e,o))<0)break;if(h!==e.src.charCodeAt(p-1))break}return e.tokens.push({type:A?"ordered_list_close":"bullet_list_close",level:--e.level}),y[1]=o,e.line=o,x&&function(e,t){var n,r,o=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===o&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].tight=!0,e.tokens[n].tight=!0,n+=2)}(e,v),!0},["paragraph","blockquote"]],["footnote",function(e,t,n,r){var o,i,a,s,l,c=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(c+4>u)return!1;if(91!==e.src.charCodeAt(c))return!1;if(94!==e.src.charCodeAt(c+1))return!1;if(e.level>=e.options.maxNesting)return!1;for(s=c+2;s<u;s++){if(32===e.src.charCodeAt(s))return!1;if(93===e.src.charCodeAt(s))break}return!(s===c+2||s+1>=u||58!==e.src.charCodeAt(++s)||(r||(s++,e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.refs||(e.env.footnotes.refs={}),l=e.src.slice(c+2,s-2),e.env.footnotes.refs[":"+l]=-1,e.tokens.push({type:"footnote_reference_open",label:l,level:e.level++}),o=e.bMarks[t],i=e.tShift[t],a=e.parentType,e.tShift[t]=e.skipSpaces(s)-s,e.bMarks[t]=s,e.blkIndent+=4,e.parentType="footnote",e.tShift[t]<e.blkIndent&&(e.tShift[t]+=e.blkIndent,e.bMarks[t]-=e.blkIndent),e.parser.tokenize(e,t,n,!0),e.parentType=a,e.blkIndent-=4,e.tShift[t]=i,e.bMarks[t]=o,e.tokens.push({type:"footnote_reference_close",level:--e.level})),0))},["paragraph"]],["heading",function(e,t,n,r){var o,i,a,s=e.bMarks[t]+e.tShift[t],l=e.eMarks[t];if(s>=l)return!1;if(35!==(o=e.src.charCodeAt(s))||s>=l)return!1;for(i=1,o=e.src.charCodeAt(++s);35===o&&s<l&&i<=6;)i++,o=e.src.charCodeAt(++s);return!(i>6||s<l&&32!==o||(r||(l=e.skipCharsBack(l,32,s),(a=e.skipCharsBack(l,35,s))>s&&32===e.src.charCodeAt(a-1)&&(l=a),e.line=t+1,e.tokens.push({type:"heading_open",hLevel:i,lines:[t,e.line],level:e.level}),s<l&&e.tokens.push({type:"inline",content:e.src.slice(s,l).trim(),level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"heading_close",hLevel:i,level:e.level})),0))},["paragraph","blockquote"]],["lheading",function(e,t,n){var r,o,i,a=t+1;return!(a>=n||e.tShift[a]<e.blkIndent||e.tShift[a]-e.blkIndent>3||(o=e.bMarks[a]+e.tShift[a])>=(i=e.eMarks[a])||45!==(r=e.src.charCodeAt(o))&&61!==r||(o=e.skipChars(o,r),(o=e.skipSpaces(o))<i||(o=e.bMarks[t]+e.tShift[t],e.line=a+1,e.tokens.push({type:"heading_open",hLevel:61===r?1:2,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:e.src.slice(o,e.eMarks[t]).trim(),level:e.level+1,lines:[t,e.line-1],children:[]}),e.tokens.push({type:"heading_close",hLevel:61===r?1:2,level:e.level}),0)))}],["htmlblock",function(e,t,n,r){var o,i,a,s=e.bMarks[t],l=e.eMarks[t],c=e.tShift[t];if(s+=c,!e.options.html)return!1;if(c>3||s+2>=l)return!1;if(60!==e.src.charCodeAt(s))return!1;if(33===(o=e.src.charCodeAt(s+1))||63===o){if(r)return!0}else{if(47!==o&&!function(e){var t=32|e;return t>=97&&t<=122}(o))return!1;if(47===o){if(!(i=e.src.slice(s,l).match(Rr)))return!1}else if(!(i=e.src.slice(s,l).match(Mr)))return!1;if(!0!==Tr[i[1].toLowerCase()])return!1;if(r)return!0}for(a=t+1;a<e.lineMax&&!e.isEmpty(a);)a++;return e.line=a,e.tokens.push({type:"htmlblock",level:e.level,lines:[t,e.line],content:e.getLines(t,a,0,!0)}),!0},["paragraph","blockquote"]],["table",function(e,t,n,r){var o,i,a,s,l,c,u,p,d,f,h;if(t+2>n)return!1;if(l=t+1,e.tShift[l]<e.blkIndent)return!1;if((a=e.bMarks[l]+e.tShift[l])>=e.eMarks[l])return!1;if(124!==(o=e.src.charCodeAt(a))&&45!==o&&58!==o)return!1;if(i=zr(e,t+1),!/^[-:| ]+$/.test(i))return!1;if((c=i.split("|"))<=2)return!1;for(p=[],s=0;s<c.length;s++){if(!(d=c[s].trim())){if(0===s||s===c.length-1)continue;return!1}if(!/^:?-+:?$/.test(d))return!1;58===d.charCodeAt(d.length-1)?p.push(58===d.charCodeAt(0)?"center":"right"):58===d.charCodeAt(0)?p.push("left"):p.push("")}if(-1===(i=zr(e,t).trim()).indexOf("|"))return!1;if(c=i.replace(/^\||\|$/g,"").split("|"),p.length!==c.length)return!1;if(r)return!0;for(e.tokens.push({type:"table_open",lines:f=[t,0],level:e.level++}),e.tokens.push({type:"thead_open",lines:[t,t+1],level:e.level++}),e.tokens.push({type:"tr_open",lines:[t,t+1],level:e.level++}),s=0;s<c.length;s++)e.tokens.push({type:"th_open",align:p[s],lines:[t,t+1],level:e.level++}),e.tokens.push({type:"inline",content:c[s].trim(),lines:[t,t+1],level:e.level,children:[]}),e.tokens.push({type:"th_close",level:--e.level});for(e.tokens.push({type:"tr_close",level:--e.level}),e.tokens.push({type:"thead_close",level:--e.level}),e.tokens.push({type:"tbody_open",lines:h=[t+2,0],level:e.level++}),l=t+2;l<n&&!(e.tShift[l]<e.blkIndent)&&-1!==(i=zr(e,l).trim()).indexOf("|");l++){for(c=i.replace(/^\||\|$/g,"").split("|"),e.tokens.push({type:"tr_open",level:e.level++}),s=0;s<c.length;s++)e.tokens.push({type:"td_open",align:p[s],level:e.level++}),u=c[s].substring(124===c[s].charCodeAt(0)?1:0,124===c[s].charCodeAt(c[s].length-1)?c[s].length-1:c[s].length).trim(),e.tokens.push({type:"inline",content:u,level:e.level,children:[]}),e.tokens.push({type:"td_close",level:--e.level});e.tokens.push({type:"tr_close",level:--e.level})}return e.tokens.push({type:"tbody_close",level:--e.level}),e.tokens.push({type:"table_close",level:--e.level}),f[1]=h[1]=l,e.line=l,!0},["paragraph"]],["deflist",function(e,t,n,r){var o,i,a,s,l,c,u,p,d,f,h,A,b,v;if(r)return!(e.ddIndent<0)&&Nr(e,t)>=0;if(u=t+1,e.isEmpty(u)&&++u>n)return!1;if(e.tShift[u]<e.blkIndent)return!1;if((o=Nr(e,u))<0)return!1;if(e.level>=e.options.maxNesting)return!1;c=e.tokens.length,e.tokens.push({type:"dl_open",lines:l=[t,0],level:e.level++}),a=t,i=u;e:for(;;){for(v=!0,b=!1,e.tokens.push({type:"dt_open",lines:[a,a],level:e.level++}),e.tokens.push({type:"inline",content:e.getLines(a,a+1,e.blkIndent,!1).trim(),level:e.level+1,lines:[a,a],children:[]}),e.tokens.push({type:"dt_close",level:--e.level});;){if(e.tokens.push({type:"dd_open",lines:s=[u,0],level:e.level++}),A=e.tight,d=e.ddIndent,p=e.blkIndent,h=e.tShift[i],f=e.parentType,e.blkIndent=e.ddIndent=e.tShift[i]+2,e.tShift[i]=o-e.bMarks[i],e.tight=!0,e.parentType="deflist",e.parser.tokenize(e,i,n,!0),e.tight&&!b||(v=!1),b=e.line-i>1&&e.isEmpty(e.line-1),e.tShift[i]=h,e.tight=A,e.parentType=f,e.blkIndent=p,e.ddIndent=d,e.tokens.push({type:"dd_close",level:--e.level}),s[1]=u=e.line,u>=n)break e;if(e.tShift[u]<e.blkIndent)break e;if((o=Nr(e,u))<0)break;i=u}if(u>=n)break;if(a=u,e.isEmpty(a))break;if(e.tShift[a]<e.blkIndent)break;if((i=a+1)>=n)break;if(e.isEmpty(i)&&i++,i>=n)break;if(e.tShift[i]<e.blkIndent)break;if((o=Nr(e,i))<0)break}return e.tokens.push({type:"dl_close",level:--e.level}),l[1]=u,e.line=u,v&&function(e,t){var n,r,o=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===o&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].tight=!0,e.tokens[n].tight=!0,n+=2)}(e,c),!0},["paragraph"]],["paragraph",function(e,t){var n,r,o,i,a,s,l=t+1;if(l<(n=e.lineMax)&&!e.isEmpty(l))for(s=e.parser.ruler.getRules("paragraph");l<n&&!e.isEmpty(l);l++)if(!(e.tShift[l]-e.blkIndent>3)){for(o=!1,i=0,a=s.length;i<a;i++)if(s[i](e,l,n,!0)){o=!0;break}if(o)break}return r=e.getLines(t,l,e.blkIndent,!1).trim(),e.line=l,r.length&&(e.tokens.push({type:"paragraph_open",tight:!1,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:r,level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"paragraph_close",tight:!1,level:e.level})),!0}]];function qr(){this.ruler=new pr;for(var e=0;e<Lr.length;e++)this.ruler.push(Lr[e][0],Lr[e][1],{alt:(Lr[e][2]||[]).slice()})}qr.prototype.tokenize=function(e,t,n){for(var r,o=this.ruler.getRules(""),i=o.length,a=t,s=!1;a<n&&(e.line=a=e.skipEmptyLines(a),!(a>=n))&&!(e.tShift[a]<e.blkIndent);){for(r=0;r<i&&!o[r](e,a,n,!1);r++);if(e.tight=!s,e.isEmpty(e.line-1)&&(s=!0),(a=e.line)<n&&e.isEmpty(a)){if(s=!0,++a<n&&"list"===e.parentType&&e.isEmpty(a))break;e.line=a}}};var Wr=/[\n\t]/g,Ur=/\r[\n\u0085]|[\u2424\u2028\u0085]/g,Vr=/\u00a0/g;function Hr(e){switch(e){case 10:case 92:case 96:case 42:case 95:case 94:case 91:case 93:case 33:case 38:case 60:case 62:case 123:case 125:case 36:case 37:case 64:case 126:case 43:case 61:case 58:return!0;default:return!1}}qr.prototype.parse=function(e,t,n,r){var o,i=0,a=0;if(!e)return[];(e=(e=e.replace(Vr," ")).replace(Ur,"\n")).indexOf("\t")>=0&&(e=e.replace(Wr,(function(t,n){var r;return 10===e.charCodeAt(n)?(i=n+1,a=0,t):(r="    ".slice((n-i-a)%4),a=n-i+1,r)}))),o=new Dr(e,this,t,n,r),this.tokenize(o,o.line,o.lineMax)};for(var Yr=[],Kr=0;Kr<256;Kr++)Yr.push(0);function Zr(e){return e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function Qr(e,t){var n,r,o,i=t,a=!0,s=!0,l=e.posMax,c=e.src.charCodeAt(t);for(n=t>0?e.src.charCodeAt(t-1):-1;i<l&&e.src.charCodeAt(i)===c;)i++;return i>=l&&(a=!1),(o=i-t)>=4?a=s=!1:(32!==(r=i<l?e.src.charCodeAt(i):-1)&&10!==r||(a=!1),32!==n&&10!==n||(s=!1),95===c&&(Zr(n)&&(a=!1),Zr(r)&&(s=!1))),{can_open:a,can_close:s,delims:o}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){Yr[e.charCodeAt(0)]=1}));var $r=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g,Gr=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g,Jr=["coap","doi","javascript","aaa","aaas","about","acap","cap","cid","crid","data","dav","dict","dns","file","ftp","geo","go","gopher","h323","http","https","iax","icap","im","imap","info","ipp","iris","iris.beep","iris.xpc","iris.xpcs","iris.lwz","ldap","mailto","mid","msrp","msrps","mtqp","mupdate","news","nfs","ni","nih","nntp","opaquelocktoken","pop","pres","rtsp","service","session","shttp","sieve","sip","sips","sms","snmp","soap.beep","soap.beeps","tag","tel","telnet","tftp","thismessage","tn3270","tip","tv","urn","vemmi","ws","wss","xcon","xcon-userid","xmlrpc.beep","xmlrpc.beeps","xmpp","z39.50r","z39.50s","adiumxtra","afp","afs","aim","apt","attachment","aw","beshare","bitcoin","bolo","callto","chrome","chrome-extension","com-eventbrite-attendee","content","cvs","dlna-playsingle","dlna-playcontainer","dtn","dvb","ed2k","facetime","feed","finger","fish","gg","git","gizmoproject","gtalk","hcp","icon","ipn","irc","irc6","ircs","itms","jar","jms","keyparc","lastfm","ldaps","magnet","maps","market","message","mms","ms-help","msnim","mumble","mvn","notes","oid","palm","paparazzi","platform","proxy","psyc","query","res","resource","rmi","rsync","rtmp","secondlife","sftp","sgn","skype","smb","soldat","spotify","ssh","steam","svn","teamspeak","things","udp","unreal","ut2004","ventrilo","view-source","webcal","wtai","wyciwyg","xfire","xri","ymsgr"],Xr=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,eo=/^<([a-zA-Z.\-]{1,25}):([^<>\x00-\x20]*)>/;function to(e,t){return e=e.source,t=t||"",function n(r,o){return r?(o=o.source||o,e=e.replace(r,o),n):new RegExp(e,t)}}var no=to(/(?:unquoted|single_quoted|double_quoted)/)("unquoted",/[^"'=<>`\x00-\x20]+/)("single_quoted",/'[^']*'/)("double_quoted",/"[^"]*"/)(),ro=to(/(?:\s+attr_name(?:\s*=\s*attr_value)?)/)("attr_name",/[a-zA-Z_:][a-zA-Z0-9:._-]*/)("attr_value",no)(),oo=to(/<[A-Za-z][A-Za-z0-9]*attribute*\s*\/?>/)("attribute",ro)(),io=to(/^(?:open_tag|close_tag|comment|processing|declaration|cdata)/)("open_tag",oo)("close_tag",/<\/[A-Za-z][A-Za-z0-9]*\s*>/)("comment",/<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->/)("processing",/<[?].*?[?]>/)("declaration",/<![A-Z]+\s+[^>]*>/)("cdata",/<!\[CDATA\[[\s\S]*?\]\]>/)(),ao=/^&#((?:x[a-f0-9]{1,8}|[0-9]{1,8}));/i,so=/^&([a-z][a-z0-9]{1,31});/i,lo=[["text",function(e,t){for(var n=e.pos;n<e.posMax&&!Hr(e.src.charCodeAt(n));)n++;return n!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}],["newline",function(e,t){var n,r,o=e.pos;if(10!==e.src.charCodeAt(o))return!1;if(n=e.pending.length-1,r=e.posMax,!t)if(n>=0&&32===e.pending.charCodeAt(n))if(n>=1&&32===e.pending.charCodeAt(n-1)){for(var i=n-2;i>=0;i--)if(32!==e.pending.charCodeAt(i)){e.pending=e.pending.substring(0,i+1);break}e.push({type:"hardbreak",level:e.level})}else e.pending=e.pending.slice(0,-1),e.push({type:"softbreak",level:e.level});else e.push({type:"softbreak",level:e.level});for(o++;o<r&&32===e.src.charCodeAt(o);)o++;return e.pos=o,!0}],["escape",function(e,t){var n,r=e.pos,o=e.posMax;if(92!==e.src.charCodeAt(r))return!1;if(++r<o){if((n=e.src.charCodeAt(r))<256&&0!==Yr[n])return t||(e.pending+=e.src[r]),e.pos+=2,!0;if(10===n){for(t||e.push({type:"hardbreak",level:e.level}),r++;r<o&&32===e.src.charCodeAt(r);)r++;return e.pos=r,!0}}return t||(e.pending+="\\"),e.pos++,!0}],["backticks",function(e,t){var n,r,o,i,a,s=e.pos;if(96!==e.src.charCodeAt(s))return!1;for(n=s,s++,r=e.posMax;s<r&&96===e.src.charCodeAt(s);)s++;for(o=e.src.slice(n,s),i=a=s;-1!==(i=e.src.indexOf("`",a));){for(a=i+1;a<r&&96===e.src.charCodeAt(a);)a++;if(a-i===o.length)return t||e.push({type:"code",content:e.src.slice(s,i).replace(/[ \n]+/g," ").trim(),block:!1,level:e.level}),e.pos=a,!0}return t||(e.pending+=o),e.pos+=o.length,!0}],["del",function(e,t){var n,r,o,i,a,s=e.posMax,l=e.pos;if(126!==e.src.charCodeAt(l))return!1;if(t)return!1;if(l+4>=s)return!1;if(126!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=l>0?e.src.charCodeAt(l-1):-1,a=e.src.charCodeAt(l+2),126===i)return!1;if(126===a)return!1;if(32===a||10===a)return!1;for(r=l+2;r<s&&126===e.src.charCodeAt(r);)r++;if(r>l+3)return e.pos+=r-l,t||(e.pending+=e.src.slice(l,r)),!0;for(e.pos=l+2,o=1;e.pos+1<s;){if(126===e.src.charCodeAt(e.pos)&&126===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),126!==(a=e.pos+2<s?e.src.charCodeAt(e.pos+2):-1)&&126!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=l+2,t||(e.push({type:"del_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"del_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=s,!0):(e.pos=l,!1)}],["ins",function(e,t){var n,r,o,i,a,s=e.posMax,l=e.pos;if(43!==e.src.charCodeAt(l))return!1;if(t)return!1;if(l+4>=s)return!1;if(43!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=l>0?e.src.charCodeAt(l-1):-1,a=e.src.charCodeAt(l+2),43===i)return!1;if(43===a)return!1;if(32===a||10===a)return!1;for(r=l+2;r<s&&43===e.src.charCodeAt(r);)r++;if(r!==l+2)return e.pos+=r-l,t||(e.pending+=e.src.slice(l,r)),!0;for(e.pos=l+2,o=1;e.pos+1<s;){if(43===e.src.charCodeAt(e.pos)&&43===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),43!==(a=e.pos+2<s?e.src.charCodeAt(e.pos+2):-1)&&43!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=l+2,t||(e.push({type:"ins_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"ins_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=s,!0):(e.pos=l,!1)}],["mark",function(e,t){var n,r,o,i,a,s=e.posMax,l=e.pos;if(61!==e.src.charCodeAt(l))return!1;if(t)return!1;if(l+4>=s)return!1;if(61!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=l>0?e.src.charCodeAt(l-1):-1,a=e.src.charCodeAt(l+2),61===i)return!1;if(61===a)return!1;if(32===a||10===a)return!1;for(r=l+2;r<s&&61===e.src.charCodeAt(r);)r++;if(r!==l+2)return e.pos+=r-l,t||(e.pending+=e.src.slice(l,r)),!0;for(e.pos=l+2,o=1;e.pos+1<s;){if(61===e.src.charCodeAt(e.pos)&&61===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),61!==(a=e.pos+2<s?e.src.charCodeAt(e.pos+2):-1)&&61!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=l+2,t||(e.push({type:"mark_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"mark_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=s,!0):(e.pos=l,!1)}],["emphasis",function(e,t){var n,r,o,i,a,s,l,c=e.posMax,u=e.pos,p=e.src.charCodeAt(u);if(95!==p&&42!==p)return!1;if(t)return!1;if(n=(l=Qr(e,u)).delims,!l.can_open)return e.pos+=n,t||(e.pending+=e.src.slice(u,e.pos)),!0;if(e.level>=e.options.maxNesting)return!1;for(e.pos=u+n,s=[n];e.pos<c;)if(e.src.charCodeAt(e.pos)!==p)e.parser.skipToken(e);else{if(r=(l=Qr(e,e.pos)).delims,l.can_close){for(i=s.pop(),a=r;i!==a;){if(a<i){s.push(i-a);break}if(a-=i,0===s.length)break;e.pos+=i,i=s.pop()}if(0===s.length){n=i,o=!0;break}e.pos+=r;continue}l.can_open&&s.push(r),e.pos+=r}return o?(e.posMax=e.pos,e.pos=u+n,t||(2!==n&&3!==n||e.push({type:"strong_open",level:e.level++}),1!==n&&3!==n||e.push({type:"em_open",level:e.level++}),e.parser.tokenize(e),1!==n&&3!==n||e.push({type:"em_close",level:--e.level}),2!==n&&3!==n||e.push({type:"strong_close",level:--e.level})),e.pos=e.posMax+n,e.posMax=c,!0):(e.pos=u,!1)}],["sub",function(e,t){var n,r,o=e.posMax,i=e.pos;if(126!==e.src.charCodeAt(i))return!1;if(t)return!1;if(i+2>=o)return!1;if(e.level>=e.options.maxNesting)return!1;for(e.pos=i+1;e.pos<o;){if(126===e.src.charCodeAt(e.pos)){n=!0;break}e.parser.skipToken(e)}return n&&i+1!==e.pos?(r=e.src.slice(i+1,e.pos)).match(/(^|[^\\])(\\\\)*\s/)?(e.pos=i,!1):(e.posMax=e.pos,e.pos=i+1,t||e.push({type:"sub",level:e.level,content:r.replace($r,"$1")}),e.pos=e.posMax+1,e.posMax=o,!0):(e.pos=i,!1)}],["sup",function(e,t){var n,r,o=e.posMax,i=e.pos;if(94!==e.src.charCodeAt(i))return!1;if(t)return!1;if(i+2>=o)return!1;if(e.level>=e.options.maxNesting)return!1;for(e.pos=i+1;e.pos<o;){if(94===e.src.charCodeAt(e.pos)){n=!0;break}e.parser.skipToken(e)}return n&&i+1!==e.pos?(r=e.src.slice(i+1,e.pos)).match(/(^|[^\\])(\\\\)*\s/)?(e.pos=i,!1):(e.posMax=e.pos,e.pos=i+1,t||e.push({type:"sup",level:e.level,content:r.replace(Gr,"$1")}),e.pos=e.posMax+1,e.posMax=o,!0):(e.pos=i,!1)}],["links",function(e,t){var n,r,o,i,a,s,l,c,u=!1,p=e.pos,d=e.posMax,f=e.pos,h=e.src.charCodeAt(f);if(33===h&&(u=!0,h=e.src.charCodeAt(++f)),91!==h)return!1;if(e.level>=e.options.maxNesting)return!1;if(n=f+1,(r=fr(e,f))<0)return!1;if((s=r+1)<d&&40===e.src.charCodeAt(s)){for(s++;s<d&&(32===(c=e.src.charCodeAt(s))||10===c);s++);if(s>=d)return!1;for(f=s,br(e,s)?(i=e.linkContent,s=e.pos):i="",f=s;s<d&&(32===(c=e.src.charCodeAt(s))||10===c);s++);if(s<d&&f!==s&&vr(e,s))for(a=e.linkContent,s=e.pos;s<d&&(32===(c=e.src.charCodeAt(s))||10===c);s++);else a="";if(s>=d||41!==e.src.charCodeAt(s))return e.pos=p,!1;s++}else{if(e.linkLevel>0)return!1;for(;s<d&&(32===(c=e.src.charCodeAt(s))||10===c);s++);if(s<d&&91===e.src.charCodeAt(s)&&(f=s+1,(s=fr(e,s))>=0?o=e.src.slice(f,s++):s=f-1),o||(void 0===o&&(s=r+1),o=e.src.slice(n,r)),!(l=e.env.references[gr(o)]))return e.pos=p,!1;i=l.href,a=l.title}return t||(e.pos=n,e.posMax=r,u?e.push({type:"image",src:i,title:a,alt:e.src.substr(n,r-n),level:e.level}):(e.push({type:"link_open",href:i,title:a,level:e.level++}),e.linkLevel++,e.parser.tokenize(e),e.linkLevel--,e.push({type:"link_close",level:--e.level}))),e.pos=s,e.posMax=d,!0}],["footnote_inline",function(e,t){var n,r,o,i,a=e.posMax,s=e.pos;return!(s+2>=a||94!==e.src.charCodeAt(s)||91!==e.src.charCodeAt(s+1)||e.level>=e.options.maxNesting||(n=s+2,(r=fr(e,s+1))<0||(t||(e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.list||(e.env.footnotes.list=[]),o=e.env.footnotes.list.length,e.pos=n,e.posMax=r,e.push({type:"footnote_ref",id:o,level:e.level}),e.linkLevel++,i=e.tokens.length,e.parser.tokenize(e),e.env.footnotes.list[o]={tokens:e.tokens.splice(i)},e.linkLevel--),e.pos=r+1,e.posMax=a,0)))}],["footnote_ref",function(e,t){var n,r,o,i,a=e.posMax,s=e.pos;if(s+3>a)return!1;if(!e.env.footnotes||!e.env.footnotes.refs)return!1;if(91!==e.src.charCodeAt(s))return!1;if(94!==e.src.charCodeAt(s+1))return!1;if(e.level>=e.options.maxNesting)return!1;for(r=s+2;r<a;r++){if(32===e.src.charCodeAt(r))return!1;if(10===e.src.charCodeAt(r))return!1;if(93===e.src.charCodeAt(r))break}return!(r===s+2||r>=a||(r++,n=e.src.slice(s+2,r-1),void 0===e.env.footnotes.refs[":"+n]||(t||(e.env.footnotes.list||(e.env.footnotes.list=[]),e.env.footnotes.refs[":"+n]<0?(o=e.env.footnotes.list.length,e.env.footnotes.list[o]={label:n,count:0},e.env.footnotes.refs[":"+n]=o):o=e.env.footnotes.refs[":"+n],i=e.env.footnotes.list[o].count,e.env.footnotes.list[o].count++,e.push({type:"footnote_ref",id:o,subId:i,level:e.level})),e.pos=r,e.posMax=a,0)))}],["autolink",function(e,t){var n,r,o,i,a,s=e.pos;return!(60!==e.src.charCodeAt(s)||(n=e.src.slice(s)).indexOf(">")<0||((r=n.match(eo))?Jr.indexOf(r[1].toLowerCase())<0||(a=Ar(i=r[0].slice(1,-1)),!e.parser.validateLink(i)||(t||(e.push({type:"link_open",href:a,level:e.level}),e.push({type:"text",content:i,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=r[0].length,0)):!(o=n.match(Xr))||(a=Ar("mailto:"+(i=o[0].slice(1,-1))),!e.parser.validateLink(a)||(t||(e.push({type:"link_open",href:a,level:e.level}),e.push({type:"text",content:i,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=o[0].length,0))))}],["htmltag",function(e,t){var n,r,o,i=e.pos;return!(!e.options.html||(o=e.posMax,60!==e.src.charCodeAt(i)||i+2>=o||33!==(n=e.src.charCodeAt(i+1))&&63!==n&&47!==n&&!function(e){var t=32|e;return t>=97&&t<=122}(n)||!(r=e.src.slice(i).match(io))||(t||e.push({type:"htmltag",content:e.src.slice(i,i+r[0].length),level:e.level}),e.pos+=r[0].length,0)))}],["entity",function(e,t){var n,r,o=e.pos,i=e.posMax;if(38!==e.src.charCodeAt(o))return!1;if(o+1<i)if(35===e.src.charCodeAt(o+1)){if(r=e.src.slice(o).match(ao))return t||(n="x"===r[1][0].toLowerCase()?parseInt(r[1].slice(1),16):parseInt(r[1],10),e.pending+=$n(n)?Gn(n):Gn(65533)),e.pos+=r[0].length,!0}else if(r=e.src.slice(o).match(so)){var a=Hn(r[1]);if(r[1]!==a)return t||(e.pending+=a),e.pos+=r[0].length,!0}return t||(e.pending+="&"),e.pos++,!0}]];function co(){this.ruler=new pr;for(var e=0;e<lo.length;e++)this.ruler.push(lo[e][0],lo[e][1]);this.validateLink=uo}function uo(e){var t=e.trim().toLowerCase();return-1===(t=tr(t)).indexOf(":")||-1===["vbscript","javascript","file","data"].indexOf(t.split(":")[0])}co.prototype.skipToken=function(e){var t,n,r=this.ruler.getRules(""),o=r.length,i=e.pos;if((n=e.cacheGet(i))>0)e.pos=n;else{for(t=0;t<o;t++)if(r[t](e,!0))return void e.cacheSet(i,e.pos);e.pos++,e.cacheSet(i,e.pos)}},co.prototype.tokenize=function(e){for(var t,n,r=this.ruler.getRules(""),o=r.length,i=e.posMax;e.pos<i;){for(n=0;n<o&&!(t=r[n](e,!1));n++);if(t){if(e.pos>=i)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},co.prototype.parse=function(e,t,n,r){var o=new dr(e,this,t,n,r);this.tokenize(o)};var po={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","replacements","smartquotes","references","abbr2","footnote_tail"]},block:{rules:["blockquote","code","fences","footnote","heading","hr","htmlblock","lheading","list","paragraph","table"]},inline:{rules:["autolink","backticks","del","emphasis","entity","escape","footnote_ref","htmltag","links","newline","text"]}}},full:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{},block:{},inline:{}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","abbr2"]},block:{rules:["blockquote","code","fences","heading","hr","htmlblock","lheading","list","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","htmltag","links","newline","text"]}}}};function fo(e,t,n){this.src=t,this.env=n,this.options=e.options,this.tokens=[],this.inlineMode=!1,this.inline=e.inline,this.block=e.block,this.renderer=e.renderer,this.typographer=e.typographer}function ho(e,t){"string"!=typeof e&&(t=e,e="default"),t&&null!=t.linkify&&console.warn("linkify option is removed. Use linkify plugin instead:\n\nimport Remarkable from 'remarkable';\nimport linkify from 'remarkable/linkify';\nnew Remarkable().use(linkify)\n"),this.inline=new co,this.block=new qr,this.core=new Pr,this.renderer=new ur,this.ruler=new pr,this.options={},this.configure(po[e]),this.set(t||{})}ho.prototype.set=function(e){Kn(this.options,e)},ho.prototype.configure=function(e){var t=this;if(!e)throw new Error("Wrong `remarkable` preset, check name/content");e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(n){e.components[n].rules&&t[n].ruler.enable(e.components[n].rules,!0)}))},ho.prototype.use=function(e,t){return e(this,t),this},ho.prototype.parse=function(e,t){var n=new fo(this,e,t);return this.core.process(n),n.tokens},ho.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},ho.prototype.parseInline=function(e,t){var n=new fo(this,e,t);return n.inlineMode=!0,this.core.process(n),n.tokens},ho.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};var Ao=function(e){return e.map((function(e,t){return t?e.charAt(0).toUpperCase()+e.substring(1):e})).join("")},bo=function(e){return e.join("-")},vo=function(e){return e.join("_")},go=[],yo=[];[["align","content"],["align","items"],["alignment","adjust"],["alignment","baseline"],["align","self"],["animation","delay"],["animation","direction"],["animation","iteration","count"],["animation","name"],["animation","play","state"],["appearance"],["backface","visibility"],["background"],["background","attachment"],["background","blend","mode"],["background","color"],["background","composite"],["background","image"],["background","origin"],["background","position"],["background","repeat"],["baseline","shift"],["behavior"],["border"],["border","bottom"],["border","bottom","color"],["border","bottom","left","radius"],["border","bottom","right","radius"],["border","bottom","style"],["border","bottom","width"],["border","collapse"],["border","color"],["border","corner","shape"],["border","image","source"],["border","image","width"],["border","left"],["border","left","color"],["border","left","style"],["border","left","width"],["border","radius"],["border","right"],["border","right","color"],["border","right","style"],["border","right","width"],["border","spacing"],["border","style"],["border","top"],["border","top","color"],["border","top","left","radius"],["border","top","right","radius"],["border","top","style"],["border","top","width"],["border","width"],["bottom"],["box","align"],["box","decoration","break"],["box","direction"],["box","flex"],["box","flex","group"],["box","line","progression"],["box","lines"],["box","ordinal","group"],["box","shadow"],["break","after"],["break","before"],["break","inside"],["clear"],["clip"],["clip","rule"],["color"],["column","count"],["column","fill"],["column","gap"],["column","rule"],["column","rule","color"],["column","rule","width"],["columns"],["column","span"],["column","width"],["counter","increment"],["counter","reset"],["cue"],["cue","after"],["cursor"],["direction"],["display"],["fill"],["fill","opacity"],["fill","rule"],["filter"],["flex"],["flex","align"],["flex","basis"],["flex","direction"],["flex","flow"],["flex","grow"],["flex","item","align"],["flex","line","pack"],["flex","order"],["flex","shrink"],["flex","wrap"],["float"],["flow","from"],["font"],["font","family"],["font","kerning"],["font","size"],["font","size","adjust"],["font","stretch"],["font","style"],["font","synthesis"],["font","variant"],["font","variant","alternates"],["font","weight"],["grid","area"],["grid","column"],["grid","column","end"],["grid","column","start"],["grid","row"],["grid","row","end"],["grid","row","position"],["grid","row","span"],["grid","template","areas"],["grid","template","columns"],["grid","template","rows"],["height"],["hyphenate","limit","chars"],["hyphenate","limit","lines"],["hyphenate","limit","zone"],["hyphens"],["ime","mode"],["justify","content"],["layout","grid"],["layout","grid","char"],["layout","grid","line"],["layout","grid","mode"],["layout","grid","type"],["left"],["letter","spacing"],["line","break"],["line","clamp"],["line","height"],["list","style"],["list","style","image"],["list","style","position"],["list","style","type"],["margin"],["margin","bottom"],["margin","left"],["margin","right"],["margin","top"],["marquee","direction"],["marquee","style"],["mask"],["mask","border"],["mask","border","repeat"],["mask","border","slice"],["mask","border","source"],["mask","border","width"],["mask","clip"],["mask","origin"],["max","font","size"],["max","height"],["max","width"],["min","height"],["min","width"],["opacity"],["order"],["orphans"],["outline"],["outline","color"],["outline","offset"],["overflow"],["overflow","style"],["overflow","x"],["overflow","y"],["padding"],["padding","bottom"],["padding","left"],["padding","right"],["padding","top"],["page","break","after"],["page","break","before"],["page","break","inside"],["pause"],["pause","after"],["pause","before"],["perspective"],["perspective","origin"],["pointer","events"],["position"],["punctuation","trim"],["quotes"],["region","fragment"],["rest","after"],["rest","before"],["right"],["ruby","align"],["ruby","position"],["shape","image","threshold"],["shape","inside"],["shape","margin"],["shape","outside"],["speak"],["speak","as"],["stroke","opacity"],["stroke","width"],["table","layout"],["tab","size"],["text","align"],["text","align","last"],["text","decoration"],["text","decoration","color"],["text","decoration","line"],["text","decoration","line","through"],["text","decoration","none"],["text","decoration","overline"],["text","decoration","skip"],["text","decoration","style"],["text","decoration","underline"],["text","emphasis"],["text","emphasis","color"],["text","emphasis","style"],["text","height"],["text","indent"],["text","justify","trim"],["text","kashida","space"],["text","line","through"],["text","line","through","color"],["text","line","through","mode"],["text","line","through","style"],["text","line","through","width"],["text","overflow"],["text","overline"],["text","overline","color"],["text","overline","mode"],["text","overline","style"],["text","overline","width"],["text","rendering"],["text","script"],["text","shadow"],["text","transform"],["text","underline","position"],["text","underline","style"],["top"],["touch","action"],["transform"],["transform","origin"],["transform","origin","z"],["transform","style"],["transition"],["transition","delay"],["transition","duration"],["transition","property"],["transition","timing","function"],["unicode","bidi"],["unicode","range"],["user","focus"],["user","input"],["vertical","align"],["visibility"],["voice","balance"],["voice","duration"],["voice","family"],["voice","pitch"],["voice","range"],["voice","rate"],["voice","stress"],["voice","volume"],["white","space"],["white","space","treatment"],["widows"],["width"],["word","break"],["word","spacing"],["word","wrap"],["wrap","flow"],["wrap","margin"],["wrap","option"],["writing","mode"],["z","index"],["zoom"]].forEach((function(e){var t=Ao(e);go.push(t),yo.push([t,t]),yo.push([bo(e),t]),yo.push([vo(e),t])}));var mo=new Map(yo),wo=go;function Eo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Co=function e(t){return null!==t&&"object"==typeof t?(0,i.u4g)((function(t,n){var r=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Eo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Eo(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(n,2),o=r[0],a=r[1];return(0,i.yGi)(Ao(o.split("_")),e(a),t)}),{},(0,i.Zpf)(t)):Array.isArray(t)?t.map(e,t):t};function ko(){ko=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var o=t&&t.prototype instanceof p?t:p,i=Object.create(o.prototype),a=new C(r||[]);return i._invoke=function(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return{value:void 0,done:!0}}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=m(a,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=c(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}(e,n,a),i}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u={};function p(){}function d(){}function f(){}var h={};s(h,o,(function(){return this}));var A=Object.getPrototypeOf,b=A&&A(A(k([])));b&&b!==t&&n.call(b,o)&&(h=b);var v=f.prototype=p.prototype=Object.create(h);function g(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function r(o,i,a,s){var l=c(e[o],e,i);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==typeof p&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(p).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(l.arg)}var o;this._invoke=function(e,n){function i(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(i,i):i()}}function m(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,m(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=c(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function k(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return d.prototype=f,s(v,"constructor",f),s(f,"constructor",d),d.displayName=s(f,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,s(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},g(y.prototype),s(y.prototype,i,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new y(l(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},g(v),s(v,a,"Generator"),s(v,o,(function(){return this})),s(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=k,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,u):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function xo(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function Oo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function So(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Bo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var _o=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Bo(this,"options",void 0),Bo(this,"md",void 0),Bo(this,"render",(function(e){return n.md.render(e)})),this.options=t,this.md=new ho(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Oo(Object(n),!0).forEach((function(t){Bo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({highlight:function(t,n){if(e.hljs){if(n&&e.hljs.getLanguage(n))try{return e.hljs.highlight(n,t).value}catch(e){}try{return e.hljs.highlightAuto(t).value}catch(e){}}else e.loadhljs();return""}},Co(this.options)))}var t,n,r,o;return t=e,null,n=[{key:"isReady",get:function(){return e._isReady}},{key:"loadhljs",value:(r=ko().mark((function t(){return ko().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,j.Z.hljs;case 2:e.hljs=t.sent,e.hljsResolve(),e._isReady=!0;case 5:case"end":return t.stop()}}),t)})),o=function(){var e=this,t=arguments;return new Promise((function(n,o){var i=r.apply(e,t);function a(e){xo(i,n,o,a,s,"next",e)}function s(e){xo(i,n,o,a,s,"throw",e)}a(void 0)}))},function(){return o.apply(this,arguments)})}],n&&So(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function jo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Po(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Do(e,t){return Do=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Do(e,t)}function Io(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Fo(e)}function Fo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function To(e){return To=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},To(e)}function Mo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Bo(_o,"hljs",void 0),Bo(_o,"hljsResolve",void 0),Bo(_o,"_isReady",new Promise((function(e){_o.hljsResolve=e})));var Ro=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Do(e,t)}(l,e);var t,n,r,i,s=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=To(r);if(i){var n=To(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Io(this,e)});function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),Mo(Fo(t=s.call(this,e)),"getMarkdown",(0,a.qe)((function(e,t,n){return{dangerouslySetInnerHTML:{__html:t.render(String(e))}}}))),!0!==_o.isReady&&_o.isReady.then((function(){t.setState({})})),t}return t=l,n=[{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.markdown,r=e.value;return o().createElement("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jo(Object(n),!0).forEach((function(t){Mo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({ref:"el",className:[t,"cell-markdown"].join(" ")},this.getMarkdown(r,n,_o.isReady)))}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.refs.el;if(n&&r&&document.activeElement!==r){var o=In.getFirstParentOfType(r,"td");o&&-1!==o.className.indexOf("phantom-cell")&&o.focus()}}}}],n&&Po(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent);function zo(e){return function(e){if(Array.isArray(e))return No(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return No(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?No(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function No(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Lo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function qo(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Wo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Uo,Vo=i.h0F(i.UID),Ho=i.h0F(i.UID);!function(e){e[e.Dropdown=0]="Dropdown",e[e.DropdownLabel=1]="DropdownLabel",e[e.Input=2]="Input",e[e.Label=3]="Label",e[e.Markdown=4]="Markdown"}(Uo||(Uo={}));var Yo=function(e){return new Ko(e)},Ko=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt(t);Lo(this,e),Wo(this,"handlers",void 0),Wo(this,"cell_selectable",void 0),Wo(this,"partialGet",(0,a.qe)((function(e,t,r,o,a,s,l){var c=i.UID(Vn,e);return Vo((function(t,r){return Ho((function(e,i){return n.getContent(!1,!1,o,e,a&&a[r][i],i,r,t,c,s,l)}),e)}),t)}))),Wo(this,"get",(0,a.qe)((function(e,t,r,o,a,s,l,c,u,p){if(!t)return e;var d=t.row,f=t.column,h=d-s.rows,A=f-s.columns;if(h<0||A<0||a.length<=h||o.length<=A)return e;var b=i.UID(Vn,o);return(e=He(e))[h][A]=n.getContent(!0,r||!1,l,o[A],c&&c[h][A],f,d,a[h],b,u,p),e}))),this.handlers=r,this.cell_selectable=t().cell_selectable}var t,n;return t=e,n=[{key:"getContent",value:function(e,t,n,r,i,a,s,l,c,u,p){var d=[].concat(zo(e?["input-active"]:[]),[n?"focused":"unfocused"],zo(this.cell_selectable?["selectable"]:[]),["dash-cell-value"]).join(" "),f=function(e,t,n,r,o){switch(r){case _.Ap.Input:return e&&t&&!o?Uo.Input:Uo.Label;case _.Ap.Dropdown:return n&&t?Uo.Dropdown:Uo.DropdownLabel;case _.Ap.Markdown:return Uo.Markdown;default:return e&&t&&!o?Uo.Input:Uo.Label}}(e,r.editable,i&&i.options,r.presentation,u);switch(f){case Uo.Dropdown:return o().createElement(Wn,{key:"column-".concat(a),active:e,applyFocus:t,clearable:i&&i.clearable,dropdown:i&&i.options,onChange:this.handlers(ot.Change,s,a),value:l[r.id],disabled:u});case Uo.Input:return o().createElement(Ft,{key:"column-".concat(a),active:e,applyFocus:t,className:d,focused:n,onChange:this.handlers(ot.Change,s,a),onMouseUp:this.handlers(ot.MouseUp,s,a),onPaste:this.handlers(ot.Paste,s,a),type:r.type,value:l[r.id]});case Uo.Markdown:return o().createElement(Ro,{active:e,applyFocus:t,className:d,markdown:p,value:l[r.id]});case Uo.DropdownLabel:case Uo.Label:default:var h=f===Uo.DropdownLabel?this.resolveDropdownLabel(i,l[r.id]):c[a](l[r.id]);return o().createElement(Lt,{active:e,applyFocus:t,className:d,key:"column-".concat(a),value:h})}}},{key:"resolveDropdownLabel",value:function(e,t){var n=e&&e.options&&e.options.find((function(e){return e.value===t}));return n?n.label:t}}],n&&qo(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Zo(e){return function(e){if(Array.isArray(e))return Qo(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Qo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Qo(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function $o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Go(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Jo(e,t,n,r,a,s){return o().createElement("td",{key:"select",className:"dash-select-cell",style:{width:"30px",maxWidth:"30px",minWidth:"30px",textAlign:"center"}},o().createElement("input",{type:"single"===n?"radio":"checkbox",style:{verticalAlign:"middle"},name:"row-select-".concat(e),checked:i.q9t(t,r),onChange:function(){var e="single"===n?[t]:i.KJl(i.q9t(t),i.zud([t]),i.R3I(t))(r);a({selected_rows:e,selected_row_ids:i.UID((function(e){return s[e].id}),e)})}}))}var Xo,ei=(0,a.Pi)((function(e,t,n,r,a,s,l,c){return i.h0F(i.UID)((function(n,u){return[].concat(Zo(s?[(p=function(){return c(function(e,t,n){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$o(Object(n),!0).forEach((function(t){Go(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({data:i.OdJ(e,1,t)},B);return i.is(Array,n)&&i.YjB((function(t){return t>=e}),n)&&(r.selected_rows=i.UID((function(t){return t>e?t-1:t}),i.zud([e],n)),r.selected_row_ids=i.UID((function(e){return r.data[e].id}),r.selected_rows)),r}(r[u],t,l))},o().createElement("td",{key:"delete",className:"dash-delete-cell",onClick:function(){return p()},style:{width:"30px",maxWidth:"30px",minWidth:"30px"}},"×"))]:[]),Zo(a?[Jo(e,r[u],a,l,c,t)]:[]));var p}),n)}));function ti(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ni(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ti(Object(n),!0).forEach((function(t){ri(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ti(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ri(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oi(e){return ni(ni({},e),{},{if:function(){return!1},terminal:!1})}!function(e){e.BlockClose="close-block",e.BlockOpen="open-block",e.LogicalOperator="logical-operator",e.RelationalOperator="relational-operator",e.UnaryOperator="unary-operator",e.Expression="expression"}(Xo||(Xo={}));var ii=function e(t){var n=0,r=t.map((function(e){var t=Object.assign({},e,{nesting:n});return n+=e.lexeme.nesting||0,t})),o=r.filter((function(e){return 0===e.nesting&&"number"==typeof e.lexeme.priority})).sort((function(e,t){return(t.lexeme.priority||-1)-(e.lexeme.priority||-1)}))[0];s.ZP.trace("parser -> pivot",o,t);var i=r.indexOf(o);if(o.lexeme.syntaxer){var a=o.lexeme.syntaxer(t,o,i);return Array.isArray(a.left)&&(a.left=e(a.left)),Array.isArray(a.right)&&(a.right=e(a.right)),Array.isArray(a.block)&&(a.block=e(a.block)),a}throw new Error(o.lexeme.type)},ai=function(e){var t=e.lexemes;if(!e.valid)return{valid:!1,error:"lexer -- ".concat(e.error)};if(0===e.lexemes.length)return{valid:!0};try{return{tree:ii(t),valid:!0}}catch(e){return{valid:!1,error:String(e)}}};function si(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function li(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ci(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ui(e){var t=e.block,n=e.left,r=e.lexeme,o=e.right,i=e.value,a={subType:r.subType,type:r.type,value:r.present?r.present(e):i};return t&&(a.block=ui(t)),n&&(a.left=ui(n)),o&&(a.right=ui(o)),a}var pi,di=function(){function e(t,n){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){return e};si(this,e),ci(this,"lexicon",void 0),ci(this,"query",void 0),ci(this,"lexerResult",void 0),ci(this,"syntaxerResult",void 0),ci(this,"evaluate",(function(e){if(!r.isValid){var t="DataTable filtering syntax is invalid for query: ".concat(r.query);throw s.ZP.error(t),new Error(t)}return!(r.tree&&r.tree.lexeme&&r.tree.lexeme.evaluate)||r.tree.lexeme.evaluate(e,r.tree)})),ci(this,"filter",(function(e){return e.filter(r.evaluate)})),this.lexicon=t,this.query=n,this.lexerResult=o(G(this.lexicon,this.query)),this.syntaxerResult=ai(this.lexerResult)}var t,n;return t=e,(n=[{key:"isValid",get:function(){return this.syntaxerResult.valid}},{key:"tree",get:function(){return this.syntaxerResult.tree}},{key:"toQueryString",value:function(){return this.lexerResult.valid?i.UID((function(e){return e.lexeme.transform?e.lexeme.transform(e.value):e.value}),this.lexerResult.lexemes).join(" "):""}},{key:"toStructure",value:function(){return this.isValid&&this.syntaxerResult.tree?ui(this.syntaxerResult.tree):null}}])&&li(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),fi=/^{(([^{}\\]|\\.)+)}/,hi=/^(('([^'\\]|\\.)*')|("([^"\\]|\\.)*")|(`([^`\\]|\\.)*`))/,Ai=function(e){return e.slice(1,e.length-1).replace(/\\(.)/g,"$1")},bi={present:function(e){return Ai(e.value)},resolve:function(e,t){if(fi.test(t.value))return e[Ai(t.value)];throw new Error},regexp:fi,subType:"field",type:Xo.Expression},vi=function(e){return e.slice(1,e.length-1).replace(/\\(.)/g,"$1")},gi={present:function(e){return vi(e.value)},resolve:function(e,t){if(hi.test(t.value))return vi(t.value);throw new Error},regexp:hi,subType:"value",type:Xo.Expression},yi=function(e,t){var n=function(e){return function(t){return t=t.match(e)[1],he()(t)?+t:t.replace(/\\(.)/g,"$1")}}(e);return{present:function(e){return n(e.value)},resolve:function(t,r){if(e.test(r.value))return n(r.value);throw new Error},regexp:e,regexpMatch:1,subType:"value",transform:t,type:Xo.Expression}},mi=yi(/^(([^\s'"`{}()\\]|\\.)+)(?:[\s)]|$)/),wi=yi(/^(([^'"`{}()\\]|\\.)+)$/,(function(e){return"string"==typeof e&&-1!==e.indexOf(" ")?'"'.concat(e,'"'):e}));!function(e){e.And="&&",e.Or="||"}(pi||(pi={}));var Ei,Ci={evaluate:function(e,t){s.ZP.trace("evaluate -> &&",e,t);var n=t,r=n.left.lexeme.evaluate(e,n.left),o=n.right.lexeme.evaluate(e,n.right);return r&&o},type:Xo.LogicalOperator,priority:2,regexp:/^(and\s|&&)/i,subType:pi.And,syntaxer:function(e,t,n){return Object.assign({left:e.slice(0,n),right:e.slice(n+1)},t)}},ki={evaluate:function(e,t){s.ZP.trace("evaluate -> ||",e,t);var n=t;return n.left.lexeme.evaluate(e,n.left)||n.right.lexeme.evaluate(e,n.right)},type:Xo.LogicalOperator,subType:pi.Or,priority:3,regexp:/^(or\s|\|\|)/i,syntaxer:function(e,t,n){return Object.assign({left:e.slice(0,n),right:e.slice(n+1)},t)}};function xi(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Oi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Oi(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Si(e){return function(t,n){return e(function(e,t){s.ZP.trace("evaluate -> relational",e,t);var n=t,r=n.left.lexeme.resolve(e,n.left),o=n.right.lexeme.resolve(e,n.right);return s.ZP.trace("opValue: ".concat(r,", expValue: ").concat(o)),[r,o,t.value]}(t,n))}}!function(e){e.Contains="contains",e.DateStartsWith="datestartswith",e.Equal="=",e.GreaterOrEqual=">=",e.GreaterThan=">",e.LessOrEqual="<=",e.LessThan="<",e.NotEqual="!="}(Ei||(Ei={}));var Bi,_i={priority:0,syntaxer:function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return Object.assign({left:n,right:o},r)},type:Xo.RelationalOperator},ji=function(e,t,n,r){return"i"==r[0]?e(t.toString().toUpperCase(),n.toString().toUpperCase()):e(t,n)},Pi=i.ATH({evaluate:Si((function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return!i.kKJ(r)&&!i.kKJ(n)&&("String"===i.dt8(r)||"String"===i.dt8(n))&&function(e,t,n){return"i"==n[0]?-1!==e.toString().toUpperCase().indexOf(t.toString().toUpperCase()):-1!==e.toString().indexOf(t.toString())}(n,r,o)})),subType:Ei.Contains,regexp:/^((i|s)?contains)(?=\s|$)/i,regexpFlags:2,regexpMatch:1},_i),Di=i.ATH({evaluate:Si((function(e){var t,n,r,o=xi(e,3);return t=o[0],n=o[1],r=o[2],he()(t)&&he()(n)?+t==+n:"i"==r[0]?t.toString().toUpperCase()===n.toString().toUpperCase():t===n})),subType:Ei.Equal,regexp:/^((i|s)?(=|(eq)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},_i),Ii=i.ATH({evaluate:Si((function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return ji((function(e,t){return e>=t}),n,r,o)})),subType:Ei.GreaterOrEqual,regexp:/^((i|s)?(>=|(ge)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},_i),Fi=i.ATH({evaluate:Si((function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return ji((function(e,t){return e>t}),n,r,o)})),subType:Ei.GreaterThan,regexp:/^((i|s)?(>|(gt)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},_i),Ti={allow_YY:!0},Mi=i.ATH({evaluate:Si((function(e){var t=xi(e,2),n=t[0],r=t[1];n="number"==typeof n?n.toString():n,r="number"==typeof r?r.toString():r;var o=Se(n,Ti),a=Se(r,Ti);return!i.kKJ(o)&&!i.kKJ(a)&&0===o.indexOf(a)})),subType:Ei.DateStartsWith,regexp:/^((datestartswith)(?=\s|$))/i,regexpMatch:1},_i),Ri=i.ATH({evaluate:Si((function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return ji((function(e,t){return e<=t}),n,r,o)})),subType:Ei.LessOrEqual,regexp:/^((i|s)?(<=|(le)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},_i),zi=i.ATH({evaluate:Si((function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return ji((function(e,t){return e<t}),n,r,o)})),subType:Ei.LessThan,regexp:/^((i|s)?(<|(lt)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},_i),Ni=i.ATH({evaluate:Si((function(e){var t=xi(e,3),n=t[0],r=t[1],o=t[2];return ji((function(e,t){return e!==t}),n,r,o)})),subType:Ei.NotEqual,regexp:/^((i|s)?(!=|(ne)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},_i);function Li(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function qi(e){return function(t,n){return e(function(e,t){s.ZP.trace("evaluate -> unary",e,t),s.ZP.trace("evaluate -> unary",e,t);var n=t;return n.left.lexeme.resolve(e,n.left)}(t,n))}}!function(e){e.Not="!"}(Bi||(Bi={}));var Wi={present:function(e){return e.value},priority:0,syntaxer:function(e){var t=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Li(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Li(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,2),n=t[0],r=t[1];return Object.assign({left:n},r)},type:Xo.UnaryOperator},Ui={evaluate:function(e,t){s.ZP.trace("evaluate -> unary not",e,t);var n=t;return!n.right.lexeme.evaluate(e,n.right)},type:Xo.UnaryOperator,subType:Bi.Not,priority:1.5,regexp:/^!/,syntaxer:function(e){return Object.assign({right:e.slice(1,e.length)},e[0])}},Vi=i.ATH({evaluate:qi((function(e){return"boolean"==typeof e})),regexp:/^(is bool)/i},Wi),Hi=i.ATH({evaluate:qi((function(e){return"number"==typeof e&&e%2==0})),regexp:/^(is even)/i},Wi),Yi=i.ATH({evaluate:qi((function(e){return null==e||""===e})),regexp:/^(is blank)/i},Wi),Ki=i.ATH({evaluate:qi((function(e){return null==e})),regexp:/^(is nil)/i},Wi),Zi=i.ATH({evaluate:qi((function(e){return"number"==typeof e})),regexp:/^(is num)/i},Wi),Qi=i.ATH({evaluate:qi((function(e){return null!==e&&"object"==typeof e})),regexp:/^(is object)/i},Wi),$i=i.ATH({evaluate:qi((function(e){return"number"==typeof e&&e%2==1})),regexp:/^(is odd)/i},Wi),Gi=i.ATH({evaluate:qi((function(e){return"number"==typeof e&&function(e){if(2===e)return!0;if(e<2||e%2==0)return!1;for(var t=3;t*t<=e;t+=2)if(e%t==0)return!1;return!0}(e)})),regexp:/^(is prime)/i},Wi),Ji=i.ATH({evaluate:qi((function(e){return"string"==typeof e})),regexp:/^(is str)/i},Wi),Xi=i.u4g((function(e,t){return e+(t.lexeme.nesting||0)})),ea=function(e,t){return 0===Xi(0,e)},ta=function(e,t){return ea(e)&&!!t&&i.q9t(t.lexeme.type,[Xo.RelationalOperator])},na=function(e,t){return!t||i.q9t(t.lexeme.type,[Xo.BlockOpen,Xo.LogicalOperator,Xo.RelationalOperator])},ra=function(e,t){return!t},oa=function(e,t){return!!t&&i.q9t(t.lexeme.type,[Xo.BlockClose,Xo.Expression,Xo.UnaryOperator])},ia=function(e,t){return!!t&&i.q9t(t.lexeme.type,[Xo.Expression])},aa=ia;function sa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function la(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?sa(Object(n),!0).forEach((function(t){ca(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ca(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ua(e){return function(e){if(Array.isArray(e))return pa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return pa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pa(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function da(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function fa(){return fa="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=ha(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}},fa.apply(this,arguments)}function ha(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=va(e)););return e}function Aa(e,t){return Aa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Aa(e,t)}function ba(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function va(e){return va=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},va(e)}var ga=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Aa(e,t)}(s,e);var t,n,r,o,a=(r=s,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=va(r);if(o){var n=va(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return ba(this,e)});function s(e,t){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),a.call(this,function(e){return[la(la({},e===_.J2.And?Ci:ki),{},{if:oa,terminal:!1})].concat(ua([Pi,Mi,Di,Ii,Fi,Ri,zi,Ni].map((function(e){return la(la({},e),{},{if:ia,terminal:!1})}))),ua([Yi,Vi,Hi,Ki,Zi,Qi,$i,Gi,Ji].map((function(e){return la(la({},e),{},{if:aa,terminal:!0})}))),ua([bi,gi,mi].map((function(e){return la(la({},e),{},{if:na,terminal:ta})}))))}(t),e)}return t=s,n=[{key:"isValid",get:function(){return fa(va(s.prototype),"isValid",this)&&this.respectsBasicSyntax()}},{key:"statements",get:function(){if(this.syntaxerResult.tree){for(var e=[],t=[this.syntaxerResult.tree];t.length;){var n=t.pop();n&&(e.push(n),n.left&&t.push(n.left),n.block&&t.push(n.block),n.right&&t.push(n.right))}return e}}},{key:"respectsBasicSyntax",value:function(){var e=i.UID((function(e){return e.value}),i.hXT((function(e){return e.lexeme.type===Xo.Expression&&"field"===e.lexeme.subType}),this.lexerResult.lexemes)),t=i.jj$(e);return e.length===t.length}}],n&&da(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(di),ya={nesting:-1,regexp:/^\)/,type:Xo.BlockClose},ma={evaluate:function(e,t){s.ZP.trace("evaluate -> ()",e,t);var n=t;return n.block.lexeme.evaluate(e,n.block)},type:Xo.BlockOpen,nesting:1,subType:"()",priority:1,regexp:/^\(/,syntaxer:function(e){return Object.assign({block:e.slice(1,e.length-1)},e[0])}};function wa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ea(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wa(Object(n),!0).forEach((function(t){Ca(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ca(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ka(e){return function(e){if(Array.isArray(e))return xa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return xa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?xa(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Oa=[].concat(ka([Ci,ki].map((function(e){return Ea(Ea({},e),{},{if:oa,terminal:!1})}))),[Ea(Ea({},ya),{},{if:function(e,t){return!!t&&i.q9t(t.lexeme.type,[Xo.BlockClose,Xo.BlockOpen,Xo.Expression,Xo.UnaryOperator])&&Xi(0,e)>0},terminal:ea}),Ea(Ea({},ma),{},{if:function(e,t){return!t||i.q9t(t.lexeme.type,[Xo.BlockOpen,Xo.LogicalOperator,Xo.UnaryOperator])},terminal:!1})],ka([Pi,Mi,Di,Ii,Fi,Ri,zi,Ni].map((function(e){return Ea(Ea({},e),{},{if:ia,terminal:!1})}))),ka([Yi,Vi,Hi,Ki,Zi,Qi,$i,Gi,Ji].map((function(e){return Ea(Ea({},e),{},{if:aa,terminal:ea})}))),[Ea(Ea({},Ui),{},{if:function(e,t){return!t||i.q9t(t.lexeme.type,[Xo.LogicalOperator,Xo.UnaryOperator])},terminal:!1})],ka([bi,gi,mi].map((function(e){return Ea(Ea({},e),{},{if:na,terminal:ta})}))));function Sa(e,t){return Sa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Sa(e,t)}function Ba(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function _a(e){return _a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_a(e)}var ja=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Sa(e,t)}(i,e);var t,n,r,o=(n=i,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=_a(n);if(r){var o=_a(this).constructor;e=Reflect.construct(t,arguments,o)}else e=t.apply(this,arguments);return Ba(this,e)});function i(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,Oa,e)}return t=i,Object.defineProperty(t,"prototype",{writable:!1}),t}(di);function Pa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Da(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pa(Object(n),!0).forEach((function(t){Ia(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ia(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fa(e){return function(e){if(Array.isArray(e))return Ta(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Ta(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ta(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ta(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ma=[].concat(Fa([Pi,Mi,Di,Ii,Fi,Ri,zi,Ni].map((function(e){return Da(Da({},e),{},{if:ra,terminal:!1})}))),Fa([Yi,Vi,Hi,Ki,Zi,Qi,$i,Gi,Ji].map((function(e){return Da(Da({},e),{},{if:ra,terminal:!0})}))),Fa([bi,wi,gi].map((function(e){return Da(Da({},e),{},{if:na,terminal:!0})}))));function Ra(e,t){return Ra=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ra(e,t)}function za(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Na(e){return Na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Na(e)}function La(e){return function(e){if(Array.isArray(e))return qa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return qa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qa(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Wa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ua(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wa(Object(n),!0).forEach((function(t){Va(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Va(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ha=[Ei.Contains,Ei.Equal,Ei.GreaterOrEqual,Ei.GreaterThan,Ei.LessOrEqual,Ei.LessThan,Ei.NotEqual];function Ya(e,t){var n=i.kKJ(e)?"":e.case===_.oN.Insensitive?"i":"s";return t.lexeme.type===Xo.RelationalOperator&&t.lexeme.subType&&-1!==Ha.indexOf(t.lexeme.subType)&&t.value&&-1===["i","s"].indexOf(t.value[0])?Ua(Ua({},t),{},{value:"".concat(n).concat(t.value)}):t}function Ka(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_.QD.Any,n=i.kKJ(e)?"":e.case===_.oN.Insensitive?"i":"s";switch(t){case _.QD.Any:case _.QD.Text:return{lexeme:oi(Pi),value:"".concat(n).concat(Ei.Contains)};case _.QD.Datetime:return{lexeme:oi(Mi),value:Ei.DateStartsWith};case _.QD.Numeric:return{lexeme:oi(Di),value:"".concat(n).concat(Ei.Equal)}}}function Za(e,t){return t.valid?(2===t.lexemes.length?t.lexemes=[{lexeme:oi(bi),value:"{".concat(e.id,"}")},Ya(e.filter_options,t.lexemes[0]),t.lexemes[1]]:1!==(n=t.lexemes).length||n[0].lexeme.type!==Xo.UnaryOperator?function(e){return 1===e.length&&e[0].lexeme.type===Xo.Expression}(t.lexemes)&&(t.lexemes=[{lexeme:oi(bi),value:"{".concat(e.id,"}")},Ka(e.filter_options,e.type)].concat(La(t.lexemes))):t.lexemes=[{lexeme:oi(bi),value:"{".concat(e.id,"}")}].concat(La(t.lexemes)),t):t;var n}var Qa=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ra(e,t)}(i,e);var t,n,r,o=(n=i,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Na(n);if(r){var o=Na(this).constructor;e=Reflect.construct(t,arguments,o)}else e=t.apply(this,arguments);return za(this,e)});function i(e,t){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,Ma,e,Za.bind(void 0,t))}return t=i,Object.defineProperty(t,"prototype",{writable:!1}),t}(di);function $a(e,t){return!e||void 0===e.column_id||(Array.isArray(e.column_id)?i.q9t(t,e.column_id):e.column_id===t)}function Ga(e,t){if(!e||void 0===e.row_index)return!0;var n=e.row_index;return"string"==typeof n?t%2==("odd"===n?1:0):Array.isArray(n)?i.q9t(t,n):t===n}var Ja=function(e,t,n,r,o){return i.hXT((function(i){return!i.checksHeaderRow()&&i.matchesActive(r)&&i.matchesSelected(o)&&i.matchesDataRow(t)&&i.matchesColumn(n)&&i.matchesFilter(e)}))},Xa=function(e){return i.hXT((function(t){return!t.checksState()&&!t.checksDataRow()&&!t.checksHeaderRow()&&t.matchesColumn(e)}))},es=function(e,t){return i.hXT((function(n){return!n.checksState()&&!n.checksDataRow()&&n.matchesHeaderRow(e)&&n.matchesColumn(t)}))},ts=function(e,t){return i.hXT((function(n){return!n.checksState()&&!n.checksColumn()&&!n.checksHeaderRow()&&n.matchesDataRow(t)&&n.matchesFilter(e)}))},ns=i.hXT((function(e){return!(e.checksState()||e.checksDataRow()||e.checksHeaderRow()||e.checksColumn())})),rs=function(e){return i.hXT((function(t){return!t.checksDataRow()&&!t.checksState()&&!t.checksColumn()&&t.matchesHeaderRow(e)}))};function os(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function is(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function as(e,t,n){return t&&is(e.prototype,t),n&&is(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ss(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ls=["borderBottom","borderLeft","borderRight","borderTop"],cs=i.hXT((function(e){return 0===e.indexOf("border")}),wo),us=function(){function e(t,n,r){var o=this;if(os(this,e),ss(this,"weights",void 0),ss(this,"edges",void 0),ss(this,"rows",void 0),ss(this,"columns",void 0),ss(this,"defaultEdge",void 0),ss(this,"getEdge",(function(e,t){return o.edges[e][t]})),ss(this,"getEdges",(function(){return o.edges})),ss(this,"getWeight",(function(e,t){return o.weights[e][t]})),ss(this,"isDefault",(function(e,t){return o.weights[e][t]===-1/0})),ss(this,"clone",(function(){return new e(o)})),"number"==typeof t&&void 0!==n){var a=t;this.rows=a,this.columns=n,this.defaultEdge=r,this.weights=i.UID((function(){return new Array(n).fill(-1/0)}),i.w6H(0,a)),this.edges=i.UID((function(){return new Array(n).fill(r)}),i.w6H(0,a))}else{var s=t;this.rows=s.rows,this.columns=s.columns,this.defaultEdge=s.defaultEdge,this.weights=He(s.weights),this.edges=He(s.edges)}}return as(e,[{key:"setEdge",value:function(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];e<0||t<0||e>=this.rows||t>=this.columns||!o&&(i.kKJ(n)||r<=this.weights[e][t])||(this.weights[e][t]=r,this.edges[e][t]=n)}}]),e}(),ps=function(){function e(t,n,r,o,a){var s=this;if(os(this,e),ss(this,"horizontal",void 0),ss(this,"vertical",void 0),ss(this,"horizontalEdges",void 0),ss(this,"verticalEdges",void 0),ss(this,"rows",void 0),ss(this,"columns",void 0),ss(this,"defaultEdge",void 0),ss(this,"getEdges",(function(){return{horizontal:s.horizontal.getEdges(),vertical:s.vertical.getEdges()}})),ss(this,"getMatrices",(function(){return{horizontal:s.horizontal,vertical:s.vertical}})),ss(this,"getStyle",(function(e,t){return{borderBottom:s.horizontal.getEdge(e+1,t)||null,borderTop:s.horizontal.getEdge(e,t)||null,borderLeft:s.vertical.getEdge(e,t)||null,borderRight:s.vertical.getEdge(e,t+1)||null}})),ss(this,"clone",(function(){return new e(s)})),"number"==typeof t&&void 0!==n){var l=t;this.rows=l,this.columns=n,this.defaultEdge=r,this.horizontalEdges=i.kKJ(o)||o,this.verticalEdges=i.kKJ(a)||a,this.horizontal=new us(l+1,n,this.horizontalEdges?r:void 0),this.vertical=new us(l,n+1,this.verticalEdges?r:void 0)}else{var c=t;this.rows=c.rows,this.columns=c.columns,this.defaultEdge=c.defaultEdge,this.horizontal=c.horizontal.clone(),this.vertical=c.vertical.clone(),this.horizontalEdges=c.horizontalEdges,this.verticalEdges=c.verticalEdges}}return as(e,[{key:"setEdges",value:function(e,t,n){this.horizontalEdges&&(n.borderTop&&this.horizontal.setEdge(e,t,n.borderTop[0],n.borderTop[1]),n.borderBottom&&this.horizontal.setEdge(e+1,t,n.borderBottom[0],n.borderBottom[1])),this.verticalEdges&&(n.borderLeft&&this.vertical.setEdge(e,t,n.borderLeft[0],n.borderLeft[1]),n.borderRight&&this.vertical.setEdge(e,t+1,n.borderRight[0],n.borderRight[1]))}}]),e}();function ds(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function fs(e){var t;return{checksColumn:function(){return!(i.kKJ(e.if)||i.kKJ(e.if.column_id)&&i.kKJ(e.if.column_type)&&i.kKJ(e.if.column_editable))},checksFilter:function(){return!i.kKJ(e.if)&&!i.kKJ(e.if.filter_query)},checksDataRow:function(){return!i.kKJ(e.if)&&!i.kKJ(e.if.row_index)},checksHeaderRow:function(){return!i.kKJ(e.if)&&!i.kKJ(e.if.header_index)},checksState:function(){var t;return!i.kKJ(null===(t=e.if)||void 0===t?void 0:t.state)},checksStateActive:function(){var t;return"active"===(null===(t=e.if)||void 0===t?void 0:t.state)},checksStateSelected:function(){var t;return"selected"===(null===(t=e.if)||void 0===t?void 0:t.state)},matchesActive:function(t){return function(e,t){return"active"!==(null==e?void 0:e.state)||t}(e.if,t)},matchesColumn:function(t){return!e.if||!i.kKJ(t)&&$a(e.if,t&&t.id)&&(n=e.if,r=t&&t.type,!n||void 0===n.column_type||n.column_type===(r||_.QD.Any))&&function(e,t){return!e||void 0===e.column_editable||t===e.column_editable}(e.if,t&&t.editable);var n,r},matchesFilter:function(n){return!e.if||void 0===e.if.filter_query||(t=t||new ja(e.if.filter_query)).evaluate(n)},matchesDataRow:function(t){return Ga(e.if,t)},matchesHeaderRow:function(t){return function(e,t){if(!e||void 0===e.header_index)return!0;var n=e.header_index;return"string"==typeof n?t%2==("odd"===n?1:0):Array.isArray(n)?i.q9t(t,n):t===n}(e.if,t)},matchesSelected:function(t){return function(e,t){return"selected"!==(null==e?void 0:e.state)||t}(e.if,t)},style:hs(e)}}function hs(e){return i.u4g((function(e,t){var n=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ds(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ds(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t,2),r=n[0],o=n[1];return mo.has(r)&&(e[mo.get(r)]=o),e}),{},i.Zpf(e))}var As=(0,a.Pi)((function(e,t,n,r){return i.UWY([e?[fs(e)]:[],i.UID(fs,n||[]),t?[fs(t)]:[],i.UID(fs,r||[])])})),bs=(0,a.Pi)((function(e,t,n,r){return i.UWY([e?[fs(e)]:[],i.UID(fs,n||[]),t?[fs(t)]:[],i.UID(fs,r||[])])})),vs=(0,a.Pi)((function(e,t,n,r){return i.UWY([e?[fs(e)]:[],i.UID(fs,n||[]),t?[fs(t)]:[],i.UID(fs,r||[])])})),gs=(0,a.Pi)((function(e,t){return[hs(e),hs(t)]}));function ys(e){for(var t={},n=0;n<e.length;++n)Object.assign(t,e[n].style);return i.CEd(cs,t)}var ms=function(e,t,n,r,o){return function(i){return ys(Ja(e,t,n,r,o)(i))}},ws=n(3419);function Es(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Cs(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Es(Object(n),!0).forEach((function(t){ks(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Es(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ks(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var xs={backgroundColor:ws.Z.supportsCssVariables?"var(--selected-background)":"rgba(255, 65, 54, 0.2)"},Os=(0,a.Pi)((function(e,t,n,r){return Ke(n,e,(function(e,n,o){return ms(e,o+r.rows,n,!1,!1)(t)}))})),Ss=(0,a.Pi)((function(e,t,n,r,o,a,s){e=He(e);var l=s.length?s:a?[a]:[],c=n.filter((function(e){return!e.checksState()})),u=n.filter((function(e){return e.checksStateSelected()})),p=n.filter((function(e){return e.checksStateActive()}));return i.Ed_((function(n){var i=n.row,s=n.column,l=i-o.rows,d=s-o.columns;if(!(l<0||d<0||e.length<=l||e[l].length<=d)){var f=st(a,i,s),h=Cs(Cs(Cs(Cs({},ms(r[i],i+o.rows,t[s],f,!0)(c)),xs),ms(r[i],i+o.rows,t[s],f,!0)(u)),ms(r[i],i+o.rows,t[s],f,!0)(p));e[l][d]=h}}),l),e})),Bs=(0,a.Pi)((function(e,t,n,r){return Ke(n,i.w6H(0,e),(function(e,n,o){return function(e,t){return function(n){return ys(ts(e,t)(n))}}(e,o+r.rows)(t)}))}));function _s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function js(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ps(e,t,n){return t&&js(e.prototype,t),n&&js(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ds(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Is=i.h0F(i.UID),Fs=function(){return(new Ts).get},Ts=Ps((function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Ds(this,"get",(0,a.qe)((function(e,n,r,o,a,s){return Is((function(n,l){return i.UID((function(e){var i=r[l],c=s&&s.length>i&&s[i]&&s[i][e.id]||a[e.id];return t.dropdown.get(e.id,l)(c,o,e,n)}),e)}),n)}))),Ds(this,"dropdown",Ge()((function(e,n,r,o){var a=i.dFj((function(e){var n=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return _s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,2),a=n[0],s=n[1];return $a(a.if,r.id)&&(i.kKJ(a.if)||i.kKJ(a.if.filter_query)||t.evaluation.get(r.id,s)(t.ast.get(r.id,s)(a.if.filter_query),o))}),i.h0F(i.UID)((function(e,t){return[e,t]}),n));return a&&a[0]||e||void 0}))),Ds(this,"ast",Ge()((function(e){return new ja(e)}))),Ds(this,"evaluation",Ge()((function(e,t){return e.evaluate(t)})))}));function Ms(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Rs(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function zs(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ns=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Yo(t),s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Fs(),l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ei(),c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:Os(),u=arguments.length>5&&void 0!==arguments[5]?arguments[5]:Ss(),p=arguments.length>6&&void 0!==arguments[6]?arguments[6]:Bs(),d=arguments.length>7&&void 0!==arguments[7]?arguments[7]:Ot(t),f=arguments.length>8&&void 0!==arguments[8]?arguments[8]:As();Ms(this,e),zs(this,"propsFn",void 0),zs(this,"cellContents",void 0),zs(this,"cellDropdowns",void 0),zs(this,"cellOperations",void 0),zs(this,"dataPartialStyles",void 0),zs(this,"dataStyles",void 0),zs(this,"dataOpStyles",void 0),zs(this,"cellWrappers",void 0),zs(this,"relevantStyles",void 0),zs(this,"getMarkdown",(0,a.qe)((function(e){return new _o(e)}))),zs(this,"getCells",(0,a.qe)((function(e,t){return Q(e,t,(function(e,t){return e.length?e.concat(t):t}))}))),zs(this,"getDataOpCell",Ge()((function(e,t,n,r,a,s){return o().cloneElement(e,{style:i.Jnq([{borderBottom:n,borderLeft:r,borderRight:a,borderTop:s},t,e.props.style])})}))),zs(this,"getDataOpCells",(0,a.qe)((function(e,t,r){return Ze(e,t,(function(e,t,o,i){var a=r&&r.getStyle(o,i);return n.getDataOpCell.get(o,i)(e,t,a&&a.borderBottom,a&&a.borderLeft,a&&a.borderRight,a&&a.borderTop)}))}))),zs(this,"getDataCell",Ge()((function(e,t,n,r,a,s,l){return o().cloneElement(e,{children:[t],style:i.ATH(n||{},{borderBottom:r,borderLeft:a,borderRight:s,borderTop:l})})}))),zs(this,"getDataCells",(0,a.qe)((function(e,t,r,o){return Qe(e,r,t,(function(e,t,r,i,a){var s=o&&o.getStyle(i,a);return n.getDataCell.get(i,a)(e,r,t,s&&s.borderBottom,s&&s.borderLeft,s&&s.borderRight,s&&s.borderTop)}))}))),this.propsFn=t,this.cellContents=r,this.cellDropdowns=s,this.cellOperations=l,this.dataPartialStyles=c,this.dataStyles=u,this.dataOpStyles=p,this.cellWrappers=d,this.relevantStyles=f}var t,n;return t=e,n=[{key:"props",get:function(){return this.propsFn()}},{key:"createCells",value:function(e,t){var n=this.props,r=n.active_cell,o=n.applyFocus,i=n.dropdown_conditional,a=n.dropdown,s=n.data,l=n.dropdown_data,c=n.id,u=n.is_focused,p=n.loading_state,d=n.markdown_options,f=n.row_deletable,h=n.row_selectable,A=n.selected_cells,b=n.selected_rows,v=n.setProps,g=n.style_cell,y=n.style_cell_conditional,m=n.style_data,w=n.style_data_conditional,E=n.virtualized,C=n.visibleColumns,k=this.relevantStyles(g,m,y,w),x=this.dataPartialStyles(C,k,E.data,E.offset),O=this.dataStyles(x,C,k,E.data,E.offset,r,A),S=this.dataOpStyles((h?1:0)+(f?1:0),k,E.data,E.offset),B=this.cellDropdowns(C,E.data,E.indices,i,a,l),_=this.cellOperations(c,s,E.data,E.indices,h,f,b,v),j=this.cellWrappers.partialGet(C,E.data,E.offset),P=this.cellWrappers.get(j,E.offset,r,A),D=this.getMarkdown(d),I=this.cellContents.partialGet(C,E.data,E.offset,!!u,B,p,D),F=this.cellContents.get(I,r,o||!1,C,E.data,E.offset,!!u,B,p,D),T=this.getDataOpCells(_,S,t),M=this.getDataCells(P,F,O,e);return this.getCells(T,M)}}],n&&Rs(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ls(e,t){var n={};return Ye(e,ls,(function(e,r,o){var i=e.style[r]||e.style.border;i&&(n[r]=[i,null!=t?t:o])})),n}var qs=function(e,t,n,r,o,i){return function(a){return Ls(Ja(e,t,n,r,o)(a),i)}};function Ws(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Us(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ws(Object(n),!0).forEach((function(t){Vs(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ws(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vs(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Hs=Number.MAX_SAFE_INTEGER,Ys=Number.MAX_SAFE_INTEGER-1,Ks=(0,a.Pi)((function(e,t,n,r,o){if(0!==n.length&&0!==e.length){var i=new ps(n.length,e.length,ws.Z.defaultEdge,!0,!o);return Ye(n,e,(function(e,n,o,a){return i.setEdges(o,a,qs(e,o+r.rows,n,!1,!1)(t))})),i}})),Zs=(0,a.Pi)((function(e,t,n,r,o,a,s){if(!e)return e;var l=e.clone(),c=s.length?s:a?[a]:[],u=n.filter((function(e){return!e.checksState()})),p=n.filter((function(e){return e.checksStateSelected()})),d=n.filter((function(e){return e.checksStateActive()}));return i.Ed_((function(e){var n=e.row,i=e.column,s=n-o.rows,c=i-o.columns;if(!(s<0||c<0||r.length<=s)){var f=st(a,n,i),h=f?Hs:Ys,A=f?ws.Z.activeEdge:ws.Z.defaultEdge,b=Us(Us(Us({},qs(r[s],s,t[i],f,!0,h)(u)),{},{borderBottom:[A,h],borderLeft:[A,h],borderRight:[A,h],borderTop:[A,h]},qs(r[s],s,t[i],f,!0,h)(p)),qs(r[s],s,t[i],f,!0,h)(d));l.setEdges(s,i,b)}}),c),l})),Qs=(0,a.Pi)((function(e,t,n,r,o){if(0!==n.length&&0!==e){var a=new ps(n.length,e,ws.Z.defaultEdge,!0,!o);return Ye(n,i.w6H(0,e),(function(e,n,o,i){return a.setEdges(o,i,function(e,t){return function(n){return Ls(ts(e,t)(n))}}(e,o+r.rows)(t))})),a}})),$s=(0,a.Pi)((function(e,t,n,r,o){if(t&&0!==e.length){var a=new ps(1,e.length,ws.Z.defaultEdge,!0,!o);return Ye(i.w6H(0,1),e,(function(e,t,o,i){a.setEdges(o,i,function(e){return function(t){return Ls(Xa(e)(t))}}(t)(r));var s=n.get(t.id.toString());s&&!s.isValid&&a.setEdges(o,i,{borderBottom:[ws.Z.activeEdge,1/0],borderLeft:[ws.Z.activeEdge,1/0],borderRight:[ws.Z.activeEdge,1/0],borderTop:[ws.Z.activeEdge,1/0]})})),a}})),Gs=(0,a.Pi)((function(e,t,n,r){if(t&&0!==e){var o=new ps(1,e,ws.Z.defaultEdge,!0,!r);return Ye(i.w6H(0,1),i.w6H(0,e),(function(e,t){return o.setEdges(e,t,function(e){return Ls(ns(e))}(n))})),o}})),Js=(0,a.Pi)((function(e,t,n,r){if(0!==t&&0!==e.length){var o=new ps(t,e.length,ws.Z.defaultEdge,!0,!r);return Ye(i.w6H(0,t),e,(function(e,t,r,i){return o.setEdges(r,i,function(e,t){return function(n){return Ls(es(e,t)(n))}}(r,t)(n))})),o}})),Xs=(0,a.Pi)((function(e,t,n,r){if(0!==t&&0!==e){var o=new ps(t,e,ws.Z.defaultEdge,!0,!r);return Ye(i.w6H(0,t),i.w6H(0,e),(function(e,t){return o.setEdges(e,t,function(e){return function(t){return Ls(rs(e)(t))}}(e)(n))})),o}}));function el(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function tl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var nl=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),tl(this,"propsFn",void 0),tl(this,"dataStyles",As()),tl(this,"filterStyles",bs()),tl(this,"headerStyles",vs()),tl(this,"getPartialDataEdges",Ks()),tl(this,"getDataEdges",Zs()),tl(this,"getDataOpEdges",Qs()),tl(this,"getFilterEdges",$s()),tl(this,"getFilterOpEdges",Gs()),tl(this,"getHeaderEdges",Js()),tl(this,"getHeaderOpEdges",Xs()),tl(this,"memoizedCreateEdges",(0,a.qe)((function(t,r,o,i,a,s,l,c,u,p,d,f,h,A,b,v,g,y,w,E){var C=n.dataStyles(d,h,f,A),k=n.filterStyles(d,b,f,v),x=n.headerStyles(d,g,f,y),O=m(r),S=n.getPartialDataEdges(o,C,w,E,p),B=n.getDataEdges(S,o,C,w,E,t,u),_=n.getDataOpEdges(i,C,w,E,p),j=n.getFilterEdges(o,a,s,k,p),P=n.getFilterOpEdges(i,a,k,p),D=n.getHeaderEdges(o,O,x,p),I=n.getHeaderOpEdges(i,O,x,p),F=(d?1:0)+f.length-1;return D=e.clone(D),I=e.clone(I),j=e.clone(j),P=e.clone(P),B=e.clone(B),_=e.clone(_),n.hReconcile(D,j||B,F),n.hReconcile(I,P||_,F),n.hReconcile(j,B,F),n.hReconcile(P,_,F),n.vReconcile(I,D,F),n.vReconcile(P,j,F),n.vReconcile(_,B,F),c===O?a?(n.hOverride(D,j,F),n.hOverride(I,P,F)):(n.hOverride(D,B,F),n.hOverride(I,_,F)):a&&c===O+1&&(n.hOverride(j,B,F),n.hOverride(P,_,F)),l===i&&(n.vOverride(I,D,F),n.vOverride(P,j,F),n.vOverride(_,B,F)),{dataEdges:B,dataOpEdges:_,filterEdges:j,filterOpEdges:P,headerEdges:D,headerOpEdges:I}}))),this.propsFn=t}var t,n,r;return t=e,n=[{key:"hOverride",value:function(t,n,r){if(t&&n){var o=t.getMatrices().horizontal,a=n.getMatrices().horizontal,s=o.rows-1;i.Ed_((function(t){e.hasPrecedence(o.getWeight(s,t),a.getWeight(0,t),r)&&a.setEdge(0,t,o.getEdge(s,t),1/0,!0),o.setEdge(s,t,"none",-1/0,!0)}),i.w6H(0,o.columns))}}},{key:"vOverride",value:function(t,n,r){if(t&&n){var o=t.getMatrices().vertical,a=n.getMatrices().vertical,s=o.columns-1;i.Ed_((function(t){e.hasPrecedence(o.getWeight(t,s),a.getWeight(t,0),r)&&a.setEdge(t,0,o.getEdge(t,s),1/0,!0),o.setEdge(t,s,"none",-1/0,!0)}),i.w6H(0,o.rows))}}},{key:"hReconcile",value:function(t,n,r){if(t&&n){var o=n.getMatrices().horizontal,a=t.getMatrices().horizontal,s=a.rows-1;isFinite(s)&&i.Ed_((function(t){return!e.hasPrecedence(a.getWeight(s,t),o.getWeight(0,t),r)&&a.setEdge(s,t,"none",-1/0,!0)}),i.w6H(0,a.columns))}}},{key:"vReconcile",value:function(t,n,r){if(t&&n){var o=n.getMatrices().vertical,a=t.getMatrices().vertical,s=a.columns-1;i.Ed_((function(t){return!e.hasPrecedence(a.getWeight(t,s),o.getWeight(t,0),r)&&a.setEdge(t,s,"none",-1/0,!0)}),i.w6H(0,a.rows))}}},{key:"props",get:function(){return this.propsFn()}},{key:"createEdges",value:function(){var e=this.props,t=e.active_cell,n=e.columns,r=e.filter_action,o=e.workFilter,i=e.fixed_columns,a=e.fixed_rows,s=e.row_deletable,l=e.row_selectable,c=e.selected_cells,u=e.style_as_list_view,p=e.style_cell,d=e.style_cell_conditional,f=e.style_data,h=e.style_data_conditional,A=e.style_filter,b=e.style_filter_conditional,v=e.style_header,g=e.style_header_conditional,y=e.virtualized,m=e.visibleColumns;return this.memoizedCreateEdges(t,n,m,(s?1:0)+(l?1:0),r.type!==_.p9.None,o.map,i,a,c,u,p,d,f,h,A,b,v,g,y.data,y.offset)}}],r=[{key:"clone",value:function(e){return e&&e.clone()}},{key:"hasPrecedence",value:function(e,t,n){return(t<=n||e===1/0)&&t<=e}}],n&&el(t.prototype,n),r&&el(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function rl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ol(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function il(e,t){return il=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},il(e,t)}function al(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return sl(e)}function sl(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ll(e){return ll=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ll(e)}function cl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ul=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&il(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ll(r);if(i){var n=ll(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return al(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),cl(sl(t=a.call(this,e)),"handleKeyDown",(function(e){var n=t.propsWithDefaults,r=n.stopPropagation,o=n.updateOnEnter;r&&e.stopPropagation(),o&&e.keyCode===h.ENTER&&t.submit()})),cl(sl(t),"handleChange",(function(e){t.setState({value:e.target.value})})),cl(sl(t),"submit",(function(){return t.state.value!==t.props.value&&t.props.submit(t.state.value)})),t.state={value:e.value},t}return t=s,n=[{key:"propsWithDefaults",get:function(){return this.props}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=this.props.value,n=e.value;t!==n&&this.setState({value:n})}},{key:"render",value:function(){var e=this.propsWithDefaults,t=e.onCopy,n=e.onPaste,r=e.placeholder,i=e.updateOnBlur,a=e.updateOnSubmit,s={onBlur:i?this.submit:void 0,onKeyDown:this.handleKeyDown,onSubmit:a?this.submit:void 0};return o().createElement("input",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?rl(Object(n),!0).forEach((function(t){cl(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):rl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({ref:"input",type:"text",value:this.state.value||"",onChange:this.handleChange,onCopy:t,onPaste:n,placeholder:r},s))}}],n&&ol(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent);cl(ul,"defaultProps",{stopPropagation:!1,updateOnEnter:!0,updateOnBlur:!0,updateOnSubmit:!0});var pl=function(e){var t=e.filterOptions,n=e.toggleFilterOptions;return o().createElement("input",{type:"button",className:"dash-filter--case ".concat(t.case===_.oN.Sensitive?"dash-filter--case--sensitive":"dash-filter--case--insensitive"),onClick:n,title:"Toggle filter case sensitivity",value:"Aa"})};function dl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function fl(e,t){return fl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},fl(e,t)}function hl(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Al(e)}function Al(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function bl(e){return bl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},bl(e)}var vl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fl(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=bl(r);if(i){var n=bl(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return hl(this,e)});function s(e){var t,n,r,o;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),n=Al(t=a.call(this,e)),o=function(e){(0,t.props.setFilter)({target:{value:e}})},(r="submit")in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,t.state={value:e.value},t}return t=s,n=[{key:"render",value:function(){var e=this.props,t=e.className,n=e.columnId,r=e.filterOptions,i=e.isValid,a=e.style,s=e.toggleFilterOptions,l=e.value;return o().createElement("th",{className:t+(i?"":" invalid"),"data-dash-column":n,style:a},o().createElement("div",null,o().createElement(ul,{onCopy:function(e){e.stopPropagation(),qe.clearClipboard()},onPaste:function(e){e.stopPropagation()},value:l,placeholder:r.placeholder_text,stopPropagation:!0,submit:this.submit}),o().createElement(pl,{filterOptions:r,toggleFilterOptions:s})))}}],n&&dl(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent),gl=(0,a.Pi)((function(e,t){return i.UID((function(e){return function(e){return function(t){return ys(Xa(e)(t))}}(e)(t)}),e)})),yl=(0,a.Pi)((function(e,t,n){return Ke(i.w6H(0,e),i.w6H(0,t),(function(){return function(e){return ys(ns(e))}(n)}))}));function ml(e){return function(e){if(Array.isArray(e))return wl(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return wl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?wl(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wl(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var El=(0,a.Pi)((function(e,t,n){return i.h0F(i.UID)((function(){return[].concat(ml(n?[o().createElement("th",{key:"delete",className:"expanded-row--empty-cell dash-delete-header",style:{width:"30px",maxWidth:"30px",minWidth:"30px"}})]:[]),ml(t?[o().createElement("th",{key:"select",className:"expanded-row--empty-cell dash-select-header",style:{width:"30px",maxWidth:"30px",minWidth:"30px"}})]:[]))}),i.w6H(0,e))})),Cl=function(e,t){return e===t?new Map(t):e},kl=(0,a.Pi)((function(e,t,n,r){var o=function(e,t){if(e.isValid){var n=new Map,r=e.statements;return r?(i.Ed_((function(e){if(e.lexeme.type===Xo.UnaryOperator&&e.left){var r=e.left.lexeme.present?e.left.lexeme.present(e.left):e.left.value,o=i.sEJ((function(e){return e.id.toString()===r}),t);if(!o)throw new Error("column ".concat(r," not found"));n.set(r,new Qa(e.value,o))}else if(e.lexeme.type===Xo.RelationalOperator&&e.left&&e.right){var a=e.left.lexeme.present?e.left.lexeme.present(e.left):e.left.value,s=i.sEJ((function(e){return e.id.toString()===a}),t);if(!s)throw new Error("column ".concat(a," not found"));e.lexeme.present&&e.lexeme.present(e)===Ei.Equal?n.set(a,new Qa("".concat(e.right.value),s)):n.set(a,new Qa("".concat(e.value," ").concat(e.right.value),s))}}),r),n):n}}(new ga(n,t),r);if(!o)return e;var a=e,s=i.jj$(i.zoF(Array.from(e.keys()),Array.from(o.keys())));return i.Ed_((function(t){var n=e.get(t),r=o.get(t);i.kKJ(r)?(a=Cl(a,e)).delete(t):(i.kKJ(n)||r.toQueryString()!==n.toQueryString())&&(a=Cl(a,e)).set(t,r)}),s),a}));function xl(e,t,n){var r=t.id.toString(),o=new Map(e);return n&&n.length?o.set(r,new Qa(n,t)):o.delete(r),o}function Ol(e,t,n){var r=Array.from(e.values()),o=function(e,t){return i.UID((function(e){return e.toQueryString()}),i.hXT((function(e){return Boolean(null==e?void 0:e.query)&&e.isValid}))(e)).join(" ".concat(t===_.J2.And?"&&":"||"," "))}(r,t);n(o,i.UID((function(e){return e.query}),i.hXT((function(e){return Boolean(null==e?void 0:e.query)}))(r)).join(t===_.J2.And?" && ":" || "),e)}var Sl=function(e,t,n,r,o){Ol(e=xl(e,t,r),n,o)};function Bl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var jl,Pl=[],Dl=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),_l(this,"propsFn",void 0),_l(this,"filterStyles",gl()),_l(this,"filterOpStyles",yl()),_l(this,"relevantStyles",bs()),_l(this,"headerOperations",El()),_l(this,"onChange",(function(e,t,n,r,o){s.ZP.debug("Filter -- onChange",e.id,o.target.value&&o.target.value.trim());var i=o.target.value.trim();Sl(t,e,n,i,r)})),_l(this,"onToggleChange",(function(e,t,n,r,o,i){var a=o(e);Sl(t,a,n,i,r)})),_l(this,"filter",Ge()((function(e,t,r,i,a,s){var l=r.get(e.id.toString());return o().createElement(vl,{key:"column-".concat(t),className:"dash-filter column-".concat(t),columnId:e.id,filterOptions:e.filter_options,isValid:!l||l.isValid,setFilter:n.onChange.bind(n,e,r,i,a),toggleFilterOptions:n.onToggleChange.bind(n,e,r,i,a).bind(n,s,l&&l.query),value:l&&l.query})}))),_l(this,"wrapperStyles",(0,a.qe)((function(e,t){return function(e,n){for(var r=e.length,o=new Array(r),a=0;a<r;++a)o[a]=(s=e[a],l=a,i.ATH(s,t&&t.getStyle(0,l)||{}));var s,l;return o}(e)}))),_l(this,"getCells",(0,a.qe)((function(e,t){return[e.concat(t)]}))),_l(this,"getFilterCells",(0,a.qe)((function(e,t,n){return Q(e,t,(function(e,t,r){return o().cloneElement(e,{style:i.Jnq([n&&n.getStyle(0,r),t,e.props.style])})}))}))),_l(this,"getOpFilterCells",(0,a.qe)((function(e,t,n){return Q(e,t,(function(e,t,r){return o().cloneElement(e,{style:i.Jnq([n&&n.getStyle(0,r),t,e.props.style])})}))}))),this.propsFn=t}var t,n;return t=e,n=[{key:"props",get:function(){return this.propsFn()}},{key:"createFilters",value:function(e,t){var n=this,r=this.props,o=r.filter_action,a=r.map,s=r.row_deletable,l=r.row_selectable,c=r.setFilter,u=r.style_cell,p=r.style_cell_conditional,d=r.style_filter,f=r.style_filter_conditional,h=r.toggleFilterOptions,A=r.visibleColumns;if(o.type===_.p9.None)return Pl;var b=this.relevantStyles(u,d,p,f),v=this.wrapperStyles(this.filterStyles(A,b),e),g=this.filterOpStyles(1,(l?1:0)+(s?1:0),b)[0],y=i.h0F(i.UID)((function(e,t){return n.filter.get(e.id,t)(e,t,a,o.operator,c,h)}),A),m=this.getFilterCells(y,v,e),w=this.headerOperations(1,l,s)[0],E=this.getOpFilterCells(w,g,t);return this.getCells(E,m)}}],n&&Bl(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();!function(e){e.Ascending="asc",e.Descending="desc",e.None="none"}(jl||(jl={}));var Il=function(e,t){return i.kKJ(e)},Fl=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Il;return t.length?i.HCG(i.UID((function(e){return e.direction===jl.Descending?i.Ukb((function(t,r){var o=e.column_id,i=t[o],a=r[o];return!n(i,e.column_id)&&(!!n(a,e.column_id)||i>a)})):i.Ukb((function(t,r){var o=e.column_id,i=t[o],a=r[o];return!n(i,e.column_id)&&(!!n(a,e.column_id)||i<a)}))}),t),e):e},Tl=function(e,t){if(s.ZP.trace("multi - update sortBy",e,t),e=i.d9v(e),t.direction===jl.None){var n=i.cxD((function(e){return e.column_id===t.column_id}),e);-1!==n&&e.splice(n,1)}else{var r=i.sEJ((function(e){return e.column_id===t.column_id}),e);r?r.direction=t.direction:e.push(t)}return e},Ml=function(e,t){return s.ZP.trace("single - update sortBy",e,t),t.direction===jl.None?[]:[t]};function Rl(e){return Rl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Rl(e)}function zl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Nl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ll(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Nl(e,t,n[t])}))}return e}function ql(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==s.return||s.return()}finally{if(o)throw i}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var Wl=function(){},Ul={},Vl={},Hl={mark:Wl,measure:Wl};try{"undefined"!=typeof window&&(Ul=window),"undefined"!=typeof document&&(Vl=document),"undefined"!=typeof MutationObserver&&MutationObserver,"undefined"!=typeof performance&&(Hl=performance)}catch(e){}var Yl=(Ul.navigator||{}).userAgent,Kl=void 0===Yl?"":Yl,Zl=Ul,Ql=Vl,$l=Hl,Gl=(Zl.document,!!Ql.documentElement&&!!Ql.head&&"function"==typeof Ql.addEventListener&&"function"==typeof Ql.createElement),Jl=(~Kl.indexOf("MSIE")||Kl.indexOf("Trident/"),"svg-inline--fa"),Xl=[1,2,3,4,5,6,7,8,9,10],ec=Xl.concat([11,12,13,14,15,16,17,18,19,20]),tc={GROUP:"group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},nc=(["xs","sm","lg","fw","ul","li","border","pull-left","pull-right","spin","pulse","rotate-90","rotate-180","rotate-270","flip-horizontal","flip-vertical","flip-both","stack","stack-1x","stack-2x","inverse","layers","layers-text","layers-counter",tc.GROUP,tc.SWAP_OPACITY,tc.PRIMARY,tc.SECONDARY].concat(Xl.map((function(e){return"".concat(e,"x")}))).concat(ec.map((function(e){return"w-".concat(e)}))),Zl.FontAwesomeConfig||{});Ql&&"function"==typeof Ql.querySelector&&[["data-family-prefix","familyPrefix"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach((function(e){var t=ql(e,2),n=t[0],r=t[1],o=function(e){return""===e||"false"!==e&&("true"===e||e)}(function(e){var t=Ql.querySelector("script["+e+"]");if(t)return t.getAttribute(e)}(n));null!=o&&(nc[r]=o)}));var rc=Ll({},{familyPrefix:"fa",replacementClass:Jl,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0},nc);rc.autoReplaceSvg||(rc.observeMutations=!1);var oc=Ll({},rc);Zl.FontAwesomeConfig=oc;var ic=Zl||{};ic.___FONT_AWESOME___||(ic.___FONT_AWESOME___={}),ic.___FONT_AWESOME___.styles||(ic.___FONT_AWESOME___.styles={}),ic.___FONT_AWESOME___.hooks||(ic.___FONT_AWESOME___.hooks={}),ic.___FONT_AWESOME___.shims||(ic.___FONT_AWESOME___.shims=[]);var ac=ic.___FONT_AWESOME___,sc=[];Gl&&((Ql.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(Ql.readyState)||Ql.addEventListener("DOMContentLoaded",(function e(){Ql.removeEventListener("DOMContentLoaded",e),sc.map((function(e){return e()}))})));var lc,cc="pending",uc="settled",pc="fulfilled",dc="rejected",fc=function(){},hc=void 0!==n.g&&void 0!==n.g.process&&"function"==typeof n.g.process.emit,Ac="undefined"==typeof setImmediate?setTimeout:setImmediate,bc=[];function vc(){for(var e=0;e<bc.length;e++)bc[e][0](bc[e][1]);bc=[],lc=!1}function gc(e,t){bc.push([e,t]),lc||(lc=!0,Ac(vc,0))}function yc(e){var t=e.owner,n=t._state,r=t._data,o=e[n],i=e.then;if("function"==typeof o){n=pc;try{r=o(r)}catch(e){Cc(i,e)}}mc(i,r)||(n===pc&&wc(i,r),n===dc&&Cc(i,r))}function mc(e,t){var n;try{if(e===t)throw new TypeError("A promises callback cannot return that same promise.");if(t&&("function"==typeof t||"object"===Rl(t))){var r=t.then;if("function"==typeof r)return r.call(t,(function(r){n||(n=!0,t===r?Ec(e,r):wc(e,r))}),(function(t){n||(n=!0,Cc(e,t))})),!0}}catch(t){return n||Cc(e,t),!0}return!1}function wc(e,t){e!==t&&mc(e,t)||Ec(e,t)}function Ec(e,t){e._state===cc&&(e._state=uc,e._data=t,gc(xc,e))}function Cc(e,t){e._state===cc&&(e._state=uc,e._data=t,gc(Oc,e))}function kc(e){e._then=e._then.forEach(yc)}function xc(e){e._state=pc,kc(e)}function Oc(e){e._state=dc,kc(e),!e._handled&&hc&&n.g.process.emit("unhandledRejection",e._data,e)}function Sc(e){n.g.process.emit("rejectionHandled",e)}function Bc(e){if("function"!=typeof e)throw new TypeError("Promise resolver "+e+" is not a function");if(this instanceof Bc==0)throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this._then=[],function(e,t){function n(e){Cc(t,e)}try{e((function(e){wc(t,e)}),n)}catch(e){n(e)}}(e,this)}Bc.prototype={constructor:Bc,_state:cc,_then:null,_data:void 0,_handled:!1,then:function(e,t){var n={owner:this,then:new this.constructor(fc),fulfilled:e,rejected:t};return!t&&!e||this._handled||(this._handled=!0,this._state===dc&&hc&&gc(Sc,this)),this._state===pc||this._state===dc?gc(yc,n):this._then.push(n),n.then},catch:function(e){return this.then(null,e)}},Bc.all=function(e){if(!Array.isArray(e))throw new TypeError("You must pass an array to Promise.all().");return new Bc((function(t,n){var r=[],o=0;function i(e){return o++,function(n){r[e]=n,--o||t(r)}}for(var a,s=0;s<e.length;s++)(a=e[s])&&"function"==typeof a.then?a.then(i(s),n):r[s]=a;o||t(r)}))},Bc.race=function(e){if(!Array.isArray(e))throw new TypeError("You must pass an array to Promise.race().");return new Bc((function(t,n){for(var r,o=0;o<e.length;o++)(r=e[o])&&"function"==typeof r.then?r.then(t,n):t(r)}))},Bc.resolve=function(e){return e&&"object"===Rl(e)&&e.constructor===Bc?e:new Bc((function(t){t(e)}))},Bc.reject=function(e){return new Bc((function(t,n){n(e)}))};var _c={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function jc(){for(var e=12,t="";e-- >0;)t+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return t}function Pc(e){return"".concat(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Dc(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,": ").concat(e[n],";")}),"")}function Ic(e){return e.size!==_c.size||e.x!==_c.x||e.y!==_c.y||e.rotate!==_c.rotate||e.flipX||e.flipY}function Fc(e){var t=e.transform,n=e.containerWidth,r=e.iconWidth,o={transform:"translate(".concat(n/2," 256)")},i="translate(".concat(32*t.x,", ").concat(32*t.y,") "),a="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),s="rotate(".concat(t.rotate," 0 0)");return{outer:o,inner:{transform:"".concat(i," ").concat(a," ").concat(s)},path:{transform:"translate(".concat(r/2*-1," -256)")}}}var Tc={x:0,y:0,width:"100%",height:"100%"};function Mc(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e.attributes&&(e.attributes.fill||t)&&(e.attributes.fill="black"),e}function Rc(e){var t=e.icons,n=t.main,r=t.mask,o=e.prefix,i=e.iconName,a=e.transform,s=e.symbol,l=e.title,c=e.maskId,u=e.titleId,p=e.extra,d=e.watchable,f=void 0!==d&&d,h=r.found?r:n,A=h.width,b=h.height,v="fak"===o,g=v?"":"fa-w-".concat(Math.ceil(A/b*16)),y=[oc.replacementClass,i?"".concat(oc.familyPrefix,"-").concat(i):"",g].filter((function(e){return-1===p.classes.indexOf(e)})).filter((function(e){return""!==e||!!e})).concat(p.classes).join(" "),m={children:[],attributes:Ll({},p.attributes,{"data-prefix":o,"data-icon":i,class:y,role:p.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(A," ").concat(b)})},w=v&&!~p.classes.indexOf("fa-fw")?{width:"".concat(A/b*16*.0625,"em")}:{};f&&(m.attributes["data-fa-i2svg"]=""),l&&m.children.push({tag:"title",attributes:{id:m.attributes["aria-labelledby"]||"title-".concat(u||jc())},children:[l]});var E=Ll({},m,{prefix:o,iconName:i,main:n,mask:r,maskId:c,transform:a,symbol:s,styles:Ll({},w,p.styles)}),C=r.found&&n.found?function(e){var t,n=e.children,r=e.attributes,o=e.main,i=e.mask,a=e.maskId,s=e.transform,l=o.width,c=o.icon,u=i.width,p=i.icon,d=Fc({transform:s,containerWidth:u,iconWidth:l}),f={tag:"rect",attributes:Ll({},Tc,{fill:"white"})},h=c.children?{children:c.children.map(Mc)}:{},A={tag:"g",attributes:Ll({},d.inner),children:[Mc(Ll({tag:c.tag,attributes:Ll({},c.attributes,d.path)},h))]},b={tag:"g",attributes:Ll({},d.outer),children:[A]},v="mask-".concat(a||jc()),g="clip-".concat(a||jc()),y={tag:"mask",attributes:Ll({},Tc,{id:v,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[f,b]},m={tag:"defs",children:[{tag:"clipPath",attributes:{id:g},children:(t=p,"g"===t.tag?t.children:[t])},y]};return n.push(m,{tag:"rect",attributes:Ll({fill:"currentColor","clip-path":"url(#".concat(g,")"),mask:"url(#".concat(v,")")},Tc)}),{children:n,attributes:r}}(E):function(e){var t=e.children,n=e.attributes,r=e.main,o=e.transform,i=Dc(e.styles);if(i.length>0&&(n.style=i),Ic(o)){var a=Fc({transform:o,containerWidth:r.width,iconWidth:r.width});t.push({tag:"g",attributes:Ll({},a.outer),children:[{tag:"g",attributes:Ll({},a.inner),children:[{tag:r.icon.tag,children:r.icon.children,attributes:Ll({},r.icon.attributes,a.path)}]}]})}else t.push(r.icon);return{children:t,attributes:n}}(E),k=C.children,x=C.attributes;return E.children=k,E.attributes=x,s?function(e){var t=e.prefix,n=e.iconName,r=e.children,o=e.attributes,i=e.symbol;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:Ll({},o,{id:!0===i?"".concat(t,"-").concat(oc.familyPrefix,"-").concat(n):i}),children:r}]}]}(E):function(e){var t=e.children,n=e.main,r=e.mask,o=e.attributes,i=e.styles,a=e.transform;if(Ic(a)&&n.found&&!r.found){var s={x:n.width/n.height/2,y:.5};o.style=Dc(Ll({},i,{"transform-origin":"".concat(s.x+a.x/16,"em ").concat(s.y+a.y/16,"em")}))}return[{tag:"svg",attributes:o,children:t}]}(E)}var zc=(oc.measurePerformance&&$l&&$l.mark&&$l.measure,function(e,t,n,r){var o,i,a,s=Object.keys(e),l=s.length,c=void 0!==r?function(e,t){return function(n,r,o,i){return e.call(t,n,r,o,i)}}(t,r):t;for(void 0===n?(o=1,a=e[s[0]]):(o=0,a=n);o<l;o++)a=c(a,e[i=s[o]],i,e);return a});function Nc(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.skipHooks,o=void 0!==r&&r,i=Object.keys(t).reduce((function(e,n){var r=t[n];return r.icon?e[r.iconName]=r.icon:e[n]=r,e}),{});"function"!=typeof ac.hooks.addPack||o?ac.styles[e]=Ll({},ac.styles[e]||{},i):ac.hooks.addPack(e,i),"fas"===e&&Nc("fa",t)}var Lc=ac.styles,qc=ac.shims,Wc=function(){var e=function(e){return zc(Lc,(function(t,n,r){return t[r]=zc(n,e,{}),t}),{})};e((function(e,t,n){return t[3]&&(e[t[3]]=n),e})),e((function(e,t,n){var r=t[2];return e[n]=n,r.forEach((function(t){e[t]=n})),e}));var t="far"in Lc;zc(qc,(function(e,n){var r=n[0],o=n[1],i=n[2];return"far"!==o||t||(o="fas"),e[r]={prefix:o,iconName:i},e}),{})};function Uc(e,t,n){if(e&&e[t]&&e[t][n])return{prefix:t,iconName:n,icon:e[t][n]}}function Vc(e){var t=e.tag,n=e.attributes,r=void 0===n?{}:n,o=e.children,i=void 0===o?[]:o;return"string"==typeof e?Pc(e):"<".concat(t," ").concat(function(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,'="').concat(Pc(e[n]),'" ')}),"").trim()}(r),">").concat(i.map(Vc).join(""),"</").concat(t,">")}Wc(),ac.styles;function Hc(e){this.name="MissingIcon",this.message=e||"Icon unavailable",this.stack=(new Error).stack}Hc.prototype=Object.create(Error.prototype),Hc.prototype.constructor=Hc;var Yc={fill:"currentColor"},Kc={attributeType:"XML",repeatCount:"indefinite",dur:"2s"},Zc=(Ll({},Yc,{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"}),Ll({},Kc,{attributeName:"opacity"}));function Qc(e){var t=e[0],n=e[1],r=ql(e.slice(4),1)[0];return{found:!0,width:t,height:n,icon:Array.isArray(r)?{tag:"g",attributes:{class:"".concat(oc.familyPrefix,"-").concat(tc.GROUP)},children:[{tag:"path",attributes:{class:"".concat(oc.familyPrefix,"-").concat(tc.SECONDARY),fill:"currentColor",d:r[0]}},{tag:"path",attributes:{class:"".concat(oc.familyPrefix,"-").concat(tc.PRIMARY),fill:"currentColor",d:r[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:r}}}}Ll({},Yc,{cx:"256",cy:"364",r:"28"}),Ll({},Kc,{attributeName:"r",values:"28;14;28;28;14;28;"}),Ll({},Zc,{values:"1;0;1;1;0;1;"}),Ll({},Yc,{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),Ll({},Zc,{values:"1;0;0;0;0;1;"}),Ll({},Yc,{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),Ll({},Zc,{values:"0;0;1;1;0;0;"}),ac.styles,ac.styles;var $c=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.definitions={}}var t,n;return t=e,n=[{key:"add",value:function(){for(var e=this,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=n.reduce(this._pullDefinitions,{});Object.keys(o).forEach((function(t){e.definitions[t]=Ll({},e.definitions[t]||{},o[t]),Nc(t,o[t]),Wc()}))}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(e,t){var n=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(n).map((function(t){var r=n[t],o=r.prefix,i=r.iconName,a=r.icon;e[o]||(e[o]={}),e[o][i]=a})),e}}],n&&zl(t.prototype,n),e}();function Gc(){oc.autoAddCss&&!nu&&(function(e){if(e&&Gl){var t=Ql.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e;for(var n=Ql.head.childNodes,r=null,o=n.length-1;o>-1;o--){var i=n[o],a=(i.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(a)>-1&&(r=i)}Ql.head.insertBefore(t,r)}}(function(){var e="fa",t=Jl,n=oc.familyPrefix,r=oc.replacementClass,o='svg:not(:root).svg-inline--fa {\n  overflow: visible;\n}\n\n.svg-inline--fa {\n  display: inline-block;\n  font-size: inherit;\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.225em;\n}\n.svg-inline--fa.fa-w-1 {\n  width: 0.0625em;\n}\n.svg-inline--fa.fa-w-2 {\n  width: 0.125em;\n}\n.svg-inline--fa.fa-w-3 {\n  width: 0.1875em;\n}\n.svg-inline--fa.fa-w-4 {\n  width: 0.25em;\n}\n.svg-inline--fa.fa-w-5 {\n  width: 0.3125em;\n}\n.svg-inline--fa.fa-w-6 {\n  width: 0.375em;\n}\n.svg-inline--fa.fa-w-7 {\n  width: 0.4375em;\n}\n.svg-inline--fa.fa-w-8 {\n  width: 0.5em;\n}\n.svg-inline--fa.fa-w-9 {\n  width: 0.5625em;\n}\n.svg-inline--fa.fa-w-10 {\n  width: 0.625em;\n}\n.svg-inline--fa.fa-w-11 {\n  width: 0.6875em;\n}\n.svg-inline--fa.fa-w-12 {\n  width: 0.75em;\n}\n.svg-inline--fa.fa-w-13 {\n  width: 0.8125em;\n}\n.svg-inline--fa.fa-w-14 {\n  width: 0.875em;\n}\n.svg-inline--fa.fa-w-15 {\n  width: 0.9375em;\n}\n.svg-inline--fa.fa-w-16 {\n  width: 1em;\n}\n.svg-inline--fa.fa-w-17 {\n  width: 1.0625em;\n}\n.svg-inline--fa.fa-w-18 {\n  width: 1.125em;\n}\n.svg-inline--fa.fa-w-19 {\n  width: 1.1875em;\n}\n.svg-inline--fa.fa-w-20 {\n  width: 1.25em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-border {\n  height: 1.5em;\n}\n.svg-inline--fa.fa-li {\n  width: 2em;\n}\n.svg-inline--fa.fa-fw {\n  width: 1.25em;\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: #ff253a;\n  border-radius: 1em;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: #fff;\n  height: 1.5em;\n  line-height: 1;\n  max-width: 5em;\n  min-width: 1.5em;\n  overflow: hidden;\n  padding: 0.25em;\n  right: 0;\n  text-overflow: ellipsis;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: 0;\n  right: 0;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: 0;\n  left: 0;\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  right: 0;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: 0;\n  right: auto;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top left;\n          transform-origin: top left;\n}\n\n.fa-lg {\n  font-size: 1.3333333333em;\n  line-height: 0.75em;\n  vertical-align: -0.0667em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: 2.5em;\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: -2em;\n  position: absolute;\n  text-align: center;\n  width: 2em;\n  line-height: inherit;\n}\n\n.fa-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.fa-pull-left {\n  float: left;\n}\n\n.fa-pull-right {\n  float: right;\n}\n\n.fa.fa-pull-left,\n.fas.fa-pull-left,\n.far.fa-pull-left,\n.fal.fa-pull-left,\n.fab.fa-pull-left {\n  margin-right: 0.3em;\n}\n.fa.fa-pull-right,\n.fas.fa-pull-right,\n.far.fa-pull-right,\n.fal.fa-pull-right,\n.fab.fa-pull-right {\n  margin-left: 0.3em;\n}\n\n.fa-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear;\n}\n\n.fa-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8);\n}\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1);\n}\n\n.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1);\n}\n\n:root .fa-rotate-90,\n:root .fa-rotate-180,\n:root .fa-rotate-270,\n:root .fa-flip-horizontal,\n:root .fa-flip-vertical,\n:root .fa-flip-both {\n  -webkit-filter: none;\n          filter: none;\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: #fff;\n}\n\n.sr-only {\n  border: 0;\n  clip: rect(0, 0, 0, 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  clip: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  position: static;\n  width: auto;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.fad.fa-inverse {\n  color: #fff;\n}';if(n!==e||r!==t){var i=new RegExp("\\.".concat(e,"\\-"),"g"),a=new RegExp("\\--".concat(e,"\\-"),"g"),s=new RegExp("\\.".concat(t),"g");o=o.replace(i,".".concat(n,"-")).replace(a,"--".concat(n,"-")).replace(s,".".concat(r))}return o}()),nu=!0)}function Jc(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map((function(e){return Vc(e)}))}}),Object.defineProperty(e,"node",{get:function(){if(Gl){var t=Ql.createElement("div");return t.innerHTML=e.html,t.children}}}),e}function Xc(e){var t=e.prefix,n=void 0===t?"fa":t,r=e.iconName;if(r)return Uc(tu.definitions,n,r)||Uc(ac.styles,n,r)}var eu,tu=new $c,nu=!1,ru={transform:function(e){return function(e){var t={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return e?e.toLowerCase().split(" ").reduce((function(e,t){var n=t.toLowerCase().split("-"),r=n[0],o=n.slice(1).join("-");if(r&&"h"===o)return e.flipX=!0,e;if(r&&"v"===o)return e.flipY=!0,e;if(o=parseFloat(o),isNaN(o))return e;switch(r){case"grow":e.size=e.size+o;break;case"shrink":e.size=e.size-o;break;case"left":e.x=e.x-o;break;case"right":e.x=e.x+o;break;case"up":e.y=e.y-o;break;case"down":e.y=e.y+o;break;case"rotate":e.rotate=e.rotate+o}return e}),t):t}(e)}},ou=(eu=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.transform,r=void 0===n?_c:n,o=t.symbol,i=void 0!==o&&o,a=t.mask,s=void 0===a?null:a,l=t.maskId,c=void 0===l?null:l,u=t.title,p=void 0===u?null:u,d=t.titleId,f=void 0===d?null:d,h=t.classes,A=void 0===h?[]:h,b=t.attributes,v=void 0===b?{}:b,g=t.styles,y=void 0===g?{}:g;if(e){var m=e.prefix,w=e.iconName,E=e.icon;return Jc(Ll({type:"icon"},e),(function(){return Gc(),oc.autoA11y&&(p?v["aria-labelledby"]="".concat(oc.replacementClass,"-title-").concat(f||jc()):(v["aria-hidden"]="true",v.focusable="false")),Rc({icons:{main:Qc(E),mask:s?Qc(s.icon):{found:!1,width:null,height:null,icon:{}}},prefix:m,iconName:w,transform:Ll({},_c,r),symbol:i,title:p,maskId:c,titleId:f,extra:{attributes:v,styles:y,classes:A}})}))}},function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(e||{}).icon?e:Xc(e||{}),r=t.mask;return r&&(r=(r||{}).icon?r:Xc(r||{})),eu(n,Ll({},t,{mask:r}))});function iu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function au(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?iu(Object(n),!0).forEach((function(t){lu(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):iu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function su(e){return su="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},su(e)}function lu(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function cu(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function uu(e){return function(e){if(Array.isArray(e))return pu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return pu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pu(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function du(e){return t=e,(t-=0)==t?e:(e=e.replace(/[\-_\s]+(.)?/g,(function(e,t){return t?t.toUpperCase():""}))).substr(0,1).toLowerCase()+e.substr(1);var t}var fu=["style"];function hu(e){return e.split(";").map((function(e){return e.trim()})).filter((function(e){return e})).reduce((function(e,t){var n,r=t.indexOf(":"),o=du(t.slice(0,r)),i=t.slice(r+1).trim();return o.startsWith("webkit")?e[(n=o,n.charAt(0).toUpperCase()+n.slice(1))]=i:e[o]=i,e}),{})}var Au=!1;try{Au=!0}catch(e){}function bu(e){return e&&"object"===su(e)&&e.prefix&&e.iconName&&e.icon?e:ru.icon?ru.icon(e):null===e?null:e&&"object"===su(e)&&e.prefix&&e.iconName?e:Array.isArray(e)&&2===e.length?{prefix:e[0],iconName:e[1]}:"string"==typeof e?{prefix:"fas",iconName:e}:void 0}function vu(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?lu({},e,t):{}}var gu=["forwardedRef"];function yu(e){var t=e.forwardedRef,n=cu(e,gu),r=n.icon,o=n.mask,i=n.symbol,a=n.className,s=n.title,l=n.titleId,c=n.maskId,u=bu(r),p=vu("classes",[].concat(uu(function(e){var t,n=e.beat,r=e.fade,o=e.beatFade,i=e.bounce,a=e.shake,s=e.flash,l=e.spin,c=e.spinPulse,u=e.spinReverse,p=e.pulse,d=e.fixedWidth,f=e.inverse,h=e.border,A=e.listItem,b=e.flip,v=e.size,g=e.rotation,y=e.pull,m=(lu(t={"fa-beat":n,"fa-fade":r,"fa-beat-fade":o,"fa-bounce":i,"fa-shake":a,"fa-flash":s,"fa-spin":l,"fa-spin-reverse":u,"fa-spin-pulse":c,"fa-pulse":p,"fa-fw":d,"fa-inverse":f,"fa-border":h,"fa-li":A,"fa-flip":!0===b,"fa-flip-horizontal":"horizontal"===b||"both"===b,"fa-flip-vertical":"vertical"===b||"both"===b},"fa-".concat(v),null!=v),lu(t,"fa-rotate-".concat(g),null!=g&&0!==g),lu(t,"fa-pull-".concat(y),null!=y),lu(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(m).map((function(e){return m[e]?e:null})).filter((function(e){return e}))}(n)),uu(a.split(" ")))),d=vu("transform","string"==typeof n.transform?ru.transform(n.transform):n.transform),f=vu("mask",bu(o)),h=ou(u,au(au(au(au({},p),d),f),{},{symbol:i,title:s,titleId:l,maskId:c}));if(!h)return function(){var e;!Au&&console&&"function"==typeof console.error&&(e=console).error.apply(e,arguments)}("Could not find icon",u),null;var A=h.abstract,b={ref:t};return Object.keys(n).forEach((function(e){yu.defaultProps.hasOwnProperty(e)||(b[e]=n[e])})),mu(A[0],b)}yu.displayName="FontAwesomeIcon",yu.propTypes={beat:Ht().bool,border:Ht().bool,beatFade:Ht().bool,bounce:Ht().bool,className:Ht().string,fade:Ht().bool,flash:Ht().bool,mask:Ht().oneOfType([Ht().object,Ht().array,Ht().string]),maskId:Ht().string,fixedWidth:Ht().bool,inverse:Ht().bool,flip:Ht().oneOf([!0,!1,"horizontal","vertical","both"]),icon:Ht().oneOfType([Ht().object,Ht().array,Ht().string]),listItem:Ht().bool,pull:Ht().oneOf(["right","left"]),pulse:Ht().bool,rotation:Ht().oneOf([0,90,180,270]),shake:Ht().bool,size:Ht().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:Ht().bool,spinPulse:Ht().bool,spinReverse:Ht().bool,symbol:Ht().oneOfType([Ht().bool,Ht().string]),title:Ht().string,titleId:Ht().string,transform:Ht().oneOfType([Ht().string,Ht().object]),swapOpacity:Ht().bool},yu.defaultProps={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1};var mu=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var o=(n.children||[]).map((function(n){return e(t,n)})),i=Object.keys(n.attributes||{}).reduce((function(e,t){var r=n.attributes[t];switch(t){case"class":e.attrs.className=r,delete n.attributes.class;break;case"style":e.attrs.style=hu(r);break;default:0===t.indexOf("aria-")||0===t.indexOf("data-")?e.attrs[t.toLowerCase()]=r:e.attrs[du(t)]=r}return e}),{attrs:{}}),a=r.style,s=void 0===a?{}:a,l=cu(r,fu);return i.attrs.style=au(au({},i.attrs.style),s),t.apply(void 0,[n.tag,au(au({},i.attrs),l)].concat(uu(o)))}.bind(null,o().createElement);function wu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Eu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Cu(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ku=function(e,t,n,r,o,a,s,l,c,u,p,d){return function(){var f=e(n,r,a,s,l,d),h=k(n,r,s,l);e===O&&i.jVA(t,h).length>0&&(f.selected_columns=i.zud(h,t)),u(f);var A=[];i.Ed_((function(e){var t=r.find((function(t){return t.id===e}));t&&A.push(t)}),h),function(e,t,n,r){i.Ed_((function(t){e=xl(e,t,"")}),t),Ol(e,n,r)}(p,A,o,c)}};function xu(e,t,n,r){return function(){var o;switch(Bu(e,t)){case jl.Descending:o=jl.None;break;case jl.Ascending:o=jl.Descending;break;case jl.None:default:o=jl.Ascending}var i=n===_.h8.Single?Ml:Tl;r(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Eu(Object(n),!0).forEach((function(t){Cu(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Eu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({sort_by:i(t,{column_id:e,direction:o})},B))}}function Ou(e,t,n,r,o){return function(){var a=function(e,t,n,r){var o=window.prompt("Enter a new column name");return null===o?null:function(e,t,n,r,o){var a=t,s=m(a),l=a.findIndex((function(t){return t.id===e.id}));if("string"==typeof e.name&&s>1){var c=Array(s).fill(e.name),u=i.ATH(e,{name:c});(a=a.slice(0))[l]=u}var p=C(e,a,n,r,l,!0),d=p.groupIndexFirst,f=p.groupIndexLast;return i.w6H(d,f+1).map((function(e){var t=[e,"name"];"Array"===i.dt8(a[e].name)&&t.push(n),a=i.t8m(i.QMA(t),o,a)})),{columns:a}}(e,t,n,r,o)}(e,t,n,o);a&&r(a)}}function Su(e,t,n,r,o,a,s,l){if(s&&!l)return function(){};var c=k(t,n,r,a,!0);return s?function(){return o({selected_columns:c})}:l?function(){return o({selected_columns:i.G0j(e,c)})}:function(){return o({selected_columns:i.zud(c,e)})}}function Bu(e,t){var n=i.sEJ((function(t){return t.column_id===e}),t);return n?n.direction:jl.None}function _u(e,t){switch(Bu(e,t)){case jl.Descending:return"sort-down";case jl.Ascending:return"sort-up";case jl.None:default:return"sort"}}var ju=(0,a.Pi)((function(e,t,n,r,a,s,l,c,u,p,d,f,h,A,b,v,g){return i.h0F(i.UID)((function(y,m){var w=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return wu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?wu(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(y,2),E=w[0],C=w[1],B=s.length-1,j=m===B;return i.h0F(i.UID)((function(s,y){var w,P=t[s];w=g?s===i.Z$Q(C)?E.length-s:C[y+1]-s:1;var D=A!==_.p9.Custom&&Ue(m,B,P.clearable),I=A!==_.p9.Custom&&Ue(m,B,P.deletable),F=Ue(m,B,P.hideable),T=Ue(m,B,P.renamable),M=Ue(m,B,P.selectable),R=t.length===w,z=k(P,n,m,g,!0),N=M&&("single"!==c||u.length===z.length)&&i.$6P((function(e){return-1!==u.indexOf(e)}),z);return o().createElement("div",{key:s},o().createElement("div",{className:"column-actions"},c&&M?o().createElement("span",{className:"column-header--select"},o().createElement("input",{checked:N,onChange:Su(u,P,n,m,v,g,"single"===c,!N),name:"column-select-".concat(e),type:"single"===c?"radio":"checkbox"})):null,p!==_.p9.None&&j?o().createElement("span",{className:"column-header--sort",onClick:xu(P.id,f,d,v)},o().createElement(yu,{icon:_u(P.id,f)})):null,T?o().createElement("span",{className:"column-header--edit",onClick:Ou(P,n,m,v,g)},o().createElement(yu,{icon:"pencil-alt"})):null,D?o().createElement("span",{className:"column-header--clear",onClick:ku(x,u,P,n,h,t,m,g,b,v,l,a)},o().createElement(yu,{icon:"eraser"})):null,I?o().createElement("span",{className:"column-header--delete"+(R?" disabled":""),onClick:R?void 0:ku(O,u,P,n,h,t,m,g,b,v,l,a)},o().createElement(yu,{icon:["far","trash-alt"]})):null,F?o().createElement("span",{className:"column-header--hide"+(R?" disabled":""),onClick:R?void 0:function(){var e=S(P,t,m,g),n=r?i.G0j(r,e):e;v({hidden_columns:n})}},o().createElement(yu,{icon:["far","eye-slash"]})):null),o().createElement("span",{className:"column-header-name"},E[s]))}),C)}),s)}));function Pu(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Du(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Du(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Du(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Iu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Fu(e,t,n){return t&&Iu(e.prototype,t),n&&Iu(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Tu(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mu(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ru=Fu((function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt(t);Tu(this,e),Mu(this,"handlers",void 0),Mu(this,"get",(function(e,t,r){return t.map((function(t,o){var a=Pu(t,2),s=a[0],l=a[1];return l.map((function(t,a){var c,u=e[t];return c=r?t===i.Z$Q(l)?s.length-t:l[a+1]-t:1,n.wrapper.get(o,t)(t,u.id,c,t===e.length-1||t===i.Z$Q(l),n.handlers(ot.EnterHeader,o,t),n.handlers(ot.Leave,o,t),n.handlers(ot.MoveHeader,o,t))}))}))})),Mu(this,"wrapper",Ge()((function(e,t,n,r,i,a,s){return o().createElement("th",{key:"header-cell-".concat(e),"data-dash-column":t,colSpan:n,className:"dash-header "+"column-".concat(e," ")+(r?"cell--right-last ":""),onMouseEnter:i,onMouseLeave:a,onMouseMove:s})}))),this.handlers=r})),zu=(0,a.Pi)((function(e,t,n){return Ke(i.w6H(0,t),e,(function(e,t){return function(e,t){return function(n){return ys(es(e,t)(n))}}(e,t)(n)}))})),Nu=(0,a.Pi)((function(e,t,n){return Ke(i.w6H(0,e),i.w6H(0,t),(function(e){return function(e){return function(t){return ys(rs(e)(t))}}(e)(n)}))}));function Lu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function qu(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Wu=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),qu(this,"propsFn",void 0),qu(this,"headerContent",ju()),qu(this,"headerOperations",El()),qu(this,"headerStyles",zu()),qu(this,"headerOpStyles",Nu()),qu(this,"headerWrappers",new Ru((function(){return n.props}))),qu(this,"relevantStyles",vs()),qu(this,"labelsAndIndices",Ve()),qu(this,"filterMergedCells",(0,a.qe)((function(e,t){for(var n=[],r=0;r<e.length;r++){for(var o=[],i=0;i<e[r].length;i++)t[r][1].includes(i)&&o.push(e[r][i]);n.push(o)}return n}))),qu(this,"getCells",(0,a.qe)((function(e,t){return Q(e,t,(function(e,t){return Array.prototype.concat(e,t)}))}))),qu(this,"getHeaderOpCells",(0,a.qe)((function(e,t,n){return Ze(e,t,(function(e,t,r,a){return o().cloneElement(e,{style:i.Jnq([n&&n.getStyle(r,a),t,e.props.style])})}))}))),qu(this,"getHeaderCells",(0,a.qe)((function(e,t,n,r){return Qe(e,n,t,(function(e,t,n,a,s){return o().cloneElement(e,{children:[n],style:i.ATH(t||{},r&&r.getStyle(a,s)||{})})}))}))),this.propsFn=t}var t,n;return t=e,n=[{key:"props",get:function(){return this.propsFn()}},{key:"createHeaders",value:function(e,t){var n=this.props,r=n.column_selectable,o=n.columns,i=n.data,a=n.filter_action,s=n.hidden_columns,l=n.id,c=n.map,u=n.merge_duplicate_headers,p=n.page_action,d=n.row_deletable,f=n.row_selectable,h=n.selected_columns,A=n.setFilter,b=n.setProps,v=n.sort_action,g=n.sort_by,y=n.sort_mode,m=n.style_cell,w=n.style_cell_conditional,E=n.style_header,C=n.style_header_conditional,k=n.visibleColumns,x=this.labelsAndIndices(o,k,u),O=x.length,S=this.relevantStyles(m,E,w,C),B=this.headerOperations(O,f,d),_=this.headerStyles(k,O,S),j=this.headerOpStyles(O,(f?1:0)+(d?1:0),S),P=this.headerWrappers.get(k,x,u),D=this.headerContent(l,k,o,s,i,x,c,r,h,v,y,g,a.operator,p,A,b,u),I=this.getHeaderOpCells(B,j,t),F=this.filterMergedCells(_,x),T=this.getHeaderCells(P,D,F,e);return this.getCells(I,T)}}],n&&Lu(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Uu(e){return function(e){if(Array.isArray(e))return Vu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Vu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Vu(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Vu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Hu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Yu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hu(Object(n),!0).forEach((function(t){Ku(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ku(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Zu=function(e,t,n,r,o){e(Yu({filter_query:n},B)),t({workFilter:{map:o,value:n},rawFilterQuery:r})},Qu=function(e){var t=(0,a.qe)((function(e,t){return Zu.bind(void 0,e,t)})),n=(0,a.qe)((function(e,t){return function(n){var r=Uu(t),o=t.indexOf(n),i=Yu({},r[o]);return i.filter_options=Yu(Yu({},i.filter_options),{},{case:i.filter_options.case===_.oN.Insensitive?_.oN.Sensitive:_.oN.Insensitive}),r.splice(o,1,i),e({columns:r}),i}})),r=new Ns(e),o=function(){var r=e();return function(e,t,n){var r=e();return i.ATH(r,{map:r.workFilter.map,setFilter:t,toggleFilterOptions:n})}(e,t(r.setProps,r.setState),n(r.setProps,r.columns))},s=new Dl(o),l=new Wu(o),c=new nl(e),u=(0,a.qe)((function(e,t,n){var r=[];return r.push.apply(r,Uu(n)),r.push.apply(r,Uu(t)),r.push.apply(r,Uu(e)),r}));return function(){var e=c.createEdges(),t=r.createCells(e.dataEdges,e.dataOpEdges),n=s.createFilters(e.filterEdges,e.filterOpEdges),o=l.createHeaders(e.headerEdges,e.headerOpEdges);return u(t,n,o)}};function $u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Gu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$u(Object(n),!0).forEach((function(t){Ju(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ju(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Xu(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e?o().createElement("table",{className:"cell-table",tabIndex:-1},o().createElement("tbody",null,e.map((function(e,n){return o().createElement("tr",{key:"row-".concat(n+t)},e)})))):null}var ep=function(e){return o().cloneElement(e,Gu(Gu({},e.props),{},{className:e.props.className?"".concat(e.props.className," phantom-cell"):"phantom-cell"}),"th"===e.type||"td"===e.type?null:e.props.children)},tp=function(e){return!e||0===e.length||0===e[0].length},np=(0,a.Pi)((function(e,t,n,r){var a=function(t){return i.jQz((function(t){return t.count<e}),(function(e,t){return e.cells++,e.count+=t.props.colSpan||1,e}),{cells:0,count:0},t).cells},s=e?i.UID((function(t){var n,r=a(t),s=t.slice(0,r).map((function(t,n){return r=t,a=e-n-1,o().cloneElement(r,Gu(Gu({},r.props),{},{colSpan:i.kKJ(r.props.colSpan)?r.props.colSpan:Math.min(r.props.colSpan,a)}));var r,a})).concat(t.slice(r).map(ep));return s[r-1]=(n=s[r-1],o().cloneElement(n,Gu(Gu({},n.props),{},{className:n.props.className?"".concat(n.props.className," last-of-type"):"last-of-type"}))),s}),n):null;n=i.kKJ(s)?n:i.UID((function(e){var t=a(e);return e.slice(0,t).map(ep).concat(e.slice(t))}),n);var l=t?n.slice(0,t):null;n=n.slice(t);var c=t&&s?s.slice(0,t):null;return s=s&&s.slice(t),{grid:[[Xu(c),Xu(l)],[Xu(s),Xu(n,r)]],empty:[[tp(c),tp(l)],[tp(s),tp(n)]]}})),rp=n(335),op=2147483647;function ip(e){return"number"==typeof e?e:0}function ap(e){return"number"==typeof e?e:op}var sp,lp=(0,a.qe)((function(e,t,n,r,o,a,s,l){var c,u=function(e,t,n,r,o,a){if(e){var s=e.header,l=e.id,c=e.row;if(void 0!==l&&void 0!==c){var u,p=s?void 0:i.dFj((function(e){return!e.if||$a(e.if,l)&&Ga(e.if,c)&&(t=e.if,n=a.data[c-a.offset.rows],!t||void 0===t.filter_query||function(e,t){return e.isValid&&e.evaluate(t)}(new ja(t.filter_query),n));var t,n}),r);if(p)return p;if(s){var d=null==n?void 0:n[l];u=Array.isArray(d)?null==d?void 0:d[c]:d}else{var f;u=null==t||null===(f=t[c])||void 0===f?void 0:f[l]}if(u)return u;var h=null==o?void 0:o[l],A=h&&"string"!=typeof h?h.use_with:rp.a.Both;return A===rp.a.Both||A===rp.a.Header===s?h:void 0}}}(e,t,n,r,o,a),p=ip(s),d=ap(l),f=rp.v.Text;return u&&("string"==typeof u?c=u:(p=function(e,t){return"number"==typeof e||null===e?ip(e):t}(u.delay,p),d=function(e,t){return"number"==typeof e||null===e?ap(e):t}(u.duration,d),f=u.type||rp.v.Text,c=u.value)),{delay:p,duration:d,type:f,value:c}})),cp=n(70);function up(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function pp(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function dp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function fp(e,t){return fp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},fp(e,t)}function hp(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ap(e){return Ap=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ap(e)}!function(e){e.Bottom="bottom",e.Left="left",e.Right="right",e.Top="top"}(sp||(sp={}));var bp=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fp(e,t)}(l,e);var t,n,r,a,s=(r=l,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Ap(r);if(a){var n=Ap(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return hp(this,e)});function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),(t=s.call(this,e)).state={md:new ho},t}return t=l,n=[{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=this,n=e.tooltip,r=n.delay,o=n.duration;(0,cp.X)(i.CEd(["arrow"],this.props),i.CEd(["arrow"],e))||this.setState({display:!1,displayTooltipId:Boolean(clearTimeout(this.state.displayTooltipId))||setTimeout((function(){return t.setState({display:!0})}),r),hideTooltipId:Boolean(clearTimeout(this.state.hideTooltipId))||setTimeout((function(){return t.setState({display:!1})}),Math.min(r+o,op))})}},{key:"render",value:function(){var e=this.props,t=e.arrow,n=e.className,r=this.props.tooltip,i=r.type,a=r.value,s=this.state.md;if(!i||!a)return null;var l=i===rp.v.Text?{children:a}:{dangerouslySetInnerHTML:{__html:s.render(a)}},c=this.state.display;return o().createElement("div",{className:"dash-tooltip","data-attr-anchor":t,style:{visibility:c?"visible":"hidden"}},o().createElement("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?up(Object(n),!0).forEach((function(t){pp(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):up(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({className:n},l)))}}],n&&dp(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent);function vp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function gp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function yp(e,t){return yp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},yp(e,t)}function mp(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return wp(e)}function wp(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ep(e){return Ep=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ep(e)}function Cp(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var kp=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yp(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Ep(r);if(i){var n=Ep(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return mp(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),Cp(wp(t=a.call(this,e)),"updateBounds",(function(e){t.setState({cell:e})})),t.state={arrow:sp.Bottom},t}return t=s,n=[{key:"shouldComponentUpdate",value:function(e,t){return this.adjustPosition(),!(0,cp.X)(this.props,e)||!(0,cp.X)(this.state,t)}},{key:"componentDidUpdate",value:function(){this.adjustPosition()}},{key:"render",value:function(){var e=this.state.arrow;return o().createElement(bp,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?vp(Object(n),!0).forEach((function(t){Cp(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({key:"tooltip",ref:"tooltip",arrow:e},this.props))}},{key:"adjustPosition",value:function(){var e=this.state.cell,t=Kt().findDOMNode(this.refs.tooltip),n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(e){for(var t=e;"relative"!==getComputedStyle(t).position&&"sticky"!==getComputedStyle(t).position&&t.parentElement;)t=t.parentElement;return t}}(t);if(n&&e&&t){var r=n.getBoundingClientRect(),o=e.getBoundingClientRect(),i=t.clientWidth,a=t.clientHeight,s=Math.max(parseFloat(getComputedStyle(t,":before").borderWidth||"0"),parseFloat(getComputedStyle(t,":after").borderWidth||"0")),l=(o.width-i)/2,c=o.left-r.left+n.scrollLeft+l,u=o.top-r.top+n.scrollTop+o.height,p=c+r.left,d=p+i,f=u+r.top+a+s,h=sp.Top;c-=Math.min(0,p),c-=Math.max(0,d-document.documentElement.clientWidth),f>document.documentElement.clientHeight&&(u-=a+s+o.height,h=sp.Bottom),t.style.top="".concat(u,"px"),t.style.left="".concat(c,"px"),t.style.position="absolute",this.state.arrow!==h&&this.setState({arrow:h})}}}],n&&gp(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.Component);function xp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Op(e,t){return Op=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Op(e,t)}function Sp(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Bp(e)}function Bp(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _p(e){return _p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_p(e)}var jp=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Op(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=_p(r);if(i){var n=_p(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Sp(this,e)});function s(e){var t,n,r,o;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),n=Bp(t=a.call(this,e)),o=function(e){var n=t.props.paginator,r=parseInt(e,10);isNaN(r)||n.loadPage(r-1)},(r="goToPage")in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,t}return t=s,n=[{key:"render",value:function(){var e,t=this,n=this.props,r=n.paginator,i=n.page_current;if(void 0!==r.lastPage&&r.lastPage<=0)return null;var a=Math.max(3,((null!==(e=r.lastPage)&&void 0!==e?e:0)+1).toString().length),s="".concat(a+1,"ch");return o().createElement("div",{className:"previous-next-container"},o().createElement("button",{className:"first-page",onClick:r.loadFirst,disabled:!r.hasPrevious()},o().createElement(yu,{icon:"angle-double-left"})),o().createElement("button",{className:"previous-page",onClick:r.loadPrevious,disabled:!r.hasPrevious()},o().createElement(yu,{icon:"angle-left"})),o().createElement("div",{className:"page-number"},o().createElement("div",{className:"current-page-container"},o().createElement("div",{className:"current-page-shadow",style:{minWidth:s}},(i+1).toString()),o().createElement("input",{type:"text",className:"current-page",style:{minWidth:s},onBlur:function(e){t.goToPage(e.target.value),e.target.value=""},onKeyDown:function(e){e.keyCode===h.ENTER&&e.currentTarget.blur()},placeholder:(i+1).toString(),defaultValue:""})),void 0!==r.lastPage?" / ":"",void 0!==r.lastPage?o().createElement("div",{className:"last-page",style:{minWidth:s}},r.lastPage+1):""),o().createElement("button",{className:"next-page",onClick:r.loadNext,disabled:!r.hasNext()},o().createElement(yu,{icon:"angle-right"})),o().createElement("button",{className:"last-page",onClick:r.loadLast,disabled:void 0===r.lastPage||r.isLast()},o().createElement(yu,{icon:"angle-double-right"})))}}],n&&xp(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.Component);function Pp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dp(e){return function(e){if(Array.isArray(e))return Fp(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Ip(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ip(e,t){if(e){if("string"==typeof e)return Fp(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Fp(e,t):void 0}}function Fp(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Tp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Mp(e,t){return Mp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Mp(e,t)}function Rp(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return zp(e)}function zp(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Np(e){return Np=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Np(e)}function Lp(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var qp={width:"100%"},Wp={minHeight:"100%",minWidth:"100%"},Up=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mp(e,t)}(u,e);var t,n,r,l,c=(r=u,l=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Np(r);if(l){var n=Np(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Rp(this,e)});function u(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),Lp(zp(t=c.call(this,e)),"menuRef",o().createRef()),Lp(zp(t),"stylesheet",new f("#".concat(CSS.escape(t.props.id)))),Lp(zp(t),"tableFn",Qu((function(){return t.props}))),Lp(zp(t),"tableFragments",np()),Lp(zp(t),"tableStyle",gs()),Lp(zp(t),"labelsAndIndices",Ve()),Lp(zp(t),"calculateTableStyle",(0,a.qe)((function(e){return i.Jnq(t.tableStyle(qp,e))}))),Lp(zp(t),"getLexerResult",(0,a.qe)(G.bind(void 0,Oa))),Lp(zp(t),"handleClick",(function(e){t.containsActiveElement()&&t.props.is_focused&&t.props.setProps({is_focused:!1});var n=t.menuRef;t.props.activeMenu&&n&&n.current&&!n.current.contains(e.target)&&t.props.setState({activeMenu:void 0})})),Lp(zp(t),"handleClipboardEvent",(function(e,n){t.containsActiveElement()&&n(e)})),Lp(zp(t),"handleCopy",(function(e){t.handleClipboardEvent(e,t.onCopy)})),Lp(zp(t),"handlePaste",(function(e){t.handleClipboardEvent(e,t.onPaste)})),Lp(zp(t),"resetFragmentCells",(function(e){var n=e.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *");n.length&&(Array.from(n).forEach(t.clearCellWidth),Array.from(e.querySelectorAll("table.cell-table > tbody > tr > th:first-of-type")).map((function(e){return e.parentElement})).forEach((function(e){var n=Array.from(null==e?void 0:e.children);n&&n.forEach(t.clearCellWidth)})))})),Lp(zp(t),"resizeFragmentCells",(function(e,n){var r=e.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *");r.length&&(Array.from(r).forEach((function(e,r){return t.setCellWidth(e,n[r])})),Array.from(e.querySelectorAll("table.cell-table > tbody > tr > th:first-of-type")).map((function(e){return e.parentElement})).forEach((function(e){var r=Array.from(null==e?void 0:e.children);r&&(r.length===n.length?r.forEach((function(e,r){return t.setCellWidth(e,n[r])})):r.forEach((function(e){return t.setCellWidth(e,0)})))})))})),Lp(zp(t),"resizeFragmentTable",(function(e,t){e&&(e.style.width=t)})),Lp(zp(t),"isDisplayed",(function(e){return"none"!==getComputedStyle(e).display})),Lp(zp(t),"forceHandleResize",(function(){return t.handleResize()})),Lp(zp(t),"getScrollbarWidthOnce",i.IHq(Z)),Lp(zp(t),"handleResizeIf",(0,a.qe)((function(){var e=t.refs,n=e.r0c0,r=e.r0c1,o=e.r1c0,i=e.r1c1;t.isDisplayed(i)&&(r.style.marginLeft="",i.style.marginLeft="",n.style.width="",o.style.width="",[n,r,o].forEach((function(e){var n=e.querySelector("table");n&&(n.style.width=""),t.resetFragmentCells(e)})),t.handleResize())}))),Lp(zp(t),"handleResize",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:NaN,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t.props,o=r.fixed_columns,i=r.fixed_rows,a=r.setState,s=t.refs,l=s.r1,c=s.r1c1;if(t.isDisplayed(c)){t.getScrollbarWidthOnce(l).then((function(e){return a({scrollbarWidth:e})}));var u=t.refs,p=u.r0c0,d=u.r0c1,f=u.r1c0,h=p.querySelector("table"),A=d.querySelector("table"),b=f.querySelector("table"),v=c.querySelector("table"),g=getComputedStyle(v).width;if(n||(t.resizeFragmentTable(h,g),t.resizeFragmentTable(A,g),t.resizeFragmentTable(b,g)),o||i){var y=Array.from(c.querySelectorAll("table.cell-table > tbody > tr:first-of-type > *")).map((function(e){return e.getBoundingClientRect().width}));n||(t.resizeFragmentCells(p,y),t.resizeFragmentCells(d,y),t.resizeFragmentCells(f,y))}if(o){var m=c.querySelector("tr:first-of-type > *:nth-of-type(".concat(o,")"));if(m){var w=m.getBoundingClientRect(),E=w.right-c.getBoundingClientRect().left;p.style.width="".concat(E,"px"),f.style.width="".concat(E,"px")}}var C=c.querySelector("tr:first-of-type > *:nth-of-type(".concat(o+1,")"));if(C){var k=c.getBoundingClientRect(),x=C.getBoundingClientRect(),O=x.left-k.left;d.style.marginLeft="-".concat(O+l.scrollLeft,"px"),c.style.marginLeft="-".concat(O,"px")}if(!n){var S=parseInt(g,10),B=parseInt(getComputedStyle(v).width,10);B!==S&&t.handleResize(S,B===e)}}})),Lp(zp(t),"handleKeyDown",(function(e){var n=t.props,r=n.setProps,o=n.is_focused;if(s.ZP.trace("handleKeyDown: ".concat(e.key)),i=e.keyCode,![h.CONTROL,h.COMMAND_LEFT,h.COMMAND_RIGHT,h.COMMAND_FIREFOX].includes(i)){var i,a=function(e){return(e.ctrlKey||e.metaKey)&&!e.altKey}(e);a&&e.keyCode===h.V||e.keyCode===h.C&&a&&!o||(e.keyCode!==h.ESCAPE?(!o&&v(e.keyCode)&&t.switchCell(e),o&&!v(e.keyCode)||(e.keyCode!==h.TAB&&e.keyCode!==h.ENTER?e.keyCode!==h.BACKSPACE&&e.keyCode!==h.DELETE||t.deleteCell(e):t.switchCell(e))):r({is_focused:!1}))}})),Lp(zp(t),"switchCell",(function(e){var n=e,r=t.props,o=r.active_cell,i=r.selected_cells,a=r.start_cell,l=r.end_cell,c=r.setProps,u=r.viewport,p=r.visibleColumns;if(e.preventDefault(),o){t.$el.focus();var d=i.length>1,f=n.keyCode===h.ENTER||n.keyCode===h.TAB;if(d&&f)c({is_focused:!1,active_cell:t.getNextCell(n,{currentCell:o,restrictToSelection:!0})});else if(n.shiftKey){var A=V(i),b=A.minRow,v=A.minCol,g=A.maxRow,y=A.maxCol,m=n.keyCode===h.ARROW_DOWN||n.keyCode===h.ENTER,w=n.keyCode===h.ARROW_UP,E=n.keyCode===h.ARROW_RIGHT||n.keyCode===h.TAB,C=n.keyCode===h.ARROW_LEFT,k=a&&a.row,x=a&&a.column,O=l&&l.row,S=l&&l.column;if(m)o.row>b?O=++b:g<u.data.length-1&&(O=++g);else if(w)o.row<g?O=--g:b>0&&(O=--b);else if(E)o.column>v?S=++v:y<p.length-1&&(S=++y);else{if(!C)return;o.column<y?S=--y:v>0&&(S=--v)}var B=K({minRow:b,maxRow:g,minCol:v,maxCol:y},p,u),_={is_focused:!1,end_cell:Y(O,S,p,u),selected_cells:B},j=O===b?g:b,P=S===v?y:v;k===j&&x===P||(_.start_cell=Y(j,P,p,u)),c(_)}else{var D=t.getNextCell(n,{currentCell:o,restrictToSelection:!1});c({is_focused:!1,selected_cells:[D],active_cell:D,start_cell:D,end_cell:D})}}else s.ZP.warning("Trying to change cell, but no cell is active.")})),Lp(zp(t),"deleteCell",(function(e){var n=t.props,r=n.data,o=n.selected_cells,a=n.setProps,s=n.viewport,l=n.visibleColumns;e.preventDefault();var c=r;i.UID((function(e){return[s.indices[e.row],e.column]}),o).forEach((function(e){var t=l[e[1]];if(t.editable){var n=Ie(null,t);c=i.t8m(i.QMA([e[0],t.id]),n.success?n.value:"",c)}})),a({data:c})})),Lp(zp(t),"getNextCell",(function(e,n){var r,o=n.restrictToSelection,a=n.currentCell,s=t.props,l=s.selected_cells,c=s.viewport,u=s.visibleColumns,p=e,d=a.row,f=a.column;switch(p.keyCode){case h.ARROW_LEFT:r=o?H([d,f-1],l):[d,i.Fp7(0,f-1)];break;case h.ARROW_RIGHT:case h.TAB:r=o?H([d,f+1],l):[d,i.VV$(u.length-1,f+1)];break;case h.ARROW_UP:r=o?H([d-1,f],l):[i.Fp7(0,d-1),f];break;case h.ARROW_DOWN:case h.ENTER:r=o?H([d+1,f],l):[i.VV$(c.data.length-1,d+1),f];break;default:throw new Error("Table.getNextCell: unknown navigation keycode ".concat(p.keyCode))}return Y(r[0],r[1],u,c)})),Lp(zp(t),"onCopy",(function(e){var n=t.props,r=n.selected_cells,o=n.viewport,i=n.columns,a=n.visibleColumns,s=n.include_headers_on_copy_paste;r.length&&qe.toClipboard(e,r,i,a,o.data,s),t.$el.focus()})),Lp(zp(t),"onPaste",(function(e){var n=t.props,r=n.active_cell,o=n.columns,i=n.data,a=n.editable,s=n.filter_query,l=n.loading_state,c=n.setProps,u=n.sort_by,p=n.viewport,d=n.visibleColumns,f=n.include_headers_on_copy_paste;if(a&&r&&!l){var h=qe.fromClipboard(e,r,p.indices,o,d,i,!0,!u.length||!s.length,f);h&&c(h)}})),Lp(zp(t),"handleDropdown",(function(){var e=t.refs.r1c1;We(e.querySelector(".Select-menu-outer"))})),Lp(zp(t),"onScroll",(function(e){var n=t.refs,r=n.r0c0,o=n.r0c1;s.ZP.trace("ControlledTable fragment scrolled to (left,top)=(".concat(e.target.scrollLeft,",").concat(e.target.scrollTop,")"));var i=parseFloat(e.target.scrollLeft)+(parseFloat(r.style.width)||0);o.style.marginLeft="".concat(-i,"px"),t.updateUiViewport(),t.handleDropdown(),t.adjustTooltipPosition()})),Lp(zp(t),"toggleColumn",(function(e,n,r){var o=t.props,a=o.columns,s=o.hidden_columns,l=o.setProps,c=S(e,a,n,r),u=s?s.slice(0):[];i.Ed_((function(e){var t=u.indexOf(e);t>=0?u.splice(t,1):u.push(e)}),c),l({hidden_columns:u})})),t.updateStylesheet(),t}return t=u,n=[{key:"lexerResult",get:function(){var e=this.props.filter_query;return this.getLexerResult(e)}},{key:"updateStylesheet",value:function(){var e=this,t=this.props.css;i.Ed_((function(t){var n=t.selector,r=t.rule;e.stylesheet.setRule(n,r)}),t)}},{key:"updateUiViewport",value:function(){var e=this.props,t=e.setState,n=e.uiViewport;if(e.virtualization){var r=this.refs.r1c1.parentElement;n&&n.scrollLeft===r.scrollLeft&&n.scrollTop===r.scrollTop&&n.height===r.clientHeight&&n.width===r.clientWidth||t({uiViewport:{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop,height:r.clientHeight,width:r.clientWidth}})}}},{key:"componentDidMount",value:function(){window.addEventListener("resize",this.forceHandleResize),document.addEventListener("mousedown",this.handleClick),document.addEventListener("paste",this.handlePaste),document.addEventListener("copy",this.handleCopy);var e=this.props,t=e.active_cell,n=e.selected_cells,r=e.setProps;n.length&&t&&!i.q9t(t,n)&&r({active_cell:n[0]}),this.updateUiViewport(),this.handleResize()}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.forceHandleResize),document.removeEventListener("mousedown",this.handleClick),document.removeEventListener("paste",this.handlePaste),document.removeEventListener("copy",this.handleCopy)}},{key:"componentDidUpdate",value:function(){this.updateStylesheet(),this.updateUiViewport();var e=this.props,t=e.fixed_columns,n=e.fixed_rows;(t||n)&&this.handleResizeIf.apply(this,Dp(i.VO0(this.props))),this.handleDropdown(),this.adjustTooltipPosition();var r=this.props.active_cell;if(this.containsActiveElement()){var o=this.getActiveCellAttributes();if(o&&r&&(o.column_id!==(null==r?void 0:r.column_id)||o.row!==(null==r?void 0:r.row))){var a=this.$el.querySelector('td[data-dash-row="'.concat(r.row,'"][data-dash-column="').concat(r.column_id,'"]:not(.phantom-cell)'));a&&a.focus()}}var s=this.props,l=s.setState,c=s.uiCell;if(s.virtualization&&!c){var u=this.refs.r1c1,p=u.querySelector("tr > td:first-of-type");if(p){var d=u.querySelectorAll("tr th:first-of-type");l({uiCell:{height:p.clientHeight},uiHeaders:i.UID((function(e){return{height:e.clientHeight}}),Array.from(d))})}}}},{key:"clearCellWidth",value:function(e){e.style.width="",e.style.minWidth="",e.style.maxWidth="",e.style.boxSizing=""}},{key:"$el",get:function(){return document.getElementById(this.props.id)}},{key:"containsActiveElement",value:function(){var e=this.$el;return e&&e.contains(document.activeElement)}},{key:"getActiveCellAttributes",value:function(){for(var e=document.activeElement;e&&"td"!==e.nodeName.toLowerCase();)e=e.parentElement;if(e){var t=e.getAttribute("data-dash-column"),n=e.getAttribute("data-dash-row");return{column_id:t,row:+(null!=n?n:0)}}}},{key:"displayPagination",get:function(){var e=this.props,t=e.data,n=e.page_action,r=e.page_size;return n===_.p9.Native&&r<t.length||n===_.p9.Custom}},{key:"render",value:function(){var e=this,t=this.props,n=t.columns,r=t.id,a=t.tooltip_conditional,s=t.tooltip,l=t.currentTooltip,c=t.fill_width,u=t.filter_action,p=t.fixed_columns,d=t.fixed_rows,f=t.loading_state,h=t.scrollbarWidth,A=t.style_as_list_view,b=t.style_table,v=t.tooltip_data,g=t.tooltip_delay,y=t.tooltip_duration,m=t.tooltip_header,w=t.uiCell,E=t.uiHeaders,C=t.uiViewport,k=t.viewport,x=t.virtualized,O=t.virtualization,S=t.visibleColumns,B=[[d&&p?"dash-fixed-row dash-fixed-column":"",d?"dash-fixed-row":""],[p?"dash-fixed-column":"","dash-fixed-content"]],j=this.tableFn(),P=this.tableFragments(p,d,j,x.offset.rows),D=P.grid,I=P.empty,F=["dash-spreadsheet"].concat(Dp(O?["dash-virtualized"]:[]),Dp(d?["dash-freeze-top"]:[]),Dp(p?["dash-freeze-left"]:[]),Dp(A?["dash-list-view"]:[]),Dp(I[0][1]?["dash-empty-01"]:[]),Dp(I[1][1]?["dash-empty-11"]:[]),Dp(S.length?[]:["dash-no-columns"]),Dp(x.data.length?[]:["dash-no-data"]),Dp(u.type!==_.p9.None?[]:["dash-no-filter"]),Dp(c?["dash-fill-width"]:[]),Dp(f?["dash-loading"]:[])),T=["dash-spreadsheet-container"].concat(Dp(F)),M=["dash-spreadsheet-inner"].concat(Dp(F)),R=this.calculateTableStyle(b),z=function(e,t,n,r,o,a,s){var l=[{},{fragment:{marginRight:s}}];if(!e||!t||!r)return[l,[{},{}]];var c=t.height*o.data.length,u=(Math.floor(r.scrollTop/t.height)-a.before)*t.height,p=i.Smz(i.UID((function(e){return e.height}),n||[])),d=e&&r&&t?Math.max(u-p,0):0;return[l,[{cell:{marginTop:d}},{fragment:{height:Math.max(c-d,0),marginTop:d}}]]}(O,w,E,C,k,x.padding.rows,h),N=lp(l,v,m,a,s,x,g,y),L=this.props,q=L.export_columns,U=L.export_format,V=L.export_headers,H=L.virtual,Y=L.merge_duplicate_headers,K=L.paginator,Z=L.page_current,Q=L.page_count,$={export_columns:q,export_format:U,virtual_data:H,columns:n,visibleColumns:S,export_headers:V,merge_duplicate_headers:Y};return o().createElement("div",{id:r,className:"dash-table-container",onKeyDown:this.handleKeyDown,onPaste:this.onPaste,style:{position:"relative"}},o().createElement(kp,{key:"tooltip",ref:"tooltip",className:"dash-table-tooltip",tooltip:N}),o().createElement("div",{className:"dash-spreadsheet-menu"},this.renderMenu(),o().createElement(W,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pp(Object(n),!0).forEach((function(t){Lp(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},$))),o().createElement("div",{className:T.join(" "),style:R},o().createElement("div",{ref:"table",className:M.join(" "),style:Wp},D.map((function(t,n){return o().createElement("div",{key:"r".concat(n),ref:"r".concat(n),className:"dt-table-container__row dt-table-container__row-".concat(n),onScroll:e.onScroll},function(e,t,r,i){for(var a=e.length,s=new Array(a),l=0;l<a;++l)s[l]=(c=e[l],u=t[l],p=r[l],d=l,o().createElement("div",{style:u.fragment,key:d,ref:"r".concat(n,"c").concat(d),className:"cell cell-".concat(n,"-").concat(d," ").concat(p)},c?o().cloneElement(c,{style:u.cell}):c));var c,u,p,d;return s}(t,z[n],B[n]))})))),this.displayPagination?o().createElement(jp,{paginator:K,page_current:Z,page_count:Q}):null)}},{key:"renderMenu",value:function(){var e=this;if(!this.showToggleColumns)return null;var t=this.props,n=t.activeMenu,r=t.columns,a=t.hidden_columns,s=t.merge_duplicate_headers,l=t.setState,c=this.labelsAndIndices(r,r,s),u=c.length-1;return o().createElement("div",{className:"dash-spreadsheet-menu-item",ref:this.menuRef},o().createElement("button",{className:"show-hide",onClick:function(){return l({activeMenu:"show/hide"===n?void 0:"show/hide"})}},"Toggle Columns"),"show/hide"!==n?null:o().createElement("div",{className:"show-hide-menu"},i.UWY(c.map((function(t,n){var i=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(s)throw o}}return i}}(e,t)||Ip(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t,2)[1];return i.map((function(t,l){var c=1===i.length,p=r[t],d=!a||a.indexOf(p.id)<0,f=Ue(n,u,p.hideable),h=c&&d||!f&&d;return{i:t,j:l,component:f?o().createElement("div",{className:"show-hide-menu-item"},o().createElement("input",{type:"checkbox",checked:d,disabled:h,onClick:e.toggleColumn.bind(e,p,n,s)}),o().createElement("label",null,p.name?"string"==typeof p.name?p.name:p.name.slice(0,n+1).filter((function(e){return 0!==e.length})).join(" | "):p.id)):null}}))}))).filter((function(e){return!i.kKJ(e)})).sort((function(e,t){return e.i-t.i||e.j-t.j})).map((function(e){return e.component}))))}},{key:"adjustTooltipPosition",value:function(){var e=this.props.currentTooltip;if(e){var t=e.id,n=e.row,r=e.header,o=this.refs,i=o.table;if(o.tooltip){var a=r?i.querySelector("tr:nth-of-type(".concat(n+1,') th[data-dash-column="').concat(t,'"]:not(.phantom-cell)')):i.querySelector('td[data-dash-column="'.concat(t,'"][data-dash-row="').concat(n,'"]:not(.phantom-cell)'));this.refs.tooltip.updateBounds(a)}}}},{key:"setCellWidth",value:function(e,t){"number"==typeof t&&(t="".concat(t,"px")),e.style.width=t,e.style.minWidth=t,e.style.maxWidth=t,e.style.boxSizing="border-box"}},{key:"showToggleColumns",get:function(){var e=this.props,t=e.columns,n=e.hidden_columns;return n&&n.length>0||i.YjB((function(e){return!!e.hideable}),t)}}],n&&Tp(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(r.PureComponent),Vp=n(3379),Hp=n.n(Vp),Yp=n(3380),Kp=n.n(Yp),Zp=n(569),Qp=n.n(Zp),$p=n(3565),Gp=n.n($p),Jp=n(9216),Xp=n.n(Jp),ed=n(4589),td=n.n(ed),nd=n(7288),rd={};rd.styleTagTransform=td(),rd.setAttributes=Gp(),rd.insert=Qp().bind(null,"head"),rd.domAPI=Kp(),rd.insertStyleElement=Xp(),Hp()(nd.Z,rd),nd.Z&&nd.Z.locals&&nd.Z.locals;var od=n(8220),id={};id.styleTagTransform=td(),id.setAttributes=Gp(),id.insert=Qp().bind(null,"head"),id.domAPI=Kp(),id.insertStyleElement=Xp(),Hp()(od.Z,id),od.Z&&od.Z.locals&&od.Z.locals;tu.add({prefix:"fas",iconName:"eraser",icon:[512,512,[],"f12d","M497.941 273.941c18.745-18.745 18.745-49.137 0-67.882l-160-160c-18.745-18.745-49.136-18.746-67.883 0l-256 256c-18.745 18.745-18.745 49.137 0 67.882l96 96A48.004 48.004 0 0 0 144 480h356c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12H355.883l142.058-142.059zm-302.627-62.627l137.373 137.373L265.373 416H150.628l-80-80 124.686-124.686z"]},{prefix:"far",iconName:"eye-slash",icon:[640,512,[],"f070","M634 471L36 3.51A16 16 0 0 0 13.51 6l-10 12.49A16 16 0 0 0 6 41l598 467.49a16 16 0 0 0 22.49-2.49l10-12.49A16 16 0 0 0 634 471zM296.79 146.47l134.79 105.38C429.36 191.91 380.48 144 320 144a112.26 112.26 0 0 0-23.21 2.47zm46.42 219.07L208.42 260.16C210.65 320.09 259.53 368 320 368a113 113 0 0 0 23.21-2.46zM320 112c98.65 0 189.09 55 237.93 144a285.53 285.53 0 0 1-44 60.2l37.74 29.5a333.7 333.7 0 0 0 52.9-75.11 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64c-36.7 0-71.71 7-104.63 18.81l46.41 36.29c18.94-4.3 38.34-7.1 58.22-7.1zm0 288c-98.65 0-189.08-55-237.93-144a285.47 285.47 0 0 1 44.05-60.19l-37.74-29.5a333.6 333.6 0 0 0-52.89 75.1 32.35 32.35 0 0 0 0 29.19C89.72 376.41 197.08 448 320 448c36.7 0 71.71-7.05 104.63-18.81l-46.41-36.28C359.28 397.2 339.89 400 320 400z"]},{prefix:"fas",iconName:"pencil-alt",icon:[512,512,[],"f303","M497.9 142.1l-46.1 46.1c-4.7 4.7-12.3 4.7-17 0l-111-111c-4.7-4.7-4.7-12.3 0-17l46.1-46.1c18.7-18.7 49.1-18.7 67.9 0l60.1 60.1c18.8 18.7 18.8 49.1 0 67.9zM284.2 99.8L21.6 362.4.4 483.9c-2.9 16.4 11.4 30.6 27.8 27.8l121.5-21.3 262.6-262.6c4.7-4.7 4.7-12.3 0-17l-111-111c-4.8-4.7-12.4-4.7-17.1 0zM124.1 339.9c-5.5-5.5-5.5-14.3 0-19.8l154-154c5.5-5.5 14.3-5.5 19.8 0s5.5 14.3 0 19.8l-154 154c-5.5 5.5-14.3 5.5-19.8 0zM88 424h48v36.3l-64.5 11.3-31.1-31.1L51.7 376H88v48z"]},{prefix:"fas",iconName:"sort",icon:[320,512,[],"f0dc","M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"]},{prefix:"fas",iconName:"sort-down",icon:[320,512,[],"f0dd","M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41z"]},{prefix:"fas",iconName:"sort-up",icon:[320,512,[],"f0de","M279 224H41c-21.4 0-32.1-25.9-17-41L143 64c9.4-9.4 24.6-9.4 33.9 0l119 119c15.2 15.1 4.5 41-16.9 41z"]},{prefix:"far",iconName:"trash-alt",icon:[448,512,[],"f2ed","M268 416h24a12 12 0 0 0 12-12V188a12 12 0 0 0-12-12h-24a12 12 0 0 0-12 12v216a12 12 0 0 0 12 12zM432 80h-82.41l-34-56.7A48 48 0 0 0 274.41 0H173.59a48 48 0 0 0-41.16 23.3L98.41 80H16A16 16 0 0 0 0 96v16a16 16 0 0 0 16 16h16v336a48 48 0 0 0 48 48h288a48 48 0 0 0 48-48V128h16a16 16 0 0 0 16-16V96a16 16 0 0 0-16-16zM171.84 50.91A6 6 0 0 1 177 48h94a6 6 0 0 1 5.15 2.91L293.61 80H154.39zM368 464H80V128h288zm-212-48h24a12 12 0 0 0 12-12V188a12 12 0 0 0-12-12h-24a12 12 0 0 0-12 12v216a12 12 0 0 0 12 12z"]},{prefix:"fas",iconName:"angle-left",icon:[256,512,[],"f104","M31.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L127.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L201.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34z"]},{prefix:"fas",iconName:"angle-right",icon:[256,512,[],"f105","M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"]},{prefix:"fas",iconName:"angle-double-left",icon:[448,512,[],"f100","M223.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L319.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L393.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34zm-192 34l136 136c9.4 9.4 24.6 9.4 33.9 0l22.6-22.6c9.4-9.4 9.4-24.6 0-33.9L127.9 256l96.4-96.4c9.4-9.4 9.4-24.6 0-33.9L201.7 103c-9.4-9.4-24.6-9.4-33.9 0l-136 136c-9.5 9.4-9.5 24.6-.1 34z"]},{prefix:"fas",iconName:"angle-double-right",icon:[448,512,[],"f101","M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34zm192-34l-136-136c-9.4-9.4-24.6-9.4-33.9 0l-22.6 22.6c-9.4 9.4-9.4 24.6 0 33.9l96.4 96.4-96.4 96.4c-9.4 9.4-9.4 24.6 0 33.9l22.6 22.6c9.4 9.4 24.6 9.4 33.9 0l136-136c9.4-9.2 9.4-24.4 0-33.8z"]});var ad=n(366),sd={};function ld(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function cd(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ud(e,t){return Math.ceil(e.length/t)}sd.styleTagTransform=td(),sd.setAttributes=Gp(),sd.insert=Qp().bind(null,"head"),sd.domAPI=Kp(),sd.insertStyleElement=Xp(),Hp()(ad.Z,sd),ad.Z&&ad.Z.locals&&ad.Z.locals;var pd=(0,a.Pi)((function(e,t,n,r,o,i){return e===_.p9.Native&&(r=ud(i,n)),r&&(r=Math.max(r,1)),function(e){if(null===e)return{loadNext(){},loadPrevious(){},loadFirst(){},loadLast(){},loadPage(){},hasPrevious(){return!0},hasNext(){return!0},isLast(){return!1},lastPage:void 0};var t=e.setProps,n=e.page_count,r=e.page_current;function o(){t(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ld(Object(n),!0).forEach((function(t){cd(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ld(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({page_current:r},B))}function i(e){e=Math.max(0,e),e=n?Math.min(n-1,e):e,r=e,o()}return n&&n-1<r&&(r=0,o()),{loadNext:function(){return i(r+1)},loadPrevious:function(){return i(r-1)},loadFirst:function(){return i(0)},loadPage:i,loadLast:function(){n&&i(n-1)},hasPrevious:function(){return 0!==r},hasNext:function(){return!n||r!==n-1},isLast:function(){return!!n&&r===n-1},lastPage:n?Math.max(0,n-1):void 0}}(e===_.p9.None?null:{setProps:o,page_current:t,page_count:r})})),dd=(0,a.Pi)((function(e,t){return e.map((function(e){return e.id})).filter((function(e){return-1!==t.indexOf(e)}))})),fd=(0,a.Pi)((function(e,t){var n=new Map;i.h0F(i.Ed_)((function(e,t){n.set(e,t)}),e);var r=[];return i.Ed_((function(e){var t=n.get(e);void 0!==t&&r.push(t)}),t),r})),hd=(0,a.Pi)((function(e,t,n,r,o){switch(e){case _.p9.None:return function(e,t){return{data:e,indices:t}}(r,o);case _.p9.Native:return function(e,t,n,r){var o=t*Math.min(e,ud(n,t)),i=Math.min(o+t,n.length);return{data:n.slice(o,i),indices:r.slice(o,i)}}(t,n,r,o);case _.p9.Custom:return function(e,t){return{data:e,indices:t}}(r,o);default:throw new Error("Unknown pagination mode: '".concat(e,"'"))}})),Ad=(0,a.Pi)((function(e,t,n,r,o){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],s=new Map;if(i.h0F(i.Ed_)((function(e,t){s.set(e,t)}),t),n.type===_.p9.Native){var l=new ja(r);t=l.isValid?l.filter(t):t}var c=function(t){var n=i.sEJ((function(e){return e.id===t}),e);return n&&n.sort_as_null||[]},u=function(e,t){return i.kKJ(e)||i.q9t(e,c(t))};o===_.p9.Native&&(t=Fl(t,a,u));var p=i.UID((function(e){return s.get(e)}),t);return{data:t,indices:p}}));function bd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bd(Object(n),!0).forEach((function(t){gd(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function gd(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var yd=(0,a.Pi)((function(e,t,n,r,o){if(!e)return vd(vd({},o),{},{offset:{rows:0,columns:0},padding:{rows:{before:0,after:0}}});if(!r||!t)return{data:o.data.slice(0,1),indices:o.indices.slice(0,1),offset:{rows:0,columns:0},padding:{rows:{before:0,after:0}}};var a=i.Smz(i.UID((function(e){return e.height}),n||[])),s=Math.max(r.scrollTop-a,0),l=Math.max(a-r.scrollTop,0),c=Math.floor(s/t.height),u=Math.ceil((r.height-l+s)/t.height),p=Math.min(c,1),d=Math.min(o.data.length-u,1);return c-=p,u+=d,{data:o.data.slice(c,u),indices:o.indices.slice(c,u),offset:{rows:c,columns:0},padding:{rows:{before:p,after:d}}}}));function md(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?md(Object(n),!0).forEach((function(t){Ed(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):md(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ed(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Cd=/^derived_/;function kd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function xd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Od(e,t){return Od=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Od(e,t)}function Sd(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Bd(e)}function Bd(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _d(e){return _d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_d(e)}function jd(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Pd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Od(e,t)}(c,e);var t,n,r,s,l=(r=c,s=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=_d(r);if(s){var n=_d(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Sd(this,e)});function c(e){var t,n,r,o,s,u,p,d,f,h,A,b,v,g,y,m,w;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),jd(Bd(t=l.call(this,e)),"__setProps",(0,a.qe)((function(e){return e?function(n){if(i.e$l("data",n)){var r=t.props.data;n.data_timestamp=Date.now(),n.data_previous=r}e(n)}:function(e){t.setState(e)}}))),jd(Bd(t),"__setState",(0,a.qe)((function(){return function(e){return t.setState(e)}}))),jd(Bd(t),"filterMap",kl()),jd(Bd(t),"controlledPropsHelper",(n=pd(),r=hd(),o=dd(),s=fd(),u=Ad(),p=fd(),d=yd(),function(e,t,a,l){var c=i.ATH(a,l),f=c.data,h=c.filter_query,A=c.filter_action,b=c.page_action,v=c.page_current,g=c.page_size,y=c.page_count,m=c.selected_columns,w=c.selected_rows,E=c.sort_action,C=c.sort_by,k=c.uiCell,x=c.uiHeaders,O=c.uiViewport,S=c.virtualization,B=c.visibleColumns,_=u(B,f,A,h,E,C),j=r(b,v,g,_.data,_.indices),P=d(S,k,x,O,j),D=p(_.indices,w),I=o(B,m),F=s(j.indices,w),T=n(b,v,g,y,e,_.data);return i.Jnq([a,l,{paginator:T,setProps:e,setState:t,viewport:j,viewport_selected_columns:I,viewport_selected_rows:F,virtual:_,virtual_selected_rows:D,virtualized:P}])})),jd(Bd(t),"updateDerivedProps",(f=(0,a.yw)((function(e){return e})),h=(0,a.yw)((function(e,t){return[e,t]})),A=(0,a.yw)((function(e){return e})),b=(0,a.yw)((function(e){return e})),v=(0,a.yw)((function(e){return e})),g=(0,a.yw)((function(e){return e})),y=(0,a.yw)((function(e){return e})),m=(0,a.yw)((function(e){return e})),w=(0,a.yw)((function(e){return new ja(e).toStructure()})),function(e,t){var n=e.filter_query,r=e.filter_action,o=e.page_action,a=e.page_current,s=e.page_size,l=e.sort_action,c=e.sort_by,u=e.viewport,p=e.viewport_selected_columns,d=e.viewport_selected_rows,E=e.virtual,C=e.virtual_selected_rows,k=w(n),x=b(u).cached,O=y(E).cached,S=v(p).cached,B=g(d).cached,j=m(C).cached,P=f(n),D=h(a,s),I=A(c),F=!P.cached&&!P.first&&r.type===_.p9.Custom||!D.cached&&!D.first&&o===_.p9.Custom||!I.cached&&!I.first&&l===_.p9.Custom,T={};k.cached||(T.derived_filter_query_structure=k.result),O||(T.derived_virtual_data=E.data,T.derived_virtual_indices=E.indices,T.derived_virtual_row_ids=i.jge("id",E.data)),x||(T.derived_viewport_data=u.data,T.derived_viewport_indices=u.indices,T.derived_viewport_row_ids=i.jge("id",u.data)),j||(T.derived_virtual_selected_rows=C,T.derived_virtual_selected_row_ids=i.UID((function(e){return E.data[e].id}),C)),S||(T.derived_viewport_selected_columns=p),B||(T.derived_viewport_selected_rows=d,T.derived_viewport_selected_row_ids=i.UID((function(e){return u.data[e].id}),d)),F&&(T.active_cell=void 0,T.selected_cells=[],T.start_cell=void 0,T.end_cell=void 0,T.selected_rows=[],T.selected_row_ids=[]),i.XPQ(T).length&&setTimeout((function(){return t(T)}),0)})),t.state={workFilter:{value:e.filter_query,map:t.filterMap(new Map,e.filter_action.operator,e.filter_query,e.visibleColumns)},rawFilterQuery:"",scrollbarWidth:0},t}return t=c,n=[{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=this;this.setState((function(n){var r=n.applyFocus,o=n.workFilter,a=o.map,s=o.value,l={};if(e.filter_query!==t.props.filter_query&&s!==e.filter_query){var c=t.filterMap(a,e.filter_action.operator,e.filter_query,e.visibleColumns);c!==a&&(l.workFilter={map:c,value:s})}if(e.active_cell!==t.props.active_cell)l.applyFocus=!0;else if(e.loading_state!==t.props.loading_state){var u=document.activeElement,p=In.getFirstParentOfType(u,"td"),d=In.getParentById(p,t.props.id);l.applyFocus=!!d}return l.applyFocus===r&&delete l.applyFocus,i.p8H(l).length?l:null}))}},{key:"shouldComponentUpdate",value:function(e,t){return function(e,t,n,r){return i.YjB((function(n){return!Cd.test(n)&&e[n]!==t[n]}),i.p8H(wd(wd({},e),t)))||!(0,cp.X)(n,r)}(this.props,e,this.state,t)}},{key:"render",value:function(){var e=this.controlledPropsHelper(this.controlledSetProps,this.controlledSetState,this.props,this.state);return this.updateDerivedProps(e,this.controlledSetProps),o().createElement(Up,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?kd(Object(n),!0).forEach((function(t){jd(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e))}},{key:"controlledSetProps",get:function(){return this.__setProps(this.props.setProps)}},{key:"controlledSetState",get:function(){return this.__setState()}}],n&&xd(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),c}(r.Component);function Dd(e){return e!==_.p9.Native}var Id=function(e){return function(e){var t=e.filter_action,n=e.sort_action;return e.page_action!==_.p9.Custom||Dd(t)&&Dd(n)}(e)?!!function(e){var t=e.columns;return i.kKJ(t)||!i.YjB((function(e){return e.format&&(e.format.symbol&&2!==e.format.symbol.length||e.format.grouping&&0===e.format.grouping.length||e.format.numerals&&10!==e.format.numerals.length)}))(t)}(e)||(s.ZP.error("Invalid column format"),!1):(s.ZP.error("Invalid combination of filter_action / sort_action / page_action"),!1)};function Fd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Td(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Md(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Td(Object(n),!0).forEach((function(t){Rd(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Td(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Rd(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var zd={symbol:["$",""],decimal:".",group:",",grouping:[3],percent:"%",separate_4digits:!0},Nd=[],Ld={case:_.oN.Sensitive,placeholder_text:"filter data..."},qd=function(e){return+e||0},Wd=function(e,t,n){return e.headers?m(t)+(n!==_.p9.None?1:0)+qd(e.data):0},Ud=function(e){return e.length>0?Object.keys(e[0]).map((function(e){return new _.sg({name:e,id:e})})):[]},Vd=function(e,t,n,r,o){return i.UID((function(n){var a,s=i.d9v(n);return s.editable=function(e,t){return i.kKJ(t)?e:t}(r,n.editable),s.filter_options=Md(Md(Md({},Ld),null!=o?o:{}),null!==(a=s.filter_options)&&void 0!==a?a:{}),s.sort_as_null=s.sort_as_null||t,s.type===_.QD.Numeric&&s.format&&(s.format.locale=Qd(e,s.format.locale),s.format.nully=Gd(s.format.nully),s.format.specifier=$d(s.format.specifier)),s}),n)},Hd=function(e){return Qd(e)},Yd=function(e){var t,n;return"object"==typeof e?{type:null!==(t=e.type)&&void 0!==t?t:_.p9.None,operator:null!==(n=e.operator)&&void 0!==n?n:_.J2.And}:{type:e,operator:_.J2.And}},Kd=function(e,t){return i.hXT((function(e){return!t||t.indexOf(e.id)<0}),e)},Zd=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Rd(this,"populateColumnsFrom",(0,a.qe)(Ud)),Rd(this,"applyDefaultToLocale",(0,a.qe)(Hd)),Rd(this,"applyDefaultsToColumns",(0,a.qe)(Vd)),Rd(this,"getFilterAction",(0,a.qe)(Yd)),Rd(this,"getVisibleColumns",(0,a.qe)(Kd))}var t,n;return t=e,n=[{key:"sanitize",value:function(e){var t,n=this.applyDefaultToLocale(e.locale_format),r=null!==(t=e.data)&&void 0!==t?t:[],o=e.columns?this.applyDefaultsToColumns(n,e.sort_as_null,e.columns,e.editable,e.filter_options):this.populateColumnsFrom(r),a=this.getVisibleColumns(o,e.hidden_columns),s=e.export_headers;e.export_format===_.T2.Xlsx&&i.kKJ(s)?s=_.CR.Names:e.export_format===_.T2.Csv&&i.kKJ(s)&&(s=_.CR.Ids);var l,c,u,p,d=e.cell_selectable?e.active_cell:void 0,f=e.cell_selectable?e.selected_cells:Nd;return i.ATH(e,{active_cell:d,columns:o,data:r,export_headers:s,filter_action:this.getFilterAction(e.filter_action),fixed_columns:(c=e.fixed_columns,u=e.row_deletable,p=e.row_selectable,c.headers?(u?1:0)+(p?1:0)+qd(c.data):0),fixed_rows:Wd(e.fixed_rows,o,e.filter_action),loading_state:(l=e.loading_state,!(!l||!l.is_loading||"data"!==l.prop_name&&""!==l.prop_name&&void 0!==l.prop_name)),locale_format:n,selected_cells:f,visibleColumns:a})}}],n&&Fd(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Qd=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.Jnq([zd].concat(t))},$d=function(e){return void 0===e?"":e},Gd=function(e){return void 0===e?"":e},Jd=n(8609);function Xd(){return Xd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xd.apply(this,arguments)}function ef(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function tf(e,t){return tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},tf(e,t)}function nf(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function rf(e){return rf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},rf(e)}var of=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tf(e,t)}(s,e);var t,n,r,i,a=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=rf(r);if(i){var n=rf(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return nf(this,e)});function s(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(t=a.call(this,e)).getId=function(){return n=n||function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36;return e+Math.random().toString(t).substring(2)}("table-")},t.sanitizer=new Zd,t}return t=s,(n=[{key:"render",value:function(){if(!Id(this.props))return o().createElement("div",null,"Invalid props combination");var e=this.sanitizer.sanitize(this.props);return this.props.id?o().createElement(Pd,e):o().createElement(Pd,Xd({},e,{id:this.getId()}))}}])&&ef(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.Component);of.defaultProps=Jd.lG,of.propTypes=Jd.iG},4184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&e.push(a)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var s in n)r.call(n,s)&&n[s]&&e.push(s)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},8220:function(e,t,n){"use strict";var r=n(7537),o=n.n(r),i=n(3645),a=n.n(i)()(o());a.push([e.id,".dash-spreadsheet.dash-freeze-left,\n.dash-spreadsheet.dash-freeze-top {\n  width: auto;\n  width: fit-content;\n  width: -moz-fit-content;\n  width: -webkit-fit-content;\n}\n.dash-spreadsheet.dash-freeze-left {\n  max-width: 500px;\n}\n.dash-spreadsheet.dash-freeze-top,\n.dash-spreadsheet.dash-virtualized {\n  max-height: 500px;\n}\n.dash-tooltip {\n  border: 1px solid #e4e4e4;\n  border-radius: 5px;\n  position: absolute;\n  z-index: 500;\n}\n.dash-tooltip .dash-table-tooltip {\n  position: relative;\n  background-color: #f6f6f6;\n  max-width: 300px;\n  min-width: 300px;\n  padding: 2px 10px;\n}\n.dash-tooltip[data-attr-anchor='top'] {\n  margin-top: 10px;\n}\n.dash-tooltip[data-attr-anchor='top']:after,\n.dash-tooltip[data-attr-anchor='top']:before {\n  bottom: 100%;\n  left: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n.dash-tooltip[data-attr-anchor='top']:after {\n  border-color: transparent;\n  border-bottom-color: #f6f6f6;\n  border-width: 8px;\n  margin-left: -8px;\n}\n.dash-tooltip[data-attr-anchor='top']:before {\n  border-color: transparent;\n  border-bottom-color: #e4e4e4;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.dash-tooltip[data-attr-anchor='bottom'] {\n  margin-bottom: 10px;\n}\n.dash-tooltip[data-attr-anchor='bottom']:after,\n.dash-tooltip[data-attr-anchor='bottom']:before {\n  top: 100%;\n  left: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n.dash-tooltip[data-attr-anchor='bottom']:after {\n  border-color: transparent;\n  border-top-color: #f6f6f6;\n  border-width: 8px;\n  margin-left: -8px;\n}\n.dash-tooltip[data-attr-anchor='bottom']:before {\n  border-color: transparent;\n  border-top-color: #e4e4e4;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.dash-spreadsheet-menu {\n  display: flex;\n  flex-direction: row;\n}\n.dash-spreadsheet-menu > * {\n  padding-right: 5px;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item {\n  position: relative;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu {\n  background-color: #fafafa;\n  border: 1px solid #d3d3d3;\n  display: flex;\n  flex-direction: column;\n  max-height: 300px;\n  overflow: auto;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 500;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu .show-hide-menu-item {\n  display: flex;\n  flex-direction: row;\n  padding: 5px;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu .show-hide-menu-item label {\n  white-space: nowrap;\n}\n.dash-table-container .previous-next-container {\n  text-align: right;\n  padding: 5px 0px;\n}\n.dash-table-container .previous-next-container .page-number {\n  font-family: monospace;\n  display: inline-block;\n}\n.dash-table-container .previous-next-container .page-number .last-page {\n  display: inline-block;\n  text-align: center;\n  padding: 1px 2px;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container {\n  display: inline-block;\n  position: relative;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page {\n  display: inline-block;\n  border-bottom: solid lightgrey 1px !important;\n  color: black;\n  border: none;\n  text-align: center;\n  font-family: monospace;\n  font-size: 10pt;\n  padding: 1px 2px;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow::placeholder,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page::placeholder {\n  color: black;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow:focus,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page:focus {\n  outline: none;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow:focus::placeholder,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page:focus::placeholder {\n  opacity: 0;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.dash-table-container .previous-next-container button.previous-page,\n.dash-table-container .previous-next-container button.next-page,\n.dash-table-container .previous-next-container button.first-page,\n.dash-table-container .previous-next-container button.last-page {\n  transition-duration: 400ms;\n  padding: 5px;\n  border: none;\n  display: inline-block;\n  margin-left: 5px;\n  margin-right: 5px;\n}\n.dash-table-container .previous-next-container button.previous-page:hover,\n.dash-table-container .previous-next-container button.next-page:hover,\n.dash-table-container .previous-next-container button.first-page:hover,\n.dash-table-container .previous-next-container button.last-page:hover {\n  color: hotpink;\n}\n.dash-table-container .previous-next-container button.previous-page:hover:disabled,\n.dash-table-container .previous-next-container button.next-page:hover:disabled,\n.dash-table-container .previous-next-container button.first-page:hover:disabled,\n.dash-table-container .previous-next-container button.last-page:hover:disabled {\n  color: graytext;\n}\n.dash-table-container .previous-next-container button.previous-page:focus,\n.dash-table-container .previous-next-container button.next-page:focus,\n.dash-table-container .previous-next-container button.first-page:focus,\n.dash-table-container .previous-next-container button.last-page:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container {\n  /* The \"normal\" reset CSS */\n  /* The \"modified\" reset CSS applied to the table to ignore markdown cells */\n  display: flex;\n  flex-direction: row;\n  position: relative;\n  line-height: initial;\n  /* focus happens after copying to clipboard */\n}\n.dash-table-container .dash-spreadsheet-container th {\n  font-style: normal;\n  font-weight: normal;\n  text-align: left;\n}\n.dash-table-container .dash-spreadsheet-container th,\n.dash-table-container .dash-spreadsheet-container td {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown),\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dl,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dl,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dt,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dt,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dd,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dd,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) li,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) li,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) pre,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) pre,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) form,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) form,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) p,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) p,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) blockquote,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) blockquote,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) td,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) td {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) table,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) table {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) img,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) img {\n  border: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) address,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) address,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) cite,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) cite,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dfn,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dfn,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) em,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) em,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) strong,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) strong,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) var,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) var {\n  font-style: normal;\n  font-weight: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ul {\n  list-style: none;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th {\n  text-align: left;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h6 {\n  font-size: 100%;\n  font-weight: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) q:before,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) q:before,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) q:after,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) q:after {\n  content: '';\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) abbr,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) abbr,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) acronym,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) acronym {\n  border: 0;\n  font-variant: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) sup,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) sup {\n  vertical-align: text-top;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) sub,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) sub {\n  vertical-align: text-bottom;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) select,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) select {\n  font-family: inherit;\n  font-size: inherit;\n  font-weight: inherit;\n  *font-size: 100%;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) legend {\n  color: #000;\n}\n.dash-table-container .dash-spreadsheet-container input[type=\"button\"] {\n  border-radius: 0;\n  -webkit-appearance: none;\n}\n.dash-table-container .dash-spreadsheet-container *:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container table {\n  font-size: inherit;\n  pointer-events: none;\n}\n.dash-table-container .dash-spreadsheet-container table td,\n.dash-table-container .dash-spreadsheet-container table th {\n  pointer-events: initial;\n}\n.dash-table-container .dash-spreadsheet-container input[type=\"radio\"] {\n  margin: initial;\n  line-height: initial;\n  box-sizing: initial;\n  padding: initial;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner {\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  /*\n             * fixes Firefox td height bug on td > dropdown children\n             * bug should only appear on FF but\n             * @supports = scoped to Firefox only\n             * to minimize side effects\n             */\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *:after,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *:before {\n  box-sizing: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select {\n  overflow: hidden;\n  position: static;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select-control {\n  background-color: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select-value {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin-top: -2px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row tr {\n  visibility: hidden !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row th {\n  height: 0 !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter input::placeholder {\n  color: inherit;\n  font-size: 0.8em;\n  padding-right: 5px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter + .dash-filter:not(:hover):not(:focus-within) input::placeholder {\n  color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter.invalid {\n  background-color: pink;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-11) .dt-table-container__row-0 tr:last-of-type td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-11) .dt-table-container__row-0 tr:last-of-type th {\n  border-bottom: none !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-0-0 tr td:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-1-0 tr td:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-0-0 tr th:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-1-0 tr th:last-of-type {\n  border-right: none !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 {\n  overflow: hidden;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-0 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 th.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-0 th.phantom-cell {\n  border-color: transparent !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-1 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-1 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-1 th.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-1 th.phantom-cell {\n  border-color: transparent inherit transparent transparent !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized {\n  overflow: hidden !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .dt-table-container__row-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .dt-table-container__row-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .dt-table-container__row-0 {\n  display: flex;\n  flex: 0 0 auto;\n  flex-direction: row;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .dt-table-container__row-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .dt-table-container__row-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .dt-table-container__row-1 {\n  display: flex;\n  flex-direction: row;\n  overflow: auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-1-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-1-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-1-0 {\n  flex: 0 0 auto;\n  left: 0;\n  position: sticky;\n  position: -webkit-sticky;\n  z-index: 400;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-0-1 {\n  z-index: 300;\n  flex: 0 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-1-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-1-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-1-1 {\n  flex: 0 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell-1-1 {\n  flex: 1 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell table {\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td {\n  background-color: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.focused {\n  margin: -1px;\n  z-index: 200;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value-container {\n  width: 100%;\n  height: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-input-cell-value-container {\n  position: relative;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value {\n  height: 100%;\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value.unfocused.selectable::selection {\n  background-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value.unfocused {\n  caret-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td input.dash-cell-value {\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .cell-value-shadow {\n  margin: auto 0;\n  opacity: 0;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .input-cell-value-shadow {\n  display: inline-block;\n  height: initial;\n  width: initial;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dropdown-cell-value-shadow {\n  display: block;\n  height: 0px;\n  padding: 0 42px 0 10px;\n}\n@supports (-moz-appearance:none) {\n  .dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.dropdown .dash-cell-value-container {\n    height: auto;\n  }\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter {\n  position: relative;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input {\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input.dash-filter--case {\n  position: relative;\n  left: auto;\n  top: auto;\n  width: auto;\n  height: 16px;\n  line-height: 0px;\n  padding: 1px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input.dash-filter--case--sensitive {\n  border-color: hotpink;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 2px;\n  color: hotpink;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  white-space: nowrap;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--clear,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--delete,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--edit,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--hide,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--sort {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  cursor: default;\n  cursor: pointer;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner tr {\n  min-height: 30px;\n  height: 30px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  background-clip: padding-box;\n  padding: 2px;\n  overflow-x: hidden;\n  white-space: nowrap;\n  box-sizing: border-box;\n  text-align: right;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.phantom-cell {\n  visibility: hidden;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value {\n  display: inline;\n  vertical-align: middle;\n  white-space: inherit;\n  overflow: inherit;\n  text-overflow: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value.cell-markdown,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value.cell-markdown {\n  text-align: left;\n  font-family: sans-serif;\n  display: inline-block;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value.cell-markdown blockquote,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value.cell-markdown blockquote {\n  white-space: pre;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner textarea {\n  white-space: pre;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner table {\n  border-collapse: collapse;\n  font-family: monospace;\n  --accent: hotpink;\n  --border: lightgrey;\n  --text-color: #3c3c3c;\n  --hover: #fdfdfd;\n  --background-color-ellipses: #fdfdfd;\n  --faded-text: #fafafa;\n  --faded-text-header: #b4b4b4;\n  --selected-background: rgba(255, 65, 54, 0.2);\n  --faded-dropdown: #f0f0f0;\n  --muted: #c8c8c8;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner table:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner thead {\n  display: table-row-group;\n}\n.dash-table-container .dash-spreadsheet-container .elip {\n  text-align: center;\n  width: 100%;\n  background-color: var(--background-color-ellipses);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.dropdown {\n  /*\n             * To view the dropdown's contents, we need\n             * overflow-y: visible.\n             * Unfortunately, overflow-x: hidden and overflow-y: visible\n             * can't both be set at the same time.\n             * So, we have to make both overflow-x: visible and overflow-y: visble\n             *\n             * See https://stackoverflow.com/questions/6421966/\n             *\n             * There might be another solution with parent divs, but I haven't\n             * tried it.\n             */\n  overflow-x: visible;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner :not(.cell--selected) tr:hover,\n.dash-table-container .dash-spreadsheet-container tr:hover input :not(.cell--selected) {\n  background-color: var(--hover);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  background-color: #fafafa;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td {\n  background-color: white;\n}\n.dash-table-container .dash-spreadsheet-container .expanded-row--empty-cell {\n  background-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .expanded-row {\n  text-align: center;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input:not([type=radio]):not([type=checkbox]) {\n  padding: 0px;\n  margin: 0px;\n  height: calc(100% - 1px);\n  line-height: 30px;\n  border: none;\n  font-family: inherit;\n  text-align: right;\n  box-sizing: border-box;\n  color: var(--text-color);\n  background-color: transparent;\n  /* so as to not overlay the box shadow */\n  /* browser's default text-shadow is `$color 0px 0px 0px;`\n             * for `input`, which makes it look a little bit heavier than dropdowns\n             * or bare `td`\n             */\n  text-shadow: none;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input.unfocused {\n  color: transparent;\n  text-shadow: 0 0 0 var(--text-color);\n  cursor: default;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input.unfocused:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container .toggle-row {\n  border: none;\n  box-shadow: none;\n  width: 10px;\n  padding-left: 10px;\n  padding-right: 10px;\n  cursor: pointer;\n  color: var(--faded-text);\n}\n.dash-table-container .dash-spreadsheet-container .toggle-row--expanded {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner tr:hover .toggle-row {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-header {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  cursor: default;\n  font-size: 1.3rem;\n  text-align: center;\n  cursor: pointer;\n  color: var(--muted);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-cell:hover,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-header:hover {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div {\n  display: flex;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div input[type=\"text\"],\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div input[type=\"text\"] {\n  flex: 1;\n  line-height: unset;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div input[type=\"text\"]::placeholder,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div input[type=\"text\"]::placeholder {\n  font-size: 0.9em;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div {\n  flex-direction: row-reverse;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-actions {\n  display: flex;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header-name {\n  flex-grow: 1;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner [class^='column-header--'],\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner [class^='dash-filter--'] {\n  cursor: pointer;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--select {\n  height: auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--select,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--sort {\n  color: var(--faded-text-header);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter--case,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--clear,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--delete,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--edit,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--hide {\n  opacity: 0.1;\n  padding-left: 2px;\n  padding-right: 2px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th:hover [class^='column-header--']:not(.disabled),\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th:hover [class^='dash-filter--']:not(.disabled) {\n  color: var(--accent);\n  opacity: 1;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter--case {\n  font-size: 10px;\n}\n","",{version:3,sources:["webpack://./src/dash-table/components/Table/Table.less","webpack://./src/dash-table/style/reset.less"],names:[],mappings:"AAoBI;;EAjBA,WAAA;EACA,kBAAA;EACA,uBAAA;EACA,0BAAA;AAAJ;AAmBI;EACI,gBAAA;AAjBR;AAoBI;;EAEI,iBAAA;AAlBR;AAsBA;EACI,yBAAA;EACA,kBAAA;EACA,kBAAA;EACA,YAAA;AApBJ;AAgBA;EAOQ,kBAAA;EACA,yBAAA;EACA,gBAAA;EACA,gBAAA;EACA,iBAAA;AApBR;AAuBI;EACI,gBAAA;AArBR;AAuBQ;;EACI,YAAA;EACA,SAAA;EACA,yBAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,oBAAA;AApBZ;AAuBQ;EACI,yBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;AArBZ;AAwBQ;EACI,yBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;AAtBZ;AA0BI;EACI,mBAAA;AAxBR;AA0BQ;;EACI,SAAA;EACA,SAAA;EACA,yBAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,oBAAA;AAvBZ;AA0BQ;EACI,yBAAA;EACA,yBAAA;EACA,iBAAA;EACA,iBAAA;AAxBZ;AA0BQ;EACI,yBAAA;EACA,yBAAA;EACA,iBAAA;EACA,iBAAA;AAxBZ;AA6BA;EACI,aAAA;EACA,mBAAA;AA3BJ;AA6BI;EACI,kBAAA;AA3BR;AAsBA;EASQ,kBAAA;AA5BR;AAmBA;EAYY,yBAAA;EACA,yBAAA;EACA,aAAA;EACA,sBAAA;EACA,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,SAAA;EACA,OAAA;EACA,YAAA;AA5BZ;AAOA;EAwBgB,aAAA;EACA,mBAAA;EACA,YAAA;AA5BhB;AAEA;EA6BoB,mBAAA;AA5BpB;AAmCA;EAEQ,iBAAA;EACA,gBAAA;AAlCR;AA+BA;EAMY,sBAAA;EACA,qBAAA;AAlCZ;AA2BA;EAUgB,qBAAA;EACA,kBAAA;EACA,gBAAA;AAlChB;AAsBA;EAgBgB,qBAAA;EACA,kBAAA;AAnChB;AAkBA;;EAqBoB,qBAAA;EACA,6CAAA;EACA,YAAA;EACA,YAAA;EACA,kBAAA;EACA,sBAAA;EACA,eAAA;EACA,gBAAA;AAnCpB;AAqCoB;;EACI,YAAA;AAlCxB;AAqCoB;;EACI,aAAA;AAlCxB;AAoCwB;;EACI,UAAA;AAjC5B;AALA;EA4CoB,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;AApCpB;AAZA;;;;EAsDY,0BAAA;EACA,YAAA;EACA,YAAA;EACA,qBAAA;EACA,gBAAA;EACA,iBAAA;AApCZ;AAsCY;;;;EACI,cAAA;AAjChB;AAmCgB;;;;EACI,eAAA;AA9BpB;AAkCY;;;;EACI,aAAA;AA7BhB;AAzCA;EA2CE,2BAA2B;EAC3B,2EAA2E;EAiCrE,aAAA;EACA,mBAAA;EACA,kBAAA;EAKA,oBAAA;EAnCN,6CAA6C;AAC/C;AAlDA;ECxIQ,kBAAA;EACA,mBAAA;EACA,gBAAA;AD6LR;AAvDA;;EClIQ,SAAA;EACA,UAAA;AD6LR;AC3LQ;;EACI,SAAA;EACA,UAAA;AD8LZ;AChMQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAIwG,SAAA;EAAS,UAAA;AD6OzH;ACjPQ;;EAKU,yBAAA;EAAyB,iBAAA;ADiP3C;ACtPQ;;;;EAMiB,SAAA;ADsPzB;AC5PQ;;;;;;;;;;;;;;;;;;EAOmD,kBAAA;EAAkB,mBAAA;AD0Q7E;ACjRQ;;;;EAQU,gBAAA;AD+QlB;ACvRQ;;;;EASe,gBAAA;ADoRvB;AC7RQ;;;;;;;;;;;;EAUsB,eAAA;EAAe,mBAAA;ADkS7C;AC5SQ;;;;EAWqB,WAAA;ADuS7B;AClTQ;;;;EAYiB,SAAA;EAAS,oBAAA;AD6SlC;ACzTQ;;EAaQ,wBAAA;ADgThB;AC7TQ;;EAcQ,2BAAA;ADmThB;ACjUQ;;;;;;EAe0B,oBAAA;EAAoB,kBAAA;EAAkB,oBAAA;GAAoB,eAAA;AD6T5F;AC5UQ;;EAgBW,WAAA;ADgUnB;AAjNA;ECxGQ,gBAAA;EACA,wBAAA;AD4TR;AArNA;ECnGQ,aAAA;AD2TR;AAxNA;EA0FY,kBAAA;EACA,oBAAA;AAiIZ;AA5NA;;EA8FgB,uBAAA;AAkIhB;AAhOA;EAsGY,eAAA;EACA,oBAAA;EAIA,mBAAA;EACA,gBAAA;AA0HZ;AAtOA;EAgHY,sBAAA;EACA,aAAA;EACA,sBAAA;EAyHV;;;;;cAKY;AACd;AAjPA;;;EAuHU,mBAAA;AA+HV;AAtPA;EA2HgB,gBAAA;EACA,gBAAA;AA8HhB;AA1PA;;EAiIU,yBAAA;AA6HV;AA9PA;EAqIgB,aAAA;EACA,sBAAA;EACA,uBAAA;EACA,gBAAA;AA4HhB;AApQA;EA6IoB,6BAAA;AA0HpB;AAvQA;;EAiJoB,oBAAA;EACA,qBAAA;EACA,oBAAA;AA0HpB;AA7QA;EAyJoB,cAAA;EACA,gBAAA;EACA,kBAAA;AAuHpB;AAnHoB;EAEQ,kBAAA;AAoH5B;AA/GgB;EACI,sBAAA;AAiHpB;AA7GY;;EAIgB,8BAAA;AA6G5B;AAlG4B;;;;EACI,6BAAA;AAuGhC;AAlSA;EAmMgB,gBAAA;AAkGhB;AA5FoB;;;;EACI,oCAAA;AAiGxB;AAzFoB;;;;EACI,oEAAA;AA8FxB;AAzFY;;;EAGI,2BAAA;AA2FhB;AA9FY;;;EAMQ,aAAA;EACA,cAAA;EACA,mBAAA;AA6FpB;AArGY;;;EAYQ,aAAA;EACA,mBAAA;EACA,cAAA;AA8FpB;AA5GY;;;;;;EAmBQ,cAAA;EACA,OAAA;EACA,gBAAA;EACA,wBAAA;EACA,YAAA;AAiGpB;AAxHY;;;EA2BQ,YAAA;EACA,cAAA;AAkGpB;AA9HY;;;EAgCQ,cAAA;AAmGpB;AA/FY;;EAGQ,cAAA;AAgGpB;AAnGY;EAQY,WAAA;AA8FxB;AAlWA;EA0QgB,yBAAA;AA2FhB;AAzFgB;EACI,YAAA;EACA,YAAA;AA2FpB;AAzWA;EAkRoB,WAAA;EACA,YAAA;AA0FpB;AA7WA;EAuRoB,kBAAA;AAyFpB;AAhXA;EA2RoB,YAAA;EACA,WAAA;AAwFpB;AAtFoB;EACI,6BAAA;AAwFxB;AArFoB;EACI,wBAAA;AAuFxB;AA1XA;EAwSoB,kBAAA;EACA,OAAA;EACA,MAAA;AAqFpB;AA/XA;EA8SoB,cAAA;EACA,UAAA;AAoFpB;AAnYA;EAmToB,qBAAA;EACA,eAAA;EACA,cAAA;AAmFpB;AAxYA;EAyToB,cAAA;EACA,WAAA;EACA,sBAAA;AAkFpB;AAxEY;EAAA;IAEQ,YAAA;EA0ElB;AACF;AAlZA;EA4UgB,kBAAA;AAyEhB;AAvEgB;EACI,OAAA;EACA,MAAA;EACA,YAAA;EACA,WAAA;AAyEpB;AAvEoB;EACI,kBAAA;EACA,UAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,YAAA;AAyExB;AAvEoB;EACI,qBAAA;EACA,kBAAA;EACA,mBAAA;EACA,iBAAA;EACA,cAAA;AAyExB;AA3aA;EAwWgB,mBAAA;AAsEhB;AA9aA;;;;;EArII,2BAAA;EACA,yBAAA;EACA,wBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,eAAA;EA+egB,eAAA;AA4EpB;AA5bA;EAqXgB,gBAAA;EACA,YAAA;AA0EhB;AAhcA;;EA2XgB,4BAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,sBAAA;EAEA,iBAAA;AAwEhB;AAtEgB;;EACI,kBAAA;AAyEpB;AA7cA;;EAwYoB,eAAA;EACA,sBAAA;EACA,oBAAA;EACA,iBAAA;EACA,sBAAA;AAyEpB;AAvEoB;;EACI,gBAAA;EACA,uBAAA;EACA,qBAAA;AA0ExB;AA7EoB;;EAMQ,gBAAA;AA2E5B;AA/dA;EA4ZY,gBAAA;AAsEZ;AAleA;EAgaY,yBAAA;EAEA,sBAAA;EACA,iBAAA;EACA,mBAAA;EACA,qBAAA;EACA,gBAAA;EACA,oCAAA;EACA,qBAAA;EACA,4BAAA;EACA,6CAAA;EACA,yBAAA;EACA,gBAAA;AAoEZ;AAhfA;EAibY,aAAA;AAkEZ;AAnfA;EAqbY,wBAAA;AAiEZ;AAtfA;EAybY,kBAAA;EACA,WAAA;EACA,kDAAA;AAgEZ;AA3fA;EA6fE;;;;;;;;;;;cAWY;EA7DF,mBAAA;AA+DZ;AA1gBA;;EAgdY,8BAAA;AA8DZ;AA9gBA;EAodY,yBAAA;AA6DZ;AAjhBA;EAwdY,uBAAA;AA4DZ;AAphBA;EA4dY,6BAAA;AA2DZ;AAvhBA;EAgeY,kBAAA;AA0DZ;AA1hBA;EAoeY,YAAA;EACA,WAAA;EACA,wBAAA;EACA,iBAAA;EACA,YAAA;EACA,oBAAA;EACA,iBAAA;EACA,sBAAA;EACA,wBAAA;EACA,6BAAA;EAyDV,wCAAwC;EACxC;;;cAGY;EAvDF,iBAAA;AAyDZ;AA5iBA;EAufY,kBAAA;EACA,oCAAA;EACA,eAAA;AAwDZ;AAjjBA;EA6fY,aAAA;AAuDZ;AApjBA;EAigBY,YAAA;EACA,gBAAA;EACA,WAAA;EACA,kBAAA;EACA,mBAAA;EACA,eAAA;EACA,wBAAA;AAsDZ;AA7jBA;EA2gBY,oBAAA;AAqDZ;AAhkBA;EA+gBY,oBAAA;AAoDZ;AAnkBA;;EArII,2BAAA;EACA,yBAAA;EACA,wBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,eAAA;EAqpBQ,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,mBAAA;AAwDZ;AAjlBA;;EA6hBY,oBAAA;AAwDZ;AArlBA;;EAmiBgB,aAAA;AAsDhB;AAzlBA;;EAsiBoB,OAAA;EACA,kBAAA;AAuDpB;AAtDoB;;EACI,gBAAA;AAyDxB;AAlmBA;EA8iBgB,2BAAA;AAuDhB;AArmBA;EAijBgB,aAAA;AAuDhB;AAxmBA;EAqjBgB,YAAA;AAsDhB;AA3mBA;;EAyjBgB,eAAA;AAsDhB;AA/mBA;EA6jBU,YAAA;AAqDV;AAlnBA;;EAkkBU,+BAAA;AAoDV;AAtnBA;;;;;EA2kBgB,YAAA;EACA,iBAAA;EACA,kBAAA;AAkDhB;AA7CoB;;EACI,oBAAA;EACA,UAAA;AAgDxB;AApoBA;EA0lBgB,eAAA;AA6ChB",sourcesContent:["@import (reference) '~dash-table/style/reset.less';\n\n.fit-content-polyfill() {\n    width: auto; // MS Edge, IE\n    width: fit-content; // Chrome\n    width: -moz-fit-content; // Firefox\n    width: -webkit-fit-content; // Safari\n}\n\n.not-selectable() {\n    -webkit-touch-callout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    cursor: default;\n}\n\n.dash-spreadsheet {\n    &.dash-freeze-left,\n    &.dash-freeze-top {\n        .fit-content-polyfill();\n    }\n\n    &.dash-freeze-left {\n        max-width: 500px;\n    }\n\n    &.dash-freeze-top,\n    &.dash-virtualized {\n        max-height: 500px;\n    }\n}\n\n.dash-tooltip {\n    border: 1px solid #e4e4e4;\n    border-radius: 5px;\n    position: absolute;\n    z-index: 500;\n\n    .dash-table-tooltip {\n        position: relative;\n        background-color: #f6f6f6;\n        max-width: 300px;\n        min-width: 300px;\n        padding: 2px 10px;\n    }\n\n    &[data-attr-anchor='top'] {\n        margin-top: 10px;\n\n        &:after, &:before {\n            bottom: 100%;\n            left: 50%;\n            border: solid transparent;\n            content: \" \";\n            height: 0;\n            width: 0;\n            position: absolute;\n            pointer-events: none;\n        }\n\n        &:after {\n            border-color: transparent;\n            border-bottom-color: #f6f6f6;\n            border-width: 8px;\n            margin-left: -8px;\n        }\n\n        &:before {\n            border-color: transparent;\n            border-bottom-color: #e4e4e4;\n            border-width: 9px;\n            margin-left: -9px;\n        }\n    }\n\n    &[data-attr-anchor='bottom'] {\n        margin-bottom: 10px;\n\n        &:after, &:before {\n            top: 100%;\n            left: 50%;\n            border: solid transparent;\n            content: \" \";\n            height: 0;\n            width: 0;\n            position: absolute;\n            pointer-events: none;\n        }\n\n        &:after {\n            border-color: transparent;\n            border-top-color: #f6f6f6;\n            border-width: 8px;\n            margin-left: -8px;\n        }\n        &:before {\n            border-color: transparent;\n            border-top-color: #e4e4e4;\n            border-width: 9px;\n            margin-left: -9px;\n        }\n    }\n}\n\n.dash-spreadsheet-menu {\n    display: flex;\n    flex-direction: row;\n\n    & > * {\n        padding-right: 5px;\n    }\n\n    .dash-spreadsheet-menu-item {\n        position: relative;\n\n        .show-hide-menu {\n            background-color: #fafafa;\n            border: 1px solid #d3d3d3;\n            display: flex;\n            flex-direction: column;\n            max-height: 300px;\n            overflow: auto;\n            position: absolute;\n            top: 100%;\n            left: 0;\n            z-index: 500;\n\n            .show-hide-menu-item {\n                display: flex;\n                flex-direction: row;\n                padding: 5px;\n\n                label {\n                    white-space: nowrap;\n                }\n            }\n        }\n    }\n}\n\n.dash-table-container {\n    .previous-next-container {\n        text-align: right;\n        padding: 5px 0px;\n\n        .page-number {\n            font-family: monospace;\n            display: inline-block;\n\n            .last-page {\n                display: inline-block;\n                text-align: center;\n                padding: 1px 2px;\n            }\n\n            .current-page-container {\n                display: inline-block;\n                position: relative;\n\n                .current-page-shadow,\n                input.current-page {\n                    display: inline-block;\n                    border-bottom: solid lightgrey 1px !important;\n                    color: black;\n                    border: none;\n                    text-align: center;\n                    font-family: monospace;\n                    font-size: 10pt;\n                    padding: 1px 2px;\n\n                    &::placeholder {\n                        color: black;\n                    }\n\n                    &:focus {\n                        outline: none;\n\n                        &::placeholder {\n                            opacity: 0;\n                        }\n                    }\n                }\n\n                input.current-page {\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    width: 100%;\n                    height: 100%;\n                }\n            }\n        }\n\n        button.previous-page, button.next-page, button.first-page, button.last-page {\n            transition-duration: 400ms;\n            padding: 5px;\n            border: none;\n            display: inline-block;\n            margin-left: 5px;\n            margin-right: 5px;\n\n            &:hover {\n                color: hotpink;\n\n                &:disabled {\n                    color: graytext\n                }\n            }\n\n            &:focus {\n                outline: none;\n            }\n        }\n    }\n\n    .dash-spreadsheet-container {\n        .reset-css();\n        display: flex;\n        flex-direction: row;\n        position: relative;\n\n        // This overrides Bootstrap 3.4.1 body styling\n        // https://github.com/twbs/bootstrap/blob/v3-dev/dist/css/bootstrap.css#L1087\n        // Also unapplies with the latest `in development` 5.0.0-alpha2 (https://github.com/twbs/bootstrap/blob/main/dist/css/bootstrap.css#L51)\n        line-height: initial;\n\n        // This overrides Chrome's default `font-size: medium;` which is causing performance issues\n        // with AutoInputResize sub-component in react-select\n        // https://github.com/JedWatson/react-input-autosize/blob/05b0f86a7f8b16de99c2b31296ff0d3307f15957/src/AutosizeInput.js#L58\n        table {\n            font-size: inherit;\n            pointer-events: none;\n\n            td, th {\n                pointer-events: initial;\n            }\n        }\n\n        input[type=\"radio\"] {\n            // These override Bootstrap 3.4.1 type=\"radio\" styling\n            // https://github.com/twbs/bootstrap/blob/v3-dev/dist/css/bootstrap.css#L2621\n            // This is not a problem with the latest `in development` 5.0.0-alpha2\n            margin: initial;\n            line-height: initial;\n            // These override Bootstrap 4.5.0 type=\"radio\" styling\n            // https://github.com/twbs/bootstrap/blob/v4-dev/dist/css/bootstrap.css#L287\n            // This is not a problem with the latest `in development` 5.0.0-alpha2\n            box-sizing: initial;\n            padding: initial;\n        }\n\n\t    .dash-spreadsheet-inner {\n            box-sizing: border-box;\n            display: flex;\n            flex-direction: column;\n\n            *,\n            *:after,\n            *:before {\n\t\t        box-sizing: inherit;\n            }\n\n            .Select {\n                overflow: hidden;\n                position: static;\n            }\n\n            .Select,\n            .Select-control {\n\t\t        background-color: inherit;\n            }\n\n            .Select-value {\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n                margin-top: -2px;\n            }\n\n            .marker-row {\n                tr {\n                    visibility: hidden !important;\n                }\n\n                td, th {\n                    height: 0 !important;\n                    padding: 0 !important;\n                    margin: 0 !important;\n                }\n            }\n\n            .dash-filter {\n\t\t        input::placeholder {\n                    color: inherit;\n                    font-size: 0.8em;\n                    padding-right: 5px;\n                }\n\n                & + .dash-filter {\n                    &:not(:hover):not(:focus-within) {\n                        input::placeholder {\n                            color: transparent;\n                        }\n                    }\n                }\n\n                &.invalid {\n                    background-color: pink;\n                }\n            }\n\n            &:not(.dash-empty-11) {\n                .dt-table-container__row-0 {\n                    tr:last-of-type {\n                        td, th {\n                            border-bottom: none !important;\n                        }\n                    }\n                }\n            }\n\n            &:not(.dash-empty-01) {\n        \t\t.cell-0-0,\n\t\t        .cell-1-0 {\n                    tr {\n                        td, th {\n                            &:last-of-type {\n                                border-right: none !important;\n                            }\n                        }\n                    }\n\t    \t    }\n            }\n\n            .cell-0-0 {\n                overflow: hidden;\n            }\n\n            .cell-0-0,\n            .cell-1-0 {\n                td, th {\n                    &.phantom-cell {\n                        border-color: transparent !important;\n                    }\n                }\n            }\n\n            .cell-0-1,\n            .cell-1-1 {\n                td, th {\n                    &.phantom-cell {\n                        border-color: transparent inherit transparent transparent !important;\n                    }\n                }\n            }\n\n            &.dash-freeze-left,\n            &.dash-freeze-top,\n            &.dash-virtualized {\n                overflow: hidden !important;\n\n                .dt-table-container__row-0 {\n                    display: flex;\n                    flex: 0 0 auto;\n                    flex-direction: row;\n                }\n\n                .dt-table-container__row-1 {\n                    display: flex;\n                    flex-direction: row;\n                    overflow: auto;\n                }\n\n                .cell-0-0,\n                .cell-1-0 {\n                    flex: 0 0 auto;\n                    left: 0;\n                    position: sticky;\n                    position:-webkit-sticky;\n                    z-index: 400;\n                }\n\n                .cell-0-1 {\n                    z-index: 300;\n                    flex: 0 0 auto;\n                }\n\n                .cell-1-1 {\n                    flex: 0 0 auto;\n                }\n            }\n\n            &.dash-fill-width {\n                .cell-0-1,\n                .cell-1-1 {\n                    flex: 1 0 auto;\n                }\n\n                .cell {\n                    table {\n                        width: 100%;\n                    }\n                }\n            }\n\n            td {\n                background-color: inherit;\n\n                &.focused {\n                    margin: -1px;\n                    z-index: 200;\n                }\n\n                .dash-cell-value-container {\n                    width: 100%;\n                    height: 100%;\n                }\n\n                .dash-input-cell-value-container {\n                    position: relative;\n                }\n\n                .dash-cell-value {\n                    height: 100%;\n                    width: 100%;\n\n                    &.unfocused.selectable::selection {\n                        background-color: transparent;\n                    }\n\n                    &.unfocused {\n                        caret-color: transparent;\n                    }\n                }\n\n                input.dash-cell-value {\n                    position: absolute;\n                    left: 0;\n                    top: 0;\n                }\n\n                .cell-value-shadow {\n                    margin: auto 0;\n                    opacity: 0;\n                }\n\n                .input-cell-value-shadow {\n                    display: inline-block;\n                    height: initial;\n                    width: initial;\n                }\n\n                .dropdown-cell-value-shadow {\n                    display: block;\n                    height: 0px;\n                    padding: 0 42px 0 10px;\n                }\n            }\n\n            /*\n             * fixes Firefox td height bug on td > dropdown children\n             * bug should only appear on FF but\n             * @supports = scoped to Firefox only\n             * to minimize side effects\n             */\n            @supports (-moz-appearance:none) {\n                td.dropdown .dash-cell-value-container {\n                    height: auto;\n                }\n            }\n\n            th.dash-filter {\n                position: relative;\n\n                & input {\n                    left: 0;\n                    top: 0;\n                    height: 100%;\n                    width: 100%;\n\n                    &.dash-filter--case {\n                        position: relative;\n                        left: auto;\n                        top: auto;\n                        width: auto;\n                        height: 16px;\n                        line-height: 0px;\n                        padding: 1px;\n                    }\n                    &.dash-filter--case--sensitive {\n                        border-color: hotpink;\n                        border-radius: 3px;\n                        border-style: solid;\n                        border-width: 2px;\n                        color: hotpink;\n                    }\n                }\n            }\n\n            th {\n                white-space: nowrap;\n\n                .column-header--clear,\n                .column-header--delete,\n                .column-header--edit,\n                .column-header--hide,\n                .column-header--sort {\n                    .not-selectable();\n                    cursor: pointer;\n                }\n            }\n\n            tr {\n                min-height: 30px;\n                height: 30px;\n            }\n\n            // cell content styling\n            td, th {\n                background-clip: padding-box;\n                padding: 2px;\n                overflow-x: hidden;\n                white-space: nowrap;\n                box-sizing: border-box;\n\n                text-align: right;\n\n                &.phantom-cell {\n                    visibility: hidden;\n                }\n\n                div.dash-cell-value {\n                    display: inline;\n                    vertical-align: middle;\n                    white-space: inherit;\n                    overflow: inherit;\n                    text-overflow: inherit;\n\n                    &.cell-markdown {\n                        text-align: left;\n                        font-family: sans-serif;\n                        display: inline-block;\n\n                        blockquote {\n                            white-space: pre;\n                        }\n                    }\n                }\n            }\n        }\n\n    \t.dash-spreadsheet-inner textarea {\n            white-space: pre;\n\t    }\n\n\t    .dash-spreadsheet-inner table {\n            border-collapse: collapse;\n\n            font-family: monospace;\n            --accent: hotpink;\n            --border: lightgrey;\n            --text-color: rgb(60, 60, 60);\n            --hover: rgb(253, 253, 253);\n            --background-color-ellipses: rgb(253, 253, 253);\n            --faded-text: rgb(250, 250, 250);\n            --faded-text-header: rgb(180, 180, 180);\n            --selected-background: rgba(255, 65, 54, 0.2);\n            --faded-dropdown: rgb(240, 240, 240);\n            --muted: rgb(200, 200, 200);\n\t    }\n\n\t    /* focus happens after copying to clipboard */\n\t    .dash-spreadsheet-inner table:focus {\n            outline: none;\n\t    }\n\n\t    .dash-spreadsheet-inner thead {\n            display: table-row-group;\n\t    }\n\n\t    .elip {\n            text-align: center;\n            width: 100%;\n            background-color: var(--background-color-ellipses);\n\t    }\n\n\t    .dash-spreadsheet-inner td.dropdown {\n            /*\n             * To view the dropdown's contents, we need\n             * overflow-y: visible.\n             * Unfortunately, overflow-x: hidden and overflow-y: visible\n             * can't both be set at the same time.\n             * So, we have to make both overflow-x: visible and overflow-y: visble\n             *\n             * See https://stackoverflow.com/questions/6421966/\n             *\n             * There might be another solution with parent divs, but I haven't\n             * tried it.\n             */\n            overflow-x: visible;\n\t    }\n\n        .dash-spreadsheet-inner :not(.cell--selected) tr:hover,\n        tr:hover input :not(.cell--selected) {\n            background-color: var(--hover);\n    \t}\n\n    \t.dash-spreadsheet-inner th {\n            background-color: rgb(250, 250, 250);\n\t    }\n\n\t    .dash-spreadsheet-inner td {\n            background-color: white;\n\t    }\n\n\t    .expanded-row--empty-cell {\n            background-color: transparent;\n\t    }\n\n\t    .expanded-row {\n            text-align: center;\n\t    }\n\n\t    .dash-spreadsheet-inner input:not([type=radio]):not([type=checkbox]) {\n            padding: 0px;\n            margin: 0px;\n            height: calc(100% - 1px);\n            line-height: 30px;\n            border: none;\n            font-family: inherit;\n            text-align: right;\n            box-sizing: border-box;\n            color: var(--text-color);\n            background-color: transparent; /* so as to not overlay the box shadow */\n\n            /* browser's default text-shadow is `$color 0px 0px 0px;`\n             * for `input`, which makes it look a little bit heavier than dropdowns\n             * or bare `td`\n             */\n            text-shadow: none;\n\t    }\n\n\t    .dash-spreadsheet-inner input.unfocused {\n            color: transparent;\n            text-shadow: 0 0 0 var(--text-color);\n            cursor: default;\n\t    }\n\n\t    .dash-spreadsheet-inner input.unfocused:focus {\n            outline: none;\n\t    }\n\n\t    .toggle-row {\n            border: none;\n            box-shadow: none;\n            width: 10px;\n            padding-left: 10px;\n            padding-right: 10px;\n            cursor: pointer;\n            color: var(--faded-text);\n\t    }\n\n    \t.toggle-row--expanded {\n            color: var(--accent);\n\t    }\n\n    \t.dash-spreadsheet-inner tr:hover .toggle-row {\n            color: var(--accent);\n\t    }\n\n        .dash-spreadsheet-inner .dash-delete-cell,\n        .dash-spreadsheet-inner .dash-delete-header {\n            .not-selectable();\n\n            font-size: 1.3rem;\n            text-align: center;\n            cursor: pointer;\n            color: var(--muted);\n    \t}\n        .dash-spreadsheet-inner .dash-delete-cell:hover,\n        .dash-spreadsheet-inner .dash-delete-header:hover {\n            color: var(--accent);\n    \t}\n\n\t    .dash-spreadsheet-inner {\n            .dash-header>div,\n            .dash-filter>div {\n                display: flex;\n\n                input[type=\"text\"] {\n                    flex: 1;\n                    line-height: unset;\n                    &::placeholder {\n                        font-size: 0.9em;\n                    }\n                }\n            }\n            .dash-filter>div {\n                flex-direction: row-reverse;\n            }\n            .column-actions {\n                display: flex;\n            }\n\n            .column-header-name {\n                flex-grow: 1;\n            }\n\n            [class^='column-header--'], [class^='dash-filter--'] {\n                cursor: pointer;\n            }\n\n            .column-header--select {\n\t\t        height: auto;\n            }\n\n            .column-header--select,\n            .column-header--sort {\n        \t\tcolor: var(--faded-text-header);\n            }\n\n\n            .dash-filter--case,\n            .column-header--clear,\n            .column-header--delete,\n            .column-header--edit,\n            .column-header--hide {\n                opacity: 0.1;\n                padding-left: 2px;\n                padding-right: 2px;\n            }\n\n            th:hover {\n        \t\t[class^='column-header--'], [class^='dash-filter--'] {\n                    &:not(.disabled) {\n                        color: var(--accent);\n                        opacity: 1;\n                    }\n                }\n            }\n\n            .dash-filter--case {\n                font-size: 10px;\n            }\n\t    }\n    }\n}\n",'/*RESET CSS*/\n.reset-css() {\n    /* The "normal" reset CSS */\n    // div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}table{border-collapse:collapse;border-spacing:0}fieldset,img{border:0}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal}ol,ul{list-style:none}caption,th{text-align:left}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal}q:before,q:after{content:\'\'}abbr,acronym{border:0;font-variant:normal}sup{vertical-align:text-top}sub{vertical-align:text-bottom}input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;*font-size:100%}legend{color:#000}#yui3-css-stamp.cssreset{display:none}\n\n    /* The "modified" reset CSS applied to the table to ignore markdown cells */\n    th {\n        font-style:normal;\n        font-weight:normal;\n        text-align:left;\n    }\n\n    th, td {\n        margin:0;\n        padding:0;\n\n        & > div:not(.cell-markdown) {\n            margin:0;\n            padding:0;\n\n            dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}\n            table{border-collapse:collapse;border-spacing:0}\n            fieldset,img{border:0}\n            address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal}\n            ol,ul{list-style:none}\n            caption,th{text-align:left}\n            h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal}\n            q:before,q:after{content:\'\'}\n            abbr,acronym{border:0;font-variant:normal}\n            sup{vertical-align:text-top}\n            sub{vertical-align:text-bottom}\n            input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;*font-size:100%}\n            legend{color:#000}\n        }\n    }\n\n    // Input buttons have an overlay + are rounded by default in iOS Mobile Safari\n    // http://stackoverflow.com/questions/2918707/turn-off-iphone-safari-input-element-rounding\n    input[type="button"] {\n        border-radius: 0;\n        -webkit-appearance: none;\n    }\n\n    *:focus {\n        outline: none;\n    }\n}'],sourceRoot:""}]),t.Z=a},7288:function(e,t,n){"use strict";var r=n(7537),o=n.n(r),i=n(3645),a=n.n(i)()(o());a.push([e.id,"/**\n * React Select\n * ============\n * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/\n * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs\n * MIT License: https://github.com/JedWatson/react-select\n*/\n.Select {\n  position: relative;\n}\n.Select input::-webkit-contacts-auto-fill-button,\n.Select input::-webkit-credentials-auto-fill-button {\n  display: none !important;\n}\n.Select input::-ms-clear {\n  display: none !important;\n}\n.Select input::-ms-reveal {\n  display: none !important;\n}\n.Select,\n.Select div,\n.Select input,\n.Select span {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.Select.is-disabled .Select-arrow-zone {\n  cursor: default;\n  pointer-events: none;\n  opacity: 0.35;\n}\n.Select.is-disabled > .Select-control {\n  background-color: #f9f9f9;\n}\n.Select.is-disabled > .Select-control:hover {\n  box-shadow: none;\n}\n.Select.is-open > .Select-control {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background: #fff;\n  border-color: #b3b3b3 #ccc #d9d9d9;\n}\n.Select.is-open > .Select-control .Select-arrow {\n  top: -2px;\n  border-color: transparent transparent #999;\n  border-width: 0 5px 5px;\n}\n.Select.is-searchable.is-open > .Select-control {\n  cursor: text;\n}\n.Select.is-searchable.is-focused:not(.is-open) > .Select-control {\n  cursor: text;\n}\n.Select.is-focused > .Select-control {\n  background: #fff;\n}\n.Select.is-focused:not(.is-open) > .Select-control {\n  border-color: #007eff;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);\n  background: #fff;\n}\n.Select.has-value.is-clearable.Select--single > .Select-control .Select-value {\n  padding-right: 42px;\n}\n.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n  color: #333;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  color: #007eff;\n  outline: none;\n  text-decoration: underline;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  background: #fff;\n}\n.Select.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select.is-open .Select-arrow,\n.Select .Select-arrow-zone:hover > .Select-arrow {\n  border-top-color: #666;\n}\n.Select.Select--rtl {\n  direction: rtl;\n  text-align: right;\n}\n.Select-control {\n  background-color: #fff;\n  border-color: #d9d9d9 #ccc #b3b3b3;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  color: #333;\n  cursor: default;\n  display: table;\n  border-spacing: 0;\n  border-collapse: separate;\n  height: 36px;\n  outline: none;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.Select-control:hover {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n}\n.Select-control .Select-input:focus {\n  outline: none;\n  background: #fff;\n}\n.Select-placeholder,\n.Select--single > .Select-control .Select-value {\n  bottom: 0;\n  color: #aaa;\n  left: 0;\n  line-height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.Select-input {\n  height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  vertical-align: middle;\n}\n.Select-input > input {\n  width: 100%;\n  background: none transparent;\n  border: 0 none;\n  box-shadow: none;\n  cursor: default;\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  outline: none;\n  line-height: 17px;\n  /* For IE 8 compatibility */\n  padding: 8px 0 12px;\n  /* For IE 8 compatibility */\n  -webkit-appearance: none;\n}\n.is-focused .Select-input > input {\n  cursor: text;\n}\n.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select-control:not(.is-searchable) > .Select-input {\n  outline: none;\n}\n.Select-loading-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 16px;\n}\n.Select-loading {\n  -webkit-animation: Select-animation-spin 400ms infinite linear;\n  -o-animation: Select-animation-spin 400ms infinite linear;\n  animation: Select-animation-spin 400ms infinite linear;\n  width: 16px;\n  height: 16px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  border-right-color: #333;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n}\n.Select-clear-zone {\n  -webkit-animation: Select-animation-fadeIn 200ms;\n  -o-animation: Select-animation-fadeIn 200ms;\n  animation: Select-animation-fadeIn 200ms;\n  color: #999;\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 17px;\n}\n.Select-clear-zone:hover {\n  color: #D0021B;\n}\n.Select-clear {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 1;\n}\n.Select--multi .Select-clear-zone {\n  width: 17px;\n}\n.Select-arrow-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 25px;\n  padding-right: 5px;\n}\n.Select--rtl .Select-arrow-zone {\n  padding-right: 0;\n  padding-left: 5px;\n}\n.Select-arrow {\n  border-color: #999 transparent transparent;\n  border-style: solid;\n  border-width: 5px 5px 2.5px;\n  display: inline-block;\n  height: 0;\n  width: 0;\n  position: relative;\n}\n.Select-control > *:last-child {\n  padding-right: 5px;\n}\n.Select--multi .Select-multi-value-wrapper {\n  display: inline-block;\n}\n.Select .Select-aria-only {\n  position: absolute;\n  display: inline-block;\n  height: 1px;\n  width: 1px;\n  margin: -1px;\n  clip: rect(0, 0, 0, 0);\n  overflow: hidden;\n  float: left;\n}\n@-webkit-keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.Select-menu-outer {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border-top-color: #e6e6e6;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n  box-sizing: border-box;\n  margin-top: -1px;\n  max-height: 200px;\n  position: absolute;\n  left: 0;\n  top: 100%;\n  width: 100%;\n  z-index: 1;\n  -webkit-overflow-scrolling: touch;\n}\n.Select-menu {\n  max-height: 198px;\n  overflow-y: auto;\n}\n.Select-option {\n  box-sizing: border-box;\n  background-color: #fff;\n  color: #666666;\n  cursor: pointer;\n  display: block;\n  padding: 8px 10px;\n}\n.Select-option:last-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.Select-option.is-selected {\n  background-color: #f5faff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.04);\n  color: #333;\n}\n.Select-option.is-focused {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  color: #333;\n}\n.Select-option.is-disabled {\n  color: #cccccc;\n  cursor: default;\n}\n.Select-noresults {\n  box-sizing: border-box;\n  color: #999999;\n  cursor: default;\n  display: block;\n  padding: 8px 10px;\n}\n.Select--multi .Select-input {\n  vertical-align: middle;\n  margin-left: 10px;\n  padding: 0;\n}\n.Select--multi.Select--rtl .Select-input {\n  margin-left: 0;\n  margin-right: 10px;\n}\n.Select--multi.has-value .Select-input {\n  margin-left: 5px;\n}\n.Select--multi .Select-value {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  border-radius: 2px;\n  border: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border: 1px solid rgba(0, 126, 255, 0.24);\n  color: #007eff;\n  display: inline-block;\n  font-size: 0.9em;\n  line-height: 1.4;\n  margin-left: 5px;\n  margin-top: 5px;\n  vertical-align: top;\n}\n.Select--multi .Select-value-icon,\n.Select--multi .Select-value-label {\n  display: inline-block;\n  vertical-align: middle;\n}\n.Select--multi .Select-value-label {\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n  cursor: default;\n  padding: 2px 5px;\n}\n.Select--multi a.Select-value-label {\n  color: #007eff;\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select--multi a.Select-value-label:hover {\n  text-decoration: underline;\n}\n.Select--multi .Select-value-icon {\n  cursor: pointer;\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-right: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-right: 1px solid rgba(0, 126, 255, 0.24);\n  padding: 1px 5px 3px;\n}\n.Select--multi .Select-value-icon:hover,\n.Select--multi .Select-value-icon:focus {\n  background-color: #d8eafd;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 113, 230, 0.08);\n  color: #0071e6;\n}\n.Select--multi .Select-value-icon:active {\n  background-color: #c2e0ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.24);\n}\n.Select--multi.Select--rtl .Select-value {\n  margin-left: 0;\n  margin-right: 5px;\n}\n.Select--multi.Select--rtl .Select-value-icon {\n  border-right: none;\n  border-left: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-left: 1px solid rgba(0, 126, 255, 0.24);\n}\n.Select--multi.is-disabled .Select-value {\n  background-color: #fcfcfc;\n  border: 1px solid #e3e3e3;\n  color: #333;\n}\n.Select--multi.is-disabled .Select-value-icon {\n  cursor: not-allowed;\n  border-right: 1px solid #e3e3e3;\n}\n.Select--multi.is-disabled .Select-value-icon:hover,\n.Select--multi.is-disabled .Select-value-icon:focus,\n.Select--multi.is-disabled .Select-value-icon:active {\n  background-color: #fcfcfc;\n}\n@keyframes Select-animation-spin {\n  to {\n    transform: rotate(1turn);\n  }\n}\n@-webkit-keyframes Select-animation-spin {\n  to {\n    -webkit-transform: rotate(1turn);\n  }\n}\n","",{version:3,sources:["webpack://./node_modules/react-select/dist/react-select.css"],names:[],mappings:"AAAA;;;;;;CAMC;AACD;EACE,kBAAkB;AACpB;AACA;;EAEE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;;;;EAIE,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;AACxB;AACA;EACE,eAAe;EACf,oBAAoB;EACpB,aAAa;AACf;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,gBAAgB;AAClB;AACA;EACE,6BAA6B;EAC7B,4BAA4B;EAC5B,gBAAgB;EAChB,kCAAkC;AACpC;AACA;EACE,SAAS;EACT,0CAA0C;EAC1C,uBAAuB;AACzB;AACA;EACE,YAAY;AACd;AACA;EACE,YAAY;AACd;AACA;EACE,gBAAgB;AAClB;AACA;EACE,qBAAqB;EACrB,kFAAkF;EAClF,gBAAgB;AAClB;AACA;EACE,mBAAmB;AACrB;AACA;;EAEE,WAAW;AACb;AACA;;EAEE,eAAe;EACf,qBAAqB;AACvB;AACA;;;;EAIE,cAAc;EACd,aAAa;EACb,0BAA0B;AAC5B;AACA;;EAEE,gBAAgB;AAClB;AACA;EACE,UAAU;AACZ;AACA;;EAEE,sBAAsB;AACxB;AACA;EACE,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,sBAAsB;EACtB,kCAAkC;EAClC,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;EACX,eAAe;EACf,cAAc;EACd,iBAAiB;EACjB,yBAAyB;EACzB,YAAY;EACZ,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,WAAW;AACb;AACA;EACE,uCAAuC;AACzC;AACA;EACE,aAAa;EACb,gBAAgB;AAClB;AACA;;EAEE,SAAS;EACT,WAAW;EACX,OAAO;EACP,iBAAiB;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,eAAe;EACf,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AACrB;AACA;EACE,YAAY;EACZ,kBAAkB;EAClB,mBAAmB;EACnB,sBAAsB;AACxB;AACA;EACE,WAAW;EACX,4BAA4B;EAC5B,cAAc;EACd,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,oBAAoB;EACpB,kBAAkB;EAClB,SAAS;EACT,aAAa;EACb,iBAAiB;EACjB,2BAA2B;EAC3B,mBAAmB;EACnB,2BAA2B;EAC3B,wBAAwB;AAC1B;AACA;EACE,YAAY;AACd;AACA;EACE,UAAU;AACZ;AACA;EACE,aAAa;AACf;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;AACb;AACA;EACE,8DAA8D;EAC9D,yDAAyD;EACzD,sDAAsD;EACtD,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,kBAAkB;EAClB,sBAAsB;EACtB,wBAAwB;EACxB,qBAAqB;EACrB,kBAAkB;EAClB,sBAAsB;AACxB;AACA;EACE,gDAAgD;EAChD,2CAA2C;EAC3C,wCAAwC;EACxC,WAAW;EACX,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;AACb;AACA;EACE,cAAc;AAChB;AACA;EACE,qBAAqB;EACrB,eAAe;EACf,cAAc;AAChB;AACA;EACE,WAAW;AACb;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;EACX,kBAAkB;AACpB;AACA;EACE,gBAAgB;EAChB,iBAAiB;AACnB;AACA;EACE,0CAA0C;EAC1C,mBAAmB;EACnB,2BAA2B;EAC3B,qBAAqB;EACrB,SAAS;EACT,QAAQ;EACR,kBAAkB;AACpB;AACA;EACE,kBAAkB;AACpB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,UAAU;EACV,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,WAAW;AACb;AACA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;AACA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;AACA;EACE,+BAA+B;EAC/B,8BAA8B;EAC9B,sBAAsB;EACtB,sBAAsB;EACtB,yBAAyB;EACzB,uCAAuC;EACvC,sBAAsB;EACtB,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;EAClB,OAAO;EACP,SAAS;EACT,WAAW;EACX,UAAU;EACV,iCAAiC;AACnC;AACA;EACE,iBAAiB;EACjB,gBAAgB;AAClB;AACA;EACE,sBAAsB;EACtB,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,WAAW;AACb;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,WAAW;AACb;AACA;EACE,cAAc;EACd,eAAe;AACjB;AACA;EACE,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,sBAAsB;EACtB,iBAAiB;EACjB,UAAU;AACZ;AACA;EACE,cAAc;EACd,kBAAkB;AACpB;AACA;EACE,gBAAgB;AAClB;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,kBAAkB;EAClB,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,cAAc;EACd,qBAAqB;EACrB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;EACf,mBAAmB;AACrB;AACA;;EAEE,qBAAqB;EACrB,sBAAsB;AACxB;AACA;EACE,+BAA+B;EAC/B,4BAA4B;EAC5B,eAAe;EACf,gBAAgB;AAClB;AACA;EACE,cAAc;EACd,eAAe;EACf,qBAAqB;AACvB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,eAAe;EACf,8BAA8B;EAC9B,2BAA2B;EAC3B,+BAA+B;EAC/B,4BAA4B;EAC5B,+CAA+C;EAC/C,oBAAoB;AACtB;AACA;;EAEE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,cAAc;AAChB;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;AAC3C;AACA;EACE,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,8BAA8B;EAC9B,4BAA4B;EAC5B,8CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,yBAAyB;EACzB,WAAW;AACb;AACA;EACE,mBAAmB;EACnB,+BAA+B;AACjC;AACA;;;EAGE,yBAAyB;AAC3B;AACA;EACE;IACE,wBAAwB;EAC1B;AACF;AACA;EACE;IACE,gCAAgC;EAClC;AACF",sourcesContent:["/**\n * React Select\n * ============\n * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/\n * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs\n * MIT License: https://github.com/JedWatson/react-select\n*/\n.Select {\n  position: relative;\n}\n.Select input::-webkit-contacts-auto-fill-button,\n.Select input::-webkit-credentials-auto-fill-button {\n  display: none !important;\n}\n.Select input::-ms-clear {\n  display: none !important;\n}\n.Select input::-ms-reveal {\n  display: none !important;\n}\n.Select,\n.Select div,\n.Select input,\n.Select span {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.Select.is-disabled .Select-arrow-zone {\n  cursor: default;\n  pointer-events: none;\n  opacity: 0.35;\n}\n.Select.is-disabled > .Select-control {\n  background-color: #f9f9f9;\n}\n.Select.is-disabled > .Select-control:hover {\n  box-shadow: none;\n}\n.Select.is-open > .Select-control {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background: #fff;\n  border-color: #b3b3b3 #ccc #d9d9d9;\n}\n.Select.is-open > .Select-control .Select-arrow {\n  top: -2px;\n  border-color: transparent transparent #999;\n  border-width: 0 5px 5px;\n}\n.Select.is-searchable.is-open > .Select-control {\n  cursor: text;\n}\n.Select.is-searchable.is-focused:not(.is-open) > .Select-control {\n  cursor: text;\n}\n.Select.is-focused > .Select-control {\n  background: #fff;\n}\n.Select.is-focused:not(.is-open) > .Select-control {\n  border-color: #007eff;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);\n  background: #fff;\n}\n.Select.has-value.is-clearable.Select--single > .Select-control .Select-value {\n  padding-right: 42px;\n}\n.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n  color: #333;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  color: #007eff;\n  outline: none;\n  text-decoration: underline;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  background: #fff;\n}\n.Select.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select.is-open .Select-arrow,\n.Select .Select-arrow-zone:hover > .Select-arrow {\n  border-top-color: #666;\n}\n.Select.Select--rtl {\n  direction: rtl;\n  text-align: right;\n}\n.Select-control {\n  background-color: #fff;\n  border-color: #d9d9d9 #ccc #b3b3b3;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  color: #333;\n  cursor: default;\n  display: table;\n  border-spacing: 0;\n  border-collapse: separate;\n  height: 36px;\n  outline: none;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.Select-control:hover {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n}\n.Select-control .Select-input:focus {\n  outline: none;\n  background: #fff;\n}\n.Select-placeholder,\n.Select--single > .Select-control .Select-value {\n  bottom: 0;\n  color: #aaa;\n  left: 0;\n  line-height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.Select-input {\n  height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  vertical-align: middle;\n}\n.Select-input > input {\n  width: 100%;\n  background: none transparent;\n  border: 0 none;\n  box-shadow: none;\n  cursor: default;\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  outline: none;\n  line-height: 17px;\n  /* For IE 8 compatibility */\n  padding: 8px 0 12px;\n  /* For IE 8 compatibility */\n  -webkit-appearance: none;\n}\n.is-focused .Select-input > input {\n  cursor: text;\n}\n.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select-control:not(.is-searchable) > .Select-input {\n  outline: none;\n}\n.Select-loading-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 16px;\n}\n.Select-loading {\n  -webkit-animation: Select-animation-spin 400ms infinite linear;\n  -o-animation: Select-animation-spin 400ms infinite linear;\n  animation: Select-animation-spin 400ms infinite linear;\n  width: 16px;\n  height: 16px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  border-right-color: #333;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n}\n.Select-clear-zone {\n  -webkit-animation: Select-animation-fadeIn 200ms;\n  -o-animation: Select-animation-fadeIn 200ms;\n  animation: Select-animation-fadeIn 200ms;\n  color: #999;\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 17px;\n}\n.Select-clear-zone:hover {\n  color: #D0021B;\n}\n.Select-clear {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 1;\n}\n.Select--multi .Select-clear-zone {\n  width: 17px;\n}\n.Select-arrow-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 25px;\n  padding-right: 5px;\n}\n.Select--rtl .Select-arrow-zone {\n  padding-right: 0;\n  padding-left: 5px;\n}\n.Select-arrow {\n  border-color: #999 transparent transparent;\n  border-style: solid;\n  border-width: 5px 5px 2.5px;\n  display: inline-block;\n  height: 0;\n  width: 0;\n  position: relative;\n}\n.Select-control > *:last-child {\n  padding-right: 5px;\n}\n.Select--multi .Select-multi-value-wrapper {\n  display: inline-block;\n}\n.Select .Select-aria-only {\n  position: absolute;\n  display: inline-block;\n  height: 1px;\n  width: 1px;\n  margin: -1px;\n  clip: rect(0, 0, 0, 0);\n  overflow: hidden;\n  float: left;\n}\n@-webkit-keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.Select-menu-outer {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border-top-color: #e6e6e6;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n  box-sizing: border-box;\n  margin-top: -1px;\n  max-height: 200px;\n  position: absolute;\n  left: 0;\n  top: 100%;\n  width: 100%;\n  z-index: 1;\n  -webkit-overflow-scrolling: touch;\n}\n.Select-menu {\n  max-height: 198px;\n  overflow-y: auto;\n}\n.Select-option {\n  box-sizing: border-box;\n  background-color: #fff;\n  color: #666666;\n  cursor: pointer;\n  display: block;\n  padding: 8px 10px;\n}\n.Select-option:last-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.Select-option.is-selected {\n  background-color: #f5faff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.04);\n  color: #333;\n}\n.Select-option.is-focused {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  color: #333;\n}\n.Select-option.is-disabled {\n  color: #cccccc;\n  cursor: default;\n}\n.Select-noresults {\n  box-sizing: border-box;\n  color: #999999;\n  cursor: default;\n  display: block;\n  padding: 8px 10px;\n}\n.Select--multi .Select-input {\n  vertical-align: middle;\n  margin-left: 10px;\n  padding: 0;\n}\n.Select--multi.Select--rtl .Select-input {\n  margin-left: 0;\n  margin-right: 10px;\n}\n.Select--multi.has-value .Select-input {\n  margin-left: 5px;\n}\n.Select--multi .Select-value {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  border-radius: 2px;\n  border: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border: 1px solid rgba(0, 126, 255, 0.24);\n  color: #007eff;\n  display: inline-block;\n  font-size: 0.9em;\n  line-height: 1.4;\n  margin-left: 5px;\n  margin-top: 5px;\n  vertical-align: top;\n}\n.Select--multi .Select-value-icon,\n.Select--multi .Select-value-label {\n  display: inline-block;\n  vertical-align: middle;\n}\n.Select--multi .Select-value-label {\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n  cursor: default;\n  padding: 2px 5px;\n}\n.Select--multi a.Select-value-label {\n  color: #007eff;\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select--multi a.Select-value-label:hover {\n  text-decoration: underline;\n}\n.Select--multi .Select-value-icon {\n  cursor: pointer;\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-right: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-right: 1px solid rgba(0, 126, 255, 0.24);\n  padding: 1px 5px 3px;\n}\n.Select--multi .Select-value-icon:hover,\n.Select--multi .Select-value-icon:focus {\n  background-color: #d8eafd;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 113, 230, 0.08);\n  color: #0071e6;\n}\n.Select--multi .Select-value-icon:active {\n  background-color: #c2e0ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.24);\n}\n.Select--multi.Select--rtl .Select-value {\n  margin-left: 0;\n  margin-right: 5px;\n}\n.Select--multi.Select--rtl .Select-value-icon {\n  border-right: none;\n  border-left: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-left: 1px solid rgba(0, 126, 255, 0.24);\n}\n.Select--multi.is-disabled .Select-value {\n  background-color: #fcfcfc;\n  border: 1px solid #e3e3e3;\n  color: #333;\n}\n.Select--multi.is-disabled .Select-value-icon {\n  cursor: not-allowed;\n  border-right: 1px solid #e3e3e3;\n}\n.Select--multi.is-disabled .Select-value-icon:hover,\n.Select--multi.is-disabled .Select-value-icon:focus,\n.Select--multi.is-disabled .Select-value-icon:active {\n  background-color: #fcfcfc;\n}\n@keyframes Select-animation-spin {\n  to {\n    transform: rotate(1turn);\n  }\n}\n@-webkit-keyframes Select-animation-spin {\n  to {\n    -webkit-transform: rotate(1turn);\n  }\n}\n"],sourceRoot:""}]),t.Z=a},366:function(e,t,n){"use strict";var r=n(7537),o=n.n(r),i=n(3645),a=n.n(i)()(o());a.push([e.id,".dash-spreadsheet .Select,\n.dash-spreadsheet .Select-control {\n    border: none;\n    cursor: pointer;\n    height: 30px;  /* matches the td height and line-height */\n    border: none;\n}\n\n.dash-spreadsheet .Select-placeholder,\n.dash-spreadsheet .Select--single > .Select-control .Select-value {\n    height: 100%;\n    line-height: inherit;\n}\n\n.dash-spreadsheet .Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.dash-spreadsheet .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n    color: var(--text-color);\n}\n\n.dash-spreadsheet .Select:hover,\n.dash-spreadsheet .Select-control:hover {\n    border: none;\n    box-shadow: none;\n    cursor: pointer;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    z-index: 100;\n}\n\n.dash-spreadsheet .Select-arrow {\n    border-top-color: var(--faded-dropdown);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .Select-control:hover .Select-arrow {\n    border-top-color: var(--accent);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .is-open > .Select-control .Select-arrow {\n    border-bottom-color: var(--background-color-8-react-select);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-top-color: transparent;\n}\n\n.dash-spreadsheet .Select--multi .Select-value,\n.dash-spreadsheet .Select--multi a.Select-value-label {\n    color: var(--color-8-react-select);\n}\n\n.dash-spreadsheet .Select {\n    border-radius: 0;\n}\n\n.dash-spreadsheet .Select.is-disabled > .Select-control {\n    border: none;\n}\n\n.dash-spreadsheet .Select--multi .Select-value-icon:hover,\n.dash-spreadsheet .Select--multi .Select-value-icon:focus {\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select.is-disabled .Select-control {\n    cursor: not-allowed;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    background-color: white;\n    border: none;\n    border-bottom: thin var(--border) solid;\n    border-radius: 0;\n    border-top: none;\n}\n\n.dash-spreadsheet .Select-option {\n    background-color: inherit;\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-option:hover {\n    font-weight: bold;\n    color: var(--accent);\n    background-color: var(--hover);\n}\n\n.dash-spreadsheet .Select.is-focused:not(.is-open) > .Select-control {\n    border: none;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    background: #fff;\n}\n\n.dash-spreadsheet .Select-option.is-focused {\n    background-color: white;\n    color: var(--accent);\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select-clear {\n    color: transparent;\n}\n.dash-spreadsheet .Select:hover .Select-clear {\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-control {\n    padding-left: 2px;\n}\n","",{version:3,sources:["webpack://./src/dash-table/components/Table/Dropdown.css"],names:[],mappings:"AAAA;;IAEI,YAAY;IACZ,eAAe;IACf,YAAY,GAAG,0CAA0C;IACzD,YAAY;AAChB;;AAEA;;IAEI,YAAY;IACZ,oBAAoB;AACxB;;AAEA;;IAEI,wBAAwB;AAC5B;;AAEA;;IAEI,YAAY;IACZ,gBAAgB;IAChB,eAAe;AACnB;;AAEA;IACI,YAAY;AAChB;;AAEA;IACI,uCAAuC;IACvC,8BAA8B;IAC9B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,+BAA+B;IAC/B,8BAA8B;IAC9B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,2DAA2D;IAC3D,8BAA8B;IAC9B,+BAA+B;IAC/B,6BAA6B;AACjC;;AAEA;;IAEI,kCAAkC;AACtC;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,YAAY;AAChB;;AAEA;;IAEI,iBAAiB;AACrB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,uBAAuB;IACvB,YAAY;IACZ,uCAAuC;IACvC,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,oBAAoB;AACxB;;AAEA;IACI,iBAAiB;IACjB,oBAAoB;IACpB,8BAA8B;AAClC;;AAEA;IACI,YAAY;IACZ,wBAAwB;IACxB,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,uBAAuB;IACvB,oBAAoB;IACpB,iBAAiB;AACrB;;AAEA;IACI,kBAAkB;AACtB;AACA;IACI,oBAAoB;AACxB;;AAEA;IACI,iBAAiB;AACrB",sourcesContent:[".dash-spreadsheet .Select,\n.dash-spreadsheet .Select-control {\n    border: none;\n    cursor: pointer;\n    height: 30px;  /* matches the td height and line-height */\n    border: none;\n}\n\n.dash-spreadsheet .Select-placeholder,\n.dash-spreadsheet .Select--single > .Select-control .Select-value {\n    height: 100%;\n    line-height: inherit;\n}\n\n.dash-spreadsheet .Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.dash-spreadsheet .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n    color: var(--text-color);\n}\n\n.dash-spreadsheet .Select:hover,\n.dash-spreadsheet .Select-control:hover {\n    border: none;\n    box-shadow: none;\n    cursor: pointer;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    z-index: 100;\n}\n\n.dash-spreadsheet .Select-arrow {\n    border-top-color: var(--faded-dropdown);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .Select-control:hover .Select-arrow {\n    border-top-color: var(--accent);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .is-open > .Select-control .Select-arrow {\n    border-bottom-color: var(--background-color-8-react-select);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-top-color: transparent;\n}\n\n.dash-spreadsheet .Select--multi .Select-value,\n.dash-spreadsheet .Select--multi a.Select-value-label {\n    color: var(--color-8-react-select);\n}\n\n.dash-spreadsheet .Select {\n    border-radius: 0;\n}\n\n.dash-spreadsheet .Select.is-disabled > .Select-control {\n    border: none;\n}\n\n.dash-spreadsheet .Select--multi .Select-value-icon:hover,\n.dash-spreadsheet .Select--multi .Select-value-icon:focus {\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select.is-disabled .Select-control {\n    cursor: not-allowed;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    background-color: white;\n    border: none;\n    border-bottom: thin var(--border) solid;\n    border-radius: 0;\n    border-top: none;\n}\n\n.dash-spreadsheet .Select-option {\n    background-color: inherit;\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-option:hover {\n    font-weight: bold;\n    color: var(--accent);\n    background-color: var(--hover);\n}\n\n.dash-spreadsheet .Select.is-focused:not(.is-open) > .Select-control {\n    border: none;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    background: #fff;\n}\n\n.dash-spreadsheet .Select-option.is-focused {\n    background-color: white;\n    color: var(--accent);\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select-clear {\n    color: transparent;\n}\n.dash-spreadsheet .Select:hover .Select-clear {\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-control {\n    padding-left: 2px;\n}\n"],sourceRoot:""}]),t.Z=a},5924:function(e,t,n){"use strict";var r=n(1244);e.exports=function(e){var t=typeof e;if("string"===t){var n=e;if(0==(e=+e)&&r(n))return!1}else if("number"!==t)return!1;return e-e<1}},1244:function(e){"use strict";e.exports=function(e){for(var t,n=e.length,r=0;r<n;r++)if(((t=e.charCodeAt(r))<9||t>13)&&32!==t&&133!==t&&160!==t&&5760!==t&&6158!==t&&(t<8192||t>8205)&&8232!==t&&8233!==t&&8239!==t&&8287!==t&&8288!==t&&12288!==t&&65279!==t)return!1;return!0}},5639:function(e,t,n){"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(9196),a=l(i),s=l(n(9064));function l(e){return e&&e.__esModule?e:{default:e}}var c={position:"absolute",top:0,left:0,visibility:"hidden",height:0,overflow:"scroll",whiteSpace:"pre"},u=["extraWidth","injectStyles","inputClassName","inputRef","inputStyle","minWidth","onAutosize","placeholderIsMinWidth"],p=function(e,t){t.style.fontSize=e.fontSize,t.style.fontFamily=e.fontFamily,t.style.fontWeight=e.fontWeight,t.style.fontStyle=e.fontStyle,t.style.letterSpacing=e.letterSpacing,t.style.textTransform=e.textTransform},d=!("undefined"==typeof window||!window.navigator)&&/MSIE |Trident\/|Edge\//.test(window.navigator.userAgent),f=function(){return d?"_"+Math.random().toString(36).substr(2,12):void 0},h=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.inputRef=function(e){n.input=e,"function"==typeof n.props.inputRef&&n.props.inputRef(e)},n.placeHolderSizerRef=function(e){n.placeHolderSizer=e},n.sizerRef=function(e){n.sizer=e},n.state={inputWidth:e.minWidth,inputId:e.id||f()},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"componentDidMount",value:function(){this.mounted=!0,this.copyInputStyles(),this.updateInputWidth()}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=e.id;t!==this.props.id&&this.setState({inputId:t||f()})}},{key:"componentDidUpdate",value:function(e,t){t.inputWidth!==this.state.inputWidth&&"function"==typeof this.props.onAutosize&&this.props.onAutosize(this.state.inputWidth),this.updateInputWidth()}},{key:"componentWillUnmount",value:function(){this.mounted=!1}},{key:"copyInputStyles",value:function(){if(this.mounted&&window.getComputedStyle){var e=this.input&&window.getComputedStyle(this.input);e&&(p(e,this.sizer),this.placeHolderSizer&&p(e,this.placeHolderSizer))}}},{key:"updateInputWidth",value:function(){if(this.mounted&&this.sizer&&void 0!==this.sizer.scrollWidth){var e=void 0;e=this.props.placeholder&&(!this.props.value||this.props.value&&this.props.placeholderIsMinWidth)?Math.max(this.sizer.scrollWidth,this.placeHolderSizer.scrollWidth)+2:this.sizer.scrollWidth+2,(e+="number"===this.props.type&&void 0===this.props.extraWidth?16:parseInt(this.props.extraWidth)||0)<this.props.minWidth&&(e=this.props.minWidth),e!==this.state.inputWidth&&this.setState({inputWidth:e})}}},{key:"getInput",value:function(){return this.input}},{key:"focus",value:function(){this.input.focus()}},{key:"blur",value:function(){this.input.blur()}},{key:"select",value:function(){this.input.select()}},{key:"renderStyles",value:function(){var e=this.props.injectStyles;return d&&e?a.default.createElement("style",{dangerouslySetInnerHTML:{__html:"input#"+this.state.inputId+"::-ms-clear {display: none;}"}}):null}},{key:"render",value:function(){var e=[this.props.defaultValue,this.props.value,""].reduce((function(e,t){return null!=e?e:t})),t=r({},this.props.style);t.display||(t.display="inline-block");var n=r({boxSizing:"content-box",width:this.state.inputWidth+"px"},this.props.inputStyle),o=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(this.props,[]);return function(e){u.forEach((function(t){return delete e[t]}))}(o),o.className=this.props.inputClassName,o.id=this.state.inputId,o.style=n,a.default.createElement("div",{className:this.props.className,style:t},this.renderStyles(),a.default.createElement("input",r({},o,{ref:this.inputRef})),a.default.createElement("div",{ref:this.sizerRef,style:c},e),this.props.placeholder?a.default.createElement("div",{ref:this.placeHolderSizerRef,style:c},this.props.placeholder):null)}}]),t}(i.Component);h.propTypes={className:s.default.string,defaultValue:s.default.any,extraWidth:s.default.oneOfType([s.default.number,s.default.string]),id:s.default.string,injectStyles:s.default.bool,inputClassName:s.default.string,inputRef:s.default.func,inputStyle:s.default.object,minWidth:s.default.oneOfType([s.default.number,s.default.string]),onAutosize:s.default.func,onChange:s.default.func,placeholder:s.default.string,placeholderIsMinWidth:s.default.bool,style:s.default.object,value:s.default.any},h.defaultProps={minWidth:1,injectStyles:!0},t.Z=h},4490:function(e){!function(t){"use strict";function n(){}function r(e){return e.split('"').length-1}n.prototype=Object.create(Object.prototype,{parse:{value:function(e){var t,n,o,i,a,s,l,c=[],u=0;for((o=e.split("\n")).length>1&&""===o[o.length-1]&&o.pop(),t=0,n=o.length;t<n;t+=1){for(o[t]=o[t].split("\t"),i=0,a=o[t].length;i<a;i+=1)c[u]||(c[u]=[]),s&&0===i?(l=c[u].length-1,c[u][l]=c[u][l]+"\n"+o[t][0],s&&1&r(o[t][0])&&(s=!1,c[u][l]=c[u][l].substring(0,c[u][l].length-1).replace(/""/g,'"'))):i===a-1&&0===o[t][i].indexOf('"')&&1&r(o[t][i])?(c[u].push(o[t][i].substring(1).replace(/""/g,'"')),s=!0):(c[u].push(o[t][i].replace(/""/g,'"')),s=!1);s||(u+=1)}return c},enumerable:!0,configurable:!1,writable:!1},stringify:{value:function(e){var t,n,r,o,i,a="";for(t=0,n=e.length;t<n;t+=1){for(r=0,o=e[t].length;r<o;r+=1)r>0&&(a+="\t"),"string"==typeof(i=e[t][r])?i.indexOf("\n")>-1?a+='"'+i.replace(/"/g,'""')+'"':a+=i:a+=null==i?"":i;a+="\n"}return a},enumerable:!0,configurable:!1,writable:!1}}),e.exports?e.exports=n:t.SheetClip=n}(this)}}]);
//# sourceMappingURL=async-table.js.map