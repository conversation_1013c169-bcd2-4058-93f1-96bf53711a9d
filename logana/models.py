#!/usr/bin/env python
from django.db import models
from django.urls import reverse
from django.core.files.storage import FileSystemStorage
from applications.models import Application



ss = FileSystemStorage('media')

class Domain(models.Model):
    url = models.CharField(max_length=150, unique=True)
    domain = models.CharField(max_length=150, unique=True)
    logo = models.ImageField(storage=ss, blank=True, null=True)
    
    def __str__(self):
        return self.domain
        
        
    def get_absolute_url(self):
        return reverse('prdomains', kwargs={
            'id':self.domain
        })    

class LnApplication(models.Model):
    app_name = models.CharField(max_length = 255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)    
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)


    def __str__(self):
        return self.app_name            


class Project(models.Model):

    project_name = models.CharField(max_length=150, unique=True)
    project_description = models.TextField(blank=True)
    creation_date = models.DateTimeField(auto_now_add=True,editable=False)
    domain= models.ForeignKey(Domain, on_delete = models.CASCADE, default="")
    logs_start_date = models.DateField(null=True, blank=True)
    logs_end_date  = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    is_shared = models.BooleanField(default=True)
    is_processed = models.BooleanField(default=False)
    no_rows = models.IntegerField(null=True, blank=True)
    no_cols = models.IntegerField(null=True, blank=True)
    app_id = models.ForeignKey(LnApplication, on_delete = models.CASCADE, default="")
    
    
    def __str__(self):
        return self.project_name

    def get_absolute_url(self):
        return reverse('prdetails', kwargs={
            'id':self.id,
        })    



class Document(models.Model):
    description = models.CharField(max_length=255, blank=True)
    document = models.FileField(upload_to='./')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.description


class VipPage(models.Model):
    '''Creates field in the database'''

    url = models.URLField(max_length=500)
    domain= models.ForeignKey(Domain, on_delete = models.CASCADE, default="")
    page_type = models.CharField(max_length=9, default='')
    language = models.CharField(max_length=9, default="")
    description = models.TextField(blank=True)


    def __str__(self) -> str:
        return self.url

    def get_absolute_url(self):
        return reverse('new_vip_page', kwargs={
            'id':self.id,
        })    


class AiBots(models.Model):
    bot_name = models.CharField(max_length = 255)
    bot_description = models.TextField(blank=True, default="")
    bot_is_active = models.BooleanField(default=False)



    def __str__(self):
        return self.bot_name    


