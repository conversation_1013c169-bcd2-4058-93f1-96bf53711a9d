import pandas as pd
import dash
from dash import dash_table
import plotly.graph_objs as go
from dash import Dash, dcc, html, Input, Output, State
from django_plotly_dash import DjangoDash
import plotly.express as px
import dash_bootstrap_components as dbc
import plotly.colors as pc











def all_bot_names(djQuery):
    '''This function extract all bot names from AiBots main table and
       returns a list ov bot names to be used in func for further filtering
    '''
    tmp_bot_query_list = djQuery.values()
    tmp_bot_list = [value['bot_name'] for value in tmp_bot_query_list] 

    return tmp_bot_list


def all_bot_data(bvals):
    '''Transforms raw bot data into structured dictionary with date grouping.
    
    Expected Output Format:
    {
        "ChatGPT-User": [("2024-12", 1530114), ("2025-01", 430350), ("2025-02", 581749)],
        "Amazon": [("2024-12", 235382), ("2025-01", 108287), ("2025-02", 127695)],
        "GPTBot": [("2024-12", 64543), ("2025-01", 84696), ("2025-02", 226034)],
        "OAI-SearchBot": [("2024-12", 10454), ("2025-01", 5443), ("2025-02", 10984)]
    }
    '''
    
    bot_data_holder = {}
    all_dates = set()

 
    for _, _, month, year in bvals:
        date_str = f"{year}-{str(month).zfill(2)}"
        all_dates.add(date_str)

    all_dates = sorted(all_dates) 


    for botName, count, month, year in bvals:
        date_str = f"{year}-{str(month).zfill(2)}"
        
        if botName not in bot_data_holder:
            bot_data_holder[botName] = {d: 0 for d in all_dates} 

        bot_data_holder[botName][date_str] = count  

    final_data = {
        bot: [(date, bot_data_holder[bot][date]) for date in all_dates]
        for bot in bot_data_holder
    }

    return final_data


def all_bot_ref_data(data, colNames):
    '''Transforms raw database output to 
       pandas DAtaframe using database column names as 
       Pandas cdataframe col names
    '''



    data_holder = []
    all_dates = set()
    for row in data:
        date_string = f"{row[5]}-{str(row[4]).zfill(2)}"
        all_dates.add(date_string)

    all_dates = sorted(all_dates) 
    for row in data:
        if "Direct Hit" in row:
            data_holder.append(row[2])         

    

    
    return data_holder, all_dates



def pack_all_ai_bots_data(data):
    '''This function packs the data'''
    
    botNames = []
    botValues = []
    botProportions = []
    botMonts = []
    botYear = []

    for item in data[0]:
        botNames.append(item[2])
        botValues.append(item[3])
        botProportions.append(item[4])
        botMonts.append(item[5])
        botYear.append(item[6])

        
    zippedVals = list(zip(botNames, botValues, botMonts, botYear))


    return zippedVals


def table_data_pack(data, colNames):
    '''Packes the data fdrom the database in dataframe and 
       uses dataframe to populate interacive table
    '''
    df = pd.DataFrame(data, columns=colNames)
    return df

def ai_individual_dash_bar(lst,
                colNames, 
                xin, 
                yin, 
                color, 
                #color_discrete_sequence, 
                label, 
                default_height, 
                default_width):
    """Takes the list as data input and returns bar plot """
    df = pd.DataFrame(lst,columns=colNames)

    barPlot = px.bar(
        df,
        x=xin,
        y=yin,
        opacity=0.99,
        template="simple_white",
        color=color,

        labels=label,
        text_auto='.7s',
        #color_discrete_sequence=color_discrete_sequence
    ).to_html(full_html=False, default_height=default_height, default_width=default_width)

    return barPlot



def ai_bot_activity_plot(bot_data):
    '''This function creates a Plotly Dash line chart for AI bot activity over time and integrates it into Django. '''

    # Transform dictionary into a DataFrame
    data = []
    for bot, values in bot_data.items():
        for date, cnt in values:
            data.append({"BotName": bot, "Date": date, "Cnt": cnt})
    
    df = pd.DataFrame(data)

    
    # Convert Date column to datetime format and sort
    df = df.sort_values(by=["BotName", "Date"])
    df["Cnt"] = df["Cnt"].astype(float) 
    # Initialize DjangoDash app
    app = DjangoDash("aiBotActivityPlot", add_bootstrap_links=True, 
                     serve_locally=True
                     )
    
    #Create the Plotly figure
    df['Cnt'] = df['Cnt'].astype(float)
    fig = px.line(df, x=df["Date"].tolist(), y='Cnt', color="BotName", markers=True,
                title="Bot Activity Over Time",
                labels={"Date": "Date", "Cnt": "Cnt", "BotName": "Bot"},
                line_group="BotName",
             
                )

    fig.update_traces(mode='lines+markers', hoverinfo='x+y+name')
    # fig.update_layout(legend_title_text="Bots", xaxis_title="Date", yaxis_title="Count",
    #                 xaxis=dict(tickformat="%b %Y"), hovermode="x unified",
    #                 yaxis=dict(type="linear", showline=True))

    fig.update_layout(
        legend_title_text="Bots",
        xaxis_title="Date",
        yaxis_title="Count",
        xaxis=dict(tickformat="%b %Y"),
        hovermode="x unified",
        yaxis=dict(type="linear", showline=True),
        autosize=True,
        margin=dict(l=40, r=40, t=40, b=40),
        height=None  # Ensures the plot adapts to its container
    )

    fig.show()
    # app.layout = html.Div([
    #     dcc.Graph(id="graph", style={"height": "500px", "overflow": "hidden"})
    # ])
    app.layout = html.Div([
    dcc.Graph(
        id="graph",
        config={"responsive": True},
        style={
            "width": "100%",
            "height": "100%",
        }
     )
    ])
    print(fig)

    @app.callback(
        Output("graph", "figure"),
        Input("graph", "id") 
    )
    def update_graph(_):
        return fig 

   
    
    return app




def create_bot_activity_app(bot_data, app_name):
    # Step 1: Prepare the DataFrame
    data = []
    for bot, values in bot_data.items():
        for date, cnt in values:
            data.append({"BotName": bot, "Date": date, "Count": cnt})
    try:
        df = pd.DataFrame(data)
        df["Date"] = pd.to_datetime(df["Date"])
        df["MonthYear"] = df["Date"].dt.to_period("M").dt.to_timestamp()
        df["MonthYearStr"] = df["MonthYear"].dt.strftime("%b %Y")
        df = df.groupby(["BotName", "MonthYear", "MonthYearStr"]).agg({"Count": "sum"}).reset_index()
        df = df.sort_values(by="MonthYear")
    except Exception as e:
        print("No data, creating placeholder")
        df = pd.DataFrame(columns=['Date', 'MonthYear', 'BotName', 'Count' ])  

    # Step 2: Define bot colors
    bot_names = df["BotName"].unique()
    color_palette = pc.qualitative.Plotly
    bot_color_map = {bot: color_palette[i % len(color_palette)] for i, bot in enumerate(bot_names)}

    # Step 3: Create DjangoDash app
    FONT_AWESOME = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    external_stylesheets = [dbc.themes.PULSE, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    # Step 4: Define layout
    app.layout = html.Div([
        dcc.Graph(
            id='bot-activity-graph',
            style={'width': '100%', 'height': '100%'}
        )
    ], style={'width': '100%', 'height': '100%'})

    # Step 5: Define callback
    @app.callback(
        Output('bot-activity-graph', 'figure'),
        Input('bot-activity-graph', 'id')
    )
    def update_graph(_):
        figure_data = []
        for bot in bot_names:
            subset = df[df["BotName"] == bot]
            color = bot_color_map[bot]

            figure_data.append({
                'x': subset["MonthYearStr"],
                'y': subset["Count"],
                'type': 'scatter',
                'mode': 'lines+markers',
                'name': bot,
                'line': {
                    'width': 3,
                    'color': color
                },
                'marker': {
                    'color': 'white',
                    'size': 10,
                    'line': {
                        'color': color,
                        'width': 2
                    },
                    'symbol': 'circle'
                }
            })

        return {
            'data': figure_data,
            'layout': {
                'xaxis': {'title': 'Month'},
                'yaxis': {'title': 'Count'},
                'hovermode': 'closest',
                'autosize': False,
                'margin': {'l': 50, 'r': 30, 't': 20, 'b': 50},
                'legend': {'orientation': 'h', 'x': 0, 'y': -0.3},
                'transition': {'duration': 300},
                'responsive': True,
                'overflow': 'hidden'
            }
        }

    return app



def create_dhit_viz(data, dates, app_name):
    '''Creates bar chart for Direct Hit with "YYYY-MM" x-axis labels.'''
    FONT_AWESOME = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    external_stylesheets = [dbc.themes.PULSE, FONT_AWESOME]
    
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)


 

    fig = go.Figure(
        [go.Bar(x=dates, y=data)]
    )

    fig.update_layout(
        xaxis_title="Month",
        yaxis_title="Hits",
        xaxis=dict(
            tickmode="array",
            tickvals=dates,
            ticktext=dates
        )
    )

    return fig.to_html(full_html=False, include_plotlyjs='cdn')






def create_landing_table_app(data, col_names, app_name):
    df = pd.DataFrame(data, columns=col_names)
    df = df[["url", "value", "month"]]

    app = DjangoDash(name=app_name, external_stylesheets=[
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    ])

    app.layout = html.Div([
        html.Div([
            html.Button("Download CSV", id="download-btn", className="btn btn-success mb-3"),
            dcc.Download(id="download-dataframe-csv"),

            dash_table.DataTable(
                id='landing-table',
                columns=[
                    {"name": "Landing Page", "id": "url", "type": "text"},
                    {"name": "Count", "id": "value", "type": "numeric"},
                    {"name": "Month", "id": "month", "type": "text"},
                ],
                data=df.to_dict('records'),
                page_size=10,
                style_table={
                    'overflowY': 'auto',
                    'maxHeight': '500px',
                    'overflowX': 'auto',
                    'border': '1px solid #ccc',
                    'width': '100%',
                },
                style_cell={
                    'textAlign': 'left',
                    'font_family': 'Arial',
                    'font_size': '14px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                    'padding': '8px',
                    'minWidth': '100px',
                    'maxWidth': '800px',
                },
                sort_action='native',
                filter_action='native',
                style_header={
                    'backgroundColor': 'paleturquoise',
                    'fontWeight': 'bold',
                    'font_family': 'Arial Black',
                    'font_size': '15px',
                },
                style_data={
                    'backgroundColor': 'lavender',
                }
            )
        ])
    ])

    @app.callback(
        Output("download-dataframe-csv", "data"),
        Input("download-btn", "n_clicks"),
        State("landing-table", "derived_virtual_data"),
        prevent_initial_call=True,
    )
    def download_filtered(n_clicks, filtered_data):
        if filtered_data is None or len(filtered_data) == 0:
            return dcc.send_data_frame(df.to_csv, "landing_pages.csv", index=False)
        else:
            filtered_df = pd.DataFrame(filtered_data)
            return dcc.send_data_frame(filtered_df.to_csv, "filtered_landing_pages.csv", index=False)
        

    



def referall_landing_table_app(data, col_names, app_name):
    df = pd.DataFrame(data, columns=col_names)
    df = df[col_names[1:5]]  #ima razlog

    app = DjangoDash(name=app_name, external_stylesheets=[
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    ])

    app.layout = html.Div([
        html.Div([
            html.Button("Download CSV", id="download-btn", className="btn btn-success mb-3"),
            dcc.Download(id="download-ref-land-csv"),

            dash_table.DataTable(
                id='landing-table-ref-lan',
                columns=[
                    {"name": "BotName", "id": "botName", "type": "text"},
                    {"name": "Landing Page", "id": "referring_url", "type": "text"},
                    {"name": "Referal Page", "id": "referral_url", "type": "text"},
                    {"name": "Count", "id": "value", "type": "numeric"},
                ],
                data=df.to_dict('records'),
                page_size=10,
                style_table={
                    'overflowY': 'auto',
                    'maxHeight': '500px',
                    'overflowX': 'auto',
                    'border': '1px solid #ccc',
                    'width': '100%',
                },
                style_cell={
                    'textAlign': 'left',
                    'font_family': 'Arial',
                    'font_size': '10px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                    'padding': '8px',
                    'minWidth': '100px',
                    'maxWidth': '800px',
                },
                sort_action='native',
                filter_action='native',
                style_header={
                    'backgroundColor': 'paleturquoise',
                    'fontWeight': 'bold',
                    'font_family': 'Arial Black',
                    'font_size': '15px',
                },
                style_data={
                    'backgroundColor': 'lavender',
                }
            )
        ])
    ])

    @app.callback(
        Output("download-ref-land-csv", "data"),
        Input("download-btn", "n_clicks"),
        State("landing-table", "derived_virtual_data"),
        prevent_initial_call=True,
    )
    def download_filtered(n_clicks, filtered_data):
        if filtered_data is None or len(filtered_data) == 0:
            return dcc.send_data_frame(df.to_csv, "landing_ref_pages.csv", index=False)
        else:
            filtered_df = pd.DataFrame(filtered_data)
            return dcc.send_data_frame(filtered_df.to_csv, "filtered_landing_ref_pages.csv", index=False)  

    return app_name 


def top_landing_table_app(data, col_names, app_name):
    df = pd.DataFrame(data, columns=col_names)
    df = df[col_names[1:5]]  #ima razlog


    app = DjangoDash(name=app_name, external_stylesheets=[
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    ])

    app.layout = html.Div([
        html.Div([
            html.Button("Download CSV", id="download-btn", className="btn btn-success mb-3"),
            dcc.Download(id="download-ref-land-csv"),

            dash_table.DataTable(
                id='landing-table-ref-lan',
                columns=[
                    {"name": "Landing Page", "id": "url", "type": "text"},
                    {"name": "Count", "id": "value", "type": "numeric"},
                ],
                data=df.to_dict('records'),
                page_size=10,
                style_table={
                    'overflowY': 'auto',
                    'maxHeight': '500px',
                    'overflowX': 'auto',
                    'border': '1px solid #ccc',
                    'width': '100%',
                },
                style_cell={
                    'textAlign': 'left',
                    'font_family': 'Arial',
                    'font_size': '10px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                    'padding': '8px',
                    'minWidth': '100px',
                    'maxWidth': '800px',
                },
                sort_action='native',
                filter_action='native',
                style_header={
                    'backgroundColor': 'paleturquoise',
                    'fontWeight': 'bold',
                    'font_family': 'Arial Black',
                    'font_size': '15px',
                },
                style_data={
                    'backgroundColor': 'lavender',
                }
            )
        ])
    ])

    @app.callback(
        Output("download-ref-land-csv", "data"),
        Input("download-btn", "n_clicks"),
        State("landing-table", "derived_virtual_data"),
        prevent_initial_call=True,
    )
    def download_filtered(n_clicks, filtered_data):
        if filtered_data is None or len(filtered_data) == 0:
            return dcc.send_data_frame(df.to_csv, "landing_ref_pages.csv", index=False)
        else:
            filtered_df = pd.DataFrame(filtered_data)
            return dcc.send_data_frame(filtered_df.to_csv, "filtered_landing_ref_pages.csv", index=False)  

    return app_name   



def referall_target_table_app(data, col_names, app_name):
    df = pd.DataFrame(data, columns=col_names)
    df = df[col_names[1:5]]  #ima razlog
    app = DjangoDash(name=app_name, external_stylesheets=[
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
    ])

    app.layout = html.Div([
        html.Div([
            html.Button("Download CSV", id="download-btn", className="btn btn-success mb-3"),
            dcc.Download(id="download-ref-land-csv"),

            dash_table.DataTable(
                id='landing-table-ref-lan',
                columns=[
                    {"name": "Landing Page", "id": "referring_url", "type": "text"},
                    {"name": "Referal Page", "id": "referral_url", "type": "text"},
                    {"name": "Count", "id": "value", "type": "numeric"},
                ],
                data=df.to_dict('records'),
                page_size=10,
                style_table={
                    'overflowY': 'auto',
                    'maxHeight': '500px',
                    'overflowX': 'auto',
                    'border': '1px solid #ccc',
                    'width': '100%',
                },
                style_cell={
                    'textAlign': 'left',
                    'font_family': 'Arial',
                    'font_size': '10px',
                    'whiteSpace': 'normal',
                    'height': 'auto',
                    'padding': '8px',
                    'minWidth': '100px',
                    'maxWidth': '800px',
                },
                sort_action='native',
                filter_action='native',
                style_header={
                    'backgroundColor': 'paleturquoise',
                    'fontWeight': 'bold',
                    'font_family': 'Arial Black',
                    'font_size': '15px',
                },
                style_data={
                    'backgroundColor': 'lavender',
                }
            )
        ])
    ])

    @app.callback(
        Output("download-ref-land-csv", "data"),
        Input("download-btn", "n_clicks"),
        State("landing-table", "derived_virtual_data"),
        prevent_initial_call=True,
    )
    def download_filtered(n_clicks, filtered_data):
        if filtered_data is None or len(filtered_data) == 0:
            return dcc.send_data_frame(df.to_csv, "landing_ref_pages.csv", index=False)
        else:
            filtered_df = pd.DataFrame(filtered_data)
            return dcc.send_data_frame(filtered_df.to_csv, "filtered_landing_ref_pages.csv", index=False)  

    return app_name 




def daily_bot_activity_app(df, bot_name, app_name="dailyActivity"):
    try:
        
        df = pd.DataFrame(df, columns=["visit_date", "visit_count", "bot_id"])

        df = df.sort_values("visit_date")
        df["visit_date"] = pd.to_datetime(df["visit_date"]).dt.date
        df["visit_count"] = df["visit_count"].astype(int).astype(object)


      
        fig = px.bar(
            df,
            x="visit_date",
            y="visit_count",
            text="visit_count",
            title=f"Daily Activity for {bot_name}",
            labels={"visit_date": "Date", "visit_count": "Number of Requests"},
            template="simple_white",
            height=400,
            color_discrete_sequence=["#FF5733"]
        )
        fig.update_traces(
            textposition="outside",
            hovertemplate="Date: %{x}<br>Number of Requests: %{y}<extra></extra>"
        )
        fig.update_layout(
            xaxis=dict(
                tickmode='array',
                tickvals=df['visit_date'],
                ticktext=df['visit_date'],
                tickangle=-45 
            )

        )        

    except Exception as e:
        print(f"[daily_bot_activity_app] No data: {e}")
        fig = px.bar(
            pd.DataFrame({"visit_date": [], "visit_count": []}),
            x="visit_date",
            y="visit_count",
            title=f"No Activity for {bot_name} this month",
            template="simple_white",
            height=400
        )

   
    app = DjangoDash(
        name=app_name,
        external_stylesheets=[
            "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
        ]
    )

    app.layout = html.Div([
        html.Div([
            html.H5(f"Activity of {bot_name}", className="text-center mb-3"),
            dcc.Graph(figure=fig)
        ], className="card-body")
    ])

    return app_name


            
  
