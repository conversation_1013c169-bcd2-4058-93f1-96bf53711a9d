from django import forms
from .models import Domain, Application
from logana.processes.utilities import conn
from django.contrib.auth.models import Group
from django.core.cache import cache

class CreateProjectForm(forms.Form):
    title = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': 'form-control rounded-10', 'placeholder': 'Title', 'id': 'title', 'name': 'title'})
    )
    description = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': "form-control rounded-10", 'id': "desc", 'rows': "10", 'placeholder': "Description...", 'name': 'Description'})
    )

    def __init__(self, *args, **kwargs):
        super(CreateProjectForm, self).__init__(*args, **kwargs)
        self.fields['domains'] = forms.ChoiceField(
            label='',
            choices=self.get_domain_choices(),
            widget=forms.Select(attrs={'class': 'form-control form-control-sm rounded-10', 'id': 'domain', 'name': 'domain'})
        )

    def get_domain_choices(self):
        domains_query = Domain.objects.all()
        if domains_query.exists():
            return [(domain.id, domain.domain) for domain in domains_query]
        else:
            return [('', 'No domains available')]

    def clean_title(self):
        cleaned_data = self.cleaned_data
        title = cleaned_data.get("title")
        return title
    

class CreateNewVipPageForm(forms.Form):
    title = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': 'form-control rounded-10', 'placeholder': 'Enter URL', 'id': 'title', 'name': 'title'})
    )
    description = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': "form-control rounded-10", 'id': "desc", 'rows': "10", 'placeholder': "Description...", 'name': 'Description'})
    )

    def __init__(self, *args, **kwargs):
        super(CreateNewVipPageForm, self).__init__(*args, **kwargs)
        self.fields['domains'] = forms.ChoiceField(
            label='',
            choices=self.get_domain_choices(),
            widget=forms.Select(attrs={'class': 'form-control form-control-sm rounded-10', 'id': 'domain', 'name': 'domain'})
        )

    def get_domain_choices(self):
        domains_query = Domain.objects.all()
        if domains_query.exists():
            return [(domain.id, domain.domain) for domain in domains_query]
        else:
            return [('', 'No domains available')]    
        
class RequestAccessForm(forms.Form):
    '''Request access form'''
    first_name = forms.CharField(
        label='First Name',
        widget=forms.TextInput(attrs={
            'class': 'form-control rounded-10',
            'placeholder': 'Enter Your First Name',
            'id': 'first_name',
            'name': 'first_name'
        })
    )
    last_name = forms.CharField(
        label='Last Name',
        widget=forms.TextInput(attrs={
            'class': "form-control rounded-10",
            'id': "last_name",
            'placeholder': "Enter Your Last Name",
            'name': 'last_name'
        })
    )
    email = forms.EmailField(
        label='Work Email',
        widget=forms.EmailInput(attrs={
            'class': "form-control rounded-10",
            'id': "email",
            'placeholder': "Enter your work email",
            'name': 'email'
        })
    )
    slack_handle = forms.CharField(
        label='Slack Handle:',
        widget=forms.TextInput(attrs={
            'class': "form-control rounded-10",
            'id': "slack_handle",
            'placeholder': "Slack Handle",
            'name': 'slack_handle'
        })
    )

    def __init__(self, *args, **kwargs):
        super(RequestAccessForm, self).__init__(*args, **kwargs)
        self.fields['teams'] = forms.ChoiceField(
            label="Choose Team",
            choices=[('', '--- Select a Team ---')] + self.get_team_choices(),
            widget=forms.Select(attrs={
                'class': 'form-control form-control-sm rounded-10',
                'id': 'teams',
                'name': 'teams'
            })
        )
        self.fields['applications'] = forms.MultipleChoiceField(
            label="Choose Applications",
            choices=self.get_application_choices(),
            widget=forms.SelectMultiple(attrs={
                'class': 'form-control form-control-sm rounded-10',
                'id': 'applications',
                'name': 'applications'
            })
        )

    def get_team_choices(self):
        cache_key = 'team_choices'  
        choices = cache.get(cache_key) 
        if not choices:
            groups_query = Group.objects.all()
            choices = [(group.id, group.name) for group in groups_query]
            cache.set(cache_key, choices, 60 * 60)  
        return choices

    def get_application_choices(self):
        cache_key = 'application_choices'  
        choices = cache.get(cache_key)  
        if not choices:
            apps_query = Application.objects.all()
            choices = [(app.id, app.app_name) for app in apps_query]
            cache.set(cache_key, choices, 60 * 60)  
        return choices

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if not email.endswith('@bettercollective.com'): 
            raise forms.ValidationError("Please use a valid company email address.")
        return email