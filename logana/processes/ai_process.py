from decouple import config
import mysql.connector
from mysql.connector import errorcode
import os
import pandas as pd
from queries import *
from ai_bot_helpers import  pack_referring_pages, pack_referrals_pages, pack_response_by_bot,  \
    calculate_props, pack_direct_hits, pack_inDirect_hits, pack_referring_pages_with_bot, prepare_iq_for_reff_pages_with_bot, \
    prepare_iq_for_reff_pages_dh_with_bot, prepare_and_pack_daily_visit_data, preapre_and_pack_top_landing_pages_stats




CSV_PATH = 'logana/processes/results/final.csv'
outputFilePath = 'logana/processes/results/ai_bots.csv'

def process_ai():
    '''Receives file prepares and isert the data in the dtabase'''

    print("Starting extraction...")
    

    def conn():
        print("Connecting DB...")
        try:
            cnx = mysql.connector.connect(
                user = config('DATABASE_USER'),
                password = config('DATABASE_PASSWORD'),
                host = config('DATABASE_HOST'),
                database = config('DATABASE_NAME'),
            )
            print('Connected...')
            return cnx
        except mysql.connector.Error as err:
            print("Connection problem...", err)  

    def locateFile(name, path):
        '''Using this to locate file in the directory if necesery'''
        filedat = os.walk(path)
        for root, dirs, files in filedat:

            if name in files:
                return name
            else:
                print("file name not found!")
                return None    
            
    def saveToDb(insertQuery, insertQueryList, many=False):
        '''Open db connection and combine insertQuery and insertQueryList'''
        cnx = conn()
        cursor = cnx.cursor()
        if many == False:
            cursor.executemany(insertQuery, insertQueryList)
        else:
            cursor.execute(insertQuery, insertQueryList)
        cnx.commit()
        cursor.close()
        cnx.close()            
    


    def list_bots_from_db():
        """
        This function retrieves a list of active AI bots from the database.
        """
        sql_query = "SELECT * FROM logana.logana_aibots WHERE bot_is_active = 1"

        try:
            cnx = conn()  
            cursor = cnx.cursor()
            cursor.execute(sql_query)
            bots = [row for row in cursor.fetchall()]
        except mysql.connector.Error as err:
            print(f"Database error: {err}")
            return []
        finally:
            cursor.close()
            cnx.close()

        return bots


    def bots_pattern(bots_lst):
        '''Receives a list of bots and return a pattern format'''
        if not isinstance(bots_lst, list):
            raise ValueError("Expected a list of bots names")
        return f"({'|'.join(bots_lst)})" if bots_lst else ""   

    def create_new_file(inputFilePath, outputFilePath, ai_bots_list):
        '''Creates new file from iput csv 
           that contains only AI related data
           and returns temp, filterd csv
        '''
        bots_list = [bot[2] for bot in ai_bots_list]
        df = pd.read_csv(inputFilePath)
        df_filtered = df[df["user_agent"].str.contains('|'.join(bots_list), case=False, na=False)]
        df_filtered = df_filtered.copy()
        df_filtered['AiBots'] = df_filtered['user_agent'].str.extract(bots_pattern(bots_list), expand=False)
        df_filtered.to_csv('logana/processes/results/ai_bots.csv', index=False)


        if os.path.exists(outputFilePath):
            return outputFilePath
        else:
            print("File not created!")    
     




    def getMainStats():
        '''Fetch user id, project id, month and year along with specific column names and return data in inner space'''
        tmp_list = []
        cnx = conn()
        cursor = cnx.cursor()
        cursor.execute('''SELECT id, creation_date, logs_start_date, no_rows, domain_id  FROM logana.logana_project where is_active= 0''')
        data = cursor.fetchone()
        dataLst = list(data)
        cnx.commit()
        colNames = [i[0] for i in cursor.description]
        clsData = dict(zip(colNames, dataLst))
        projectId = clsData['id']
        try:
            lsd = clsData['logs_start_date'].month
        except:
            lsd = None    
        month = clsData['logs_start_date'].date().month
        year = clsData['logs_start_date'].date().year
        noRows = clsData['no_rows']
        domain_id = clsData['domain_id']
        tmp_list = [projectId, month, lsd, year, noRows, domain_id]
        cursor.close()
        cnx.close()
        return tmp_list
    
    main_stats_lst =getMainStats()
    bot_list= list_bots_from_db()

    
    

    def calculate_aibots_summary(inputFilePath, mainStats):
        '''Calculating ai bots summary from the csv
           This is cumulative summarization for particular projec
           meaning for the  speciffic month
        '''
        df = pd.read_csv(inputFilePath)
        data = df['AiBots'].value_counts()
        insert_list = []
        botNames = list(data.index)
        botValues = list(data.values)
        zippedBvals = zip(botNames, botValues)
        for names, values in zippedBvals:
            insert_list.append((
                               0,  #id
                               mainStats[0], #project id
                               names,  #names
                               int(values), #bot values
                               calculate_props(mainStats[4], values),  #This will be fetched via separate function
                               mainStats[1], #month
                               mainStats[3],  #yesr
                               mainStats[5], #domain _id
                             ))
        saveToDb(insertQuery=ai_bot_main_stats_query, insertQueryList=insert_list)


    def insert_refferring_page_summary(data):
        '''Receives the data from external function
           for reffering pages and return a list ov values
           this is used to populate the database
        '''
        insertList = []
        lsd, year, zippedObj = data
        project_id = main_stats_lst[0]
        for urls, values in zippedObj:
            insertList.append((0, urls, int(values), calculate_props(main_stats_lst[4], int(values)), int(lsd), int(year), project_id, main_stats_lst[5]))

        saveToDb(insertQuery=ai_bot_referrp_main_stats_query, insertQueryList=insertList)



    def insert_referal_page_summary(data):
        ''' Receives the data from external function 
            for referal pages and returns a zipped object and month and logs
            start date
        '''    
        
        insertList = []
        lsd, year, zippedObj = data
        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5]
        for url, value in zippedObj:
            insertList.append((0, url, int(value), calculate_props(main_stats_lst[4], int(value)), int(lsd), int(year), project_id, domain_id))

        saveToDb(insertQuery=ai_bot_referal_main_stats_query, insertQueryList=insertList)



    def insert_response_cd_by_bot(inputAiCsv, bots, noRows):
        '''Insert response code data in ln_AiBotsMainRespCodeStats db table'''

        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5]
        data = pack_response_by_bot(inputAiCsv, bots, noRows, project_id=project_id, domain_id=domain_id)
                 
        saveToDb(insertQuery=ai_bot_referal_main_resp_codes_stats_query, insertQueryList=data)


    def refereal_landing_insert(inputAiCsv, bots, noRows):
        '''Insert direct hits in the database'''
        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5] 


        data = pack_direct_hits(inputAiCsv, bots, noRows, project_id=project_id, domain_id=domain_id)
        print(data)
        saveToDb(insertQuery=ai_bot_referal_referring_stats, insertQueryList= data)
        

    def indirect_hits_insert(inputAiCsv, bots, noRows, colName, query):
        '''Inser indirect hits in the database'''
        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5] 
        data = prepare_iq_for_reff_pages_with_bot(
                insertCsv=inputAiCsv,
                botList=bots,
                noRows=noRows,
                project_id=project_id,
                domain_id=domain_id,
                colName=colName
            )
        
        print(data[0:10])
        saveToDb(insertQuery=query, insertQueryList= data)

    def direct_hits_insert(inputAiCsv, bots, noRows, colName, query):
        '''INsert Direct hits from referring_pages column into the database''' 
        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5]
        data = prepare_iq_for_reff_pages_dh_with_bot(
            insertCsv=inputAiCsv,
            botList=bots,
            noRows=int(noRows),  #Direktna konverzija javljao mi gresku u nekim slucajevima
            project_id=project_id,
            domain_id=domain_id,
            colName=colName
        )   
        # import numpy as np
        # for item in data:
        #     for part in item:
        #         if type(part) == np.int64:
        #             print(part)
        
        #Dodaj ovde jos za insert DB I NAPRAVI QUERY

        saveToDb(insertQuery=query, insertQueryList= data)


    def daily_bot_freq(inputAiCsv, bots, noRows, colName, query):
        '''Saves the daily frequency visit
          for each bot detected in the csv  
        '''  
        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5]        
        #DOdaj funkciju za pakovanje podataka i query  
        data = prepare_and_pack_daily_visit_data(insertCsv=inputAiCsv,
                                           botList=bots, 
                                           noRows=noRows, 
                                           project_id=project_id,
                                           domain_id=domain_id,
                                           colName=colName
                                           )
        
        print(data)
        saveToDb(insertQuery=query, insertQueryList= data)
    
   


    def insert_top_landing_pages_by_bot(inputAiCsv, bots, noRows, colName, query):
        '''
        Saves the data in the ln_AiBotsReferalLAndingPageStatByBotId
        sanding insertQueryList to saveDb func
        
        '''
        project_id = main_stats_lst[0]
        domain_id = main_stats_lst[5]         
        data = preapre_and_pack_top_landing_pages_stats(
                                           insertCsv=inputAiCsv,
                                           botList=bots, 
                                           noRows=noRows, 
                                           project_id=project_id,
                                           domain_id=domain_id,
                                           colName=colName
        )
    


        saveToDb(insertQuery=query, insertQueryList= data)




    #Main section meaning all bots stats

    create_new_file(inputFilePath=CSV_PATH, outputFilePath=outputFilePath, ai_bots_list=bot_list)   #Extraction - Creates new csv  - Min Njet

    # calculate_aibots_summary(inputFilePath=outputFilePath, mainStats=main_stats_lst)  #Insert into database main stats +Min Njet
    # insert_refferring_page_summary(data=pack_referring_pages(inputAiCsv=outputFilePath))   # Provereno Min Njet
    # insert_referal_page_summary(data=pack_referrals_pages(inputAiCsv=outputFilePath))   #Provereno Min NJet
    # insert_response_cd_by_bot(inputAiCsv=outputFilePath, bots=bot_list, noRows=main_stats_lst[4])   #Provereno Min Njet
    # indirect_hits_insert(inputAiCsv=outputFilePath, bots=bot_list, noRows=main_stats_lst[4], colName='referrer', query=ai_botpl_referal_referring_stats)  #+  Provereno Min Njet
    # direct_hits_insert(inputAiCsv=outputFilePath, bots=bot_list, noRows=main_stats_lst[4], colName='referring_page', query=None) #Ne radi
    # refereal_landing_insert(inputAiCsv=outputFilePath, bots=bot_list, noRows=main_stats_lst[4])   #Provereno min njet
    #insert_top_landing_pages_by_bot(inputAiCsv=outputFilePath, bots=bot_list, noRows=main_stats_lst[4], colName='referring_page', query=ai_top_landing_pages_query)
    daily_bot_freq(inputAiCsv=outputFilePath, bots=bot_list,noRows=main_stats_lst[4], colName=None, query=ai_daily_visits_query)


if __name__=="__main__":
    process_ai()   