#!usr/bin/env python3




#Logana-project ctable


#General Main Bot Stats Area
gen_bot_main_stats_query=  '''INSERT INTO ln_genBotsMainStats(id,project_id,botName,botStats,botStatsProp,isNumeric,month,year,notes) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_target_query =  '''INSERT INTO ln_genBotsMainStatsByTarget(id,target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_source_query = '''INSERT INTO ln_genBotsMainStatsBySource(id, source, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_method_query = '''INSERT INTO ln_genBotsMainStatsByMethod(id, method, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_verification = '''INSERT INTO ln_genBotsMainStatsByVerification(id, verified, unverified, month, year, project_id) VALUES (%s, %s, %s, %s, %s, %s) '''
gen_bot_main_by_ref_page_query = '''INSERT INTO ln_genBotsMainStatsByRefPage(id, url, value, valueProp, month, year, project_id) VALUES (%s, %s, %s, %s, %s, %s, %s)'''
gen_bot_main_by_res_code_stats = '''INSERT INTO ln_geBotsMainRespCodeStats(id, code, value, valueProp, month, year, project_id) VALUES (%s, %s, %s, %s, %s, %s, %s)'''
gen_bot_main_by_target_query_dhit= '''INSERT INTO ln_genBotsMainStatsByTargetDhit(id,target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_target_query_ndhit= '''INSERT INTO ln_genBotsMainStatsByTargetNDhit(id,target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_target_query_version= '''INSERT INTO ln_genBotsMainStatsByProtVersion(id,protocol, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
gen_bot_main_by_sitemap_query = '''INSERT INTO ln_genBotsMainStatsBySitemap(id, url, value, valueProp, month, year, project_id) VALUES (%s, %s, %s, %s, %s, %s, %s)'''
#main resp code stats
gen_bot_main_rp_stats_referring = '''INSERT INTO ln_genRespCodes(id, response_code, value_list, url, domain, month, year, project_id) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)'''

#Google Bot Stats Area
google_bot_main_by_source_query = '''INSERT INTO ln_googleBotsMainStatsBySource(id, source, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_main_by_target_query = '''INSERT INTO ln_googleBotsMainStatsByTarget(id, target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_main_resp_code_st_query = '''INSERT INTO ln_googleBotsMainRespCodeStats(id,code,value, valueProp,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_main_method_st_query = '''INSERT INTO ln_googleBotsMainMethodStats(id,method,value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_main_protocol_st_query = '''INSERT INTO ln_googleBotsMainProtocolStats(id,protocol,value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''

#Google Bot Stats Area - Direct Hits
google_bot_direct_hit_main_query = '''INSERT INTO ln_googleBotsStatsDirectHit(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_stcode_query = '''INSERT INTO ln_googleBotsStatsDirectHitByStCode(id, status_code, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_page_query = '''INSERT INTO ln_googleBotsStatsDirectHitByBigPage(id, target, value, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_bigimage_query = '''INSERT INTO ln_googleBotsStatsDirectHitByBigImage(id, target, size,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_bigcss_query = '''INSERT INTO ln_googleBotsStatsDirectHitByBigCSS(id, target, size,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_bigjson_query = '''INSERT INTO ln_googleBotsStatsDirectHitByBigJson(id, target, size,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_php_query = '''INSERT INTO ln_googleBotsStatsDirectHitByPhP(id, target, value, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_direct_hit_by_target_query = '''INSERT INTO ln_googleBotsStatsDirectHitByTarget(id, target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''

##Google bot Stats Area - Non DIrect Hits
google_bot_ndirect_hit_main_query = '''INSERT INTO ln_googleBotsStatsNDHit(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_stcode_query = '''INSERT INTO ln_googleBotsStatsNDHitByCode(id, status_code, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_bigimage_query = '''INSERT INTO ln_googleBotsStatsNDirectHitByBigImage(id, target, size,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_bigcss_query = '''INSERT INTO ln_googleBotsStatsNDirectHitByBigCSS(id, target, size,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_bigjson_query = '''INSERT INTO ln_googleBotsStatsNDirectHitByBigJson(id, target, size,month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_page_query = '''INSERT INTO ln_googleBotsStatsNDirectHitByBigPage(id, target, size, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_php_query = '''INSERT INTO ln_googleBotsStatsNDirectHitByPhP(id, target, size, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s)'''
google_bot_ndirect_hit_by_referring_page = '''INSERT INTO ln_googleBotsStatsNDirectHitRefferingPage(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''


## Google Bot Desktop
google_bot_desktop_by_ip= '''INSERT INTO ln_googleBotsDesktopIPStats(id, ip, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_method= '''INSERT INTO ln_googleBotsDesktopMethodStats(id, method, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_protocol= '''INSERT INTO ln_googleBotsDesktopProtocolStats(id, protocol, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_ref_page= '''INSERT INTO ln_googleBotsDesktopRefPageStats(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_ref_url_page= '''INSERT INTO ln_googleBotsDesktopRefUrlStats(id, referrer_url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_size_page= '''INSERT INTO ln_googleBotsDesktopSizeStats(id, sizeCount, mean, std, min, twfive, fifty, seventyFive, max, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s, %s, %s, %s, %s, %s)'''
google_bot_desktop_by_status_code= '''INSERT INTO ln_googleBotsDesktopStatusCodeStats(id, statusCode, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_target= '''INSERT INTO ln_googleBotsDesktopTargetStats(id, target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_desktop_by_user_agent = '''INSERT INTO ln_googleBotsDesktopUserAgentStats(id, uaString, value, valuesProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
## Google Bot Mobile
google_bot_mobile_by_ip= '''INSERT INTO ln_googleBotsMobileIPStats(id, ip, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_method= '''INSERT INTO ln_googleBotsMobileMethodStats(id, method, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_protocol= '''INSERT INTO ln_googleBotsMobileProtocolStats(id, protocol, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_ref_page= '''INSERT INTO ln_googleBotsMobileRefPageStats(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_ref_url_page= '''INSERT INTO ln_googleBotsMobileRefUrlStats(id, referrer_url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_size_page= '''INSERT INTO ln_googleBotsMobileSizeStats(id, sizeCount, mean, std, min, twfive, fifty, seventyFive, max, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s, %s, %s, %s, %s, %s)'''
google_bot_mobile_by_status_code= '''INSERT INTO ln_googleBotsMobileStatusCodeStats(id, statusCode, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_target= '''INSERT INTO ln_googleBotsMobileTargetStats(id, target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_mobile_by_user_agent = '''INSERT INTO ln_googleBotsMobileUserAgentStats(id, uaString, value, valuesProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
## Google Bot ImageBot
google_bot_image_by_ip= '''INSERT INTO ln_googleBotsImageIPStats(id, ip, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_method= '''INSERT INTO ln_googleBotsImageMethodStats(id, method, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_protocol= '''INSERT INTO ln_googleBotsImageProtocolStats(id, protocol, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_ref_page= '''INSERT INTO ln_googleBotsImageRefPageStats(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_ref_url_page= '''INSERT INTO ln_googleBotsImageRefUrlStats(id, referrer_url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_size_page= '''INSERT INTO ln_googleBotsImageSizeStats(id, sizeCount, mean, std, min, twfive, fifty, seventyFive, max, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s, %s, %s, %s, %s, %s)'''
google_bot_image_by_status_code= '''INSERT INTO ln_googleBotsImageStatusCodeStats(id, statusCode, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_target= '''INSERT INTO ln_googleBotsImageTargetStats(id, target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_image_by_user_agent = '''INSERT INTO ln_googleBotsImageUserAgentStats(id, uaString, value, valuesProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
## Google Bot AdsenseBot
google_bot_adsense_by_ip= '''INSERT INTO ln_googleBotsAdSenseIPStats(id, ip, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_method= '''INSERT INTO ln_googleBotsAdSenseMethodStats(id, method, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_protocol= '''INSERT INTO ln_googleBotsAdSenseProtocolStats(id, protocol, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_ref_page= '''INSERT INTO ln_googleBotsAdSenseRefPageStats(id, url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_ref_url_page= '''INSERT INTO ln_googleBotsAdSenseRefUrlStats(id, referrer_url, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_size_page= '''INSERT INTO ln_googleBotsAdSenseSizeStats(id, sizeCount, mean, std, min, twfive, fifty, seventyFive, max, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s, %s, %s, %s, %s, %s)'''
google_bot_adsense_by_status_code= '''INSERT INTO ln_googleBotsAdSenseStatusCodeStats(id, statusCode, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_target= '''INSERT INTO ln_googleBotsAdSenseTargetStats(id, target, value, valueProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''
google_bot_adsense_by_user_agent = '''INSERT INTO ln_googleBotsAdSenseUserAgentStats(id, uaString, value, valuesProp, month, year, project_id) VALUES (%s,%s,%s,%s,%s,%s,%s)'''


#! GOOGLE BOT REPORT QUERIES ----------------------------------------------------------------
query_googlebot_percentage = """SELECT 
    ln_genBotsMainStats.botStatsProp,
    ln_genBotsMainStats.botStats,
    ln_genBotsMainStats.month,
    ln_genBotsMainStats.year,
    ln_genBotsMainStats.project_id,
    logana_project.domain_id,
    logana_project.project_name
FROM
    ln_genBotsMainStats
        INNER JOIN
    logana_project ON ln_genBotsMainStats.project_id = logana_project.id
WHERE
    ln_genBotsMainStats.botName = 'googlebot.com'
        AND logana_project.id = ln_genBotsMainStats.project_id
ORDER BY ln_genBotsMainStats.year , ln_genBotsMainStats.month"""

query_googlebot_source = """SELECT 
    logana_project.project_name,
    ln_googleBotsMainStatsBySource.project_id,
    logana_project.domain_id,
    ln_googleBotsMainStatsBySource.source,
    ln_googleBotsMainStatsBySource.valueProp,
    ln_googleBotsMainStatsBySource.month,
    ln_googleBotsMainStatsBySource.year
FROM
    logana_project
        INNER JOIN
    ln_googleBotsMainStatsBySource ON logana_project.id = ln_googleBotsMainStatsBySource.project_id
WHERE
    ln_googleBotsMainStatsBySource.year = 2022
ORDER BY ln_googleBotsMainStatsBySource.month ASC"""

query_googlebot_target = """SELECT 
    logana_project.project_name,
    ln_googleBotsMainStatsByTarget.project_id,
    logana_project.domain_id,
    ln_googleBotsMainStatsByTarget.source,
    ln_googleBotsMainStatsByTarget.valueProp,
    ln_googleBotsMainStatsByTarget.month,
    ln_googleBotsMainStatsByTarget.year
FROM
    logana_project
        INNER JOIN
    ln_googleBotsMainStatsByTarget ON logana_project.id = ln_googleBotsMainStatsByTarget.project_id
WHERE
    ln_googleBotsMainStatsByTarget.year = 2022
ORDER BY ln_googleBotsMainStatsByTarget.month ASC"""

query_googlebot_direct_hit = """ SELECT 
    logana_project.project_name,
    ln_googleBotsStatsDirectHit.project_id,
    logana_project.domain_id,
    ln_googleBotsStatsDirectHit.url,
    ln_googleBotsStatsDirectHit.valueProp,
    ln_googleBotsStatsDirectHit.month,
    ln_googleBotsStatsDirectHit.year
FROM
    logana_project
        INNER JOIN
    ln_googleBotsStatsDirectHit ON logana_project.id = ln_googleBotsStatsDirectHit.project_id
WHERE
    ln_googleBotsStatsDirectHit.year = 2022 
ORDER BY ln_googleBotsStatsDirectHit.month ASC"""

query_googlebot_indirect_hit= """SELECT 
    logana_project.project_name,
    ln_googleBotsStatsNDHit.project_id,
    logana_project.domain_id,
    ln_googleBotsStatsNDHit.url,
    ln_googleBotsStatsNDHit.valueProp,
    ln_googleBotsStatsNDHit.month,
    ln_googleBotsStatsNDHit.year
FROM
    logana_project
        INNER JOIN
    ln_googleBotsStatsNDHit ON logana_project.id = ln_googleBotsStatsNDHit.project_id
WHERE
    ln_googleBotsStatsNDHit.year = 2022 
ORDER BY ln_googleBotsStatsNDHit.month ASC """




#AI bots queries
ai_bot_main_stats_query=  '''INSERT INTO logana.ln_genAiBotsMainStats(id,project_id,botName,botStats,botStatsProp,month,year, domain_id) VALUES (%s,%s,%s,%s,%s,%s,%s,%s)'''
ai_bot_referrp_main_stats_query=  '''INSERT INTO logana.ln_AiBotsMainStatsByRefPage(id,url,value,valueProp,month,year, project_id, domain_id) VALUES (%s,%s,%s,%s,%s,%s,%s,%s)'''
ai_bot_referal_main_stats_query=  '''INSERT INTO logana.ln_AiBotsMainStatsByReferalPage(id,url,value,valueProp,month,year, project_id, domain_id) VALUES (%s,%s,%s,%s,%s,%s,%s,%s)'''
ai_bot_referal_main_resp_codes_stats_query=  '''INSERT INTO logana.ln_AiBotsMainRespCodeStats(id,botName,code,value,valueProp,year,month,project_id, domain_id) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)'''
ai_bot_referal_referring_stats=  '''INSERT INTO logana.ln_AiBotsReferalRefferingStats(id,botName,referring_url,referral_url,value,valueProp,year,month,project_id, domain_id) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)'''

#Ai Bot / Project level
ai_botpl_referal_referring_stats = '''INSERT INTO logana.ln_AiBotsStatsByRefferingByBot(
                                       id,
                                       url,
                                       value,
                                       valueProp,
                                       month,
                                       year,
                                       project_id, 
                                       domain_id,
                                       bot_id
                                       ) 
                                      VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)'''

ai_top_landing_pages_query = '''INSERT INTO logana.ln_AiBotsReferalLAndingPageStatByBotId(
                                       id,
                                       url,
                                       value,
                                       valueProp,
                                       month,
                                       year,
                                       project_id, 
                                       domain_id,
                                       bot_id
                                       ) 
                                      VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)'''

ai_daily_visits_query = '''INSERT INTO logana.AiBots_bot_daily_visits(
                                    
                                       bot_id,
                                       visit_date,
                                       visit_count,
                                       project_id, 
                                       domain_id
                                       ) 
                                      VALUES (%s,%s,%s,%s,%s)'''
