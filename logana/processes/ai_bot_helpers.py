import pandas as pd
import numpy as np



def pack_referring_pages(inputAiCsv):
    '''This function receives the csv, convert it to dataframe 
        and calculates the number the most visited pages
            input:inputCsv
            columns:referring_page,

        Function return no value and insert values into the database    
    '''   
    print("Extraction most visted referring pages pages") 
    df = pd.read_csv(inputAiCsv)
    vals = df['referring_page'].value_counts()
    val_index = vals.index.tolist()
    val_values = vals.values.tolist()
    lsd = df.iloc[1].month
    year = df.iloc[1].year
    zipped_data = zip(val_index, val_values)



    return lsd, year, zipped_data

def calculate_props(noRows, botCount):
    '''This function takkes as a parameter a value thata represewnts 
        speciffic bot count and a number of total rows in that particular month in 
        the log file. return value is simple proportion
        input values:
                    noRows:int,
                    botCount: pandas object  
    '''
    botCount = int(botCount)
    proportion = (botCount/noRows)*100

    return proportion


def pack_referrals_pages(inputAiCsv):
    '''This function receives the csv, convert it to dataframe 
        and calculates the number the most visited pages
            input:inputCsv
            columns:referrer,
                   
        Function return no value and insert values into the database    
    '''   
    print("Extraction most visted referring pages pages") 
    df = pd.read_csv(inputAiCsv)
    vals = df['referrer'].value_counts()
    val_index = vals.index.tolist()
    val_values = vals.values.tolist()
    lsd = df.iloc[1].month
    year = df.iloc[1].year
    zipped_data = zip(val_index, val_values)



    return lsd, year, zipped_data


def pack_response_by_bot(inputAiCsv, botList, noRows, project_id, domain_id):
    '''This function receices csv and bot_list as an input and filter the data by response code
       producing the dictionery and prepare the data for DB insert
    '''
    data = []
    df = pd.read_csv(inputAiCsv)
    lsd = df.iloc[1].month
    year = df.iloc[1].year
 
    for bot in botList:

        status_count = df[df['AiBots'] == bot[1]]['status_code'].value_counts()

        indVals = status_count.index.tolist()
        valVals = status_count.values.tolist()

        bot_tuple = [(0, bot[1], int(status), int(count),
                    calculate_props(noRows, count),
                    int(year), int(lsd), int(project_id), int(domain_id)) 
                    for status, count in zip(indVals, valVals)]
        data.extend(bot_tuple) 

  

    return data


def pack_direct_hits(inputAiCsv, botList, noRows, project_id, domain_id):
    '''This function receives csv, aibotList, and project and domain id with number of rows
       and packs the list of tuples prepared to be inserted into the database
    '''
    data = []
    df = pd.read_csv(inputAiCsv)
    lsd = df.iloc[1].month
    year = df.iloc[1].year

    for bot in botList:
        direct_hits_stats = df[(df['referrer'] != 'Direct Hit') & df['AiBots'].eq(bot[1])]
        if not direct_hits_stats.empty: 
            counts = direct_hits_stats[['referrer', 'referring_page']].value_counts()
            referrerIndex = counts.index.get_level_values('referring_page').tolist()
            referalIndex = counts.index.get_level_values('referrer').tolist()
            valCounts = counts.values.tolist()
       
            zippedVals = zip(referrerIndex, referalIndex, valCounts)
            bot_tuple = [(0, bot[1], referrer, referal, int(count),
                             calculate_props(noRows, count),
                             int(year), int(lsd),  project_id, domain_id)
                    for referrer, referal, count in zippedVals]

            data.extend(bot_tuple)
            

    return data


def pack_inDirect_hits(inputAiCsv, botList, noRows, project_id, domain_id):
    '''This function receives csv, aibotList, and project and domain id with number of rows
       and packs the list of tuples prepared to be inserted into the database
    '''
    data = []
    df = pd.read_csv(inputAiCsv)
    lsd = df.iloc[1].month
    year = df.iloc[1].year

    return data


def pack_referring_pages_with_bot(inputCsv,colName):
    """
    Reads a CSV file and processes referral page hit counts per bot.
    Returns a dictionary with bot-wise data and metadata (month, year).
    """
    df = pd.read_csv(inputCsv)

    month = df.iloc[1]['month']
    year = df.iloc[1]['year']
    
    input_dict = {}
    available_bots = df['AiBots'].value_counts().index.tolist()

    for bot in available_bots:
        bot_df = df[df['AiBots'] == bot]
        ref_counts = bot_df[colName].value_counts()
        input_dict[bot] = [{colName: r, 'count': c} for r, c in ref_counts.items()]
    
    return {
        "meta": {
            "month": month,
            "year": year
        },
        "data": input_dict
    }


def pack_referring_pages_with_bot_for_dh_subset(inputCsv,colName):
    '''Reads the CSV file and processes it by doing filtering on referring page
       and taking dataframe containing the urls and stats for Direct Hit pages
    '''
    df = pd.read_csv(inputCsv)
    dhit = df[df['referrer'] == 'Direct Hit']
    
    month = dhit.iloc[1]['month']
    year  = dhit.iloc[1]['year']

    input_dict = {}
    available_bots = dhit['AiBots'].value_counts().index.tolist()

    for bot in available_bots:
        bot_df = dhit[dhit['AiBots'] == bot]
        ref_counts = bot_df[colName].value_counts() #Primice kolonu referring_page
        input_dict[bot] = [{colName: r, 'count': c} for r, c in ref_counts.items()]

    return {
        "meta": {
            "month": month,
            "year": year
        },
        "data": input_dict
    }   
 

def prepare_iq_for_reff_pages_with_bot(insertCsv,botList, noRows, project_id, domain_id, colName):
    '''Receives a dictioney and combine this with the parameters to form insertQuery'''
    insertQueryList = []
    prepared_dict = pack_referring_pages_with_bot(inputCsv=insertCsv, colName=colName)
    meta = prepared_dict['meta']
    data = prepared_dict['data']
    month = meta['month']
    year = meta['year']


    for bot_name, ref_list in data.items():
        matched_bot = next((b for b in botList if b[1] == bot_name), None)
        print("Bot matched", matched_bot)
        if matched_bot is None:
            continue
        for row in ref_list:
            url = row[colName]
            count = row['count']
            valueProp = round(count / noRows, 4)

            tpl = (
                0,
                url, 
                count,
                valueProp,  #Procenat
                int(month),
                int(year),
                project_id,
                domain_id,
                matched_bot[0]  #id bota
            )

            insertQueryList.append(tpl)

    return insertQueryList

def prepare_iq_for_reff_pages_dh_with_bot(insertCsv,botList, noRows, project_id, domain_id, colName):
    '''This function takes the subset of the dataframe filtered by DIrect Hit on referring 
       column and takes values from referring_page column taking 
       the value count and return insertQueryList for Direct Hits
       urls and their counts and proportions
    '''
    insertQueryList = []
    prepared_dict = pack_referring_pages_with_bot_for_dh_subset(inputCsv=insertCsv, colName=colName)
    meta = prepared_dict['meta']
    data = prepared_dict['data']
    month = meta['month']
    year = meta['year']

    for bot_name, ref_list in data.items():
        matched_bot = next((b for b in botList if b[1] == bot_name), None)
        print("Bot matched", matched_bot)
        if matched_bot is None:
            continue
        for row in ref_list:
            url = row[colName]
            count = row['count']
            valueProp = round(count / noRows, 4)
            tpl = (
                0,
                url, 
                count,
                valueProp,  #Procenat
                int(month),  #Eksplicitna konverzija u int
                int(year),   #Eksplicitna konverzija u int
                project_id,
                domain_id,
                matched_bot[0]  #id bota
            )

            insertQueryList.append(tpl)

    return insertQueryList    


def prepare_and_pack_daily_visit_data(insertCsv,botList, noRows, project_id, domain_id, colName):
    '''This function is responsible for 
       packing of the data and returns 
       inputQueryList
    '''
    inputQueryList = []
    df = pd.read_csv(insertCsv)
    time_col = colName if colName else 'time_zone'
    df[time_col] = pd.to_datetime(df[time_col])
    for botName in botList:
        bot_id = botName[0]
        bot_name = botName[1]
        filtered_df = df[df['AiBots'] == bot_name].copy()
        if filtered_df.empty:
            continue
        filtered_df['date'] = filtered_df[time_col].dt.date
        
        daily_counts = (
            filtered_df.groupby('date').size().reset_index(name='count')
        )

        for _, row in daily_counts.iterrows():
            inputQueryList.append((
                bot_id,
                row['date'],
                int(row['count']),
                project_id,
                domain_id
            ))

    return inputQueryList        

def preapre_and_pack_top_landing_pages_stats(insertCsv, botList, noRows, project_id, domain_id, colName=None):
    '''
    This function opens the csv and extract and prepare data to be inserted 
    in the database.
    Function returns InsertQueryList
    '''

    insertQueryList = []
    
    data = pack_referring_pages_with_bot_for_dh_subset(insertCsv,colName="referring_page")
    meta = data['meta']
    data = data['data']
    month = meta['month']
    year = meta['year']

    for bot_name, ref_list in data.items():
        matched_bot = next((b for b in botList if b[1] == bot_name), None)
        print("Bot matched", matched_bot)
        for row in ref_list:
            url = row[colName]
            count = row['count']
            valueProp = round(count / noRows, 4)

            tpl = (
                 0,
                 url, 
                 count,
                 valueProp,  #Procenat
                 int(month),
                 int(year),
                 project_id,
                 domain_id,
                 matched_bot[0]  #id bota
             )

            insertQueryList.append(tpl)

    
    return insertQueryList

