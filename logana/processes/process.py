#!usr/bin/env python3
from doctest import FAIL_FAST
import mysql.connector
from mysql.connector import errorcode
import pandas as pd
import numpy as np
import datetime
import os
from datetime import date
from preprocessing import getDomain 
from queries import *
from scripts.bashRunner import runner
from decouple import config

#These should be declared in the global namespace or pooled from the database
finalCsv = 'logana/processes/results/final.csv'
today = date.today()


def getStats():
    #This is invoked in the inner namespace
    df = pd.read_csv(finalCsv)


    def conn():
        print('Connecting DB...')
        try:
            cnx = mysql.connector.connect(
                user=config('DATABASE_LOGANA_USER'),
                password=config('DATABASE_LOGANA_PASSWORD'),
                host=config('DATABASE_LOGANA_HOST'),
                database='logana')
            print("Connected!")                          
            return cnx                                                      
        except mysql.connector.Error as err:
            print("Connection problem.")

    def getDomainName(domain_id):
        query = '''SELECT * from logana_domain where id='''+str(domain_id)
        cnx = conn()
        cursor = cnx.cursor()
        ftch = cursor.execute(query)
        domain = cursor.fetchone()
        print(domain[2], type(domain))
        return domain[2]    

    def getMainStats():
        '''Fetch user id, project id, month and year along with specific column names and return data in inner space'''
        tmp_list = []
        cnx = conn()
        cursor = cnx.cursor()
        ftch = cursor.execute('''SELECT id, creation_date, logs_start_date, no_rows, domain_id  FROM logana_project where is_active= 0''')
        data = cursor.fetchone()
        dataLst = list(data)
        cnx.commit()
        colNames = [i[0] for i in cursor.description]
        clsData = dict(zip(colNames, dataLst))
        print(type(clsData))
        projectId = clsData['id']
        try:
            lsd = clsData['logs_start_date'].month
        except:
            lsd = None    
        month = clsData['creation_date'].date().month
        year = clsData['creation_date'].date().year
        noRows = clsData['no_rows']
        domain_id = clsData['domain_id']
        tmp_list = [projectId, month, lsd, year, noRows, getDomainName(domain_id)]
        cursor.close()
        cnx.close()
        return tmp_list

    initStats = getMainStats()
    initStats = list(initStats)
    print(initStats)

    def saveToDb(insertQuery, insertQueryList, many=False):
        '''Open db connection and combine insertQuery and insertQueryList'''
        cnx = conn()
        cursor = cnx.cursor()
        if many == False:
            cursor.executemany(insertQuery, insertQueryList)
        else:
            cursor.execute(insertQuery, insertQueryList)
        cnx.commit()
        cursor.close()
        cnx.close()


    def updateDb(insertQuery, insertQueryList, insert = False):
        '''Opendb connection and combine insertQuery and insertQueryList'''
        cnx = conn()
        cursor = cnx.cursor()
        if insert == False:
            cursor.executemany(insertQuery, insertQueryList)
        else:
            cursor.execute(insertQuery, insertQueryList)
        cnx.commit()
        cnx.close()



    def getFirstLastDate():
        '''Extract start and ending date from the input log csv'''
        df['time_zone'] = pd.to_datetime(df['time_zone'])
        dt = df.sort_values(by='time_zone')
        dateDesc = dt['time_zone'].describe()
        fdate = dateDesc.loc['min'].strftime('%Y-%m-%d')
        ldate = dateDesc.loc['max'].strftime('%Y-%m-%d')
        return(fdate, ldate)


    fdate,ldate = getFirstLastDate()
    lsd = datetime.datetime.strptime(fdate, "%Y-%m-%d").month
    year = datetime.datetime.strptime(fdate, "%Y-%m-%d").year
    print(lsd, year)

    def updateMainStats():
        '''Update projects main table'''
        pr_status=1
        vals=df.shape
        noRows,noCols = vals
        insertQueryList=(fdate, ldate, noRows, noCols, pr_status)
        print(insertQueryList)
        updateDb(insertQuery='UPDATE logana_project SET logs_start_date=%s, logs_end_date=%s, no_rows=%s, no_cols=%s, is_active=%s WHERE is_active=0', insertQueryList=(fdate, ldate, noRows, noCols, pr_status), insert=True)
    

    print('Updating main stats')    
    updateMainStats() #Invoking this function to update main stats and to set project status to 1.

    def isNumericBoolFilter(names):
        '''Filter out boolean true or false and returns a masked list where true if number othervise false'''
        isNum = []
        for botName in names:
            if any(map(str.isdigit, botName)):
                isNum.append(True)
            else:
                isNum.append(False)
        return isNum
        
    def filterGoogleBotsBySource(col, tar):
        '''This function filters google dataset dataset and return monthly stats for one project for all bots or speciffied'''
        df = pd.read_csv(finalCsv)
        if tar:
            return df[df[col]==tar]
        else:
            return df
            
                    

    def filterBots1(botName, tDirect, inverse):
        '''This function filters dataset by the bot name and by direct hit'''
        if isinstance(botName, type(None)) and tDirect == False:
            print('No bots no filter!')
            print(df.shape[0])
            return df
        elif isinstance(botName, str) & tDirect == False:
            print('Bot name no direct traffic filter active')
            dfb = df[df['botName'] == botName]
            print('Dataframe size: ', dfb.shape[0])
            return dfb
            #print(dfb['botName'].value_counts())
        elif isinstance(botName, str) & tDirect == True:
            print('Bot name plus direct hit filter On')
            if inverse == True:
                print('Inversion On')
                dfbdhi = df[(df['botName'] == botName) & ~(df['referrer'] == 'Direct Hit')]
                print(dfbdhi.shape[0])
                return dfbdhi
            else:
                print('Inversion Off!')
                dfbdh = df[(df['botName'] == botName) & (df['referrer'] == 'Direct Hit')]
                print(dfbdh.shape[0])
                return dfbdh  

    #! TDIRECT
    def filterBots(botName, tDirect, inverse):
        """This function filters the dataset by the bot name and traffic type"""
        #print(df)
        if isinstance(botName, type(None)):
            if tDirect==False:
                print("No bot name and non direct hits speccified.")
                return df
            else:
                if inverse==False:
                    print("No bot name speciffied and direct hits processed.")
                    dfnbdt = df[df['referrer']=="Direct Hit"]
                    return dfnbdt
                else:
                    print("No bot name speciffied and inverse hits processed") 
                    dfnbdt = df[~(df['referrer']=="Direct Hit")]
                    return dfnbdt


        elif isinstance(botName, str):
            if tDirect==False:
                print("Bot name speciffied and non direct traffic processed.") 
                dfb = df[(df['botName'] == botName) & ~(df['referrer'] == 'Direct Hit')]
                return dfb
            else:
                if inverse==False:
                    print("Bot name speciffied and direct traffic is processed.")
                    dfbdh = df[(df['botName'] == botName) & (df['referrer'] == 'Direct Hit')] 
                    return dfbdh 
                else:
                    print("Bot name speciffied and invert hits processed.")
                    dfbdh = df[(df['botName'] == botName) & (df['referrer'] == 'Direct Hit')]     




    def insertBotsMainStats(botName, tDirect, specInsertQuery, colName, inverse):
        '''Populate main stats table for all bots separatelly for different table format'''
        insertQueryList = []
        df = filterBots(botName, tDirect, inverse)
        valCount = df[colName].value_counts()
        valNames = valCount.index.tolist()
        valStats = valCount.values.tolist()
    
        for names, stats, isNumeric in zip(valNames, valCount, isNumericBoolFilter(names=valNames)):
            insertQueryList.append((0, initStats[0], names, stats, (stats/df.shape[0])*100, isNumeric, lsd, year, None))
        print('Inserting...')
        print(insertQueryList)
        saveToDb(insertQuery=specInsertQuery, insertQueryList=insertQueryList)

    def insertMainStats(botName, tDirect, specInsertQuery, colName, inverse):
        '''Populates main bot stats for similar tables'''
        insertQueryList = []
        dfm = filterBots(botName, tDirect, inverse)

        valCount = dfm[colName].value_counts()[0:5000]
        valNames = valCount.index.tolist()
        valStats = valCount.values.tolist()
        for names, stats in zip(valNames, valCount):
            insertQueryList.append((0, str(names), str(stats), (stats/dfm.shape[0])*100, lsd, year, initStats[0]))
        saveToDb(insertQuery=specInsertQuery, insertQueryList=insertQueryList, many=False)
        print('Inserting')
        print(insertQueryList)


    def insertBotStatsDhitByTarget(botName, tDirect, target, size, insertQuery, inverse):
        '''Predefined document size threshold 1MB -- 1000000b for direct hits'''
        insertQueryList = []
        din = filterBots(botName, tDirect, inverse)
        print(din)
        dtar = din[din['target'] == target]
        if dtar.empty:
            print('Dataframe empty, nothing to insert...')

        else:
            targets = dtar['referring_page'].value_counts().index.tolist()[0:500]
            values = dtar['referring_page'].value_counts().values.tolist()[0:500]
            for target, value in zip(targets, values):
                insertQueryList.append((0, target, value, lsd, year, initStats[0]))
        print(insertQueryList)    
        saveToDb(insertQuery, insertQueryList=insertQueryList) 
        print('Inserted')    


    def insertRespCodeStats(botName, tDirect, inverse, insertQuery):
        '''Insert frequency of response codes in a list'''
        insertQueryList = []
        dts = filterBots(botName, tDirect, inverse)
        status_code_list = dts['status_code'].value_counts().index.tolist()
        sclst = []
        for code in status_code_list:
            scodes = dts[dts['status_code'] == code]['referring_page'].value_counts()[0:20]
            sclst.append((code, scodes))
        scdict = dict(sclst)
        for key in scdict.keys():
            insertQueryList.append((0, key, str(scdict[key].index.tolist()), str(scdict[key].values.tolist()), initStats[5], lsd, year, initStats[0] ))
        #print(insertQueryList)  
        saveToDb(insertQuery=insertQuery, insertQueryList=insertQueryList)

    def insertGoogleBotStatsBySource(botName, col, tar,insertQuery):
        '''Takes dataframe created for source and creates insert query and insert that in corresponding database'''
        insertQueryList = []
        sdt = filterGoogleBotsBySource(col, tar)
        if botName:
            botDf = sdt[sdt['botName'] == botName]
            source_list = botDf[col].value_counts().index.tolist()
            sc_lst = []
            if tar !=None:
            #extract one by one
                for source in source_list:
                    src = botDf[botDf[col] == tar][col].value_counts()
                    sc_lst.append(src)
                return sc_lst  
            else:
                src_inds = botDf[col].value_counts().index.tolist()
                src_vals = botDf[col].value_counts().values.tolist()
                for source, value in zip(src_inds, src_vals):    
                    insertQueryList.append((0, str(source), str(value), (value/botDf.shape[0])*100, lsd, year, initStats[0]))
            print("inserting...") 
            try:  
                saveToDb(insertQuery=insertQuery, insertQueryList=insertQueryList)
            except Exception as e:
                print(e)        
            print(insertQueryList)  



    def paginationCheck(botName, tDirect, term, specInsertQuery, inverse):
        '''This function check how pagination is crawled by specific bot'''
        insertQueryList = []
        dts = filterBots(botName, tDirect, inverse)
        pgdts = dts[dts['referrer'].str.contains(term)]
        #print(pgdts['referrer'], pgdts['referring_page'])
        return pgdts


    def sitemap_extract():
        runner("logana/processes/scripts/sitemapfind.sh")
        df_sitemap = pd.read_csv("logana/processes/results/sitemap.csv")
        insertQueryList = []
        valCount = df_sitemap["referring_page"].value_counts()
        valNames = valCount.index.tolist()
        valStats = valCount.values.tolist()
        lsd = df_sitemap.iloc[1]['month']
        year = df_sitemap.iloc[1]['year']
        # print(f"lsd is {lsd}")
        # print(f"year is {year}")
        for names, stats in zip(valNames, valCount):
            insertQueryList.append((0, str(names), str(stats), (stats/df_sitemap.shape[0])*100, str(lsd), str(year), initStats[0])) # lsd, year
        saveToDb(insertQuery=gen_bot_main_by_sitemap_query, insertQueryList=insertQueryList, many=False)
        print('Inserting')
        os.remove("logana/processes/results/sitemap.csv")
        print(insertQueryList)
        

    def bots_source_insert(col_name: str, source_name: str, bot_name: str, query: str):
        """Populates tables for source and botName, can be used for:
                IPStats
                MethodStats
                ProtocolStats
                RefPageStats
                RefUrlStats
                StatusCodeStats
                TargetStats
                UserAgentStats
                CANT BE USED FOR DesktopSizeStats
            """
        insertQueryList = []
        lsd = df.iloc[1]['month']
        year = df.iloc[1]['year']
        df_cond = df[(df['source']==source_name) & (df['botName']==bot_name)]
        df_col = df_cond[col_name]
        valCount = df_col.value_counts()[0:5000]
        valNames = valCount.index.tolist()
        valStats = valCount.values.tolist()
        for names, stats in zip(valNames, valCount):
            insertQueryList.append((0, str(names), str(stats), (stats/df_col.shape[0])*100, str(lsd), str(year), initStats[0])) # lsd, year
        saveToDb(insertQuery=query, insertQueryList=insertQueryList, many=False)
        print('Inserting')
        print(insertQueryList)


    def mainErpInsert():
        '''This function reads data from the csv and populates ln_geBotsMainRespCodeStats table with error code statistics'''
        insertQueryList = []
        valCount = df['status_code'].value_counts()
        valNames = valCount.index.tolist()
        valStats = valCount.values.tolist() 
        for names, stats in zip(valNames, valCount):
            insertQueryList.append((0, str(names), str(stats), (stats/df.shape[0])*100, lsd, year, initStats[0]))
        #print(insertQueryList)
        saveToDb(insertQuery= gen_bot_main_by_res_code_stats, insertQueryList=insertQueryList)  

  


    def google_bots_source_size_stats(source_name: str, botName: str, query: str):
        """This populates ln_googleBotsDesktopSizeStats cause it has different format!"""
        insertQueryList = []
        lsd = df.iloc[1]['month']
        year = df.iloc[1]['year']
        size_df = df[(df['botName']==botName) & (df['source']==source_name)]['size'].describe()
        size_df.fillna(0, inplace=True)
        insertQueryList.append((0, str(size_df['count']), str(size_df['mean']), str(size_df['std']), str(size_df['min']), str(size_df['25%']), str(size_df['50%']), str(size_df['75%']), str(size_df['max']), str(lsd), str(year), initStats[0]))
        saveToDb(insertQuery=query, insertQueryList=insertQueryList, many=False)
        





    def convertIntToMonth(dayInt):
        '''This function use integer from 1 - 12 and convert that to corresponding month'''
        return datetime.date(1900, dayInt, 1).strftime('%B')  

    

    def input_file_rename(inputFile):
        os.rename(str(inputFile), 'logana/processes/results/'+convertIntToMonth(dayInt=lsd)+'-'+str(year)+'-'+str(initStats[5])+'.csv')

     
    #Sitemap
    sitemap_extract() 
        
    #Main Stats
    insertBotsMainStats(botName=None, tDirect=False, specInsertQuery=gen_bot_main_stats_query, colName='botName', inverse=False)
    insertMainStats(botName=None, tDirect=False, specInsertQuery=gen_bot_main_by_method_query, colName='method', inverse=False)
    insertMainStats(botName=None, tDirect=False, specInsertQuery=gen_bot_main_by_source_query, colName='source', inverse=False)
    insertMainStats(botName=None, tDirect=False, specInsertQuery=gen_bot_main_by_ref_page_query, colName='referring_page', inverse=False)
    insertMainStats(botName=None, tDirect=False, specInsertQuery=gen_bot_main_by_target_query, colName='target', inverse=False)
    insertMainStats(botName=None, tDirect=True, specInsertQuery=gen_bot_main_by_target_query_dhit, colName='target', inverse=False)
    insertMainStats(botName=None, tDirect=True, specInsertQuery=gen_bot_main_by_target_query_ndhit, colName='target', inverse=True)
    insertMainStats(botName=None, tDirect=True, specInsertQuery=gen_bot_main_by_target_query_version, colName='protocol', inverse=True)
    mainErpInsert()

    #Google Area Main Stats

    insertGoogleBotStatsBySource(botName='googlebot.com', col='status_code', tar=None, insertQuery=google_bot_main_resp_code_st_query) #+ 
    insertGoogleBotStatsBySource(botName='googlebot.com', col='source', tar=None, insertQuery=google_bot_main_by_source_query) #+
    insertGoogleBotStatsBySource(botName='googlebot.com', col='target', tar=None, insertQuery=google_bot_main_by_target_query) #+   
    insertGoogleBotStatsBySource(botName='googlebot.com', col='method', tar=None, insertQuery=google_bot_main_method_st_query) #+   
    insertGoogleBotStatsBySource(botName='googlebot.com', col='protocol', tar=None, insertQuery=google_bot_main_protocol_st_query) #+    


    #Populate Google Bots Stat Area - Direct Hits
    insertMainStats(botName='googlebot.com', tDirect=True, specInsertQuery=google_bot_direct_hit_main_query, colName='referring_page', inverse=False) #direct hit url +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = True, target='CSS', size=0, insertQuery=google_bot_direct_hit_by_bigcss_query, inverse=False) # css request in direct hits +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = True,  target='Image', size=0, insertQuery=google_bot_direct_hit_by_bigimage_query, inverse=False) #Image requests in direct hits +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = True, target='JSON', size=0, insertQuery=google_bot_direct_hit_by_bigjson_query, inverse=False)  #populates JSON section on dh +
    insertMainStats(botName='googlebot.com', tDirect=True, specInsertQuery=google_bot_direct_hit_by_target_query, colName='target', inverse=False) #populates direct request by target +
    insertMainStats(botName='googlebot.com', tDirect=True, specInsertQuery=google_bot_direct_hit_by_stcode_query, colName='status_code', inverse=False) #populates resp code stats for DH +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = True,  target='Page', size=10000, insertQuery=google_bot_direct_hit_by_page_query, inverse=False) #not tested
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = True, target='PHP', size=100, insertQuery=google_bot_direct_hit_by_php_query, inverse=False)    #not tested


    

    # #! CHANGE TDIRECT TO FALSE
    #Populate Google Bots Stat Area -Non Direct Hits
    insertMainStats(botName='googlebot.com', tDirect=False, specInsertQuery=google_bot_ndirect_hit_by_referring_page, colName='referrer', inverse=True) #referring page +
    insertMainStats(botName='googlebot.com', tDirect=False, specInsertQuery=google_bot_ndirect_hit_main_query, colName='referring_page', inverse=True) #landing page +
    insertMainStats(botName='googlebot.com', tDirect=False, specInsertQuery=google_bot_ndirect_hit_by_stcode_query, colName='status_code', inverse=False) #populates status code section +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = False, target='CSS', size=0, insertQuery=google_bot_ndirect_hit_by_bigcss_query, inverse=False) #populates CSS indirect requests by size +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = False,  target='Image', size=0, insertQuery=google_bot_ndirect_hit_by_bigimage_query, inverse=False) #populates Image requests + 
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = False, target='JSON', size=0, insertQuery=google_bot_ndirect_hit_by_bigjson_query, inverse=False) #populates JSON ND hits section +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = False,  target='PHP', size=0, insertQuery=google_bot_ndirect_hit_by_page_query, inverse=False) #populates PHP ndh +
    insertBotStatsDhitByTarget(botName='googlebot.com', tDirect = False, target='Page', size=100, insertQuery=google_bot_ndirect_hit_by_php_query, inverse=False) #not used

    #Google Bots divided

    ## Google Bot Desktop
    bots_source_insert(col_name='host_ip', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_ip)
    bots_source_insert(col_name='method', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_method)
    bots_source_insert(col_name='protocol', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_protocol)
    bots_source_insert(col_name='referring_page', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_ref_page)
    bots_source_insert(col_name='referrer', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_ref_url_page)

    #google_bots_source_size_stats(source_name='Desktop', botName='googlebot.com', query=google_bot_desktop_by_size_page)
    bots_source_insert(col_name='status_code', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_status_code)
    bots_source_insert(col_name='target', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_target)
    bots_source_insert(col_name='user_agent', source_name='Desktop', bot_name='googlebot.com', query=google_bot_desktop_by_user_agent)
    # Google Bot Mobile
    bots_source_insert(col_name='host_ip', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_ip)
    bots_source_insert(col_name='method', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_method)
    bots_source_insert(col_name='protocol', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_protocol)
    bots_source_insert(col_name='referring_page', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_ref_page)
    bots_source_insert(col_name='referrer', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_ref_url_page)

    #google_bots_source_size_stats(source_name='Mobile', botName='googlebot.com', query=google_bot_mobile_by_size_page)
    bots_source_insert(col_name='status_code', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_status_code)
    bots_source_insert(col_name='target', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_target)
    bots_source_insert(col_name='user_agent', source_name='Mobile', bot_name='googlebot.com', query=google_bot_mobile_by_user_agent)
    ## Google Bot image
    bots_source_insert(col_name='host_ip', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_ip)
    bots_source_insert(col_name='method', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_method)
    bots_source_insert(col_name='protocol', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_protocol)
    bots_source_insert(col_name='referring_page', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_ref_page)
    bots_source_insert(col_name='referrer', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_ref_url_page)
 
    #google_bots_source_size_stats(source_name='ImageBot', botName='googlebot.com', query=google_bot_image_by_size_page)
    bots_source_insert(col_name='status_code', source_name='Mobile', bot_name='googlebot.com', query=google_bot_image_by_status_code)
    bots_source_insert(col_name='target', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_target)
    bots_source_insert(col_name='user_agent', source_name='ImageBot', bot_name='googlebot.com', query=google_bot_image_by_user_agent)
    ## Google Bot AdSense
    bots_source_insert(col_name='host_ip', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_ip)
    bots_source_insert(col_name='method', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_method)
    bots_source_insert(col_name='protocol', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_protocol)
    bots_source_insert(col_name='referring_page', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_ref_page)
    bots_source_insert(col_name='referrer', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_ref_url_page)

    #google_bots_source_size_stats(source_name='AdsenseBot', botName='googlebot.com', query=google_bot_adsense_by_size_page)
    bots_source_insert(col_name='status_code', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_status_code)
    bots_source_insert(col_name='target', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_target)
    bots_source_insert(col_name='user_agent', source_name='AdsenseBot', bot_name='googlebot.com', query=google_bot_adsense_by_user_agent)


    #Pagination check
    #paginationCheck(botName='googlebot.com', tDirect=False, term = 'strona', specInsertQuery=google_bot_tDirect_hit_by_php_query, inverse=False)

    #Rename input file to match current month
    input_file_rename(inputFile=finalCsv)

    #Insert response codes globaly
    insertRespCodeStats(botName=None, tDirect=False, inverse=False, insertQuery=gen_bot_main_rp_stats_referring)

    
def process():
    getStats()




if __name__=="__main__":
    process()            



