#!usr/bin/env python3
from html.entities import html5
from logging.config import valid_ident
import dash
from dash import dash_table
import plotly.graph_objects as go
from dash import Dash, dcc, html, Input, Output, State
from django_plotly_dash import DjangoDash
import pandas as pd
import plotly.express as px
import dash_bootstrap_components as dbc


def erpData(lst):
    '''This function returns dash app for status code section'''
    df = pd.DataFrame(lst, columns=["url", "value", "code"])
    FONT_AWESOME = (
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [FONT_AWESOME]
    erpapp = DjangoDash("respCodeData", external_stylesheets=external_stylesheets, add_bootstrap_links=True,
                        serve_locally=True)
    erpapp.layout = html.Div([
        html.H1('Pick a status code from Dropdown', style={'textAlign': 'center'}),
        html.Div([
            dcc.Dropdown(id='data-set-chosen', multi=False, value=200,
                         options=df['code'].unique())
        ], className='row',
            style={'width': '50%', 'margin-left': 150, 'align-items': 'center', 'justify-content': 'center'}),
        html.Div([
            html.Div(id='graph1', children=[], className='six columns'),
        ], className='row'),
        # dcc.Store inside the user's current browser session
        dcc.Store(id='store-data', data=[], storage_type='session')  # 'local' or 'session'
    ])

    # saving input
    @erpapp.callback(
        Output('store-data', 'data'),
        Input('data-set-chosen', 'value')
    )
    def store_data(value):
        data = value
        return data

    @erpapp.callback(
        Output('graph1', 'children'),
        Input('store-data', 'data')
    )
    def create_graph1(data):
        df1 = df[df['code'] == data]
        fig1 = px.bar(
            data_frame=df1,
            x='value',
            y='url'
        )
        return dcc.Graph(figure=fig1)

    return erpapp


def dashTableFig(lst, colNames):
    df = pd.DataFrame(lst, columns=colNames)
    app = DjangoDash("SusLookup", add_bootstrap_links=True)
    app.css.append_css({"external_url": "/static/assets/style.css"})

    app.layout = dash_table.DataTable(
        data=df.to_dict('records'),
        columns=[{'id': c, 'name': c} for c in df.columns],
    )

    return app


def dash_bar(lst, colNames, xin, yin, color, color_discrete_sequence, label, default_height, default_width):
    """Takes the list as data input and returns bar plot """
    df = pd.DataFrame(lst, columns=colNames)

    barPlot = px.bar(
        df,
        x=xin,
        y=yin,
        opacity=0.99,
        template="simple_white",
        color=color,
        labels=label,
        text_auto='.2s',
        color_discrete_sequence=color_discrete_sequence
    ).to_html(full_html=False, default_height=default_height, default_width=default_width)

    return barPlot


def dash_pie(lst, colNames, xin, yin, title, default_height, default_width):
    """Takes the list as data input and returns bar plot"""
    df = pd.DataFrame(lst, columns=colNames)

    piePlot = px.pie(
        df,
        values=xin,
        names=yin,
        title=title,
    ).to_html(full_html=False, default_height=default_height, default_width=default_width)

    return piePlot


def dash_app_table(lst, colNames, app_name, csv_name, pr_name, df_col: list, a: int, b: int):
    df = pd.DataFrame(lst, columns=colNames)
    if 'botName' and 'botStats' in df.columns:
        df.rename(columns={'botName': 'Events', 'botStats': 'Hits'}, inplace=True)
    elif 'url' and 'value' in df.columns:
        df.rename(columns={'url': 'Events', 'value': 'Hits'}, inplace=True)
    FONT_AWESOME = (
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    )
    external_stylesheets = [dbc.themes.BOOTSTRAP, FONT_AWESOME]
    table_app = DjangoDash(app_name, external_stylesheets=external_stylesheets)
    my_table = dash_table.DataTable(
        id='table',
        columns=[{"name": i, "id": i} for i in df.columns[a:b]],
        data=df.to_dict('records'),
        filter_action='native',
        style_cell={'fontSize': 16, 'font-family': 'sans-serif'},
        style_cell_conditional=[  # style_cell_c. refers to the whole table
            {
                'if': {'column_id': c},
                'textAlign': 'left'
            } for c in ['botName', 'values']
        ],
        style_header={
            'fontWeight': 'bold',
            # 'border': '5px solid black'
        },
        sort_action='native',
        page_action='native',
        page_size=15,
        style_as_list_view=True,
        style_data_conditional=[
            {
                'if': {'row_index': 'odd'},
                'backgroundColor': 'rgb(230, 255, 255)',
            }
        ],
        style_data={
            'whiteSpace': 'normal',
            'height': 'auto'
        }
    )
    table_app.layout = html.Div([
        dbc.Button(id='btn',
                   children=[html.I(className="fa fa-download mr-1")],
                   color="info",
                   className="mt-2",
                   ), my_table,
        dcc.Download(id="download-component"),
        # dcc.Store inside the user's current browser session
        dcc.Store(id='store-data', data=[], storage_type='session')
    ])

    @table_app.callback(
        Output('table', 'data'),
        Input('table-data', 'data')
    )
    def create_table(data):
        return my_table

    @table_app.callback(
        Output("download-component", "data"),
        Input("btn", "n_clicks"),
        prevent_initial_call=True,
    )
    def complete_csv(n_clicks):
        csvname = pr_name + "_" + csv_name
        return dcc.send_data_frame(df[df_col].to_csv, csvname)


def table_below_graph(lst):
    df = pd.DataFrame(lst, columns=["url", "value", "code"])
    FONT_AWESOME = (
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    # dbc.themes.BOOTSTRAP
    external_stylesheets = [FONT_AWESOME]
    tableapp = DjangoDash("TableBelowGraph", external_stylesheets=external_stylesheets, serve_locally=True)
    tableapp.layout = html.Div([
        dbc.Button(id='btn',
                   children=[html.I(className="fa fa-download mr-1"), "Download"],
                   color="info",
                   className="mt-1",
                   style={'width': '50%', 'margin-left': 170, 'align-items': 'center', 'justify-content': 'center'}
                   ),
        dcc.Download(id="download-component"),
        html.Div([
            html.Div(id='table-placeholder', children=[])
        ], className='row'),
        # dbc.Button(id='btn',
        #            children=[html.I(className="fa fa-download mr-1"), "Download"],
        #            color="info",
        #            className="mt-1",
        #            style={'width': '50%', 'margin-left': 170, 'align-items': 'center', 'justify-content': 'center'}
        #            ),
        # dcc.Download(id="download-component"),
        dcc.Store(id='store-data', data=[], storage_type='session')
    ])

    @tableapp.callback(
        Output('table-placeholder', 'children'),
        Input('store-data', 'data')
    )
    def create_graph1(data):
        dff = df[df['code'] == data]
        my_table = dash_table.DataTable(
            columns=[{"name": i, "id": i} for i in dff.columns],
            data=dff.to_dict('records'),
            filter_action='native',
            style_cell_conditional=[  # style_cell_c. refers to the whole table
                {
                    'if': {'column_id': c},
                    'textAlign': 'left'
                } for c in ['url']
            ],
            style_header={
                'fontWeight': 'bold',
                # 'border': '1px solid black'
            }, style_cell={
                'height': 'auto',
                # all three widths are needed
                'minWidth': '180px', 'width': '180px', 'maxWidth': '180px',
                'whiteSpace': 'normal'
            },
            style_as_list_view=True,
            page_action='native',
            page_size=20,
        )
        return my_table

    @tableapp.callback(
        Output("download-component", "data"),
        Input("btn", "n_clicks"),
        prevent_initial_call=True,
    )
    def complete_csv(n_clicks):
        df_csv = df
        return dcc.send_data_frame(df_csv.to_csv, "status_codes.csv")


def download_button(lst, colNames, download_app_name: str, csv_name: str, pr_name: str):
    df = pd.DataFrame(lst, columns=colNames)
    print(df)
    FONT_AWESOME = (
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    downloadapp = DjangoDash(download_app_name, external_stylesheets=external_stylesheets)
    downloadapp.layout = html.Div([
        dbc.Button(id='btn',
                   children=[html.I(className="fa fa-download mr-1"), "Download CSV"],
                   color="info",
                   # className="mt-1",
                   style={'width': '50%', 'margin-left': 170, 'align-items': 'center', 'justify-content': 'center'}
                   ),
        dcc.Download(id="download-component"),
    ])

    @downloadapp.callback(
        Output("download-component", "data"),
        Input("btn", "n_clicks"),
        prevent_initial_call=True,
    )
    def complete_csv(n_clicks):
        csvname = pr_name + "_" + csv_name
        print(csvname)
        return dcc.send_data_frame(df.to_csv, csvname)


def describe_line_df(data, col):
    df_desc = pd.DataFrame(data=data, columns=col).transpose()
    fig = px.line(df_desc, title="Size Statistics", log_y=True, width=500, height=500, markers=True).to_html()
    return fig


def display_dash_bar(lst, colNames, app_name, xin, yin, width, height, name, color, opacity, title, facet_row=None):
    '''This is going to be used to display bar grapfh instead the ones created by plotly express'''
    df = pd.DataFrame(lst, columns=colNames)
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    fig = go.Figure(
        data=[go.Bar(x=df['year'],
                     y=df['HealthScore'],
                     opacity=opacity,
                     text=df['HealthScore'],
                     name=name,
                     marker_color=color,
                     )]

    )

    fig.update_xaxes(
        dtick='M1',

    )

    fig.update_yaxes(
        automargin='left+top'
    )
    fig.update_traces(
        texttemplate='%{text:.2s}',
        textposition='outside',
    )

    fig.update_layout(
        autosize=True,
        width=width,
        height=height,
        plot_bgcolor='white',
        title=title

    )

    app.layout = html.Div(children=[
        dcc.Graph(
            figure=fig,

        )

    ])
    return app


def subplot_bar(lst, colNames, app_name, width, height, opacity, text, facet, color):
    '''Creates bar with subplots'''

    df = pd.DataFrame(lst, columns=colNames)
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    fig = px.bar(df, x='year',
                 y=text,
                 opacity=opacity,
                 text=df[text],
                 facet_row=facet,
                 width=width,
                 height=height,
                 log_y=False,

                 )

    fig.update_xaxes(
        dtick='M1',

    )

    fig.update_yaxes(
        automargin='left+top'
    )
    fig.update_traces(
        textposition = 'inside',
        marker_color=color
    )

    fig.update_layout(
        autosize=True,
        bargap=0.05,
        # plot_bgcolor='white',

    )

    app.layout = html.Div(children=[
        dcc.Graph(
            figure=fig,

        )

    ])


def subplot_bar_fix(lst, colNames, app_name, width, height, opacity, text, facet, color):
    '''Creates bars with subplots taking into acc if the list is empty'''

    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    if bool(lst):
        print("List is not empty")
        print(lst)
        subplot_bar(lst, colNames, app_name, width, height, opacity, text, facet, color)
    else:
        print('input list is empty!')
        app.layout = html.Div(children=[
            dcc.Graph(
                figure={},
        )
    ])


def subplot_hist(lst, colNames, app_name, xin, yin, frow, color, width, height):
    '''This function will display subplots of histogram'''

    df = pd.DataFrame(lst, columns=colNames)
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    fig = px.line(
        data_frame=df,
        x=xin,
        y=yin,
        facet_row=frow,
        color=color,
        width=width,
        height=height,
        markers=True,
        render_mode='swg',

    )

    fig.update_layout(
        autosize=False,

        margin=dict(l=20, r=20, t=20, b=20),
        # plot_bgcolor='white',

    )

    app.layout = html.Div(children=[
        dcc.Graph(
            figure=fig,

        )

    ])

def plot_bar_from_df(df, colNames, y_value, app_name, xin, yin, width, height, name, color, opacity, title,
                     facet_row=None):
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    fig = go.Figure(
        data=[go.Bar(x=df['month'],
                     y=df[y_value],
                     opacity=opacity,
                     text=df['project_name'],
                     name=name,
                     marker_color=color,
                     )]

    )

    fig.update_xaxes(
        dtick='M1',

    )

    fig.update_yaxes(
        automargin='left+top'
    )
    fig.update_traces(
        texttemplate="%{text}<br>(%{a:.2f}, %{b:.2f}, %{c:.2f})",
        textposition='outside',
    )

    fig.update_layout(
        autosize=True,
        width=width,
        height=height,
        plot_bgcolor='white',
        title=title

    )

    app.layout = html.Div(children=[
        dcc.Graph(
            figure=fig,

        )

    ])
    return app


def stacked_bar_chart(lst, colNames, app_name, width, height, opacity, text, facet, color):

    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    # Create DataFrame from list
    df = pd.DataFrame(lst, columns=colNames)
    print(df)
    code_family = df.columns.tolist()[1]
    print(code_family)
    print(type(code_family))

    # Group by response code and year-month
    grouped = df.groupby(['Code', 'year'])[code_family].sum().reset_index()

    print(grouped)

    if grouped.empty:
        loading_spinner = dbc.Spinner(color="primary", size="lg", type="grow")
        empty_state_message = html.P("No data available.", style={'font-size': '18px'})

        app.layout = html.Div([
            html.Div([
                loading_spinner,
            ], style={'display': 'flex', 'align-items': 'center', 'justify-content': 'center', 'height': '50vh'}),

            html.Div([
                empty_state_message,
            ], style={'display': 'flex', 'align-items': 'center', 'justify-content': 'center', 'height': '50vh'})
        ], style={'display': 'flex', 'flex-direction': 'column'})
    else:

        # Create stacked bar chart
        fig = px.bar(grouped, x='year', y=code_family, color='Code',
                     hover_data={'year': '|%B %Y', code_family: True, 'Code': False},
                     facet_col=facet)

        # Set chart layout
        fig.update_layout(barmode='stack', width=width, height=height, hovermode='x unified', hoverlabel=dict(bgcolor=color, font_size=16),
                          plot_bgcolor='#F2F2F2', legend=dict(title='Code', orientation='h', y=1.1, x=0.5),
                          yaxis=dict(title=code_family, showgrid=False),
                          xaxis=dict(title='year', showgrid=False, tickangle=45, automargin=True),
                          uniformtext=dict(mode='hide', minsize=12), bargap=0.2, bargroupgap=0.1)

        # Set opacity of bars
        for trace in fig.data:
            trace.opacity = opacity

        # Set text on top of bars
        if text:
            fig.update_traces(texttemplate='%{y}', textposition='inside')

        app.layout = (html.Div([dcc.Graph(figure=fig)]))
    return app


def stacked_bar_chart2(lst, colNames, app_name, width, height, opacity, text, facet, color):
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.FLATLY, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    # Create DataFrame from list
    df = pd.DataFrame(lst, columns=colNames)
    print(df)
    code_family = df.columns.tolist()[1]

    # Group by response code and year-month
    grouped = df.groupby(['Code', 'year', 'month'])[code_family].sum().reset_index()

    # Pivot the table to include all possible combinations of response codes and months
    grouped = grouped.pivot_table(index=['year', 'month'], columns='Code', values=code_family,
                                  fill_value=0).reset_index()

    # Melt the table to create the long format needed for plotting with Plotly
    melted = grouped.melt(id_vars=['year', 'month'], var_name='Code', value_name=code_family)

    # Create stacked bar chart
    fig = px.bar(melted, x='month', y=code_family, color='Code',
                 hover_data={'month': '|%B %Y', code_family: True, 'Code': False},
                 facet_col=facet)

    # Set chart layout
    fig.update_layout(barmode='stack', width=width, height=height, hovermode='x unified',
                      hoverlabel=dict(bgcolor=color, font_size=16),
                      plot_bgcolor='#F2F2F2', legend=dict(title='Code', orientation='h', y=1.1, x=0.5),
                      yaxis=dict(title=code_family, showgrid=False),
                      xaxis=dict(title='Month', showgrid=False, tickangle=45, automargin=True),
                      uniformtext=dict(mode='hide', minsize=12), bargap=0.2, bargroupgap=0.1)

    # Set opacity of bars
    for trace in fig.data:
        trace.opacity = opacity

    # Set text on top of bars
    if text:
        fig.update_traces(texttemplate='%{y}', textposition='inside')

    app.layout = (html.Div([dcc.Graph(figure=fig)]))
    return app


def create_bar_chart(df, app_name, color, width=900, height=500):
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.PULSE, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    default_months = 12
    last_months = df.tail(default_months)
    trace = go.Bar(x=last_months["month-year"], y=last_months["no_rows"], marker=dict(color=color))
    data = [trace]

    app.layout = html.Div(children=[
        html.H1(children="Bar Chart"),
        dcc.Graph(
            id="bar-chart",
            figure={
                "data": data,
                "layout": go.Layout(title="Bar Chart", width=width, height=height)
            }
        ),
        dcc.RangeSlider(
            id="date-slider",
            min=0,
            max=len(df)-1,
            value=[len(df)-default_months, len(df)-1],
            marks={i: df.loc[i, "month-year"] for i in range(len(df)) if i%default_months==0},
            step=1
        ),
        html.Div(id="slider-output")
    ])

    @app.callback(
        dash.dependencies.Output("bar-chart", "figure"),
        [dash.dependencies.Input("date-slider", "value")]
    )
    def update_figure(selected_dates):
        start_date = selected_dates[0]
        end_date = selected_dates[1]
        filtered_df = df.iloc[start_date:end_date+1]
        trace = go.Bar(x=filtered_df["month-year"], y=filtered_df["no_rows"], marker=dict(color=color))
        return {"data": [trace], "layout": go.Layout(title="Bar Chart", width=width, height=height)}

    @app.callback(
        dash.dependencies.Output("slider-output", "children"),
        [dash.dependencies.Input("date-slider", "value")]
    )
    def update_slider_output(selected_dates):
        start_date = df.loc[selected_dates[0], "month-year"]
        end_date = df.loc[selected_dates[1], "month-year"]
        return f"You have selected from {start_date} to {end_date}"

    return app


def stacked_bar_chart_df(df, app_name, width=900, height=500):
    FONT_AWESOME = ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")
    external_stylesheets = [dbc.themes.PULSE, FONT_AWESOME]
    app = DjangoDash(app_name, external_stylesheets=external_stylesheets, add_bootstrap_links=True, serve_locally=True)

    # Define the unique status codes and generate colors for each code
    unique_codes = df['statuscode'].unique()
    num_codes = len(unique_codes)
    colors = ['hsl(' + str(h) + ',50%' + ',50%)' for h in range(0, 360, int(360/num_codes))]
    color_dict = dict(zip(unique_codes, colors))

    # Create a list of traces for each status code
    traces = []
    for code in unique_codes:
        filtered_df = df[df['statuscode'] == code]
        trace = go.Bar(x=filtered_df["month-year"], y=filtered_df["numberofoccurence"], name=str(code),
                       marker=dict(color=color_dict[code]))
        traces.append(trace)

    app.layout = html.Div(children=[
        #html.H1(children="Stacked Bar Chart"),
        dcc.Graph(
            id="stacked-bar-chart",
            figure={
                "data": traces,
                "layout": go.Layout(title="Stacked Bar Chart", barmode='stack', width=width, height=height)
            }
        ),
        dcc.RangeSlider(
            id="date-slider",
            min=0,
            max=len(df)-1,
            value=[len(df)-12, len(df)-1],
            marks={i: df.loc[i, "month-year"] for i in range(len(df)) if i%12==0},
            step=1
        ),
        html.Div(id="slider-output")
    ])

    @app.callback(
        Output("stacked-bar-chart", "figure"),
        [Input("date-slider", "value")]
    )
    def update_figure(selected_dates):
        start_date = selected_dates[0]
        end_date = selected_dates[1]
        filtered_df = df.iloc[start_date:end_date+1]
        traces = []
        for code in unique_codes:
            filtered_df_code = filtered_df[filtered_df['statuscode'] == code]
            trace = go.Bar(x=filtered_df_code["month-year"], y=filtered_df_code["numberofoccurence"],
                           name=str(code), marker=dict(color=color_dict[code]))
            traces.append(trace)
        return {"data": traces, "layout": go.Layout(title="Stacked Bar Chart", barmode='stack', width=width, height=height)}

    @app.callback(
        Output("slider-output", "children"),
        [Input("date-slider", "value")]
    )
    def update_slider_output(selected_dates):
        start_date = df.loc[selected_dates[0], "month-year"]
        end_date = df.loc[selected_dates[1], "month-year"]
        return f"You have selected from {start_date} to {end_date}"

    return app


