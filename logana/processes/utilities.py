#!usr/bin/env python3

import mysql.connector
import pandas as pd
from mysql.connector import errorcode
import plotly.graph_objects as go
import ast
import math
from decouple import config
from decimal import Decimal
from operator import itemgetter


def conn():
    print('Connecting DB...')
    try:
        cnx = mysql.connector.connect(
            user=config('DATABASE_LOGANA_USER'),
            password=config('DATABASE_LOGANA_PASSWORD'),
            host=config('DATABASE_LOGANA_HOST'),
            database=config('DATABASE_LOGANA_NAME'))
        print("Connected!")
        return cnx
    except mysql.connector.Error as err:
        print("Connection problem.")


def lngenBotsMST(stmt, prmtr=None):
    '''Fetching the data from the table with table header and returns data + column names'''
    cnx = conn()
    cursor = cnx.cursor()
    ex = cursor.execute(stmt, prmtr)
    data = cursor.fetchall()
    colNames = [i[0] for i in cursor.description]
    cnx.commit()
    return data, colNames


def tablePop(df):
    '''Populates table from df'''
    fig = go.Figure(data=[go.Table(
        header=dict(values=list(['Status Code', 'Occurance', 'Percentage']),
                    fill_color='#666564',
                    font_size=16,
                    font_color='white',
                    height=30,
                    align='left'),
        cells=dict(values=[df.code, df.value, df.valueProp],
                   fill_color='#f7f8fa',
                   font_size=16,
                   height=30,

                   align='right'))
    ])
    fig.update_layout(height=500, width=500)

    return fig.to_html


def convertStrToList(lst):
    '''Converts string representation of a list to actual list'''
    try:
        converted = ast.literal_eval(lst)
        return converted
    except:
        print("Conversion error")


def queryFormatter(db, additional, id, isNumeric=None):
    '''This formats a query string taking into acc if any additional parametar is required'''
    if additional == True and isNumeric == 1:
        qs = '''SELECT * FROM ''' + str(db) + ''' WHERE project_id=''' + str(id) + ''' AND isNumeric=''' + str(
            isNumeric)
        print(qs)
    elif additional == True and isNumeric == 0:
        qs = '''SELECT * FROM ''' + str(db) + ''' WHERE project_id=''' + str(id) + ''' AND isNumeric=''' + str(
            isNumeric)
        print(qs)
    else:
        qs = '''SELECT * FROM ''' + str(db) + ''' WHERE project_id=''' + str(id)

    return qs


def extractErrorCode(row, index=1):
    '''This is utility to extract error codes from tuple'''
    error_codes = []
    for x in range(len(row)):
        error_codes.append(row[x][index])
    return error_codes


def extractVals(row):
    '''This utility extracts values from tuple and return lists'''
    errorCodes = []
    values = []
    urls = []
    for i in range(len(row)):
        errorCodes.append(row[i][0])
        values.append(row[i][1])
        urls.append(row[i][2])

    urls = [convertStrToList(url) for url in urls]
    values = [convertStrToList(val) for val in values]
    return errorCodes, values, urls


def prepareTuple(tpl):
    '''This function prepare tuple to be used in dataframe'''
    tpl_list = []
    for i in range(0, len(tpl)):
        keys = (tpl[i][0],)
        sec_tpl = tpl[i][1]
        for j in sec_tpl:
            new_tpl = (j + keys)
            tpl_list.append(new_tpl)
    return tpl_list


def customQuerySetter(db, id, colNames):
    '''This function set qustom query'''
    qs = '''SELECT ''' + str(colNames) + ''' FROM ''' + str(db) + ''' WHERE project_id=''' + str(id)
    print(qs)
    return qs


def percentage_calc(val, preval):
    '''Calculate percentage difference'''
    if val == preval:
        return 0
    vald = ((val - preval) / ((val + preval) / 2)) * 100
    return math.floor(vald)


def health_score(id, field, table, code, previous_id):
    '''Populate health score or previous and current month with changes'''
    cur_query = '''SELECT ''' + field + ''' FROM ''' + table + ''' WHERE code=''' + str(
        code) + ''' AND project_id=''' + str(id)
    prev_query = '''SELECT ''' + field + ''' FROM ''' + table + ''' WHERE code=''' + str(
        code) + ''' AND project_id=''' + str(previous_id)
    cur_erps, colNames = lngenBotsMST(cur_query)
    try:
        prev_erps, colNames = lngenBotsMST(prev_query)
        prev_erp = prev_erps[0][0]
    except Exception as e:
        prev_erp = 0
    try:
        curr_erp = cur_erps[0][0]
    except Exception as e:
        curr_erp = 0
    # print(code)
    # print(f"curr erp is {curr_erp}")
    difference_erp = curr_erp - prev_erp
    perc_erp = percentage_calc(curr_erp, prev_erp)
    erps = {'current': curr_erp, 'previous': prev_erp, 'erpdiff': difference_erp, 'perc': perc_erp}
    return erps


def source_comparison(table, col, month, did, year):
    '''Populates source comparison table for current and previous month'''
    
    prev_query = '''SELECT ''' + str(col) + ''', value FROM logana.'''+str(table)+''' as lgs
            LEFT JOIN logana_project
            ON logana_project.id = lgs.project_id
            WHERE domain_id='''+str(did) + ''' 
            AND month='''+str(month) + '''
            AND year=''' + str(year) + '''
            ORDER BY value ASC '''
    print(prev_query)
    prev_src, prevsrcNames = lngenBotsMST(prev_query)
    return prev_src, prevsrcNames


def top_refering_page(id_curr, id_prev):
    """This function returns the top refering page for the id"""
    curr_query = '''SELECT url,valueProp FROM ln_genBotsMainStatsByRefPage WHERE project_id=''' + str(
        id_curr) + ''' LIMIT 1'''
    prev_query = '''SELECT url,valueProp FROM ln_genBotsMainStatsByRefPage WHERE project_id=''' + str(
        id_prev) + ''' LIMIT 1'''
    print(curr_query)
    print(prev_query)
    cnx = conn()
    cursor = cnx.cursor()
    cursor.execute(curr_query)
    top_curr_ref_page = cursor.fetchone()
    print("top current referring page: ", top_curr_ref_page)
    try:
        cursor.execute(prev_query)
        top_prev_ref_page = cursor.fetchone()
        print(top_prev_ref_page)
        prev_perc = top_prev_ref_page[1]
    except Exception as e:
        prev_perc = 0
    curr_perc = top_curr_ref_page[1]
    ref_diff = curr_perc - prev_perc
    perc = percentage_calc(curr_perc, prev_perc)
    top_ref_page_dict = {"curr_perc": curr_perc, "prev_perc": prev_perc, "ref_diff": ref_diff, "perc": perc}
    return top_ref_page_dict


def hits_on_sitemap(id_curr, id_prev):
    """This function returns the data for sitemap hits on summary page!"""
    curr_query = '''SELECT valueProp FROM ln_genBotsMainStatsBySitemap WHERE project_id=''' + str(
        id_curr) + ''' LIMIT 1'''
    prev_query = '''SELECT valueProp FROM ln_genBotsMainStatsBySitemap WHERE project_id=''' + str(
        id_prev) + ''' LIMIT 1'''
    cnx = conn()
    cursor = cnx.cursor()
    cursor.execute(curr_query)
    top_curr_sitemap = cursor.fetchone()
    print(top_curr_sitemap)
    try:
        cursor.execute(prev_query)
        top_prev_sitemap = cursor.fetchone()
        prev_perc = top_prev_sitemap[0]
    except Exception as e:
        prev_perc = 0
    curr_perc = top_curr_sitemap[0]
    sitemap_diff = curr_perc - prev_perc
    perc = percentage_calc(curr_perc, prev_perc)
    top_sitemap_dict = {"curr_perc": curr_perc, "prev_perc": prev_perc, "sitemap_diff": sitemap_diff, "perc": perc}
    return top_sitemap_dict


def health_score_rep(domain_id, code, val):
    '''Fetch health score by month and year to populate MOM report for a given domain'''
    query = '''SELECT code, ''' + str(
        val) + ''', year, month, domain_id FROM logana.ln_geBotsMainRespCodeStats, logana_project WHERE ln_geBotsMainRespCodeStats.project_id=logana_project.id AND code=''' + str(
        code) + ''' AND domain_id=''' + str(domain_id)
    cnx = conn()
    cursor = cnx.cursor()
    try:
        cursor.execute(query)
        query_res = cursor.fetchall()
    except Exception as e1:
        print(e1)

    return query_res

def health_score_bots_rep(domain_id, code, val, table):
    '''Fetches health score for google bot  by month and year to populate Google Bot MoM report for a given domain'''
    query = '''SELECT code, ''' + str(
        val) + ''', year, month, domain_id FROM logana.'''+table+ ''', logana_project WHERE ''' + table+ '''.project_id=logana_project.id AND code=''' + str(
        code) + ''' AND domain_id=''' + str(domain_id)
    cnx = conn()
    cursor = cnx.cursor()
    print(query)
    try:
        cursor.execute(query)
        query_res = cursor.fetchall()
    except Exception as e1:
        print(e1)

    return query_res

def scaffold_date(hsquery_res):
    '''Filter out health score query to contain only last 12 months'''
    sorted_data_tml = sorted(hsquery_res, key=itemgetter(2), reverse=False)
    sorted_data_tml.reverse()  # inplace
    newl = [list(x) for x in sorted_data_tml]
    for y in newl:
        y[2] = y[2] + '-' + y[3]
    sorted_data = [tuple(x) for x in newl]
    return sorted_data[0:12]


def filter_occs(domain_id, code, val, tableName):
    '''This function is filtering all occurence for speciffic error variation (sxxx)'''
    query = '''SELECT code, ''' + str(
        val) + ''', year, month, domain_id FROM logana.'''+str(tableName)+''', logana_project WHERE '''+str(tableName)+'''.project_id=logana_project.id AND code LIKE ''' + str(
        code) + ''' AND domain_id=''' + str(domain_id)
    print(query)
    cnx = conn()
    cursor = cnx.cursor()
    try:
        cursor.execute(query)
        query_res = cursor.fetchall()
        print(len(query_res))

        return query_res
    except Exception as e2:
        print(e2)
        return None
    


def filter_occs_direct_indirect(domain_id, code, val, direct):
    """This function is filtering all occurrence for specific error variation (sxxx)"""
    query_direct = '''SELECT status_code, ''' + str(
        val) + ''', year, month, domain_id FROM logana.ln_googleBotsStatsDirectHitByStCode, logana_project WHERE 
        ln_googleBotsStatsDirectHitByStCode.project_id=logana_project.id AND status_code LIKE ''' + str(
        code) + ''' AND domain_id=''' + str(domain_id)
    query_indirect = '''SELECT status_code, ''' + str(
        val) + ''', year, month, domain_id FROM logana.ln_googleBotsStatsNDHitByCode, logana_project WHERE 
        ln_googleBotsStatsNDHitByCode.project_id=logana_project.id AND status_code LIKE ''' + str(
        code) + ''' AND domain_id=''' + str(domain_id)
    if direct:
        query = query_direct
    elif not direct:
        query = query_indirect
    print(query)
    cnx = conn()
    cursor = cnx.cursor()
    try:
        cursor.execute(query)
        query_res = cursor.fetchall()
        return query_res
    except Exception as e2:
        print(e2)
        return None


def filter_occs_wexclusion(domain_id, codeto_keep, codeto_exclude, val, tableName):
    '''This function is filtering all occurence for speciffic error variation (sxxx)'''
    query = '''SELECT code, ''' + str(
        val) + ''', year, month, domain_id FROM logana.'''+str(tableName)+''', logana_project WHERE '''+str(tableName)+'''.project_id=logana_project.id AND code LIKE ''' + str(
        codeto_keep) + ''' AND domain_id=''' + str(domain_id) + ''' AND code NOT LIKE ''' + str(codeto_exclude)
    print(query)
    cnx = conn()
    cursor = cnx.cursor()
    try:
        cursor.execute(query)
        query_res = cursor.fetchall()

        return query_res
    except Exception as e2:
        print(e2)
        return None


def filter_unq(query_result):
    '''Filter out unique occurances for errors or response code that belong to the same familly'''
    try:
        unq = {x[0] for x in query_result}
        unq_vals = [val for val in unq]
    except:
        unq = []
        unq_vals = []
    return unq_vals


def queryFormatter_wparams(db, additional, id, params, botName, isNumeric=None,):
    '''This formats a query string taking into acc if any additional parametar is required'''
    if additional == True and isNumeric == 1:
        qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(
            db) + ''' WHERE ln_genBotsMainStats.project_id=logana_project.id AND isNumeric=''' + str(
            isNumeric) + ''' AND botName=''' + str(botName) + ''' AND domain_id='''+str(id)
    
    elif additional == True and isNumeric == 0:
        qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''' WHERE project_id=''' + str(
            id) + ''' AND isNumeric=''' + str(isNumeric)

    else:
        qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''' WHERE project_id=''' + str(id)

    return qs


def targetQueryFormatter(params, db, select, id):
    ''' Formats a query string to fetch target '''
    qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''', logana_project WHERE ''' + str(
        db) + '''.project_id=logana_project.id AND target=''' + str(select) + ''' AND domain_id='''+str(id)
    print(qs)
    return qs


def methodQueryFormatter(params, db, select, id):
    ''' Formats a query string to fetch target '''
    qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''', logana_project WHERE ''' + str(
        db) + '''.project_id=logana_project.id AND method=''' + str(select) + ''' AND domain_id='''+str(id)

    return qs


def protocolQueryFormatter(params, db, select, id):
    ''' Formats a query string to fetch target '''
    qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''', logana_project WHERE ''' + str(
        db) + '''.project_id=logana_project.id AND protocol=''' + str(select) + ''' AND domain_id='''+str(id)
  
    return qs

def sourceQueryFormatter(params, db, select, crit, id):
    ''' Formats a query string to fetch target '''
    if crit:
        qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''', logana_project WHERE ''' + str(
            db) + '''.project_id=logana_project.id AND ''' + str(crit)+''' = ''' + str(select) + '''AND domain_id='''+str(id)
    
        return qs
    else:
        qs = '''SELECT ''' + str(params) + ''' FROM ''' + str(db) + ''', logana_project WHERE ''' + str(
            db) + '''.project_id=logana_project.id AND domain_id='''+str(id)
        return qs     


def find_suspicious_months(df):
    """
    Determines the months in which the number of rows in the dataframe is suspiciously high or low.
    Returns a dataframe with the month-year, number of rows, mean, and standard deviation.
    """
    # Calculate the mean and standard deviation of the number of rows
    mean_rows = df['no_rows'].mean()
    std_rows = df['no_rows'].std()

    # Calculate the upper and lower limits for a suspicious number of rows
    upper_limit = mean_rows + 2 * std_rows
    lower_limit = mean_rows - 2 * std_rows

    # Create a dataframe to store the suspicious months
    suspicious_months_df = pd.DataFrame(columns=['month-year', 'no_rows', 'mean', 'std'])

    # Iterate over each row in the dataframe
    for i, row in df.iterrows():
        # Calculate the z-score of the number of rows for this month
        z_score = (row['no_rows'] - mean_rows) / std_rows

        # If the z-score is above the upper limit or below the lower limit, mark this month as suspicious
        if z_score > 2 or z_score < -2:
            month_year = row['month-year']
            no_rows = row['no_rows']
            suspicious_months_df = suspicious_months_df._append({'month-year': month_year, 'no_rows': no_rows, 'mean': mean_rows, 'std': std_rows}, ignore_index=True)

    return suspicious_months_df


def create_df_st_codes(data, start_year=None, start_month=None, end_year=None, end_month=None):
    # Create a Pandas DataFrame with the data
    df = pd.DataFrame(data, columns=["statuscode", "numberofoccurence", "year", "month", "domain_id"])

    # Combine year and month columns into a single column "month-year"
    df['month-year'] = df[['year', 'month']].apply(lambda x: f"{x[0]}-{x[1]}", axis=1)

    # Sort the DataFrame by month and year
    df['month-year'] = pd.to_datetime(df['month-year'], format="%Y-%m")
    df.sort_values('month-year', inplace=True)
    df['month-year'] = df['month-year'].dt.strftime('%Y-%m')

    # Filter data based on start and end year/month
    if start_year and start_month:
        start_date = f"{start_year}-{start_month}"
        df = df[df['month-year'] >= start_date]
    if end_year and end_month:
        end_date = f"{end_year}-{end_month}"
        df = df[df['month-year'] <= end_date]

    # Reset the index of the DataFrame
    df.reset_index(drop=True, inplace=True)
    print(df)
    return df[['statuscode', 'numberofoccurence', 'year', 'month', 'domain_id', 'month-year']]





