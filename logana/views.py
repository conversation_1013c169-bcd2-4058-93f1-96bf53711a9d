#!/usr/bin/env python
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages

import logana.processes.queries
from .models import Project, Domain, VipPage, AiBots
from applications.models import Application
import pandas as pd
import plotly.express as px
from django.core.files.storage import FileSystemStorage
from django.core.paginator import Paginator
from .processes.scripts.bashRunner import runner
from logana.processes.utilities import lngenBotsMST, tablePop, queryFormatter, convertStrToList, extractErrorCode, \
    extractVals, prepareTuple, customQuerySetter, percentage_calc, health_score, health_score_bots_rep, \
    source_comparison, top_refering_page, hits_on_sitemap, health_score_rep, scaffold_date, filter_occs, filter_unq, \
    filter_occs_wexclusion, queryFormatter_wparams, targetQuery<PERSON>ormatter, \
    methodQueryFormatter, protocolQueryFormatter, filter_occs_direct_indirect, sourceQueryFormatter,\
    find_suspicious_months, create_df_st_codes

from logana.processes.dashes import erpData, dash_bar, dashTableFig, dash_pie, dash_app_table, table_below_graph, \
    download_button, describe_line_df, display_dash_bar, subplot_bar, \
    subplot_hist, plot_bar_from_df, subplot_bar_fix, stacked_bar_chart, stacked_bar_chart2, \
    create_bar_chart, stacked_bar_chart_df
from logana.forms import CreateProjectForm, CreateNewVipPageForm
from logana.processes.utilities import conn
import calendar
from datetime import datetime
from logana.logana_helpers import pack_all_ai_bots_data, ai_individual_dash_bar,  all_bot_names, all_bot_data, ai_bot_activity_plot, create_bot_activity_app, all_bot_ref_data, \
create_dhit_viz, create_landing_table_app, referall_landing_table_app, top_landing_table_app, referall_target_table_app,daily_bot_activity_app


@login_required(login_url='/login')
def rp_err(request):
    return render(request, 'logana/rcodes.html', {})


@login_required(login_url='/login')
def project_dash(request):
    """Project dashboard per domain """
    projects_list = Project.objects.all()

    context = {
        'projects_list': projects_list,
    }
    print(context)
    return render(request, 'logana/logadmin.html', context=context)


@login_required(login_url='/login')
def create(request):
    dom_results = Domain.objects.all()
    form = CreateProjectForm()

    context = {
        "form": form,
        "domains": dom_results
    }
    user = request.user
    if user.is_staff:
        print(user.is_staff)
        if request.method == "POST":
            title = request.POST.get('title')
            domains = request.POST.get('domains')
            description = request.POST.get('description')
            print(context)
            project = Project.objects.create(project_name=title, project_description=description, is_active=False,
                                             is_shared=True, app_id_id=1, domain_id=domains)
            messages.success(request, 'Data has been submitted')
        return render(request, 'logana/create.html', context=context)
    else:
        messages.info(request, 'You have been redirected because you don\'t have the credentials to view this page.')
    return render(request, 'logana/project.html', {})


@login_required(login_url='/login')
def upload(request):
    user = request.user
    if user.is_staff:
        if request.method == "POST" and request.FILES:
            uploaded_file = request.FILES["files"]
            print(uploaded_file)
            fs = FileSystemStorage()
            name = fs.save(uploaded_file.name, uploaded_file)
            url = fs.url(name)
            print(url)
            try:
                runner(bashFile="logana/processes/scripts/moveInput.sh")
            except:
                print("Nothing to move")
            try:
                runner(bashFile="logana/processes/scripts/runPreporcessing.sh")
            except:
                print("Preprocessing not performed!")
        return render(request, 'logana/upload.html', {})
    else:
        messages.info(request, 'You have been redirected because you don\'t have the credentials to view this page.')
        return redirect(create)


@login_required(login_url='/login')
def domain_dash(request):
    '''List projects by domain id'''
    domains = Domain.objects.all()
    context = {
        'domains': domains,
    }
    return render(request, 'logana/domain-dash.html', context=context)


@login_required(login_url='/login')
def domain_list(request, id):
    no_rows_lst = []
    domains = get_object_or_404(Domain, pk=id)
    projects = Project.objects.filter(domain_id=id).order_by("-id")
    # no_rows_data = list(Project.objects.values('no_rows'))
    # print(projects)
    # print(no_rows_data)

    project_query_set = Project.objects.filter(domain_id=id).values('no_rows', 'logs_start_date')

    # Convert the QuerySet to a list of dictionaries
    project_list = list(project_query_set)

    # Convert the logs_start_date field to a formatted date string in the format 'yyyy-mm'
    try:
        for project in project_query_set:
            logs_start_date_str = project['logs_start_date'].strftime('%Y-%m-%d %H:%M:%S.%f')
            project['logs_start_date'] = datetime.strptime(logs_start_date_str, '%Y-%m-%d %H:%M:%S.%f').strftime('%Y-%m')
    except Exception as e:
        print(e)
        logs_start_date_str = None


    # Convert the QuerySet to a list of dictionaries
    project_list = list(project_query_set)

    # Create a DataFrame from the list of dictionaries
    df = pd.DataFrame(project_list)

    # Group the DataFrame by the logs_start_date column and sum the no_rows for each group
    df = df.groupby('logs_start_date', as_index=False)['no_rows'].sum()

    # Split the logs_start_date column into separate 'year' and 'month' columns
    df[['year', 'month']] = df['logs_start_date'].str.split('-', expand=True)

    # Reorder the columns of the DataFrame
    df = df[['no_rows', 'month', 'year']]

    # Sort the DataFrame by year and month
    df = df.sort_values(by=['year', 'month'])

    df['month-year'] = df[['month', 'year']].apply(lambda x: '-'.join(x.astype(str)), axis=1)

    print(df)

    create_bar_chart(
        df=df,
        app_name='no_rows_dashboard',
        color='blue'
    )

    create_bar_chart(df=find_suspicious_months(df), app_name='suspicious_no_rows_dashboard', color='red')

    for pr in projects:
        try:
            no_rows_lst.append((pr.no_rows, pr.logs_start_date.month))
        except:
            pass

    pages = Paginator(projects, 12)
    page_lst = request.GET.get('page')
    print(Project.objects.all().values_list('logs_start_date')[0][0].month)
    pages = pages.get_page(page_lst)
    context = {
        'domains': domains,
        'projects': projects,
        'pages':pages,
    }
    return render(request, 'logana/domain.html', context=context)


@login_required(login_url='/login')
def processing_details(request):
    user = request.user
    if user.is_staff:
        return render(request, 'logana/processing-details.html', {})
    else:
        messages.info(request, 'You have been redirected because you don\'t have the credentials to view this page.')
        return redirect(project_dash)


@login_required(login_url='/login')
def project_details(request, id):
    '''Display project details for current domain id'''
    user = request.user
    obj = get_object_or_404(Project, pk=id)
    groups = request.user.groups.all()
    cid = obj.domain_id
    same_dom_projects = Project.objects.filter(domain_id=cid, id__lt=id).order_by("-id")

    print(bool(same_dom_projects))
    # ---------------Number of requests---------------------
    try:
        previous_id = same_dom_projects[0].id
        print("Previous id: ",previous_id)
    except:
        previous_id=0
    try:
        if same_dom_projects:
            prev_obj = Project.objects.get(id=previous_id)
            print('There are project before this one')
            prev_obj = {'no_rows': prev_obj.no_rows}
        else:
            print("There's no projects before this one")
            prev_obj = {'no_rows':0}
    except Exception as e:
        prev_obj = {'no_rows': 770}
    try:
        print(obj.no_rows)
        diff = obj.no_rows - same_dom_projects[0].no_rows
    except:
        diff = 0
    try:
        perdif = percentage_calc(obj.no_rows, same_dom_projects[0].no_rows)
    except:
        perdif = percentage_calc(obj.no_rows, 0)
    # ------------------------------Health Score-------------------------------------------------------------------------------#
    erps_200 = health_score(id=obj.id, field='valueProp', table="ln_geBotsMainRespCodeStats", code=200, previous_id=previous_id)
    print(erps_200)
    # ---------------------------- Redirection Check----------------------------------------------------------------------------
    erps_301 = health_score(id=obj.id, field='valueProp', table="ln_geBotsMainRespCodeStats", code=301, previous_id=previous_id)
    erps_404 = health_score(id=obj.id, field='valueProp', table="ln_geBotsMainRespCodeStats", code=404, previous_id=previous_id)
    erps_500 = health_score(id=obj.id, field='valueProp', table="ln_geBotsMainRespCodeStats", code=500, previous_id=previous_id)
    #------------------------------Top referring page -------------------------------------------------------------------------#
    try:
        top_ref_pages = top_refering_page(id_curr=obj.id, id_prev=previous_id)
    except:
        top_ref_pages = []
    #------------------------------Sitemap Hits--------------------------------------------------------------------------------#
    try:
        top_sitemap_pages = hits_on_sitemap(id_curr=obj.id, id_prev=previous_id)
    except:
        top_sitemap_pages = []
    # -----------------------------------End-----------------------------------------------------------------------------------#
    context = {
        'erps_301': erps_301,  # status code 301
        'erps_200': erps_200,  # status code 200
        'erps_404': erps_404,  # status code 404
        'erps_500': erps_500,  # status code 500
        'diff': diff,
        'perdif': perdif,
        'prev_obj': prev_obj,
        'obj': obj,
        'groups': groups,
        'top_ref_pages': top_ref_pages,
        'top_sitemap_pages': top_sitemap_pages,
    }
    if user.is_staff:
        return render(request, 'logana/project.html', context=context)
    else:
        messages.info(request, 'You have been redirected because you don\'t have the credentials to view this page.')
        return render(request, 'logana/project.html', context=context)


@login_required(login_url='/login')
def googlebot_details(request):
    return render(request, 'logana/google.html', {})


@login_required(login_url='/login')
def erp_details(request, id):
    user = request.user
    obj = get_object_or_404(Project, pk=id)
    groups = request.user.groups.all()
    data1, colNames1 = lngenBotsMST(
        queryFormatter(db='ln_geBotsMainRespCodeStats', id=id, additional=False, isNumeric=1))
    erp_data, colNames3 = lngenBotsMST(
        '''SELECT response_code, url, value_list FROM ln_genRespCodes WHERE project_id=''' + str(id))
    df1 = pd.DataFrame(data1, columns=colNames1)
    df2 = pd.DataFrame(data1[1:], columns=colNames1)
    
    healthScore = df1['valueProp'][0]
     
    barPlot1 = dash_bar(df1, colNames=colNames1, xin='code', yin='value', color=None,
                        color_discrete_sequence=['#66FF66'] * 3, label={'target': "Status codes", 'value': "Values"},
                        default_height=500, default_width=600)
    barPlot2 = dash_bar(df2, colNames=colNames1, xin='code', yin='value', color=None,
                        color_discrete_sequence=['#51E84E'] * 3, label={'target': "Status codes", 'value': "Values"},
                        default_height=500, default_width=600)

    codes = extractErrorCode(erp_data)
    codes, urls, values = extractVals(row=erp_data)
    erps = zip(codes, urls, values)
    combined = []
    for c, u, v in erps:
        combined.append((c, tuple((list((list(zip(v, u))))))))

    df_list = prepareTuple(combined)

    app = erpData(lst=df_list)
    table_ex = table_below_graph(lst=df_list)

    rcTable = tablePop(df1)
    context = {
        'user': user,
        'obj': obj,
        'groups': groups,
        'healthScore': healthScore,
        'rcTable': rcTable,
        'barPlot1': barPlot1,
        'barPlot2': barPlot2,
        'app': app,
        'table_ex': table_ex,
    }

    if user.is_staff:
        return render(request, 'logana/rcodes.html', context=context)
    else:
        messages.info(request, 'You have been redirected because you don\'t have the credentials to view this page.')
        return render(request, 'project.html', {})


@login_required(login_url='/login')
def googlebot_main(request, id):
    obj = get_object_or_404(Project, pk=id)
    source, sourceCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMainStatsBySource', id=id, colNames="source, value, month, project_id"))
    targets, targetCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMainStatsByTarget', id=id, colNames="target, value, valueProp"))
    targetBar = dash_pie(targets, colNames=targetCols, yin='target', xin='valueProp', title="", default_height=350,
                         default_width=450)

    # -------------  GB previous month comparison ----------------------

    current_log_year = obj.logs_start_date.year
    current_log_month = obj.logs_start_date.month
    current_domain_id = obj.domain.id
    prev_stats, _ = source_comparison(table='ln_googleBotsMainStatsBySource', col='source',
                                   month=(current_log_month - 1), did=current_domain_id, year=current_log_year)
    print("printing previous stats...")
    print(prev_stats)
    prev_stats_list = list(reversed(prev_stats))
    prev_stats_target, _ = source_comparison(table='ln_googleBotsMainStatsByTarget', col='target',
                                             month=(current_log_month - 1), did=current_domain_id,
                                             year=current_log_year)
    prev_target_stats_list = list(reversed(prev_stats_target))
    # ToDo - transform list to dictionary -----
    print(f"previous is {prev_stats_list}")
    print(_)

    context = {
        'obj': obj,
        'gdata': source,
        'previous': prev_stats_list,
        'prev_target_stats_list': prev_target_stats_list,
        'targets': targets,
        'targetBar': targetBar,
    }
    return render(request, 'logana/google-main.html', context=context)


@login_required(login_url='/login')
def googlebot_direct(request, id):
    '''Google Bot Direct Hits'''
    obj = get_object_or_404(Project, pk=id)
    direct, directCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsDirectHit', id=id, colNames="url, value, month, project_id"))
    directCSS, directCssCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsDirectHitByBigCSS', id=id, colNames="target, size, month, project_id"))
    directImage, directImageCols = lngenBotsMST(customQuerySetter(db='ln_googleBotsStatsDirectHitByBigImage', id=id,
                                                                  colNames="target, size, month, project_id"))
    directJson, directJsonCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsDirectHitByBigJson', id=id, colNames="target, size, month, project_id"))
    dir_urls_table = dash_app_table(lst=direct, colNames=directCols, app_name="dirUrl_tbl",
                                    csv_name="dirurl_lookup.csv", pr_name="Direct TRaffic Urls Lookup", a=0, b=2,
                                    df_col=['url, value'])
    dir_css_table = dash_app_table(lst=directCSS, colNames=directCssCols, app_name="dircss_tbl",
                                   csv_name="dircss_lookup.csv", pr_name="Direct Requests By CSS", a=0, b=2,
                                   df_col=['target', 'size'])
    dir_image_table = dash_app_table(lst=directImage, colNames=directImageCols, app_name="dirimage_tbl",
                                     csv_name="dirimage_lookup.csv", pr_name="Direct Requests By Image", a=0, b=2,
                                     df_col=['target', 'size'])
    dir_json_table = dash_app_table(lst=directJson, colNames=directJsonCols, app_name="dirjson_tbl",
                                    csv_name="dirjson_lookup.csv", pr_name="Direct Requests By Json", a=0, b=2,
                                    df_col=['target', 'size'])
    durl_download_button = download_button(lst=direct, colNames=directCols, download_app_name="durl_download",
                                           csv_name="target.csv", pr_name=str(obj))
    dcss_download_button = download_button(lst=directCSS, colNames=directCssCols, download_app_name="dcss_download",
                                           csv_name="target.csv", pr_name=str(obj))
    dimage_download_button = download_button(lst=directImage, colNames=directImageCols,
                                             download_app_name="dimage_download", csv_name="target.csv",
                                             pr_name=str(obj))
    djson_download_button = download_button(lst=directJson, colNames=directJsonCols, download_app_name="djson_download",
                                            csv_name="target.csv", pr_name=str(obj))

    # ----------------Response Code Stats--------------

    direct_respcd, directRespCdCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsDirectHitByStCode', id=id, colNames="status_code, value"))
    dhrp_barPlot = dash_bar(direct_respcd, colNames=directRespCdCols, xin='status_code', yin='value',
                            color='status_code', color_discrete_sequence=None,
                            label={'botName': "Bot Name", 'botStats': "Number of Hits"}, default_height=400,
                            default_width=450)

    # ----------------Target---------------------------
    directTargets, directTargetsCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsDirectHitByTarget', id=id, colNames="target, value, month, project_id"))
    dtar_barPlot = dash_bar(directTargets, colNames=directTargetsCols, xin='target', yin='value', color='target',
                            color_discrete_sequence=None,
                            label={'botName': "Bot Name", 'botStats': "Number of Hits On Spec Taret"},
                            default_height=400, default_width=450)

    context = {
        'obj': obj,
        'direct': direct,
        'dir_urls_table': dir_urls_table,
        'durl_download_button': durl_download_button,
        'dir_css_table': dir_css_table,
        'dcss_download_button': dcss_download_button,
        'dir_image_table': dir_image_table,
        'dimage_download_button': dimage_download_button,
        'dir_json_table': dir_json_table,
        'djson_download_button': djson_download_button,
        'dhrp_barPlot': dhrp_barPlot,
        'dtar_barPlot': dtar_barPlot,

    }
    return render(request, 'logana/google-direct.html', context=context)


@login_required(login_url='/login')
def googlebot_indirect(request, id):
    '''Google Bot Indirect Hits'''
    obj = get_object_or_404(Project, pk=id)
    indirect, indirectCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsNDHit', id=id, colNames="url, value, month, project_id"))
    indirectCSS, indirectCssCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsNDirectHitByBigCSS', id=id, colNames="target, size, month, project_id"))
    indirectImage, indirectImageCols = lngenBotsMST(customQuerySetter(db='ln_googleBotsStatsNDirectHitByBigImage', id=id,
                                                                      colNames="target, size, month, project_id"))
    indirectScode, indirectScodeCols = lngenBotsMST(customQuerySetter(db='ln_googleBotsStatsNDHitByCode', id=id,
                                                                      colNames="status_code, value, month, project_id"))
    indirectJson, indirectJsonCols = lngenBotsMST(customQuerySetter(db='ln_googleBotsStatsNDirectHitByBigJson', id=id,
                                                                    colNames="target, size, month, project_id"))
    indirectPhp, indirectPhpCols = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsStatsNDirectHitByPhP', id=id, colNames="target, size, month, project_id"))

    indirectRefPage, indirectRefPageCols = lngenBotsMST(customQuerySetter(db='ln_googleBotsStatsNDirectHitRefferingPage', id=id,
                                                                    colNames="url, value, month, project_id"))

    indir_ref_urls_table = dash_app_table(lst=indirectRefPage, colNames=indirectRefPageCols, app_name="indirRefUrl_tbl",
                                      csv_name="indirrefurl_lookup.csv", pr_name="Indirect Ref Urls Lookup", a=0, b=2,
                                      df_col=['url', 'value'])

    indir_urls_table = dash_app_table(lst=indirect, colNames=indirectCols, app_name="indirUrl_tbl",
                                      csv_name="indirurl_lookup.csv", pr_name="Inirect Traffic Urls Lookup", a=0, b=2,
                                      df_col=['target', 'size'])
    indir_css_table = dash_app_table(lst=indirectCSS, colNames=indirectCssCols, app_name="indircss_tbl",
                                     csv_name="indircss_lookup.csv", pr_name="Indirect Requests By CSS", a=0, b=2,
                                     df_col=['target', 'size'])
    indir_image_table = dash_app_table(lst=indirectImage, colNames=indirectImageCols, app_name="indirImage_tbl",
                                       csv_name="indImage_lookup.csv", pr_name="Indirect Traffic Urls Lookup", a=0, b=2,
                                       df_col=['status_code', 'value'])
    indir_json_table = dash_app_table(lst=indirectJson, colNames=indirectJsonCols, app_name="indirJson_tbl",
                                      csv_name="indJson_lookup.csv", pr_name="Indirect Json Lookup", a=0, b=2,
                                      df_col=['target', 'size'])
    indir_php_table = dash_app_table(lst=indirectPhp, colNames=indirectPhpCols, app_name="indirPhp_tbl",
                                     csv_name="indPhp_lookup.csv", pr_name="Indirect Php Lookup", a=0, b=2,
                                     df_col=['target', 'size'])
    indurl_download_button = download_button(lst=indirect, colNames=indirectCols, download_app_name="indurl_download",
                                             csv_name="target.csv", pr_name=str(obj))
    indrefurl_download_button = download_button(lst=indirectRefPage, colNames=indirectRefPageCols, download_app_name="indurefrl_download",
                                             csv_name="target.csv", pr_name=str(obj))
    indcss_download_button = download_button(lst=indirectCSS, colNames=indirectCssCols,
                                             download_app_name="indcss_download", csv_name="target.csv",
                                             pr_name=str(obj))
    indjson_download_button = download_button(lst=indirectJson, colNames=indirectJsonCols,
                                              download_app_name="indjson_download", csv_name="target.csv",
                                              pr_name=str(obj))
    indphp_download_button = download_button(lst=indirectPhp, colNames=indirectPhpCols,
                                             download_app_name="indjphp_download", csv_name="target.csv",
                                             pr_name=str(obj))

    # ----------------Status Code ---------------------
    indhrp_barPlot = dash_bar(indirectScode, colNames=indirectScodeCols, xin='status_code', yin='value',
                              color='status_code', color_discrete_sequence=None,
                              label={'botName': "Bot Name", 'botStats': "Number of Hits"}, default_height=625,
                              default_width=450)

    context = {
        'obj': obj,
        'indir_urls_table': indir_urls_table,
        'indurl_download_button': indurl_download_button,
        'indir_css_table': indir_css_table,
        'indcss_download_button': indcss_download_button,
        'indir_image_table': indir_image_table,
        'indhrp_barPlot': indhrp_barPlot,
        'indir_json_table': indir_json_table,
        'indjson_download_button': indjson_download_button,
        'indir_php_table': indir_php_table,
        'indphp_download_button': indphp_download_button,
        'indir_ref_urls_table': indir_ref_urls_table,
        'indrefurl_download_button':indrefurl_download_button,

    }

    return render(request, 'logana/google-indirect.html', context=context)


@login_required(login_url='/login')
def googlebot_mobile(request, id):
    obj = get_object_or_404(Project, pk=id)
    data, colNames = lngenBotsMST(customQuerySetter(db='ln_googleBotsMobileIPStats', id=id, colNames="ip, value"))
    data1, colNames1 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMobileMethodStats', id=id, colNames="method, value"))
    data2, colNames2 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMobileProtocolStats', id=id, colNames="protocol, value"))
    data3, colNames3 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMobileStatusCodeStats', id=id, colNames="statusCode, value"))
    data4, colNames4 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMobileTargetStats', id=id, colNames="target, value"))
    data5, colNames5 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsMobileUserAgentStats', id=id, colNames="uaString, value"))
    data6, colNames6 = lngenBotsMST(customQuerySetter(db='ln_googleBotsMobileSizeStats', id=id,
                                                      colNames="mean, std, min, twfive, fifty, seventyFive, max"))
    data7, colNames7 = lngenBotsMST(queryFormatter(db='ln_googleBotsMobileRefPageStats', id=id, additional=False))
    data8, colNames8 = lngenBotsMST(queryFormatter(db='ln_googleBotsMobileRefUrlStats', id=id, additional=False))

    ip_bar = dash_bar(data, colNames=colNames, xin='ip', yin='value', color=None,
                      color_discrete_sequence=['#3399FF'] * 10, label={'target': "Target", 'value': "Hits on target"},
                      default_height=550, default_width=550)
    method_bar = dash_bar(data1, colNames=colNames1, xin='method', yin='value', color=None,
                          color_discrete_sequence=['maroon'] * 3, label={'target': "Target", 'value': "Hits by method"},
                          default_height=500, default_width=500)
    protocol_pie = dash_pie(data2, colNames=colNames2, yin='protocol', xin='value', title="", default_height=500,
                            default_width=500)
    status_codes_bar = dash_bar(data3, colNames=colNames3, xin='statusCode', yin='value', color=None,
                                color_discrete_sequence=['#66FF66'] * 3,
                                label={'target': "Status codes", 'value': "Values"}, default_height=450,
                                default_width=450)
    target_bar = dash_bar(data4, colNames=colNames4, xin='target', yin='value', color=None,
                          color_discrete_sequence=['#3399FF'] * 10,
                          label={'target': "Target", 'value': "Hits on target"}, default_height=450, default_width=450)
    user_agent_pie = dash_pie(data5, colNames=colNames5, yin='uaString', xin='value', title="", default_height=500,
                              default_width=500)
    line_statistics = describe_line_df(data=data6, col=colNames6)
    ref_page_table = dash_app_table(lst=data7, colNames=colNames7, app_name="ref_page_tbl",
                                    csv_name="googleBot_desktop_ref_page.csv", pr_name=str(obj),
                                    df_col=['url', 'value'], a=1, b=3)
    ref_url_table = dash_app_table(lst=data8, colNames=colNames8, app_name="ref_url_tbl",
                                   csv_name="googleBot_desktop_ref_url.csv", pr_name=str(obj),
                                   df_col=['referrer_url', 'value'], a=1, b=3)
    context = {
        'obj': obj,
        'ip_bar': ip_bar,
        'method_bar': method_bar,
        'protocol_pie': protocol_pie,
        'status_codes_bar': status_codes_bar,
        'target_bar': target_bar,
        'user_agent_pie': user_agent_pie,
        'line_statistics': line_statistics,
        'ref_page_table': ref_page_table,
        'ref_url_table': ref_url_table,
    }
    return render(request, 'logana/google-mobile.html', context=context)


@login_required(login_url='/login')
def googlebot_desktop(request, id):
    obj = get_object_or_404(Project, pk=id)
    data, colNames = lngenBotsMST(customQuerySetter(db='ln_googleBotsDesktopIPStats', id=id, colNames="ip, value"))
    data1, colNames1 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsDesktopMethodStats', id=id, colNames="method, value"))
    data2, colNames2 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsDesktopProtocolStats', id=id, colNames="protocol, value"))
    data3, colNames3 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsDesktopStatusCodeStats', id=id, colNames="statusCode, value"))
    data4, colNames4 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsDesktopTargetStats', id=id, colNames="target, value"))
    data5, colNames5 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsDesktopUserAgentStats', id=id, colNames="uaString, value"))
    data6, colNames6 = lngenBotsMST(customQuerySetter(db='ln_googleBotsDesktopSizeStats', id=id,
                                                      colNames="mean, std, min, twfive, fifty, seventyFive, max"))
    data7, colNames7 = lngenBotsMST(queryFormatter(db='ln_googleBotsDesktopRefPageStats', id=id, additional=False))
    data8, colNames8 = lngenBotsMST(queryFormatter(db='ln_googleBotsDesktopRefUrlStats', id=id, additional=False))

    ip_bar = dash_bar(data, colNames=colNames, xin='ip', yin='value', color=None,
                      color_discrete_sequence=['#3399FF'] * 10, label={'target': "Target", 'value': "Hits on target"},
                      default_height=550, default_width=550)
    method_bar = dash_bar(data1, colNames=colNames1, xin='method', yin='value', color=None,
                          color_discrete_sequence=['maroon'] * 3, label={'target': "Target", 'value': "Hits by method"},
                          default_height=500, default_width=500)
    protocol_pie = dash_pie(data2, colNames=colNames2, yin='protocol', xin='value', title="", default_height=500,
                            default_width=500)
    status_codes_bar = dash_bar(data3, colNames=colNames3, xin='statusCode', yin='value', color=None,
                                color_discrete_sequence=['#66FF66'] * 3,
                                label={'target': "Status codes", 'value': "Values"}, default_height=450,
                                default_width=450)
    target_bar = dash_bar(data4, colNames=colNames4, xin='target', yin='value', color=None,
                          color_discrete_sequence=['#3399FF'] * 10,
                          label={'target': "Target", 'value': "Hits on target"}, default_height=450, default_width=450)
    user_agent_pie = dash_pie(data5, colNames=colNames5, yin='uaString', xin='value', title="", default_height=500,
                              default_width=500)
    line_statistics = describe_line_df(data=data6, col=colNames6)
    ref_page_table = dash_app_table(lst=data7, colNames=colNames7, app_name="ref_page_tbl",
                                    csv_name="googleBot_desktop_ref_page.csv", pr_name=str(obj),
                                    df_col=['url', 'value'], a=1, b=3)
    ref_url_table = dash_app_table(lst=data8, colNames=colNames8, app_name="ref_url_tbl",
                                   csv_name="googleBot_desktop_ref_url.csv", pr_name=str(obj),
                                   df_col=['referrer_url', 'value'], a=1, b=3)
    context = {
        'obj': obj,
        'ip_bar': ip_bar,
        'method_bar': method_bar,
        'protocol_pie': protocol_pie,
        'status_codes_bar': status_codes_bar,
        'target_bar': target_bar,
        'user_agent_pie': user_agent_pie,
        'line_statistics': line_statistics,
        'ref_page_table': ref_page_table,
        'ref_url_table': ref_url_table,
    }
    return render(request, 'logana/google-desktop.html', context=context)


@login_required(login_url='/login')
def googlebot_images(request, id):
    obj = get_object_or_404(Project, pk=id)
    data, colNames = lngenBotsMST(customQuerySetter(db='ln_googleBotsImageIPStats', id=id, colNames="ip, value"))
    data1, colNames1 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsImageMethodStats', id=id, colNames="method, value"))
    data2, colNames2 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsImageProtocolStats', id=id, colNames="protocol, value"))
    data3, colNames3 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsImageStatusCodeStats', id=id, colNames="statusCode, value"))
    data4, colNames4 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsImageTargetStats', id=id, colNames="target, value"))
    data5, colNames5 = lngenBotsMST(
        customQuerySetter(db='ln_googleBotsImageUserAgentStats', id=id, colNames="uaString, value"))
    data6, colNames6 = lngenBotsMST(customQuerySetter(db='ln_googleBotsImageSizeStats', id=id,
                                                      colNames="mean, std, min, twfive, fifty, seventyFive, max"))
    data7, colNames7 = lngenBotsMST(queryFormatter(db='ln_googleBotsImageRefPageStats', id=id, additional=False))
    data8, colNames8 = lngenBotsMST(queryFormatter(db='ln_googleBotsImageRefUrlStats', id=id, additional=False))
    print(data7[0:15])
    print(colNames7)
    print(data8[0:15])
    print(colNames8)
    ip_bar = dash_bar(data, colNames=colNames, xin='ip', yin='value', color=None,
                      color_discrete_sequence=['#3399FF'] * 10, label={'target': "Target", 'value': "Hits on target"},
                      default_height=550, default_width=550)
    method_bar = dash_bar(data1, colNames=colNames1, xin='method', yin='value', color=None,
                          color_discrete_sequence=['maroon'] * 3, label={'target': "Target", 'value': "Hits by method"},
                          default_height=500, default_width=500)
    protocol_pie = dash_pie(data2, colNames=colNames2, yin='protocol', xin='value', title="", default_height=500,
                            default_width=500)
    status_codes_bar = dash_bar(data3, colNames=colNames3, xin='statusCode', yin='value', color=None,
                                color_discrete_sequence=['#66FF66'] * 3,
                                label={'target': "Status codes", 'value': "Values"}, default_height=450,
                                default_width=450)
    target_bar = dash_bar(data4, colNames=colNames4, xin='target', yin='value', color=None,
                          color_discrete_sequence=['#3399FF'] * 10,
                          label={'target': "Target", 'value': "Hits on target"}, default_height=450, default_width=450)
    user_agent_pie = dash_pie(data5, colNames=colNames5, yin='uaString', xin='value', title="", default_height=500,
                              default_width=500)
    line_statistics = describe_line_df(data=data6, col=colNames6)
    ref_page_table = dash_app_table(lst=data7, colNames=colNames7, app_name="ref_page_tbl",
                                    csv_name="googleBot_desktop_ref_page.csv", pr_name=str(obj),
                                    df_col=['url', 'value'], a=1, b=3)
    ref_url_table = dash_app_table(lst=data8, colNames=colNames8, app_name="ref_url_tbl",
                                   csv_name="googleBot_desktop_ref_url.csv", pr_name=str(obj),
                                   df_col=['referrer_url', 'value'], a=1, b=3)
    context = {
        'obj': obj,
        'ip_bar': ip_bar,
        'method_bar': method_bar,
        'protocol_pie': protocol_pie,
        'status_codes_bar': status_codes_bar,
        'target_bar': target_bar,
        'user_agent_pie': user_agent_pie,
        'line_statistics': line_statistics,
        'ref_page_table': ref_page_table,
        'ref_url_table': ref_url_table,
    }
    return render(request, 'logana/google-images.html', context=context)


@login_required(login_url='/login')
def googlebot_news(request, id):
    obj = get_object_or_404(Project, pk=id)
    return render(request, 'logana/google-news.html', {})


@login_required(login_url='/login')
def googlebot_video(request, id):
    obj = get_object_or_404(Project, pk=id)
    return render(request, 'logana/google-videobot.html', {})


@login_required(login_url='/login')
def googlebot_ads(request, id):
    obj = get_object_or_404(Project, pk=id)
    return render(request, 'logana/google-ads.html', {})


@login_required(login_url='/login')
def bot_details(request, id):
    obj = get_object_or_404(Project, pk=id)
    score_data, scoreNames = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStats', id=id, colNames="botName, botStatsProp"))
    data, colNames = lngenBotsMST(queryFormatter(db='ln_genBotsMainStats', id=id, additional=True, isNumeric=0))
    data1, colNames = lngenBotsMST(queryFormatter(db='ln_genBotsMainStats', id=id, additional=True, isNumeric=1))
    data2, colNames3 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStatsByTarget', id=id, colNames="target, value, valueProp"))
    data3, colNames4 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStatsByTargetNDhit', id=id, colNames="target, value"))
    data4, colNames5 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStatsByTargetDhit', id=id, colNames="target, value"))
    data5, colNames6 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStatsByMethod', id=id, colNames="method, value"))
    data6, colNames7 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStatsBySource', id=id, colNames="source, value"))
    data7, colNames8 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStats', id=id, colNames="botName, botStatsProp"))
    data8, colNames9 = lngenBotsMST(
        customQuerySetter(db='ln_genBotsMainStatsByProtVersion', id=id, colNames="protocol, value"))
    data9, colNames10 = lngenBotsMST(customQuerySetter(db='ln_genBotsMainStatsByRefPage', id=id, colNames="url, value"))
    target_download_button = download_button(lst=data2, colNames=colNames3, download_app_name="target_download",
                                             csv_name="target.csv", pr_name=str(obj))
    top5refpages_download_button = download_button(lst=data9, colNames=colNames10,
                                                   download_app_name="top5_ref_download", csv_name="urls.csv",
                                                   pr_name=str(obj))
    direct_hits_download_button = download_button(lst=data4, colNames=colNames5,
                                                  download_app_name="direct_hits_download", csv_name="direct_hits.csv",
                                                  pr_name=str(obj))
    version_download_button = download_button(lst=data8, colNames=colNames9, download_app_name="version_download",
                                              csv_name="version.csv", pr_name=str(obj))
    source_download_button = download_button(lst=data6, colNames=colNames7, download_app_name="source_download",
                                             csv_name="source.csv", pr_name=str(obj))
    method_download_button = download_button(lst=data5, colNames=colNames6, download_app_name="method_download",
                                             csv_name="method.csv", pr_name=str(obj))

    barPlot = dash_bar(data[0:11], colNames=colNames, xin='botStats', yin='botName', color='botStats',
                       color_discrete_sequence=None, label={'botName': "Bot Name", 'botStats': "Number of Hits"},
                       default_height=500, default_width=500)
    barplotSus = dash_bar(data1[0:11], colNames=colNames, xin='botStats', yin='botName', color='botStats',
                          color_discrete_sequence=None, label={'botName': "Bot Name", 'botStats': "Number of Hits"},
                          default_height=400, default_width=450)
    table_ver = dashTableFig(data[1:], colNames=colNames)
    targetBar = dash_bar(data2, colNames=colNames3, xin='target', yin='value', color=None,
                         color_discrete_sequence=['#3399ff'] * 10,
                         label={'target': "Target", 'value': "Hits on target"}, default_height=350, default_width=350)
    dhitBar = dash_bar(data3, colNames=colNames4, xin='target', yin='value', color=None,
                       color_discrete_sequence=['#666699'] * 3,
                       label={'target': "Target", 'value': "Direct Hits on target"}, default_height=350,
                       default_width=350)
    ndhitBar = dash_bar(data4, colNames=colNames5, xin='target', yin='value', color=None,
                        color_discrete_sequence=['orange'] * 3,
                        label={'target': "Target", 'value': "Non Direct Hits on target"}, default_height=350,
                        default_width=350)
    methodBar = dash_bar(data5, colNames=colNames6, xin='method', yin='value', color=None,
                         color_discrete_sequence=['maroon'] * 3, label={'target': "Target", 'value': "Hits by method"},
                         default_height=300, default_width=300)
    methodBar = dash_pie(data5, colNames6, xin='value', yin='method', title='Overall bot Activity by method',
                         default_height=300, default_width=300)
    sourceBar = dash_bar(data6, colNames=colNames7, xin='source', yin='value', color=None,
                         color_discrete_sequence=['#00ffff'] * 3, label={'target': "Source", 'value': "Hits by Source"},
                         default_height=300, default_width=300)
    versionBar = dash_pie(data8, colNames=colNames9, yin='protocol', xin='value', title="", default_height=400,
                          default_width=400)
    urlsBar = dash_bar(data9[0:5], colNames=colNames10, yin='url', xin='value', color=None,
                       color_discrete_sequence=['#66ff66'] * 3,
                       label={'target': "Url", 'value': "Total number of requests"}, default_height=400,
                       default_width=400)

    success_table = dash_app_table(lst=data, colNames=colNames, app_name="success_tbl",
                                   csv_name="successfull_lookup.csv", pr_name="Successfull Lookup", a=2, b=4,
                                   df_col=['botName', 'botStats'])
    suspicious_table = dash_app_table(lst=data1, colNames=colNames, app_name="suspiccious_tbl",
                                      csv_name="suspicious_lookup.csv", pr_name="Suspiccious-lookup", a=2, b=4,
                                      df_col=['botName', 'botStats'])

    context = {
        'obj': obj,
        'scores': data7[0:5],
        'barplot': barPlot,
        'barplot_sus': barplotSus,
        'table_ver': table_ver,
        "targetBar": targetBar,
        'dhitBar': dhitBar,
        'ndhitBar': ndhitBar,
        'methodBar': methodBar,
        'sourceBar': sourceBar,
        'versionBar': versionBar,
        'urlsBar': urlsBar,
        'success_table': success_table,
        'suspicious_table': suspicious_table,
        'target_download_button': target_download_button,
        'top5refpages_download_button': top5refpages_download_button,
        'direct_hits_download_button': direct_hits_download_button,
        'version_download_button': version_download_button,
        'source_download_button': source_download_button,
        'method_download_button': method_download_button,

    }
    return render(request, 'logana/bots.html', context=context)


@login_required(login_url = '/login')
def report(request, id):
    '''Populates month over month report page'''
    dom = get_object_or_404(Domain, pk=id)

    #Invoking function with modified data queried from the database and assign the data to variable for MoM health Score
    health_data =  scaffold_date(health_score_rep(domain_id=dom.id, code=200, val='valueProp'))


    healthBar = display_dash_bar(lst=health_data, colNames=['Code', 'HealthScore', 'year', 'month', 'domain_id'], app_name='healthBar', xin='year', yin='HealthScore', width=650, height=450, name="healthBar", color="#e28743", opacity=1, title=None)
    #healthBar = subplot_bar(lst=health_data, colNames=['Code', 'HealthScore', 'year', 'month', 'domain_id'], app_name='healthBar', width=400, height=400, opacity=0.9, text='HealthScore', facet='Code', color='green')


    #MoM for response codes filters all variations of 50x error code found historically
    error_50x = filter_occs(domain_id=dom.id, code='"5%"', val='value', tableName='ln_geBotsMainRespCodeStats')
    print(error_50x)
    print(len(error_50x[0]))
    stacked_bar_chart_df(df=create_df_st_codes(data=error_50x), app_name='5xx_dash', width=550, height=500)
    # print(error_50x)
    # error50x_data = scaffold_date(error_50x)
    # print(f"500 DATA FOR THIS PROJECT IS {error50x_data}")
    # error50x = subplot_bar(lst=error50x_data, colNames=['Code', 'Error5xx', 'year', 'month', 'domain_id'], app_name='error50x', width=460, height=450, opacity=0.9, text='Error5xx', facet='Code', color='#ff0000')
    #error50x = subplot_hist(lst=error50x_data, colNames=['Code', 'Error5xx', 'year', 'month', 'domain_id'],  app_name='error50x',xin='year', yin='Error5xx', frow=None, color='Code', width=650, height=450)
    #error_50x = stacked_bar_chart(lst=error50x_data, colNames=['Code', 'Error5xx', 'year', 'month', 'domain_id'], app_name='error50x', width=600, height=600, opacity=0.9, text='Error5xx', facet='Code', color='#ff0000')
    #MoM for response code filters all variations for 40x resp code found historically

    error_40x = filter_occs(domain_id=dom.id, code='"4%"', val='value', tableName='ln_geBotsMainRespCodeStats')
    stacked_bar_chart_df(df=create_df_st_codes(data=error_40x), app_name='error40x', width=550, height=500)
    #error40x_data = scaffold_date(error_40x)
    #error40x = subplot_bar(lst=error40x_data, colNames=['Code', 'Error4xx', 'year', 'month', 'domain_id'], app_name='error40x', width=460, height=450, opacity=0.9, text='Error4xx', facet='Code', color='#ff6666')
    #error40x = subplot_hist(lst=error40x_data, colNames=['Code', 'Error4xx', 'year', 'month', 'domain_id'],  app_name='error40x',xin='year', yin='Error4xx', frow=None, color='Code', width=650, height=450)
    #error40x = stacked_bar_chart(lst=error40x_data, colNames=['Code', 'Error4xx', 'year', 'month', 'domain_id'], app_name='error40x', width=600, height=600, opacity=0.9, text='Error4xx', facet='Code', color='#ff6666')
    #MoM for response code filters all variations for 30x resp code found historically
    error_30x = filter_occs(domain_id=dom.id, code='"3%"', val='value', tableName='ln_geBotsMainRespCodeStats')
    stacked_bar_chart_df(df=create_df_st_codes(data=error_30x), app_name='error30x', width=550, height=500)
    #error30x_data = scaffold_date(error_30x)
    #error30x = subplot_bar(lst=error30x_data, colNames=['Code', 'Error3xx', 'year', 'month', 'domain_id'], app_name='error30x', width=700, height=600, opacity=0.9, text='Error3xx', facet='Code', color='#0066ff')
    #error30x = stacked_bar_chart2(lst=error30x_data, colNames=['Code', 'Error3xx', 'year', 'month', 'domain_id'], app_name='error30x', width=460, height=450, opacity=0.9, text='Error3xx', facet='Code', color='#0066ff')
    #error30x = subplot_hist(lst=error30x_data, colNames=['Code', 'Error3xx', 'year', 'month', 'domain_id'],  app_name='error30x',xin='year', yin='Error3xx', frow=None, color='Code', width=650, height=450)

    #MoM for response code filters all variations for 20x resp code found historically
    error_20x = filter_occs_wexclusion(domain_id=dom.id, codeto_keep='"2%"', val='value', codeto_exclude='200', tableName='ln_geBotsMainRespCodeStats')
    error20x_data = scaffold_date(error_20x)
    #error20x = subplot_bar_fix(lst=error20x_data, colNames=['Code', 'Error2xx', 'year', 'month', 'domain_id'], app_name='error20x', width=650, height=450, opacity=0.9, text='Error2xx', facet='Code', color='green')
    error20x = stacked_bar_chart(lst=error20x_data, colNames=['Code', 'Error2xx', 'year', 'month', 'domain_id'], app_name='error20x', width=475, height=450, opacity=0.9, text='Error2xx', facet='Code', color='green')


    #Extract succefful loocups
    scl1,colNamesScl = lngenBotsMST(queryFormatter_wparams(db = 'logana.ln_genBotsMainStats, logana_project', id=id, additional=True, params='botName, botStats, year, month, project_id', botName="'[Errno 1] Unknown host'", isNumeric=1))
    scl2,colNamesScl = lngenBotsMST(queryFormatter_wparams(db = 'logana.ln_genBotsMainStats, logana_project', id=id, additional=True, params='botName, botStats, year, month, project_id', botName="'[Errno 2] Host name lookup failure'", isNumeric=1))
    scl3,colNamesScl = lngenBotsMST(queryFormatter_wparams(db = 'logana.ln_genBotsMainStats, logana_project', id=id, additional=True, params='botName, botStats, year, month, project_id', botName="'No botName specified-er-01'", isNumeric=1))
    scflScl1 = scaffold_date(scl1)
    scflScl2 = scaffold_date(scl2)
    scflScl3 = scaffold_date(scl3)

    #print(scl)
    scflSclx1 = subplot_bar(lst=scflScl1, colNames=['Code', 'lookup', 'year', 'month', 'domain_id'], app_name='lookupx-1', width=650, height=450, opacity=0.9, text='lookup', facet=None, color='#32a852')
    scflSclx2 = subplot_bar(lst=scflScl2, colNames=['Code', 'lookup', 'year', 'month', 'domain_id'], app_name='lookupx-2', width=650, height=450, opacity=0.9, text='lookup', facet=None, color='#abdbb8')
    scflSclx3 = subplot_bar(lst=scflScl3, colNames=['Code', 'lookup', 'year', 'month', 'domain_id'], app_name='lookupx-3', width=650, height=450, opacity=0.9, text='lookup', facet=None, color='#8fb398')



    #Populates Target and Source Section
    #Page as target
    targetPage,colNamesScl = lngenBotsMST(targetQueryFormatter(params='target, valueProp, year, month',db='logana.ln_genBotsMainStatsByTarget', select='"Page"', id=dom.id))
    scflTarP = scaffold_date(targetPage)
    targetPbar = subplot_bar(lst=scflTarP, colNames=['target', 'valueProp', 'year', 'month'], app_name='target-p', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')


    #CSS as target
    targetCss,colNamesScl = lngenBotsMST(targetQueryFormatter(params='target, valueProp, year, month',db='logana.ln_genBotsMainStatsByTarget', select='"CSS"', id=dom.id))
    scflTarCs = scaffold_date(targetCss)
    targetCssBar = subplot_bar(lst=scflTarCs, colNames=['target', 'valueProp', 'year', 'month'], app_name='target-css', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#53bdd7')

    #JavaScript As target
    targetJas,colNamesScl = lngenBotsMST(targetQueryFormatter(params='target, valueProp, year, month',db='logana.ln_genBotsMainStatsByTarget', select='"JavaScript"', id=dom.id))
    scflTarJas = scaffold_date(targetJas)
    targetJasBar = subplot_bar(lst=scflTarJas, colNames=['target', 'valueProp', 'year', 'month'], app_name='target-jas', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#eab676')

    #PHP as a target
    targetPhp,colNamesScl = lngenBotsMST(targetQueryFormatter(params='target, valueProp, year, month',db='logana.ln_genBotsMainStatsByTarget', select='"PHP"', id=dom.id))
    scflTarPhp = scaffold_date(targetPhp)
    targetPhpBar = subplot_bar(lst=scflTarPhp, colNames=['target', 'valueProp', 'year', 'month'], app_name='target-php', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#d86044')

    #JSON as a target
    targetJson,colNamesScl = lngenBotsMST(targetQueryFormatter(params='target, valueProp, year, month',db='logana.ln_genBotsMainStatsByTarget', select='"JSON"', id=dom.id))
    scflTarJson = scaffold_date(targetJson)
    targetJsonBar = subplot_bar(lst=scflTarJson, colNames=['target', 'valueProp', 'year', 'month'], app_name='target-json', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#bcd844')

    #Image as target
    targetImage,colNamesScl = lngenBotsMST(targetQueryFormatter(params='target, valueProp, year, month',db='logana.ln_genBotsMainStatsByTarget', select='"Image"', id=dom.id))
    scflTarImage = scaffold_date(targetImage)
    targetImageBar = subplot_bar(lst=scflTarImage, colNames=['target', 'valueProp', 'year', 'month'], app_name='target-imag', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#8b44d8')


    #Method
    #Get
    methodGet,colNamesScl = lngenBotsMST(methodQueryFormatter(params='method, valueProp, year, month',db='logana.ln_genBotsMainStatsByMethod', select='"GET"', id=dom.id))
    scflTarmethod = scaffold_date(methodGet)
    methodGetBar = subplot_bar(lst=scflTarmethod, colNames=['method', 'valueProp', 'year', 'month'], app_name='method-get', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#FFFFCC')

    #Post Method
    methodGet,colNamesScl = lngenBotsMST(methodQueryFormatter(params='method, valueProp, year, month',db='logana.ln_genBotsMainStatsByMethod', select='"POST"', id=dom.id))
    scflTarmethod1 = scaffold_date(methodGet)
    methodGetBar = subplot_bar(lst=scflTarmethod1, colNames=['method', 'valueProp', 'year', 'month'], app_name='method-post', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#FFFF99')

    #OPtions
    methodOptions,colNamesScl = lngenBotsMST(methodQueryFormatter(params='method, valueProp, year, month',db='logana.ln_genBotsMainStatsByMethod', select='"OPTIONS"', id=dom.id))
    scflTarmethodOpt = scaffold_date(methodOptions)
    methodOprBar = subplot_bar(lst=scflTarmethodOpt, colNames=['method', 'value', 'year', 'month'], app_name='method-options', width=650, height=450, opacity=0.9, text='value', facet=None, color='#FFFF66')

    #Head
    methodHead,colNamesScl = lngenBotsMST(methodQueryFormatter(params='method, valueProp, year, month',db='logana.ln_genBotsMainStatsByMethod', select='"HEAD"', id=dom.id))
    scflTarmethodHead = scaffold_date(methodHead)
    methodHeadBar = subplot_bar(lst=scflTarmethodHead, colNames=['method', 'valueProp', 'year', 'month'], app_name='method-head', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#FFFF33')

    #Protocol
    #1.0
    prot10,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, valueProp, year, month',db='logana.ln_genBotsMainStatsByProtVersion', select='"HTTP/1.0"', id=dom.id))
    scflProt10 = scaffold_date(prot10)
    print(scflProt10)
    prot10Bar = subplot_bar(lst=scflProt10, colNames=['protocol', 'valueProp', 'year', 'month'], app_name='protocol-10', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#66CDAA')

    #1.1
    prot11,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, value, year, month',db='logana.ln_genBotsMainStatsByProtVersion', select='"HTTP/1.1"', id=dom.id))
    scflProt11 = scaffold_date(prot11)
    prot11Bar = subplot_bar(lst=scflProt11, colNames=['protocol', 'valueProp', 'year', 'month'], app_name='protocol-11', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#00FFFF')

    #2.0
    prot20,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, valueProp, year, month',db='logana.ln_genBotsMainStatsByProtVersion', select='"HTTP/2.0"', id=dom.id))
    scflProt20 = scaffold_date(prot20)
    print(scflProt20)
    prot20Bar = subplot_bar(lst=scflProt20, colNames=['protocol', 'valueProp', 'year', 'month'], app_name='protocol-20', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#AFEEEE')

    #No Protocol
    noprot,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, value, year, month',db='logana.ln_genBotsMainStatsByProtVersion', select='"No protocol Specified"', id=dom.id))
    scflnoProt = scaffold_date(noprot)
    noprotBar = subplot_bar(lst=scflnoProt, colNames=['protocol', 'value', 'year', 'month'], app_name='protocol-noproto', width=500, height=400, opacity=0.9, text='value', facet=None, color='#40E0D0')

    #Source Mobile VS Desktop
    desktop,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, valueProp, year, month, domain_id',db='logana.ln_genBotsMainStatsBySource', crit=None, select='"Mobile"', id=dom.id))
    print(colNamesScl)
    print(desktop)
    print(pd.DataFrame(desktop, columns=colNamesScl))
    scflDesktop = scaffold_date(desktop)

    #desktopBar = subplot_bar(lst=scflDesktop, colNames=['source', 'value', 'year', 'month'], app_name='source-desktop', width=550, height=450, opacity=0.9, text='value', facet=None, color='#4456d8')
    #desktopHist =  subplot_hist(lst=scflDesktop, colNames=['source', 'valueProp', 'year', 'month'],  app_name='source-desktop',xin='year', yin='valueProp', frow=None, color='source', width=700, height=450)
    stacked_bar_chart_df(create_df_st_codes(desktop), app_name='source-desktop', width=550, height=500)
    #print(scflDesktop)

    #Bots
    #AdsBot
    adsBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_genBotsMainStatsBySource', crit='source', select='"AdsBot"', id=dom.id))
    scflAdsBot = scaffold_date(adsBot)

    adsBotBar = subplot_bar(lst=scflAdsBot, colNames=['protocol', 'value', 'year', 'month'], app_name='adsBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#fe9971')

    #AdsenseBot
    adsenseBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_genBotsMainStatsBySource', crit='source', select='"AdsenseBot"', id=dom.id))
    scflAdsenseBot = scaffold_date(adsenseBot)

    adsenseBotBar = subplot_bar(lst=scflAdsenseBot, colNames=['source', 'value', 'year', 'month'], app_name='adsenseBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#e2bd8d')

    #Video Bot
    videoBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_genBotsMainStatsBySource', crit='source', select='"VideoBot"', id=dom.id))
    scflVideoBot = scaffold_date(videoBot)
    videoBotBar = subplot_bar(lst=scflVideoBot, colNames=['source', 'value', 'year', 'month'], app_name='videoBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#2a6665')

    #Image Bot
    imageBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_genBotsMainStatsBySource', crit='source', select='"ImageBot"', id=dom.id))
    scflImageBot = scaffold_date(imageBot)
    imageBotBar = subplot_bar(lst=scflImageBot, colNames=['source', 'value', 'year', 'month'], app_name='imageBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#f1d1c9')




    context = {
        "dom":dom,
    }
    return render(request, 'logana/report.html', context=context)


@login_required(login_url='/login')
def googlebot_report(request, id):
    dom = get_object_or_404(Domain, pk=id)
    #Google MOM Heath Score
    gbot_health_score = scaffold_date(health_score_bots_rep(domain_id=dom.id, code=200, val='valueProp', table='ln_googleBotsMainRespCodeStats'))
    print(gbot_health_score)
    googlehealthBar = display_dash_bar(lst=gbot_health_score, colNames=['Code', 'HealthScore', 'year', 'month', 'domain_id'], app_name='googleMoMHealthBar', xin='year', yin='HealthScore', width=650, height=450, name="healthBar", color="#e28743", opacity=1, title=None)

    #Response code section
    #Response CODE 500
    gerror_50x = filter_occs(domain_id=dom.id, code='"5%"', val='value', tableName='ln_googleBotsMainRespCodeStats')
    stacked_bar_chart_df(df=create_df_st_codes(gerror_50x), app_name='gerror50x', width=550, height=500)
    #gerror50x_data = scaffold_date(gerror_50x)
    #error50x = subplot_bar(lst=gerror50x_data, colNames=['Code', 'Error5xx', 'year', 'month', 'domain_id'], app_name='gerror50x', width=600, height=600, opacity=0.9, text='Error5xx', facet='Code', color='#ff0000')
    #error50x = subplot_hist(lst=gerror50x_data, colNames=['Code', 'Error5xx', 'year', 'month', 'domain_id'],  app_name='gerror50x',xin='year', yin='Error5xx', frow=None, color='Code', width=650, height=450)


   #MoM for response code filters all variations for 40x resp code found historically
    gerror_40x = filter_occs(domain_id=dom.id, code='"4%"', val='value', tableName='ln_googleBotsMainRespCodeStats')
    stacked_bar_chart_df(df=create_df_st_codes(gerror_40x), app_name='gerror40x', width=550, height=500)
    #gerror40x_data = scaffold_date(gerror_40x)
    #error40x = subplot_bar(lst=gerror40x_data, colNames=['Code', 'Error4xx', 'year', 'month', 'domain_id'], app_name='gerror40x', width=600, height=600, opacity=0.9, text='Error4xx', facet='Code', color='#ff6666')
    #error40x = subplot_hist(lst=gerror40x_data, colNames=['Code', 'Error4xx', 'year', 'month', 'domain_id'],  app_name='gerror40x',xin='year', yin='Error4xx', frow=None, color='Code', width=650, height=450)

    #MoM for response code filters all variations for 30x resp code found historically
    gerror_30x = filter_occs(domain_id=dom.id, code='"3%"', val='value', tableName='ln_googleBotsMainRespCodeStats')
    stacked_bar_chart_df(df=create_df_st_codes(gerror_30x), app_name='gerror30x', width=550, height=500)
    #gerror30x_data = scaffold_date(gerror_30x)
    #error30x = subplot_bar(lst=error30x_data, colNames=['Code', 'Error3xx', 'year', 'month', 'domain_id'], app_name='error30x', width=700, height=600, opacity=0.9, text='Error3xx', facet='Code', color='#0066ff')
    #gerror30x = subplot_hist(lst=gerror30x_data, colNames=['Code', 'Error3xx', 'year', 'month', 'domain_id'],  app_name='gerror30x',xin='year', yin='Error3xx', frow=None, color='Code', width=650, height=450)

    #MoM for response code filters all variations for 20x resp code found historically
    gerror_20x = filter_occs_wexclusion(domain_id=dom.id, codeto_keep='"2%"', val='value', codeto_exclude='200', tableName='ln_googleBotsMainRespCodeStats')
    #stacked_bar_chart_df(df=create_df_st_codes(gerror_20x), app_name='gerror20x', width=550, height=500)
    gerror20x_data = scaffold_date(gerror_20x)
    gerror20x = stacked_bar_chart(lst=gerror20x_data, colNames=['Code', 'Error2xx', 'year', 'month', 'domain_id'], app_name='gerror20x', width=475, height=450, opacity=0.9, text='Error2xx', facet='Code', color='green')
    #gerror20x = subplot_bar_fix(lst=gerror20x_data, colNames=['Code', 'Error2xx', 'year', 'month', 'domain_id'], app_name='gerror20x', width=650, height=450, opacity=0.9, text='Error2xx', facet='Code', color='green')


    #Source Mobile VS Desktop
    desktop,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, valueProp, year, month', db='logana.ln_googleBotsMainStatsBySource', crit=None, select='"Mobile"', id=dom.id))
    desktop1,colNamesScl1 = lngenBotsMST(sourceQueryFormatter(params='source, valueProp, year, month, domain_id', db='logana.ln_googleBotsMainStatsBySource', crit=None, select='"Mobile"', id=dom.id))
    scflDesktop = scaffold_date(desktop)
    print(desktop)
    stacked_bar_chart_df(df=create_df_st_codes(desktop1), app_name='gsource-desktop', width=550, height=500)
    #desktopBar = subplot_bar(lst=scflDesktop, colNames=['source', 'value', 'year', 'month'], app_name='source-desktop', width=550, height=450, opacity=0.9, text='value', facet=None, color='#4456d8')
    #desktopHist =  subplot_hist(lst=scflDesktop, colNames=['source', 'valueProp', 'date', 'month'],  app_name='gsource-desktop',xin='date', yin='valueProp', frow=None, color='source', width=650, height=450)
    #print(scflDesktop)

    #Bots
    #AdsBot
    adsBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_googleBotsMainStatsBySource', crit='source', select='"AdsBot"', id=dom.id))
    scflAdsBot = scaffold_date(adsBot)

    adsBotBar = subplot_bar(lst=scflAdsBot, colNames=['protocol', 'value', 'year', 'month'], app_name='gadsBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#fe9971')

    #AdsenseBot
    adsenseBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_googleBotsMainStatsBySource', crit='source', select='"AdsenseBot"', id=dom.id))
    scflAdsenseBot = scaffold_date(adsenseBot)

    adsenseBotBar = subplot_bar(lst=scflAdsenseBot, colNames=['source', 'value', 'year', 'month'], app_name='gadsenseBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#e2bd8d')

    #Video Bot
    videoBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_googleBotsMainStatsBySource', crit='source', select='"VideoBot"', id=dom.id))
    scflVideoBot = scaffold_date(videoBot)
    videoBotBar = subplot_bar(lst=scflVideoBot, colNames=['source', 'value', 'year', 'month'], app_name='gvideoBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#2a6665')

    #Image Bot
    imageBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, value, year, month',db='ln_googleBotsMainStatsBySource', crit='source', select='"ImageBot"', id=dom.id))
    scflImageBot = scaffold_date(imageBot)
    imageBotBar = subplot_bar(lst=scflImageBot, colNames=['source', 'value', 'year', 'month'], app_name='gimageBot', width=500, height=400, opacity=0.9, text='value', facet=None, color='#f1d1c9')

    #Populates Target and Source Section
    #Page as target
    gtargetPage,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMainStatsByTarget', select='"Page"',  crit='target', id=dom.id))
    gscflTarP = scaffold_date(gtargetPage)
    gtargetPbar = subplot_bar(lst=gscflTarP, colNames=['source', 'valueProp', 'year', 'month'], app_name='gtarget-p', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')


    #CSS as target
    gtargetCss,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMainStatsByTarget', select='"CSS"', crit='target', id=dom.id))
    gscflTarCs = scaffold_date(gtargetCss)
    gtargetCssBar = subplot_bar(lst=gscflTarCs, colNames=['source', 'valueProp', 'year', 'month'], app_name='gtarget-css', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#53bdd7')

    #JavaScript As target
    gtargetJas,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMainStatsByTarget', select='"JavaScript"',  crit='target', id=dom.id))
    gscflTarJas = scaffold_date(gtargetJas)
    gtargetJasBar = subplot_bar(lst=gscflTarJas, colNames=['source', 'valueProp', 'year', 'month'], app_name='gtarget-jas', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#eab676')

    #PHP as a target
    gtargetPhp,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMainStatsByTarget', select='"PHP"',  crit='target', id=dom.id))
    gscflTarPhp = scaffold_date(gtargetPhp)
    gtargetPhpBar = subplot_bar(lst=gscflTarPhp, colNames=['source', 'valueProp', 'year', 'month'], app_name='garget-php', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#d86044')

    #JSON as a target
    gtargetJson,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMainStatsByTarget', select='"JSON"',  crit='target', id=dom.id))
    gscflTarJson = scaffold_date(gtargetJson)
    gtargetJsonBar = subplot_bar(lst=gscflTarJson, colNames=['source', 'valueProp', 'year', 'month'], app_name='gtarget-json', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#bcd844')

    #Image as target
    gtargetImage,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMainStatsByTarget', select='"Image"',  crit='target', id=dom.id))
    gscflTarImage = scaffold_date(gtargetImage)
    gtargetImageBar = subplot_bar(lst=gscflTarImage, colNames=['source', 'valueProp', 'year', 'month'], app_name='gtarget-imag', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#8b44d8')


    #OPtions
    gmethodOptions,colNamesScl = lngenBotsMST(methodQueryFormatter(params='method, valueProp, year, month',db='logana.ln_googleBotsMainMethodStats', select='"OPTIONS"', id=dom.id))
    gscflTarmethodOpt = scaffold_date(gmethodOptions)
    gmethodOprBar = subplot_bar(lst=gscflTarmethodOpt, colNames=['method', 'value', 'year', 'month'], app_name='gmethod-options', width=650, height=450, opacity=0.9, text='value', facet=None, color='#FFFF66')

    #Head
    gmethodHead,colNamesScl = lngenBotsMST(methodQueryFormatter(params='method, valueProp, year, month',db='logana.ln_googleBotsMainMethodStats', select='"HEAD"', id=dom.id))
    gscflTarmethodHead = scaffold_date(gmethodHead)
    gmethodHeadBar = subplot_bar(lst=gscflTarmethodHead, colNames=['method', 'valueProp', 'year', 'month'], app_name='gmethod-head', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#FFFF33')

    #Protocol
    #1.0
    gprot10,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, valueProp, year, month',db='logana.ln_googleBotsMainProtocolStats', select='"HTTP/1.0"', id=dom.id))
    gscflProt10 = scaffold_date(gprot10)
    gprot10Bar = subplot_bar(lst=gscflProt10, colNames=['protocol', 'valueProp', 'year', 'month'], app_name='gprotocol-10', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#66CDAA')

    #1.1
    gprot11,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, value, year, month',db='logana.ln_googleBotsMainProtocolStats', select='"HTTP/1.1"', id=dom.id))
    gscflProt11 = scaffold_date(gprot11)
    gprot11Bar = subplot_bar(lst=gscflProt11, colNames=['protocol', 'valueProp', 'year', 'month'], app_name='gprotocol-11', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#00FFFF')

    #2.0
    gprot20,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, valueProp, year, month',db='logana.ln_googleBotsMainProtocolStats', select='"HTTP/2.0"', id=dom.id))
    gscflProt20 = scaffold_date(gprot20)
    gprot20Bar = subplot_bar(lst=gscflProt20, colNames=['protocol', 'valueProp', 'year', 'month'], app_name='gprotocol-20', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#AFEEEE')

    #No Protocol
    noprot,colNamesScl = lngenBotsMST(protocolQueryFormatter(params='protocol, value, year, month',db='logana.ln_googleBotsMainProtocolStats', select='"No protocol Specified"', id=dom.id))
    scflnoProt = scaffold_date(noprot)
    noprotBar = subplot_bar(lst=scflnoProt, colNames=['protocol', 'value', 'year', 'month'], app_name='protocol-noproto', width=500, height=400, opacity=0.9, text='value', facet=None, color='#40E0D0')


    #Mobile Section
    #Mobile Bot
    mobileBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, valueProp, year, month',db='ln_googleBotsMainStatsBySource', crit='source', select='"Mobile"', id=dom.id))
    scflMobileBot = scaffold_date(mobileBot)
    mobileBotBar = subplot_bar(lst=scflMobileBot, colNames=['source', 'valueProp', 'year', 'month'], app_name='gmobileBot', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#f1d1c9')


    #Desktop Bot
    desktopBot,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='source, valueProp, year, month',db='ln_googleBotsMainStatsBySource', crit='source', select='"Desktop"', id=dom.id))
    scflDesktopBot = scaffold_date(desktopBot)
    desktopBotBar = subplot_bar(lst=scflDesktopBot, colNames=['source', 'valueProp', 'year', 'month'], app_name='gdesktopBot', width=500, height=400, opacity=0.9, text='valueProp', facet=None, color='#f1d1c9')

    desktopHist =  subplot_hist(lst=scflDesktop, colNames=['source', 'valueProp', 'date', 'month'],  app_name='gsource-mobile',xin='date', yin='valueProp', frow=None, color='source', width=500, height=400)


    #Mobile Hits by target
    #Mobile Hits On Page
    gtargetMobilePage,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMobileTargetStats', select='"Page"',  crit='target', id=dom.id))
    gscflTarMobileP = scaffold_date(gtargetMobilePage)
    gtargetMobilePbar = subplot_bar(lst=gscflTarMobileP, colNames=['target', 'valueProp', 'year', 'month'], app_name='gtarget-p-mobile', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')

    #Mobile Hits On CSS
    gtargetMobileCSS,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMobileTargetStats', select='"css"',  crit='target', id=dom.id))
    gscflTarMobileCSS = scaffold_date(gtargetMobileCSS)
    gtargetMobileCSSbar = subplot_bar(lst=gscflTarMobileCSS, colNames=['target', 'valueProp', 'year', 'month'], app_name='gtarget-css-mobile', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')



    #Mobile Hits On JavaScript
    gtargetMobileJavaScript,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMobileTargetStats', select='"JavaScript"',  crit='target', id=dom.id))
    gscflTarMobileJavaScript = scaffold_date(gtargetMobileJavaScript)
    gtargetMobileJavaScriptbar = subplot_bar(lst=gscflTarMobileJavaScript, colNames=['target', 'valueProp', 'year', 'month'], app_name='gtarget-javascript-mobile', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')


    #Mobile Hits On JSON
    gtargetMobileJSON,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMobileTargetStats', select='"JSON"',  crit='target', id=dom.id))
    gscflTarMobileJSON = scaffold_date(gtargetMobileJSON)
    gtargetMobileJSONbar = subplot_bar(lst=gscflTarMobileJSON, colNames=['target', 'valueProp', 'year', 'month'], app_name='gtarget-json-mobile', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')


    #Mobile Hits On PHP
    gtargetMobilePHP,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMobileTargetStats', select='"PHP"',  crit='target', id=dom.id))
    gscflTarMobilePHP = scaffold_date(gtargetMobilePHP)
    gtargetMobilePHPbar = subplot_bar(lst=gscflTarMobilePHP, colNames=['target', 'valueProp', 'year', 'month'], app_name='gtarget-php-mobile', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')


    #Mobile Hits On Image
    gtargetMobileImage,colNamesScl = lngenBotsMST(sourceQueryFormatter(params='target, valueProp, year, month',db='logana.ln_googleBotsMobileTargetStats', select='"Image"',  crit='target', id=dom.id))
    gscflTarMobileImage = scaffold_date(gtargetMobileImage)
    gtargetMobileImagebar = subplot_bar(lst=gscflTarMobileImage, colNames=['target', 'valueProp', 'year', 'month'], app_name='gtarget-image-mobile', width=650, height=450, opacity=0.9, text='valueProp', facet=None, color='#4456d8')
    context = {
        'dom': dom,


    }
    return render(request, 'logana/report_googlebot.html', context=context)



@login_required(login_url = '/login')
def add_new_vip(request):
    '''Create new entry for VIP page in the database'''
    dom_results = Domain.objects.all()
    form = CreateNewVipPageForm()

    context={
        "form": form,
        "domains": dom_results
        }
    user = request.user
    if user.is_staff:
        print(user.is_staff)
        if request.method == "POST":
            title = request.POST.get('title')
            domains = request.POST.get('domains')
            ptype = request.POST.get('page_type')
            description = request.POST.get('description')
            print(context)
            vip_page = VipPage.objects.create(url =title, description=description,domain_id=domains, page_type=ptype)
            messages.success(request,'Data has been submitted')
        return render(request, 'logana/vip-enter.html', context=context)
    else:
        messages.info(request, 'You have been redirected because you don\'t have the credentials to view this page.')
    return render(request, 'logana/vip-enter.html', {})


@login_required(login_url='./login')
def ai_bot_summary(request, id):
    '''New page for AI bots Summary - Monthly Level'''
    

    
    obj = get_object_or_404(Domain, pk=id)
    bot_details = AiBots.objects.all()



    all_ai_bots_stats = "SELECT * FROM logana.ln_genAiBotsMainStats WHERE domain_id = %s"
    ai_strat_ref_pages_query = "SELECT * FROM logana.ln_AiBotsMainStatsByReferalPage WHERE domain_id= %s" 
    ai_top_landing_pages_query = "SELECT * FROM logana.ln_AiBotsMainStatsByReferalPage WHERE domain_id= %s" 

    
    bvals = pack_all_ai_bots_data(lngenBotsMST(stmt=all_ai_bots_stats, prmtr=(id,)))  
    lpa, colNames = lngenBotsMST(stmt=ai_strat_ref_pages_query, prmtr=(id,))
    bot_data = all_bot_data(bvals) 
    
    ref_data, ref_dates = all_bot_ref_data(lpa, colNames)
    app_name = "allAiBotsLinePlot"  
    create_bot_activity_app(bot_data, app_name)  
    dhit_bars = create_dhit_viz(ref_data, ref_dates, "ref_dhit")

    #Fetching the data for the table  ln_AiBotsReferalRefferingStats
    ai_referral_landing_query = "SELECT *FROM logana.ln_AiBotsReferalRefferingStats WHERE domain_id= %s"
    lrp, colNames = lngenBotsMST(stmt=ai_referral_landing_query, prmtr=(id,))

    lap_app_name = "landingRefferelsDownload"
    rlp_with_download = referall_landing_table_app(lrp, col_names=colNames, app_name=lap_app_name)

    #Fetching top refering pages table ln_AiBotsMainStatsByRefPage
    ai_referral_l_query = "SELECT *FROM logana.ln_AiBotsMainStatsByRefPage WHERE domain_id= %s"
    allp, lpcolNames = lngenBotsMST(stmt=ai_referral_l_query, prmtr=(id,))
    lap_app_name = "landingPagesDashTable"
    create_landing_table_app(allp, lpcolNames, lap_app_name)

    
    

    


    context = {
        'bot_details': bot_details,
        'domain_id': id,
        'obj': obj,
        'dash_app_name':app_name,
        'dhit_bars':dhit_bars,
        'rlp_with_download':rlp_with_download,
        'lap_dash_app_name':lap_app_name,

    }

    return render(request, 'logana/aibot.html', context=context)


@login_required(login_url='./login')
def ai_bot_single(request, id):
    '''Individual Bot page for speciffic domain'''

    all_ai_bots_stats = f"SELECT * FROM logana.ln_genAiBotsMainStats WHERE project_id={id}"
    

    bvals = pack_all_ai_bots_data(lngenBotsMST(stmt=all_ai_bots_stats))  
    allDAta = all_bot_data(bvals=bvals)
    bot_names = [bot for bot in allDAta.keys()]
    bot_details = AiBots.objects.filter(bot_name__in=bot_names)
    context = {
     'bot_details':bot_details,
     'domain_id': id,
    }

    return render(request, 'logana/ai_individual.html', context=context)

@login_required(login_url='./login')
def ai_bot_rep(request, id, bot_id):
    '''Showing satats for selected bot'''
    bot = get_object_or_404(AiBots, id=bot_id)
    ai_stat_code_query = f"SELECT * FROM logana.ln_AiBotsMainRespCodeStats WHERE botName='{bot.bot_name}' AND project_id={id}"
    data_lst, colNames = lngenBotsMST(stmt=ai_stat_code_query)
    individual_error_stats_bar = ai_individual_dash_bar(lst=data_lst, 
                                                        colNames=colNames,
                                                        xin='code',
                                                        yin='value',
                                                        color='code',
                                                        #color_discrete_sequence=['#66FF66'] * 3,
                                                        label='Status Code',
                                                        default_height='auto',
                                                        default_width='auto'
                                                        )
    



    #Fetching the data for top landing pages  taking into the acc the fact that in prev table we have bot NAme insetead of bot id
    referral_landing_query = f"SELECT *FROM logana.ln_AiBotsReferalLAndingPageStatByBotId WHERE bot_id=%s AND project_id=%s"
    lap, lapcolNames = lngenBotsMST(stmt=referral_landing_query, prmtr=(bot.id, id))
    top_landings = top_landing_table_app(data=lap, col_names=lapcolNames, app_name="topLandings")



    #Fetching the data for Direct Hits taking into the acc the fact that in prev table we have bot NAme insetead of bot id
    ai_dhit_query = f"SELECT * FROM logana.ln_AiBotsReferalRefferingStats WHERE botName=%s AND project_id=%s"
    allp, lpcolNames = lngenBotsMST(stmt=ai_dhit_query, prmtr=(bot.bot_name, id))
    referall_target_table_app(data=allp, col_names=lpcolNames, app_name="referalTarget")

    activity_query = """
        SELECT visit_date AS datetime, visit_count AS count, bot_id
        FROM AiBots_bot_daily_visits 
        WHERE bot_id = %s AND project_id = %s
    """
    da, _ = lngenBotsMST(stmt=activity_query, prmtr=(bot.id, id))

    daily_bot_activity_app(df=da, bot_name=bot.bot_name, app_name="dailyActivity")
  


    
    
    context = {
        'bot': bot,
        'id': id,  
        'individual_error_stats_bar':individual_error_stats_bar,

       
    }

    return render(request, 'logana/ai_bot_rep.html', context=context)


