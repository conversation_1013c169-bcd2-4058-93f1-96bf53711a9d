import os
import mysql.connector
import pandas as pd
import csv
import io
from datetime import datetime, timedelta
from dotenv import load_dotenv
from alerts.management.utils.import_slack_sdk import send_slack_message, send_slack_message_with_file
from .slack_channels import brasil_channel_id, chl_channel_id

# Load environment variables
load_dotenv()

def get_database_connection():
    """Get a connection to the topstories database"""
    return mysql.connector.connect(
        host=os.getenv("DATABASE_GT_HOST", "localhost"),
        user=os.getenv("DATABASE_GT_USER", "root"),
        password=os.getenv("DATABASE_GT_PASSWORD"),
        database=os.getenv("DATABASE_GT_NAME", "topstories")
    )

def get_last_4_hours_trending_data():
    """
    Query gt_trending_data table for data from the last 4 hours
    Returns a pandas DataFrame with the results
    """
    conn = get_database_connection()

    try:
        # Calculate timestamp for 4 hours ago
        four_hours_ago = datetime.now() - timedelta(hours=4)

        # SQL query to get data from last 4 hours
        query = """
        SELECT id, country_code, keyword, title, time, search_volume,
               started_date, url, timestamp, hours, category_param, status
        FROM gt_trending_data
        WHERE timestamp >= %s
        ORDER BY timestamp DESC, country_code
        """

        # Execute query and return as DataFrame
        df = pd.read_sql(query, conn, params=[four_hours_ago])

        print(f"Retrieved {len(df)} records from the last 4 hours")
        return df

    except Exception as e:
        print(f"Error querying trending data: {e}")
        return pd.DataFrame()  # Return empty DataFrame on error
    finally:
        conn.close()

def get_recent_trending_data(hours=24):
    """
    Query gt_trending_data table for data from the last N hours (default 24)
    Returns a pandas DataFrame with the results
    """
    conn = get_database_connection()

    try:
        # Calculate timestamp for N hours ago
        hours_ago = datetime.now() - timedelta(hours=hours)

        # SQL query to get data from last N hours
        query = """
        SELECT id, country_code, keyword, title, time, search_volume,
               started_date, url, timestamp, hours, category_param, status
        FROM gt_trending_data
        WHERE timestamp >= %s
        ORDER BY timestamp DESC, country_code
        """

        # Execute query and return as DataFrame
        df = pd.read_sql(query, conn, params=[hours_ago])

        print(f"Retrieved {len(df)} records from the last {hours} hours")
        return df

    except Exception as e:
        print(f"Error querying trending data: {e}")
        return pd.DataFrame()  # Return empty DataFrame on error
    finally:
        conn.close()

def get_all_recent_data():
    """
    Query gt_trending_data table for recent data (last 7 days)
    Returns a pandas DataFrame with the results
    """
    conn = get_database_connection()

    try:
        # Calculate timestamp for 7 days ago
        seven_days_ago = datetime.now() - timedelta(days=7)

        # SQL query to get recent data
        query = """
        SELECT id, country_code, keyword, title, time, search_volume,
               started_date, url, timestamp, hours, category_param, status
        FROM gt_trending_data
        WHERE timestamp >= %s
        ORDER BY timestamp DESC, country_code
        LIMIT 100
        """

        # Execute query and return as DataFrame
        df = pd.read_sql(query, conn, params=[seven_days_ago])

        print(f"Retrieved {len(df)} records from the last 7 days")
        return df

    except Exception as e:
        print(f"Error querying trending data: {e}")
        return pd.DataFrame()  # Return empty DataFrame on error
    finally:
        conn.close()

def generate_csv_from_dataframe(df, country_code=None):
    """
    Generate CSV content from DataFrame
    If country_code is provided, filter data for that country
    Returns CSV content as string
    """
    if df.empty:
        return "No data available for the specified criteria."

    # Filter by country if specified
    if country_code:
        filtered_df = df[df['country_code'] == country_code]
        if filtered_df.empty:
            return f"No data available for country: {country_code}"
        df = filtered_df

    # Convert DataFrame to CSV string
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_content = csv_buffer.getvalue()
    csv_buffer.close()

    return csv_content

def get_country_data_summary(df, country_code):
    """
    Generate a summary of trending data for a specific country
    Returns a formatted string with key statistics
    """
    if df.empty:
        return f"No trending data found for {country_code}"

    country_data = df[df['country_code'] == country_code]

    if country_data.empty:
        return f"No trending data found for {country_code}"

    # Generate summary statistics
    total_records = len(country_data)
    unique_keywords = country_data['keyword'].nunique()
    latest_timestamp = country_data['timestamp'].max()
    oldest_timestamp = country_data['timestamp'].min()

    # Get top trending keywords
    top_keywords = country_data['keyword'].value_counts().head(5)

    summary = f"""
📊 *Google Trends Data Summary for {country_code.upper()}*
📅 Time Range: {oldest_timestamp} to {latest_timestamp}
📈 Total Records: {total_records}
🔍 Unique Keywords: {unique_keywords}

🔥 *Top Trending Keywords:*
"""

    for i, (keyword, count) in enumerate(top_keywords.items(), 1):
        summary += f"{i}. {keyword} ({count} entries)\n"

    return summary

def get_slack_channel_for_country(country_code):
    """
    Map country codes to appropriate Slack channel IDs
    Returns the channel ID for the given country, or None if not found
    """
    # Country to channel mapping
    country_channel_mapping = {
        'BR': brasil_channel_id,  # Brazil
        'CL': chl_channel_id,     # Chile
        # Add more country mappings as needed
    }

    # Convert country code to uppercase for consistency
    country_code_upper = country_code.upper()

    channel_id = country_channel_mapping.get(country_code_upper)

    if channel_id:
        print(f"Found Slack channel for {country_code_upper}: {channel_id}")
        return channel_id
    else:
        print(f"No Slack channel configured for country: {country_code_upper}")
        return None

def get_available_countries_with_channels():
    """
    Get list of countries that have configured Slack channels
    Returns a list of country codes
    """
    country_channel_mapping = {
        'BR': brasil_channel_id,  # Brazil
        'CL': chl_channel_id,     # Chile
    }

    return list(country_channel_mapping.keys())

def send_trending_notifications():
    """
    Main function to send Google Trends notifications to Slack channels
    Gets last 4 hours of data and sends to appropriate channels by country
    """
    print("Starting Google Trends Slack notifications...")

    # Get trending data from last 4 hours
    df = get_last_4_hours_trending_data()

    if df.empty:
        print("No trending data found for the last 4 hours")
        return False

    # Get unique countries in the data
    countries_in_data = df['country_code'].unique()
    print(f"Countries found in data: {countries_in_data}")

    # Get countries that have configured Slack channels
    available_countries = get_available_countries_with_channels()
    print(f"Countries with configured channels: {available_countries}")

    # Send notifications for each country that has both data and a configured channel
    notifications_sent = 0

    for country_code in countries_in_data:
        channel_id = get_slack_channel_for_country(country_code)

        if channel_id:
            # Generate summary and CSV for this country
            summary = get_country_data_summary(df, country_code)
            csv_content = generate_csv_from_dataframe(df, country_code)

            # Create filename with timestamp and country
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"google_trends_{country_code.lower()}_{timestamp}.csv"

            # Send to Slack with CSV file attachment
            try:
                success = send_slack_message_with_file(
                    recipient_id=channel_id,
                    message=summary,
                    file_content=csv_content,
                    filename=filename
                )
                if success:
                    print(f"✅ Successfully sent notification for {country_code}")
                    notifications_sent += 1
                else:
                    print(f"❌ Failed to send notification for {country_code}")
            except Exception as e:
                print(f"❌ Error sending notification for {country_code}: {e}")
        else:
            print(f"⚠️ Skipping {country_code} - no Slack channel configured")

    print(f"Notifications process completed. Sent {notifications_sent} notifications.")
    return notifications_sent > 0

def send_test_notification_to_brasil():
    """
    Test function to send a notification specifically to the Brasil channel
    """
    print("Testing notification to Brasil channel...")

    # Get trending data from last 4 hours
    df = get_last_4_hours_trending_data()

    if df.empty:
        print("No trending data found for the last 4 hours")
        # Send a test message anyway
        test_message = "🧪 *Test Message*\nNo trending data available for the last 4 hours."
        try:
            success = send_slack_message(brasil_channel_id, test_message)
            if success:
                print("✅ Test message sent to Brasil channel")
                return True
            else:
                print("❌ Failed to send test message to Brasil channel")
                return False
        except Exception as e:
            print(f"❌ Error sending test message: {e}")
            return False

    # Generate summary and CSV for Brasil (BR)
    summary = get_country_data_summary(df, 'BR')
    csv_content = generate_csv_from_dataframe(df, 'BR')

    # Create filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"test_brasil_trends_{timestamp}.csv"

    # Create the message
    message = f"🧪 *Test Notification for Brasil*\n\n{summary}"

    # Send to Brasil channel with CSV file
    try:
        success = send_slack_message_with_file(
            recipient_id=brasil_channel_id,
            message=message,
            file_content=csv_content,
            filename=filename
        )
        if success:
            print("✅ Successfully sent test notification to Brasil channel")
            return True
        else:
            print("❌ Failed to send test notification to Brasil channel")
            return False
    except Exception as e:
        print(f"❌ Error sending test notification to Brasil: {e}")
        return False

def test_with_live_data():
    """
    Test function with live data from the database
    """
    print("Testing with live data from database...")

    # Try different time ranges to find data
    print("\n1. Checking last 4 hours...")
    df = get_last_4_hours_trending_data()

    if df.empty:
        print("No data in last 4 hours, trying last 24 hours...")
        df = get_recent_trending_data(24)

    if df.empty:
        print("No data in last 24 hours, trying last 7 days...")
        df = get_all_recent_data()

    if df.empty:
        print("❌ No data found in database")
        return False

    print(f"Found {len(df)} records")
    print(f"Countries in data: {df['country_code'].unique()}")
    print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")

    # Test with Brasil data if available
    brasil_data = df[df['country_code'] == 'BR']
    if not brasil_data.empty:
        print(f"\nFound {len(brasil_data)} records for Brasil")

        # Generate summary and CSV for Brasil
        summary = get_country_data_summary(df, 'BR')
        csv_content = generate_csv_from_dataframe(df, 'BR')

        print("Brasil Summary:")
        print(summary)

        print(f"\nCSV Preview (first 300 chars):")
        print(csv_content[:300] + "..." if len(csv_content) > 300 else csv_content)

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"live_brasil_trends_{timestamp}.csv"

        # Send to Brasil channel with CSV file
        message = f"🔴 *Live Data Test for Brasil*\n\n{summary}"

        try:
            success = send_slack_message_with_file(
                recipient_id=brasil_channel_id,
                message=message,
                file_content=csv_content,
                filename=filename
            )
            if success:
                print("✅ Successfully sent live data to Brasil channel")
                return True
            else:
                print("❌ Failed to send live data to Brasil channel")
                return False
        except Exception as e:
            print(f"❌ Error sending live data: {e}")
            return False
    else:
        print("No Brasil data found, testing with all available data...")

        # Show what countries we have
        countries = df['country_code'].unique()
        print(f"Available countries: {countries}")

        # Send a summary of all data to Brasil channel for testing
        total_records = len(df)
        unique_countries = len(countries)
        date_range = f"{df['timestamp'].min()} to {df['timestamp'].max()}"

        # Get a sample of the data (first 50 rows)
        sample_df = df.head(50)
        csv_content = generate_csv_from_dataframe(sample_df)

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"live_all_countries_sample_{timestamp}.csv"

        message = f"""🔴 *Live Database Test - All Countries*

📊 *Database Summary:*
📈 Total Records: {total_records}
🌍 Countries ({unique_countries}): {', '.join(countries)}
📅 Date Range: {date_range}

📄 *CSV file contains sample data (first 50 records)*"""

        try:
            success = send_slack_message_with_file(
                recipient_id=brasil_channel_id,
                message=message,
                file_content=csv_content,
                filename=filename
            )
            if success:
                print("✅ Successfully sent database summary to Brasil channel")
                return True
            else:
                print("❌ Failed to send database summary to Brasil channel")
                return False
        except Exception as e:
            print(f"❌ Error sending database summary: {e}")
            return False

def test_with_sample_data():
    """
    Test function with sample data to demonstrate the CSV functionality
    """
    print("Testing with sample data...")

    # Create sample data
    sample_data = {
        'id': [1, 2, 3, 4],
        'country_code': ['BR', 'BR', 'CL', 'BR'],
        'keyword': ['futebol', 'carnaval', 'chile trends', 'brasil news'],
        'title': ['Football News', 'Carnival 2024', 'Chile Trends', 'Brazil News'],
        'time': ['2024-01-01', '2024-01-01', '2024-01-01', '2024-01-01'],
        'search_volume': [1000, 800, 600, 900],
        'started_date': ['2024-01-01', '2024-01-01', '2024-01-01', '2024-01-01'],
        'url': ['http://example1.com', 'http://example2.com', 'http://example3.com', 'http://example4.com'],
        'timestamp': [datetime.now(), datetime.now(), datetime.now(), datetime.now()],
        'hours': [1, 2, 1, 3],
        'category_param': ['sports', 'culture', 'general', 'news'],
        'status': ['active', 'active', 'active', 'active']
    }

    df = pd.DataFrame(sample_data)

    # Test CSV generation for Brasil
    csv_content = generate_csv_from_dataframe(df, 'BR')
    print("Sample CSV for Brasil:")
    print(csv_content[:200] + "..." if len(csv_content) > 200 else csv_content)

    # Test summary generation
    summary = get_country_data_summary(df, 'BR')
    print("\nSample Summary for Brasil:")
    print(summary)

    # Test sending to Brasil channel with sample data
    message = f"🧪 *Sample Data Test for Brasil*\n\n{summary}\n\n📄 *CSV Data:*\n```\n{csv_content}\n```"

    try:
        success = send_slack_message(brasil_channel_id, message)
        if success:
            print("✅ Successfully sent sample data to Brasil channel")
            return True
        else:
            print("❌ Failed to send sample data to Brasil channel")
            return False
    except Exception as e:
        print(f"❌ Error sending sample data: {e}")
        return False

def send_live_data_to_brasil():
    """
    Simple function to send live data to Brasil channel
    Call this function directly when database is connected
    """
    print("🔴 Sending live data to Brasil channel...")

    try:
        # Try to get data from different time ranges
        df = get_recent_trending_data(24)  # Last 24 hours

        if df.empty:
            df = get_all_recent_data()  # Last 7 days, limit 100

        if df.empty:
            print("❌ No data found in database")
            return False

        print(f"Found {len(df)} total records")
        print(f"Countries: {df['country_code'].unique()}")

        # Filter for Brasil or send all data
        brasil_data = df[df['country_code'] == 'BR']

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if not brasil_data.empty:
            print(f"Found {len(brasil_data)} Brasil records")
            summary = get_country_data_summary(df, 'BR')
            csv_content = generate_csv_from_dataframe(df, 'BR')
            filename = f"live_brasil_trends_{timestamp}.csv"
            message = f"🇧🇷 *Live Brasil Trends Data*\n\n{summary}"
        else:
            print("No Brasil data found, sending sample of all data")
            sample_df = df.head(20)  # First 20 records
            csv_content = generate_csv_from_dataframe(sample_df)
            countries = df['country_code'].unique()
            filename = f"live_all_countries_sample_{timestamp}.csv"

            message = f"""🌍 *Live Google Trends Data Sample*

📊 *Database Summary:*
📈 Total Records: {len(df)}
🌍 Countries: {', '.join(countries)}
📅 Date Range: {df['timestamp'].min()} to {df['timestamp'].max()}

📄 *CSV file contains sample data (first 20 records)*"""

        # Send to Brasil channel with CSV file
        success = send_slack_message_with_file(
            recipient_id=brasil_channel_id,
            message=message,
            file_content=csv_content,
            filename=filename
        )
        if success:
            print("✅ Successfully sent live data to Brasil channel")
            return True
        else:
            print("❌ Failed to send live data to Brasil channel")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_database_connection():
    """
    Test database connection and show available data
    """
    print("Testing database connection...")
    try:
        conn = get_database_connection()
        print("✅ Database connection successful!")

        # Test basic query
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM gt_trending_data")
        total_count = cursor.fetchone()[0]
        print(f"Total records in gt_trending_data: {total_count}")

        # Get recent data count
        cursor.execute("SELECT COUNT(*) FROM gt_trending_data WHERE timestamp >= NOW() - INTERVAL 7 DAY")
        recent_count = cursor.fetchone()[0]
        print(f"Records from last 7 days: {recent_count}")

        # Get countries
        cursor.execute("SELECT DISTINCT country_code FROM gt_trending_data ORDER BY country_code")
        countries = [row[0] for row in cursor.fetchall()]
        print(f"Available countries: {countries}")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    # Test the implementation
    print("=" * 50)
    print("Google Trends Slack Notifications Test")
    print("=" * 50)

    # First test database connection
    print("\n1. Testing database connection...")
    db_connected = test_database_connection()

    if db_connected:
        print("\n" + "=" * 50)
        print("2. Testing with LIVE DATA from database...")
        test_with_live_data()

        print("\n" + "=" * 50)
        print("3. Testing Brasil channel notification (4 hours)...")
        send_test_notification_to_brasil()

        print("\n" + "=" * 50)
        print("4. Testing all country notifications (4 hours)...")
        send_trending_notifications()
    else:
        print("\n⚠️ Skipping database tests due to connection issues")
        print("Please check your database configuration in .env file:")
        print("- DATABASE_GT_HOST")
        print("- DATABASE_GT_USER")
        print("- DATABASE_GT_PASSWORD")
        print("- DATABASE_GT_NAME")

    print("\n" + "=" * 50)
    print("5. Testing with sample data (no database required)...")
    test_with_sample_data()

    print("\n" + "=" * 50)
    print("Test completed!")
    print("=" * 50)

