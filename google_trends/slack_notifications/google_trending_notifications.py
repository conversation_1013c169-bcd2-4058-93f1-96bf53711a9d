import os
import mysql.connector
import pandas as pd
import csv
import io
from datetime import datetime, timedelta
from dotenv import load_dotenv
from alerts.management.utils.import_slack_sdk import send_slack_message
from .slack_channels import brasil_channel_id, chl_channel_id

# Load environment variables
load_dotenv()

def get_database_connection():
    """Get a connection to the topstories database"""
    return mysql.connector.connect(
        host=os.getenv("DATABASE_GT_HOST", "localhost"),
        user=os.getenv("DATABASE_GT_USER", "root"),
        password=os.getenv("DATABASE_GT_PASSWORD"),
        database=os.getenv("DATABASE_GT_NAME", "topstories")
    )

def get_last_4_hours_trending_data():
    """
    Query gt_trending_data table for data from the last 4 hours
    Returns a pandas DataFrame with the results
    """
    conn = get_database_connection()

    try:
        # Calculate timestamp for 4 hours ago
        four_hours_ago = datetime.now() - timedelta(hours=4)

        # SQL query to get data from last 4 hours
        query = """
        SELECT id, country_code, keyword, title, time, search_volume,
               started_date, url, timestamp, hours, category_param, status
        FROM gt_trending_data
        WHERE timestamp >= %s
        ORDER BY timestamp DESC, country_code
        """

        # Execute query and return as DataFrame
        df = pd.read_sql(query, conn, params=[four_hours_ago])

        print(f"Retrieved {len(df)} records from the last 4 hours")
        return df

    except Exception as e:
        print(f"Error querying trending data: {e}")
        return pd.DataFrame()  # Return empty DataFrame on error
    finally:
        conn.close()

def generate_csv_from_dataframe(df, country_code=None):
    """
    Generate CSV content from DataFrame
    If country_code is provided, filter data for that country
    Returns CSV content as string
    """
    if df.empty:
        return "No data available for the specified criteria."

    # Filter by country if specified
    if country_code:
        filtered_df = df[df['country_code'] == country_code]
        if filtered_df.empty:
            return f"No data available for country: {country_code}"
        df = filtered_df

    # Convert DataFrame to CSV string
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_content = csv_buffer.getvalue()
    csv_buffer.close()

    return csv_content

def get_country_data_summary(df, country_code):
    """
    Generate a summary of trending data for a specific country
    Returns a formatted string with key statistics
    """
    if df.empty:
        return f"No trending data found for {country_code}"

    country_data = df[df['country_code'] == country_code]

    if country_data.empty:
        return f"No trending data found for {country_code}"

    # Generate summary statistics
    total_records = len(country_data)
    unique_keywords = country_data['keyword'].nunique()
    latest_timestamp = country_data['timestamp'].max()
    oldest_timestamp = country_data['timestamp'].min()

    # Get top trending keywords
    top_keywords = country_data['keyword'].value_counts().head(5)

    summary = f"""
📊 *Google Trends Data Summary for {country_code.upper()}*
📅 Time Range: {oldest_timestamp} to {latest_timestamp}
📈 Total Records: {total_records}
🔍 Unique Keywords: {unique_keywords}

🔥 *Top Trending Keywords:*
"""

    for i, (keyword, count) in enumerate(top_keywords.items(), 1):
        summary += f"{i}. {keyword} ({count} entries)\n"

    return summary

def get_slack_channel_for_country(country_code):
    """
    Map country codes to appropriate Slack channel IDs
    Returns the channel ID for the given country, or None if not found
    """
    # Country to channel mapping
    country_channel_mapping = {
        'BR': brasil_channel_id,  # Brazil
        'CL': chl_channel_id,     # Chile
        # Add more country mappings as needed
    }

    # Convert country code to uppercase for consistency
    country_code_upper = country_code.upper()

    channel_id = country_channel_mapping.get(country_code_upper)

    if channel_id:
        print(f"Found Slack channel for {country_code_upper}: {channel_id}")
        return channel_id
    else:
        print(f"No Slack channel configured for country: {country_code_upper}")
        return None

def get_available_countries_with_channels():
    """
    Get list of countries that have configured Slack channels
    Returns a list of country codes
    """
    country_channel_mapping = {
        'BR': brasil_channel_id,  # Brazil
        'CL': chl_channel_id,     # Chile
    }

    return list(country_channel_mapping.keys())

def send_trending_notifications():
    """
    Main function to send Google Trends notifications to Slack channels
    Gets last 4 hours of data and sends to appropriate channels by country
    """
    print("Starting Google Trends Slack notifications...")

    # Get trending data from last 4 hours
    df = get_last_4_hours_trending_data()

    if df.empty:
        print("No trending data found for the last 4 hours")
        return False

    # Get unique countries in the data
    countries_in_data = df['country_code'].unique()
    print(f"Countries found in data: {countries_in_data}")

    # Get countries that have configured Slack channels
    available_countries = get_available_countries_with_channels()
    print(f"Countries with configured channels: {available_countries}")

    # Send notifications for each country that has both data and a configured channel
    notifications_sent = 0

    for country_code in countries_in_data:
        channel_id = get_slack_channel_for_country(country_code)

        if channel_id:
            # Generate summary and CSV for this country
            summary = get_country_data_summary(df, country_code)
            csv_content = generate_csv_from_dataframe(df, country_code)

            # Create the complete message
            message = f"{summary}\n\n📄 *CSV Data:*\n```\n{csv_content}\n```"

            # Send to Slack
            try:
                success = send_slack_message(channel_id, message)
                if success:
                    print(f"✅ Successfully sent notification for {country_code}")
                    notifications_sent += 1
                else:
                    print(f"❌ Failed to send notification for {country_code}")
            except Exception as e:
                print(f"❌ Error sending notification for {country_code}: {e}")
        else:
            print(f"⚠️ Skipping {country_code} - no Slack channel configured")

    print(f"Notifications process completed. Sent {notifications_sent} notifications.")
    return notifications_sent > 0

def send_test_notification_to_brasil():
    """
    Test function to send a notification specifically to the Brasil channel
    """
    print("Testing notification to Brasil channel...")

    # Get trending data from last 4 hours
    df = get_last_4_hours_trending_data()

    if df.empty:
        print("No trending data found for the last 4 hours")
        # Send a test message anyway
        test_message = "🧪 *Test Message*\nNo trending data available for the last 4 hours."
        try:
            success = send_slack_message(brasil_channel_id, test_message)
            if success:
                print("✅ Test message sent to Brasil channel")
                return True
            else:
                print("❌ Failed to send test message to Brasil channel")
                return False
        except Exception as e:
            print(f"❌ Error sending test message: {e}")
            return False

    # Generate summary and CSV for Brasil (BR)
    summary = get_country_data_summary(df, 'BR')
    csv_content = generate_csv_from_dataframe(df, 'BR')

    # Create the complete message
    message = f"🧪 *Test Notification for Brasil*\n\n{summary}\n\n📄 *CSV Data:*\n```\n{csv_content}\n```"

    # Send to Brasil channel
    try:
        success = send_slack_message(brasil_channel_id, message)
        if success:
            print("✅ Successfully sent test notification to Brasil channel")
            return True
        else:
            print("❌ Failed to send test notification to Brasil channel")
            return False
    except Exception as e:
        print(f"❌ Error sending test notification to Brasil: {e}")
        return False

def test_with_sample_data():
    """
    Test function with sample data to demonstrate the CSV functionality
    """
    print("Testing with sample data...")

    # Create sample data
    sample_data = {
        'id': [1, 2, 3, 4],
        'country_code': ['BR', 'BR', 'CL', 'BR'],
        'keyword': ['futebol', 'carnaval', 'chile trends', 'brasil news'],
        'title': ['Football News', 'Carnival 2024', 'Chile Trends', 'Brazil News'],
        'time': ['2024-01-01', '2024-01-01', '2024-01-01', '2024-01-01'],
        'search_volume': [1000, 800, 600, 900],
        'started_date': ['2024-01-01', '2024-01-01', '2024-01-01', '2024-01-01'],
        'url': ['http://example1.com', 'http://example2.com', 'http://example3.com', 'http://example4.com'],
        'timestamp': [datetime.now(), datetime.now(), datetime.now(), datetime.now()],
        'hours': [1, 2, 1, 3],
        'category_param': ['sports', 'culture', 'general', 'news'],
        'status': ['active', 'active', 'active', 'active']
    }

    df = pd.DataFrame(sample_data)

    # Test CSV generation for Brasil
    csv_content = generate_csv_from_dataframe(df, 'BR')
    print("Sample CSV for Brasil:")
    print(csv_content[:200] + "..." if len(csv_content) > 200 else csv_content)

    # Test summary generation
    summary = get_country_data_summary(df, 'BR')
    print("\nSample Summary for Brasil:")
    print(summary)

    # Test sending to Brasil channel with sample data
    message = f"🧪 *Sample Data Test for Brasil*\n\n{summary}\n\n📄 *CSV Data:*\n```\n{csv_content}\n```"

    try:
        success = send_slack_message(brasil_channel_id, message)
        if success:
            print("✅ Successfully sent sample data to Brasil channel")
            return True
        else:
            print("❌ Failed to send sample data to Brasil channel")
            return False
    except Exception as e:
        print(f"❌ Error sending sample data: {e}")
        return False

if __name__ == "__main__":
    # Test the implementation
    print("=" * 50)
    print("Google Trends Slack Notifications Test")
    print("=" * 50)

    # Test Brasil channel specifically
    print("\n1. Testing Brasil channel notification...")
    send_test_notification_to_brasil()

    print("\n" + "=" * 50)
    print("2. Testing all country notifications...")
    send_trending_notifications()

    print("\n" + "=" * 50)
    print("3. Testing with sample data...")
    test_with_sample_data()

    print("\n" + "=" * 50)
    print("Test completed!")
    print("=" * 50)

