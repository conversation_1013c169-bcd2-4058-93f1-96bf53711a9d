the purpose of this task is to use send_slack_message function to send a message to appropriate slack channels based on the country

it needs to get data from topstories database table gt_trending_data

get last 4 hours of data from gt_trending_data table (there is a column called timestamp) and based on the country column, send a message to the appropriate slack channel
you have slack channels in slack_channels.py file

you need to send the full csv containing that data

let's also test it on the brasil channel