from django.shortcuts import render, redirect
from django.http import HttpResponse, HttpResponseRedirect
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.core.files.storage import FileSystemStorage
import os
import mysql.connector
from dotenv import load_dotenv
import pandas as pd
import csv
import math
from datetime import datetime
from urllib.parse import urlencode
from alerts.models import Country, Market

# Load environment variables from .env file
load_dotenv()

def get_database_connection():
    """Get a connection to the database"""
    return mysql.connector.connect(
        host=os.getenv("DATABASE_GT_HOST", "localhost"),
        user=os.getenv("DATABASE_GT_USER", "root"),
        password=os.getenv("DATABASE_GT_PASSWORD"),
        database=os.getenv("DATABASE_GT_NAME", "topstories")
    )

def trending_data_view(request):
    """View for displaying and managing Google Trends trending data"""
    conn = get_database_connection()
    cursor = conn.cursor()
    user = request.user

    # Get user information
    full_username = user.full_name if hasattr(user, 'full_name') else user.username

    try:
        # Get available countries
        cursor.execute("SELECT country_code, country_name FROM gt_countries_regions ORDER BY country_name")
        countries = cursor.fetchall()

        # Get available categories from the ALLOWED_CATEGORIES constant
        cursor.execute("SELECT DISTINCT category_param FROM gt_trending_data WHERE category_param IS NOT NULL")
        categories = [row[0] for row in cursor.fetchall() if row[0]]

        # Initial SQL query to get all records
        sql_query = """
            SELECT id, country_code, keyword, title, time, search_volume,
                   started_date, url, timestamp, hours, category_param, status, active
            FROM gt_trending_data
            WHERE 1=1
        """
        query_params = []

        # Apply filters from GET parameters
        country = request.GET.get('country', '')
        category = request.GET.get('category', '')
        hours = request.GET.get('hours', '')
        status = request.GET.get('status', '')
        search = request.GET.get('search', '')

        if country:
            sql_query += " AND country_code = %s"
            query_params.append(country)

        if category:
            sql_query += " AND category_param = %s"
            query_params.append(category)

        if hours:
            sql_query += " AND hours = %s"
            query_params.append(int(hours))

        if status:
            sql_query += " AND status = %s"
            query_params.append(status)

        if search:
            sql_query += " AND (title LIKE %s OR keyword LIKE %s)"
            query_params.extend([f"%{search}%", f"%{search}%"])

        # Add order by timestamp desc to get the most recent data first
        sql_query += " ORDER BY timestamp DESC"

        # Check if this is an export request
        if request.GET.get('export') == 'csv':
            # Execute the query with the accumulated filters to get all matching records
            cursor.execute(sql_query, query_params)
            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            # Create CSV response
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="trending_data.csv"'
            
            # Write CSV data
            writer = csv.writer(response)
            writer.writerow(columns)  # Write header
            writer.writerows(rows)    # Write all rows
            
            return response
        
        # For regular page view, execute the query and paginate results
        cursor.execute(sql_query, query_params)
        rows = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]

        # Pagination
        paginator = Paginator(rows, 20)  # 20 items per page
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)

        # Create query string for pagination links
        filters = {
            'country': country,
            'category': category,
            'hours': hours,
            'status': status,
            'search': search,
        }
        query_string = urlencode({k: v for k, v in filters.items() if v})

        # Handle POST requests (import CSV, add/edit/delete records)
        if request.method == 'POST':
            if 'csv_file' in request.FILES:
                csv_file = request.FILES['csv_file']
                fs = FileSystemStorage('media')
                filename = fs.save(csv_file.name, csv_file)
                file_path = fs.path(filename)

                try:
                    # Process CSV file
                    with open(file_path, newline='', encoding='utf-8') as csvfile:
                        reader = csv.DictReader(csvfile)
                        required_fields = {'country_code', 'title'}
                        fieldnames = reader.fieldnames
                        missing_fields = required_fields - set(fieldnames)

                        if missing_fields:
                            return HttpResponse(f"Error: Missing required fields: {', '.join(missing_fields)}", status=400)

                        # Prepare data for insertion
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        values = []

                        for row in reader:
                            # Set default values for missing fields
                            if 'timestamp' not in row or not row['timestamp']:
                                row['timestamp'] = timestamp

                            # Prepare SQL parameters
                            values.append((
                                row.get('country_code', ''),
                                row.get('category', ''),
                                row.get('title', ''),
                                row.get('time', ''),
                                row.get('search_volume', ''),
                                row.get('started_date', ''),
                                row.get('timestamp', timestamp),
                                int(row.get('hours', 24)) if row.get('hours') else None,
                                row.get('category_param', ''),
                                row.get('status', ''),
                                1  # active by default
                            ))

                        # Insert data into database
                        insert_sql = """
                        INSERT INTO gt_trending_data
                        (country_code, category, title, time, search_volume, started_date,
                         timestamp, hours, category_param, status, active)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.executemany(insert_sql, values)
                        conn.commit()

                    # Clean up
                    fs.delete(filename)
                    return HttpResponseRedirect('?page=1')

                except Exception as e:
                    print(f"Error processing CSV file: {e}")
                    return HttpResponse(f"Error processing CSV file: {e}", status=500)
            else:
                # Handle form submissions for add/edit/delete
                form_data = request.POST.dict()
                action = form_data.pop('action', None)

                if action == 'add':
                    # Add new record
                    new_row_data = []
                    columns_without_id = [col for col in columns if col != 'id']

                    for column in columns_without_id:
                        new_row_data.append(form_data.get(f'new_{column}', ''))

                    try:
                        placeholders = ', '.join(['%s'] * len(columns_without_id))
                        sql = f"INSERT INTO gt_trending_data ({', '.join(columns_without_id)}) VALUES ({placeholders})"
                        cursor.execute(sql, new_row_data)
                        conn.commit()
                        return HttpResponseRedirect(f'?page=1&{query_string}')
                    except Exception as e:
                        print(f"Error adding new row: {e}")
                        return HttpResponse(f"Error adding new row: {e}", status=500)
                elif action == 'delete':
                    # Delete record
                    row_id = form_data.get('id')
                    try:
                        cursor.execute("DELETE FROM gt_trending_data WHERE id = %s", [row_id])
                        conn.commit()
                        return HttpResponseRedirect(f'?page={page_number}&{query_string}')
                    except Exception as e:
                        print(f"Error deleting row: {e}")
                        return HttpResponse(f"Error deleting row: {e}", status=500)
                elif action == 'edit':
                    # Edit record
                    row_id = form_data.get('id')

                    # Get original data
                    cursor.execute("SELECT * FROM gt_trending_data WHERE id = %s", [row_id])
                    original_data = cursor.fetchone()

                    if not original_data:
                        return HttpResponse("Error: Record not found", status=404)

                    # Prepare update data
                    set_clauses = []
                    params = []

                    for key, value in form_data.items():
                        if key not in ['action', 'id', 'csrfmiddlewaretoken']:
                            column_name = key
                            original_index = columns.index(column_name)
                            original_value = original_data[original_index]

                            if str(original_value) != value:
                                set_clauses.append(f"{column_name} = %s")
                                params.append(value)

                    if set_clauses:
                        sql = f"UPDATE gt_trending_data SET {', '.join(set_clauses)} WHERE id = %s"
                        params.append(row_id)

                        try:
                            cursor.execute(sql, params)
                            conn.commit()
                            return HttpResponseRedirect(f'?page={page_number}&{query_string}')
                        except Exception as e:
                            print(f"Error updating row: {e}")
                            return HttpResponse(f"Error updating row: {e}", status=500)

    except Exception as e:
        print(f"Error in trending_data_view: {e}")
        return HttpResponse(f"Error: {e}", status=500)
    finally:
        cursor.close()
        conn.close()

    # Prepare context for template
    context = {
        'page_obj': page_obj,
        'columns': columns,
        'rows': rows,
        'countries': countries,
        'categories': categories,
        'hours_options': [4, 24, 48, 168],
        'status_options': ['active', 'recently'],
        'country_filter': country,
        'category_filter': category,
        'hours_filter': hours,
        'status_filter': status,
        'search_query': search,
        'query_string': query_string,
        'user_info': {'full_name': full_username},
    }

    return render(request, 'google_trends/trending_data.html', context)

def trending_config_view(request):
    """View for managing Google Trends trending configurations"""
    conn = get_database_connection()
    cursor = conn.cursor()

    try:
        # Get available countries
        cursor.execute("SELECT country_code, country_name FROM gt_countries_regions ORDER BY country_name")
        countries = cursor.fetchall()

        # Get configurations
        cursor.execute("""
            SELECT c.id, c.country_code, r.country_name, c.category, c.hours, c.status, c.active,
                   c.created_at, c.updated_at
            FROM gt_trending_config c
            JOIN gt_countries_regions r ON c.country_code = r.country_code
            ORDER BY c.updated_at DESC
        """)
        configs = cursor.fetchall()
        config_columns = [desc[0] for desc in cursor.description]

        # Handle form submission
        if request.method == 'POST':
            form_data = request.POST.dict()
            action = form_data.get('action')

            if action == 'add':
                # Add new configuration
                country_code = form_data.get('country_code')
                category = form_data.get('category') or None
                hours = int(form_data.get('hours', 24))
                status = form_data.get('status') or None

                try:
                    cursor.execute(
                        """INSERT INTO gt_trending_config
                           (country_code, category, hours, status, active)
                           VALUES (%s, %s, %s, %s, 1)""",
                        (country_code, category, hours, status)
                    )
                    conn.commit()
                    return HttpResponseRedirect('?added=1')
                except Exception as e:
                    print(f"Error adding configuration: {e}")
                    return HttpResponse(f"Error adding configuration: {e}", status=500)

            elif action == 'toggle':
                # Toggle active status
                config_id = form_data.get('id')
                active = int(form_data.get('active', 0))
                new_active = 1 if active == 0 else 0

                try:
                    cursor.execute(
                        "UPDATE gt_trending_config SET active = %s WHERE id = %s",
                        (new_active, config_id)
                    )
                    conn.commit()
                    return HttpResponseRedirect('?toggled=1')
                except Exception as e:
                    print(f"Error toggling configuration: {e}")
                    return HttpResponse(f"Error toggling configuration: {e}", status=500)

            elif action == 'delete':
                # Delete configuration
                config_id = form_data.get('id')

                try:
                    cursor.execute("DELETE FROM gt_trending_config WHERE id = %s", [config_id])
                    conn.commit()
                    return HttpResponseRedirect('?deleted=1')
                except Exception as e:
                    print(f"Error deleting configuration: {e}")
                    return HttpResponse(f"Error deleting configuration: {e}", status=500)

    except Exception as e:
        print(f"Error in trending_config_view: {e}")
        return HttpResponse(f"Error: {e}", status=500)
    finally:
        cursor.close()
        conn.close()

    # Prepare context for template
    context = {
        'countries': countries,
        'configs': configs,
        'config_columns': config_columns,
        'categories': ['all', 'entertainment', 'business', 'health', 'sci_tech', 'sports', 'top'],
        'hours_options': [4, 24, 48, 168],
        'status_options': ['active', 'recently'],
    }

    return render(request, 'google_trends/trending_config.html', context)
