class GoogleTrendsRouter:
    """
    A router to control all database operations on models in the Google Trends application.
    """
    app_label = 'google_trends'

    def db_for_read(self, model, **hints):
        """
        Attempts to read Google Trends models go to google_trends_db.
        """
        if model._meta.app_label == self.app_label:
            return 'google_trends_db'
        return None

    def db_for_write(self, model, **hints):
        """
        Attempts to write Google Trends models go to google_trends_db.
        """
        if model._meta.app_label == self.app_label:
            return 'google_trends_db'
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if a model in the Google Trends app is involved.
        """
        if obj1._meta.app_label == self.app_label or \
           obj2._meta.app_label == self.app_label:
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure the Google Trends app only appears in the 'google_trends_db' database.
        """
        if app_label == self.app_label:
            return db == 'google_trends_db'
        return None
