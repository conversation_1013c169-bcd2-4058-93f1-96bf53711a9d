from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import os
import mysql.connector
from datetime import datetime
from dotenv import load_dotenv
import argparse
from unidecode import unidecode
import re

# Load environment variables from .env file
load_dotenv()

# Define allowed parameters
ALLOWED_HOURS = [4, 24, 48, 168]
ALLOWED_CATEGORIES = {
    "all": 0,
    "entertainment": 3,
    "business": 12,
    "health": 45,
    "sci_tech": 8,
    "sports": 17,
    "top": 22
}
ALLOWED_STATUSES = ["active", "recently"]

# Function to clean text
def clean_text(text):
    if not text:
        return ""
    # Convert to ASCII while intelligently handling accents and special characters
    text = unidecode(text)
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text)
    # Strip any leading/trailing whitespace
    return text.strip()

# Function to get database connection
def get_database_connection():
    return mysql.connector.connect(
        host=os.getenv("DATABASE_GT_HOST", "localhost"),
        user=os.getenv("DATABASE_GT_USER", "root"),
        password=os.getenv("DATABASE_GT_PASSWORD"),
        database="topstories"
    )

# Function to get all country codes from database
def get_country_codes():
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT country_code FROM gt_countries_regions")
        result = cursor.fetchall()
        cursor.close()
        conn.close()
        # Return a list of country codes
        return [code[0] for code in result]
    except Exception as e:
        print(f"Error fetching country codes: {e}")
        return []

# Function to scrape trending data
def scrape_trending_data(country_code, hours=None, category=None, status=None):
    # Validate country code
    if not country_code:
        raise ValueError("Country code is required")

    # Validate hours parameter
    if hours is not None and hours not in ALLOWED_HOURS:
        raise ValueError(f"Hours must be one of {ALLOWED_HOURS}")

    # Validate category parameter
    if category is not None and category not in ALLOWED_CATEGORIES:
        raise ValueError(f"Category must be one of {list(ALLOWED_CATEGORIES.keys())}")

    # Validate status parameter
    if status is not None and status not in ALLOWED_STATUSES:
        raise ValueError(f"Status must be one of {ALLOWED_STATUSES}")

    # Build URL with parameters
    base_url = "https://trends.google.com/trending"
    url = f"{base_url}?geo={country_code}"

    if hours is not None:
        url += f"&hours={hours}"

    if category is not None:
        url += f"&category={ALLOWED_CATEGORIES[category]}"

    if status is not None:
        url += f"&status={status}"

    print(f"Scraping URL: {url}")

    # Create a new instance of the Chrome driver
    options = webdriver.ChromeOptions()
    # options.add_argument('headless')  # Uncomment for headless mode
    options.add_argument('--start-fullscreen')
    options.add_argument("disable-gpu")
    options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3")
    options.add_argument('headless')
    # options.add_argument('window-size=1920x1080')


    # Install the chrome driver if not already installed
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    try:
        # Go to the Google Trends page
        driver.get(url)

        # Find all elements with class mZ3RIc
        # Wait for the elements with class mZ3RIc to be present
        elements = WebDriverWait(driver, 10).until(
            EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.mZ3RIc"))
        )
        volume_elements = driver.find_elements(By.CSS_SELECTOR, "div.lqv0Cb")
        date_elements = driver.find_elements(By.CSS_SELECTOR, "div.vdw3Ld")

        len_elements = len(elements)
        print(len_elements)
        i = 0

        # List to store all trending data
        trending_data = []
        

        # Timestamp for the scrape
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Iterate through each element
        for element in elements:
            try:
                # Extract category name before clicking
                category_name = clean_text(element.text)

                # Try to extract search volume and started data before clicking
                search_volume = ""
                started_date = ""

                # Look for search volume data (usually in format like "100K+ searches")
                try:
                    # First, check if there's a child element with search volume info
                    # volume_element = driver.find_elements(By.CSS_SELECTOR, "div.lqv0Cb")
                    volume_element = volume_elements[i]
                    if volume_element:
                        search_volume = clean_text(volume_element.text)

                    # If not found, try alternative selectors
                    if not search_volume:
                        volume_element = element.find_elements(By.CSS_SELECTOR, "div.YVIcad")
                        if volume_element and len(volume_element) > 0:
                            search_volume = clean_text(volume_element[0].text)
                except Exception as e:
                    print(f"Error extracting search volume: {e}")

                # Look for started date (when the trend started)
                try:
                    # Try to find the started date element
                    date_element = date_elements[i]
                    if date_element:
                        started_date = clean_text(date_element.text)

                    # If not found, try alternative selectors
                    if not started_date:
                        date_element = element.find_elements(By.CSS_SELECTOR, "div.YVIcad + div")
                        if date_element and len(date_element) > 0:
                            started_date = clean_text(date_element[0].text)
                except Exception as e:
                    print(f"Error extracting started date: {e}")

                # Click the element
                element.click()

                # Wait for the div with class EMz5P to be present and visible
                WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "div.EMz5P"))
                )

                # Wait for the div with class k44Spe to be present and visible
                WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "div.k44Spe"))
                )

                # Wait for the element with jsaction rcuQ6b:npT2md;z4IIob:iqy7Fe to be present and visible
                WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "div[jsaction='rcuQ6b:npT2md;z4IIob:iqy7Fe']"))
                )

                # Wait for all elements with jsaction click:vx9mmb;contextmenu:rbJKIe to be present
                elements_vx9mmb = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div[jsaction='click:vx9mmb;contextmenu:rbJKIe']"))
                )

                # Extract details for each trend
                for trend_element in elements_vx9mmb:
                    title_element = trend_element.find_element(By.CSS_SELECTOR, "div.QbLC8c")
                    time_element = trend_element.find_element(By.CSS_SELECTOR, "div.pojp0c")
                    url_element = trend_element.find_element(By.CSS_SELECTOR, "a.xZCHj")
                    url = url_element.get_attribute("href") if url_element else ""

                    title = clean_text(title_element.text if title_element else "")
                    time_info = clean_text(time_element.text if time_element else "")

                    trend_info = {
                        "country_code": country_code,
                        "keyword": category_name,
                        "title": title,
                        "time": time_info,
                        "search_volume": search_volume,
                        "started_date": started_date,
                        "url": url, 
                        "timestamp": timestamp,
                        "hours": hours,
                        "category_param": category,
                        "status": status
                    }
                    trending_data.append(trend_info)
        
                i += 1
                if i >= len_elements:
                    break
            except Exception as e:
                print(f"Error processing element {element.text}: {e}")

        # Data will only be saved to the database, not to files
        print(f"Found {len(trending_data)} trending items for {country_code}")
        # print(trending_data)
        return trending_data

    except Exception as e:
        print(f"Error scraping data: {e}")
        return []
    finally:
        # Close the driver
        driver.quit()

# Main function to run the script
def main():
    parser = argparse.ArgumentParser(description='Scrape Google Trends data')
    parser.add_argument('country_code', help='Country code to scrape (e.g., RS, US)')
    parser.add_argument('--hours', type=int, choices=ALLOWED_HOURS, help='Time period in hours')
    parser.add_argument('--category', choices=list(ALLOWED_CATEGORIES.keys()), help='Category to filter by')
    parser.add_argument('--status', choices=ALLOWED_STATUSES, help='Status to filter by')
    parser.add_argument('--list-countries', action='store_true', help='List all available country codes')
    parser.add_argument('--save-to-db', action='store_true', help='Save results to database')

    args = parser.parse_args()

    if args.list_countries:
        country_codes = get_country_codes()
        print("Available country codes:")
        for code in country_codes:
            print(code)
        return

    # Scrape data with provided parameters
    trending_data = scrape_trending_data(
        country_code=args.country_code,
        hours=args.hours,
        category=args.category,
        status=args.status
    )

    print(f"Successfully scraped {len(trending_data)} trending items for {args.country_code}")
    print("Data will be saved to the database when run through the trending_data_manager")

if __name__ == "__main__":
    main()
