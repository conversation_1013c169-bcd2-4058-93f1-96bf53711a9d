#!/usr/bin/env python3
"""
Google Trends Scraper

This script runs the Google Trends scraper to collect trending data based on configurations
stored in the database. It's designed to be run directly or as a cron job.

Usage:
    python run_trending_scraper.py                  # Run the scraper with all active configurations
    python run_trending_scraper.py run              # Same as above
    python run_trending_scraper.py add US --category entertainment --hours 24  # Add a new configuration
    python run_trending_scraper.py import data.csv  # Import data from a CSV file
"""

import os
import sys
import argparse
import logging
import traceback
from datetime import datetime
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "opat.settings")
django.setup()

from trending_data_manager import run_trending_scraper, add_trending_config, import_trending_data_from_csv

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, 'google_trends_scraper.log')
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Add console handler to show logs in console as well
console = logging.StreamHandler()
console.setLevel(logging.INFO)
console.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logging.getLogger('').addHandler(console)

def log_message(message, level='info'):
    """Log a message to both console and log file"""
    if level == 'info':
        logging.info(message)
    elif level == 'error':
        logging.error(message)
    elif level == 'warning':
        logging.warning(message)

def run_scraper():
    """Run the trending scraper with all active configurations"""
    try:
        log_message("Starting Google Trends scraper...")
        start_time = datetime.now()
        run_trending_scraper()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        log_message(f"Google Trends scraper completed successfully in {duration:.2f} seconds.")
        return True
    except Exception as e:
        error_msg = f"Error running Google Trends scraper: {str(e)}"
        log_message(error_msg, 'error')
        log_message(traceback.format_exc(), 'error')
        return False

def main():
    # Check if command-line arguments are provided
    if len(sys.argv) > 1:
        parser = argparse.ArgumentParser(description='Google Trends Trending Data Scraper')
        subparsers = parser.add_subparsers(dest='command', help='Command to run')

        # Run scraper command
        run_parser = subparsers.add_parser('run', help='Run the trending scraper')

        # Add config command
        add_parser = subparsers.add_parser('add', help='Add a new trending configuration')
        add_parser.add_argument('country_code', help='Country code (e.g., US, GB)')
        add_parser.add_argument('--category', help='Category to filter by')
        add_parser.add_argument('--hours', type=int, help='Time period in hours')
        add_parser.add_argument('--status', help='Status to filter by')

        # Import command
        import_parser = subparsers.add_parser('import', help='Import trending data from CSV')
        import_parser.add_argument('csv_file', help='Path to CSV file')

        args = parser.parse_args()

        if args.command == 'run':
            run_scraper()
        elif args.command == 'add':
            log_message(f"Adding new trending configuration...")
            config_id = add_trending_config(
                country_code=args.country_code,
                category=args.category,
                hours=args.hours,
                status=args.status
            )
            if config_id:
                log_message(f"Configuration added successfully with ID: {config_id}")
            else:
                log_message(f"Failed to add configuration.", 'error')
        elif args.command == 'import':
            log_message(f"Importing trending data from CSV: {args.csv_file}")
            if import_trending_data_from_csv(args.csv_file):
                log_message(f"CSV import completed successfully.")
            else:
                log_message(f"CSV import failed.", 'error')
        else:
            parser.print_help()
    else:
        # If no arguments provided, just run the scraper (for cron job)
        run_scraper()

if __name__ == "__main__":
    main()
