#!/usr/bin/env python3
import os
import mysql.connector
import pandas as pd
from datetime import datetime
import csv
import json
from dotenv import load_dotenv
from trendingnow_enhanced import scrape_trending_data, ALLOWED_CATEGORIES, ALLOWED_HOURS, ALLOWED_STATUSES

# Load environment variables from .env file
load_dotenv()

def get_database_connection():
    """Get a connection to the database"""
    return mysql.connector.connect(
        host=os.getenv("DATABASE_GT_HOST", "localhost"),
        user=os.getenv("DATABASE_GT_USER", "root"),
        password=os.getenv("DATABASE_GT_PASSWORD"),
        database=os.getenv("DATABASE_GT_NAME", "topstories")
    )

def get_trending_configs():
    """Get all active trending configurations from the database"""
    conn = get_database_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        cursor.execute(
            "SELECT id, country_code, category, hours, status FROM gt_trending_config WHERE active = 1"
        )
        configs = cursor.fetchall()
        return configs
    except Exception as e:
        print(f"Error fetching trending configurations: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def save_trending_data(trending_data):
    """Save trending data to the database"""
    if not trending_data:
        print("No trending data to save")
        return

    conn = get_database_connection()
    cursor = conn.cursor()

    try:
        # Prepare SQL statement
        sql = """
        INSERT INTO gt_trending_data
        (country_code, keyword, title, time, search_volume, started_date, url, timestamp, hours, category_param, status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        # Prepare data for batch insert
        values = []
        for item in trending_data:
            values.append((
                item['country_code'],
                item['keyword'],
                item['title'],
                item['time'],
                item['search_volume'],
                item['started_date'],
                item['url'],
                item['timestamp'],
                item['hours'],
                item['category_param'],
                item['status'],
            ))

        # Execute batch insert
        cursor.executemany(sql, values)
        conn.commit()

        print(f"Successfully saved {len(values)} trending data records")

    except Exception as e:
        print(f"Error saving trending data: {e}")
    finally:
        cursor.close()
        conn.close()

def run_trending_scraper():
    """Run the trending scraper for all active configurations"""
    configs = get_trending_configs()

    if not configs:
        print("No active trending configurations found")
        return

    all_trending_data = []
    successful_configs = 0
    failed_configs = 0

    print(f"Found {len(configs)} active configurations to process")

    for config in configs:
        config_info = f"country={config['country_code']}, category={config['category']}, hours={config['hours']}, status={config['status']}"
        print(f"Processing config: {config_info}")
        try:
            # Run the scraper
            trending_data = scrape_trending_data(
                country_code=config['country_code'],
                hours=config['hours'],
                category=config['category'],
                status=config['status']
            )

            if trending_data:
                all_trending_data.extend(trending_data)
                print(f"Successfully scraped {len(trending_data)} items for {config['country_code']}")
                successful_configs += 1
            else:
                print(f"No data scraped for {config['country_code']} (this may be normal)")
                successful_configs += 1  # Still count as successful even if no data

        except Exception as e:
            print(f"Error scraping trending data for {config_info}: {str(e)}")
            failed_configs += 1

    # Save all trending data to the database
    if all_trending_data:
        save_trending_data(all_trending_data)
        print(f"Total trending data saved: {len(all_trending_data)} items from {successful_configs} configurations")
    else:
        print("No trending data was collected")

    # Print summary
    print(f"Scraping summary: {successful_configs} successful, {failed_configs} failed, {len(all_trending_data)} total items collected")

def add_trending_config(country_code, category=None, hours=24, status=None):
    """Add a new trending configuration"""
    # Validate parameters
    if category is not None and category not in ALLOWED_CATEGORIES:
        raise ValueError(f"Category must be one of {list(ALLOWED_CATEGORIES.keys())}")

    if hours is not None and hours not in ALLOWED_HOURS:
        raise ValueError(f"Hours must be one of {ALLOWED_HOURS}")

    if status is not None and status not in ALLOWED_STATUSES:
        raise ValueError(f"Status must be one of {ALLOWED_STATUSES}")

    conn = get_database_connection()
    cursor = conn.cursor()

    try:
        # Check if configuration already exists
        cursor.execute(
            "SELECT id FROM gt_trending_config WHERE country_code = %s AND category = %s AND hours = %s AND status = %s",
            (country_code, category, hours, status)
        )
        existing = cursor.fetchone()

        if existing:
            # Update existing configuration
            cursor.execute(
                "UPDATE gt_trending_config SET active = 1, updated_at = NOW() WHERE id = %s",
                (existing[0],)
            )
            conn.commit()
            print(f"Updated existing configuration with ID {existing[0]}")
            return existing[0]
        else:
            # Insert new configuration
            cursor.execute(
                "INSERT INTO gt_trending_config (country_code, category, hours, status) VALUES (%s, %s, %s, %s)",
                (country_code, category, hours, status)
            )
            conn.commit()
            config_id = cursor.lastrowid
            print(f"Added new configuration with ID {config_id}")
            return config_id

    except Exception as e:
        print(f"Error adding trending configuration: {e}")
        return None
    finally:
        cursor.close()
        conn.close()

def import_trending_data_from_csv(csv_file_path):
    """Import trending data from a CSV file"""
    try:
        # Read CSV file
        df = pd.read_csv(csv_file_path)

        # Convert DataFrame to list of dictionaries
        trending_data = df.to_dict('records')

        # Add timestamp if missing
        for item in trending_data:
            if 'timestamp' not in item or not item['timestamp']:
                item['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Save to database
        save_trending_data(trending_data)

        print(f"Successfully imported {len(trending_data)} records from {csv_file_path}")
        return True
    except Exception as e:
        print(f"Error importing trending data from CSV: {e}")
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Google Trends Trending Data Manager')
    subparsers = parser.add_subparsers(dest='command', help='Command to run')

    # Run scraper command
    run_parser = subparsers.add_parser('run', help='Run the trending scraper')

    # Add config command
    add_parser = subparsers.add_parser('add', help='Add a new trending configuration')
    add_parser.add_argument('country_code', help='Country code (e.g., US, GB)')
    add_parser.add_argument('--category', choices=list(ALLOWED_CATEGORIES.keys()), help='Category to filter by')
    add_parser.add_argument('--hours', type=int, choices=ALLOWED_HOURS, default=24, help='Time period in hours')
    add_parser.add_argument('--status', choices=ALLOWED_STATUSES, help='Status to filter by')

    # Import command
    import_parser = subparsers.add_parser('import', help='Import trending data from CSV')
    import_parser.add_argument('csv_file', help='Path to CSV file')

    args = parser.parse_args()

    if args.command == 'run':
        run_trending_scraper()
    elif args.command == 'add':
        add_trending_config(
            country_code=args.country_code,
            category=args.category,
            hours=args.hours,
            status=args.status
        )
    elif args.command == 'import':
        import_trending_data_from_csv(args.csv_file)
    else:
        parser.print_help()
