from django.core.management.base import BaseCommand
from django.db import IntegrityError
from autorevap.models import SiteSource, Author, Article
from autorevap.autorevap_utils.api_parser import (
    get_json_from_api, get_items, parse_metadata, decode_body, extract_author_name
)
from bs4 import BeautifulSoup
import zlib


class Command(BaseCommand):
    help = "Fetches articles from the FSN API and stores them in the database."

    def handle(self, *args, **options):
        sources = SiteSource.objects.filter(is_active=True)
        for source in sources:
            self.import_articles_for_source(source)

    def import_articles_for_source(self, source):
        api_url = source.api_url
        self.stdout.write(f"Fetching data from {api_url}")

        try:
            json_data = get_json_from_api(api_url)
            items = get_items(json_data)
            metadata = parse_metadata(items)
        except Exception as e:
            self.stderr.write(f"❌ Error fetching or parsing data for {source.shortcode}: {e}")
            return

        imported_count = 0
        for data in metadata.values():
            created = self.create_article_from_data(data, source)
            if created:
                imported_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"✅ Imported {imported_count} new articles for {source.shortcode} (from {len(metadata)} total)"
            )
        )

    def create_article_from_data(self, data, source):
        try:
            html_body = decode_body(data.get("body", ""))
            compressed_body = zlib.compress(html_body.encode("utf-8"))
            soup = BeautifulSoup(html_body, "html.parser")

            # Author Handling
            author_name = extract_author_name(data.get("author"))
            author_obj, _ = Author.objects.get_or_create(name=author_name)

            # Article Stats
            links = soup.find_all("a", href=True)
            tema_links = [a for a in links if "/tema/" in a["href"]]
            h2_tags = soup.find_all("h2")
            strong_tags = soup.find_all("strong")
            related = soup.find_all("div", class_="wp_fsn_relatedlinks")
            lazy_imgs = soup.find_all("img", loading="lazy")
            all_imgs = soup.find_all("img")
            paragraphs = soup.find_all("p")
            para_lengths = [len(p.get_text()) for p in paragraphs]
            avg_paragraph_length = sum(para_lengths) / len(para_lengths) if para_lengths else 0

            # Try to create or get existing
            _, created = Article.objects.get_or_create(
                external_id=data["id"],
                defaults={
                    "source": source,
                    "author": author_obj,
                    "url": data["url"],
                    "title": data["title"],
                    "section": data.get("section", ""),
                    "section_slug": data.get("section_slug", ""),
                    "link": data.get("link", ""),
                    "published_at": data.get("published_at"),
                    "modified_at": data.get("modified_at"),
                    "body_compressed": compressed_body,
                    "created_by": data.get("created_by", ""),
                    "canonical": data.get("canonical", ""),
                    "seo_title": data.get("seo_title", ""),
                    "seo_description": data.get("seo_description", ""),
                    "site_code": data.get("siteCode", ""),
                    "tags": data.get("tags", []),
                    "images": data.get("images", []),
                    "link_count": len(links),
                    "tema_link_count": len(tema_links),
                    "h2_count": len(h2_tags),
                    "strong_tag_count": len(strong_tags),
                    "related_links_count": len(related),
                    "lazy_img_count": len(lazy_imgs),
                    "total_img_count": len(all_imgs),
                    "paragraph_count": len(paragraphs),
                    "avg_paragraph_length": avg_paragraph_length
                }
            )

            if not created:
                self.stdout.write(self.style.WARNING(f"⚠️ Article already exists: {data.get('id')}"))

            return created

        except IntegrityError:
            self.stderr.write(f"❌ IntegrityError while processing article {data.get('id')}, skipping.")
            return False