from django.core.management.base import BaseCommand
from autorevap.models import SiteSource, RSSArticle, Author
from autorevap.autorevap_utils.rss_parser import parse_rss_feed, decode_body, extract_author_name
from bs4 import BeautifulSoup
import dateutil.parser


class Command(BaseCommand):
    help = "Imports articles from active RSS feeds and stores them in the RSSArticle table."

    def handle(self, *args, **kwargs):
        sources = SiteSource.objects.filter(is_active=True).exclude(rss_feed_url__isnull=True)
        for source in sources:
            self.import_rss_for_source(source)

    def import_rss_for_source(self, source):
        rss_url = source.rss_feed_url
        self.stdout.write(f"Fetching RSS feed for {source.shortcode} → {rss_url}")

        metadata = parse_rss_feed(rss_url)
        if not metadata:
            self.stdout.write(self.style.WARNING(f"⚠️ No articles found for {source.shortcode}"))
            return

        urls = [entry.get("url") for entry in metadata.values() if entry.get("url")]
        existing_urls = set(
            RSSArticle.objects.filter(url__in=urls).values_list("url", flat=True)
        )
        existing_authors = self.fetch_existing_authors()

        articles_to_create = []
        for data in metadata.values():
            url = data.get("url")
            if not url or url in existing_urls:
                continue

            article = self.prepare_article(data, source, existing_authors)
            if article:
                articles_to_create.append(article)

        RSSArticle.objects.bulk_create(articles_to_create, batch_size=500)
        self.stdout.write(
            self.style.SUCCESS(f"✅ Imported {len(articles_to_create)} new RSS articles for {source.shortcode}")
        )

    def fetch_existing_authors(self):
        return {name: author for name, author in Author.objects.all().values_list("name", "id")}

    def prepare_article(self, data, source, existing_authors):
        url = data.get("url")
        html_body = decode_body(data.get("body", ""))
        soup = BeautifulSoup(html_body, "html.parser")

        author_name = extract_author_name(data.get("author"))
        author_obj = self.get_or_create_author(author_name, existing_authors)

        links = soup.find_all("a", href=True)
        h2_tags = soup.find_all("h2")
        paragraphs = soup.find_all("p")

        para_lengths = [len(p.get_text()) for p in paragraphs]
        avg_paragraph_length = sum(para_lengths) / len(para_lengths) if para_lengths else 0

        try:
            published = dateutil.parser.parse(data.get("published_at", ""))
        except Exception:
            published = None

        return RSSArticle(
            source=source,
            author_id=author_obj.id,
            url=url,
            title=data.get("title", "Untitled"),
            category=data.get("category", "Uncategorized"),
            published_at=published,
            link_count=len(links),
            h2_count=len(h2_tags),
            paragraph_count=len(paragraphs),
            avg_paragraph_length=avg_paragraph_length
        )

    def get_or_create_author(self, name, existing_authors):
        if name in existing_authors:
            return Author.objects.get(id=existing_authors[name])
        author_obj = Author.objects.create(name=name)
        existing_authors[name] = author_obj.id
        return author_obj
