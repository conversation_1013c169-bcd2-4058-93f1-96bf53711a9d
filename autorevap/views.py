from django.shortcuts import render
from django.http import HttpResponse
from .models import SiteSource, Article, Author, RSSArticle
from .autorevap_utils.api_parser import generate_csv_response, fig_to_html
from .autorevap_utils.analyze import analyze_authors, audit_articles, render_table_html
from .autorevap_utils.charts import (
    plot_avg_links_heatmap, plot_avg_h2_heatmap, plot_avg_tema_links_heatmap,
    plot_stacked_theme_vs_article_links, plot_strong_tags_bar, plot_related_links_chart,
    plot_lazy_images_chart, plot_paragraph_count_chart, plot_paragraph_length_chart,
    plot_missing_elements_chart, plot_author_activity_over_time,
    plot_author_hourly_distribution, plot_author_weekday_distribution, plot_total_images_chart
)

def dashboard_view(request):
    sources_qs = SiteSource.objects.filter(is_active=True).order_by("site_name")

    if not sources_qs.exists():
        return HttpResponse("No active sites found", status=404)

    selected_site = request.GET.get("site") or sources_qs.first().shortcode
    selected_source = sources_qs.filter(shortcode=selected_site).first()

    if not selected_source:
        return HttpResponse("Selected site not found", status=404)

    export_format = request.GET.get("export")
    export_type = request.GET.get("type")
    source_type = request.GET.get("source", "api")

    if source_type == "rss":
        articles_qs = RSSArticle.objects.filter(source=selected_source)
    else:
        articles_qs = Article.objects.filter(source=selected_source)

    if not articles_qs.exists():
        # Only pass minimal context to render form + friendly message
        context = {
            "websites": {s.site_name: s.shortcode for s in sources_qs},
            "selected_site": selected_site,
            "selected_name": selected_source.site_name,
            "selected_source": source_type,
            "no_data": True,
        }
        return render(request, "fsn/fsn.html", context)

    # Proceed with building metadata and analysis
    metadata = {}
    for article in articles_qs:
        key = article.url if source_type == "rss" else article.external_id
        metadata[key] = {
            "id": getattr(article, "external_id", None),
            "url": article.url,
            "title": article.title,
            "section": getattr(article, "section", getattr(article, "category", "")),
            "section_slug": getattr(article, "section_slug", ""),
            "link": getattr(article, "link", ""),
            "published_at": article.published_at,
            "modified_at": getattr(article, "modified_at", None),
            "body": article.get_body(),
            "created_by": getattr(article, "created_by", ""),
            "canonical": getattr(article, "canonical", ""),
            "seo_title": getattr(article, "seo_title", ""),
            "seo_description": getattr(article, "seo_description", ""),
            "siteCode": getattr(article, "site_code", ""),
            "tags": getattr(article, "tags", []),
            "images": getattr(article, "images", []),
            "author": [{"name": article.author.name if article.author else "Unknown"}],
        }

    author_df, total_fig, avg_fig = analyze_authors(metadata)
    article_df, audit_fig = audit_articles(metadata)

    if export_format == "csv" and export_type in ["author", "article"]:
        df = author_df if export_type == "author" else article_df
        return generate_csv_response(df, filename=f"{export_type}_table.csv")

    context = {
        "websites": {s.site_name: s.shortcode for s in sources_qs},
        "selected_site": selected_site,
        "selected_name": selected_source.site_name,
        "selected_source": source_type,
        "author_table": render_table_html(author_df, table_id="authorTable"),
        "article_table": render_table_html(article_df, table_id="articleTable", drop_cols=["ID"]),
        "link_heatmap": fig_to_html(plot_avg_links_heatmap(metadata)),
        "h2_heatmap": fig_to_html(plot_avg_h2_heatmap(metadata)),
        "tema_heatmap": fig_to_html(plot_avg_tema_links_heatmap(metadata)),
        "stacked_links_chart": fig_to_html(plot_stacked_theme_vs_article_links(metadata)),
        "strong_tags_chart": fig_to_html(plot_strong_tags_bar(metadata)),
        "related_links_chart": fig_to_html(plot_related_links_chart(metadata)),
        "lazy_images_chart": fig_to_html(plot_lazy_images_chart(metadata)),
        "paragraph_count_chart": fig_to_html(plot_paragraph_count_chart(metadata)),
        "paragraph_length_chart": fig_to_html(plot_paragraph_length_chart(metadata)),
        "missing_elements_chart": fig_to_html(plot_missing_elements_chart(metadata)),
        "author_activity_chart": fig_to_html(plot_author_activity_over_time(metadata)),
        "hourly_distribution_chart": fig_to_html(plot_author_hourly_distribution(metadata)),
        "weekday_distribution_chart": fig_to_html(plot_author_weekday_distribution(metadata)),
        "total_content_metrics_chart": fig_to_html(total_fig),
        "per_article_avg_metrics_chart": fig_to_html(avg_fig),
        "audit_chart": fig_to_html(audit_fig),
        "total_images_chart": fig_to_html(plot_total_images_chart(metadata)),
    }

    return render(request, "fsn/fsn.html", context)

