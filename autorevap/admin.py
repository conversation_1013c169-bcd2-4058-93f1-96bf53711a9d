from django.contrib import admin
from .models import SiteSource, AutorevapApplication

# Register your models here.

class ReadOnlyAdminMixin:

    def has_add_permission(self, request):
        return True

    def has_change_permision(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True

    def has_view_permission(self, request, obj=None):
        return True





@admin.register(SiteSource)
class SiteSourceAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('site_name', 'site_url', 'shortcode', 'is_active')
    #list_filter = ('source_type', 'is_active')
    #search_fields = ("site_name", "shortcode")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['site_name'].disabled = True
            form.base_fields['site_url'].disabled = True
            form.base_fields['shortcode'].disabled = True
            form.base_fields['is_active'].disabled = True

        return form

@admin.register(AutorevapApplication)
class AutorevapApplicationAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('app_name', 'app_description', 'app_is_active')

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['app_name'].disabled = True
            form.base_fields['app_description'].disabled = True
            form.base_fields['app_is_active'].disabled = True

        return form
