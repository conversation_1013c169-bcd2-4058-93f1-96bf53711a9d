import requests
from bs4 import BeautifulSoup
import html


def parse_rss_feed(url):
    """
    Parse RSS feed using requests and extract article metadata.
    Returns metadata as a dictionary keyed by article URL.
    """
    try:
        response = requests.get(url)
        response.raise_for_status()
    except Exception as e:
        print(f"❌ Error fetching RSS feed: {e}")
        return {}

    soup = BeautifulSoup(response.text, "xml")
    items = soup.find_all("item")

    if not items:
        print("⚠️ No <item> tags found in RSS feed. Feed may be empty or malformed.")
        return {}

    metadata = {}
    for item in items:
        try:
            link_tag = item.find("link")
            link = link_tag.text.strip() if link_tag else None
            if not link:
                continue 
            title_tag = item.find("title")
            title = title_tag.text.strip() if title_tag else "Untitled"

            pub_date_tag = item.find("pubDate")
            pub_date = pub_date_tag.text.strip() if pub_date_tag else ""

            category_tag = item.find("category")
            category = category_tag.text.strip() if category_tag else "Uncategorized"

            author_tag = item.find("dc:creator")
            author = author_tag.text.strip() if author_tag else "Unknown"

            description_tag = item.find("description")
            description_html = str(description_tag.text.strip() if description_tag else "")

            metadata[link] = {
                "url": link,
                "title": title,
                "published_at": pub_date,
                "category": category,
                "author": [{"name": author}],
                "body": description_html,
            }

        except Exception as e:
            print(f"⚠️ Error parsing item: {e}")
            continue

    return metadata



def decode_body(html_body):
    """
    Decode HTML entities in a string.

    Parameters
    ----------
    html_body : str
        HTML string with entities.

    Returns
    -------
    decoded_body : str
        Cleaned HTML string.
    """
    return html.unescape(html_body)


def extract_author_name(author_field):
    """
    Extract author's name from field.

    Parameters
    ----------
    author_field : list of dict
        Field in API/RSS format.

    Returns
    -------
    str
        Author's name or "Unknown".
    """
    if isinstance(author_field, list) and author_field:
        return author_field[0].get("name", "Unknown")
    return "Unknown"


