from collections import defaultdict
import pandas as pd
from bs4 import BeautifulSoup
from .api_parser import extract_author_name, decode_body
from .plotly_utils import plot_heatmap, plot_barh, plot_stacked_barh
import plotly.graph_objects as go
import plotly.express as px

def plot_avg_links_heatmap(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        count = len(soup.find_all("a", href=True))
        data[author].append(count)

    df = pd.DataFrame([(k, sum(v) / len(v)) for k, v in data.items()],
                      columns=["Author", "Avg Links"])

    return plot_barh(df, x_col="Avg Links", y_col="Author",
                     title="Average Links per Author", color="cornflowerblue")


def plot_avg_h2_heatmap(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        count = len(soup.find_all("h2"))
        data[author].append(count)
    df = pd.DataFrame([(k, sum(v)/len(v)) for k, v in data.items()],
                      columns=["Author", "Avg H2"]).set_index("Author")
    fig = plot_heatmap(df, title="Heatmap of Average <h2> Tags per Author")

    return fig

def plot_avg_tema_links_heatmap(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        links = [a for a in soup.find_all("a", href=True) if "/tema/" in a["href"]]
        data[author].append(len(links))
    df = pd.DataFrame([(k, sum(v)/len(v)) for k, v in data.items()],
                      columns=["Author", "Avg Tema Links"]).set_index("Author")
    return plot_heatmap(df, title="Heatmap of Average '/tema/' Links per Author")

def plot_stacked_theme_vs_article_links(metadata):
    data = defaultdict(lambda: {"tema": 0, "notema": 0})
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        links = soup.find_all("a", href=True)
        for link in links:
            if "/tema/" in link["href"]:
                data[author]["tema"] += 1
            else:
                data[author]["notema"] += 1
    df = pd.DataFrame(data).T.reset_index().rename(columns={"index": "Author"})
    return plot_stacked_barh(df, y_col="Author", x1_col="tema", x2_col="notema",
                             label1="Enlaces a tags", label2="Enlaces a artículos",
                             title="Links with and without '/tema/' per Author")

def plot_strong_tags_bar(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        strong_count = len(soup.find_all("strong"))
        data[author].append(strong_count)
    df = pd.DataFrame([(k, sum(v)/len(v)) for k, v in data.items()],
                      columns=["Author", "Avg <strong>"])
    return plot_barh(df, x_col="Avg <strong>", y_col="Author",
                     title="Average <strong> Tags per Author", color="lightseagreen")

def plot_related_links_chart(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        count = len(soup.find_all("div", class_="wp_fsn_relatedlinks"))
        data[author].append(count)
    df = pd.DataFrame({"Author": list(data.keys()),
                       "Avg Related Links": [sum(c)/len(c) for c in data.values()]})
    return plot_barh(df, x_col="Avg Related Links", y_col="Author",
                     title="Average Related Links per Author", color="royalblue")

def plot_lazy_images_chart(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        count = len(soup.find_all("img", loading="lazy"))
        data[author].append(count)
    df = pd.DataFrame({"Author": list(data.keys()),
                       "Avg Lazy Images": [sum(c)/len(c) for c in data.values()]})
    return plot_barh(df, x_col="Avg Lazy Images", y_col="Author",
                     title="Average <img loading='lazy'> per Author", color="tomato")

def plot_total_images_chart(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        count = len(soup.find_all("img"))
        data[author].append(count)
    df = pd.DataFrame({
        "Author": list(data.keys()),
        "Avg Total Images": [sum(c)/len(c) for c in data.values()]
    })
    return plot_barh(df, x_col="Avg Total Images", y_col="Author",
                     title="Average <img> Tags per Author", color="darkcyan")


def plot_paragraph_length_chart(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        lengths = [len(p.get_text()) for p in soup.find_all("p")]
        avg_length = sum(lengths) / len(lengths) if lengths else 0
        data[author].append(avg_length)
    df = pd.DataFrame({"Author": list(data.keys()),
                       "Avg Paragraph Length": [sum(c)/len(c) for c in data.values()]})
    return plot_barh(df, x_col="Avg Paragraph Length", y_col="Author",
                     title="Average Characters per Paragraph per Author", color="lightcoral")

def plot_paragraph_count_chart(metadata):
    data = defaultdict(list)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        count = len(soup.find_all("p"))
        data[author].append(count)
    df = pd.DataFrame({"Author": list(data.keys()),
                       "Avg Paragraphs": [sum(c)/len(c) for c in data.values()]})
    return plot_barh(df, x_col="Avg Paragraphs", y_col="Author",
                     title="Average Paragraph Count per Author", color="lightblue")

def plot_missing_elements_chart(metadata):
    missing_counts = defaultdict(int)
    for article in metadata.values():
        soup = BeautifulSoup(decode_body(article.get("body", "")), "html.parser")
        if not soup.find(['b', 'strong']):
            missing_counts["Bold tags (<b>/<strong>)"] += 1
        if not soup.find("h2"):
            missing_counts["Header <h2>"] += 1
        if not soup.find("div", class_="wp_fsn_relatedlinks"):
            missing_counts["Div 'wp_fsn_relatedlinks'"] += 1
        if not soup.find("img", loading="lazy"):
            missing_counts["Lazy-loaded Image"] += 1
        links = soup.find_all("a", href=True)
        if not any("/tema/" in a["href"] for a in links):
            missing_counts["Link with '/tema/'"] += 1
        if not any("/tema/" not in a["href"] for a in links):
            missing_counts["Link without '/tema/'"] += 1
    df = pd.DataFrame({"Missing Element": list(missing_counts.keys()),
                       "Count": list(missing_counts.values())})
    return plot_barh(df, x_col="Count", y_col="Missing Element",
                     title="Number of Missing Elements in Articles", color="skyblue")

def plot_author_activity_over_time(metadata):
    author_counts = defaultdict(int)
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        author_counts[author] += 1
    df = pd.DataFrame({"Author": list(author_counts.keys()),
                       "Articles": list(author_counts.values())})
    df = df.sort_values("Articles")
    return plot_barh(df, x_col="Articles", y_col="Author",
                     title="Total Number of Articles Published per Author", color="mediumseagreen")

def plot_author_hourly_distribution(metadata):

    hourly_counts = defaultdict(lambda: defaultdict(int))
    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        published_at = article.get("published_at")
        if published_at:
            try:
                hour = pd.to_datetime(published_at).hour
                hourly_counts[author][hour] += 1
            except Exception:
                continue

    df = pd.DataFrame(hourly_counts).T.fillna(0).astype(int)
    df = df[[h for h in range(24) if h in df.columns]]
    df.columns = df.columns.astype(str)
    df.index = df.index.astype(str)


    fig = go.Figure(data=go.Heatmap(
        z=df.values,
        x=df.columns.tolist(),
        y=df.index.tolist(),
        text=df.values,
        texttemplate="%{text}",
        colorscale="YlGnBu",
        hoverongaps=False
    ))
    
    fig.update_layout(
        title="Articles by Hour per Author",
        xaxis_title="Hour",
        yaxis_title="Author",
        height=600 + len(df) * 20,
        width=1200,
        margin=dict(t=60, b=40)
    )

    return fig


def plot_author_weekday_distribution(metadata):
    from collections import defaultdict
    from .api_parser import extract_author_name

    weekday_counts = defaultdict(lambda: defaultdict(int))
    weekday_labels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        published_at = article.get("published_at")
        if published_at:
            try:
                weekday = pd.to_datetime(published_at).weekday()
                weekday_counts[author][weekday] += 1
            except Exception:
                continue

    df = pd.DataFrame(weekday_counts).T.fillna(0).astype(int)
    df.columns = [weekday_labels[i] for i in df.columns]
    df = df[[day for day in weekday_labels if day in df.columns]]
    df.index = df.index.astype(str)

    fig = go.Figure(data=go.Heatmap(
        z=df.values,
        x=df.columns.tolist(),
        y=df.index.tolist(),
        text=df.values,
        texttemplate="%{text}",
        colorscale="YlGnBu",
        hoverongaps=False
    ))

    fig.update_layout(
        title="Articles by Weekday per Author",
        xaxis_title="Day",
        yaxis_title="Author",
        height=600 + len(df) * 20,
        width=1200,
        margin=dict(t=60, b=40)
    )

    return fig




