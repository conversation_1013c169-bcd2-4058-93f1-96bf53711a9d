from collections import defaultdict, Counter
from bs4 import BeautifulSoup
import pandas as pd
import plotly.express as px
from .api_parser import decode_body, extract_author_name

def analyze_authors(metadata):
    author_stats = defaultdict(lambda: Counter())
    article_counts = Counter()

    for article in metadata.values():
        author = extract_author_name(article.get("author"))
        article_counts[author] += 1
        body_html = decode_body(article.get("body", ""))
        soup = BeautifulSoup(body_html, "html.parser")

        author_stats[author]["paragraphs"] += len(soup.find_all("p"))
        author_stats[author]["bold_tags"] += len(soup.find_all(['b', 'strong']))
        author_stats[author]["lazy_images"] += len(soup.find_all("img", loading="lazy"))
        author_stats[author]["tema_links"] += len([a for a in soup.find_all("a", href=True) if "/tema/" in a["href"]])

    df = pd.DataFrame.from_dict(author_stats, orient="index").fillna(0).astype(int)
    df["Author"] = df.index
    df["Articles"] = df.index.map(article_counts.get).fillna(0).astype(int)

    for col in ["paragraphs", "bold_tags", "lazy_images", "tema_links"]:
        df[f"avg_{col}"] = (df[col] / df["Articles"]).round(2)

    ordered_cols = [
        "Author", "Articles", "paragraphs", "avg_paragraphs", "bold_tags", "avg_bold_tags",
        "lazy_images", "avg_lazy_images", "tema_links", "avg_tema_links"
    ]
    df = df[ordered_cols]

    total_melted = df.melt(id_vars="Author", value_vars=["paragraphs", "bold_tags", "lazy_images", "tema_links"],
                           var_name="Metric", value_name="Count")
    avg_melted = df.melt(id_vars="Author", value_vars=["avg_paragraphs", "avg_bold_tags", "avg_lazy_images", "avg_tema_links"],
                         var_name="Metric", value_name="Avg Count")

    fig1 = px.bar(total_melted, x="Count", y="Author", color="Metric", orientation="h",
                  title="Total Content Metrics by Author")
    fig2 = px.bar(avg_melted, x="Avg Count", y="Author", color="Metric", orientation="h",
                  title="Per-Article Averages by Author")

    return df.sort_values("Articles", ascending=False), fig1, fig2

def audit_articles(metadata):
    article_issues = []

    for article_id, article in metadata.items():
        title = article.get("title", "Untitled")
        url = article.get("url")
        author = extract_author_name(article.get("author"))
        published_at = article.get("published_at")
        body_html = decode_body(article.get("body", ""))
        soup = BeautifulSoup(body_html, "html.parser")

        missing = []
        if not soup.find(['b', 'strong']):
            missing.append("No <strong>/<b>")
        if not soup.find("h2"):
            missing.append("No <h2>")
        if not soup.find("div", class_="wp_fsn_relatedlinks"):
            missing.append("No related links div")
        if not soup.find("img", loading="lazy"):
            missing.append("No lazy-loaded image")
        links = soup.find_all("a", href=True)
        if not any("/tema/" in a["href"] for a in links):
            missing.append("No /tema/ link")

        article_issues.append({
            "ID": article_id,
            "URL": url,
            "Title": title,
            "Author": author,
            "Published": published_at,
            "Missing Elements": ", ".join(missing),
            "Paragraphs": len(soup.find_all("p")),
            "Words": len(soup.get_text().split())
        })

    df = pd.DataFrame(article_issues)

    df["Published"] = pd.to_datetime(df["Published"], errors='coerce')
    df["Date Published"] = df["Published"].dt.date.astype(str)
    df["Time Published"] = df["Published"].dt.time.astype(str)
    df.drop(columns=["Published"], inplace=True)

    if "Missing Elements" not in df.columns:
        df["Missing Elements"] = ""

    df["Missing Count"] = df["Missing Elements"].apply(lambda x: len(x.split(", ")) if x else 0)

    fig = px.histogram(df, x="Missing Count", nbins=int(df["Missing Count"].max()) + 1,
                       title="Distribution of Missing Elements in Articles")

    return df, fig

def render_table_html(df, table_id="dataTable", drop_cols=None, classes="table table-striped text-center"):
    if drop_cols:
        df = df.drop(columns=drop_cols, errors="ignore")
    return df.to_html(classes=classes, index=False, table_id=table_id)
