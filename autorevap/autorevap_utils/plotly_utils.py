import plotly.express as px
import plotly.graph_objects as go


def plot_barh(df, x_col, y_col, title, color="lightblue", height=600, width=1200):
    fig = px.bar(
        df,
        x=x_col,
        y=y_col,
        orientation="h",
        title=title,
        color_discrete_sequence=[color]
    )
    fig.update_layout(
        height=height,
        width=width,
        margin=dict(t=60, b=40),
        xaxis_title=x_col,
        yaxis_title=y_col
    )
    return fig


def plot_stacked_barh(df, y_col, x1_col, x2_col, label1, label2, title, height=600, width=1200):
    fig = go.Figure()
    fig.add_trace(go.Bar(
        y=df[y_col], x=df[x1_col], name=label1, orientation='h'
    ))
    fig.add_trace(go.Bar(
        y=df[y_col], x=df[x2_col], name=label2, orientation='h'
    ))
    fig.update_layout(
        barmode='stack',
        title=title,
        height=height,
        width=width,
        margin=dict(t=60, b=40),
        xaxis_title="Count",
        yaxis_title=y_col
    )
    return fig


def plot_heatmap(df, title, x_title="Authors", y_title="Metric", height=600, width=1200):
    df.columns = df.columns.astype(str)
    df.index = df.index.astype(str)
    z = df.values
    x = df.columns.tolist()
    y = df.index.tolist()

    if df.shape[0] > 1 and df.shape[1] > 1:
        fig = go.Figure(data=go.Heatmap(
            z=df.values,
            x=df.columns.tolist(),
            y=df.index.tolist(),
            text=df.values,                 
            texttemplate="%{text}",         
            colorscale="YlGnBu",
            hoverongaps=False
        ))

        fig.update_layout(
            title=title,
            xaxis_title=x_title,
            yaxis_title=y_title,
            height=height,
            width=width,
            margin=dict(t=60, b=40)
        )
        return fig

    # Fallback bar chart for single-row data
    df_reset = df.reset_index()
    return plot_barh(
        df_reset,
        x_col=df.columns[0],
        y_col=df.index.name if df.index.name else df_reset.columns[0],
        title=title,
        color="cornflowerblue",
        height=height,
        width=width
    )


