from django.db import models
from django.urls import reverse
import zlib

# Create your models here.

class AutorevapApplication(models.Model):
    app_name = models.CharField(max_length = 255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)    
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)


    def __str__(self):
        return self.app_name    


class SiteSource(models.Model):

    site_name = models.CharField(max_length = 255, unique=True)
    site_url = models.URLField(max_length = 255, default=None)
    shortcode = models.CharField(max_length = 10, unique=True)
    rss_feed_url = models.URLField(max_length = 255, default=None)
    api_url = models.URLField(max_length = 255, default=None)

    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ('site_name', 'shortcode')
        ordering = ['site_name']

    def __str__(self):
        return f"{self.site_name} [{self.shortcode}]"
    
    def get_absolute_url(self):
        return reverse("autorevap:site_detail", kwargs={"shortcode": self.shortcode})
    

class Author(models.Model):
    name = models.CharField(max_length = 255)

    def __str__(self):
        return self.name
    

class Article(models.Model):
    source = models.ForeignKey(SiteSource, on_delete=models.CASCADE, related_name="articles", null=True)
    author = models.ForeignKey(Author, on_delete=models.SET_NULL, null=True)
    external_id = models.CharField(max_length=100, unique=True)
    url = models.URLField()
    title = models.CharField(max_length=500)
    section = models.CharField(max_length=255, blank=True)
    section_slug = models.SlugField(max_length=255, blank=True)
    link = models.URLField(blank=True)
    published_at = models.DateTimeField(null=True, blank=True)
    modified_at = models.DateTimeField(null=True, blank=True)
    body_compressed = models.BinaryField(blank=True, null=True)
    created_by = models.CharField(max_length=255, blank=True)
    canonical = models.URLField(blank=True)
    seo_title = models.CharField(max_length=500, blank=True)
    seo_description = models.TextField(blank=True)
    site_code = models.CharField(max_length=20)
    tags = models.JSONField(blank=True, null=True)
    images = models.JSONField(blank=True, null=True)

    # Raw content statistics
    link_count = models.IntegerField(default=0)
    tema_link_count = models.IntegerField(default=0)
    h2_count = models.IntegerField(default=0)
    strong_tag_count = models.IntegerField(default=0)
    related_links_count = models.IntegerField(default=0)
    lazy_img_count = models.IntegerField(default=0)
    total_img_count = models.IntegerField(default=0)
    paragraph_count = models.IntegerField(default=0)
    avg_paragraph_length = models.FloatField(default=0.0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["source", "author"]),
            models.Index(fields=["author"]),
            models.Index(fields=["published_at"]),
            models.Index(fields=["section"]),
        ]


    def set_body(self, html):
        self.body_compressed = zlib.compress(html.encode("utf-8"))

    def get_body(self):
        if self.body_compressed:
            return zlib.decompress(self.body_compressed).decode("utf-8")
        else:
            return ""



    def __str__(self):
        return self.title


class RSSArticle(models.Model):
    source = models.ForeignKey(
        "SiteSource", on_delete=models.CASCADE, related_name="rss_articles", null=True
    )
    author = models.ForeignKey(
        "Author", on_delete=models.SET_NULL, null=True, blank=True
    )
    url = models.URLField()
    title = models.CharField(max_length=500)
    category = models.CharField(max_length=255, blank=True)
    published_at = models.DateTimeField(null=True, blank=True)

    # Compressed HTML body
    body_compressed = models.BinaryField(blank=True, null=True)

    # Raw content statistics
    link_count = models.IntegerField(default=0)
    h2_count = models.IntegerField(default=0)
    paragraph_count = models.IntegerField(default=0)
    avg_paragraph_length = models.FloatField(default=0.0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def set_body(self, html):
        self.body_compressed = zlib.compress(html.encode("utf-8"))

    def get_body(self):
        if self.body_compressed:
            return zlib.decompress(self.body_compressed).decode("utf-8")
        return ""

    def __str__(self):
        return self.title

    class Meta:
        indexes = [
            models.Index(fields=["source", "author"]),
            models.Index(fields=["published_at"]),
            models.Index(fields=["category"]),
        ]