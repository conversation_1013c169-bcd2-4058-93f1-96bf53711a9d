from django.shortcuts import render,redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from applications.models import Application
from alerts.models import CustomUser
from alerts.models import Market


# Create your views here.

@login_required(login_url = '/login')
def apps(request):

    user = request.user
    apps_details = user.apps.all()

    user_market = Market.objects.filter(customuser=request.user).exists()

    context = {
        "app_details":apps_details,
        "user_market": user_market

    }
    
    return render(request, 'applications/apps.html', context=context)



