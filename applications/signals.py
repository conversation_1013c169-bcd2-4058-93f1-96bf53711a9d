from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from .models import Application
from logana.models import LnApplication
from psi.models import PsiApplication
from ts.models import TsApplication
from alerts.models import Application as AlertApplication
from gis.models import GisApplication
from sitemap_checker.models import SitemapCheckerApplication


@receiver(post_save, sender=Application)
def record_callback(sender, instance, created, **kwargs):

    # Check if the signal is triggered by a new record creation
    if created:
        print(f"Created: {instance.app_name}")
        for aps in [AlertApplication, LnApplication, PsiApplication, TsApplication, GisApplication, SitemapCheckerApplication],:

        # Create a new record in LnApplication using data from the newly created Application
            try:
                aps.objects.create(
                    app_name=instance.app_name,
                    app_description=instance.app_description,
                    app_standalone = instance.app_standalone,
                    app_is_active=instance.app_is_active,
                    in_test=instance.in_test,
                    receive_alerts = instance.receive_alerts,
            )
            except Exception as e:
                print(e)

    else:

        print(f"Updated: {instance.app_name}")
        try:
            # Find the corresponding LnApplication and PsiApplication records by id
            alert_application = AlertApplication.objects.filter(id=instance.id).first()
            ln_application = LnApplication.objects.filter(id=instance.id).first()
            psi_application = PsiApplication.objects.filter(id=instance.id).first()
            ts_application = TsApplication.objects.filter(id=instance.id).first()
            gis_application = GisApplication.objects.filter(id=instance.id).first()
            sitemap_application = SitemapCheckerApplication.objects.filter(id=instance.id).first()


            if alert_application:
                # update the existing AlertApplication records with new data
                alert_application.app_name = instance.app_name
                alert_application.app_description = instance.app_description
                alert_application.app_standalone = instance.app_standalone
                alert_application.app_is_active = instance.app_is_active
                alert_application.in_test = instance.in_test
                alert_application.receive_alerts = instance.receive_alerts
                alert_application.save()


            if ln_application:
                # Update the existing LnApplication record with the new data
                ln_application.app_name = instance.app_name
                ln_application.app_description = instance.app_description
                ln_application.app_standalone = instance.app_standalone
                ln_application.app_is_active = instance.app_is_active
                ln_application.in_test = instance.in_test
                ln_application.receive_alerts = instance.receive_alerts
                ln_application.save()

            if psi_application:
                print("Updating psi app")
                # Update the existing PsiApplication record with the new data
                psi_application.app_name = instance.app_name
                psi_application.app_description = instance.app_description
                psi_application.app_standalone = instance.app_standalone
                psi_application.app_is_active = instance.app_is_active
                psi_application.in_test = instance.in_test
                psi_application.receive_alerts = instance.receive_alerts
                psi_application.save()
            if ts_application:
                print("Updating topstories app")
                # Update the existing PsiApplication record with the new data
                ts_application.app_name = instance.app_name
                ts_application.app_description = instance.app_description
                ts_application.app_standalone = instance.app_standalone
                ts_application.app_is_active = instance.app_is_active
                ts_application.in_test = instance.in_test
                ts_application.receive_alerts = instance.receive_alerts
                ts_application.save()
            if gis_application:
                print("Updating GIS app")
                #Update existing GisApplication record with the new data
                gis_application.app_name = instance.app_name
                gis_application.app_description = instance.app_description
                gis_application.app_standalone = instance.app_standalone
                gis_application.app_is_active = instance.app_is_active
                gis_application.in_test = instance.in_test
                gis_application.receive_alerts = instance.receive_alerts
                gis_application.save()
            if sitemap_application:
                print("Updating sitemap application")
                sitemap_application.app_name = instance.app_name
                sitemap_application.app_description = instance.app_description
                sitemap_application.app_standalone = instance.app_standalone
                sitemap_application.app_is_active = instance.app_is_active
                sitemap_application.in_test = instance.in_test
                sitemap_application.receive_alerts = instance.receive_alerts
                sitemap_application.save()


        except Exception as e:
            print(e)


@receiver(pre_delete, sender=Application)
def delete_related_apps(sender, instance, **kwargs):
    # Delete related Apps before the Application is deleted // Add here new app each time it's created
    AlertApplication.objects.filter(id=instance.id).delete()
    LnApplication.objects.filter(id=instance.id).delete()
    PsiApplication.objects.filter(id=instance.id).delete()
    TsApplication.objects.filter(id=instance.id).delete()
    GisApplication.objects.filter(id=instance.id).delete()
    SitemapCheckerApplication.objects.filter(id=instance.id).delete()
    print(f"Deleted related applications for application: {instance.app_name}")