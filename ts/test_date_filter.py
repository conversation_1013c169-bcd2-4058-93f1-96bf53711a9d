#!/usr/bin/env python3
"""
Test script to check date filtering in the staging database
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import utilitiesTS
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database connection function from utilitiesTS
from ts.utilitiesTS import get_database_connection, close_connection

def test_date_filter():
    """Test date filtering in the staging database"""
    print("\n\n==== TESTING DATE FILTERING ====\n")
    
    # Get a database connection
    conn = get_database_connection(caller='test_date_filter')
    
    if conn:
        print("Database connection successful!")
        
        # Get available dates
        query = """
        SELECT DISTINCT DATE(timestamp) as date
        FROM ScrapedData
        WHERE timestamp IS NOT NULL
        ORDER BY date DESC
        LIMIT 10
        """
        df_dates = pd.read_sql(query, conn)
        print("\nAvailable dates:")
        print(df_dates)
        
        # Get the latest date
        latest_date = df_dates['date'].iloc[0]
        print(f"\nLatest date: {latest_date}")
        
        # Check data for the latest date
        query = f"""
        SELECT keyword, COUNT(*) as count
        FROM ScrapedData
        WHERE DATE(timestamp) = '{latest_date}'
        GROUP BY keyword
        ORDER BY count DESC
        LIMIT 10
        """
        df_latest = pd.read_sql(query, conn)
        print(f"\nTop keywords for {latest_date}:")
        print(df_latest)
        
        # Check data for a date range (last 7 days)
        seven_days_ago = (datetime.strptime(str(latest_date), '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d')
        query = f"""
        SELECT keyword, COUNT(*) as count
        FROM ScrapedData
        WHERE DATE(timestamp) BETWEEN '{seven_days_ago}' AND '{latest_date}'
        GROUP BY keyword
        ORDER BY count DESC
        LIMIT 10
        """
        df_range = pd.read_sql(query, conn)
        print(f"\nTop keywords for date range {seven_days_ago} to {latest_date}:")
        print(df_range)
        
        # Check if Premier League exists in any date range
        query = """
        SELECT DATE(timestamp) as date, COUNT(*) as count
        FROM ScrapedData
        WHERE keyword = 'Premier League'
        GROUP BY date
        ORDER BY date DESC
        """
        df_premier = pd.read_sql(query, conn)
        print("\nPremier League data by date:")
        print(df_premier)
        
        # Close the connection
        close_connection(conn, caller='test_date_filter')
        print("\nDatabase connection closed")
    else:
        print("Database connection failed!")

if __name__ == "__main__":
    test_date_filter()
