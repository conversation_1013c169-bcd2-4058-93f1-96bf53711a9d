#!/usr/bin/env python3
"""
Test script to verify the global distribution map visualization
"""

import os
import sys
import pandas as pd
import plotly.express as px
import mysql.connector

# Add the parent directory to the path so we can import utilitiesTS
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database connection function from utilitiesTS
from ts.utilitiesTS import get_database_connection, close_connection, alpha2_to_alpha3

def test_map_visualization():
    """Test the global distribution map visualization"""
    print("\n\n==== TESTING MAP VISUALIZATION ====\n")
    
    # Get a database connection
    conn = get_database_connection(caller='test_map')
    
    if conn:
        print("Database connection successful!")
        
        # Query data for the map
        query = """
        SELECT country, COUNT(*) as count
        FROM ScrapedData
        WHERE country IS NOT NULL AND country != ''
        GROUP BY country
        """
        
        # Execute the query
        df = pd.read_sql(query, conn)
        print(f"Retrieved {len(df)} countries from the database")
        print(f"Sample data: {df.head().to_dict()}")
        
        # Convert country codes to alpha-3 for the map
        df['country_alpha_3'] = df['country'].apply(alpha2_to_alpha3)
        country_counts = df[['country_alpha_3', 'count']].rename(columns={'country_alpha_3': 'country'})
        
        print(f"Converted country codes. Sample data: {country_counts.head().to_dict()}")
        
        # Create the map visualization
        try:
            map_fig = px.choropleth(
                country_counts,
                locations="country",
                color="count",
                hover_name="country",
                color_continuous_scale=["lightblue", "blue"],
                title="Global Distribution",
                labels={'count': 'Number of Stories'},
                projection='natural earth'
            )
            
            # Save the map to an HTML file
            map_fig.write_html("map_visualization.html")
            print("Map visualization created successfully and saved to map_visualization.html")
            
        except Exception as e:
            print(f"Error creating map visualization: {e}")
        
        # Close the connection
        close_connection(conn, caller='test_map')
        print("Database connection closed")
    else:
        print("Database connection failed!")

if __name__ == "__main__":
    test_map_visualization()
