from ts.utilitiesTS import get_database_connection
from django.shortcuts import render
# from dash import dcc, html
# import pandas as pd
# import plotly.express as px
# import dash_core_components as dcc
# import dash_html_components as html
# from dash import Dash, Input, Output, State, ALL, MATCH
# from django_plotly_dash import DjangoDash
# from ts.utilitiesTS import create_dash_app
from ts.utilitiesTS import ai_dashboard, single_keyword_report
from django.core.paginator import Paginator
from django.shortcuts import render
from .forms import TriggerScriptForm
from django.http import HttpResponse, HttpResponseRedirect
from django.views.decorators.csrf import csrf_exempt
import schedule
from ts.forms import DynamicRowForm
import pandas as pd
import csv
from django.core.files.storage import FileSystemStorage
import math
from alerts.models import Country, Market
from urllib.parse import urlencode


# Create your views here.
def top_stories_report(request):
    app = ai_dashboard()
    return render(request, 'topstories/topstories_report.html')


def top_stories_single_report(request, keyword=None):
    app = single_keyword_report(keyword)
    return render(request, 'topstories/topstories_singlereport.html')


def search_targets(request):
    language_filter = request.GET.get('language', '')
    country_filter = request.GET.get('country', '')

    # Search Query
    search_query = request.GET.get('search', '')

    conn = get_database_connection()
    if not conn:
        # Handle connection error gracefully
        return render(request, 'topstories/search_targets.html', {})

    with conn.cursor() as cursor:
        # Fetch distinct languages and countries from the database
        languages_query = "SELECT DISTINCT language FROM InputSearchTargets"
        cursor.execute(languages_query)
        languages = [row[0] for row in cursor.fetchall()]

        countries_query = "SELECT DISTINCT country FROM InputSearchTargets"
        cursor.execute(countries_query)
        countries = [row[0] for row in cursor.fetchall()]

        # Fetch search targets with applied filters and search query
        targets_query = """
            SELECT id, keyword, language, country 
            FROM InputSearchTargets 
            WHERE 1=1 
        """
        params = []
        if language_filter:
            targets_query += " AND language=%s"
            params.append(language_filter)
        if country_filter:
            targets_query += " AND country=%s"
            params.append(country_filter)
        if search_query:
            targets_query += " AND keyword LIKE %s"
            params.append("%" + search_query + "%")

        cursor.execute(targets_query, params)
        targets = [dict(zip(['id', 'keyword', 'language', 'country'], row)) for row in cursor.fetchall()]

        # Pagination
        paginator = Paginator(targets, 25)  # 25 items per page
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)

        conn.close()

        return render(request, 'topstories/topstories_searchtargets.html', {
            'page': page_obj,
            'languages': languages,
            'countries': countries,
            'language_filter': language_filter,
            'country_filter': country_filter,
            'search_query': search_query,
        })



@csrf_exempt
def trigger_script(request):
    if request.method == 'POST':
        form = TriggerScriptForm(request.POST)
        if form.is_valid():
            interval = int(form.cleaned_data['interval'])
            # update_database(1)

            # Set the schedule interval based on the selected option
            if interval == 15:
                print('ok')
            #     schedule.every(15).minutes.do(make_csv_and_notify)
            # elif interval == 30:
            #     schedule.every(30).minutes.do(make_csv_and_notify)
            # elif interval == 60:
            #     schedule.every().hour.do(make_csv_and_notify)

            global run_script_flag
            run_script_flag = True  # Allow script execution
            return HttpResponse("Script triggered successfully!")

    else:
        form = TriggerScriptForm()

    return render(request, 'topstories/topstories_trigger.html', {'form': form})


def keyword_tracking_handling(request):
    conn = get_database_connection()
    cursor = conn.cursor()
    user = request.user

    print(user)
    print(f'USER IS {user.email}')
    full_username = user.full_name

    everything_market = Market.objects.get(market_name='Everything')
    total_keyword_limit = everything_market.keyword_limit
    print(f'TOTAL LIMIT IS:{total_keyword_limit}')

    market = Market.objects.filter(customuser=user).first()
    print(f'{market} IS THE MARKET {user} HAS ACCESS TO!')
    market_limit = market.keyword_limit
    print(market_limit)

    markets = Market.objects.filter(customuser=user)


    # market_limits = {market.id: market.keyword_limit for market in markets}
    # print('\n')
    # print(market_limits.keyword_limit)

    cursor.execute("SELECT hl_code, language FROM hl_parameters")
    languages = cursor.fetchall()

    countries = [
        (country.country_name, country.country_code)
        for market in markets
        for country in market.countries.all()
    ]
    print(countries)

    try:
        # Fetch total keyword amount for the market before applying filters
        country_codes = [country[0] for country in countries]
        print(f'Country Codes: {country_codes}')

        if country_codes:
            placeholders = ', '.join(['%s'] * len(country_codes))
            query = f"SELECT COUNT(*) FROM InputSearchTargets WHERE country IN ({placeholders})"
            print(f'Query for total keywords: {query}')
            cursor.execute(query, country_codes)
            keyword_amount = cursor.fetchone()[0]
        else:
            keyword_amount = 0
        print(f'TOTAL KEYWORD AMOUNT: {keyword_amount}')

        # Initial SQL query to get all records filtered by available countries
        sql_query = "SELECT * FROM InputSearchTargets WHERE 1=1"
        query_params = []

        country = request.GET.get('country', '')
        valid_countries = [c[0] for c in countries]  # List of valid country names

        if country and country in valid_countries:
            sql_query += " AND country = %s"
            query_params.append(country)
        elif countries:
            country_names = [country[0] for country in countries]
            sql_query += " AND country IN (" + ",".join(["%s"] * len(country_names)) + ")"
            query_params.extend(country_names)

        # Filtering based on GET parameters
        keyword = request.GET.get('keyword', '')
        language = request.GET.get('language', '')
        active = request.GET.get('active', '')

        if keyword:
            sql_query += " AND keyword LIKE %s"
            query_params.append(f"%{keyword}%")
        if language:
            sql_query += " AND language LIKE %s"
            query_params.append(f"%{language}%")
        if active:
            sql_query += " AND active = %s"
            query_params.append(active)

        print(sql_query, query_params)

        # Execute the query with the accumulated filters
        cursor.execute(sql_query, query_params)
        rows = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]

        # keyword_amount = len(rows)
        # print(f'CAPACITY IS {keyword_amount}')

        paginator = Paginator(rows, 20)
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)

        filters = {
            'country': country,
            'keyword': keyword,
            'language': language,
            'active': active,
        }
        query_string = urlencode(filters)

        if request.method == 'POST':
            if 'csv_file' in request.FILES:
                csv_file = request.FILES['csv_file']
                fs = FileSystemStorage('media')
                filename = fs.save(csv_file.name, csv_file)
                file_path = fs.path(filename)

                try:
                    with open(file_path, newline='', encoding='utf-8') as csvfile:
                        reader = csv.DictReader(csvfile)
                        required_fields = {'keyword', 'language', 'country'}
                        fieldnames = reader.fieldnames
                        missing_fields = required_fields - set(fieldnames)

                        if missing_fields:
                            return HttpResponse(f"Error: Missing required fields: {', '.join(missing_fields)}", status=400)

                        # Normalize fieldnames
                        normalized_rows = []
                        for row in reader:
                            normalized_row = {
                                'keyword': row.get('keyword', row.get('Keyword', '')).strip(),
                                'language': row.get('language', row.get('Language', '')).strip(),
                                'country': row.get('country', row.get('Country', '')).strip(),
                                'active': 1  # default value
                            }
                            normalized_rows.append(normalized_row)

                        if keyword_amount + len(normalized_rows) > total_keyword_limit:
                            return HttpResponse(
                                f"Error: Adding these keywords exceeds the total limit of {total_keyword_limit}.",
                                status=400)
                        if keyword_amount + len(normalized_rows) > market_limit:
                            return HttpResponse(
                                f"Error: Adding these keywords exceeds the total limit of {market_limit}.",
                                status=400)


                        placeholders = ', '.join(['%s'] * len(normalized_rows[0]))
                        columns_without_id = [col for col in columns if col != 'id']
                        sql = f"INSERT INTO InputSearchTargets ({', '.join(columns_without_id)}) VALUES ({placeholders})"

                        for row in normalized_rows:
                            cursor.execute(sql, list(row.values()))
                        conn.commit()
                    fs.delete(filename)

                    cursor.execute("SELECT COUNT(*) FROM InputSearchTargets")
                    total_rows = cursor.fetchone()[0]
                    total_pages = math.ceil(total_rows / paginator.per_page)

                    return HttpResponseRedirect(f'?page={total_pages}')
                except Exception as e:
                    print(f"Error processing CSV file: {e}")
                    return HttpResponse(f"Error processing CSV file: {e}", status=500)
            else:
                form_data = request.POST.dict()

                print(f"Complete form data: {form_data}")

                action = form_data.pop('action', None)

                if action == 'add':
                    new_row_data = []
                    columns_without_id = [col for col in columns if col != 'id' and col != 'active']

                    for column in columns_without_id:
                        new_row_data.append(form_data.get(f'new_{column}', ''))

                    # Set the default value for 'active' to 1
                    new_row_data.append(1)

                    if keyword_amount + 1 > total_keyword_limit or keyword_amount + 1 > market_limit:
                        return HttpResponse(
                            "Error: Adding these keywords exceeds the limit assigned to the market.",
                            status=400)

                    print(f"new_row_data: {new_row_data}")

                    try:
                        placeholders = ', '.join(['%s'] * len(columns_without_id) + ['%s'])
                        sql = f"INSERT INTO InputSearchTargets ({', '.join(columns_without_id)}, active) VALUES ({placeholders})"
                        cursor.execute(sql, new_row_data)
                        conn.commit()

                        cursor.execute("SELECT COUNT(*) FROM InputSearchTargets")
                        total_rows = cursor.fetchone()[0]
                        total_pages = math.ceil(total_rows / paginator.per_page)

                        return HttpResponseRedirect(f'?page={total_pages}')
                    except Exception as e:
                        print(f"Error adding new row: {e}")
                        return HttpResponse(f"Error adding new row: {e}", status=500)
                else:
                    row_id = form_data.pop('id')
                    print(f"row id: {row_id}")
                    index = next((i for i, row in enumerate(rows) if str(row[0]) == row_id), None)
                    if index is None:
                        print("Error: Row ID not found in the fetched rows.")
                        return HttpResponse("Error: Row ID not found.", status=400)
                    original_data = rows[index]
                    print(f"original_data: {original_data}")

                    if action == 'delete':
                        try:
                            cursor.execute("DELETE FROM InputSearchTargets WHERE id = %s", [row_id])
                            conn.commit()
                            return HttpResponseRedirect(f'?page={page_number}')
                        except Exception as e:
                            print(f"Error deleting row: {e}")
                            return HttpResponse(f"Error deleting row: {e}", status=500)
                    else:
                        if 'csrfmiddlewaretoken' in form_data:
                            form_data.pop('csrfmiddlewaretoken')

                        # Prepare the set clauses and parameters for the update query
                        set_clauses = []
                        params = []

                        for key, value in form_data.items():
                            cleaned_key = columns[int(key)]
                            original_value = original_data[int(key)]
                            if str(original_value) != value:
                                set_clauses.append(f"{cleaned_key} = %s")
                                params.append(value)

                        if set_clauses:
                            sql = f"UPDATE InputSearchTargets SET {', '.join(set_clauses)} WHERE id = %s"
                            params.append(row_id)
                            print(f"set_clauses: {set_clauses}")
                            print(f"sql: {sql}")
                            print(f"params: {params}")

                            try:
                                cursor.execute(sql, params)
                                conn.commit()
                                return HttpResponseRedirect(f'?page={page_number}')
                            except Exception as e:
                                print(f"Error updating row: {e}")
                                return HttpResponse(f"Error updating row: {e}", status=500)

            return redirect(keyword_tracking_handling)
    except Exception as e:
        print(f"Error fetching data: {e}")
        return HttpResponse(f"Error fetching data: {e}", status=500)
    finally:
        cursor.close()
        conn.close()

    manager_info = {'full_name': full_username, 'market': market, 'keyword_amount': keyword_amount, 'market_limit': market_limit}

    return render(request, 'topstories/topstories_input.html', {
        'page_obj': page_obj,
        'columns': columns,
        'rows': rows,
        'countries': countries,
        'languages': languages,
        'request': request,
        'manager_info': manager_info,
        'query_string': query_string,

    })
