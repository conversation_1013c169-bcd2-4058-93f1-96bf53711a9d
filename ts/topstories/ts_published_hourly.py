import pandas as pd
import re
from deep_translator import GoogleTranslator
import os

def process_csv(csv_file):
    print(csv_file)
    # Read CSV into a DataFrame
    df = pd.read_csv(csv_file)

    # Extract unique time strings (without numbers) from the 'published' column
    def extract_unique_time_strings(time_str):
        # Handle None or non-string values
        if time_str is None or not isinstance(time_str, str):
            return ''
        
        try:
            return re.sub(r'\d+', '', time_str).strip()
        except Exception as e:
            print(f"Error processing time string: {time_str}, Error: {e}")
            return ''

    df['time_strings'] = df['published'].apply(extract_unique_time_strings)
    unique_time_strings = df['time_strings'].unique()

    # Translate unique time strings to English
    translator = GoogleTranslator(source='auto', target='en')
    translations = {time_str: translator.translate(time_str).lower() for time_str in unique_time_strings}
    
    # Reverse map translations to original
    reverse_translations = {v: k for k, v in translations.items()}

    # Define the function to convert time strings to hourly values based on English translation
    def convert_to_hourly(time_str):
        time_str_lower = time_str.lower()
        
        # First try to match using the primary translation dictionary
        for original, translated in translations.items():
            if original in time_str_lower:
                translated_lower = translated
                match = re.search(r'(\d+)', time_str)
                if match:
                    quantity = int(match.group(1))
                    if 'day' in translated_lower:
                        return quantity * 24
                    elif 'week' in translated_lower:
                        return quantity * 7 * 24
                    elif 'minute' in translated_lower:
                        return quantity / 60
                    elif 'month' in translated_lower:
                        return quantity * 30 * 24
                    elif 'hour' in translated_lower:
                        return quantity
                elif 'now' in translated_lower:
                    return 0
        
        # If not found in translations, handle specific cases directly
        if 'há' in time_str_lower:
            match = re.search(r'(\d+)\s+(hora[s]?|horas)', time_str_lower)
            if match:
                quantity = int(match.group(1))
                return quantity
        
            match = re.search(r'(\d+)\s+(minuto[s]?|minutos)', time_str_lower)
            if match:
                quantity = int(match.group(1))
                return quantity / 60
        
        elif 'hace' in time_str_lower:
            match = re.search(r'(\d+)\s+(hora[s]?|horas)', time_str_lower)
            if match:
                quantity = int(match.group(1))
                return quantity
        
            match = re.search(r'(\d+)\s+(minuto[s]?|minutos)', time_str_lower)
            if match:
                quantity = int(match.group(1))
                return quantity / 60
            
            # Handle 'hace días' and 'hace día'
            match = re.search(r'(\d+)\s+d[ií]a[s]?', time_str_lower)
            if match:
                quantity = int(match.group(1))
                return quantity * 24
            
            match = re.search(r'(\d+)\s+d[ií]a[s]?', time_str_lower)
            if match:
                quantity = int(match.group(1))
                return quantity * 24

        # If no match is found, return 0
        return 0

    # Apply the conversion function to the 'published' column
    df['published_hourly'] = df['published'].apply(convert_to_hourly)

    # Round 'published_hourly' to 2 decimal places
    df['published_hourly'] = df['published_hourly'].round(2)

    # Ensure the 'published_hourly' column contains only numeric values
    df['published_hourly'] = pd.to_numeric(df['published_hourly'], errors='coerce').fillna(0)

    # Save the final DataFrame to a CSV file
    output_file = os.path.join(os.path.dirname(csv_file), os.path.basename(csv_file))
    df.to_csv(output_file, index=False, columns=['keyword', 'language', 'country', 'titles', 'urls', 'sources', 'published', 'device', 'ts_position', 'published_hourly', 'timestamp'])

    # Optional: Save entries where published_hourly is 0 to a separate CSV file for further inspection
    df_0 = df[df['published_hourly'] == 0.0]
    # output_file_0 = os.path.join(os.path.dirname(csv_file), 'explore_0.csv')
    # df_0.to_csv(output_file_0, index=False, columns=['keyword', 'language', 'country', 'titles', 'urls', 'sources', 'published', 'device', 'ts_position', 'published_hourly', 'timestamp'])

# Example usage:
# process_csv('path_to_your_csv_file.csv')
