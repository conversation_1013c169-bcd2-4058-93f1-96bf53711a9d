import random
import time
import traceback
import subprocess
import os
from datetime import datetime

# Third-party imports
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from botasaurus.browser import browser, Driver, Wait
from botasaurus.user_agent import UserAgent
from botasaurus.window_size import WindowSize
from tenacity import retry, stop_after_attempt, wait_random_exponential
from decouple import config

# Local imports
from database.scraped_data import get_database_connection
from ts_published_hourly import process_csv
from database.scraped_data import update_db_remove_csv

print("Starting Google Top Stories Scraper...")
start = time.time()

# Load proxy configuration from environment variables with fallback
try:
    smartproxy_username = config('SMARTPROXY_USERNAME')
    smartproxy_password = config('SMARTPROXY_PASSWORD')
    smartproxy_proxy_ip = config('SMARTPROXY_PROXY_IP')
    smartproxy_proxy_port = config('SMARTPROXY_PROXY_PORT')
    print("Proxy configuration loaded successfully")
except Exception as e:
    print(f"Error loading proxy configuration: {e}")
    print("Using fallback proxy configuration")
    # Fallback configuration (replace with your actual fallback values)
    smartproxy_username = "username"
    smartproxy_password = "password"
    smartproxy_proxy_ip = "gate.smartproxy.com"
    smartproxy_proxy_port = "7000"

# Constants for scraping
MAX_RETRIES = 3
CAPTCHA_RETRY_DELAY_MIN = 3
CAPTCHA_RETRY_DELAY_MAX = 8
HUMAN_DELAY_MIN = 1
HUMAN_DELAY_MAX = 3


# Database connection and data fetching
connection = get_database_connection()

# Create a MySQL cursor
cursor = connection.cursor()

# Execute a SELECT query
table_name = 'InputSearchTargets'  # Replace with your actual table name
query = f'SELECT * FROM {table_name};'
cursor.execute(query)

# Fetch all rows
rows = cursor.fetchall()

# Get column names from the cursor description
column_names = [desc[0] for desc in cursor.description]

# Create a DataFrame from the fetched data
input_df = pd.DataFrame(rows, columns=column_names)
shuffled_df = input_df.sample(frac=1).reset_index(drop=True)
# print(shuffled_df)

# Print column names to verify
# print("Columns in DataFrame:", list(shuffled_df.columns))


# Configure proxy with error handling
try:
    proxy_smart_datacenter = f"http://{smartproxy_username}:{smartproxy_password}@{smartproxy_proxy_ip}:{smartproxy_proxy_port}"
    print(f"Using proxy: {smartproxy_proxy_ip}:{smartproxy_proxy_port}")
except Exception as e:
    print(f"Error configuring proxy: {e}")
    print("Using direct connection (not recommended)")
    proxy_smart_datacenter = None

# Define browser configuration with enhanced anti-detection measures
@browser(
    remove_default_browser_check_argument=True,
    user_agent=UserAgent.RANDOM,  # Randomize user agent
    window_size=WindowSize.RANDOM,  # Randomize window size
    wait_for_complete_page_load=False,  # Faster page loading
    max_retry=5,  # Retry mechanism for robustness
    reuse_driver=True,  # Reuse browser for efficiency
    close_on_crash=True,  # Close browser on crash to continue processing
    proxy=proxy_smart_datacenter,  # Use proxy configuration
    headless=True,  # Set to True for production, False for debugging
    parallel=5,  # Number of parallel browsers
    output=None,
    create_error_logs=False,
    # Enhanced browser arguments for better anti-detection
    add_arguments=[
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--disable-dev-shm-usage',
        '--disable-infobars',
        # '--no-sandbox',
        '--ignore-certificate-errors',
        '--disable-extensions',
        '--disable-notifications'
    ],
)
def scrape_data(driver: Driver, data):
    """
    Scrape Google top stories for a given keyword, language, and country.

    Args:
        driver: Botasaurus Driver instance
        data: Dictionary containing keyword, language, and country

    Returns:
        List of dictionaries containing scraped data or a single dictionary with error info
    """
    # Randomly choose device type for more variation
    device = random.choice(['Mobile', 'Desktop'])

    # Unpack data dictionary with error handling
    try:
        keyword = data.get('keyword', '')
        language = data.get('language', 'en')
        country = data.get('country', 'us')

        # Validate inputs
        if not keyword:
            raise ValueError("Keyword is required")
    except Exception as e:
        print(f"Error unpacking data: {e}")
        return {
            "keyword": data.get('keyword', 'Unknown'),
            "language": data.get('language', 'Unknown'),
            "country": data.get('country', 'Unknown'),
            "titles": "No Data",
            "urls": "No Data",
            "sources": "No data",
            "published": "No Data",
            "device": device,
            "ts_position": 0,
            "timestamp": datetime.now().strftime('%d-%m-%Y %H:%M:%S')
        }

    # Construct URL with additional randomization
    base_url = "https://www.google.com/search"
    params = {
        'q': keyword,
        'hl': language,
        'gl': country,
        'num': random.choice([10, 20, 30]),  # Randomize number of results for less detection
        'pws': '0',  # Disable personalized search
        'nfpr': '1',  # No auto-correction of spelling
    }

    # Add random parameters to avoid detection patterns
    if random.random() > 0.5:
        params['sourceid'] = 'chrome'
    if random.random() > 0.7:
        params['ie'] = 'utf-8'
    if random.random() > 0.6:
        params['oe'] = 'utf-8'

    url = base_url + '?' + '&'.join(f"{k}={v}" for k, v in params.items())
    print(f"Requesting URL: {url}")

    # Add random delay before navigation to mimic human behavior
    human_delay = random.uniform(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX)
    print(f"Adding human-like delay of {human_delay:.2f} seconds...")
    time.sleep(human_delay)

    # More human-like navigation with google_get
    try:
        # Check available Wait options
        wait_option = Wait.SHORT  # Default to SHORT
        if hasattr(Wait, 'LONG'):
            wait_option = Wait.LONG
        elif hasattr(Wait, 'MEDIUM'):
            wait_option = Wait.MEDIUM

        driver.google_get(
            url,
            accept_google_cookies=True,  # Automatically handle cookie consent
            bypass_cloudflare=True,      # Handle Cloudflare protection
            wait=wait_option             # Use appropriate wait option
        )
    except Exception as e:
        print(f"Error navigating to URL: {e}")
        traceback.print_exc()
        return {
            "keyword": keyword,
            "language": language,
            "country": country,
            "titles": "No Data - Navigation Error",
            "urls": "No Data",
            "sources": "No data",
            "published": "No Data",
            "device": device,
            "ts_position": 0,
            "timestamp": datetime.now().strftime('%d-%m-%Y %H:%M:%S')
        }

    # Enable human mode for realistic, human-like mouse movements
    driver.enable_human_mode()

    # Random scrolling to mimic human behavior
    try:
        # Use driver's scroll methods instead of execute_script
        # Scroll down randomly
        for _ in range(random.randint(2, 5)):
            # Use driver's scroll_down method if available, otherwise just add delay
            try:
                if hasattr(driver, 'scroll_down'):
                    driver.scroll_down()
                elif hasattr(driver, 'scroll'):
                    driver.scroll(by=random.randint(300, 800), smooth_scroll=True)
                # Add random delay regardless
                time.sleep(random.uniform(0.5, 1.5))
            except Exception as scroll_error:
                print(f"Error during scroll down: {scroll_error}")

        # Scroll back up randomly
        for _ in range(random.randint(1, 3)):
            try:
                if hasattr(driver, 'scroll_up'):
                    driver.scroll_up()
                elif hasattr(driver, 'scroll'):
                    driver.scroll(by=random.randint(-400, -200), smooth_scroll=True)
                # Add random delay regardless
                time.sleep(random.uniform(0.5, 1.0))
            except Exception as scroll_error:
                print(f"Error during scroll up: {scroll_error}")
    except Exception as e:
        print(f"Error during scrolling: {e}")
        # Continue execution, scrolling is just for human-like behavior

    # Check for CAPTCHA or cookie consent redirects
    current_url = driver.current_url

    # Enhanced CAPTCHA handling with multiple retries
    captcha_retries = 0
    captcha_detected = "/sorry/index" in current_url or "captcha" in current_url.lower()

    while captcha_detected and captcha_retries < MAX_RETRIES:
        captcha_retries += 1
        print(f"CAPTCHA FOUND for {keyword} (Attempt {captcha_retries}/{MAX_RETRIES})")

        # Add random delay and retry with exponential backoff
        retry_delay = random.uniform(
            CAPTCHA_RETRY_DELAY_MIN * (2 ** (captcha_retries - 1)),
            CAPTCHA_RETRY_DELAY_MAX * (2 ** (captcha_retries - 1))
        )
        print(f"Waiting {retry_delay:.2f} seconds before retrying...")
        time.sleep(retry_delay)

        # Try to solve CAPTCHA if it's a simple one
        try:
            # Look for common CAPTCHA elements
            if hasattr(driver, 'has_element') and driver.has_element("input[type='checkbox']"):
                print("Found checkbox CAPTCHA, attempting to click...")
                checkbox = driver.select("input[type='checkbox']")
                if checkbox:
                    checkbox.click()
                    time.sleep(2)

            # Look for "I'm not a robot" text
            robot_elements = driver.select_all("*:contains('I'm not a robot')")
            if robot_elements and len(robot_elements) > 0:
                print("Found 'I'm not a robot' element, attempting to click...")
                robot_elements[0].click()
                time.sleep(2)

            # Look for submit buttons
            submit_buttons = driver.select_all("button[type='submit'], input[type='submit']")
            if submit_buttons and len(submit_buttons) > 0:
                print("Found submit button, attempting to click...")
                submit_buttons[0].click()
                time.sleep(3)

        except Exception as captcha_solve_error:
            print(f"Error attempting to solve CAPTCHA: {captcha_solve_error}")

        # Try rotating proxy if available (by restarting the browser)
        if captcha_retries > 1:
            print("Attempting to rotate proxy by restarting browser...")
            try:
                if hasattr(driver, 'restart'):
                    driver.restart()
                    print("Browser restarted successfully")
                else:
                    print("Driver does not have restart method")
            except Exception as e:
                print(f"Error restarting browser: {e}")

        # Retry the request with different parameters
        try:
            # Modify URL slightly to avoid detection patterns
            params['ei'] = ''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=22))

            # Add a random parameter to further differentiate the request
            random_param = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=5))
            random_value = ''.join(random.choices('0123456789', k=3))
            params[random_param] = random_value

            url = base_url + '?' + '&'.join(f"{k}={v}" for k, v in params.items())

            print(f"Retrying with URL: {url}")

            # Use the same wait_option as defined earlier
            driver.google_get(
                url,
                accept_google_cookies=True,
                bypass_cloudflare=True,
                wait=wait_option
            )
            # Check if we're still on a CAPTCHA page
            current_url = driver.current_url
            captcha_detected = "/sorry/index" in current_url or "captcha" in current_url.lower()

            if not captcha_detected:
                print("Successfully bypassed CAPTCHA!")
        except Exception as e:
            print(f"Error during CAPTCHA retry: {e}")
            break

    # If still on CAPTCHA page after all retries, log and continue
    captcha_detected = "/sorry/index" in driver.current_url or "captcha" in driver.current_url.lower()
    if captcha_detected:
        print(f"CAPTCHA still present after {MAX_RETRIES} retries for {keyword}")
        return {
            "keyword": keyword,
            "language": language,
            "country": country,
            "titles": "No Data - CAPTCHA",
            "urls": "No Data",
            "sources": "No data",
            "published": "No Data",
            "device": device,
            "ts_position": 0,
            "timestamp": datetime.now().strftime('%d-%m-%Y %H:%M:%S')
        }

    # Extract title for logging
    title = driver.title
    print(f"Keyword: {keyword}, Title: {title}")

    try:
        # Use wait and more specific selector with multiple fallbacks
        selectors = [
            "a.WlydOe",                # Primary selector
            "div.Gx5Zad",              # Fallback 1
            "g-card",                  # Fallback 2
            "div[data-hveid]",         # Fallback 3
            "div.xuvV6b"               # Fallback 4
        ]

        # Check available Wait options
        wait_option = Wait.SHORT  # Default to SHORT
        if hasattr(Wait, 'MEDIUM'):
            wait_option = Wait.MEDIUM
        elif hasattr(Wait, 'LONG'):
            wait_option = Wait.LONG

        top_stories_divs = None
        for selector in selectors:
            try:
                top_stories_divs = driver.select_all(selector, wait=wait_option)
                if top_stories_divs and len(top_stories_divs) > 0:
                    print(f"Found {len(top_stories_divs)} top stories using selector: {selector}")
                    break
            except Exception as selector_error:
                print(f"Selector '{selector}' failed: {selector_error}")
                continue

        # If no divs found with any selector, return empty result
        if not top_stories_divs or len(top_stories_divs) == 0:
            print(f"No top stories divs found for keyword: {keyword} with any selector")
            return {
                "keyword": keyword,
                "language": language,
                "country": country,
                "titles": "No Data - No Top Stories",
                "urls": "No Data",
                "sources": "No data",
                "published": "No Data",
                "device": device,
                "ts_position": 0,
                "timestamp": datetime.now().strftime('%d-%m-%Y %H:%M:%S')
            }

        results = []
        timestamp = datetime.now().strftime('%d-%m-%Y %H:%M:%S')

        ts_position = 1
        for div in top_stories_divs:
            try:
                # Add small delay between processing each div
                time.sleep(random.uniform(0.1, 0.3))

                # Safely extract title with multiple selectors
                title = "No Title"
                for title_selector in ["div[role='heading']", "h3", "div.vvjwJb", "div.mCBkyc"]:
                    title_elem = div.select(title_selector)
                    if title_elem:
                        title = title_elem.text
                        break

                if not title or title == "No Title":
                    continue

                print(f"Title: {title} (length: {len(title)})")

                # Safely extract URL
                url = div.get_attribute("href") or "No URL"
                if url == "No URL":
                    # Try to find URL in child elements
                    url_elem = div.select("a")
                    if url_elem:
                        url = url_elem.get_attribute("href") or "No URL"
                print(f"URL: {url}")

                # Safely extract source with multiple approaches
                source = "No Source"
                for source_selector in [
                    "g-img",
                    "div.CEMjEf",
                    "div.MgUUmf",
                    "span.TVtOme"
                ]:
                    try:
                        source_elem = div.select(source_selector)
                        if source_elem:
                            source_parent = source_elem.parent
                            source_span = source_parent.select("span")
                            if source_span:
                                source = source_span.text
                                break
                    except:
                        continue

                print(f"Source: {source}")

                # Safely extract published time with multiple selectors
                published = "No Published Time"
                for published_selector in [
                    "div[style*='bottom:12px']",
                    "span.OSrXXb",
                    "div.ZE0LJd",
                    "div.YnLGIf"
                ]:
                    published_elem = div.select(published_selector)
                    if published_elem:
                        published = published_elem.text
                        break

                print(f"Published: {published}")

                # Only add to results if we have both published time and title
                if published != 'No Published Time' and title != 'No Title':
                    # Add ts_position to the results
                    results.append({
                        "keyword": keyword,
                        "language": language,
                        "country": country,
                        "titles": title,
                        "urls": url,
                        "sources": source,
                        "published": published,
                        "device": device,
                        "ts_position": ts_position,
                        "timestamp": timestamp
                    })
                    ts_position += 1

            except Exception as div_error:
                print(f"Error processing individual div for keyword {keyword}: {div_error}")
                # Continue processing other divs even if one fails
                continue

        # Return results if any were found
        if results:
            print(f"Found {len(results)} valid top stories for {keyword}")
            return results
        else:
            print(f"No valid top stories found for {keyword}")
            return {
                "keyword": keyword,
                "language": language,
                "country": country,
                "titles": "No Data - No Valid Stories",
                "urls": "No Data",
                "sources": "No data",
                "published": "No Data",
                "device": device,
                "ts_position": 0,
                "timestamp": datetime.now().strftime('%d-%m-%Y %H:%M:%S')
            }

    except Exception as e:
        print(f"Error processing top stories for keyword {keyword}: {e}")
        traceback.print_exc()
        return {
            "keyword": keyword,
            "language": language,
            "country": country,
            "titles": "No Data - Processing Error",
            "urls": "No Data",
            "sources": "No data",
            "published": "No Data",
            "device": device,
            "ts_position": 0,
            "timestamp": datetime.now().strftime('%d-%m-%Y %H:%M:%S')
        }

@retry(stop=stop_after_attempt(3), wait=wait_random_exponential(multiplier=1, max=10))
def process_row(row):
    """
    Process a single row of input data with retry capability.

    Args:
        row: DataFrame row containing keyword, language, and country

    Returns:
        Scraped data or error information
    """
    print(f"Processing row: {row['keyword']} ({row['language']}, {row['country']})")
    try:
        # Ensure all values are strings and handle missing values
        data = {
            'keyword': str(row['keyword']) if not pd.isna(row['keyword']) else "",
            'language': str(row['language']) if not pd.isna(row['language']) else "en",
            'country': str(row['country']) if not pd.isna(row['country']) else "us"
        }

        # Skip empty keywords
        if not data['keyword'].strip():
            print("Skipping empty keyword")
            return {
                'keyword': "Empty",
                'language': data['language'],
                'country': data['country'],
                'titles': "No Data - Empty Keyword",
                'urls': "No Data",
                'sources': "No data",
                'published': "No Data",
                'device': random.choice(['Mobile', 'Desktop']),
                'ts_position': 0,
                'timestamp': datetime.now().strftime('%d-%m-%Y %H:%M:%S')
            }

        # Call the scrape_data function
        result = scrape_data(data)
        return result
    except Exception as e:
        print(f"Error processing row: {e}")
        traceback.print_exc()
        device = random.choice(['Mobile', 'Desktop'])
        return {
            'keyword': str(row['keyword']) if not pd.isna(row['keyword']) else "Error",
            'language': str(row['language']) if not pd.isna(row['language']) else "Error",
            'country': str(row['country']) if not pd.isna(row['country']) else "Error",
            'titles': f"No Data - Error: {str(e)[:50]}",
            'urls': "No Data",
            'sources': "No data",
            'published': "No Data",
            'device': device,
            'ts_position': 0,
            'timestamp': datetime.now().strftime('%d-%m-%Y %H:%M:%S')
        }

# Initialize global results DataFrame
results_df = None

def process_all_rows():
    """
    Process all rows from the input DataFrame in parallel with improved error handling.

    This function:
    1. Gets the maximum number of workers from config
    2. Uses ThreadPoolExecutor to process rows in parallel
    3. Collects and processes results with robust error handling
    4. Updates the global DataFrame with the results
    """
    global results_df
    try:
        # Get max workers from config with fallback
        try:
            MAX_WORKERS_TS = config('MAX_TOPSTORIES_WORKERS', default='3')
            max_workers = int(MAX_WORKERS_TS)
            print(f"Using {max_workers} parallel workers")
        except Exception as e:
            print(f"Error getting max workers from config: {e}")
            max_workers = 3
            print(f"Using fallback of {max_workers} workers")

        # Limit max workers to a reasonable number
        max_workers = min(max_workers, 10)

        # Process rows in parallel with ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all rows for processing
            future_to_row = {executor.submit(process_row, row): row for _, row in shuffled_df.iterrows()}

            # Initialize results list
            all_results = []
            processed_count = 0
            total_count = len(future_to_row)

            # Process completed futures as they complete
            for future in as_completed(future_to_row):
                processed_count += 1
                print(f"Processing result {processed_count}/{total_count} ({processed_count/total_count*100:.1f}%)")

                try:
                    # Get result from future
                    result = future.result()

                    # Process result based on type
                    if isinstance(result, list):
                        # Multiple top stories (list of dictionaries)
                        for single_result in result:
                            # Ensure all values are properly formatted
                            flattened_result = {
                                'keyword': single_result.get('keyword', 'No Data'),
                                'language': single_result.get('language', 'No Data'),
                                'country': single_result.get('country', 'No Data'),
                                'titles': single_result.get('titles', 'No Data'),
                                'urls': single_result.get('urls', 'No Data'),
                                'sources': single_result.get('sources', 'No data'),
                                'published': single_result.get('published', 'No Data'),
                                'device': single_result.get('device', 'No Data'),
                                'ts_position': single_result.get('ts_position', 0),
                                'timestamp': single_result.get('timestamp', datetime.now().strftime('%d-%m-%Y %H:%M:%S'))
                            }
                            all_results.append(flattened_result)
                    elif isinstance(result, dict):
                        # Single result (dictionary)
                        # Handle potential nested lists by extracting first element if needed
                        flattened_result = {}

                        # Process each field, handling potential list values
                        for key in ['keyword', 'language', 'country', 'titles', 'urls', 'published', 'device']:
                            value = result.get(key, 'No Data')
                            flattened_result[key] = value[0] if isinstance(value, list) else value

                        # Special handling for sources
                        sources = result.get('sources', 'No data')
                        flattened_result['sources'] = sources[0] if isinstance(sources, list) else sources

                        # Handle numeric fields
                        ts_position = result.get('ts_position', 0)
                        flattened_result['ts_position'] = ts_position[0] if isinstance(ts_position, list) else ts_position

                        # Handle timestamp
                        timestamp = result.get('timestamp', datetime.now().strftime('%d-%m-%Y %H:%M:%S'))
                        flattened_result['timestamp'] = timestamp[0] if isinstance(timestamp, list) else timestamp

                        all_results.append(flattened_result)
                    else:
                        print(f"Unexpected result type: {type(result)}")

                except Exception as e:
                    print(f"Error processing future: {e}")
                    traceback.print_exc()

            # Create DataFrame from all results
            if all_results:
                results_df = pd.DataFrame(all_results)
                print(f"Created DataFrame with {len(results_df)} results")
            else:
                print("No results found, creating empty DataFrame")
                results_df = pd.DataFrame(columns=[
                    'keyword', 'language', 'country', 'titles', 'urls',
                    'sources', 'published', 'device', 'ts_position', 'timestamp'
                ])

    except Exception as e:
        print(f"Error in process_all_rows: {e}")
        traceback.print_exc()
        # Create empty DataFrame as fallback
        results_df = pd.DataFrame(columns=[
            'keyword', 'language', 'country', 'titles', 'urls',
            'sources', 'published', 'device', 'ts_position', 'timestamp'
        ])

def main():
    """
    Main function to run the scraper.

    This function:
    1. Processes all rows from the input DataFrame
    2. Saves results to a CSV file
    3. Processes the CSV file with the ts_published_hourly module
    4. Updates the database and removes the CSV file
    5. Cleans up resources
    """
    try:
        print("Starting parallel processing of search queries...")
        # Process all rows in parallel
        process_all_rows()

        # Create the output folder if it doesn't exist
        output_folder = os.path.join('ts', 'topstories', 'CSV_results')
        os.makedirs(output_folder, exist_ok=True)

        # Generate timestamp for unique filename
        timestamp = datetime.now().strftime('%d-%m-%Y_%H-%M-%S')
        output_file = os.path.join(output_folder, f'all_data-{timestamp}.csv')

        # Save results to CSV
        try:
            results_df.to_csv(output_file, index=False)
            print(f"Results saved to '{output_file}'")
        except Exception as e:
            print(f"Error saving results to CSV: {e}")
            traceback.print_exc()

        # Close database connection
        try:
            cursor.close()
            connection.close()
            print("Database connection closed")
        except Exception as e:
            print(f"Error closing database connection: {e}")

        # Process CSV file
        try:
            print("Processing CSV file...")
            process_csv(output_file)
        except Exception as e:
            print(f"Error processing CSV file: {e}")
            traceback.print_exc()

        # Update database and remove CSV
        try:
            print("Updating database and removing CSV...")
            update_db_remove_csv()
        except Exception as e:
            print(f"Error updating database or removing CSV: {e}")
            traceback.print_exc()

        # Close all Chromium browsers
        try:
            print("Closing all Chromium browsers...")
            subprocess.run(['python', '-m', 'close_chrome'])
            subprocess.run(['killall', '/opt/google/chrome/chrome'])
        except Exception as e:
            print(f"Error closing browsers: {e}")

        # Calculate and print total execution time
        end = time.time()
        total = end - start
        print(f"Total execution time: {total:.2f} seconds")
        print(f"Average time per query: {total/len(shuffled_df):.2f} seconds")

    except Exception as e:
        print(f"Error in main function: {e}")
        traceback.print_exc()

        # Attempt to close database connection in case of error
        try:
            cursor.close()
            connection.close()
        except:
            pass

# Run the main function if this script is executed directly
if __name__ == "__main__":
    main()
