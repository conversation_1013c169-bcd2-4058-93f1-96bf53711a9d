import os
import random
import subprocess
import time
import schedule
from database.scraped_data import update_db_remove_csv
from database.scraped_data import get_database_connection

# Specify the path to the ts_scraper_old.py script
script_path = "ts/topstories/ts_scraper.py"

# Specify the directory where CSV files are generated
csv_directory = "ts/topstories/CSV_results"


def making_the_csv_file():
    if not os.path.exists(csv_directory):
        os.makedirs(csv_directory)
    i = 0
    if not os.path.exists(csv_directory):
        os.makedirs(csv_directory)
    initial_csv_count = len([file for file in os.listdir(csv_directory) if file.endswith(".csv")])
    print('CURRENT CSV COUNT:')
    print(initial_csv_count)
    print('\n')
    while True:
        # Execute the script
        subprocess.run(["python", script_path])

        # Check the current count of CSV files
        current_csv_count = len([file for file in os.listdir(csv_directory) if file.endswith(".csv")])

        # Check if a new CSV file has been generated
        if current_csv_count > initial_csv_count:
            print("New CSV file generated.")
            update_db_remove_csv()
            break
        else:
            print("No new CSV file. Waiting for changes...")

        # Update the initial count for the next iteration
        initial_csv_count = current_csv_count

        # Wait for a while before checking again
        time.sleep(random.randint(3, 57))  # Wait for 60 seconds before checking again (adjust as needed)
        i += 1

    print(f"It took {i} executions to make a new file.")


def check_keep_running():
    # Assume that get_database_connection returns a valid database connection object
    connection = get_database_connection()

    # Assuming there is a table named 'Configuration' with a column 'keep_running'
    cursor = connection.cursor()
    cursor.execute("SELECT keep_running FROM Configuration LIMIT 1")
    result = cursor.fetchone()
    print(f'RESULT IS {result}')
    print('\n')

    if result and result[0] == 1:
        return True
    else:
        return False


if check_keep_running():
# Run the making_the_csv_file function immediately when the script starts
    making_the_csv_file()

# Schedule the task to run every hour
schedule.every().hour.do(making_the_csv_file)

# Continuously run the scheduled tasks
while True:
    if check_keep_running():
        schedule.run_pending()
        time.sleep(1)
    else:
        print('KEEP RUNNING IS 0')
