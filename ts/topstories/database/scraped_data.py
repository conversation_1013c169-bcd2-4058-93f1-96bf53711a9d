import pandas as pd
import mysql.connector
from datetime import datetime
import os
from decouple import config

def get_database_connection():
    try:
        conn = mysql.connector.connect(
            user=config('DATABASE_TOPSTORIES_USER'),
            password=config('DATABASE_TOPSTORIES_PASSWORD'),
            host=config('DATABASE_TOPSTORIES_HOST'),
            port=config('DATABASE_TOPSTORIES_PORT'),
            database=config('DATABASE_TOPSTORIES_NAME'),
        )
        print('connection successful')
        return conn
    except mysql.connector.Error as e:
        print(f"Error connecting to the database: {e}")
        return None

def clean_data_entry(entry):
    """
    Clean data entry by removing brackets and handling list-like strings
    """
    if entry is None or pd.isna(entry):
        return None
    
    if isinstance(entry, str):
        # Remove brackets and strip
        entry = entry.strip('[]\'\"')
    
    # Convert numeric positions to integers
    if isinstance(entry, str) and entry.startswith('[') and entry.endswith(']'):
        try:
            entry = int(entry.strip('[]'))
        except ValueError:
            entry = None
    
    return entry

def parse_timestamp(timestamp_str):
    """
    Parse timestamp with robust handling of different formats and list-like strings
    """
    # Clean the timestamp first
    timestamp_str = clean_data_entry(timestamp_str)
    
    if timestamp_str is None:
        return None
    
    # Try multiple timestamp formats
    timestamp_formats = [
        '%d-%m-%Y %H:%M:%S',  # Original format
        '%Y-%m-%d %H:%M:%S',  # Alternative format
        '%d-%m-%Y_%H-%M-%S'   # Another possible format
    ]
    
    for fmt in timestamp_formats:
        try:
            return datetime.strptime(timestamp_str.strip(), fmt).strftime('%Y-%m-%d %H:%M:%S')
        except ValueError:
            continue
    
    print(f"Warning: Could not parse timestamp: {timestamp_str}")
    return None

def insert_data(csv_to_read):
    db_connection = get_database_connection()
    db_cursor = db_connection.cursor()
    csv_path = f'ts/topstories/CSV_results/{csv_to_read}'
    scraped_data_df = pd.read_csv(csv_path)
    data_to_insert = []
    
    for _, row in scraped_data_df.iterrows():
        # Clean and parse each column
        cleaned_row = {
            'keyword': clean_data_entry(row['keyword']),
            'language': clean_data_entry(row['language']),
            'country': clean_data_entry(row['country']),
            'titles': clean_data_entry(row['titles']),
            'urls': clean_data_entry(row['urls']),
            'sources': clean_data_entry(row['sources']),
            'published': clean_data_entry(row['published']),
            'device': clean_data_entry(row['device']),
            'ts_position': clean_data_entry(row['ts_position']),
            'published_hourly': clean_data_entry(row['published_hourly']),
            'timestamp': parse_timestamp(row['timestamp'])
        }
        
        # Convert to tuple, maintaining order
        row_data = [
            cleaned_row['keyword'],
            cleaned_row['language'],
            cleaned_row['country'],
            cleaned_row['titles'],
            cleaned_row['urls'],
            cleaned_row['sources'],
            cleaned_row['published'],
            cleaned_row['device'],
            cleaned_row['ts_position'],
            cleaned_row['published_hourly'],
            cleaned_row['timestamp']
        ]

        # Only add non-empty rows
        if any(row_data):
            data_to_insert.append(tuple(row_data))

    # Only insert if there's data
    if data_to_insert:
        insert_query = "INSERT INTO ScrapedData (keyword, language, country, titles, urls, sources, published, device, ts_position, published_hourly, timestamp) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
        db_cursor.executemany(insert_query, data_to_insert)

        db_connection.commit()
    else:
        print("No data to insert")

    db_cursor.close()
    db_connection.close()

def update_db_remove_csv():
    CSVs = []
    csv_directory = 'ts/topstories/CSV_results'
    for filename in os.listdir(csv_directory):
        if filename.endswith('.csv'):
            print(filename)
            CSVs.append(filename)
    print(CSVs)

    for i in CSVs:
        print(f"doing {i}")
        insert_data(i)
        print(f"done {i}")
        csv_to_remove = f'ts/topstories/CSV_results/{i}'
        os.remove(csv_to_remove)
        print(f"removed {i}")
        print('\n')
    print('Done')
