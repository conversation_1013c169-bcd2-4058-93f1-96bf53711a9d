import pandas as pd
import mysql.connector

# Replace these with your MySQL database credentials
db_username = 'root'
db_password = '123456789'
db_host = 'localhost'
db_port = '3306'
db_name = 'topstories'

# Read the input CSV data
input_csv_file = '/home/<USER>/Work/Work Projects/TopStoriesFinal/input_files/Claus.csv'
input_df = pd.read_csv(input_csv_file)

# Connect to the database
db_connection = mysql.connector.connect(
    user=db_username,
    password=db_password,
    host=db_host,
    port=db_port,
    database=db_name
)

# Create the cursor to execute queries
db_cursor = db_connection.cursor()

# Insert data into the InputSearchTargets table with the 'active' column set to 1 (True)
for _, row in input_df.iterrows():
    keyword = row['keyword']
    language = row['language']
    country = row['country']

    insert_query = "INSERT INTO InputSearchTargets (keyword, language, country, active) VALUES (%s, %s, %s, 1)"
    values = (keyword, language, country)

    db_cursor.execute(insert_query, values)

# Commit the changes and close the connection
db_connection.commit()
db_cursor.close()
db_connection.close()

print("Input data inserted into the InputSearchTargets table successfully.")
