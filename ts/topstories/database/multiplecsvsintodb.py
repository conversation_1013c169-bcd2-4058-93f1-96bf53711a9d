import os
import pandas as pd
import mysql.connector
from datetime import datetime

# MySQL database credentials
db_username = 'root'
db_password = '123456789'
db_host = 'localhost'
db_port = '3306'
db_name = 'topstories'

# Directory containing CSV files
csv_directory = '/home/<USER>/Work/Work Projects/TopStoriesFinal/CSV_results'
for filename in os.listdir(csv_directory):
    if filename.endswith('.csv'):
        print(filename)


# Connect to the database
db_connection = mysql.connector.connect(
    user=db_username,
    password=db_password,
    host=db_host,
    port=db_port,
    database=db_name
)

# Create a cursor to execute queries
db_cursor = db_connection.cursor()

# Function to convert timestamp to MySQL-compatible format
def convert_timestamp(timestamp_str):
    if not pd.isna(timestamp_str):
        return datetime.strptime(timestamp_str, '%d-%m-%Y %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
    else:
        return None

# Iterate through CSV files in the directory
for filename in os.listdir(csv_directory):
    if filename.endswith('.csv'):
        csv_file_path = os.path.join(csv_directory, filename)
        scraped_data_df = pd.read_csv(csv_file_path)

        # Prepare the data for insertion
        data_to_insert = []
        for _, row in scraped_data_df.iterrows():
            keyword = row['keyword']
            language = row['language']
            country = row['country']
            titles = row['titles']
            urls = row['urls']
            sources = row['sources']
            published = row['published']
            timestamp_str = row['timestamp']

            timestamp = convert_timestamp(timestamp_str)

            data_to_insert.append((keyword, language, country, titles, urls, sources, published, timestamp))

        # Insert data into the ScrapedData table
        insert_query = "INSERT INTO ScrapedData (keyword, language, country, titles, urls, sources, published, timestamp) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"
        db_cursor.executemany(insert_query, data_to_insert)

        # Commit the changes for each CSV file
        db_connection.commit()

# Close the database connection
db_cursor.close()
db_connection.close()

print("All scraped data from CSV files inserted into the ScrapedData table successfully.")
