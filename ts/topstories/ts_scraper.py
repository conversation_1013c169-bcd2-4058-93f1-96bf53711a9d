import os
import asyncio
import aiohttp
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
from urllib.parse import urlparse, parse_qs
import time
from aiolimiter import AsyncLimiter
import base64
from random import choice
import mysql.connector
from database.scraped_data import get_database_connection
import ssl
from ts_published_hourly import process_csv
from database.scraped_data import update_db_remove_csv
import json
import ssl

ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

DEBUG_TS = False


start = time.time()

connection = get_database_connection()

# Create a MySQL cursor
cursor = connection.cursor()

# Execute a SELECT query
table_name = 'InputSearchTargets'  # Replace 'your_table_name' with the actual table name
query = f'SELECT * FROM {table_name};'
cursor.execute(query)

# Fetch all rows
rows = cursor.fetchall()

# Get column names from the cursor description
column_names = [desc[0] for desc in cursor.description]

# Create a DataFrame from the fetched data
input_df = pd.DataFrame(rows, columns=column_names)
shuffled_df = input_df.sample(frac=1).reset_index(drop=True)
if DEBUG_TS:
    print(input_df)
    print(shuffled_df)

# Close the cursor and connection
cursor.close()
connection.close()


#? ---------------------
with open('.secure_files/useragents.txt') as file:
    user_agents = [line.rstrip() for line in file]
    if DEBUG_TS:
        print(type(user_agents))



# Create a folder to store the CSV results
output_folder = 'ts/topstories/CSV_results'
os.makedirs(output_folder, exist_ok=True)

# Configure the rate limit for concurrent requests
rate_limit = 18  # Maximum X concurrent requests at a time


async def scrape_data_with_retry(limiter, keyword, language, country):
    """
    Asynchronously attempts to scrape data with automatic retries upon failure.

    This function continuously tries to scrape data using the provided parameters,
    with a retry mechanism in case of specific exceptions. If a 429 Too Many 
    Requests error occurs, the function will wait for a short period before retrying.
    Other exceptions will also trigger a retry after a brief pause.

    Args:
        limiter: An object that limits the rate of requests (e.g., a rate limiter).
        keyword (str): The keyword to be used in the scraping operation.
        language (str): The language to be used in the scraping operation.
        country (str): The country code to be used in the scraping operation.

    Returns:
        The result of the `scrape_data` function if successful.

    Raises:
        The function will re-raise exceptions other than aiohttp.ClientResponseError with status 429
        after retry attempts.
    """
    while True:
        try:
            return await scrape_data(limiter, keyword, language, country)
        except aiohttp.ClientResponseError as e:
            if e.status == 429:
                if DEBUG_TS:
                    print("Too Many Requests (429). Retrying...")
                await asyncio.sleep(10)  # Wait for a few seconds before retrying
                continue
            else:
                print(f"scrape data with retry-{e}")
        except Exception as e:
            print(f"Except - {e}")
            await asyncio.sleep(10)  # Wait for a few seconds before retrying
            continue

            


async def scrape_data(limiter, keyword, language, country):
    """
    Asynchronously scrapes Google search results for a given keyword, language, and country,
    using a rotating proxy and user-agent. Extracts and returns top stories and related data.

    The function constructs a Google search URL with the specified keyword, language, and 
    country, and makes an HTTP request using the aiohttp library. It uses a proxy service 
    for the request, which is authenticated using credentials stored in a secure file. 
    The function parses the HTML response to extract relevant top stories information such as 
    titles, URLs, sources, publication times, and device type (mobile or desktop).

    If no relevant data is found, the function returns a DataFrame with "No Data" placeholders.

    Args:
        limiter: A rate-limiter object that controls the frequency of requests.
        keyword (str): The search keyword for scraping.
        language (str): The language code (e.g., 'en') for the search.
        country (str): The country code (e.g., 'US') to filter search results.

    Returns:
        pd.DataFrame: A DataFrame containing the scraped data with columns:
            - "keyword": The search keyword.
            - "language": The language code.
            - "country": The country code.
            - "titles": Titles of the top stories or "No Data".
            - "urls": URLs of the top stories or "No Data".
            - "sources": News sources of the top stories or "No Data".
            - "published": Publication times of the top stories or "No Data".
            - "device": The type of device used ('Mobile' or 'Desktop').
            - "ts_position": The position of the top story in the list (starts from 1) or 0 if "No Data".
            - "timestamp": The timestamp when the data was scraped.

    Raises:
        aiohttp.ClientResponseError: If the HTTP request fails with a client response error.
    """

    url = f"https://www.google.com/search?q={keyword}&hl={language}&gl={country}"
    if DEBUG_TS:
        print(url)
    ua = choice(user_agents)
    if DEBUG_TS:
        print(ua)
    headers = {
        'User-Agent': ua,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
    }

    smart_proxy_json_path = ".secure_files/smartproxy_credentials.json"

    with open(smart_proxy_json_path, "r") as f:
        smart_proxy_credentials = json.load(f)

    username = smart_proxy_credentials["username"]
    password = smart_proxy_credentials["password"]
    proxy_port = smart_proxy_credentials["proxy_port"]

    proxy_auth = aiohttp.BasicAuth(username, password)
    proxy = f"http://{username}:{password}@gate.smartproxy.com:{proxy_port}"

    if DEBUG_TS:
        print(proxy)
    proxies = {
        'http': proxy,
        'https': proxy
    }

    # Create the basic authentication header
    auth_header = base64.b64encode(f'{username}:{password}'.encode()).decode('ascii')
    headers['Proxy-Authorization'] = f'Basic {auth_header}'

    async with limiter:  # Enforce rate limit for concurrent requests
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60), connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
            async with session.get(url, headers=headers, proxy=proxy, proxy_auth=proxy_auth) as response:
                response.raise_for_status()  # Raise an exception if the request fails
                html = await response.text()

    soup = BeautifulSoup(html, 'html.parser')
    
    top_stories_div_name = 'Xdlr0d'

    top_stories_divs = soup.find_all("div", {"class": top_stories_div_name})
    if DEBUG_TS:
        print(top_stories_divs)

    # Find the first div that contains the desired data
    selected_div = None
    for div in top_stories_divs:
        titles_span = div.find_all("span", class_="rQMQod Xb5VRe")
        source_published_div = div.find_all("div", class_="BNeawe tAd8D AP7Wnd")
        if titles_span and source_published_div:
            selected_div = div
            break

    if DEBUG_TS:
        print(selected_div)

    if any(keyword in ua.lower() for keyword in ['mobile', 'android', 'iphone']):
        device = 'Mobile'
    else:
        device = 'Desktop'

    if DEBUG_TS:
        print(device)

    

    if selected_div is None:
        # Return empty DataFrame with "No Data" placeholders
        data = {
            "keyword": [keyword],
            "language": [language],
            "country": [country],
            "titles": ["No Data"],
            "urls": ["No Data"],
            "sources": ["No Data"],
            "published": ["No Data"],
            "device": [device],
            "ts_position": [0],  # Set position to 0 for 'No data' keywords
            "timestamp": [datetime.now().strftime('%d-%m-%Y %H:%M:%S')]
        }
    else:
        titles_span = selected_div.find_all("span", class_="rQMQod Xb5VRe")
        titles = [span.get_text() for span in titles_span]
        position = 0
        for idx, title in enumerate(titles):
            if keyword in title:
                position = idx + 1
                break

        links_a_class_name = 'BVG0Nb'
        links_hrefs = selected_div.find_all("a", class_=links_a_class_name)
        hrefs = [link.get("href") for link in links_hrefs]

        urls = []
        for href in hrefs:
            parsed_url = urlparse(href)
            query_params = parse_qs(parsed_url.query)
            url = query_params.get('url', [''])[0]
            if url:
                urls.append(url)
            else:
                # Extract URL using alternative method
                url = query_params.get('q', [''])[0]
                urls.append(url)

        source_published_texts = selected_div.find_all("div", class_="BNeawe tAd8D AP7Wnd")
        texts = [div.get_text() for div in source_published_texts]
        sources = []
        published = []
        for text in texts:
            parts = text.split('\n')
            if len(parts) == 2:
                source, publish_time = parts
                sources.append(source)
                published.append(publish_time)

        timestamps = [datetime.now().strftime('%d-%m-%Y %H:%M:%S')] * len(titles)

        # Check if all arrays have the same length
        if len(titles) == len(urls) == len(sources) == len(published):
            data = {
                "keyword": [keyword] * len(titles),
                "language": [language] * len(titles),
                "country": [country] * len(titles),
                "titles": titles,
                "urls": urls,
                "sources": sources,
                "published": published,
                "device": [device] * len(titles),  # Add device column with appropriate value for each row
                "ts_position": list(range(1, len(titles) + 1)),  # Position starts from 1
                "timestamp": timestamps  # New single timestamp column
            }
        else:
            # Return empty DataFrame with "No Data" placeholders
            data = {
                "keyword": [keyword],
                "language": [language],
                "country": [country],
                "titles": ["No Data"],
                "urls": ["No Data"],
                "sources": ["No Data"],
                "published": ["No Data"],
                "device": [device],
                "ts_position": [0],  # Set position to 0 for 'No data' keywords
                "timestamp": [datetime.now().strftime('%d-%m-%Y %H:%M:%S')]
            }
    if DEBUG_TS:
        print(data['ts_position'])
    df = pd.DataFrame(data)


    return df


async def main():
    """
    Main function to orchestrate the asynchronous scraping of data for multiple keywords.

    This function performs the following tasks:
    1. Initializes an asynchronous rate limiter to control the frequency of requests.
    2. Iterates over each row in the input DataFrame (`input_df`), extracting the keyword, 
       language, and country for each row.
    3. Creates an asynchronous task to scrape data for each keyword using the `scrape_data_with_retry` 
       function, with automatic retries on failure.
    4. Gathers all the tasks and concatenates the results into a single DataFrame.
    5. Saves the concatenated DataFrame to a CSV file. If the output file already exists, the function 
       appends the data to it; otherwise, it creates a new CSV file.
    6. If a new CSV file is created, it processes the file further using the `process_csv` function 
       and prints a message indicating the successful generation of the output file.

    Returns:
        None
    """

    tasks = []
    limiter = AsyncLimiter(rate_limit, 1)
    for index, row in shuffled_df.iterrows():
        keyword = row['keyword']
        language = row['language']
        country = row['country']
        task = asyncio.create_task(scrape_data_with_retry(limiter, keyword, language, country))
        tasks.append(task)

    dfs = await asyncio.gather(*tasks)
    result_df = pd.concat(dfs, ignore_index=True)

    timestamp = datetime.now().strftime('%d-%m-%Y_%H-%M-%S')
    output_file = os.path.join(output_folder, f'all_data-{timestamp}.csv')

    # Check if the output file already exists
    if os.path.exists(output_file):
        # Append to the existing CSV file
        result_df.to_csv(output_file, mode='a', index=False, header=False)
    else:
        # Create a new CSV file
        result_df.to_csv(output_file, index=False)
        process_csv(output_file)
        print(f"Output file '{output_file}' generated for all keywords")

try:
    asyncio.run(main())
    update_db_remove_csv()
except Exception as e:
    print("Error occurred during request:", e)

end = time.time()

total = end - start
print(f"Total time: {total:.2f} seconds")
