#!/usr/bin/env python3
"""
Test script to verify database connection to staging
"""

import os
import sys
import mysql.connector

# Add the parent directory to the path so we can import utilitiesTS
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database connection function from utilitiesTS
from ts.utilitiesTS import get_database_connection, close_connection

def test_database_connection():
    """Test the database connection to staging"""
    print("\n\n==== TESTING DATABASE CONNECTION ====\n")
    
    # Get a database connection
    conn = get_database_connection(caller='test_script')
    
    if conn:
        print("Database connection successful!")
        
        # Test a simple query
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM ScrapedData")
        result = cursor.fetchone()
        print(f"Total records in ScrapedData: {result[0]}")
        
        # Test a query to get available dates
        cursor.execute("SELECT DISTINCT DATE(timestamp) as available_date FROM ScrapedData WHERE timestamp IS NOT NULL ORDER BY available_date DESC LIMIT 5")
        dates = cursor.fetchall()
        print(f"Latest available dates: {[str(date[0]) for date in dates]}")
        
        # Close the connection
        close_connection(conn, caller='test_script')
        print("Database connection closed")
    else:
        print("Database connection failed!")

if __name__ == "__main__":
    test_database_connection()
