#!/usr/bin/env python3
"""
Test script to check data in the staging database
"""

import os
import sys
import pandas as pd
import mysql.connector

# Add the parent directory to the path so we can import utilitiesTS
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database connection function from utilitiesTS
from ts.utilitiesTS import get_database_connection, close_connection

def test_data():
    """Test data in the staging database"""
    print("\n\n==== TESTING DATABASE DATA ====\n")
    
    # Get a database connection
    conn = get_database_connection(caller='test_data')
    
    if conn:
        print("Database connection successful!")
        
        # Check top keywords
        print("\nTop 10 Keywords:")
        query = """
        SELECT keyword, COUNT(*) as count
        FROM ScrapedData
        GROUP BY keyword
        ORDER BY count DESC
        LIMIT 10
        """
        df_keywords = pd.read_sql(query, conn)
        print(df_keywords)
        
        # Check Premier League data
        print("\nPremier League Data:")
        query = """
        SELECT COUNT(*) as count
        FROM ScrapedData
        WHERE keyword = 'Premier League'
        """
        df_premier = pd.read_sql(query, conn)
        print(f"Premier League count: {df_premier['count'].iloc[0]}")
        
        # Check top countries
        print("\nTop 10 Countries:")
        query = """
        SELECT country, COUNT(*) as count
        FROM ScrapedData
        GROUP BY country
        ORDER BY count DESC
        LIMIT 10
        """
        df_countries = pd.read_sql(query, conn)
        print(df_countries)
        
        # Check top languages
        print("\nTop 10 Languages:")
        query = """
        SELECT language, COUNT(*) as count
        FROM ScrapedData
        GROUP BY language
        ORDER BY count DESC
        LIMIT 10
        """
        df_languages = pd.read_sql(query, conn)
        print(df_languages)
        
        # Close the connection
        close_connection(conn, caller='test_data')
        print("\nDatabase connection closed")
    else:
        print("Database connection failed!")

if __name__ == "__main__":
    test_data()
