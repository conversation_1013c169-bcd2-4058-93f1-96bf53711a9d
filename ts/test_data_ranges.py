#!/usr/bin/env python3
"""
Test script to check data availability for different date ranges
"""

import os
import sys
import pandas as pd
import mysql.connector
from datetime import datetime, timedel<PERSON>

# Add the parent directory to the path so we can import utilitiesTS
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database connection function from utilitiesTS
from ts.utilitiesTS import get_database_connection, close_connection

def test_data_ranges():
    """Test data availability for different date ranges"""
    print("\n\n==== TESTING DATA RANGES ====\n")
    
    # Get a database connection
    conn = get_database_connection(caller='test_ranges')
    
    if conn:
        print("Database connection successful!")
        
        # Get available dates
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT DATE(timestamp) as available_date FROM ScrapedData WHERE timestamp IS NOT NULL ORDER BY available_date DESC")
        available_dates = [row[0] for row in cursor.fetchall()]
        
        print(f"Available dates in database: {available_dates[:10]} (showing first 10)")
        
        # Check data for the most recent date
        latest_date = available_dates[0]
        print(f"\nChecking data for latest date: {latest_date}")
        
        # Get top keywords for the latest date
        cursor.execute("""
            SELECT keyword, COUNT(*) as count
            FROM ScrapedData
            WHERE DATE(timestamp) = %s
            GROUP BY keyword
            ORDER BY count DESC
            LIMIT 10
        """, (latest_date,))
        
        top_keywords_latest = cursor.fetchall()
        print(f"Top keywords for {latest_date}:")
        for keyword, count in top_keywords_latest:
            print(f"  - {keyword}: {count}")
            
        # Check data for a date 7 days ago
        if len(available_dates) > 7:
            older_date = available_dates[7]
            print(f"\nChecking data for older date: {older_date}")
            
            # Get top keywords for the older date
            cursor.execute("""
                SELECT keyword, COUNT(*) as count
                FROM ScrapedData
                WHERE DATE(timestamp) = %s
                GROUP BY keyword
                ORDER BY count DESC
                LIMIT 10
            """, (older_date,))
            
            top_keywords_older = cursor.fetchall()
            print(f"Top keywords for {older_date}:")
            for keyword, count in top_keywords_older:
                print(f"  - {keyword}: {count}")
        
        # Check data for Premier League specifically
        print("\nChecking data for 'Premier League' keyword:")
        cursor.execute("""
            SELECT DATE(timestamp) as date, COUNT(*) as count
            FROM ScrapedData
            WHERE keyword = 'Premier League'
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
            LIMIT 10
        """)
        
        premier_league_data = cursor.fetchall()
        if premier_league_data:
            print("Premier League data by date:")
            for date, count in premier_league_data:
                print(f"  - {date}: {count}")
        else:
            print("No data found for Premier League keyword")
        
        # Check country distribution
        print("\nChecking country distribution:")
        cursor.execute("""
            SELECT country, COUNT(*) as count
            FROM ScrapedData
            WHERE country IS NOT NULL AND country != ''
            GROUP BY country
            ORDER BY count DESC
            LIMIT 10
        """)
        
        country_data = cursor.fetchall()
        print("Top countries:")
        for country, count in country_data:
            print(f"  - {country}: {count}")
        
        # Close the connection
        close_connection(conn, caller='test_ranges')
        print("\nDatabase connection closed")
    else:
        print("Database connection failed!")

if __name__ == "__main__":
    test_data_ranges()
