from django import forms

class TriggerScriptForm(forms.Form):
    INTERVAL_CHOICES = [
        (15, '15 minutes'),
        (30, '30 minutes'),
        (60, '1 hour'),
    ]

    interval = forms.ChoiceField(choices=INTERVAL_CHOICES)



class DynamicRowForm(forms.Form):
    # This form will be dynamically generated for each row

    def __init__(self, *args, **kwargs):
        columns = kwargs.pop('columns')
        super(DynamicRowForm, self).__init__(*args, **kwargs)
        for column in columns:
            self.fields[column] = forms.CharField(required=False)
