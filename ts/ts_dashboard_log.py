import logging
import os
import datetime
from functools import wraps
import time
import traceback
import json
import inspect

# Create logs directory if it doesn't exist
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# Configure logger
log_file = os.path.join(log_dir, 'ai_dashboard.log')
logger = logging.getLogger('ai_dashboard')
logger.setLevel(logging.DEBUG)

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)

# Create console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add handlers to logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def log_function_call(func):
    """Decorator to log function calls, arguments, and execution time"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        module_name = func.__module__
        
        # Log function call with arguments
        arg_str = ', '.join([repr(a) for a in args])
        kwarg_str = ', '.join([f"{k}={repr(v)}" for k, v in kwargs.items()])
        all_args = f"{arg_str}{', ' if arg_str and kwarg_str else ''}{kwarg_str}"
        
        logger.info(f"CALL: {module_name}.{func_name}({all_args})")
        
        # Measure execution time
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"RETURN: {module_name}.{func_name} completed in {execution_time:.4f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"ERROR: {module_name}.{func_name} failed after {execution_time:.4f}s: {str(e)}")
            logger.error(f"TRACEBACK: {traceback.format_exc()}")
            raise
    return wrapper

def log_db_query(query, params=None):
    """Log database queries"""
    logger.debug(f"DB QUERY: {query}")
    if params:
        logger.debug(f"DB PARAMS: {params}")

def log_data(data_name, data):
    """Log data structures (truncated for large data)"""
    try:
        if isinstance(data, (list, dict)):
            data_sample = data
            is_truncated = False
            
            # Truncate large data structures
            if isinstance(data, list) and len(data) > 10:
                data_sample = data[:10]
                is_truncated = True
            
            # Convert to JSON string with indentation
            data_str = json.dumps(data_sample, indent=2, default=str)
            
            if is_truncated:
                data_str += f"\n... (truncated, total length: {len(data)})"
                
            logger.debug(f"DATA ({data_name}): {data_str}")
        else:
            logger.debug(f"DATA ({data_name}): {str(data)}")
    except Exception as e:
        logger.error(f"Failed to log data {data_name}: {str(e)}")

def log_error(error_message, exception=None):
    """Log error messages with optional exception details"""
    if exception:
        logger.error(f"ERROR: {error_message} - {str(exception)}")
        logger.error(f"TRACEBACK: {traceback.format_exc()}")
    else:
        logger.error(f"ERROR: {error_message}")

def get_caller_info():
    """Get information about the caller function"""
    stack = inspect.stack()
    if len(stack) > 2:  # 0 is this function, 1 is the logging function, 2+ is the caller
        caller = stack[2]
        return f"{caller.filename}:{caller.lineno} in {caller.function}"
    return "unknown"

def log_dashboard_event(event_type, details=None):
    """Log dashboard-specific events"""
    caller = get_caller_info()
    if details:
        logger.info(f"DASHBOARD EVENT [{event_type}] from {caller}: {details}")
    else:
        logger.info(f"DASHBOARD EVENT [{event_type}] from {caller}")
