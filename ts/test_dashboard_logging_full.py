#!/usr/bin/env python3
"""
Test script to verify the dashboard logging with full data
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import utilitiesTS
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the database connection function from utilitiesTS
from ts.utilitiesTS import (
    get_database_connection, 
    close_connection,
    log_callback,
    log_db_operation,
    alpha2_to_alpha3
)

def test_dashboard_logging_full():
    """Test the dashboard logging with full data"""
    print("\n\n==== TESTING DASHBOARD LOGGING WITH FULL DATA ====\n")
    
    # Get a database connection
    conn = get_database_connection(caller='test_dashboard_logging_full')
    
    if conn:
        print("Database connection successful!")
        
        # Define date range
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        print(f"Using date range: {start_date} to {end_date}")
        
        # Query to fetch data
        query = "SELECT keyword, language, country, titles, urls, sources, published, timestamp FROM ScrapedData WHERE DATE(timestamp) BETWEEN %s AND %s"
        params = [start_date, end_date]
        
        # Log the main query
        log_db_operation(
            operation='main_dashboard_query',
            query=query,
            params=params,
            additional_info={
                'caller': 'test_dashboard_logging_full'
            }
        )
        
        # Execute the query
        df = pd.read_sql(query, conn, params=params)
        
        # Log the query results
        log_callback(
            callback_name='main_dashboard_query_result',
            inputs={
                'start_date': start_date,
                'end_date': end_date
            },
            outputs={
                'row_count': len(df),
                'column_count': len(df.columns),
                'has_premier_league': 'Premier League' in df['keyword'].values,
                'premier_league_count': int(df[df['keyword'] == 'Premier League'].shape[0]),
                'unique_keywords': int(df['keyword'].nunique()),
                'unique_countries': int(df['country'].nunique()),
                'unique_languages': int(df['language'].nunique()),
                'data_sample': str(df.head(3).to_dict())
            }
        )
        
        # Process data for pie chart
        pie_chart_query = """
            SELECT keyword, language, country, titles, ts_position
            FROM ScrapedData
            WHERE DATE(timestamp) BETWEEN %s AND %s
        """
        pie_chart_params = [start_date, end_date]
        
        # Execute the pie chart query
        pie_chart_df = pd.read_sql(pie_chart_query, conn, params=pie_chart_params)
        
        # Log the raw pie chart data
        log_callback(
            callback_name='pie_chart_data_raw',
            inputs={'date_range': f"{start_date} to {end_date}"},
            outputs={
                'data_shape': str(pie_chart_df.shape),
                'data_sample': str(pie_chart_df.head().to_dict()),
                'query': pie_chart_query,
                'params': str(pie_chart_params)
            }
        )
        
        # Categorize the data for pie chart
        def categorize_story(row):
            if row['ts_position'] == 0:
                return 'Not in Top Stories'
            elif 'No data' in str(row['titles']):
                return 'No Data Available'
            else:
                return 'Has Top Stories'
        
        pie_chart_df['story_status'] = pie_chart_df.apply(categorize_story, axis=1)
        story_presence_counts = pie_chart_df['story_status'].value_counts().to_dict()
        
        # Log the categorized data
        log_callback(
            callback_name='pie_chart_data_categorized',
            inputs={'date_range': f"{start_date} to {end_date}"},
            outputs={
                'story_presence_counts': str(story_presence_counts),
                'total_records': str(sum(story_presence_counts.values()))
            }
        )
        
        # Process data for map visualization
        df['country_alpha_3'] = df['country'].apply(alpha2_to_alpha3)
        country_counts = df['country_alpha_3'].value_counts().reset_index()
        country_counts.columns = ['country', 'count']
        
        # Log the map data
        log_callback(
            callback_name='map_visualization_data',
            inputs={'date_range': f"{start_date} to {end_date}"},
            outputs={
                'country_counts': str(country_counts.to_dict()),
                'unique_countries': str(df['country'].nunique()),
                'total_countries_with_data': str(len(country_counts))
            }
        )
        
        # Get top 10 statistics
        top_10_keywords = df['keyword'].value_counts().nlargest(10)
        top_10_countries = df['country'].value_counts().nlargest(10)
        top_10_languages = df['language'].value_counts().nlargest(10)
        
        # Log the top 10 statistics
        log_callback(
            callback_name='top_10_statistics',
            inputs={'date_range': f"{start_date} to {end_date}"},
            outputs={
                'top_10_keywords': str(top_10_keywords.to_dict()),
                'top_10_countries': str(top_10_countries.to_dict()),
                'top_10_languages': str(top_10_languages.to_dict()),
                'premier_league_count': str(df[df['keyword'] == 'Premier League'].shape[0]),
                'total_records': str(len(df))
            }
        )
        
        # Close the connection
        close_connection(conn, caller='test_dashboard_logging_full')
        print("\nDatabase connection closed")
        
        print("\n==== DASHBOARD LOGGING TESTS COMPLETE ====\n")
        print("Check the logs directory for dashboard_log.log")
    else:
        print("Database connection failed!")

if __name__ == "__main__":
    test_dashboard_logging_full()
