from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.contrib.auth.models import UserManager, AbstractUser
from .models import *


# Register your models here.
class ReadOnlyAdminMixin:

    def has_add_permission(self, request):
        return True

    def has_change_permission(self, request, obj=None):
        print(f"has_change_permission: {request.user.is_superuser}")
        return True

    def has_delete_permission(self, request, obj=None):
        return True

    def has_view_permission(self, request, obj=None):
        return True


@admin.register(Application)
class ApplicationsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("app_name", "id", "app_description", "app_is_active", "receive_alerts")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['app_name'].disabled = True
            form.base_fields['app_description'].disabled = True
            form.base_fields['app_is_active'].disabled = True
            form.base_fields['receive_alerts'].disabled = True
        return form


@admin.register(Domain)
class DomainsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("url", "domain", "logo", "in_use",)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['url'].disabled = True
            form.base_fields['domain'].disabled = True
            form.base_fields['logo'].disabled = True
            form.base_fields['in_use'].disabled = True
        return form


class CustomUserCreationForm(UserCreationForm):
    class Meta:
        model = CustomUser
        fields = (
        'email', 'full_name', 'receive_alerts', 'slack_handle', 'access_all_markets', 'markets', 'user_permissions',
        'groups')


class CustomUserChangeForm(UserChangeForm):
    class Meta:
        model = CustomUser
        fields = ('email', 'full_name', 'receive_alerts', 'slack_handle', 'is_active', 'is_staff', 'is_superuser',
                  'access_all_markets', 'markets', 'user_permissions', 'groups')


class CustomUserAdmin(BaseUserAdmin):
    form = CustomUserChangeForm
    add_form = CustomUserCreationForm

    list_display = (
    'email', 'full_name', 'is_staff', 'is_active', 'slack_handle', 'receive_alerts', 'access_all_markets', 'last_login')
    list_filter = ('is_staff', 'is_active', 'access_all_markets', 'last_login')
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('Personal info', {'fields': ('full_name', 'slack_handle', 'receive_alerts')}),
        ('Permissions',
         {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions', 'access_all_markets')}),
        ('Markets', {'fields': ('markets',)}),
        ('Applications', {'fields': ('apps',)}),
        ('Important dates', {'fields': ('last_login',)}),
        ('Domains', {'fields': ('domains',)}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
            'email', 'password1', 'password2', 'full_name', 'slack_handle', 'receive_alerts', 'is_active', 'is_staff',
            'is_superuser', 'access_all_markets', 'markets', 'user_permissions', 'groups'),
        }),
    )
    search_fields = ('email', 'full_name')
    ordering = ('-last_login',)
    filter_horizontal = ('user_permissions', 'groups', 'markets', 'domains')

    def get_queryset(self, request):
        return super().get_queryset(request).order_by('-last_login')    

    def save_related(self, request, form, formsets, change):
        # Save related groups
        if 'groups' in form.cleaned_data:
            form.instance.groups.set(form.cleaned_data['groups'])

        # Save related user permissions
        if 'user_permissions' in form.cleaned_data:
            form.instance.user_permissions.set(form.cleaned_data['user_permissions'])

        # Save related markets
        if 'markets' in form.cleaned_data:
            form.instance.markets.set(form.cleaned_data['markets'])

        # Save related domains
        if 'domains' in form.cleaned_data:
            form.instance.domains.set(form.cleaned_data['domains'])

        # Save related applications
        if 'apps' in form.cleaned_data:
            form.instance.apps.set(form.cleaned_data['apps'])

        # Call super method at the end to save any remaining formsets
        super().save_related(request, form, formsets, change)


admin.site.register(CustomUser, CustomUserAdmin)


@admin.register(SpeedIndexDifference)
class SpeedIndexDifferenceAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(FirstContentfulPaint)
class FirstContentfulPaintAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(LargestContentfulPaint)
class LargestContentfulPaintAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(TotalBlockingTime)
class TotalBlockingTimeAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(CumulativeLayoutShift)
class CumulativeLayoutShiftAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(RenderBlockingResources)
class RenderBlockingResourcesAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(ServerResponseTime)
class ServerResponseTimeAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(PerformanceScore)
class PerformanceScoreAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("last_added_value",
                    "prev_added_value",
                    "difference",
                    "extraction_date",)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['last_added_value'].disabled = True
            form.base_fields['prev_added_value'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
        return form


@admin.register(MetricTable)
class MetricTableAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("metric_name",
                    "metric_abbr",
                    "metric_unit",
                    "metric_importance",
                    "alert_inclusion",)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['metric_name'].disabled = True
            form.base_fields['metric_abbr'].disabled = True
            form.base_fields['metric_unit'].disabled = True
            form.base_fields['metric_importance'].disabled = True
            form.base_fields['alert_inclusion'].disabled = True
        return form


@admin.register(ThresholdsRanges)
class ThresholdsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("metric_name",
                    "good_val_lower_end",
                    "good_val_high_end",
                    "acceptable_val_low_end",
                    "acceptable_val_high_end",
                    "bad_val_low_end",
                    "bad_val_high_end",
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['metric_name'].disabled = True
            form.base_fields['good_val_lower_end'].disabled = True
            form.base_fields['good_val_high_end'].disabled = True
            form.base_fields['acceptable_val_low_end'].disabled = True
            form.base_fields['acceptable_val_high_end'].disabled = True
            form.base_fields['bad_val_low_end'].disabled = True
            form.base_fields['bad_val_high_end'].disabled = True
        return form


@admin.register(Zones)
class ZoneAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "zone_name",
        "zone_description",
        "zone_active",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['zone_name'].disabled = True
            form.base_fields['zone_description'].disabled = True
            form.base_fields['zone_active'].disabled = True

        return form


@admin.register(PsiAlertingHub)
class PsiAlertingHubAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    search_fields = ('id', 'domain_id__domain', 'domain_id__url', 'metric__metric_name')
    list_display = ("id",
                    "alert_trigger",
                    "previous_val",
                    "last_val",
                    "difference",
                    "extraction_date",
                    "domain_id",
                    "strategy_id",
                    "url_id",
                    "metric",
                    "movement",
                    "movement_flag",
                    "prev_val_dist_high_end",
                    "prev_val_dist_high_end_flag",
                    "prev_val_dist_low_end",
                    "prev_val_dist_low_end_flag",
                    "latest_val_dist_low_end",
                    "latest_val_dist_low_end_flag",
                    "latest_val_dist_high_end",
                    "latest_val_dist_high_end_flag",
                    "percentage"
                    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['difference'].disabled = True
            form.base_fields['extraction_date'].disabled = True
            form.base_fields['percentage'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['url_id'].disabled = True
            form.base_fields['metric'].disabled = True
            form.base_fields['movement'].disabled = True
            form.base_fields['prev_val_dist_high_end'].disabled = True
            form.base_fields['prev_val_dist_low_end'].disabled = True
            form.base_fields['movement_flag'].disabled = True
            form.base_fields['prev_val_dist_high_end_flag'].disabled = True
            form.base_fields['prev_val_dist_low_end_flag'].disabled = True
            form.base_fields['alert_trigger'].disabled = True
        return form


admin.site.register(RangeTote)

admin.site.register(Market)
admin.site.register(Country)


@admin.register(SlackCredentials)
class SlackCredentialsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "slack_api_id",
        "is_active",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['slack_api_id'].disabled = True
            form.base_fields['slack_client_id'].disabled = True
            form.base_fields['slack_client_secret'].disabled = True
            form.base_fields['slack_signin_secret'].disabled = True
            form.base_fields['slack_verification_token'].disabled = True
            form.base_fields['user_oauth_token'].disabled = True
            form.base_fields['bot_user_oauth_token'].disabled = True
            form.base_fields['is_active'].disabled = True

        return form


@admin.register(Alerting_Notes)
class Alerting_NotesAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "alerting_note",
        "metric_name",
        "trend",

    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['alerting_note'].disabled = True
            form.base_fields['metric_name'].disabled = True
            form.base_fields['trend'].disabled = True

        return form


@admin.register(PsiTrends)
class PsiTrendsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "trend_name",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['trend_name'].disabled = True

        return form


@admin.register(AlertCollector)
class AlertCollectorAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "alert_included",
        "final_score",
        "get_url_id",
        "trend_name",
        "date_time",
        "percentage_change",
        "alert_trigger",
        "metric_name",
        "zone_change_score",
        "percentage_change_score",
        "metric_importance_score",
        "trend_score",
    )

    def get_url_id(self, obj):
        return obj.psi_alerting_hub.url_id

    get_url_id.short_description = 'URL ID'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['psi_alerting_hub'].disabled = True
            form.base_fields['final_score'].disabled = True
            form.base_fields['trend_name'].disabled = True
            form.base_fields['date_time'].disabled = True
            form.base_fields['percentage_change'].disabled = True
            form.base_fields['alert_trigger'].disabled = True
            form.base_fields['metric_name'].disabled = True
            form.base_fields['zone_change_score'].disabled = True
            form.base_fields['percentage_change_score'].disabled = True
            form.base_fields['metric_importance_score'].disabled = True
            form.base_fields['trend_score'].disabled = True

        return form


@admin.register(AlertLevel)
class AlertLevelAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "alert_level",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['alert_level'].disabled = True

        return form


@admin.register(SitemapCheckerAlerts)
class SitemapCheckerAlertsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("user_name", "sitemap_checker_alert")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if not request.user.is_superuser:
            form.base_fields['user_name'].disabled = True
            form.base_fields['sitemap_checker_alert'].disabled = True

        form.base_fields['user_name'].queryset = CustomUser.objects.all()
        form.base_fields['user_name'].label_from_instance = lambda obj: obj.dropdown_display()
        return form


@admin.register(StabilityMetricMonitor)
class StabilityMetricMonitorAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        'metric_name',
        'status',
        'stability_index_score',
        'url_id',
        'domain_id',
        'strategy_id',
        'previous_val',
        'prev_val_zone',
        'prev_val_dist_low_end',
        'prev_val_dist_high_end',
        'last_val',
        'last_val_zone',
        'last_val_dist_low_end',
        'last_val_dist_high_end',
        'difference',
        'zone_change',
        'mean_val',
        'std_dev',
        'std_prc',
        'value_deviation',
        'stability_index',
        'week_number',
        'week_day',
        'month',
        'extraction_date',
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser
        if not is_superuser:
            form.base_fields['metric_name'].disabled = True
            form.base_fields['url_id'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['previous_val'].disabled = True
            form.base_fields['prev_val_zone'].disabled = True
            form.base_fields['prev_val_dist_low_end'].disabled = True
            form.base_fields['prev_val_dist_high_end'].disabled = True
            form.base_fields['percentage_change_score'].disabled = True
            form.base_fields['last_val'].disabled = True
            form.base_fields['last_val_zone'].disabled = True
            form.base_fields['last_val_dist_low_end'].disabled = True
            form.base_fields['last_val_dist_high_end'].disabled = True
            form.base_fields['difference'].disabled = True
            form.base_fields['zone_change'].disabled = True
            form.base_fields['mean_val'].disabled = True
            form.base_fields['std_dev'].disabled = True
            form.base_fields['std_prc'].disabled = True
            form.base_fields['value_deviation'].disabled = True
            form.base_fields['stability_index'].disabled = True
            form.base_fields['week_number'].disabled = True
            form.base_fields['week_day'].disabled = True
            form.base_fields['month'].disabled = True
            form.base_fields['extraction_date'].disabled = True

        return form
    