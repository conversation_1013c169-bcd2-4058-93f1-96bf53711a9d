from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404
from psi.models import PsiUrl
from alerts.models import Domain, StabilityMetricMonitor
from psi.forms import CreateNewFormDropdownForPsiWeeklyReport
from alerts.management.utils.data_util import week_getter,  data_dealer, data_prepack, prepare_all_plotly_data
from alerts.management.utils.alert_plots import ahor_bar_plot


from django.utils.timezone import make_aware, make_naive



@login_required(login_url='./login')
def psi_alerts_report(request, id):
    '''Reporting page for Alerts on weekly level'''
    domain = get_object_or_404(Domain, pk=id)
    url_data = PsiUrl.objects.filter(domain=id)
    url_select_form = None
    selected_form_factor = None
    context_data = None

    if request.method == 'POST':
        url_form = CreateNewFormDropdownForPsiWeeklyReport(request.POST, domain_id=id)

        if url_form.is_valid():
            url_select_form = url_form.cleaned_data.get('url_form')
            selected_form_factor = url_form.cleaned_data.get('form_factor')           
            url_id = url_data.filter(url=url_select_form).values_list('id', flat=True).first()
            strategy_id = int(request.POST.get('form_factor'))
            data = StabilityMetricMonitor.objects.filter(
                domain_id=id,
                url_id=url_id, 
                strategy_id=strategy_id,
                extraction_date__date__gte=week_getter().date(),
            )
 
            response_data = data_prepack(data, strategy_id)
            context_data = data_dealer(response_data=response_data, smm=StabilityMetricMonitor)

            if context_data:
                for metric, metric_data in context_data.items():
                    if metric_data.get("data"):
                        dates, differences = metric_data["data"]
                       
                        metric_data['bar_plot'] = ahor_bar_plot(dates, differences, title=metric)
        else:
            url_select_form = None
            selected_form_factor = None    
    else:
        url_form = CreateNewFormDropdownForPsiWeeklyReport(
            initial={"url_form": None, "form_factor": None}, domain_id=id
        )

    context = {
        "data": context_data,
        "domain": domain,
        "url_data": url_data,
        "url_form": url_form,
        "url_select_form": url_select_form,
        "selected_form_factor": selected_form_factor,
    }

    return render(request, 'psi/psi-report-alerts.html', context=context)