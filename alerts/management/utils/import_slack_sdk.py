import slack_sdk
from slack_sdk.errors import SlackApiError
from decouple import config
import tempfile
import os

def test_slack_credentials():
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        response = client.auth_test()
        if response['ok']:
            print("Credentials are valid.")
            print(f"Bot User ID: {response.get('user_id')}")
            print(f"Bot Name: {response.get('user')}")
            print(f"Team: {response.get('team')}")
            return True
        else:
            print("Credentials are invalid.")
            return False

    except SlackApiError as e:
        print(f"Error during authentication: {e.response['error']}")
        return False

def check_bot_permissions_and_channel_access(channel_id):
    """
    Check if bot has proper permissions and channel access
    """
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        # Check bot info
        auth_response = client.auth_test()
        print(f"Bot User ID: {auth_response.get('user_id')}")

        # Check channel info
        channel_info = client.conversations_info(channel=channel_id)
        print(f"Channel Name: {channel_info['channel']['name']}")
        print(f"Channel ID: {channel_info['channel']['id']}")
        print(f"Is Member: {channel_info['channel'].get('is_member', False)}")

        # Check if bot is in channel members
        try:
            members = client.conversations_members(channel=channel_id)
            bot_user_id = auth_response.get('user_id')
            is_bot_member = bot_user_id in members['members']
            print(f"Bot is member of channel: {is_bot_member}")

            if not is_bot_member:
                print("❌ Bot is not a member of this channel!")
                print("To fix this:")
                print("1. Go to the Slack channel")
                print("2. Type: /invite @your_bot_name")
                print("3. Or add the bot through channel settings")

        except SlackApiError as e:
            print(f"Could not check channel members: {e.response['error']}")

        return True

    except SlackApiError as e:
        print(f"Error checking permissions: {e.response['error']}")
        return False


def send_slack_message(recipient_id, message):
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        response = client.chat_postMessage(
            channel=recipient_id,
            text=message
        )
        if response['ok']:
            print("Message sent successfully!")
            return True
        else:
            print("Failed to send message.")
            return False

    except SlackApiError as e:
        print(f"Error sending message: {e.response['error']}")
        return False


def send_csv_as_snippet(recipient_id, csv_content, filename, message=""):
    """
    Send CSV content as a formatted snippet that looks like a file
    This works with basic chat:write permissions
    """
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        # Count lines and size
        lines = csv_content.count('\n')
        size_bytes = len(csv_content.encode('utf-8'))

        # Truncate if too long but show full data when possible
        display_content = csv_content
        truncated = False

        if len(csv_content) > 3000:
            display_content = csv_content[:3000]
            truncated = True

        # Create a file-like snippet message
        snippet_message = {
            "channel": recipient_id,
            "text": f"{message}\n\n📄 {filename}",
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": message
                    }
                },
                {
                    "type": "divider"
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"📄 *{filename}*\n📊 {lines} lines • {size_bytes} bytes"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"```\n{display_content}{'...\n\n[Content truncated - full data available]' if truncated else ''}\n```"
                    }
                }
            ]
        }

        response = client.chat_postMessage(**snippet_message)

        if response['ok']:
            print(f"✅ CSV content sent as formatted snippet successfully!")
            return True
        else:
            print(f"❌ Failed to send snippet: {response}")
            return False

    except SlackApiError as e:
        print(f"❌ Error sending snippet: {e.response['error']}")
        return False

def send_slack_file_simple(recipient_id, file_content, filename, message=""):
    """
    Simple file upload function - tries different approaches
    """
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        # Convert string content to bytes if needed
        if isinstance(file_content, str):
            file_content = file_content.encode('utf-8')

        print(f"Attempting to upload {filename} to channel {recipient_id}")

        # Try uploading file and then sharing it to the channel
        try:
            print("Trying files_upload and share to channel...")
            response = client.files_upload(
                content=file_content,
                filename=filename,
                title=filename,
                initial_comment=message
            )

            if response['ok']:
                file_id = response['file']['id']
                print(f"✅ File '{filename}' uploaded successfully! File ID: {file_id}")

                # Send message with file link to the channel
                try:
                    file_url = response['file']['permalink']
                    file_name = response['file']['name']
                    file_size = response['file']['size']

                    # Create a rich message with file info
                    file_message = f"{message}\n\n📄 **{file_name}** ({file_size} bytes)\n🔗 [Download CSV File]({file_url})"

                    msg_response = client.chat_postMessage(
                        channel=recipient_id,
                        text=file_message
                    )

                    if msg_response['ok']:
                        print(f"✅ File link sent to channel successfully!")
                        return True
                    else:
                        print(f"Failed to send file link: {msg_response}")

                except Exception as link_error:
                    print(f"Could not send file link: {link_error}")

                # Even if link fails, file was uploaded successfully
                print(f"✅ File uploaded successfully (ID: {file_id})")
                return True

            else:
                print(f"files_upload failed: {response}")

        except SlackApiError as e:
            print(f"files_upload error: {e.response['error']}")

        # Try files_upload with channel (original method)
        try:
            print("Trying files_upload with channel...")
            response = client.files_upload(
                channels=recipient_id,
                content=file_content,
                filename=filename,
                title=filename,
                initial_comment=message
            )

            if response['ok']:
                print(f"✅ File '{filename}' uploaded successfully via files_upload!")
                return True
            else:
                print(f"files_upload failed: {response}")

        except SlackApiError as e:
            print(f"files_upload error: {e.response['error']}")

        # Try files_upload_v2
        try:
            print("Trying files_upload_v2...")
            response = client.files_upload_v2(
                channel=recipient_id,
                content=file_content,
                filename=filename,
                title=filename,
                initial_comment=message
            )

            if response['ok']:
                print(f"✅ File '{filename}' uploaded successfully via files_upload_v2!")
                return True
            else:
                print(f"files_upload_v2 failed: {response}")

        except SlackApiError as e:
            print(f"files_upload_v2 error: {e.response['error']}")

        # Try using temporary file approach
        try:
            print("Trying temporary file approach...")
            with tempfile.NamedTemporaryFile(mode='w+b', suffix='.csv', delete=False) as temp_file:
                if isinstance(file_content, str):
                    temp_file.write(file_content.encode('utf-8'))
                else:
                    temp_file.write(file_content)
                temp_file.flush()

                # Try uploading the temporary file
                with open(temp_file.name, 'rb') as f:
                    response = client.files_upload_v2(
                        channel=recipient_id,
                        file=f,
                        filename=filename,
                        title=filename,
                        initial_comment=message
                    )

                # Clean up temp file
                os.unlink(temp_file.name)

                if response['ok']:
                    print(f"✅ File '{filename}' uploaded via temp file successfully!")
                    return True
                else:
                    print(f"Temp file upload failed: {response}")

        except SlackApiError as e:
            print(f"Temp file upload error: {e.response['error']}")
        except Exception as e:
            print(f"Temp file error: {e}")

        print("❌ All file upload methods failed")
        return False

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def send_slack_message_with_file(recipient_id, message, file_content, filename):
    """
    Send a Slack message with a file attachment, with multiple fallback methods

    Args:
        recipient_id: Slack channel or user ID
        message: Text message to send
        file_content: Content of the file as string or bytes
        filename: Name of the file (e.g., "trends_data.csv")
    """

    # Method 1: Try actual file upload (works best when permissions are correct)
    print("🔄 Attempting file upload...")
    file_upload_success = send_slack_file_simple(recipient_id, file_content, filename, message)

    if file_upload_success:
        return True

    # Method 2: Try formatted snippet (better formatting than plain text)
    print("🔄 Falling back to formatted snippet...")
    snippet_success = send_csv_as_snippet(recipient_id, file_content, filename, message)

    if snippet_success:
        return True

    # Method 3: Final fallback to plain text
    print("🔄 Final fallback to plain text...")
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        # Convert to string if needed
        if isinstance(file_content, bytes):
            file_content = file_content.decode('utf-8')

        # Fallback: send as formatted text
        fallback_message = f"{message}\n\n📄 *{filename}:*\n```\n{file_content}\n```"

        # Truncate if too long (Slack has message limits)
        if len(fallback_message) > 4000:
            truncated_content = file_content[:3500] + "\n... (truncated)"
            fallback_message = f"{message}\n\n📄 *{filename} (truncated):*\n```\n{truncated_content}\n```"

        response = client.chat_postMessage(
            channel=recipient_id,
            text=fallback_message
        )

        if response['ok']:
            print(f"✅ Message with CSV content sent as plain text successfully!")
            return True
        else:
            print(f"❌ Failed to send fallback message. Response: {response}")
            return False

    except SlackApiError as e:
        print(f"❌ Error sending fallback message: {e.response['error']}")
        return False
    

# #Testing  - Ovaj deo ide u slack_bot.py file ovde samo testiram
# if test_slack_credentials():
#     recipient_id = "channel_or_user_id"  
#     message = "Test, Test, Testing."
#     send_slack_message(recipient_id, message)
    