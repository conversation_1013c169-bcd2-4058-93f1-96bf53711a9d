import slack_sdk
from slack_sdk.errors import SlackApiError
from decouple import config

def test_slack_credentials():
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)
    
    try:
        response = client.auth_test()
        if response['ok']:
            print("Credentials are valid.")
            return True
        else:
            print("Credentials are invalid.")
            return False
            
    except SlackApiError as e:
        print(f"Error during authentication: {e.response['error']}")
        return False


def send_slack_message(recipient_id, message):
    token = config('SLACK_BOT_TOKEN')  
    client = slack_sdk.WebClient(token=token)
    
    try:
        response = client.chat_postMessage(
            channel=recipient_id,
            text=message
        )
        if response['ok']:
            print("Message sent successfully!")
            return True
        else:
            print("Failed to send message.")
            return False
            
    except SlackApiError as e:
        print(f"Error sending message: {e.response['error']}")
        return False
    

# #Testing  - Ovaj deo ide u slack_bot.py file ovde samo testiram
# if test_slack_credentials():
#     recipient_id = "channel_or_user_id"  
#     message = "Test, Test, Testing."
#     send_slack_message(recipient_id, message)
    