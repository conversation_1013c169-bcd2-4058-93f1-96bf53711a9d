import slack_sdk
from slack_sdk.errors import SlackApiError
from decouple import config

def test_slack_credentials():
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)
    
    try:
        response = client.auth_test()
        if response['ok']:
            print("Credentials are valid.")
            return True
        else:
            print("Credentials are invalid.")
            return False
            
    except SlackApiError as e:
        print(f"Error during authentication: {e.response['error']}")
        return False


def send_slack_message(recipient_id, message):
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        response = client.chat_postMessage(
            channel=recipient_id,
            text=message
        )
        if response['ok']:
            print("Message sent successfully!")
            return True
        else:
            print("Failed to send message.")
            return False

    except SlackApiError as e:
        print(f"Error sending message: {e.response['error']}")
        return False


def send_slack_message_with_file(recipient_id, message, file_content, filename):
    """
    Send a Slack message with a file attachment, with fallback to text format

    Args:
        recipient_id: Slack channel or user ID
        message: Text message to send
        file_content: Content of the file as string or bytes
        filename: Name of the file (e.g., "trends_data.csv")
    """
    token = config('SLACK_BOT_TOKEN')
    client = slack_sdk.WebClient(token=token)

    try:
        # Convert string content to bytes if needed
        if isinstance(file_content, str):
            file_content_bytes = file_content.encode('utf-8')
            file_content_str = file_content
        else:
            file_content_bytes = file_content
            file_content_str = file_content.decode('utf-8')

        # Try the newer files_upload_v2 method first
        try:
            response = client.files_upload_v2(
                channel=recipient_id,
                content=file_content_bytes,
                filename=filename,
                title=filename,
                initial_comment=message
            )

            if response['ok']:
                print(f"Message with file '{filename}' sent successfully!")
                return True
            else:
                print(f"Failed to send message with file. Response: {response}")
                raise SlackApiError("File upload failed", response)

        except SlackApiError as e:
            if e.response['error'] == 'not_in_channel':
                print("Bot not in channel for files_upload_v2, trying legacy files.upload...")
                # Fallback to legacy method
                try:
                    response = client.files_upload(
                        channels=recipient_id,
                        content=file_content_bytes,
                        filename=filename,
                        title=filename,
                        initial_comment=message
                    )

                    if response['ok']:
                        print(f"Message with file '{filename}' sent successfully via legacy method!")
                        return True
                    else:
                        raise SlackApiError("Legacy file upload failed", response)

                except SlackApiError as legacy_error:
                    print(f"Legacy file upload also failed: {legacy_error.response['error']}")
                    print("Falling back to sending CSV as text...")

                    # Fallback: send as formatted text
                    fallback_message = f"{message}\n\n📄 *{filename}:*\n```\n{file_content_str}\n```"

                    # Truncate if too long (Slack has message limits)
                    if len(fallback_message) > 4000:
                        truncated_content = file_content_str[:3500] + "\n... (truncated)"
                        fallback_message = f"{message}\n\n📄 *{filename} (truncated):*\n```\n{truncated_content}\n```"

                    response = client.chat_postMessage(
                        channel=recipient_id,
                        text=fallback_message
                    )

                    if response['ok']:
                        print(f"Message with CSV content sent as text successfully!")
                        return True
                    else:
                        print(f"Failed to send fallback message. Response: {response}")
                        return False
            else:
                raise e

    except SlackApiError as e:
        print(f"Error sending message with file: {e.response['error']}")
        return False
    

# #Testing  - Ovaj deo ide u slack_bot.py file ovde samo testiram
# if test_slack_credentials():
#     recipient_id = "channel_or_user_id"  
#     message = "Test, Test, Testing."
#     send_slack_message(recipient_id, message)
    