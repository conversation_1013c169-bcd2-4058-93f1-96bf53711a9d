#!/usr/bin/env python 
from html.entities import html5
import dash
import plotly.graph_objects as go
from django_plotly_dash import DjangoDash
import plotly.express as px
import dash_bootstrap_components as dbc


def ahor_bar_plot(dates, differences, title="Bar Plot"):
    """
    Kreira vertikalni bar plot gde su x-ose datumi,
    a y-ose razlike, koriste<PERSON>i dinamičke podatke.
    
    Parametri:
      dates (list): Lista datuma (npr. stringova ili datetime objekata).
      differences (list): Lista numeričkih vrednosti razlika.
      title (str): Naslov grafikona.
    
    Vraća:
      HTML reprezentaciju grafikona (bez full HTML wrapper-a).
    """
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=dates,
        y=differences,
        marker_color='green',  
        name='Differences',
        text=differences,

    ))
    fig.update_layout(
        title=title,
        xaxis_title="Date",
        yaxis_title="Difference",
        width=450,  
        height=250,  
        margin=dict(l=50, r=50, t=50, b=50),
        bargap=0.1,      
        bargroupgap=0.1, 
        shapes=[
            dict(
                type="line",
                xref="paper", 
                x0=0,
                x1=1,
                yref="y",
                y0=0,
                y1=0,
                line=dict(
                    color="red",
                    width=2
                )
            )
        ]
    )
    return fig.to_html(full_html=False)
