#!/usr/bin/env python 
import pandas as pd
from datetime import datetime, timedelta
from django.utils import timezone
from alerts.models import MetricTable
from django.utils.timezone import make_aware, make_naive

def zone_dict_packer(zone_model, threshold_ranges_model):
    '''""" This function fetches active zones and related thresholds,
           try to pack them into a dictionary
           and outputs the dictionery 
              """'''
    zones = zone_model.objects.filter(zone_active=True).order_by('id') 
    thresholds = threshold_ranges_model.objects.select_related("metric_name").all()
    packed_dict ={}

    zone_names = [zone.zone_name for zone in zones]
    #["Red", "Green", "Orange"]
    for threshold in thresholds:
        metric_name = threshold.metric_name.metric_name

        if metric_name not in packed_dict:
            packed_dict[metric_name] = {}   #Here i am initializing an empy dict with metric names as keys

        if len(zone_names) >= 3:
            packed_dict[metric_name] = {
                zone_names[0]: {
                        "bad_val_high_end": threshold.bad_val_high_end,
                        "bad_val_low_end": threshold.bad_val_low_end                    
                },
                zone_names[1]: {
                        "acceptable_val_high_end": threshold.acceptable_val_high_end,
                        "acceptable_val_low_end": threshold.acceptable_val_low_end
                }, 
                zone_names[2]: {
                        "good_val_low_end": threshold.good_val_lower_end,
                        "good_val_high_end": threshold.good_val_high_end
                }
            }           
    return packed_dict       


def range_tote_dict_packer(range_tote_model):
    """ Creates a dictionary of range totes for each metric
        and return metric_tote_dict containg the dictionery including

       """    
    metric_tote_dict = {}
    
    range_totes = range_tote_model.objects.select_related('metric').all() 
    for tote in range_totes:
        metric_tote_dict[tote.metric.metric_name] = tote.range


    return metric_tote_dict


def logic_determinator():
    '''This function tryies to determine
       the relationship between the previous and latest values with high end and
       low end values deremined by each zone
       as the result it returns 
       preval_zone (booolean)
       zone_name (string)
       prev_range (dictionery keys:[upper, lower])

    '''

def instance_convertor():
    '''This function convert the value to coresponding
       model instance
    ''' 
    pass   

def db_inserter():
    '''This function insert into DB values'''
    pass    

def status_dict_packer():
    '''This function packs the dictionery containing all data
       necessery for DB insert
       It takes 
       Args:    prev_val: previous value
                latest_val: latest value
                model_dict with_names: dixtionery wher keys are metric names and values respective models
                datatict: zone_dict (packed by zone_dict_packer)
                tote_dict : tote_dict (packed by range_tote_dict_packer)
       Return dict: status_dict
    '''

def trigger(prev_val_zone, curr_val_zone):
    """
    Determines if a trigger should be initiated based on the previous and current zone names.

    Args:
        prev_val_zone (str): The previous zone name.
        curr_val_zone (str): The current zone name.

    Returns:
        bool: True if a trigger should be initiated, False otherwise.
    """

    trigger = False

    # Check for valid zone names
    if prev_val_zone not in ["Red", "Green", "Orange"] or curr_val_zone not in ["Red", "Green", "Orange"]:
        return False

    # Determine trigger conditions
    if prev_val_zone == "Green" and curr_val_zone == "Red":
        trigger = True
    elif prev_val_zone == "Orange" and curr_val_zone == "Red":
        trigger = True
    elif prev_val_zone == "Green" and curr_val_zone == "Orange":
        trigger = True

    return trigger
 


def alternate_determine_status(prev_val, latest_val, url, strategy_id, metric_instance, domain_instance, metric_name, zone_model, zone_dict=None, tote_dict=None):
    """
    Determine the status of a metric by comparing the previous and current values with their respective zones 
    and the range tote value. The function returns a dictionary containing the status information.

    Args:
        prev_val (float): The previous value of the metric.
        latest_val (float): The current value of the metric.
        url (int): The id of the url.
        strategy_id (int): The id of the strategy.
        metric_instance (MetricTable): The metric instance.
        domain_instance (Domain): The domain instance.
        metric_name (str): The name of the metric.
        zone_model (Zones): The zone model.
        zone_dict (dict): The zone dictionary packed by zone_dict_packer.
        tote_dict (dict): The range tote dictionary packed by range_tote_dict_packer.

    Returns:
        dict: A dictionary containing the status information.
    """
    if zone_dict is None or tote_dict is None:
        return None
    
    prev_val_zone = None
    latest_val_zone = None
    trigger_value = None
    status_dict = {}
    prev_range = {}
    latest_range = {}  

    tote_value = tote_dict.get(metric_name, None)
    zone_value = zone_dict.get(metric_name, None)

    # Define a zone_names dictionary to hold prev_zone_name and latest_zone_name values, added dynamically
    zone_names = {"prev_zone_name":None, "latest_zone_name":None}
    for k, v in zone_names.items():
        #Loop through zone_value dictionary, in order to determine the zone ranges and order the thresholds to be in the right order for comparison.
        for colour, ranges in zone_value.items():
            # initialise the values list to store the threshold values(ranges) in a list for easier access.
            values_list = [value for value in ranges.values()]
            # Define a dictionary to hold the higher and lower threshold values by using the values list. The comparison logic is used to avoid ordering issues in conversions between lists and dicts.
            values_dict = {
                'higher': values_list[0] if values_list[0]>values_list[1] else values_list[1],
                'lower': values_list[0] if values_list[0]<values_list[1] else values_list[1],
            }
            # initialize the vals list for comparing the prev_val and latest_val in one loop.    
            vals = [prev_val, latest_val]
            for val in vals:
                if values_dict['lower'] <= val <= values_dict['higher']:
                    prev_val_zone = True
                    latest_val_zone = True
                    #If the value is in the range of the zone, add the colour to the zone_names dictionary, avoiding the QuerySet in the process.
                    zone_val = zone_model.objects.get(zone_name=colour)
                    zone_names[k] = zone_val
                    prev_range = {
                        'upper': values_dict['higher'],
                        'lower': values_dict['lower'],
                    }
                # elif condition to capture values outside the defines ranges - this can only be higher than upper theshold for the Red zone for Performance score. Without this - it returns "None None".    
                elif val > values_dict['higher']:
                    prev_val_zone = True
                    latest_val_zone = True
                    zone_names[k] = zone_model.objects.get(zone_name="Red")
                    prev_range = {
                        'upper': values_dict['higher'],
                        'lower': values_dict['lower'],
                    }               
    #Define the prev_zone_name and latest_zone_name values by accessing the zone_names dictionary for corresponding values. These variables are used down the pipeline, in the trigger function defined above.
    prev_zone_name = zone_names['prev_zone_name']
    latest_zone_name = zone_names['latest_zone_name']

#Calculating distance        
# 
    movement = prev_val-latest_val
    try:
        prev_val_dist_high_end = prev_range['upper'] - prev_val
    except Exception as e:
        prev_val_dist_high_end = 0
    try:        
        prev_val_dist_low_end = prev_val - prev_range['lower']
    except Exception as e:
        prev_val_dist_low_end = 0    
    try:    
        latest_val_dist_high_end = latest_range['upper'] - latest_val
    except Exception as e:
        latest_val_dist_high_end = 0
    try:        
        latest_val_dist_low_end = latest_val - latest_range['lower']
    except Exception as e:
        latest_val_dist_low_end = 0   


    #Comparing previous and current val zone


    trigger_value = trigger(prev_val_zone=prev_zone_name, curr_val_zone=latest_zone_name)


    status_dict[url] = {

                    'strategy':
                    { 
                        'strategy_id':strategy_id,
                    },

                    'metric':
                    {
                        'metric_instance':metric_instance,
                        'metric_name':metric_name, 
                    },
                    'url_id':url,
                    'domain':{
                        'domain_instance': domain_instance,
                    },
                    'prev_val': {
                        'zone_name': prev_zone_name,
                        'value': prev_val, 
                        'status': prev_val_zone,
                        'range': prev_range,
                        'prev_val_dist_high_end': prev_val_dist_high_end,  
                        'prev_val_dist_low_end': prev_val_dist_low_end    
                    },
                    'latest_val': {                      
                        'zone_name': latest_zone_name,
                        'value': latest_val,
                        'status': latest_val_zone,
                        'range': latest_range,
                        'latest_val_dist_high_end': latest_val_dist_high_end,  
                        'latest_val_dist_low_end': latest_val_dist_low_end    
                    },                    

                    'mdr':{
                        'movement': movement,
                        'distance': movement,
                        'range_tote_value': tote_value,
                        'trigger': trigger_value
                    }     
                }   


    return status_dict 


def calc_percentage(preval, latestval, metric_name):
    """Calculate percentage change between two values."""
    # PageSpeed logic reversal
    if metric_name == 'Speed Index':
        difference = latestval - preval  # Reverse calculation for PageSpeed
    else:
        difference = preval - latestval  # Standard calculation for other metrics
        
    if preval != 0:
        percentage_change = (difference / preval) * 100
    else:
        percentage_change = 0
        return percentage_change  # Avoid division by zero

    return percentage_change  




def calculate_final_score(metric_name, metric_score,zone_score, threshold_ranges, row, range_tote_model, percentage):
    """Calculate final score based upon metric importance and individual score"""
    zone_score = 0
    difference_score = 0
    percentage_score = 0
    total_score = 0

    metric_ranges = threshold_ranges.objects.filter(metric_name__metric_name=metric_name).last()
    range_tote = range_tote_model.objects.filter(metric__metric_name = metric_name).last()
    #-----------------------------------------SPEED INDEX ---------------------------------------
    if metric_name == 'Speed Index':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange":  
            print(row.id) 
            zone_score = 2
            if row.difference < 0 and abs(row.difference) > range_tote.range:
                difference_score = 2
            if row.percentage < 10:
                percentage_score = 1
            elif row.percentage >10 and row.percentage < 25:
                percentage_score = 3
            else:
                percentage_score = 5                
            print("Green - Orange", zone_score, "Percentage: ", percentage_score)
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":   
            zone_score = 3      
            print(row.id) 
            if row.difference < 0 and abs(row.difference) > range_tote.range:
                difference_score = 2
            if row.percentage < 10:
                percentage_score = 1
            elif row.percentage >10 and row.percentage < 25:
                percentage_score = 3
            else:
                percentage_score = 5                          
            print("Orange - Red", zone_score, "Percentage: ", percentage_score)    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":
            print(row.id)     
            zone_score = 5   
            difference_score = 5
            percentage_score = 5


                                  
            print("Green - Red", zone_score, "Percentage: ", percentage_score)    
    #--------------------------------- PERFORMANCE SCORE ---------------------------------                     
    elif metric_name == 'Performance Score':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange":  
            print(row.id)
            zone_score = 1   
            if row.difference < 0 and abs(row.difference) < range_tote.range:
                difference_score = 1
            if row.percentage < 10:
                percentage_score = 1    
            print("Green - Orange")
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red": 
            print(row.id)
            zone_score = 2
            if row.difference  <=10:
                difference_score = 1
            elif row.difference > 10 and row.difference < range_tote.range:
                difference_score = 2
            elif row.difference > range_tote.range:
                difference_score = 3    
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":      
            zone_score = 5
            difference_score = 5
            percentage_score = 5
            print("Green - Red")  
    # -------------------- SERVER NETWORK LATENCY ------------------------------------------------           
    elif metric_name == 'Server Network Latency':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange":    
            print(row.id)
            zone_score = 1
            if row.difference <= range_tote.range:
                difference_score = 1
            elif row.difference  > range_tote.range:
                difference_score = 2   
            if row.percentage <  10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 30:
                percentage_score = 3
            elif row.percentage > 30:
                percentage_score = 5            
            print("Green - Orange", metric_name)
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":      
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red": 
            zone_score = 5
            difference_score = 5
            percentage_score = 5                 
            print("Green - Red") 
    # -------------------- FIRST CONTENTFUL PAINT ------------------------------------------------                
    elif metric_name == 'First Contentful Paint':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange": 
            print(row.id)
            zone_score = 1
            if row.difference < 600:
                difference_score = 1
            elif row.difference > 600 and row.difference < 1200:
                difference_score = 3
            elif row.difference > 1200:
                difference_score = 5
            if row.percentage < 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage <  50:
                percentage_score = 3
            elif row.percentage > 50:
                percentage_score = 5                   
            print("Green - Orange")
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":   
            print(row.id)
            zone_score = 3
            if row.difference < 600:
                difference_score = 1
            elif row.difference > 600 and row.difference < 1200:
                difference_score = 3
            elif row.difference > 1200:
                difference_score = 5
            if row.percentage < 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage <  50:
                percentage_score = 3
            elif row.percentage > 50:
                percentage_score = 5       
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":   
            zone_score = 5
            difference_score = 5
            percentage_score = 5     
            print("Green - Red")  
    # -------------------- LARGEST CONTENTFUL PAINT ------------------------------------------------                   
    elif metric_name == 'Largest Contentful Paint':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange":  
            print(row.id)
            zone_score = 1  
            if row.difference >2500 and row.difference < 3000:
                difference_score = 1
            elif row.difference > 3000 and row.difference <=3500:
                difference_score = 3
            elif row.difference > 3500:
                difference_score = 5
            if row.percentage <=10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 50:
                percentage_score = 3
            elif row.percentage > 50:
                percentage_score = 5            
            print("Green - Orange")
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":  
            zone_score = 3   
            if row.difference > 4000 and row.difference <  5000:
                difference_score = 1
            elif row.difference > 5000 and row.difference < 8000:
                difference_score = 3
            elif row.difference > 8000:
                difference_score = 5
            if row.percentage <= 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 30:
                percentage_score = 3
            elif row.percentage > 30:
                percentage_score = 5                     
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":    
            zone_score = 5  
            difference_score = 5
            percentage_score = 5               
            print("Green - Red")   
    # -------------------- TOTAL BLOCKING TIME ------------------------------------------------               
    elif metric_name == 'Total Blocking Time':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange": 
            print(row.id)
            zone_score = 1  
            if row.difference > 200 and row.difference < 300:
                difference_score = 1
            elif row.difference > 300 and row.difference < 500:
                difference_score = 3
            elif row.difference > 500:
                difference_score = 5
            if row.percentage <= 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 30:
                percentage_score = 3
            elif row.percentage > 30:
                percentage_score = 5                    
            print("Green - Orange")
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":   
            zone_score = 3   
            if row.difference > 600 and row.difference < 700:
                difference_score = 1
            elif row.difference > 700 and row.difference < 800:
                difference_score = 3
            elif row.difference > 800:
                difference_score = 5        
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":   
            zone_score = 5  
            difference_score = 5
            percentage_score = 5                
            print("Green - Red") 
    # -------------------- CUMULATIVE LAYOUT SHIFT ------------------------------------------------                  
    elif metric_name == 'Cumulative Layout Shift':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange":    
            zone_score = 1
            if row.difference > 0.1 and row.difference < 0.11:
                difference_score = 1
            elif row.difference > 0.11 and row.difference < 0.15:
                difference_score = 3
            elif row.difference > 0.15:
                difference_score = 5
            if row.percentage <= 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 15:
                percentage_score = 3
            elif row.percentage > 0.15:
                percentage_score = 5                     
            print("Green - Orange")
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":   
            zone_score = 3   
            if row.difference > 0.25 and row.difference <= 0.3:
                difference_score = 1
            elif row.difference > 0.3 and row.difference <= 0.4:
                difference_score = 3
            elif row.difference > 0.4:
                difference_score = 5  
            if row.percentage <= 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage <= 15:
                percentage_score = 3
            elif row.percentage > 15:
                percentage_score = 5                  
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":
            zone_score = 5  
            difference_score = 5
            percentage_score = 5      
            print("Green - Red")    
    # -------------------- SERVER RESPONSE TIME ------------------------------------------------              
    elif metric_name == 'Server Response Time':
        if row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Orange":   
            zone_score = 1 
            if row.difference < 100:
                difference_score = 1
            elif row.difference > 100 and row.difference < 200:
                difference_score = 3
            elif row.difference > 200:
                difference_score = 5
            if row.percentage <= 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 30:
                percentage_score = 3
            elif row.percentage > 30:
                percentage_score = 5                    
            print("Green - Orange")
        elif row.prev_val_zone.zone_name == "Orange" and row.latest_val_zone.zone_name == "Red":    
            zone_score = 3 
            if row.difference <= 100:
                difference_score = 1
            elif row.difference > 100 and row.difference <= 300:
                difference_score = 3
            elif row.difference > 300:
                difference_score = 5
            if row.percentage <= 10:
                percentage_score = 1
            elif row.percentage > 10 and row.percentage < 15:
                percentage_score = 3
            elif row.percentage > 15:
                percentage_score = 5                    
            print("Orange - Red")    
        elif row.prev_val_zone.zone_name == "Green" and row.latest_val_zone.zone_name == "Red":  
            zone_score = 5  
            difference_score = 5
            percentage_score = 5   
            print("Green - Red") 

        total_score = zone_score + difference_score  + percentage_score    

    return zone_score, difference_score, percentage_score, total_score     



def calculate_stability_index(data_list, metric_id, thresholds):
    """
    Calculate mean, std, zone changes, stability index, stability score, and status for a list of values.

    Args:
        data_list (list): List of tuples containing values from PsiAlertingHub table.
        thresholds (dict): Dictionary containing 'red', 'orange', and 'green' thresholds.
    
    Returns:
        pd.DataFrame: DataFrame with calculated stability metrics.
    """
    import numpy as np
    import pandas as pd

    epsilon = 1e-9  # identify zero-like values
    data = pd.DataFrame(data_list)
    data['extraction_date'] = pd.to_datetime(data['extraction_date'])
    

    data['mean'] = data['last_val'].rolling(window=7).mean().fillna(0)
    data['std'] = data['last_val'].rolling(window=7).std().fillna(0)
    data['stdPrc'] = data['last_val'].pct_change() * 100
    

    data['value_deviation'] = data.apply(
        lambda row: 0 if abs(row['mean']) <= epsilon else (float(row['last_val']) - row['mean']) / row['mean'],
        axis=1
    )
    

    data['zone_change'] = data['latest_val_zone'] - data['prev_val_zone']
    penalty_map = {1: +1, 2: +2, -1: -1, -2: -2, 0: 0}  
    data['zone_change_penalty'] = data['zone_change'].map(penalty_map).fillna(0)
    
    # Rolling zone change score
    data['rolling_zone_change_score'] = data['zone_change_penalty'].rolling(window=7).sum().fillna(0)
    
    # Normalized zone change
    max_penalty = abs(data['rolling_zone_change_score']).max()
    data['normalized_zone_change'] = data['rolling_zone_change_score'] / max_penalty if max_penalty != 0 else 0
    
    # Stability Index
    k1, k2, k3 = 0.1, 0.1, 0.1  
    data['stability_index'] = (
        k1 * data['value_deviation'] +
        k2 * (1 - data['normalized_zone_change']) +
        k3
    )
    
    # Rolling Stability Index
    data['rolling_stability_index'] = data['stability_index'].rolling(window=7).mean().fillna(0)
    
    # Stability Score
    def calculate_stability_score(index):
        if 0 < index < 0.09:
            return "Stable"
        elif 0.1 < index < 0.2:
            return "Relatively Unstable"
        elif index > 0.2:
            return "Very Unstable"
    

    data['stability_score'] = data['stability_index'].apply(calculate_stability_score)
    data['rolling_stability_score'] = data['rolling_stability_index'].apply(calculate_stability_score)

    
    # Status
    def determine_status(row):
        """
        Determines the status based on the latest zone and stability score.
        """
        if row['latest_val_zone'] == 3:
            return "Optimized"
        elif row['latest_val_zone'] == 2:
            return str("Unoptimized but Acceptable")
        else:
            return "Very Unoptimized"
    
    data['status'] = data.apply(determine_status, axis=1)

    

    data['week_number'] = data['extraction_date'].dt.isocalendar().week
    data['week_day'] = data['extraction_date'].dt.isocalendar().day
    data['month'] = data['extraction_date'].dt.month
    

    data.replace([np.inf, -np.inf, np.nan], 0, inplace=True)

    

    return data

def calculate_current_week_day():
    '''Function return current week day
       current_week is a tuple (year, week_number, weekday)
    '''
    current_date = datetime.now()
    current_week = current_date.isocalendar()

    return current_week, current_date


def week_getter():
    '''Calculates last 7 days starting from current date'''
    seven_days_ago = timezone.now() - timedelta(days=7)
    return seven_days_ago

def metric_mapper():
    '''This function gets the metric data from the database and return dictionery
    mapping the metric name with corresponding id
       {"metric_name": metric_id}
    '''
    metrics_data  = MetricTable.objects.all()
    metric_map= {metric.metric_name: metric.id for metric in metrics_data}

    return metric_map
        
def models_fields(modelName):
    '''Receves a model name and return a field names packed in a list'''
    models_fields = [field.name for field in modelName._meta.get_fields()]
    return models_fields

def data_prepack(responseQuery, strategy_id):
    '''Receives response and prepack the dtaat in dictinonery'''
    data_details_list = []
    for item in responseQuery:
        data_details = {
            "metric_name": item.metric_name.metric_name,                    
            "status":item.status,
            "stability_index_score":item.stability_index_score,
            "last_value":float(item.last_val),
            "url_id":item.url_id,
            "strategy":strategy_id,
            "date":make_naive(item.extraction_date),
            "wmean":item.mean_val,
            "stability_index":item.stability_index,
            "deviation":item.value_deviation, 
            "std_dev":item.std_dev,
            "zone_change":item.zone_change,
            "rolling_zone_change_score": item.rolling_zone_change_score,
            "normalized_zone_change":item.normalized_zone_change,
            "difference":item.difference,
            "extraction_date":item.extraction_date.date(),
            "previous_value":item.previous_val,
            "prev_zone":item.prev_val_zone,
            "last_zone":item.last_val_zone
        }  
        data_details_list.append(data_details)  

    return data_details_list 


def sort_dict(inputDict):
    '''TAkes the list as an input and return 
    sorted list by date'''
    for metric, items in inputDict.items():
        items.sort(key=lambda x: x['date'])

    return inputDict

def prepare_all_plotly_data(grouped_data):
    """
    Prima grupisani ulaz (dictionary) gde su ključevi nazivi metrika,
    a vrednosti su liste rečnika sa podacima.
    
    Vraća novi rečnik gde za svaki metric dobijamo tuple (dates, differences)
    koji se mogu koristiti u Plotly grafu.
    
    Parametri:
      grouped_data (dict): Primer strukture:
          {
              'Performance Score': [dict1, dict2, ...],
              'Speed Index': [dict1, dict2, ...],
              ...
          }
    
    Vraća:
      dict: Rečnik gde su ključevi metrike, a vrednosti tuple (dates, differences)
    """
    prepared_data = {}
    for metric, data_list in grouped_data.items():

        
        dates = [item['date'] for item in data_list]
        differences = [float(item['difference']) for item in data_list]
        
        prepared_data[metric] = (dates, differences)
    return prepared_data


def data_dealer(response_data, smm):
    '''Reciecive response data and name of the model itself and 
       groups the data by metric name returnig grouped conext data taht can be used in context
    '''
    metrics_maps = metric_mapper()
    grouped_context_data = {}
    for item in response_data:
        metric=item.get("metric_name")
        grouped_context_data.setdefault(metric, []).append(item)
    for metric, items in grouped_context_data.items():
        items.sort(key=lambda x: x['extraction_date'], reverse=True)  

    plot_data = prepare_all_plotly_data(grouped_context_data)   

    updated_group_data = {}
    for metric, items in grouped_context_data.items():
        updated_group_data[metric] = {
            "entries": items,
            "data": plot_data[metric]
        }       

  
    return updated_group_data








    
              
    



             
    


