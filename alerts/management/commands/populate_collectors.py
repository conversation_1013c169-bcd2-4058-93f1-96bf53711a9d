from typing import Any
from django.core.management import BaseCommand
from django.utils import timezone
from alerts.models import *
from alerts.management.utils.data_util import calculate_final_score
from datetime import datetime


specific_date = timezone.now()
class Command(BaseCommand):
    help = "Fetching the data from PsiAlerts Hub and calculating percentages and trends"

    def calc_percentage(self, preval, latestval, metric_name, threshold_values):
        """
        Calculate percentage change between two values and check if the difference 
        exceeds the specified threshold for each metric.
        
        Args:
            preval (float): Previous value of the metric.
            latestval (float): Latest value of the metric.
            metric_name (str): Name of the metric.
            threshold_values (dict): A dictionary with metric names as keys and threshold values as values.

        Returns:
            tuple: (percentage_change, metric_name) if the difference exceeds the threshold, otherwise None.
        """
        # PageSpeed logic reversal
        if metric_name == 'Speed Index':
            difference = latestval - preval  
        else:
            difference = preval - latestval  

            # Additional specific logic for Server Response Time
            if metric_name == "Server Response Time" and (preval - latestval < 200):
                difference = preval - latestval

        # Check if the difference exceeds the threshold for the given metric
        threshold_value = threshold_values.get(metric_name, 0)  
        if abs(difference) > threshold_value:  
            return None 

        # Calculate percentage change if the difference exceeds the threshold
        if preval != 0:
            percentage_change = (difference / preval) * 100
        else:
            percentage_change = 0
            return percentage_change  # In case division by zero

        return percentage_change


    def calculate_trend(self, preval, latestval, metric_name, modelname):
        """Calculate the trend based on the metric type."""
        trend_score = 0
        if metric_name == 'Speed Index':
            if preval > latestval:
                trend = "increase"
                trend_score = 0
            elif preval < latestval:
                trend = "decline"
                trend_score = 5
            else:
                trend = "neutral"
                trend_score = 1
        else:
            if preval > latestval:
                trend = "decline"
                trend_score = 5
            elif preval < latestval:
                trend = "increase"
                trend_score = 5                
            else:
                trend = "neutral"
                trend_score = 1                
        

        return modelname.objects.get(trend_name=trend), trend_score
    
    def calculate_zone_change_score(self, previous_zone_val,  last_zone_val):

        zone_score_change = 0

        if previous_zone_val.zone_name == "Green" and last_zone_val.zone_name == "Orange":
 
            zone_score_change =1
        elif previous_zone_val.zone_name == "Orange" and last_zone_val.zone_name == "Red":

            zone_score_change = 3
        elif previous_zone_val.zone_name == "Green" and last_zone_val.zone_name == "Red":
           
            zone_score_change = 5

        return zone_score_change


    def calculate_metric_importance_score(self, metric_importance_name):
        metric_importance_score = 0

        if metric_importance_name == "low":

            metric_importance_score = 1
        elif metric_importance_name == "medium":
           
            metric_importance_score = 3
        elif metric_importance_name == "high":
          
            metric_importance_score = 5        
        
        return metric_importance_score


    def calculate_alert_score(self, psi_alerting_hub):
        """Calculate the alert score based on zones and percentage change."""
        zone_change_score = 0
        percentage_change_score = 0
        metric_importance_score = 0
        trend_score = 0
        

            

        
        total_score = zone_change_score + percentage_change_score + metric_importance_score + trend_score
        return total_score, trend_score  # Return both total and individual trend score

    def handle(self, *args, **options):

        thresholds = {
                'Speed Index': 340,
                'Server Response Time': 200,
                'Total Blocking Time': 200,
                'Server Network Latency': 200,
                'Largest Contentful Paint': 2500,
                'Performance Score': 35,
                'First Contentful Paint': 2500,
                'Cumulative Layout Shift': 0.15
            }
        psi_alerting_hubs = PsiAlertingHub.objects.filter(
            alert_trigger=True,
            extraction_date__date = specific_date.date(),  
            )

        for row in psi_alerting_hubs:
            metric_name = MetricTable.objects.get(id=row.metric.id).metric_name
            zone_change_score = self.calculate_zone_change_score(previous_zone_val=row.prev_val_zone, last_zone_val=row.latest_val_zone)

            metric_importance_score = self.calculate_metric_importance_score(row.metric.metric_importance)
            percentage_difference = self.calc_percentage(row.previous_val, row.last_val, metric_name, threshold_values=thresholds)
            zone_score, difference_score, percentage_score, total_score=calculate_final_score(metric_name=metric_name, 
                                  metric_score=metric_importance_score, 
                                  zone_score=zone_change_score, 
                                  threshold_ranges=ThresholdsRanges, 
                                  row=row, 
                                  range_tote_model=RangeTote,
                                  percentage=percentage_difference
                                  )
            
            # Calculate percentage change and trend with custom logic for PageSpeed
            percentage_change = self.calc_percentage(row.previous_val, row.last_val, metric_name, threshold_values=thresholds)
            trend, trend_score = self.calculate_trend(row.previous_val, row.last_val, metric_name, PsiTrends)

            # Calculate the alert score and get trend score
            alert_score, trend_score = self.calculate_alert_score(row)

            # # Insert the result into the AlertCollector model
            alert_collector = AlertCollector.objects.create(
                psi_alerting_hub=row,
                trend_name=trend,
                date_time=row.extraction_date,
                percentage_change=percentage_change,
                alert_trigger=True,
                metric_name=row.metric,
                alert_included = (total_score >= 6),
                zone_change_score = zone_score,
                percentage_change_score = percentage_score,
                metric_importance_score = metric_importance_score,
                trend_score = trend_score,
                final_score = total_score+metric_importance_score + trend_score

            )