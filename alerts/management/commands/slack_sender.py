from typing import Any
from django.core.management import BaseCommand
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from alerts.models import *
from alerts.management.utils.data_util import calculate_final_score
from alerts.management.utils.import_slack_sdk import *
from psi.models import PsiUrl, PsiStrategy
from datetime import datetime, timedelta

current_date = timezone.now().date()
class Command(BaseCommand):
    help = "Sending Slack messages to the registered users"


    def get_registered_users(self, custom_user_model):
        '''This function fetches the users and return a list'''

        users = custom_user_model.objects.filter(receive_alerts =True)
        
        return users



    def handle(self, *args, **options):
        # Fetch all users who have 'receive_alerts' set to True
        users_with_alerts = CustomUser.objects.filter(receive_alerts=True).prefetch_related('domains')

        for user in users_with_alerts:
  
            aggregated_message = f"Hi {user.full_name}, here are your latest alerts for the domains you're associated with:\n\n"


            user_domains = user.domains.all()

      
            alerts_for_user = (
                AlertCollector.objects
                .filter(
                    date_time__date=current_date, 
                    psi_alerting_hub__domain_id__in=user_domains,
                    metric_name__alert_inclusion=True,
                )
            )

            # Dictionary to group alerts by domain
            domain_alerts = {}
            for alert in alerts_for_user:           
                domain_name = alert.psi_alerting_hub.domain_id.domain
                date = alert.psi_alerting_hub.extraction_date
                metric_name = alert.metric_name.metric_name
                alert_type = alert.trend_name
                url = PsiUrl.objects.get(id=alert.psi_alerting_hub.url_id).url.replace("https://", "")    
                url_id = alert.psi_alerting_hub.url_id         
                strategy = PsiStrategy.objects.get(id=alert.psi_alerting_hub.strategy_id)

        
                if domain_name not in domain_alerts:
                    domain_alerts[domain_name] = []

                domain_alerts[domain_name].append(
                    f"• For url: {url} {strategy} devices metric *{metric_name}* is in {alert_type} for {abs(alert.psi_alerting_hub.percentage)}%!"
                )
           
            for domain_name, alerts in domain_alerts.items():
                
                aggregated_message += f"*Date :{current_date}*"
                aggregated_message += f"\nDomain: {domain_name}\n"
                aggregated_message += "\n".join(alerts)
                aggregated_message += "\n"

            #print(aggregated_message)
            if domain_alerts:
               
                send_slack_message(user.slack_handle, message=aggregated_message)
            else:
                print(f"No alerts found for user {user.full_name}.")