from django.core.management import BaseCommand
from django.utils import timezone
from alerts.models import *
from psi.models import PsiUrl, PsiStrategy
from alerts.management.utils.data_util import zone_dict_packer, range_tote_dict_packer, alternate_determine_status, trigger, calc_percentage



currentDate = timezone.now()

# Global dictionary mapping metric names to their corresponding model
model_dict_with_names = {
    "Performance Score": PerformanceScore,    
    "Speed Index": SpeedIndexDifference,
    "Server Network Latency": ServerNetworkLatency,    
    "First Contentful Paint": FirstContentfulPaint,
    "Largest Contentful Paint": LargestContentfulPaint,
    "Total Blocking Time": TotalBlockingTime,
    "Cumulative Layout Shift": CumulativeLayoutShift,
    "Server Response Time": ServerResponseTime,


}


class Command(BaseCommand):
    help = "Fetching data from each metrics table and populating alerting hub table"

    def handle(self, *args, **options):   
        url_ids = PsiUrl.objects.filter(is_active=True)
        strategies = PsiStrategy.objects.filter(strategy_inuse=True)
        tote_dict = range_tote_dict_packer(range_tote_model=RangeTote)
        zone_dict = zone_dict_packer(zone_model=Zones, threshold_ranges_model=ThresholdsRanges)


        for url in url_ids:

            for strategy in strategies:


                all_status_dicts = []
                for metric_name, model_class in model_dict_with_names.items():
                    metric_record_template = model_class.objects.filter(
                        strategy_id=strategy.id,
                        url_id = url.id
                    ).order_by('-extraction_date')
                    if metric_record_template:
                        previous_value = getattr(metric_record_template.first(), 'prev_added_value')

                        latest_value = getattr(metric_record_template.first(), 'last_added_value')

                        status_dictionery = alternate_determine_status(
                            url = url.id,
                            strategy_id=strategy.id,
                            domain_instance=Domain.objects.get(id=url.domain.id),
                            metric_instance= MetricTable.objects.get(metric_name=metric_name),
                            metric_name=metric_name,
                            prev_val=previous_value,
                            latest_val=latest_value,
                            zone_model=Zones,
                            zone_dict=zone_dict,
                            tote_dict=tote_dict
                            
                        )
                        if status_dictionery:
                    
                            PsiAlertingHub.objects.create(
                                            difference = status_dictionery[url.id]['mdr']['movement'],
                                            extraction_date = currentDate,
                                            domain_id = status_dictionery[url.id]['domain']['domain_instance'],
                                            strategy_id = status_dictionery[url.id]['strategy']['strategy_id'],
                                            url_id = url.id,
                                            metric = status_dictionery[url.id]['metric']['metric_instance'],
                                            prev_val_dist_high_end = status_dictionery[url.id]['prev_val']['prev_val_dist_high_end'],
                                            prev_val_dist_low_end = status_dictionery[url.id]['prev_val']['prev_val_dist_low_end'],
                                            latest_val_dist_high_end = status_dictionery[url.id]['latest_val']['latest_val_dist_high_end'],
                                            latest_val_dist_low_end = status_dictionery[url.id]['latest_val']['latest_val_dist_low_end'],
                                            movement = status_dictionery[url.id]['mdr']['movement'],
                                            movement_flag = False,
                                            prev_val_dist_low_end_flag = False,
                                            prev_val_dist_high_end_flag = False,
                                            latest_val_dist_low_end_flag = False,
                                            latest_val_dist_high_end_flag = False,
                                            previous_val = status_dictionery[url.id]['prev_val']['value'],
                                            last_val = status_dictionery[url.id]['latest_val']['value'],
                                            prev_val_zone = status_dictionery[url.id]['prev_val']['zone_name'],
                                            latest_val_zone= status_dictionery[url.id]['latest_val']['zone_name'],
                                            alert_trigger = status_dictionery[url.id]['mdr']['trigger'],
                                            percentage = calc_percentage(status_dictionery[url.id]['prev_val']['value'], 
                                                                         status_dictionery[url.id]['latest_val']['value'],
                                                                         status_dictionery[url.id]['metric']['metric_instance'].metric_name
                                                            )
                                        )                        