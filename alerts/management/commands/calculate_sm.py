from django.core.management import BaseCommand, CommandError
from alerts.models import PsiAlertingHub, StabilityMetricMonitor
from psi.models import PsiUrl
from alerts.management.utils.data_util import calculate_stability_index
from datetime import datetime
from django.db import transaction


class Command(BaseCommand):
    help = "Calculate Stability Metrics for a given week and insert into StabilityMetricMonitor"

    def add_arguments(self, parser):
        parser.add_argument(
            'week_number',
            type=int,
            help="Week number (1 - 52) to extract data"
        )
        parser.add_argument(
            '--year',
            type=int,
            default=datetime.now().year,
            help="Year for the specified week (defaults to current year)"
        )

    def handle(self, *args, **kwargs):
        week_number = kwargs['week_number']
        year = kwargs['year']

        if not (1 <= week_number <= 52):
            raise CommandError("Week number must be between 1 and 52.")

        # Fetch active URLs
        urls = PsiUrl.objects.filter(is_active=True)
        thresholds = {'red': 10, 'orange': 20, 'green': 30}  # Customize thresholds if needed

        for url in urls:
            for strategy in range(1, 3):  # Strategies: Mobile and Desktop
                for metric_id in range(1, 9):  # Assuming metrics are 1 to 8
                    try:
                        # Fetch data for the given week and year
                        data = PsiAlertingHub.objects.filter(
                            url_id=url.id,
                            strategy_id=strategy,
                            metric=metric_id,
                            #extraction_date__week=week_number,
                            #extraction_date__year=year,
                        ).values(
                            'metric', 'url_id', 'domain_id', 'strategy_id',
                            'previous_val', 'prev_val_zone',
                            'prev_val_dist_low_end', 'prev_val_dist_high_end',
                            'last_val', 'latest_val_zone',
                            'latest_val_dist_low_end', 'latest_val_dist_high_end',
                            'difference', 'extraction_date'
                        ).order_by('extraction_date')

                        if data.exists():
                            processed_data = calculate_stability_index(
                                list(data), metric_id, thresholds
                            )

                            print(processed_data)

                            # Preparing StabilityMetricMonitor instances
                            stability_metrics = [
                                StabilityMetricMonitor(
                                    metric_name_id=entry.get('metric'),
                                    url_id=entry.get('url_id'),
                                    domain_id_id=entry.get('domain_id'),
                                    strategy_id=entry.get('strategy_id'),
                                    previous_val=entry.get('previous_val', 0),  # Default to 0 if missing
                                    prev_val_zone_id=entry.get('prev_val_zone'),
                                    prev_val_dist_low_end=entry.get('prev_val_dist_low_end', 0),  # Default to 0
                                    prev_val_dist_high_end=entry.get('prev_val_dist_high_end', 0),  # Default to 0
                                    last_val=entry.get('last_val', 0),  # Default to 0
                                    last_val_zone_id=entry.get('latest_val_zone'),
                                    last_val_dist_low_end=entry.get('last_val_dist_low_end', 0),  # Default to 0
                                    last_val_dist_high_end=entry.get('last_val_dist_high_end', 0),  # Default to 0
                                    difference=entry.get('difference', 0),
                                    mean_val=entry.get('mean', 0),
                                    std_dev=entry.get('std', 0),
                                    std_prc=entry.get('stdPrc', 0),
                                    value_deviation=entry.get('value_deviation', 0),
                                    zone_change=(entry.get('zone_change', False) != 0),
                                    zone_change_penalty=entry.get('zone_change_penalty', 0),
                                    rolling_zone_change_score=entry.get('rolling_zone_change_score', 0),
                                    normalized_zone_change=entry.get('normalized_zone_change', 0),
                                    stability_index=entry.get('stability_index', 0),
                                    stability_index_score=entry.get('stability_index_score', 'Stable'),  # Default to 'Stable'
                                    status=entry.get('status', 'Optimized'),  # Default to 'Optimized'
                                    week_number=entry.get('week_number', 0),
                                    week_day=entry.get('week_day', 0),
                                    month=entry.get('month', 0),
                                    extraction_date=entry.get('extraction_date', datetime.now()),
                                )
                                for entry in processed_data.to_dict(orient='records')
                            ]



                            #Insert data into StabilityMetricMonitor
                            with transaction.atomic():
                                StabilityMetricMonitor.objects.bulk_create(stability_metrics)

                            self.stdout.write(
                                f"Processed URL ID: {url.id}, Strategy: {strategy}, Metric: {metric_id}"
                            )
                    except Exception as e:
                        self.stderr.write(
                            f"Error for URL {url.id}, Strategy {strategy}, Metric {metric_id}: {e}"
                        )