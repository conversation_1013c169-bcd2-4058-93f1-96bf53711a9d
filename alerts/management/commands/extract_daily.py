from django.core.management import BaseCommand
from django.utils import timezone
from alerts.models import *
from psi.models import PsiUrl, PsiStrategy, PsiMainCoreWebVitalsMetrics
from django.core.exceptions import ObjectDoesNotExist

currentDate = timezone.now()

model_list = [
    SpeedIndexDifference,
    FirstContentfulPaint,
    LargestContentfulPaint,
    TotalBlockingTime,
    CumulativeLayoutShift,
    RenderBlockingResources,
    ServerResponseTime,
    ServerNetworkLatency,
    PerformanceScore
]

class Command(BaseCommand):
    help = "fetching the data from main metrics table and decide if alarm should be sent"

    def handle(self, *args, **options):
        url_ids = PsiUrl.objects.filter(is_active=True)
        strategies = PsiStrategy.objects.filter(strategy_inuse=True)

        for url in url_ids:
            try:
                domain_instance = Domain.objects.get(id=url.domain_id)
            except Domain.DoesNotExist:
                print(f"Domain with id {url.domain_id} does not exist.")
                continue

            for strategy in strategies:
                try:
                    main_metrics = PsiMainCoreWebVitalsMetrics.objects.filter(
                        url_id=url.id, strategy=strategy.strategy_type
                    ).order_by('-extractionDate')[:2]

                    if main_metrics.count() > 1:
                        previous_metric = main_metrics[1]
                        last_metric = main_metrics[0]
                        #Fetch only metric fields from DB
                        metric_fields = PsiMainCoreWebVitalsMetrics._meta.get_fields()[2:11]

                        for model, field in zip(model_list, metric_fields):
                            metric_name = field.name
                            last_added_value = getattr(last_metric, metric_name)
                            previous_value = getattr(previous_metric, metric_name)
                            difference = last_added_value - previous_value

                            # Create dictionary for each metric
                            metric_dict = {
                                f"last_added_value": last_added_value,
                                f"prev_added_value": previous_value,
                                f"difference": difference,
                                "extraction_date": currentDate,
                                "domain_id": domain_instance,  
                                "strategy_id": strategy.id,
                                "url_id": url.id
                            }

                            # Create and save the instance of the appropriate model
                            alert_instance = model(**metric_dict)
                            alert_instance.save()

                    else:
                        print("Not enough data in the main metrics list")
                except ObjectDoesNotExist as em:
                    print(em)

        return None