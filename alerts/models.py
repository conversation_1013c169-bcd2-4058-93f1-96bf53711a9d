from django.db import models
from django.urls import reverse
from django.core.files.storage import FileSystemStorage
from django.contrib.auth.models import Abstract<PERSON>ase<PERSON><PERSON>, BaseUserManager, PermissionsMixin


# Create your models here.

# Application model
class Application(models.Model):
    app_name = models.CharField(max_length=255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)

    def __str__(self):
        return self.app_name

    def get_absolute_url(self):
        return reverse('appexplore', kwargs={'id': self.id})


# Domain model - Psi DOmain model replica
ss = FileSystemStorage("media")


class Country(models.Model):
    country_name = models.CharField(max_length=255)
    country_code = models.CharField(max_length=255)

    def __str__(self):
        return self.country_name


class Market(models.Model):
    market_name = models.CharField(max_length=255, default='market')
    countries = models.ManyToManyField(Country)
    keyword_limit = models.IntegerField()

    def __str__(self):
        countries = ", ".join([country.country_name for country in self.countries.all()])
        return f"{self.market_name}: {countries}"


class Domain(models.Model):
    url = models.CharField(max_length=150, unique=True)
    domain = models.CharField(max_length=150, unique=True)
    logo = models.ImageField(storage=ss, blank=True, null=True)
    in_use = models.BooleanField(default=False)

    def __str__(self):
        return self.domain

    def get_absolute_url(self):
        return reverse('psiadmin', kwargs={'id': self.domain})


# Custom User management - minimal overide of the main User model with two additional fields added
class CustomUserManager(BaseUserManager):
    """Custom user management - minimal overide of the main User model with two additional fields"""

    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError("The email must be set in order to register new user")
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """Custom superuser management - minimal overide to create superuser with email and password"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.setdefault('is_staff') is not True:
            raise ValueError("A stuff member must be flagged as True")
        if extra_fields.setdefault('is_superuser') is not True:
            raise ValueError("A superuser must have flag set up as True")

        return self.create_user(email, password, **extra_fields)


class CustomUser(AbstractBaseUser, PermissionsMixin):
    """This class overides the main user model on minimal level"""
    email = models.EmailField(unique=True)
    full_name = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    receive_alerts = models.BooleanField(default=False)  # Additional Field
    slack_handle = models.CharField(max_length=255, blank=True)  # Additional field
    access_all_markets = models.BooleanField(default=False)  # New flag to indicate access to all markets
    domains = models.ManyToManyField(Domain, blank=True)  # Adding one to many relationship
    markets = models.ManyToManyField(Market, blank=True)  # Adding one to many relationship with Market
    apps = models.ManyToManyField(Application, blank=True)  # Ading one to many relationship with applications

    objects = CustomUserManager()
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.email

    def get_full_name(self):
        return self.full_name

    def dropdown_display(self):
        return self.slack_handle if self.slack_handle else self.email


class MetricTable(models.Model):
    LEVEL_CHOICES = [
        ('low', 'low'),
        ('high', 'high'),
        ('medium', 'medium')
    ]
    metric_name = models.CharField(max_length=255, blank=True, unique=True)
    metric_abbr = models.CharField(max_length=255, blank=True, unique=True)
    metric_unit = models.CharField(max_length=255, blank=True)
    metric_importance = models.CharField(max_length=255, choices=LEVEL_CHOICES, default=None)
    alert_inclusion = models.BooleanField(default=True)
    description = models.TextField(max_length=500, default=None)

    def __str__(self):
        return self.metric_name

    def get_absolute_url(self):
        return reverse('metexplore', kwargs={'id': self.id})


class ThresholdsRanges(models.Model):
    metric_name = models.ForeignKey(MetricTable, on_delete=models.CASCADE)
    good_val_lower_end = models.FloatField(default=None)
    good_val_high_end = models.FloatField(default=None)
    acceptable_val_low_end = models.FloatField(default=None)
    acceptable_val_high_end = models.FloatField(default=None)
    bad_val_low_end = models.FloatField(default=None)
    bad_val_high_end = models.FloatField(default=None)

    def __str__(self):
        return f"{self.metric_name.metric_name} - Min Good: {self.good_val_lower_end}, Max Good: {self.good_val_high_end}"

    def get_absolute_url(self):
        return reverse('trexplore', kwargs={'id': self.id})


class RangeTote(models.Model):
    metric = models.ForeignKey(MetricTable, on_delete=models.CASCADE)
    range = models.FloatField()

    def __str__(self):
        return f"{self.metric.metric_name} - {self.range}"

    def get_absolute_url(self):
        return reverse('mtrtoteexplore', kwargs={'id': self.id})


class SpeedIndexDifference(models.Model):
    """
    Model for Speed index difference
    """
    last_added_value = models.FloatField(
        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(
        null=True,
        blank=True
    )

    difference = models.FloatField(
        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class FirstContentfulPaint(models.Model):
    """
    Model for First Contentful Paint
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class LargestContentfulPaint(models.Model):
    """
    Model for Largest Contentful Paint
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class TotalBlockingTime(models.Model):
    """
    Model for Total Blocking Time
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class CumulativeLayoutShift(models.Model):
    """
    Model for Cumulative Layout Shift
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class RenderBlockingResources(models.Model):
    """
    Model for Render Blocking Resources
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class ServerResponseTime(models.Model):
    """
    Model for Server Response Time
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class ServerNetworkLatency(models.Model):
    """
    Model for Server Network Latency
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class PerformanceScore(models.Model):
    """
    Model for Performance Score
    """
    last_added_value = models.FloatField(

        null=True,
        blank=True
    )

    prev_added_value = models.FloatField(

        null=True,
        blank=True
    )

    difference = models.FloatField(

        null=True,
        blank=True
    )

    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField()
    url_id = models.IntegerField()

    class Meta:
        ordering = ["extraction_date"]


class Zones(models.Model):
    zone_name = models.CharField(max_length=255, blank=True, unique=True)
    zone_description = models.CharField(max_length=255, blank=True)
    zone_active = models.BooleanField(default=False)

    def __str__(self):
        return self.zone_name

    def get_absolute_url(self):
        return reverse('zones', kwargs={'id': self.id})


class PsiAlertingHub(models.Model):
    difference = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    extraction_date = models.DateTimeField(auto_now_add=True, editable=False)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField(blank=True)
    url_id = models.IntegerField(blank=True)
    metric = models.ForeignKey(MetricTable, on_delete=models.CASCADE)
    prev_val_dist_high_end = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    prev_val_dist_low_end = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    latest_val_dist_high_end = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    latest_val_dist_low_end = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    movement = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    movement_flag = models.BooleanField(default=False)
    prev_val_dist_low_end_flag = models.BooleanField(default=False)
    prev_val_dist_high_end_flag = models.BooleanField(default=False)
    latest_val_dist_low_end_flag = models.BooleanField(default=False)
    latest_val_dist_high_end_flag = models.BooleanField(default=False)
    previous_val = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    last_val = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    prev_val_zone = models.ForeignKey(Zones, on_delete=models.CASCADE, related_name='prevalz')
    latest_val_zone = models.ForeignKey(Zones, on_delete=models.CASCADE, related_name='currvalz')
    alert_trigger = models.BooleanField(default=False)
    application_name = models.ForeignKey(Application, on_delete=models.CASCADE, default=2)
    percentage = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)

    def __str__(self):
        return f"{self.id}"

    def get_absolute_url(self):
        return reverse('trexplore', kwargs={'id': self.id})


class SlackCredentials(models.Model):
    slack_api_id = models.CharField(max_length=255, default=None)
    slack_client_id = models.CharField(max_length=255, default=None)
    slack_client_secret = models.CharField(max_length=255, default=None)
    slack_signin_secret = models.CharField(max_length=255, default=None)
    slack_verification_token = models.CharField(max_length=255, default=None)
    user_oauth_token = models.CharField(max_length=255, default=None)
    bot_user_oauth_token = models.CharField(max_length=255, default=None)
    is_active = models.BooleanField(default=False)

    def __str__(self):
        return self.slack_api_id


class AlertLevel(models.Model):
    LEVEL_CHOICES = [
        ('critical', 'Critical'),
        ('warning', 'Warning'),
        ('info', 'Info')
    ]
    alert_level = models.CharField(max_length=255, choices=LEVEL_CHOICES, default=None)
    description = models.TextField(blank=True, null=True)  # Optional description field

    def __str__(self) -> str:
        return self.get_alert_level_display()


class PsiTrends(models.Model):
    trend_name = models.CharField(max_length=255, default=None)

    def __str__(self):
        return self.trend_name


class Alerting_Notes(models.Model):
    alerting_note = models.CharField(max_length=255, default=None)
    metric_name = models.ForeignKey(MetricTable, on_delete=models.CASCADE)
    trend = models.ForeignKey(PsiTrends, on_delete=models.CASCADE, default=None)
    alert_level = models.ForeignKey(AlertLevel, on_delete=models.CASCADE, default=None)

    def __str__(self) -> str:
        return self.alerting_note


class AlertCollector(models.Model):
    psi_alerting_hub = models.ForeignKey(PsiAlertingHub, on_delete=models.CASCADE)
    trend_name = models.ForeignKey(PsiTrends, on_delete=models.CASCADE)
    date_time = models.DateTimeField(auto_now_add=True, editable=False)
    percentage_change = models.DecimalField(max_digits=10, decimal_places=3, null=True,
                                            blank=True)  # Consolidated field for change
    alert_trigger = models.BooleanField(default=False)
    metric_name = models.ForeignKey(MetricTable, on_delete=models.CASCADE, default=1)
    alert_included = models.BooleanField(default=False)
    zone_change_score = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    percentage_change_score = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    metric_importance_score = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    trend_score = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    final_score = models.DecimalField(max_digits=10, decimal_places=3, default=0)

    def __str__(self):
        return f"Alert for URL {self.psi_alerting_hub.url_id} - {self.psi_alerting_hub.metric.metric_name}"


class SitemapCheckerAlerts(models.Model):
    user_name = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sitemap_alerts')
    sitemap_checker_alert = models.BooleanField(default=False)

    def __str__(self):
        return f"Alert for URL {self.psi_alerting_hub.url_id} - {self.psi_alerting_hub.metric.metric_name}"


class StabilityMetricMonitor(models.Model):
    metric_name = models.ForeignKey(MetricTable, on_delete=models.CASCADE)
    url_id = models.IntegerField(blank=True)
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE)
    strategy_id = models.IntegerField(blank=True)
    previous_val = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    prev_val_zone = models.ForeignKey(Zones, on_delete=models.CASCADE, related_name='prevalzmonitor')
    prev_val_dist_low_end = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    prev_val_dist_high_end = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    last_val = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    last_val_zone = models.ForeignKey(Zones, on_delete=models.CASCADE, related_name='lastvalmonitor')
    last_val_dist_low_end = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    last_val_dist_high_end = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    difference = models.DecimalField(max_digits=10, decimal_places=3, null=True, blank=True)
    mean_val = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    std_dev = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    std_prc = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    value_deviation = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    zone_change = models.BooleanField(default=False)
    zone_change_penalty = models.IntegerField(default=0)
    rolling_zone_change_score = models.IntegerField(default=0)
    normalized_zone_change = models.DecimalField(max_digits=10, decimal_places=3,
                                                 default=0)  # Changed to DecimalField for consistency
    stability_index = models.DecimalField(max_digits=10, decimal_places=3, default=0)

    # Updated fields for textual stability score and status
    stability_index_score = models.CharField(
        max_length=50,
        choices=[("Stable", "Stable"), ("Relatively Unstable", "Relatively Unstable"),
                 ("Very Unstable", "Very Unstable")],
        default="Stable"
    )

    status = models.CharField(
        max_length=50,
        choices=[
            ("Optimized", "Optimized"),
            ("Unoptimized but Acceptable", "Unoptimized but Acceptable"),
            ("Very Unoptimized", "Very Unoptimized")
        ],
        default="Optimized"
    )

    week_number = models.IntegerField(default=0)
    week_day = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    extraction_date = models.DateTimeField(auto_now_add=False, editable=False)

    def __str__(self):
        return f"Change Monitor - {self.metric_name}"

