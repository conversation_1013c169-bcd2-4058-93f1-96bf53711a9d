{% extends "admin/change_list.html" %}
{% load i18n admin_urls %}

{% block object-tools-items %}
  <li>
    <a href="{% url 'the_django_plotly_dash:add_stateless_apps' %}" class="addlink">
      {% blocktrans with cl.opts.verbose_name as name %}Find Apps{% endblocktrans %}
    </a>
  </li>
  <li>
    {% url cl.opts|admin_urlname:'add' as add_url %}
    <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
      {% blocktrans with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktrans %}
    </a>
  </li>
  {% endblock %}
  
