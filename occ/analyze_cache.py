from datetime import datetime
import numpy as np

def calculate_median(times):
    times_median = np.median(times)
    return times_median

def calculate_mean(times):
    times_mean = np.mean(times)
    return times_mean

def cache_max_min(times):

    max = np.max(times)
    min = np.min(times)

    cache_max_min = max-min 

    return cache_max_min, max, min

def analyze_cache(headers):
    """
    Analyze the cache-related headers in an HTTP response to determine cache settings, duration, 
    age status, CDN caching status, and content type.

    Args:
        headers (dict): A dictionary of HTTP headers from the response.

    Returns:
        dict: A dictionary containing:
            - "Cache-Control": The value of the Cache-Control header or 'Not Found'.
            - "Content-Type": The identified resource type based on the Content-Type header.
            - "Expires": The datetime object of the Expires header or 'Invalid Date Format'.
            - "ETag": The value of the ETag header or 'Not Found'.
            - "Cache Duration (max-age)": The cache duration in seconds or 'Invalid max-age format'.
            - "Age": The age of the cached response or 'Not Found'.
            - "Age Status": The status of the cache based on the Age header.
            - "X-Cache": The value of the X-Cache header or 'Not Found'.
            - "CF-Cache-Status": The value of the CF-Cache-Status header or 'Not Found'.
            - "CDN Cache Status": The CDN cache status based on X-Cache or CF-Cache-Status.
            - "Cache Evaluation": The evaluation of cache effectiveness and potential improvements.
    """

    cache_control = headers.get('Cache-Control', 'Not Found')
    content_type = headers.get('Content-Type', 'Not Found')
    expires = headers.get('Expires', 'Not Found')
    etag = headers.get('ETag', 'Not Found')

    cache_duration = None
    if 'max-age' in cache_control:
        try:
            max_age = int(cache_control.split('max-age=')[-1].split(',')[0])
            cache_duration = max_age
        except ValueError:
            cache_duration = "Invalid max-age format"

    expires_at = None
    if expires != 'Not Found':
        try:
            expires_at = datetime.strptime(expires, "%a, %d %b %Y %H:%M:%S GMT")
        except ValueError:
            expires_at = "Invalid Date Format"

    age = headers.get('Age', 'Not Found')
    age_status = None
    if age != 'Not Found':
        try:
            age = int(age)
            if age == 0:
                age_status = "Cache Miss (Fresh Fetch)"
            elif age > 86400:
                age_status = "Stale Content (Consider Refreshing)"
            else:
                age_status = "Content Cached"
        except ValueError:
            age_status = "Invalid Age Value"
    else:
        age_status = "No Cache"

    # CDN Cache Verification
    x_cache = headers.get('X-Cache', 'Not Found')
    cf_cache_status = headers.get('Cf-Cache-Status', 'Not Found')
    if cf_cache_status != 'Not Found':
        cf_cache_status = cf_cache_status.upper()


    cdn_status = "Unknown"

    if isinstance(x_cache, str):
        x_cache = x_cache.upper()
    if isinstance(cf_cache_status, str):
        cf_cache_status = cf_cache_status.upper()

    if "HIT" in x_cache or cf_cache_status == "HIT":
        cdn_status = "Cached at CDN"
    elif "MISS" in x_cache or cf_cache_status == "MISS":
        cdn_status = "Not Cached at CDN"
    elif cf_cache_status == "STALE":
        cdn_status = "Stale Content at CDN"
    elif cf_cache_status == "EXPIRED":
        cdn_status = "CDN Cache Expired"
    elif cf_cache_status == "BYPASS":
        cdn_status = "CDN Cache Bypassed"

    

    def identify_resource_type(content_type):
        """
        Identifies the resource type based on the Content-Type header.

        Args:
            content_type (str): The value of the Content-Type header.

        Returns:
            str: The identified resource type, which can be HTML, CSS, JavaScript, Image, Font, JSON, Other, or Unknown.
        """
        if content_type is None:
            return "Unknown"
        if "text/html" in content_type:
            return "HTML"
        elif "text/css" in content_type:
            return "CSS"
        elif "application/javascript" in content_type or "application/x-javascript" in content_type or "text/javascript" in content_type:
            return "JavaScript"
        elif "image/" in content_type:
            return "Image"
        elif "font/" in content_type:
            return "Font"
        elif "application/json" in content_type:
            return "JSON"
        
        else:
            return "Other"
        
    resource_type = identify_resource_type(content_type)
    evaluation = evaluate_cache(cache_control, cache_duration, age, cdn_status,resource_type)

    return {
        "Cache_Control": cache_control,
        "Content_Type": resource_type,
        "Expires": expires_at,
        "ETag": etag,
        "Cache_Duration_max_age": f"{cache_duration} seconds" if isinstance(cache_duration, int) else cache_duration,
        "Age": age,
        "Age_Status": age_status,
        "X_Cache": x_cache,
        "CF_Cache_Status": cf_cache_status,
        "CDN_Cache_Status": cdn_status,
        "Cache_Evaluation": evaluation,
    }


def evaluate_cache(cache_control, cache_duration, age, cdn_status, resource_type):
    """
    Evaluate the cache settings in an HTTP response to detect potential issues or room for improvement.

    Args:
        cache_control (str): The value of the Cache-Control header.
        cache_duration (int or None): The cache duration in seconds.
        age (int or None): The age of the cached response in seconds.
        cdn_status (str): The CDN cache status.
        resource_type (str): The identified resource type.

    Returns:
        str: A message indicating whether the cache is properly configured or if there are issues detected. If issues are detected, a list of detected issues is included.
    """
    issues = []

    if cache_control == 'Not Found' or 'no-cache' in cache_control:
        issues.append("No caching or 'no-cache' detected; consider enabling caching.")
    if resource_type == "HTML":
        if cache_duration is None or cache_duration < 300:
            issues.append("HTML content has a short cache duration; if this is not news website consider increasing it for better performance.")
    elif resource_type in ["CSS", "JavaScript"]:
        if cache_duration is None or cache_duration < 31536000:
            issues.append(f"{resource_type} resources should have a longer cache duration.")
    elif resource_type == "Image":
        if cache_duration is None or cache_duration < 604800:
            issues.append("Images should have a longer cache duration (at least 1 week).")
    if isinstance(age, int) and age > 86400:
        issues.append("Content may be stale; consider refreshing it.")

    if cdn_status == "Not Cached at CDN":
        issues.append("Content is not cached at the CDN; verify CDN caching rules.")
    elif cdn_status == "Stale Content at CDN":
        issues.append("Stale content detected at CDN; consider refreshing cache.")

    if not issues:
        return "Good caching setup. All checks passed."
    else:
        return "Issues detected: " + "; ".join(issues)