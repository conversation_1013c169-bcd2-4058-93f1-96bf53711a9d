from django.shortcuts import render
from django.contrib import messages
from .forms import CacheAnalysisForm
from .cache_app import run_cache
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt


def cache_analysis_view(request):
    analysis = None
    url = None

    if request.method == "POST":
        form = CacheAnalysisForm(request.POST)
        if form.is_valid():
            url = form.cleaned_data["url"]
            try:
                analysis = run_cache(url)
            except Exception as e:
                messages.error(request, f"Error analyzing cache: {e}")
        else:
            messages.error(request, "Please enter a valid URL.")
    else:
        form = CacheAnalysisForm()

    return render(request, "occ/cache_check.html", {
        "form": form,
        "analysis": analysis,
        "url": url,
    })


