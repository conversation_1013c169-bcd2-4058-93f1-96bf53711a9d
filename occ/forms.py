from django import forms
from django.core.validators import URLValidator
from urllib.parse import urlparse

class CacheAnalysisForm(forms.Form):
    url = forms.CharField(
        max_length=200,
        required=True,
        label="URL",
        widget=forms.TextInput(attrs={
            'class': 'form-control',  # Bootstrap form styling
            'placeholder': 'Enter domain (e.g., domain.com or https://domain.com)',
        })
    )

    def clean_url(self):
        raw_url = self.cleaned_data['url'].strip()

        # If the user didn't include a scheme, prepend 'https://'
        parsed = urlparse(raw_url)
        if not parsed.scheme:
            raw_url = "https://" + raw_url

        # Validate the final URL
        validator = URLValidator()
        validator(raw_url)  # Raises ValidationError if invalid

        return raw_url
