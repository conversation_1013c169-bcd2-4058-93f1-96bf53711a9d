import requests
from urllib.parse import urlparse
import random 
import time
import numpy as np

from .analyze_cache import analyze_cache, calculate_mean, cache_max_min, calculate_median

def verify_input(url, cache=True):

    if cache==True:

        urlib = urlparse(url)

        if not urlib.scheme:
            url = f"https://{url}"

        return url 

    else:
        pass


def cache_busted_url(url, iterations=6):

    cache_busted_urls = []

    param = "?="
    for i in range(iterations):
        rng = random.randint(100000, 999999)
        busted_url = f"{url}{param}{rng}"

        cache_busted_urls.append(busted_url)
    
    return cache_busted_urls



def get_headers(cache=True, mobile=False, desktop=False):

    user_agent_mobile = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
    user_agent_desktop = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

    cache_control = "no-cache, no-store, must-revalidate"
    pragma = "no-cache"

    if cache==True:

        if mobile==True:
            user_agent = {"User-Agent":user_agent_mobile}
            return user_agent
        
        elif desktop==True:
            user_agent = {"User-Agent":user_agent_desktop}
            return user_agent
        
        else:
            pass
    
    elif cache==False and mobile==True:
        no_cache_mobile = {
            "User-Agent":user_agent_mobile,
            "Cache-Control":cache_control,
            "Pragma": pragma,
        }
        return no_cache_mobile

    elif cache==False and desktop==True:
        no_cache_desktop = {
            "User-Agent":user_agent_desktop,
            "Cache-Control":cache_control,
            "Pragma": pragma,
        }
        return no_cache_desktop
    
    else:
        pass

    return None 



def make_requests(url, cache=True, iterations=6, mobile=False, desktop=False):

    times = []
    responses = []

    if cache==False:
        cache_busted_list = cache_busted_url(url)
    else:
        pass

    for iteration in range(iterations):

        if cache==True and mobile==True:
            headers_mobile = get_headers(mobile=True)
            try:
                start_time = time.time()
                response = requests.get(url, headers=headers_mobile)
                response_time = time.time() - start_time
                times.append(response_time)
                responses.append(response)

            except Exception as e:
                return f"Error occured in function make_requests(): {e}"
        
        elif cache==True and desktop==True:
            headers_desktop = get_headers(desktop=True)
            try:
                start_time = time.time()
                response = requests.get(url, headers=headers_desktop)
                response_time = time.time() - start_time
                times.append(response_time)
                responses.append(response)

            except Exception as e:
                return f"Error occured in function make_requests(): {e}"
        
        elif cache==False and mobile==True:

            no_cache_mobile = get_headers(cache=False, mobile=True)

            
            try:
                start_time = time.time()
                response = requests.get(cache_busted_list[iteration], headers=no_cache_mobile)
                response_time = time.time() - start_time
                times.append(response_time)
                responses.append(response)
                print(f"NO CACHE MOBILE USING: {cache_busted_list[iteration]}")

            except Exception as e:
                return f"Error occured in function make_requests(): {e}"

        elif cache==False and desktop==True:
            no_cache_desktop = get_headers(cache=False, desktop=True)


            try:
                start_time = time.time()
                response = requests.get(cache_busted_list[iteration], headers=no_cache_desktop)
                response_time = time.time() - start_time
                times.append(response_time)
                responses.append(response)
                print(f"NO CACHE DESKTOP USING: {cache_busted_list[iteration]}")

            except Exception as e:
                return f"Error occured in function make_requests(): {e}"
            
        else:
            pass
    
    return times, responses



def run_cache(url):

    # Checking for https:// schema and appending if not found
    url_ver=verify_input(url)

    # Getting the mobile and desktop, cache and no cache response times and actual responses
    mobile_cached_times, mobile_cached_response = make_requests(url=url_ver, mobile=True)
    desktop_cached_times, desktop_cached_response = make_requests(url=url_ver, desktop=True)

    no_cache_mobile_times, no_cache_mobile_response = make_requests(url=url_ver, cache=False, mobile=True)
    no_cache_desktop_times, no_cache_desktop_response = make_requests(url=url_ver, cache=False, desktop=True)

    # Getting the headers from the responses to analyze
    results_mobile_cache = analyze_cache(mobile_cached_response[0].headers)
    results_desktop_cache = analyze_cache(desktop_cached_response[0].headers)
    results_no_cache_mobile = analyze_cache(no_cache_mobile_response[0].headers)
    results_no_cache_desktop = analyze_cache(no_cache_desktop_response[0].headers)

    # Calculating the mean for each response time type
    mobile_cache_average = calculate_mean(mobile_cached_times)
    desktop_cache_average = calculate_mean(desktop_cached_times)
    mobile_no_cache_average = calculate_mean(no_cache_mobile_times)
    desktop_no_cache_average = calculate_mean(no_cache_desktop_times)

    # Calculating the difference between the longest and the shortest response times
    mobile_cache_max_range, mobile_cache_max, mobile_cache_min = cache_max_min(mobile_cached_times)
    desktop_cache_max_range, desktop_cache_max, desktop_cache_min = cache_max_min(desktop_cached_times)
    mobile_no_cache_max_range, mobile_no_cache_max, mobile_no_cache_min = cache_max_min(no_cache_mobile_times)
    desktop_no_cache_max_range, desktop_no_cache_max, desktop_no_cache_min = cache_max_min(no_cache_desktop_times)


    # Calculating the median to account for the response from the first request with cache being longer, due to it not having been cached in the first place.
    mobile_cached_median = calculate_median(mobile_cached_times)
    desktop_cached_median = calculate_median(desktop_cached_times)
    no_cache_mobile_median = calculate_median(no_cache_mobile_times)
    no_cache_desktop_median = calculate_median(no_cache_desktop_times)


    context = {
        "url": url_ver,

        "mobile_cache_average": round(mobile_cache_average, 3),
        "desktop_cache_average": round(desktop_cache_average, 3),
        "mobile_no_cache_average": round(mobile_no_cache_average, 3),
        "desktop_no_cache_average": round(desktop_no_cache_average, 3),

        "mobile_cached_times": mobile_cached_times,
        "desktop_cached_times": desktop_cached_times,
        "no_cache_mobile_times": no_cache_mobile_times,
        "no_cache_desktop_times": no_cache_desktop_times,

        "mobile_cache_max_range": round(mobile_cache_max_range, 3),
        "desktop_cache_max_range": round(desktop_cache_max_range, 3),
        "mobile_no_cache_max_range": round(mobile_no_cache_max_range, 3),
        "desktop_no_cache_max_range": round(desktop_no_cache_max_range, 3),

        "mobile_cache_max": round(mobile_cache_max, 3),
        "mobile_cache_min": round(mobile_cache_min, 3),

        "mobile_no_cache_max": round(mobile_no_cache_max, 3),
        "mobile_no_cache_min": round(mobile_no_cache_min, 3),

        "desktop_no_cache_max": round(desktop_no_cache_max, 3),
        "desktop_no_cache_min": round(desktop_no_cache_min, 3),

        "desktop_cache_max": round(desktop_cache_max, 3),
        "desktop_cache_min": round(desktop_cache_min, 3),

        "results_mobile_cache": results_mobile_cache,
        "results_desktop_cache": results_desktop_cache,
        "results_no_cache_mobile": results_no_cache_mobile,
        "results_no_cache_desktop": results_no_cache_desktop,

        "mobile_cached_median": round(mobile_cached_median, 3),
        "desktop_cached_median": round(desktop_cached_median, 3),
        "no_cache_mobile_median": round(no_cache_mobile_median, 3),
        "no_cache_desktop_median": round(no_cache_desktop_median, 3),
    }


    ### --- FOR DEBUGGING PURPOSES --- ###
    # print(f"Results for Mobile Cached URL: {url} : \n{mobile_cached_times} \n{mobile_cached_response}")
    # print(f"Results for Desktop Cached URL: {url} : \n{desktop_cached_times}")
    # print(f"Results for No Cache Mobile URLs: {url} : \n{no_cache_mobile_times} \n{no_cache_mobile_response}")
    # print(f"Results for No Cache Desktop URLs: {url} : \n{no_cache_desktop_times}")

    # print(f"Cache Analysis and Evaluation for Mobile Cached: {results_mobile_cache}")
    # print(f"Cache Analysis and Evaluation for Desktop Cached: {results_desktop_cache}")
    # print(f"Cache Analysis and Evaluation for Mobile No Cache: {results_no_cache_mobile}")
    # print(f"Cache Analysis and Evaluation for Desktop No Cache: {results_no_cache_desktop}")

    return context


if __name__ == "__main__":
    run_cache()





