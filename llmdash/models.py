from django.db import models
from django.utils import timezone
import zlib


class LlmApplication(models.Model):
    app_name = models.CharField(max_length=255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)    
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)

    def __str__(self):
        return self.app_name   


class Country(models.Model):
    code = models.CharField(max_length=12, unique=True)

    def __str__(self):
        return self.code


class City(models.Model):
    name = models.CharField(max_length=255)
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='cities')

    def __str__(self):
        return f"{self.name}, {self.country.code}"


class Website(models.Model):
    url = models.CharField(unique=True, max_length=255)
    property_of = models.Char<PERSON>ield(max_length=255, blank=True)
    primary_market = models.CharField(max_length=20, blank=True)
    secondary_market = models.CharField(max_length=20, blank=True)

    def __str__(self):
        return self.url


class SearchQuery(models.Model):
    query = models.TextField()
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    city = models.ForeignKey(City, on_delete=models.SET_NULL, null=True, blank=True)
    brand = models.CharField(max_length=255, blank=True)
    websites = models.ManyToManyField(Website, blank=True)

    def __str__(self):
        return self.query[:50]


class Citations(models.Model):
    query = models.ForeignKey(SearchQuery, on_delete=models.CASCADE, related_name="citations")

    response_compressed = models.BinaryField(blank=True, null=True)
    response_citations = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    snapshot_date = models.DateField(default=timezone.now)

    def __str__(self):
        return f"Citation for: {self.query.query[:60]}"


class SourceDomain(models.Model):
    domain = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return self.domain


class CitationSource(models.Model):
    citation = models.ForeignKey(Citations, on_delete=models.CASCADE, related_name="source_links")
    domain = models.ForeignKey(SourceDomain, on_delete=models.CASCADE)

    class Meta:
        unique_together = ("citation", "domain")  # Prevent duplicates

    def __str__(self):
        return f"{self.domain.domain} in Citation {self.citation_id}"


class LlmModels(models.Model):
    model_name = models.CharField(max_length=255, unique=True)

    price_low = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    price_medium = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    price_high = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)

    def __str__(self):
        return self.model_name

class MarketReport(models.Model):
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='market_reports')
    summary_text_compressed = models.BinaryField()
    generated_at = models.DateTimeField(auto_now_add=True)

    def get_summary(self):
        return zlib.decompress(self.summary_text_compressed).decode("utf-8")

    def __str__(self):
        return f"Market Report for {self.country.code}"
    

class GptPromptConfig(models.Model):
    name = models.CharField(max_length=100, unique=True, default="default")
    system_prompt = models.TextField(default="You are an expert SEO analyst.")
    market_prompt_template = models.TextField(default="""
        You are an expert SEO analyst. Based on the following data for the {{ market_code }} market:

        Top 5 Domains by Citation Volume:
        {{ top_domains }}

        For each domain, here are the top 10 queries (with citation counts) it was cited for:
        {{ domain_query_map }}

        Generate a detailed summary of key trends, which domains dominate which topics, and any notable overlaps or gaps in keyword coverage.
        
        Always include the following:
        1. Explanation of the report, what is shows;
        2. Describe the data used to generate the report;
        3. Always include tables, examples, and suggestions;
        4. Create charts;
        5. Add a final summary.
                                              

    """.strip())

    def __str__(self):
        return f"PromptConfig ({self.name})"

    def __str__(self):
        return f"PromptConfig ({self.name})"
    

class GeminiCitations(models.Model):
    query = models.ForeignKey(SearchQuery, on_delete=models.CASCADE, related_name="gemini_citations")

    response_compressed = models.BinaryField(blank=True, null=True)
    response_citations = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    snapshot_date = models.DateField(default=timezone.now)

    def __str__(self):
        return f"Citation for: {self.query.query[:60]}"