from django.shortcuts import render
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from datetime import datetime

from llmdash.models import MarketReport
from llmdash.llmdash_utilities.llmdash_dashboard import build_dashboard_context
from llmdash.llmdash_utilities.llmdash_report import build_market_report_pdf_response
from collections import defaultdict
from datetime import datetime, timedelta

def llm_dashboard_view(request):
    context_or_response = build_dashboard_context(request)
    if isinstance(context_or_response, HttpResponse):
        return context_or_response
    return render(request, 'llm_dashboard/llm_dashboard.html', context_or_response)

def export_market_report_pdf(request):
    report_id = request.GET.get("report_id")
    if not report_id:
        raise Http404("Missing report_id")
    return build_market_report_pdf_response(report_id)
