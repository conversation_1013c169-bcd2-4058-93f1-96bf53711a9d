from django.contrib import admin
from .models import SearchQuery, Website, City, Country, LlmApplication, LlmModels, MarketReport, GptPromptConfig

# Register your models here.

class ReadOnlyAdminMixin:

    def has_add_permission(self, request):
        return True

    def has_change_permision(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True

    def has_view_permission(self, request, obj=None):
        return True


@admin.register(LlmApplication)
class LlmApplicationAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('app_name', 'app_description', 'app_is_active')

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['app_name'].disabled = True
            form.base_fields['app_description'].disabled = True
            form.base_fields['app_is_active'].disabled = True

        return form

@admin.register(SearchQuery)
class SiteSourceAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ('query', 'get_websites', 'country', 'city')
    list_filter = ('country', 'city')
    search_fields = ('query',)

    def get_websites(self, obj):
        return ", ".join([w.url for w in obj.websites.all()])
    get_websites.short_description = "Websites"

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['query'].disabled = True
            form.base_fields['websites'].disabled = True
            form.base_fields['country'].disabled = True
            form.base_fields['city'].disabled = True
            form.base_fields['is_active'].disabled = True

        return form


@admin.register(Website)
class WebsiteAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('url', )
    list_filter = ('url', )
    search_fields = ('url', )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['url'].disabled = True
            form.base_fields['domain'].disabled = True

        return form
    
@admin.register(City)
class CityAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('name', 'country',)
    list_filter = ('name', 'country', )
    search_fields = ('name', 'country',)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['city'].disabled = True
            form.base_fields['country'].disabled = True

        return form
    
@admin.register(Country)
class CountryAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    display = ('code')
    list_filter = ('code',)
    search_fields = ('code',)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['country'].disabled = True

        return form
    
@admin.register(LlmModels)
class LlmModelsAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('model_name', 'price_low', 'price_medium', 'price_high')

@admin.register(MarketReport)
class MarketReportAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ('country', 'generated_at')


@admin.register(GptPromptConfig)
class GptPromptConfigAdmin(admin.ModelAdmin):
    list_display = ('name',)