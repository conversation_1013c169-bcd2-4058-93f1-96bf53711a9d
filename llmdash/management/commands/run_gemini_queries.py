from django.core.management.base import BaseCommand
from llmdash.llmdash_utilities.llmdash_gemini import run_gemini_for_query, parse_gemini_response
from llmdash.models import SearchQuery, GeminiCitations
import zlib
import time

class Command(BaseCommand):
    help = "Run GPT completions and store citations"

    def handle(self, *args, **kwargs):
        queries = SearchQuery.objects.all()[66::]

        for query_obj in queries:
            self.stdout.write(f"\n🟢 Running Gemini for: {query_obj.query[0:7]}...")

            try:
                gemini_response = run_gemini_for_query(query_obj)
                gemini_response_text, gemini_sources = parse_gemini_response(gemini_response)

                # Compress GPT response
                compressed_response = zlib.compress(gemini_response_text.encode("utf-8"))

                # Create Citations object
                GeminiCitations.objects.create(
                    query=query_obj,
                    response_compressed=compressed_response,
                    response_citations="; ".join(gemini_sources),
                )

                self.stdout.write("✅ Saved Gemini response and sources.")

                time.sleep(10)

            except Exception as e:
                self.stderr.write(f"❌ Failed for query ID {query_obj.id}: {e}")


                