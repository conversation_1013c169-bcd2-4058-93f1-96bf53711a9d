from django.core.management.base import BaseCommand
from llmdash.llmdash_utilities.llmdash_helpers import run_completion_for_query, parse_completions
from llmdash.models import SearchQuery, Citations
import zlib


class Command(BaseCommand):
    help = "Run GPT completions and store citations"

    def handle(self, *args, **kwargs):
        queries = SearchQuery.objects.all()

        for query_obj in queries:
            self.stdout.write(f"\n🟢 Running GPT for: {query_obj.query[:60]}...")

            try:
                completion = run_completion_for_query(query_obj)
                response_text, sources, annotations = parse_completions(completion)

                # Compress GPT response
                compressed_response = zlib.compress(response_text.encode("utf-8"))

                # Create Citations object
                Citations.objects.create(
                    query=query_obj,
                    response_compressed=compressed_response,
                    response_citations="; ".join(annotations),
                )

                self.stdout.write("✅ Saved GPT response + citations.")

            except Exception as e:
                self.stderr.write(f"❌ Failed for query ID {query_obj.id}: {e}")