from django.core.management.base import BaseCommand
from llmdash.llmdash_utilities.llmdash_markets import build_citations_dataframe
from llmdash.llmdash_utilities.llmdash_helpers import initialize_client
from llmdash.llmdash_utilities.llmdash_report import generate_market_prompt, generate_gpt_summary, save_market_report


class Command(BaseCommand):
    help = "Generates GPT summaries for each market"

    def handle(self, *args, **kwargs):
        df = build_citations_dataframe()
        client = initialize_client()

        for code in df["Country"].unique():
            market_df = df[df["Country"] == code]
            prompt = generate_market_prompt(market_df, code)
            summary = generate_gpt_summary(client, prompt)
            save_market_report(code, summary)
            self.stdout.write(self.style.SUCCESS(f"✅ Saved GPT report for {code}"))