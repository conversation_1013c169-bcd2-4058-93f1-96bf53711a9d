from google import genai
from google.genai.types import <PERSON><PERSON>, GenerateContentConfig, GoogleSearch
from llmdash.models import Website, SearchQuery
from decouple import config


def initialize_client():
    api_key = config('GEMINI_API_KEY')
    client = genai.Client(api_key=api_key)
    model_id = "gemini-2.0-flash"
    return model_id, client

def run_gemini_for_query(query_obj):
    model_id, client = initialize_client()
    google_search_tool = Tool(
        google_search = GoogleSearch()
    )

    gemini_response = client.models.generate_content(
        model=model_id,
        contents=query_obj.query,
        config=GenerateContentConfig(
            tools=[google_search_tool],
            response_modalities=["TEXT"],
        )
    )
    return gemini_response

def parse_gemini_response(gemini_response):
    gemini_text = ""
    for each in gemini_response.candidates[0].content.parts:
        gemini_text.join(each.text)
    
    gemini_sources = []
    for each in gemini_response.candidates[0].grounding_metadata.grounding_chunks:
        gemini_sources.append(each.web.title)
    
    return gemini_text, gemini_sources
