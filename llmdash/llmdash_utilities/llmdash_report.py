import zlib
from llmdash.models import MarketReport, Country, GptPromptConfig
from openai.types.chat import ChatCompletionMessageParam
from llmdash.llmdash_utilities.llmdash_helpers import format_report_text
from django.template import Template, Context
import base64
from io import BytesIO
from datetime import datetime
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from weasyprint import HTML
import markdown2
import plotly.io as pio
from llmdash.models import MarketReport
from .llmdash_markets import build_citations_dataframe

def dict_to_markdown_table(d: dict, key_header="Key", val_header="Value"):
    if not d:
        return "_No data available_"
    lines = [f"| {key_header} | {val_header} |", "|---|---|"]
    for k, v in d.items():
        lines.append(f"| {k} | {v} |")
    return "\n".join(lines)


def nested_dict_to_markdown(domain_map: dict):
    if not domain_map:
        return "_No data available_"
    blocks = []
    for domain, queries in domain_map.items():
        blocks.append(f"**{domain}**\n")
        blocks.append(dict_to_markdown_table(queries, "Query", "Citations"))
        blocks.append("")  # blank line for spacing
    return "\n".join(blocks)


def generate_market_prompt(market_df, code):
    if market_df.empty or "Source" not in market_df.columns:
        return f"No citation data available for market {code}."

    # === Loop over each model (Source) ===
    full_domain_query_analysis = {}
    full_top_domains = {}

    for source in sorted(market_df["Source"].dropna().unique()):
        source_df = market_df[market_df["Source"] == source]
        top_domains = source_df["Domain"].value_counts().head(5).to_dict()
        domain_query_map = {}

        for domain in top_domains:
            citations_per_query = (
                source_df[source_df["Domain"] == domain]
                .groupby("QueryText")["URL"]
                .count()
                .sort_values(ascending=False)
                .head(10)
                .to_dict()
            )
            domain_query_map[domain] = citations_per_query

        full_top_domains[source] = top_domains
        full_domain_query_analysis[source] = domain_query_map

    # === Optional: groupby Source → QueryText → Domain ===
    grouped = (
        market_df
        .groupby(["Source", "QueryText", "Domain"])
        .size()
        .reset_index(name="Citations")
    )

    def format_grouped_citations_as_markdown(df):
        lines = []
        for source in df["Source"].unique():
            lines.append(f"### {source}")
            source_df = df[df["Source"] == source]
            for query in source_df["QueryText"].unique():
                lines.append(f"#### Query: {query}")
                query_df = source_df[source_df["QueryText"] == query]
                top_domains = query_df.sort_values(by="Citations", ascending=False).head(10)
                for _, row in top_domains.iterrows():
                    lines.append(f"- {row['Domain']} — {row['Citations']} citation{'s' if row['Citations'] != 1 else ''}")
                lines.append("")
            lines.append("---")
        return "\n".join(lines)

    source_query_breakdown = format_grouped_citations_as_markdown(grouped)

    # === Get prompt template ===
    try:
        config = GptPromptConfig.objects.get(name="default")
        raw_template = config.market_prompt_template
        print("Using model template")
    except GptPromptConfig.DoesNotExist:
        print("Using fallback template")
        raw_template = """
        GPT Market Report for {{ market_code }}

        {% for source in top_domains %}
        ## {{ source }}

        Top 5 Domains:

        {{ top_domains[source] }}

        Top 10 queries per domain:

        {{ domain_query_map[source] }}

        {% endfor %}

        GPT model-wide breakdown:

        {{ source_query_breakdown }}

        Please summarize the differences and patterns between the sources.
        """.strip()

    # === Prepare context with per-source blocks formatted ===
    context = Context({
        "market_code": code,
        "top_domains": {
            source: dict_to_markdown_table(domains, "Domain", "Citations")
            for source, domains in full_top_domains.items()
        },
        "domain_query_map": {
            source: nested_dict_to_markdown(queries)
            for source, queries in full_domain_query_analysis.items()
        },
        "source_query_breakdown": source_query_breakdown,
    })

    return Template(raw_template).render(context).strip()




def generate_gpt_summary(client, prompt: str) -> str:
    try:
        config = GptPromptConfig.objects.get(name="default")
        system_prompt = config.system_prompt
    except GptPromptConfig.DoesNotExist:
        system_prompt = "You are an expert SEO analyst."

    messages: list[ChatCompletionMessageParam] = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": prompt}
    ]

    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=messages,
        max_tokens=2000
    )
    return response.choices[0].message.content.strip()


def save_market_report(code: str, summary: str):
    compressed = zlib.compress(summary.encode("utf-8"))
    MarketReport.objects.create(
        country=Country.objects.get(code=code),
        summary_text_compressed=compressed
    )

def get_market_report_context(df, market_code, selected_date):
    market_df = df[df["Country"] == market_code]
    top_networks = market_df["Owner"].value_counts().head(5).to_dict()
    top_domains = market_df["Domain"].value_counts().head(5).to_dict()

    try:
        country = Country.objects.get(code=market_code)
        reports = MarketReport.objects.filter(
            country=country,
            generated_at__date=selected_date
        ).order_by('-generated_at')
    except Country.DoesNotExist:
        top_networks = {}
        top_domains = {}
        reports = []

    return {
        "top_networks": top_networks,
        "top_domains": top_domains,
        "market_summary": reports[0].get_summary() if reports else "No summary found for this market.",
        "report_data": [
            {
                "id": r.id, 
                "country": r.country.code,
                "summary": format_report_text(r.get_summary()),
                "date": r.generated_at.strftime("%Y-%m-%d"),
                "time": r.generated_at.strftime("%H:%M")
            }
            for r in reports
        ]


    }

def get_all_reports_for_date():
    return [
        {
            "id": r.id,
            "country": r.country.code,
            "summary": format_report_text(r.get_summary()),
            "date": r.generated_at.strftime("%Y-%m-%d"),
            "time": r.generated_at.strftime("%H:%M"),
        }
        for r in MarketReport.objects.all().order_by("-generated_at")
    ]





def build_market_report_pdf_response(report_id):
    from .llmdash_dashboard import generate_charts
    try:
        report = MarketReport.objects.get(id=report_id)
        summary_text = zlib.decompress(report.summary_text_compressed).decode("utf-8")
    except MarketReport.DoesNotExist:
        raise Http404("Report not found")

    html_body = markdown2.markdown(summary_text, extras=["tables"])
    df = build_citations_dataframe(OpenAI=True, Gemini=True)
    market_df = df[df["Country"] == report.country.code]
    chart_data = generate_charts(market_df, report.country.code)

    chart_images = {
        "network_chart": fig_to_base64(chart_data["fig1_obj"]),
        "website_chart": fig_to_base64(chart_data["fig2_obj"]),
        "bubble_owner": fig_to_base64(chart_data["fig_left_obj"]),
        "bubble_domain": fig_to_base64(chart_data["fig_right_obj"]),
    }

    html_string = render_to_string('llm_dashboard/report.html', {
        'country_code': report.country.code,
        'report_html': html_body,
        **chart_images,
        'generation_time': datetime.now().strftime("%Y-%m-%d %H:%M"),
        'current_year': datetime.now().year,
    })

    pdf_file = HTML(string=html_string).write_pdf()
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename=market_report_{report.country.code}.pdf'
    return response

def fig_to_base64(fig):
    buf = BytesIO()
    pio.write_image(fig, buf, format="png", width=1000, height=600)
    buf.seek(0)
    return base64.b64encode(buf.read()).decode("utf-8")
