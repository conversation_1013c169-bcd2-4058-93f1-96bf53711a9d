from urllib.parse import urlparse
import csv
from io import StringIO
import zlib
import re
import zlib
from llmdash.models import Website
from openai import OpenAI
from decouple import config
import colorsys
import textwrap

def format_report_text(text: str) -> str:
    # Wrap long lines and ensure line spacing
    return textwrap.fill(text, width=100)


def generate_blue_shades(n=8, hue=0.6):  # Hue ≈ 0.6 is blue
    return [
        '#{:02x}{:02x}{:02x}'.format(*[
            int(c * 255) for c in colorsys.hls_to_rgb(hue, 0.3 + (i / n) * 0.5, 0.8)
        ])
        for i in range(n)
    ]


def get_openai_api_key():
    api_key = config("OPENAI_API_KEY", default=None)
    if not api_key:
        raise ValueError("Missing OpenAI API Key!")
    return api_key

def initialize_client(api_key=None):
    if api_key is None:
        api_key = get_openai_api_key()
    return OpenAI(api_key=api_key)

def run_completion_for_query(query_obj):
    client = initialize_client()
    country_code = query_obj.country.code if query_obj.country else ""
    city_name = query_obj.city.name if query_obj.city else ""
    query_text = query_obj.query

    completion = client.chat.completions.create(
        model="gpt-4o-mini-search-preview",
        web_search_options={
            "user_location": {
                "type": "approximate",
                "approximate": {
                    "country": country_code,
                    "city": city_name,
                }
            },
            "search_context_size": "medium"
        },
        messages=[
            {
            "role": "user",
            "content": query_text,
        }]
    )

    return completion


def parse_completions(completion):
    message = completion.choices[0].message
    sources = re.findall(r'\(\[([^\]]+)\]\([^)]+\)', message.content)
    annotations = getattr(message, "annotations", [])
    annotations_used = [a.url_citation.url for a in annotations]
    return message.content, sources, annotations_used



def decompress_response_blob(blob):
    try:
        return zlib.decompress(blob).decode("utf-8")
    except Exception as e:
        return f"[Error decompressing response: {e}]"


def extract_domain(url):
    try:
        print("EXTRACTING DOMAIN:", url)
        return urlparse(url).netloc.replace("www.", "")
    except Exception as e:
        print(e)
        return None

def get_owner_by_domain(domain):
    try:
        match = Website.objects.filter(url__iendswith=domain).order_by('id').first()
        return match.property_of if match else "Unknown"
    except Exception as e:
        return f"Error: {str(e)}"

    

def generate_website_coverage_csv(df, market):
    df = df[df["Country"] == market]

    all_query_ids = df["QueryID"].unique()
    query_texts = df.drop_duplicates("QueryID").set_index("QueryID")["QueryText"].to_dict()

    domain_query_map = (
        df.groupby(["Domain", "QueryID"])["URL"]
        .apply(list)
        .reset_index()
    )

    domains = df["Domain"].unique()

    output = StringIO()
    writer = csv.writer(output)
    writer.writerow(["Domain", "QueryID", "QueryText", "Citations", "Gap"])

    for domain in domains:
        domain_df = domain_query_map[domain_query_map["Domain"] == domain]
        present_query_ids = domain_df["QueryID"].tolist()

        for query_id in all_query_ids:
            query_text = query_texts.get(query_id, "")
            if query_id in present_query_ids:
                urls = domain_df[domain_df["QueryID"] == query_id]["URL"].values[0]
                writer.writerow([domain, query_id, query_text, "; ".join(urls), "No"])
            else:
                writer.writerow([domain, query_id, query_text, "", "Yes"])

    return output.getvalue()