import pandas as pd
from django.http import HttpResponse
from llmdash.llmdash_utilities.llmdash_markets import (
    build_citations_dataframe, website_sov, network_sov,
    get_bubble_chart_data, plot_bubble
)
from llmdash.llmdash_utilities.llmdash_keywords import (
    get_keyword_serp_overview, get_decompressed_response
)
from llmdash.llmdash_utilities.llmdash_helpers import (
    generate_website_coverage_csv
)
from llmdash.llmdash_utilities.llmdash_report import get_all_reports_for_date
from llmdash.llmdash_utilities.llmdash_helpers import decompress_response_blob
import plotly.io as pio


def build_dashboard_context(request):
    selected_source = request.GET.get("source", "openai")
    use_openai = selected_source == "openai"
    use_gemini = selected_source == "gemini"

    full_df = build_citations_dataframe(OpenAI = use_openai, Gemini=use_gemini)

    date_options = sorted(full_df["Date"].dropna().unique(), reverse=True)
    selected_date = request.GET.get("date")

    if date_options:
        # Validate or fallback
        if not selected_date or pd.to_datetime(selected_date).date() not in pd.to_datetime(date_options).date:
            selected_date = str(date_options[0])
        try:
            selected_date = pd.to_datetime(selected_date).date()
        except Exception:
            return HttpResponse("Invalid date", status=400)

        df = full_df[full_df["Date"] == selected_date]
    else:
        selected_date = None
        df = pd.DataFrame()

    active_tab = request.GET.get("tab", "markets")

    data_table_df = prepare_data_table(df)

    # === Shared filters (Data tab only) ===
    selected_country = request.GET.get("country", "")
    selected_network = request.GET.get("network", "")
    selected_domain = request.GET.get("domain", "")

    if active_tab == "data":
        if selected_country:
            data_table_df = data_table_df[data_table_df["Country"] == selected_country]
        if selected_network:
            data_table_df = data_table_df[data_table_df["Network"] == selected_network]

        if selected_domain:
            data_table_df = data_table_df[data_table_df["Domain"] == selected_domain]

    if active_tab == "data" and request.GET.get("export") == "csv":
        csv_content = data_table_df.to_csv(index=False)
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=data_tab_export_{selected_country or "all"}_{selected_network or "all"}_{selected_domain or "all"}.csv'
        return response

    # === Markets tab export ===
    if active_tab == "markets" and request.GET.get("export") == "csv":
        selected_market = request.GET.get("market")
        return export_coverage_csv(df, selected_market)

    # === Keywords tab logic ===
    market_options = sorted(df["Country"].dropna().unique())
    selected_market = request.GET.get("market")

    if market_options:
        if not selected_market or selected_market not in market_options:
            selected_market = market_options[0]
    else:
        selected_market = None
    
    charts = generate_charts(df, selected_market)

    market_df = df[df["Country"] == selected_market].copy()
    keyword_options = sorted(market_df["QueryText"].dropna().unique())
    website_options = sorted(market_df["Domain"].dropna().unique())

    selected_keyword = request.GET.get("keyword", "")
    selected_site = request.GET.get("site", "")

    filtered_df = market_df.copy()
    if selected_keyword:
        filtered_df = filtered_df[filtered_df["QueryText"] == selected_keyword]
    if selected_site:
        filtered_df = filtered_df[filtered_df["Domain"] == selected_site]

    response_text = get_decompressed_response(selected_market, selected_keyword) if selected_keyword else ""

    if active_tab == "keywords" and request.GET.get("export") == "csv":
        keyword_data = get_keyword_serp_overview(df, selected_market, selected_keyword)
        results_df = keyword_data["results"]
        if selected_site:
            results_df = results_df[results_df["Domain"] == selected_site]
        csv_content = results_df.to_csv(index=False)
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=keyword_serp_{selected_market}_{selected_keyword or "all"}_{selected_site or "all"}.csv'
        return response
    
    if active_tab == "keywords" and request.GET.get("export") == "all_gaps":
        all_keywords = df[df["Country"] == selected_market]["QueryText"].dropna().unique()
        domain_stats = (
            df[df["Country"] == selected_market]
            .groupby("Domain")
            .agg(
                citation_count=("URL", "count"),
                keyword_count=("QueryID", "nunique")
            )
            .reset_index()
        )
        total_keywords = len(all_keywords)

        # Build full gap list
        rows = []
        for domain in domain_stats["Domain"]:
            for kw in all_keywords:
                # Check if domain appears for this keyword
                match = df[
                    (df["Country"] == selected_market) &
                    (df["Domain"] == domain) &
                    (df["QueryText"] == kw)
                ]
                if match.empty:
                    stats = domain_stats[domain_stats["Domain"] == domain].iloc[0]
                    rows.append({
                        "Keyword": kw,
                        "Domain": domain,
                        "Citation Count": stats["citation_count"],
                        "Keyword Count": stats["keyword_count"],
                        "Gap": "Yes",
                        "Country": selected_market,
                    })

        gap_df = pd.DataFrame(rows)
        csv_content = gap_df.to_csv(index=False)
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=keyword_gaps_all_{selected_market}.csv'
        return response
    
    # === Gap Export (Keywords tab) ===
    if active_tab == "keywords" and request.GET.get("export") == "gaps":
        keyword_data = get_keyword_serp_overview(df, selected_market, selected_keyword)
        gaps_df = keyword_data["gaps"]
        if selected_site:
            gaps_df = gaps_df[gaps_df["Domain"] == selected_site]
        csv_content = gaps_df.to_csv(index=False)
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=gaps_{selected_market}_{selected_site or "all"}.csv'
        return response

    # === Export all gaps for selected keyword (all websites)
    if active_tab == "keywords" and request.GET.get("export") == "gaps_all":
        keyword_data = get_keyword_serp_overview(df, selected_market, selected_keyword)
        gaps_df = keyword_data["gaps"]
        csv_content = gaps_df.to_csv(index=False)
        response = HttpResponse(csv_content, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=all_gaps_{selected_market}_{selected_keyword or "all"}.csv'
        return response


    # === Build charts, SERP table, reports ===
    if selected_keyword:
        serp_data = get_keyword_serp_overview(df, selected_market, selected_keyword)
        results_df = serp_data["results"]
        if selected_site:
            results_df = results_df[results_df["Domain"] == selected_site]
    else:
        # Build all keyword coverage for selected site
        if selected_site:
            all_keywords = df[df["Country"] == selected_market]["QueryText"].dropna().unique()
            market_df = df[df["Country"] == selected_market]
            domain_stats = (
                market_df.groupby("Domain")
                .agg(
                    citation_count=("URL", "count"),
                    keyword_count=("QueryID", "nunique")
                )
                .reset_index()
            )
            rows = []
            for kw in all_keywords:
                match = market_df[(market_df["Domain"] == selected_site) & (market_df["QueryText"] == kw)]
                if not match.empty:
                    rows.append({
                        "Keyword": kw,
                        "Domain": selected_site,
                        "URL": match.iloc[0]["URL"],
                        "Citation Count": domain_stats[domain_stats["Domain"] == selected_site]["citation_count"].values[0],
                        "Keyword Count": domain_stats[domain_stats["Domain"] == selected_site]["keyword_count"].values[0],
                        "Gap": "No"
                    })
                else:
                    rows.append({
                        "Keyword": kw,
                        "Domain": selected_site,
                        "URL": "",
                        "Citation Count": domain_stats[domain_stats["Domain"] == selected_site]["citation_count"].values[0],
                        "Keyword Count": domain_stats[domain_stats["Domain"] == selected_site]["keyword_count"].values[0],
                        "Gap": "Yes"
                    })
            results_df = pd.DataFrame(rows)
        else:
            # Show all keyword-domain matches in selected market
            market_df = df[df["Country"] == selected_market]
            grouped = (
                market_df.groupby(["QueryText", "Domain"])
                .agg(URL=("URL", "first"))
                .reset_index()
            )

            domain_stats = (
                market_df.groupby("Domain")
                .agg(
                    citation_count=("URL", "count"),
                    keyword_count=("QueryID", "nunique")
                )
                .reset_index()
            )

            rows = []
            for _, row in grouped.iterrows():
                kw = row["QueryText"]
                domain = row["Domain"]
                url = row["URL"]

                stats = domain_stats[domain_stats["Domain"] == domain].iloc[0]

                rows.append({
                    "Keyword": kw,
                    "Domain": domain,
                    "URL": url,
                    "Citation Count": stats["citation_count"],
                    "Keyword Count": stats["keyword_count"],
                    "Gap": "No"
                })

            results_df = pd.DataFrame(rows)



    serp_html = results_df.to_html(
        index=False, escape=False,
        classes="table table-bordered table-sm align-middle",
        table_id="keywords-table"
    )

    report_data = get_all_reports_for_date()

    data_table_html = data_table_df.to_html(
        index=False, escape=False,
        classes="table table-bordered table-sm align-middle",
        table_id="data-table"
    )




    column_options = get_filter_options(data_table_df)

    return {
        **charts,
        "source": selected_source,
        "market_options": market_options,
        "selected_market": selected_market,
        "keyword_options": keyword_options,
        "selected_keyword": selected_keyword,
        "selected_site": selected_site,
        "website_options": website_options,
        "serp_html": serp_html,
        "active_tab": active_tab,
        "response_text": response_text,
        "data_table": data_table_df.to_dict(orient="records"),
        "country_options": sorted(full_df["Country"].dropna().unique()),
        "network_options": sorted(full_df["Owner"].dropna().unique()),
        "date_options": date_options,
        "selected_date": selected_date,
        "report_data": report_data,
        "column_options": column_options,
        "selected_country": selected_country,
        "selected_network": selected_network,
        "selected_domain": selected_domain,
        "data_table_html": data_table_html,

    }



def prepare_data_table(df):
    return df[["Country", "Owner", "Domain", "QueryText", "URL", "Date"]].rename(columns={
        "Owner": "Network",
        "QueryText": "Query",
        "URL": "Citations"
    })


def export_data_csv(data_table_df):
    csv_content = data_table_df.to_csv(index=False)
    response = HttpResponse(csv_content, content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=data_table.csv'
    return response


def export_coverage_csv(df, market):
    csv_content = generate_website_coverage_csv(df, market)
    response = HttpResponse(csv_content, content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename=website_coverage_{market}.csv'
    return response


def generate_charts(df, selected_market):
    fig1 = network_sov(df)
    fig2 = website_sov(df)

    owner_df, domain_df = get_bubble_chart_data(df, selected_market)
    fig_left = plot_bubble(owner_df, "Owner", f"Network Performance in {selected_market}")
    fig_right = plot_bubble(domain_df, "Domain", f"Website Performance in {selected_market}")

    # HTML versions for dashboard
    fig1_html = pio.to_html(fig1, full_html=False, include_plotlyjs='cdn')
    fig2_html = pio.to_html(fig2, full_html=False, include_plotlyjs=False)
    fig_left_html = pio.to_html(fig_left, full_html=False, include_plotlyjs=False)
    fig_right_html = pio.to_html(fig_right, full_html=False, include_plotlyjs=False)

    return {
        "fig1_html": fig1_html,
        "fig2_html": fig2_html,
        "fig_left_html": fig_left_html,
        "fig_right_html": fig_right_html,
        # Add the real figures for PDF export
        "fig1_obj": fig1,
        "fig2_obj": fig2,
        "fig_left_obj": fig_left,
        "fig_right_obj": fig_right,
    }



def build_serp_table(df, market, keyword):
    table = get_keyword_serp_overview(df, market, keyword)
    return table.to_html(index=False, escape=False, classes="table table-bordered table-sm align-middle")


def get_filter_options(df):
    return {
        column: sorted(df[column].dropna().unique())
        for column in ["Country", "Network", "Domain", "Query", "Citations", "Date"]
    }
