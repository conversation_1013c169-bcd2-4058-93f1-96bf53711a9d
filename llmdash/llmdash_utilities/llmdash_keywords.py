import pandas as pd
from urllib.parse import urlparse
import zlib
from llmdash.models import Citations


def get_keyword_serp_overview(df, market, keyword):
    market_df = df[df["Country"] == market]
    total_keywords_in_market = market_df["QueryID"].nunique()

    # Domain-level stats across the whole market
    domain_stats = (
        market_df.groupby("Domain")
        .agg(
            citation_count=("URL", "count"),
            keyword_count=("QueryID", "nunique")
        )
        .reset_index()
    )
    #domain_stats["Keyword Coverage"] = (domain_stats["keyword_count"] / total_keywords_in_market * 100).round(1)
    domain_stats["Keyword Coverage"] = domain_stats["keyword_count"]


    all_domains = set(domain_stats["Domain"])
    keyword_df = market_df[market_df["QueryText"] == keyword].copy()
    keyword_df["Domain"] = keyword_df["Domain"].fillna("").apply(lambda u: u.replace("www.", ""))

    keyword_domains = set(keyword_df["Domain"])
    gap_domains = all_domains - keyword_domains

    # GAP domains: appear in market, not in selected keyword
    gaps_df = domain_stats[domain_stats["Domain"].isin(gap_domains)].copy()
    gaps_df["Gap"] = "Yes"
    gaps_df["Keyword"] = keyword
    gaps_df["URL"] = ""  # No URL because domain didn’t appear for this keyword

    if keyword_df.empty:
        return {
            "results": gaps_df[["Keyword", "Domain", "URL", "citation_count", "Keyword Coverage", "Gap"]].rename(columns={
                "citation_count": "Citation Count"
            }),
            "gaps": gaps_df
        }

    # Matched domains: appear in SERP for this keyword
    matched = keyword_df.drop_duplicates("Domain")[["Domain", "URL"]].copy()
    matched["Keyword"] = keyword
    matched["Gap"] = "No"
    matched = matched.merge(domain_stats, how="left", on="Domain")

    final_df = pd.concat([matched, gaps_df], ignore_index=True)

    return {
        "results": final_df[["Keyword", "Domain", "URL", "citation_count", "Keyword Coverage", "Gap"]].rename(columns={
            "citation_count": "Citation Count"
        }),
        "gaps": gaps_df
    }



def get_decompressed_response(market, keyword):
    try:
        citation = (
            Citations.objects
            .filter(query__query=keyword, query__country__code=market)
            .exclude(response_compressed__isnull=True)
            .first()
        )
        if citation and citation.response_compressed:
            return zlib.decompress(citation.response_compressed).decode("utf-8")
    except Exception as e:
        return f"[Error loading GPT response: {e}]"
    return ""
