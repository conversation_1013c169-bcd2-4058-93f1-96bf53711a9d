# Data pipeline for the markets tab visualizations
from llmdash.models import Citations, GeminiCitations
from .llmdash_helpers import extract_domain, get_owner_by_domain

import pandas as pd
from urllib.parse import urlparse
import plotly.express as px
import plotly.graph_objects as go
import plotly.colors as pc
import colorsys

def build_citations_dataframe(OpenAI=None, Gemini=None):
    rows = []
    
    if OpenAI:
        for citation in Citations.objects.select_related("query__country").all():
            country = citation.query.country.code
            query_id = citation.query_id
            date = citation.snapshot_date
            urls = citation.response_citations.split(";")
            query = citation.query.query

            for url in urls:
                if not url:
                    continue
                domain = extract_domain(url)
                owner = get_owner_by_domain(domain)
                rows.append({
                    "Country": country,
                    "Owner": owner,
                    "Domain": domain,
                    "QueryID": query_id,
                    "Date": date,
                    "URL": url,
                    "QueryText": query,
                    "Source":"OpenAI",
                })
    if Gemini:
        for citation in GeminiCitations.objects.select_related("query__country").all():
            country = citation.query.country.code
            print(country)
            query_id = citation.query_id
            date = citation.snapshot_date
            urls = citation.response_citations.split(";")
            query = citation.query.query

            for url in urls:
                url = url.strip()
                if not url:
                    continue

                owner = get_owner_by_domain(url)
                rows.append({
                    "Country": country,
                    "Owner": owner,
                    "Domain": url,
                    "QueryID": query_id,
                    "Date": date,
                    "URL": url,
                    "QueryText": query,
                    "Source":"Gemini",
                })
        else:
            pass       

    df = pd.DataFrame(rows)
    print(df.head(10), df.columns)
    return df


def network_sov(df, color_sequence=None, dark_mode=False):
    grouped = df.groupby(['Country', 'Owner']).size().reset_index(name='Mentions')
    total_per_market = grouped.groupby('Country')['Mentions'].transform('sum')
    grouped['SoV'] = (grouped['Mentions'] / total_per_market) * 100

    fig = go.Figure()
    seen_owners = set()
    fixed_hue = 0.58

    for country in grouped['Country'].unique():
        country_df = grouped[grouped['Country'] == country].copy()
        country_df.sort_values('SoV', ascending=True, inplace=True)

        for _, row in country_df.iterrows():
            owner = row['Owner']

            if owner.lower() == 'unknown':
                continue  

            show_legend = owner not in seen_owners
            seen_owners.add(owner)

            
            if owner.lower() == "better collective":
                color = "rgb(0, 160, 100)"
            elif color_sequence:
                color = color_sequence[hash(owner) % len(color_sequence)]
            else:
                lightness = 0.8 - (row['SoV'] / 100) * 0.4
                r, g, b = colorsys.hls_to_rgb(fixed_hue, lightness, 0.8)
                color = f'rgb({int(r*255)}, {int(g*255)}, {int(b*255)})'

     
            fig.add_trace(go.Bar(
                y=[row['Country']],
                x=[row['SoV']],
                name=owner,
                orientation='h',
                marker_color=color,
                hovertemplate=f"<b>{owner}</b><br>SoV: {row['SoV']:.1f}%<br>Mentions: {row['Mentions']}",
                showlegend=show_legend
            ))

    fig.update_layout(
        barmode='stack',
        title='Share of Voice by Network per Market',
        xaxis_title='Share of Voice (%)',
        yaxis_title='Market',
        yaxis={'categoryorder': 'total ascending'},
        margin=dict(l=100, r=20, t=40, b=40),
        showlegend=True,
        template='plotly_dark' if dark_mode else 'plotly_white'
    )

    return fig


def website_sov(df, color_sequence=None, dark_mode=False):
    grouped = df.groupby(['Country', 'Domain']).size().reset_index(name='Mentions')
    total_per_market = grouped.groupby('Country')['Mentions'].transform('sum')
    grouped['SoV'] = (grouped['Mentions'] / total_per_market) * 100
    grouped = grouped.sort_values(['Country', 'SoV'], ascending=[True, False])

    fixed_hue = 0.58
    fig = go.Figure()

    for country in grouped['Country'].unique():
        country_df = grouped[grouped['Country'] == country].copy()

        for _, row in country_df.iterrows():
            domain = row['Domain']
            owner = get_owner_by_domain(domain)

            if owner.lower() == "better collective":
                color = "rgb(0, 160, 100)"
            elif color_sequence:
                color = color_sequence[hash(domain) % len(color_sequence)]
            else:
                lightness = 0.6 - (row['SoV'] / 100)
                r, g, b = colorsys.hls_to_rgb(fixed_hue, lightness, 0.8)
                color = f'rgb({int(r*255)}, {int(g*255)}, {int(b*255)})'

            fig.add_trace(go.Bar(
                y=[country],
                x=[row['SoV']],
                name=domain,
                orientation='h',
                marker_color=color,
                hovertemplate=(
                    f"<b>{domain}</b><br>Owner: {owner}<br>SoV: {row['SoV']:.1f}%<br>Mentions: {row['Mentions']}"
                ),
                text=[f"{row['SoV']:.1f}%"],
                textposition='inside'
            ))

    fig.update_layout(
        barmode='stack',
        title='Share of Voice by Website per Market (Heatmap Style)',
        xaxis_title='Share of Voice (%)',
        yaxis_title='Market',
        yaxis={'categoryorder': 'total ascending'},
        margin=dict(l=100, r=20, t=40, b=40),
        showlegend=True,
        template='plotly_dark' if dark_mode else 'plotly_white'
    )

    return fig





def get_bubble_chart_data(df, market):
    filtered = df[df["Country"] == market]

    # Domain-level
    domain_grouped = (
        filtered.groupby("Domain")
        .agg(
            keyword_count=("QueryID", "nunique"),
            citation_count=("URL", "count")
        )
        .reset_index()
    )
    domain_grouped["coverage_score"] = domain_grouped["keyword_count"] * domain_grouped["citation_count"]
    domain_grouped["type"] = "Domain"

    # Network-level
    owner_grouped = (
        filtered.groupby("Owner")
        .agg(
            keyword_count=("QueryID", "nunique"),
            citation_count=("URL", "count")
        )
        .reset_index()
    )
    owner_grouped["coverage_score"] = owner_grouped["keyword_count"] * owner_grouped["citation_count"]
    owner_grouped["type"] = "Owner"

    return owner_grouped, domain_grouped




def plot_bubble(df, label_col, title, color_sequence=None, dark_mode=False):
    df = df[df[label_col].str.lower() != 'unknown']

    # Apply jitter only for 'Owner' (network chart), not for 'Domain' (website chart)
    if label_col.lower() == 'owner':
        df = apply_deduplication_jitter(df)

    fig = px.scatter(
        df,
        x="keyword_count",
        y="citation_count",
        size="coverage_score",
        color=label_col,
        hover_name=label_col,
        size_max=60,
        title=title,
        labels={
            "keyword_count": "Keywords Covered",
            "citation_count": "Citations",
            "coverage_score": "Total Coverage Score"
        },
        color_discrete_sequence=color_sequence or px.colors.qualitative.Plotly
    )

    fig.update_layout(
        margin=dict(l=40, r=40, t=60, b=40),
        xaxis_title="Keywords Covered",
        yaxis_title="Citations",
        legend_title=label_col,
        template='plotly_dark' if dark_mode else 'plotly_white'
    )

    return fig



def apply_deduplication_jitter(df, bump=0.1):
    counts = df.groupby(['keyword_count', 'citation_count']).cumcount()
    df = df.copy()
    df['keyword_count'] += counts * bump
    df['citation_count'] += counts * bump
    return df











# def main():
#     df = build_citations_dataframe()
#     print(df)
#     networks_by_market = network_sov(df)
#     networks_by_market.show()
#     websites_by_market = website_sov(df)
#     websites_by_market.show()


# if __name__ == "__main__":
#     main()











        
    






