#!/usr/bin/env python
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
#from django.conf.urls import url

from django.views.generic import TemplateView
from django_plotly_dash.views import add_to_session

from .views import oplogin, apps, home, oplogout, request_access
from logana.views import upload, project_dash, create, project_details, erp_details, googlebot_main, bot_details, \
 googlebot_mobile,googlebot_desktop, googlebot_images,googlebot_news, googlebot_video, googlebot_ads,domain_dash, domain_list, report, \
    googlebot_direct, googlebot_indirect, googlebot_report, add_new_vip, ai_bot_summary, ai_bot_single, ai_bot_rep
from psi.views import psi_domain_dash, psi_admin, add_new_url, add_new_ptype, add_new_domain, crux_domain_explore,performance, \
      lighthouse, crux_url_explore  , monthly_performance_report, cruxdb_url_explore, crux_dbdomain_explore
#from logana.processes.dashes import erpData, table_below_graph
from ts.views import top_stories_report, search_targets, top_stories_single_report, trigger_script, keyword_tracking_handling
from gis.views import url_checker_view, task_status
from alerts.views import psi_alerts_report
from sitemap_checker.views import check_sitemap_status
from drbot.views import chat_view
from occ.views import cache_analysis_view
from autorevap.views import dashboard_view
from google_trends import views as google_trends_views
from llmdash.views import llm_dashboard_view, export_market_report_pdf

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', home, name='home'),
    path('login/', oplogin, name='login'),
    path('request/', request_access, name='request'),
    path('logout/', oplogout, name='logout'),
    path('apps/', apps),
    path('logana/upload', upload, name='prupload'),
    path('logana/project/<id>/', project_details, name='prdetails'),
    path('logana/project/<id>/erp', erp_details, name='erp'),
    path('logana/projects', project_dash, name="dash"),
    path('logana/create/', create, name='prcreate'),
    path('logana/domain/', domain_dash, name='ddash'),
    path('logana/domain/<id>/', domain_list, name='prdomains'),
    path('logana/erp', erp_details, name='erp'),
    path('logana/project/<id>/erp', erp_details, name='erp'),
    path('logana/report/<id>/', report, name='lnreport'),
    path('logana/add-vip/', add_new_vip, name='new_vip_page'),
    path('logana/googlebot-report/<id>/', googlebot_report, name='ln_googlebot_report'),
    path('logana/project/<id>/google-main', googlebot_main, name='googlebot-main'),
    path('logana/project/<id>/google-mobile', googlebot_mobile, name='googlebot-mobile'),
    path('logana/project/<id>/google-desktop', googlebot_desktop, name='googlebot-desktop'),
    path('logana/project/<id>/google-imagebot', googlebot_images, name='googlebot-images'),
    path('logana/project/<id>/google-news', googlebot_news, name='googlebot-news'),
    path('logana/project/<id>/google-videobot', googlebot_video, name='googlebot-video'),
    path('logana/project/<id>/google-adsensebot', googlebot_ads, name='googlebot-ads'),
    path('logana/project/<id>/google-direct', googlebot_direct, name='googlebot-direct'),
    path('logana/project/<id>/google-indirect', googlebot_indirect, name='googlebot-indirect'),
    path('logana/project/<id>/bots', bot_details, name='bot_details'),

    #LOgana AI bots
    path('logana/ai-report/<int:id>', ai_bot_summary, name='aibotsumm'),
    path('logana/project/<id>/ai_individual', ai_bot_single, name="aibotsingle"),
    path('logana/project/<int:id>/rep/<int:bot_id>/', ai_bot_rep, name="aibotrep"),

    #Psi Application

    path('psi/domain_dash/', psi_domain_dash, name='psiddash'),
    path('psi/admin/', psi_admin, name='psiadmin'),
    path('psi/psi-add-new-domain.html/', add_new_domain, name='psiadddomain'),
    path('psi/psi-add-page-type.html/', add_new_ptype, name='ptype'),
    path('psi/psi-add-new-url.html/', add_new_url, name='psiurl'),
    path('psi/domain_dash-live/<id>/', crux_domain_explore, name='psiddashexplore'),
    path('psi/domain_dash/<id>/', crux_dbdomain_explore, name='psidbddashexplore'),
    path('psi/page-speed/<id>/', performance, name='psipsexplore'),
    path('psi/lighthouse/<id>/', lighthouse, name='lighthouseexplore'),
    path('psi/crux-url-explore-live/<id>', crux_url_explore, name = 'cruxurlexplre'),
    path('psi/crux-url-explore/<id>', cruxdb_url_explore, name = 'cruxdburlexplre'),
    path('psi/performance-report/<id>', monthly_performance_report, name = 'preport'),

    #Psi Alerting
    path('psi/alert-report/<id>', psi_alerts_report, name='weeklyrep'),


    #Dash
    path('django_plotly_dash/', include('django_plotly_dash.urls')),
    path('demo-session-var', add_to_session, name="session-variable-example"),

    #! topstories -------------------------------------------------------------------------
    path('topstories/report', top_stories_report, name='ts_report'),
    path('topstories/search_targets', search_targets, name='ts_search_targets'),
    path('topstories/keyword/<str:keyword>/', top_stories_single_report, name='ts_keyword'),
    #path('topstories/keyword/', top_stories_single_report, name='ts_keyword'),
    path('topstories/trigger/', trigger_script, name='trigger_script'),
    path('topstories/keyword_tracking/', keyword_tracking_handling, name='input_keywords'),

    # Google Indexing Script ----------------------------------------------------------------

    path('gis/indexing/', url_checker_view, name='url_checker'),
    path('gis/task_status/<str:task_id>/', task_status, name='task_status'),

    # Sitemap checker ----------------------------------------------------------------
    path('sitemap_checker/', check_sitemap_status),

    #drbot
    path('chatbot/', chat_view, name='chatbot'),

    #occ
    path('cache/', cache_analysis_view, name='cache_check'),

    #autorevap
    path('fsn/', dashboard_view, name='fsn'),

    # Google Trends
    path('google-trends/', include('google_trends.urls')),

    #llmdash
    path('llm-search/', llm_dashboard_view, name='llm-search'),
    path('export-pdf/', export_market_report_pdf, name='export_pdf'),

]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root = settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root = settings.MEDIA_ROOT)