from dotenv import load_dotenv
from datetime import date
import os
import shutil
import subprocess
import sys

load_dotenv()

SOURCE_HOST = os.getenv('DATABASE_SYNC_LOGANA_HOST')
SOURCE_USER = os.getenv('DATABASE_SYNC_LOGANA_USER')
SOURCE_PASS = os.getenv('DATABASE_SYNC_LOGANA_PASSWORD')
SOURCE_DB = os.getenv('DATABASE_SYNC_LOGANA_NAME')
DEST_HOST = os.getenv('DATABASE_LOGANA_HOST')
DEST_USER = os.getenv('DATABASE_LOGANA_USER')
DEST_PASS = os.getenv('DATABASE_LOGANA_PASSWORD')
DEST_DB = os.getenv('DATABASE_LOGANA_NAME')

DATE = date.today().strftime('%Y-%m-%d')
TEMP_DIR = f'/tmp/{DATE}'
DB_DUMP_PATH = f'{TEMP_DIR}/{SOURCE_DB}_{DATE}_dump.sql'
MYSQL_CONF_PATH = f'{TEMP_DIR}/.my.cnf'

os.mkdir(TEMP_DIR)

def auth() -> None:
  config = f'''[clientstaging]
user={SOURCE_USER}
password={SOURCE_PASS}
host={SOURCE_HOST}
[clientproduction]
user={DEST_USER}
password={DEST_PASS}
host={DEST_HOST}'''
  with open(MYSQL_CONF_PATH, 'w') as file:
    file.write(config)
  os.chmod(MYSQL_CONF_PATH, 0o600)

def dump() -> str:
  command = f'mysqldump --defaults-group-suffix=staging --defaults-file={MYSQL_CONF_PATH} {SOURCE_DB} --ignore-table={SOURCE_DB}.django_migrations  --add-drop-table --set-gtid-purged=OFF > {DB_DUMP_PATH}'
  try:
    subprocess.check_output(command, shell=True, stderr=subprocess.STDOUT)
  except subprocess.CalledProcessError as e:
    clean_up()
    sys.exit('Error: ' + e.output.decode())
  else:
    return 'Database dump created successfully.'

def sync() -> str:
  command = f'mysql --defaults-group-suffix=production --defaults-file={MYSQL_CONF_PATH} {DEST_DB} < {DB_DUMP_PATH}'
  try:
    subprocess.check_output(command, shell=True, stderr=subprocess.STDOUT)
  except subprocess.CalledProcessError as e:
    return 'Error: ' + e.output.decode()
  else:
    return 'Database sync completed successfully.'

def clean_up() -> str:
  if os.path.isdir(TEMP_DIR):
    shutil.rmtree(TEMP_DIR)
    return 'Clean up completed successfully.'
  return f'{TEMP_DIR} does not exist.'

def main() -> None:
  auth()
  
  print('Dumping staging database...')
  print(dump())

  print('Syncing production database...')
  print(sync())

  print('Cleaning up after db sync...')
  print(clean_up())

if __name__ == '__main__':
  main()
