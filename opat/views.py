from django.shortcuts import render
from django.contrib.auth import authenticate, login, logout
from django.shortcuts import redirect
from applications.views import apps
from logana.forms import RequestAccessForm
from django.contrib import messages
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
from alerts.models import CustomUser, Application
from django.contrib.auth.models import Group




def home(request):    
    return render(request, 'home.html', {})

def oplogin(request):

    if request.user.is_authenticated:
        return redirect(apps)
    

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
 
        allowed_domains = ["bettercollective.com"]
        if '@' in username:
            domain = username.split('@')[-1]
            if domain not in allowed_domains:
                messages.error(request, "Please use a business email address.")
                return render(request, 'login.html', {'form': None})

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            return redirect(apps)
        else:
            messages.error(request, "Invalid username or password.")
    
    return render(request, 'login.html', {'form': None})
    
    
def request_access(request):
    '''User requests access via form'''
    if request.method == 'POST':
        form = RequestAccessForm(request.POST)
        if form.is_valid():
            first_name = form.cleaned_data.get("first_name")
            last_name = form.cleaned_data.get("last_name")
            email = form.cleaned_data.get("email")
            slack_handle = form.cleaned_data.get("slack_handle")
            team_id = form.cleaned_data.get("teams")
            application_ids = form.cleaned_data.get("applications")
            

            if CustomUser.objects.filter(email=email).exists():
                messages.error(request, "A user with this email already exists!")
                return redirect(request_access)
            
            user = CustomUser.objects.create_user(
                email=email,
                password="temppass",
                full_name=first_name + " " + last_name,
                slack_handle=slack_handle,
                is_active=False
            )    

            try:
                team = Group.objects.get(id=team_id) 
                user.groups.add(team)
            except Group.DoesNotExist:
                messages.error(request, "The selected team does not exist.")


            if application_ids:
                applications = Application.objects.filter(id__in=application_ids) 
                user.apps.add(*applications)  

    
         
            messages.success(request, "Your request has been submitted successfully.")

            return redirect('home')  
        else:

            messages.error(request, "There was an error with your submission. Please check the form and try again.")
    else:
        form = RequestAccessForm()

    return render(request, 'request.html', {"form": form})



def oplogout(request):
    logout(request)
    return redirect(home)
