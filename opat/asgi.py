import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'opat.settings')


from channels.routing import ProtocolTypeRouter, URLRouter
from django.core.asgi import get_asgi_application
from channels.auth import AuthMiddlewareStack
from django.urls import path
from opat.routing import application as dpd_application



application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": dpd_application,
})

