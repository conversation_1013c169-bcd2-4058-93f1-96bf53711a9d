"""
Django settings for opat project.

Generated by 'django-admin startproject' using Django 3.2.8.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import os
from pathlib import Path
import mimetypes
from decouple import config


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
#SECRET_KEY = 'django-insecure-!)$b1ww0!864&l*635j8r+6d@#nk)h2envfpli@zb^=u32yc*&'
SECRET_KEY = config('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['opat.bettercollective.rocks', 'opat-staging.bettercollective.rocks', 'opat.bc.rocks', 'opat-staging.bc.rocks','localhost', '127.0.0.1']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    'channels',
    'alerts',
    'bootstrap4',
    
    'applications',
    'logana',
    'psi',
    'ts',
    'gis',
    'sitemap_checker',
    'drbot',
    'occ',
    'autorevap',
    'google_trends',  # Add your new app here
    'gsc',
    'llmdash',

    'django_plotly_dash.apps.DjangoPlotlyDashConfig',
    'dpd_static_support',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',

    'whitenoise.middleware.WhiteNoiseMiddleware',

    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',

    'django_plotly_dash.middleware.BaseMiddleware',
    'django_plotly_dash.middleware.ExternalRedirectionMiddleware',

    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'opat.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

mimetypes.add_type("image/svg+xml", ".svg", True)
mimetypes.add_type("image/svg+xml", ".svgz", True)

X_FRAME_OPTIONS = 'SAMEORIGIN'

#WSGI_APPLICATION = 'opat.wsgi.application'
ASGI_APPLICATION = 'opat.asgi.application'

# database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    'default':{
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_NAME'),#'mysqldb',
        'USER': config('DATABASE_USER'),#'root',
        'PASSWORD': config('DATABASE_PASSWORD'),
        'PORT': config('DATABASE_PORT'),#3306,
        'HOST': config('DATABASE_HOST'),#'127.0.0.1',
    },
    'main_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_NAME'),#'mysqldb',
        'USER': config('DATABASE_USER'),#'root',
        'PASSWORD': config('DATABASE_PASSWORD'),
        'PORT': config('DATABASE_PORT'),#3306,
        'HOST': config('DATABASE_HOST'),#'127.0.0.1',


    },
    'apps_db':{
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_APPLICATIONS_NAME'),#'mysqldb',
        'USER': config('DATABASE_APPLICATIONS_USER'),#'root',
        'PASSWORD': config('DATABASE_APPLICATIONS_PASSWORD'),
        'PORT': config('DATABASE_APPLICATIONS_PORT'),#3306,
        'HOST': config('DATABASE_APPLICATIONS_HOST'),#'127.0.0.1',


    },
    'logana_db':{
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_LOGANA_NAME'),#'mysqldb',
        'USER': config('DATABASE_LOGANA_USER'),#'root',
        'PASSWORD': config('DATABASE_LOGANA_PASSWORD'),
        'PORT': config('DATABASE_LOGANA_PORT'),#3306,
        'HOST': config('DATABASE_LOGANA_HOST'),#'127.0.0.1',

    },
    'psi_db':{
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_PSI_NAME'),#'mysqldb',
        'USER': config('DATABASE_PSI_USER'),#'root',
        'PASSWORD': config('DATABASE_PSI_PASSWORD'),
        'PORT': config('DATABASE_PSI_PORT'),#3306,
        'HOST': config('DATABASE_PSI_HOST'),#'127.0.0.1',
    },
    'ts_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_TOPSTORIES_NAME'),  # 'mysqldb',
        'USER': config('DATABASE_TOPSTORIES_USER'),  # 'root',
        'PASSWORD': config('DATABASE_TOPSTORIES_PASSWORD'),
        'PORT': config('DATABASE_TOPSTORIES_PORT'),  # 3306,
        'HOST': config('DATABASE_TOPSTORIES_HOST'),  # '127.0.0.1',
    },
    'alerts_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_ALARMS_NAME'),  # 'mysqldb',
        'USER': config('DATABASE_ALARMS_USER'),  # 'root',
        'PASSWORD': config('DATABASE_ALARMS_PASSWORD'),
        'PORT': config('DATABASE_ALARMS_PORT'),  # 3306,
        'HOST': config('DATABASE_ALARMS_HOST'),  # '127.0.0.1',
    },
    'gis_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_GIS_NAME'),  # 'mysqldb',
        'USER': config('DATABASE_GIS_USER'),  # 'root',
        'PASSWORD': config('DATABASE_GIS_PASSWORD'),
        'PORT': config('DATABASE_GIS_PORT'),  # 3306,
        'HOST': config('DATABASE_GIS_HOST'),  # '127.0.0.1',

    },

    'sc_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_SC_NAME'),#'mysqldb',
        'USER': config('DATABASE_SC_USER'),#'root',
        'PASSWORD': config('DATABASE_SC_PASSWORD'),
        'PORT': config('DATABASE_SC_PORT'),#3306,
        'HOST': config('DATABASE_SC_HOST'),#'127.0.0.1',
    },


    'drbot_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_DRBOT_NAME'),#'mysqldb',
        'USER': config('DATABASE_DRBOT_USER'),#'root',
        'PASSWORD': config('DATABASE_DRBOT_PASSWORD'),
        'PORT': config('DATABASE_DRBOT_PORT'),#3306,
        'HOST': config('DATABASE_DRBOT_HOST'),#'127.0.0.1',
    },
    'occ_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_OCC_NAME'),#'mysqldb',
        'USER': config('DATABASE_OCC_USER'),#'root',
        'PASSWORD': config('DATABASE_OCC_PASSWORD'),
        'PORT': config('DATABASE_OCC_PORT'),#3306,
        'HOST': config('DATABASE_OCC_HOST'),#'127.0.0.1',
    },    
    'autorevap_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_ARP_NAME'),#'mysqldb',
        'USER': config('DATABASE_ARP_USER'),#'root',
        'PASSWORD': config('DATABASE_ARP_PASSWORD'),
        'PORT': config('DATABASE_ARP_PORT'),#3306,
        'HOST': config('DATABASE_ARP_HOST'),#'127.0.0.1',
    },
    'google_trends_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DATABASE_GT_NAME'),#'mysqldb',
        'USER': config('DATABASE_GT_USER'),#'root',
        'PASSWORD': config('DATABASE_GT_PASSWORD'),
        'PORT': config('DATABASE_GT_PORT'),#3306,
        'HOST': config('DATABASE_GT_HOST'),#'127.0.0.1',
    },     

}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True




# Plotly dash settings

PLOTLY_DASH = {
    "ws_route" : "ws/channel",

    "insert_demo_migrations" : True,  # Insert model instances used by the demo

    "http_poke_enabled" : True, # Flag controlling availability of direct-to-messaging http endpoint

    "view_decorator" : None, # Specify a function to be used to wrap each of the dpd view functions

    "cache_arguments" : True, # True for cache, False for session-based argument propagation

    #"serve_locally" : True, # True to serve assets locally, False to use their unadulterated urls (eg a CDN)

    "stateless_loader" : "logana.scaffold.stateless_app_loader",
    }

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'
MEDIA_URL = '/media/'
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static_in_env')]
STATIC_ROOT = os.path.join(BASE_DIR, 'static')
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')


# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

#SESSION_SAVE_EVERY_REQUEST = True


DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

LOGIN_REDIRECT_URL = '/apps'

# Caching -  uses redis as this is present due to channels use

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": config("REDIS_HOST"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient"
        },
        "KEY_PREFIX": "dpd-opat"
    }
}

# Channels config, to use channel layers

CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('127.0.0.1', 6379),],
        },
    },
}

# Staticfiles finders for locating dash app assets and related files

STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',

    'django_plotly_dash.finders.DashAssetFinder',
    'django_plotly_dash.finders.DashComponentFinder',
    'django_plotly_dash.finders.DashAppDirectoryFinder',
]

# Plotly components containing static content that should
# be handled by the Django staticfiles infrastructure

PLOTLY_COMPONENTS = [

    'dash_bootstrap_components',
    'dpd_components',
    'dpd_static_support',
]


#database routers config

DATABASE_ROUTERS = [
    'opat.main_router.AuthRouter',
    'applications.app_router.OpatApps',
    'logana.logana_router.LoganaRouter',
    'psi.psi_router.PsiRouter',
    'ts.ts_router.TsRouter',
    'alerts.alerts_router.AlertsRouter',
    'gis.gis_router.GisRouter',
    'sitemap_checker.sitemap_checker_router.SitemapCheckerRouter',
    'drbot.drbot_router.DrBotRouter',
    'occ.occ_router.OccRouter',
    'autorevap.autor_router.AuthorAppRouter',
    'google_trends.google_trends_router.GoogleTrendsRouter',
    'gsc.gsc_router.GscRouter',    
    'llmdash.llmdash_router.LlmDashAppRouter',
    ]


# settings.py

# CELERY_BROKER_URL = 'redis://localhost:6379/0'
# CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'


#Registering Custom User System
AUTH_USER_MODEL = 'alerts.CustomUser'


# Redis Configuration
REDIS_URL = config('REDIS_HOST')

# Celery Configuration
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 3600  # 1 hour
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True


CSRF_TRUSTED_ORIGINS = config(
    'CSRF_TRUSTED_ORIGINS',
    cast=lambda v: [s.strip() for s in v.split(',')]
)


