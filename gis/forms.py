from django import forms

class URLCheckerForm(forms.Form):
    url_text = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 10, 'class': 'form-control'}),
        required=False,
        label="Enter URLs (one per line):"
    )
    file_upload = forms.FileField(
        required=False,
        widget=forms.ClearableFileInput(attrs={'class': 'form-control-file'}),
        label="Or upload a file:"
    )

    def clean(self):
        cleaned_data = super().clean()
        url_text = cleaned_data.get('url_text')
        file_upload = cleaned_data.get('file_upload')

        if not url_text and not file_upload:
            raise forms.ValidationError("You must either enter URLs or upload a file.")

        return cleaned_data