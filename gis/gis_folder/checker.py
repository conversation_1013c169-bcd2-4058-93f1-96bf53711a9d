import csv
import asyncio
import random
import shutil

import aiohttp
from bs4 import BeautifulSoup
from urllib.parse import urlencode
from datetime import datetime
from gis.gis_folder import config_file
from random import uniform
import ssl
from aiolimiter import AsyncLimiter
import subprocess
import os

# Import the global variables
from gis.gis_folder import globals
now = datetime.now()
start_time = now.strftime("%H:%M:%S")

def run_checker_script(urlinput, output, retry_url_file, max_retries, max_script_runs):
    print("Running checker script")
    print(os.path.abspath(urlinput))
    print(os.path.abspath(retry_url_file))
    print(os.path.abspath(output))

    urls = open(urlinput, "r").read().splitlines()
    print(urls)

    # Configure the rate limit for concurrent requests
    rate_limit = 200  # Maximum X concurrent requests at a time

    f = open(output, "a", newline="\n", encoding="utf-8")
    writer = csv.writer(f)

    # Smartproxy settings
    username = 'l<PERSON><PERSON>ovic'
    password = 'u923mfeA2JVktm+mUp'
    proxy_ip = '*************'
    proxy_port = '7000'
    proxy_auth = aiohttp.BasicAuth(username, password)
    proxy = f"http://{username}:{password}@*************:{proxy_port}"
    print(proxy)
    proxies = {
      'http': proxy,
      'https': proxy
    }

    # Configure SSL context to ignore certificate verification (use with caution)
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    # Set to keep track of processed URLs
    processed_urls = set()

    async def check_indexed(session, url, limiter, retries=0):
        # Select a random user agent for each request
        ua = random.choice(config_file.user_agents_list)
        headers = {
            'User-Agent': ua,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }

        query = {'q': 'site:' + url}
        google = "https://www.google.com/search?" + urlencode(query)

        async with limiter:  # Enforce rate limit for concurrent requests
            try:
                async with session.get(google, headers=headers, proxy=proxy, proxy_auth=proxy_auth, ssl=ssl_context) as resp:
                    print(ua)
                    data = await resp.text()
                    soup = BeautifulSoup(data, "html.parser")

                    try:
                        check = soup.find_all('div', class_=lambda x: x and 'kCrYT' in x.split())[0].find("h3")
                        if check:
                            if url not in processed_urls:
                                result = [url, "True"]
                                globals.global_results.append(result)
                                writer.writerow(result)
                                processed_urls.add(url)
                                print(f"{url} is indexed!")
                        else:
                            if url not in processed_urls:
                                result = [url, "False"]
                                globals.global_results.append(result)
                                writer.writerow(result)
                                processed_urls.add(url)
                                print(f"{url} is NOT indexed!")
                    except TypeError:
                        if url not in processed_urls:
                            result = [url, "False"]
                            globals.global_results.append(result)
                            writer.writerow(result)
                            processed_urls.add(url)
                        print(f"{url} is NOT indexed!")
                    except IndexError as ie:
                        print(f"{url} RobotCheckError!")
                        raise ie  # Raise IndexError to trigger retry in the retry wrapper

                    await asyncio.sleep(uniform(3, 6))
            except aiohttp.ClientResponseError as e:
                if e.status == 429:
                    print("Too Many Requests (429). Retrying...")
                    raise e  # Raise exception to trigger retry in the retry wrapper
                else:
                    print(e)
                    if url not in processed_urls:
                        result = [url, "RequestError"]
                        globals.global_results.append(result)
                        writer.writerow(result)
                        processed_urls.add(url)
                    return False  # Handle other client response errors without retries

            except aiohttp.ClientError as e:
                print(f"Request failed: {e}")
                if url not in processed_urls:
                    result = [url, "ClientError"]
                    globals.global_results.append(result)
                    writer.writerow(result)
                    processed_urls.add(url)
                return False  # Handle other client errors without retries

    async def check_indexed_with_retry(session, url, limiter):
        retries = 0
        while retries < max_retries:
            try:
                return await check_indexed(session, url, limiter, retries)
            except (IndexError, aiohttp.ClientResponseError) as e:
                retries += 1
                print(f"Error encountered for URL {url}. Retrying {retries}/{max_retries}...")
                await asyncio.sleep(10)  # Wait before retrying
                continue
            except Exception as e:
                print(e)
                break

        # If max retries are exceeded, add to retry list
        if retries >= max_retries and url not in processed_urls:
            with open(retry_url_file, "a") as retry_file:
                retry_file.write(f"{url}\n")
            print(f"{url} added to retry list.")

    async def main():
        async with aiohttp.ClientSession() as session:
            limiter = AsyncLimiter(rate_limit, 1)
            tasks = [check_indexed_with_retry(session, line.strip(), limiter) for line in urls]
            await asyncio.gather(*tasks)

    now = datetime.now()
    start_time = now.strftime("%H:%M:%S")
    asyncio.run(main())
    now = datetime.now()
    end_time = now.strftime("%H:%M:%S")
    print(start_time)
    print(end_time)

    f.close()

    return os.path.exists(retry_url_file)


def handle_retries(urlinput, output, retry_url_file, max_retries, max_script_runs):
    globals.global_results = []

    try:
        script_runs = 1
        while script_runs <= max_script_runs:
            has_retry_urls = run_checker_script(urlinput, output, retry_url_file, max_retries, max_script_runs)
            if not has_retry_urls:
                break

            print(f"Retry attempt {script_runs}/{max_script_runs}")

            # Load retry URLs
            with open(retry_url_file, "r") as file:
                retry_urls = list(set(file.read().strip().splitlines()))  # Remove duplicates

            # Update input file with retry URLs
            with open(urlinput, "w") as file:
                file.write("\n".join(retry_urls))

            # Clear the retry file
            os.remove(retry_url_file)

            script_runs += 1

        if script_runs > max_script_runs:
            print("Max script runs exceeded. Exiting...")

    finally:
        globals.is_processing = False  # Set to False when processing is complete

        # Clean up tmp_gis directory
        tmp_gis_path = os.path.dirname(urlinput)  # Assuming urlinput is in the tmp_gis directory
        try:
            for filename in os.listdir(tmp_gis_path):
                file_path = os.path.join(tmp_gis_path, filename)
                if os.path.isfile(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            print(f"All files in {tmp_gis_path} have been removed.")
        except Exception as e:
            print(f"Error while cleaning up {tmp_gis_path}: {e}")
