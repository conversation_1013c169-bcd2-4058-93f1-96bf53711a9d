import time
import pandas as pd
import hrequests
from urllib.parse import quote
import random
from hrequests.proxies import evomi
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
import threading
from typing import List, Dict
import logging
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants - Optimized for faster processing
MAX_WORKERS = 3  # Reduced from 5 to 3 to prevent overload
BATCH_SIZE = 5   # Reduced from 10 to 5 for faster processing
RETRY_ATTEMPTS = 2  # Reduced from 3 to 2 to speed up processing
DELAY_MIN = 0.5  # Reduced from 1 to 0.5
DELAY_MAX = 1.5  # Reduced from 3 to 1.5

# Initialize proxy with timeout
proxy = evomi.ResidentialProxy(
    username='dlukic0', 
    key='jERbNQ3rXR3o1DnVKqBN',
    # timeout=30  # Added timeout
)

def read_urls(filename: str) -> List[str]:
    """Read URLs from file and return as list."""
    try:
        with open(filename, 'r') as f:
            return [line.strip() for line in f if line.strip()]
    except Exception as e:
        logger.error(f"Error reading URLs file: {str(e)}")
        return []

def create_google_search_url(url: str) -> str:
    """Create Google search URL with site: operator."""
    search_query = f"site:{url}"
    encoded_query = quote(search_query)
    return f"https://www.google.com/search?q={encoded_query}&sourceid=chrome&ie=UTF-8"

def process_url(url: str, session: hrequests.Session) -> Dict:
    """Process a single URL and return result."""
    for attempt in range(RETRY_ATTEMPTS):
        try:
            search_url = create_google_search_url(url)
            logger.info(f"Checking: {search_url} (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            
            resp = session.get(search_url, timeout=30)  # Added timeout
            with resp.render(mock_human=True, headless=True) as page:
                time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
                
                # Accept cookies if present
                try:
                    cookie_button = page.find("button:has-text('Accept all')")
                    if cookie_button:
                        cookie_button.click()
                        time.sleep(0.5)  # Short delay after clicking
                except:
                    pass
                
                # Check for search results
                try:
                    search_div = page.find("div#search")
                    if search_div:
                        mjj_yud = search_div.find("div.MjjYud")
                        if mjj_yud:
                            logger.info(f"Found {url} Indexed")
                            return {"url": url, "indexation_status": "True"}
                except:
                    pass
                
                logger.info(f"Not found {url} Indexed")
                return {"url": url, "indexation_status": "False"}
                
        except Exception as e:
            logger.error(f"Error for {url} (Attempt {attempt + 1}): {str(e)}")
            if attempt == RETRY_ATTEMPTS - 1:
                return {"url": url, "indexation_status": "Error"}
            time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    return {"url": url, "indexation_status": "Error"}

def worker(url_queue: Queue, results_queue: Queue):
    """Worker function to process URLs from queue."""
    try:
        session = hrequests.Session(proxy=proxy)
        
        while True:
            try:
                url = url_queue.get_nowait()
                try:
                    result = process_url(url, session)
                    results_queue.put(result)
                finally:
                    url_queue.task_done()
            except Empty:
                break
            except Exception as e:
                logger.error(f"Worker error: {str(e)}")
    except Exception as e:
        logger.error(f"Session creation error: {str(e)}")

def process_batch(urls: List[str]) -> List[Dict]:
    """Process a batch of URLs concurrently."""
    if not urls:
        return []
        
    url_queue = Queue()
    results_queue = Queue()
    
    for url in urls:
        url_queue.put(url)
    
    threads = []
    for _ in range(min(MAX_WORKERS, len(urls))):
        t = threading.Thread(target=worker, args=(url_queue, results_queue))
        t.start()
        threads.append(t)
    
    # Wait for all threads with timeout
    for t in threads:
        t.join(timeout=60)  # 60 second timeout per thread
    
    results = []
    while not results_queue.empty():
        results.append(results_queue.get())
    
    return results

def run_optimized_checker(urlinput: str, output_file: str) -> List[Dict]:
    """Main function to run the optimized checker."""
    try:
        # Read URLs
        urls = read_urls(urlinput)
        if not urls:
            logger.error("No URLs found in input file")
            return []
            
        logger.info(f"Loaded {len(urls)} URLs")
        
        # Process URLs in batches
        all_results = []
        for i in range(0, len(urls), BATCH_SIZE):
            batch = urls[i:i + BATCH_SIZE]
            logger.info(f"Processing batch {i//BATCH_SIZE + 1}/{(len(urls) + BATCH_SIZE - 1)//BATCH_SIZE}")
            batch_results = process_batch(batch)
            all_results.extend(batch_results)
            
            # Save intermediate results
            try:
                df = pd.DataFrame(all_results)
                df.to_csv(output_file, index=False)
                logger.info(f"Saved intermediate results ({len(all_results)}/{len(urls)})")
            except Exception as e:
                logger.error(f"Error saving results: {str(e)}")
        
        logger.info("All URLs processed. Results saved to indexation_results.csv")
        return all_results
    except Exception as e:
        logger.error(f"Fatal error in run_optimized_checker: {str(e)}")
        return [] 