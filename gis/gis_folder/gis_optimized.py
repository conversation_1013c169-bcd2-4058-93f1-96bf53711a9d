import time
import pandas as pd
import hrequests
from urllib.parse import quote
import random
from hrequests.proxies import evomi
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
import threading
from typing import List, Dict
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
MAX_WORKERS = 5  # Number of concurrent workers
BATCH_SIZE = 10  # Number of URLs to process in each batch
RETRY_ATTEMPTS = 3  # Number of retry attempts for failed requests
DELAY_MIN = 1  # Minimum delay between requests
DELAY_MAX = 3  # Maximum delay between requests

# Initialize proxy
proxy = evomi.ResidentialProxy(username='dlukic0', key='jERbNQ3rXR3o1DnVKqBN')

def read_urls(filename: str) -> List[str]:
    """Read URLs from file and return as list."""
    with open(filename, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def create_google_search_url(url: str) -> str:
    """Create Google search URL with site: operator."""
    search_query = f"site:{url}"
    encoded_query = quote(search_query)
    return f"https://www.google.com/search?q={encoded_query}&sourceid=chrome&ie=UTF-8"

def process_url(url: str, session: hrequests.Session) -> Dict:
    """Process a single URL and return result."""
    for attempt in range(RETRY_ATTEMPTS):
        try:
            search_url = create_google_search_url(url)
            logger.info(f"Checking: {search_url} (Attempt {attempt + 1}/{RETRY_ATTEMPTS})")
            
            resp = session.get(search_url)
            with resp.render(mock_human=True, headless=True) as page:
                time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
                
                # Accept cookies if present
                try:
                    cookie_button = page.find("button:has-text('Accept all')")
                    if cookie_button:
                        cookie_button.click()
                except:
                    pass
                
                # Check for search results
                try:
                    search_div = page.find("div#search")
                    if search_div:
                        mjj_yud = search_div.find("div.MjjYud")
                        if mjj_yud:
                            logger.info(f"Found {url} Indexed")
                            return {"url": url, "indexation_status": "Yes"}
                except:
                    pass
                
                logger.info(f"Not found {url} Indexed")
                return {"url": url, "indexation_status": "No"}
                
        except Exception as e:
            logger.error(f"Error for {url} (Attempt {attempt + 1}): {str(e)}")
            if attempt == RETRY_ATTEMPTS - 1:
                return {"url": url, "indexation_status": "Error"}
            time.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    return {"url": url, "indexation_status": "Error"}

def worker(url_queue: Queue, results_queue: Queue):
    """Worker function to process URLs from queue."""
    session = hrequests.Session(proxy=proxy)
    
    while True:
        try:
            url = url_queue.get_nowait()
            try:
                result = process_url(url, session)
                results_queue.put(result)
            finally:
                url_queue.task_done()  # Only call task_done() if we successfully got an item
        except Empty:
            break
        except Exception as e:
            logger.error(f"Worker error: {str(e)}")
            # Don't call task_done() here as we didn't get an item from the queue

def process_batch(urls: List[str]) -> List[Dict]:
    """Process a batch of URLs concurrently."""
    url_queue = Queue()
    results_queue = Queue()
    
    # Add URLs to queue
    for url in urls:
        url_queue.put(url)
    
    # Start workers
    threads = []
    for _ in range(min(MAX_WORKERS, len(urls))):
        t = threading.Thread(target=worker, args=(url_queue, results_queue))
        t.start()
        threads.append(t)
    
    # Wait for all URLs to be processed
    url_queue.join()
    
    # Collect results
    results = []
    while not results_queue.empty():
        results.append(results_queue.get())
    
    return results

def main():
    # Read URLs
    urls = read_urls('urlinput.txt')
    logger.info(f"Loaded {len(urls)} URLs")
    
    # Process URLs in batches
    all_results = []
    for i in range(0, len(urls), BATCH_SIZE):
        batch = urls[i:i + BATCH_SIZE]
        logger.info(f"Processing batch {i//BATCH_SIZE + 1}/{(len(urls) + BATCH_SIZE - 1)//BATCH_SIZE}")
        batch_results = process_batch(batch)
        all_results.extend(batch_results)
        
        # Save intermediate results
        df = pd.DataFrame(all_results)
        df.to_csv('indexation_results.csv', index=False)
        logger.info(f"Saved intermediate results ({len(all_results)}/{len(urls)})")
    
    logger.info("All URLs processed. Results saved to indexation_results.csv")

if __name__ == "__main__":
    main()
