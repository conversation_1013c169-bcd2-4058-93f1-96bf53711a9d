from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from django.conf import settings
from decouple import config
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'opat.settings')

# Get Redis URL from config
REDIS_URL = config('REDIS_HOST')
logger.info(f"Using Redis URL: {REDIS_URL}")

# Create Celery app
app = Celery('gis')

# Configure Celery
app.conf.update(
    broker_url=REDIS_URL,
    result_backend=REDIS_URL,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,  # 1 hour
    worker_prefetch_multiplier=1,  # Disable prefetch
    task_acks_late=True,  # Only acknowledge task after completion
    task_reject_on_worker_lost=True,  # Requeue task if worker dies
    imports=('gis.tasks',),  # Explicitly import tasks
)

# Load task modules from all registered Django app configs.
app.config_from_object('django.conf:settings')

# Auto-discover tasks in all installed apps
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

@app.task(bind=True)
def debug_task(self):
    logger.info(f'Request: {self.request!r}')

# Register tasks explicitly
from gis.tasks import run_checker_task
app.tasks.register(run_checker_task)
