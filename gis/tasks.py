from celery import shared_task
from celery.result import AsyncResult
from gis.gis_folder.gis_optimized import process_batch, read_urls
import logging
from opat.celery import app
import os
import shutil

logger = logging.getLogger(__name__)

@app.task(
    name='gis.run_checker_task',
    bind=True,
    max_retries=3,
    default_retry_delay=60,
    rate_limit='10/m',
    acks_late=True,
    reject_on_worker_lost=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    retry_jitter=True
)
def run_checker_task(self, urlinput, output_file, retry_url_file, max_retries, max_script_runs):
    """
    Celery task to run the URL checker script.
    """
    try:
        logger.info(f"Starting URL checker task with input file: {urlinput}")
        
        # Check if input file exists
        if not os.path.exists(urlinput):
            logger.error(f"Input file not found: {urlinput}")
            # Don't retry if file doesn't exist
            return {
                'error': 'Input file not found',
                'status': 'FAILURE',
                'results': []
            }
        
        # Read URLs from input file
        urls = read_urls(urlinput)
        total_urls = len(urls)
        logger.info(f"Loaded {total_urls} URLs to process")
        
        # Process URLs in batches and update task info with partial results
        all_results = []
        batch_size = 10
        
        for i in range(0, total_urls, batch_size):
            batch = urls[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(total_urls + batch_size - 1)//batch_size}")
            
            # Process the batch
            batch_results = process_batch(batch)
            logger.info(f"Batch results: {batch_results}")
            
            # Convert results to the format expected by the frontend
            formatted_batch_results = []
            for result in batch_results:
                formatted_result = {
                    'url': result['url'],
                    'status': 'True' if result['indexation_status'] == 'Yes' else 'False'
                }
                formatted_batch_results.append(formatted_result)
                logger.info(f"Formatted result: {formatted_result}")
            
            all_results.extend(formatted_batch_results)
            logger.info(f"Current all_results: {all_results}")
            
            # Update task info with current results and progress
            current = min(i + len(batch), total_urls)
            self.update_state(
                state='STARTED',
                meta={
                    'current': current,
                    'total': total_urls,
                    'results': all_results,
                    'status': f'Processing URLs... ({current}/{total_urls})'
                }
            )
            
            logger.info(f"Processed {len(all_results)}/{total_urls} URLs")
        
        # Send final progress update
        self.update_state(
            state='STARTED',
            meta={
                'current': total_urls,
                'total': total_urls,
                'results': all_results,
                'status': f'Processing URLs... ({total_urls}/{total_urls})'
            }
        )
        
        logger.info(f"Final results: {all_results}")
        
        # Clean up temporary files after successful completion
        tmp_gis_dir = os.path.dirname(urlinput)
        try:
            if os.path.exists(tmp_gis_dir):
                shutil.rmtree(tmp_gis_dir)
                logger.info(f"Cleaned up temporary directory: {tmp_gis_dir}")
        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {str(e)}")
        
        return {
            'status': 'SUCCESS',
            'results': all_results,
            'current': total_urls,
            'total': total_urls
        }
        
    except Exception as e:
        logger.error(f"Error in URL checker task: {str(e)}")
        # Only retry for non-file-not-found errors
        if not isinstance(e, FileNotFoundError):
            self.retry(exc=e)
        return {
            'error': str(e),
            'status': 'FAILURE',
            'results': []
        }
