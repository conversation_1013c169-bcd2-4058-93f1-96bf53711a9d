# views.py
import csv
import os
from django.http import JsonResponse
from django.shortcuts import render
from gis.gis_folder.checker import run_checker_script, handle_retries
from django.views.decorators.csrf import csrf_exempt
from .forms import URLCheckerForm
import threading
from gis.gis_folder import globals
import logging
from celery import shared_task
from celery.result import AsyncResult
from gis.gis_folder.gis_optimized import process_batch, read_urls
from opat.celery import app
import shutil

logger = logging.getLogger(__name__)

# @csrf_exempt
# def url_checker_view(request):
#     max_retries = 3
#     max_script_runs = 3
#     tmp_gis_dir = 'gis/gis_folder/tmp_gis'
#     retry_url_file = 'gis/gis_folder/tmp_gis/retry_urls.txt'
#     output_file = 'gis/gis_folder/tmp_gis/output.csv'
#
#     os.makedirs(tmp_gis_dir, exist_ok=True)
#
#     if request.method == 'POST':
#         form = URLCheckerForm(request.POST, request.FILES)
#         if form.is_valid():
#             urlinput = 'gis/gis_folder/tmp_gis/input_urls.txt'
#
#             if form.cleaned_data['file_upload']:
#                 with open(urlinput, 'wb+') as destination:
#                     for chunk in form.cleaned_data['file_upload'].chunks():
#                         destination.write(chunk)
#             else:
#                 with open(urlinput, 'w') as f:
#                     f.write(form.cleaned_data['url_text'])
#
#             # Clear previous results
#             globals.global_results.clear()
#             globals.is_processing = True
#
#             # Run the checker script in a separate thread
#             thread = threading.Thread(target=handle_retries, args=(urlinput, output_file, retry_url_file, max_retries, max_script_runs))
#             thread.start()
#
#             # If it's an AJAX request, return a simple confirmation
#             if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
#                 return JsonResponse({'status': 'processing'})
#
#             # If it's a regular POST request, render the template
#             return render(request, 'gis/gis_main_page.html', {'form': form})
#
#     elif request.method == 'GET' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
#         # This is for AJAX polling
#         indexed_count = sum(1 for row in globals.global_results if row[1] == 'True')
#         not_indexed_count = sum(1 for row in globals.global_results if row[1] == 'False')
#         return JsonResponse({
#             'results': globals.global_results,
#             'indexed_count': indexed_count,
#             'not_indexed_count': not_indexed_count,
#             'is_processing': globals.is_processing
#         })
#
#     else:
#         form = URLCheckerForm()
#
#     return render(request, 'gis/gis_main_page.html', {'form': form})



from .tasks import run_checker_task

@csrf_exempt
def url_checker_view(request):
    """
    Handles URL checker functionality. Accepts URLs through file upload or text input,
    triggers a Celery task to process them, and allows AJAX polling for progress updates.
    """
    max_retries = 3
    max_script_runs = 3
    tmp_gis_dir = 'gis/gis_folder/tmp_gis'
    retry_url_file = 'gis/gis_folder/tmp_gis/retry_urls.txt'
    output_file = 'gis/gis_folder/tmp_gis/output.csv'

    # Clean up any existing temporary files
    if os.path.exists(tmp_gis_dir):
        shutil.rmtree(tmp_gis_dir)
    os.makedirs(tmp_gis_dir, exist_ok=True)

    if request.method == 'POST':
        form = URLCheckerForm(request.POST, request.FILES)
        if form.is_valid():
            # Purge the Redis queue before starting new tasks
            app.control.purge()
            
            # Determine whether input is from a file or text area
            urlinput = 'gis/gis_folder/tmp_gis/input_urls.txt'

            if form.cleaned_data['file_upload']:
                with open(urlinput, 'wb+') as destination:
                    for chunk in form.cleaned_data['file_upload'].chunks():
                        destination.write(chunk)
            else:
                with open(urlinput, 'w') as f:
                    f.write(form.cleaned_data['url_text'])

            # Count total URLs
            total_urls = 0
            with open(urlinput, 'r') as f:
                total_urls = sum(1 for line in f if line.strip())
            logger.info(f"Total URLs to process: {total_urls}")

            # Clear any previous global results and set processing flag
            globals.global_results.clear()
            globals.is_processing = True

            try:
                # Trigger the Celery task for URL checking
                task = run_checker_task.apply_async(
                    args=[urlinput, output_file, retry_url_file, max_retries, max_script_runs],
                    countdown=1  # Add a small delay to ensure proper task registration
                )
                task_id = task.id
                logger.info(f"Task created with ID: {task_id}")

                # For AJAX requests, return task ID and status
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'task_id': task_id,
                        'status': 'processing',
                        'message': 'Task started successfully',
                        'total_urls': total_urls
                    })

                # For normal form submission, render the template
                return render(request, 'gis/gis_main_page.html', {'form': form})
            except Exception as e:
                logger.error(f"Error creating task: {str(e)}")
                return JsonResponse({
                    'status': 'error',
                    'message': f'Failed to start task: {str(e)}'
                }, status=500)

    elif request.method == 'GET' and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # Polling the results using AJAX
        indexed_count = sum(1 for row in globals.global_results if row[1] == 'True')
        not_indexed_count = sum(1 for row in globals.global_results if row[1] == 'False')
        logger.debug(f"Current counts - Indexed: {indexed_count}, Not Indexed: {not_indexed_count}")

        return JsonResponse({
            'results': globals.global_results,
            'indexed_count': indexed_count,
            'not_indexed_count': not_indexed_count,
            'is_processing': globals.is_processing
        })

    else:
        # Handle GET request with no AJAX (regular page load)
        form = URLCheckerForm()

    return render(request, 'gis/gis_main_page.html', {'form': form})


from celery.result import AsyncResult
from django.http import JsonResponse

def task_status(request, task_id):
    """Handle task status polling."""
    try:
        result = AsyncResult(task_id)
        logger.info(f"Checking status for task {task_id}: {result.state}")
        logger.info(f"Task info: {result.info}")

        if result.state == 'PENDING':
            response = {
                'state': result.state,
                'status': 'Pending...',
                'info': 'Task is waiting for execution or unknown.'
            }
        elif result.state == 'STARTED':
            # Get partial results if available
            if hasattr(result, 'info') and result.info:
                logger.info(f"Processing STARTED state with info: {result.info}")
                try:
                    # Extract results from the meta dictionary
                    if isinstance(result.info, dict) and 'results' in result.info:
                        formatted_results = result.info['results']
                        current = result.info.get('current', 0)
                        total = result.info.get('total', 0)
                        
                        response = {
                            'state': result.state,
                            'result': formatted_results,
                            'status': f'Processing... ({current}/{total} URLs)',
                            'info': 'Task is running and processing URLs.',
                            'progress': {
                                'current': current,
                                'total': total
                            }
                        }
                    else:
                        response = {
                            'state': result.state,
                            'status': 'Started',
                            'info': 'Task has been started.'
                        }
                except Exception as e:
                    logger.error(f"Error formatting results: {str(e)}")
                    logger.error(f"Result info type: {type(result.info)}")
                    logger.error(f"Result info content: {result.info}")
                    raise
            else:
                response = {
                    'state': result.state,
                    'status': 'Started',
                    'info': 'Task has been started.'
                }
        elif result.state == 'RETRY':
            response = {
                'state': result.state,
                'status': 'Retrying',
                'info': 'Task is being retried.'
            }
        elif result.state == 'SUCCESS':
            # Format results for the frontend
            if result.result:  # Check if result exists
                logger.info(f"Processing SUCCESS state with result: {result.result}")
                try:
                    # Extract progress information from the result
                    current = result.result.get('current', 0)
                    total = result.result.get('total', 0)
                    
                    response = {
                        'state': result.state,
                        'result': result.result.get('results', []),
                        'status': 'Task finished successfully',
                        'info': 'Task completed successfully.',
                        'progress': {
                            'current': current,
                            'total': total
                        }
                    }
                except Exception as e:
                    logger.error(f"Error formatting results: {str(e)}")
                    logger.error(f"Result type: {type(result.result)}")
                    logger.error(f"Result content: {result.result}")
                    raise
            else:
                response = {
                    'state': result.state,
                    'status': 'Task finished successfully',
                    'info': 'Task completed successfully.'
                }
        elif result.state == 'FAILURE':
            response = {
                'state': result.state,
                'status': 'Failed',
                'info': str(result.info),
                'error': str(result.info)
            }
        else:
            response = {
                'state': result.state,
                'status': 'Unknown',
                'info': f'Task is in unknown state: {result.state}'
            }

        return JsonResponse(response)
    except Exception as e:
        logger.error(f"Error checking task status: {str(e)}")
        return JsonResponse({
            'state': 'ERROR',
            'status': 'Error checking task status',
            'error': str(e)
        }, status=500)
