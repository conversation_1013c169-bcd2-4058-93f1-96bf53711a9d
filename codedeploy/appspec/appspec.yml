version: 0.0
os: linux
files:
  - source: alerts
    destination: /var/www/opat/alerts
  - source: applications
    destination: /var/www/opat/applications
  - source: logana
    destination: /var/www/opat/logana
  - source: media
    destination: /var/www/opat/media
  - source: opat
    destination: /var/www/opat/opat
  - source: templates
    destination: /var/www/opat/templates
  - source: psi
    destination: /var/www/opat/psi
  - source: manage.py
    destination: /var/www/opat
  - source: media  
    destination: /var/www/opat
  - source: requirements.txt
    destination: /var/www/opat
  - source: django_plotly_dash 
    destination: /var/www/opat/django_plotly_dash
  - source: ts
    destination: /var/www/opat/ts
  - source: gis
    destination: /var/www/opat/gis         
  - source: static_in_env
    destination: /var/www/opat/static_in_env
  - source: sitemap_checker
    destination: /var/www/opat/sitemap_checker  
  - source: drbot
    destination: /var/www/opat/drbot  
  - source: occ
    destination: /var/www/opat/occ  
  - source: autorevap
    destination: /var/www/opat/autorevap  
  - source: google_trends
    destination: /var/www/opat/google_trends
  - source: gsc
    destination: /var/www/opat/gsc
  - source: llmdash
    destination : /var/www/opat/llmdash


permissions:
  - object: /var/www/opat
    owner: opat
    group: opat
    mode : 664
    type:
      - file
  - object: /var/www/opat
    owner: opat
    group: opat
    mode : 775
    type:
      - directory

hooks:
  AfterInstall:
    - location: scripts/deploy.sh
      timeout: 300
      runas: root
