#!/bin/bash

cd /var/www/opat

# Get the environment
env=`echo -n $DEPLOYMENT_GROUP_NAME | awk '{print tolower($0)}'`

# Run for environment
case "$env" in
    "staging")
    ;;
    "release")
    ;;
    "production")
    ;;
esac


su --command 'cd /var/www/opat && pip install -r requirements.txt' --login opat
su --command 'cd /var/www/opat && python manage.py makemigrations llmdash' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=main_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations gsc' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=apps_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations alerts' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=alerts_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations applications' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=apps_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations logana' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=logana_db' --login opat
su --command 'cd /var/www/opat && python manage.py makemigrations psi' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=psi_db' --login opat
su --command 'cd /var/www/opat && python manage.py makemigrations ts' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=ts_db' --login opat  
su --command 'cd /var/www/opat && python manage.py makemigrations gis' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=gis_db' --login opat  
su --command 'cd /var/www/opat && python manage.py makemigrations sitemap_checker' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=main_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations drbot' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=main_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations occ' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=main_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations autorevap' --login opat
su --command 'cd /var/www/opat && python manage.py makemigrations --no-input' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=main_db' --login opat 
su --command 'cd /var/www/opat && python manage.py makemigrations google_trends' --login opat
su --command 'cd /var/www/opat && python manage.py migrate --database=google_trends_db' --login opat 

su --command 'cd /var/www/opat && python manage.py collectstatic --clear --no-input' --login opat




supervisorctl restart all
