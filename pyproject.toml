[tool.poetry]
name = "organic-performance-automated-tool"
version = "1.3.0"
description = "Organic performance automated tool app"
authors = ["BC"]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
Django = "5.2"
asgiref = "^3.8.1"
gitdb = "4.0.7"
GitPython = "3.1.18"
mysqlclient = "2.0.3"
python-decouple = "^3.8"
python-dotenv = "^1.0.1"
pytz = "2021.1"
smmap = "4.0.0"
sqlparse = "0.4.2"
swifter = "1.1.2"
lars = "^1.0"
Pillow = "^9.3.0"
dash = {version = "2.17.0", extras = ["diskcache"]}
django-plotly-dash = "^2.4.6"
mysql-connector-python = "8.0.31"
diskcache = "^5.6.3"
whitenoise = "^6.2.0"
dpd-static-support = "0.0.5"
django-bootstrap4 = "22.2"
pytest = "^7.2.0"
django-redis = "^5.2.0"
requests = "^2.31.0"
schedule = "^1.2.1"
pycountry = "^23.12.11"
textblob = "^0.17.1"
networkx = "^3.2.1"
aiohttp = "^3.9.1"
aiolimiter = "^1.1.0"
deep-translator = "^1.11.4"
slack-sdk = "^3.33.1"
celery = "^5.4.0"
scipy = "^1.14.1"
botasaurus = "4.0.85"
tqdm = "4.66.4"
uwsgi = "2.0.20"
pandas = "^2.2.3"
numpy = "^2.2.4"
openai = "^1.68.0"
gevent = "24.11.1"
daphne = "^4.1.2"
channels = "^4.2.2"
selenium = "4.32.0"
webdriver-manager = "4.0.2"
unidecode = "1.4.0"
markdown2 = "^2.5.3"
weasyprint = "^65.1"
hrequests = {extras = ["all"], version = "^0.9.2"}
camoufox = "^0.3.10"
google-genai = "^1.20.0"
kaleido = "^1.0.0"
gsc-api-handler = "^1.0.2"


[tool.poetry.dev-dependencies]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
