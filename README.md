# organic-performance-automated-tool
Django Application   
# Technologies used:
Python v. 3.9
Django v. 3.2.13

# Setup:
Create your virtualenv with desired python version (or use pyenv)
```bash
python -m virtualenv venv
```

Install poetry
```bash
pip install poetry
```

Install dependencies:
```bash
poetry install
```

We use poetry as package and dependency manager. For more information about poetry basic usage see [here](https://python-poetry.org/docs/basic-usage/).

Some dependencies are not needed for running the application. You should add them as development dependencies. Use `poetry add --dev <package-name>` to add `<package-name>` as a development dependency.
> **INFO:** We install production dependencies using `requirements.txt` on the AWS server. `requirements.txt` file is generated through `poetry export` command during a project build.

