#!/bin/bash
#==============================================================================
#   USAGE:  static-tests.sh
#
#   DESCRIPTION:  Runs the static code tests
#
#   VERSION: 1.0
#   COMPANY: Better Collective A/S
#==============================================================================

set -ex

#------------------------------------------------------------------------------
# Setup variables
#------------------------------------------------------------------------------
SCRIPTS_DIR_PATH="$(dirname $(realpath $0))"
source "$SCRIPTS_DIR_PATH/environment.sh"

#------------------------------------------------------------------------------
# Run tests
#------------------------------------------------------------------------------
pyenv local ${PYTHON_VERSION}

