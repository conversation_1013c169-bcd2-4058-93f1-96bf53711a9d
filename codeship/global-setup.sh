#!/bin/bash
#==============================================================================
#   USAGE:  global-setup.sh
#
#   DESCRIPTION:  Sets up the codeship environment
#
#   VERSION: 1.0
#   COMPANY: Better Collective A/S
#==============================================================================

set -ex

#------------------------------------------------------------------------------
# Setup variables
#------------------------------------------------------------------------------

SCRIPTS_DIR_PATH="$(dirname $(realpath $0))"
source "$SCRIPTS_DIR_PATH/environment.sh"

#------------------------------------------------------------------------------
# Setup python version
#------------------------------------------------------------------------------
pyenv local ${PYTHON_VERSION}
#------------------------------------------------------------------------------
# Install dependencies
#------------------------------------------------------------------------------
pip install poetry==2.1.3
pip install poetry-plugin-export
poetry export --without-hashes -f requirements.txt -o requirements.txt
pip install -r requirements.txt

# Build static files so we wont build it on instance
