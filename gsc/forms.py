from django import forms
from .models import GscPagedData, GscProject, GscSourceType

class GscPagedDataForm(forms.ModelForm):
    class Meta:
        model = GscPagedData
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Ovde postavljamo queryset-e da koriste apps_db
        self.fields["project"].queryset = GscProject.objects.using("apps_db").all()
        self.fields["source_type"].queryset = GscSourceType.objects.using("apps_db").all()