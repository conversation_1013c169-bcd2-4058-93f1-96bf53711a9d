from django.contrib import admin
from .models import GscProject, GscPagedData, GSCLog, GscApplication, GscSourceType, GscProperties, GscFetchCycle
from .utils.admin_helpers import safe_fk_value, safe_fk_chain
from .forms import GscPagedDataForm


class ReadOnlyAdminMixin:
    def has_add_permission(self, request):
        return True

    def has_change_permission(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True

    def has_view_permission(self, request, obj=None):
        return True


class AdminFromAppsDbMixin:
    def get_queryset(self, request):
        return super().get_queryset(request).using('apps_db')

    def get_object(self, request, object_id, from_field=None):
        return self.model.objects.using('apps_db').get(pk=object_id)

    def save_model(self, request, obj, form, change):
        obj.save(using='apps_db')

    def delete_model(self, request, obj):
        obj.delete(using='apps_db')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.remote_field and db_field.remote_field.model._meta.app_label == 'gsc':
            kwargs["queryset"] = db_field.remote_field.model.objects.using("apps_db").all()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(GscProperties)
class GscPropertiesAdmin(ReadOnlyAdminMixin, AdminFromAppsDbMixin, admin.ModelAdmin):
    list_display = ("property_name", "is_active",)
    list_editable = ("is_active",)
    search_fields = ("property_name",)    


@admin.register(GscApplication)
class GscApplicationAdmin(ReadOnlyAdminMixin, AdminFromAppsDbMixin, admin.ModelAdmin):
    list_display = ("app_name", "app_description")


@admin.register(GscSourceType)
class GscSourceTypeAdmin(ReadOnlyAdminMixin, AdminFromAppsDbMixin, admin.ModelAdmin):
    list_display = ("code", "description")
    list_editable = ("description",)
    search_fields = ("code", "description")


@admin.register(GscProject)
class GscProjectAdmin(ReadOnlyAdminMixin, AdminFromAppsDbMixin, admin.ModelAdmin):
    list_display = (
        "name_display", "active", "is_initialized",
         "property_id", "last_fetched", "created_at"
    )
    list_editable = ("active", )
    readonly_fields = (
        "name_display", "property_id", "last_fetched",
        "created_at", "updated_at", "is_initialized"
    )
    search_fields = ("property_id",)

    def name_display(self, obj):
        return safe_fk_value(obj, fk_field="name", target_attr="property_name")

    name_display.short_description = "Property Name"



@admin.register(GSCLog)
class GSCLogAdmin(ReadOnlyAdminMixin, AdminFromAppsDbMixin, admin.ModelAdmin):
    list_display = ("property_display", "status", "operation", "run_date", "records_fetched")
    list_editable = ("status", "operation")
    readonly_fields = ("property_display", "run_date")

    fields = ("property_display", "project", "status", "operation", "message", "records_fetched", "run_date")

    def property_display(self, obj):
        return safe_fk_chain(obj, ["project", "name", "property_name"])

    property_display.short_description = "Property Name"


@admin.register(GscPagedData)
#TODO - This here has a bug
class GscPagedDataAdmin(ReadOnlyAdminMixin, AdminFromAppsDbMixin, admin.ModelAdmin):
    form = GscPagedDataForm
    list_display = (
        "project_display", "date", "page", "device", "country", "clicks", "ctr", "position"
    )
    readonly_fields = (
        "project_display", "date", "page", "device", "country", "clicks", "ctr", "position"
    )

    def project_display(self, obj):
        return safe_fk_chain(obj, ["project", "name", "property_name"])

    # project_display.short_description = "Project Name"

    # def formfield_for_foreignkey(self, db_field, request, **kwargs):
    #     if db_field.name == "project":
    #         kwargs["queryset"] = GscProject.objects.using("apps_db").all()
    #     elif db_field.name == "source_type":
    #         kwargs["queryset"] = GscSourceType.objects.using("apps_db").all()
    #     return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(GscFetchCycle)
class GscFetchCycleAdmin(AdminFromAppsDbMixin, admin.ModelAdmin):
    list_display = ("project_display", "status", "triggered_by", "started_at", "completed_at")
    readonly_fields = ("project_display", "status", "triggered_by", "started_at", "completed_at", "notes")
    search_fields = ("project__property_id",)
    list_filter = ("status", "triggered_by")

    def project_display(self, obj):
        return safe_fk_value(obj, fk_field="project", target_attr="property_id")

    project_display.short_description = "Project"

