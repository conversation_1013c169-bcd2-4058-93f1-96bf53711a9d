from django.core.management.base import BaseCommand
from gsc.models import GscPagedData, GscProject, GscSourceType, GSCLog
import sqlite3
from datetime import datetime
import os
from django.db import transaction


class Command(BaseCommand):
    help = "Importuje podatke iz SQLite baze u GscPagedData model"

    def add_arguments(self, parser):
        parser.add_argument("--sqlite-path", type=str, required=True, help="Putanja do SQLite fajla")
        parser.add_argument("--project-id", type=int, required=True, help="ID iz GscProject tabele")

    def handle(self, *args, **options):
        sqlite_path = options["sqlite_path"]
        project_id = options["project_id"]

        if not os.path.exists(sqlite_path):
            self.stdout.write(self.style.ERROR(f"❌ SQLite fajl nije pronađen: {sqlite_path}"))
            return

        try:
            project = GscProject.objects.using("apps_db").get(id=project_id)
        except GscProject.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"❌ Ne postoji projekat sa id: {project_id}"))
            return

        # Mapa device → GscSourceType instance
        device_map = {}
        for code in ["MOBILE", "DESKTOP", "TABLET"]:
            try:
                device_map[code] = GscSourceType.objects.using("apps_db").get(code=code)
            except GscSourceType.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"⚠️ GscSourceType nije pronađen za: {code}"))
                device_map[code] = None

        conn = sqlite3.connect(sqlite_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT DISTINCT country, device, query, page, date, clicks, impressions, ctr, position
                FROM gsc_data
            """)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ SQL greška: {e}"))
            return

        rows = cursor.fetchall()
        conn.close()

        gsc_objects = []
        skipped = 0

        for row in rows:
            country, device, query, page, date_str, clicks, impressions, ctr, position = row
            device_clean = device.upper()

            if device_clean not in device_map or not device_map[device_clean]:
                skipped += 1
                continue

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                skipped += 1
                continue

            gsc_objects.append(GscPagedData(
                project=project,
                source_type=device_map[device_clean],
                date=date,
                page=page,
                clicks=clicks,
                ctr=ctr,
                position=position,
                device=device,
                country=country,
                impressions=impressions,
                query=query
            ))

        total_inserted = len(gsc_objects)
        self.stdout.write(self.style.WARNING(f"🔢 Redova za unos: {total_inserted}, preskočeno: {skipped}"))

        if gsc_objects:
            BATCH_SIZE = 1000
            try:
                with transaction.atomic(using="apps_db"):
                    for i in range(0, total_inserted, BATCH_SIZE):
                        GscPagedData.objects.using("apps_db").bulk_create(
                            gsc_objects[i:i+BATCH_SIZE],
                            ignore_conflicts=True
                        )
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Greska pri bulk_create: {e}"))
                return

            self.stdout.write(self.style.SUCCESS(f"✅ Ubačeno {total_inserted} redova (preskočeno {skipped})"))

            GSCLog.objects.using("apps_db").create(
                project=project,
                status="SUCCESS",
                operation="SQLITE_IMPORT",
                message=f"Importovano {total_inserted} redova iz {sqlite_path}, preskočeno {skipped}",
                records_fetched=total_inserted
            )
        else:
            self.stdout.write(self.style.WARNING("⚠️ Nema validnih redova za upis."))

            GSCLog.objects.using("apps_db").create(
                project=project,
                status="FAIL",
                operation="SQLITE_IMPORT",
                message="Nema validnih redova za upis iz SQLite fajla.",
                records_fetched=0
            )
