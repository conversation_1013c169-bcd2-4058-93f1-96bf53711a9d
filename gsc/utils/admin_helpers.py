def safe_fk_value(obj, fk_field, target_attr="__str__", using="apps_db"):

    try:
        fk_model = obj._meta.get_field(fk_field).remote_field.model
        fk_id = getattr(obj, f"{fk_field}_id")

        fk_obj = fk_model.objects.using(using).get(pk=fk_id)

        if target_attr == "__str__":
            return str(fk_obj)
        return getattr(fk_obj, target_attr)

    except Exception:
        return f"[Missing {fk_field} #{fk_id}]"


def safe_fk_chain(obj, chain, using="apps_db"):

    try:
        current = obj
        for i, attr in enumerate(chain):
            if i < len(chain) - 1:
                field = current._meta.get_field(attr)
                fk_model = field.remote_field.model
                fk_id = getattr(current, f"{attr}_id")
                current = fk_model.objects.using(using).get(pk=fk_id)
            else:
                current = getattr(current, attr)
        return current
    except Exception as e:
        return f"[Missing {chain[-2]}]"