from django.db import models
from django.urls import reverse




class GscApplication(models.Model):
    app_name = models.CharField(max_length = 255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)


    def __str__(self):
        return self.app_name
    
    def get_absolute_url(self):
        return reverse('gsc_explore', kwargs={
            'id':self.id,
        })        

class GscProperties(models.Model):
    property_name = models.CharField(max_length=255, default=None)
    is_active = models.BooleanField(default=False)

    def __str__(self):
        return self.property_name or "Unnamed Property"

class GscSourceType(models.Model):
    code = models.CharField(max_length=20, default=None )
    description = models.CharField(max_length=255, default=None) 

    def __str__(self):
        return self.code 

class GscProject(models.Model):
    name = models.ForeignKey(GscProperties, on_delete=models.CASCADE)
    property_id = models.CharField(max_length=255, default=None)
    active = models.BooleanField(default=False)
    is_initialized = models.BooleanField(default=False)  
    last_fetched = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"GscProject #{self.property_id}"



class GscPagedData(models.Model):
    project = models.ForeignKey(GscProject, on_delete=models.CASCADE)
    source_type = models.ForeignKey(GscSourceType, on_delete=models.CASCADE, default="")
    date = models.DateField()
    page = models.URLField()
    clicks = models.IntegerField()
    ctr = models.FloatField()
    position = models.FloatField()
    device = models.CharField(max_length=255)
    country = models.CharField(max_length=255)
    impressions = models.IntegerField(default=0)
    query = models.TextField(blank=True)

    def __str__(self):
        return self.page



class GSCLog(models.Model):
    STATUS_CHOICES = [
        ("SUCCESS", "success"),
        ("FAIL", "fail")
    ]
    OPERATION_CHOICES = [
        ("FETCH", "Initial fetch from GSC"),
        ("SQLITE_IMPORT", "Import from SQLite"),
        ("DEDUP", "Deduplication"),
        ("VALIDATE", "Validation"),
        ("API_SYNC", "Periodic API sync"),
        ("AUTO_PROJECT", "Auto project creation"),
    ]

    project = models.ForeignKey(GscProject, on_delete=models.CASCADE)
    run_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    operation = models.CharField(max_length=50, choices=OPERATION_CHOICES, default=None)
    message = models.TextField(blank=True)
    records_fetched = models.IntegerField(default=0)

    def __str__(self):
        return f"GSCLog #{self.id}"
    


class GscFetchCycle(models.Model):
    project = models.ForeignKey(GscProject, on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=[("STARTED", "Started"), ("COMPLETED", "Completed"), ("FAILED", "Failed")])
    triggered_by = models.CharField(max_length=50, choices=[("SCHEDULER", "Scheduler"), ("MANUAL", "Manual")])
    notes = models.TextField(blank=True)
    
    cycle_batch = models.PositiveIntegerField(default=0) 

    def __str__(self):
        return f"{self.project} | Batch #{self.cycle_batch} | {self.status}"



