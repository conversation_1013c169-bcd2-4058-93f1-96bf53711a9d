#!/usr/bin/env python
from django.db import models
from django.urls import reverse
from django.core.files.storage import FileSystemStorage
from django.utils import timezone


# Create your models here.

ss = FileSystemStorage('media')


class Domain(models.Model):
    url = models.CharField(max_length=150, unique=True)
    domain = models.CharField(max_length=150, unique=True)
    logo = models.ImageField(storage=ss, blank=True, null=True)
    in_use = models.BooleanField(default=False)
    
    def __str__(self):
        return self.domain
        
        
    def get_absolute_url(self):
        return reverse('psiadmin', kwargs={
            'id':self.domain
        })  
    
class PsiApplication(models.Model):
    app_name = models.CharField(max_length = 255)
    app_description = models.TextField(blank=True)
    app_standalone = models.BooleanField(default=False)
    app_is_active = models.BooleanField(default=False)
    in_test = models.BooleanField(default=True)
    receive_alerts = models.BooleanField(default=False)


    def __str__(self):
        return self.app_name 


    def get_absolute_url(self):
        return reverse('appname', kwargs={
            'id':self.app_name
        })  

class PsiProject(models.Model):

    project_name = models.CharField(max_length=150, unique=True)
    project_description = models.TextField(blank=True)
    creation_date = models.DateTimeField(auto_now_add=True,editable=False)
    domain= models.ForeignKey(Domain, on_delete = models.CASCADE, default="")
    is_active = models.BooleanField(default=False)
    is_shared = models.BooleanField(default=True)
    is_processed = models.BooleanField(default=False)
    app_id = models.ForeignKey(PsiApplication, on_delete = models.CASCADE, default="")
    
    
    def __str__(self):
        return self.project_name

    def get_absolute_url(self):
        return reverse('prdetails', kwargs={
            'id':self.id,
        })    
    

class PsiPageType(models.Model):
    type = models.CharField(max_length=150, unique=True)


    def __str__(self) -> str:
        return self.type

    def get_absolute_url(self):
        return reverse('ptype', kwargs={
            'id':self.id,
        })   


class PsiUrl (models.Model):
    url = models.URLField(max_length=200, unique=True)
    type = models.ForeignKey(PsiPageType, on_delete=models.CASCADE, default="")
    domain = models.ForeignKey(Domain, on_delete=models.CASCADE, default="")
    description = models.CharField(max_length=250, unique=False, default="")
    is_active = models.BooleanField(default=True)

    def __str__(self) -> str:
        return self.url

    def get_absolute_url(self):
        return reverse("psiurl", kwargs={
            'id':self.url
        })   


class PsiStrategy(models.Model):

    strategy_type = models.CharField(max_length=150, unique=True) 
    strategy_inuse = models.BooleanField(default=False)
    def __str__(self) -> str:
        return self.strategy_type

    def get_absolute_url(self):
        return reverse("psiexplore", kwargs={
            'id':self.id,
        })  

class PsiOption(models.Model):

    option_type = models.CharField(max_length=150, unique=True)

    def __str__(self) -> str:
        return self.option_type 


class PsiConnectionType(models.Model):

    connection = models.CharField(max_length=150, unique=True)  

    def __str__(self) -> str:
        return self.connection  


class PsiEndPoint(models.Model):

    end_point = models.CharField(max_length=255, unique=True) 
    ep_description = models.CharField(max_length=255, default=None) 
    ep_api_name = models.CharField(max_length=255, default=None)
    ep_is_deprecated = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.end_point 
    

class PsiApiKey(models.Model):

    api_key  = models.CharField(max_length=255, unique=True) 
    is_valid = models.BooleanField(default=False) 

    def __str__(self) -> str:
        return self.api_key   


class PsiCruxMetric(models.Model):

    metric = models.CharField(max_length=255, unique=True)
    data_type = models.CharField(max_length=255,  default=None)
    crux_metric_name = models.CharField(max_length=255, default=None)
    unit = models.CharField(max_length=255, default=None)
    in_use = models.BooleanField(default=False)  
    good_values = models.CharField(max_length=255, unique=False, default="")
    need_improvement = models.CharField(max_length=255, unique=False, default="")
    poor = models.CharField(max_length=255, unique=False, default="")
    unit = models.CharField(max_length=255, unique=False, default="") 
    abbreviation = models.CharField(max_length=255, unique=False, default="")  


    def __str__(self) -> str:
        return self.metric   

class PsiMetricWeight(models.Model):

    metric = models.CharField(max_length=255, unique=True, default=None)
    weight = models.IntegerField(default=None)           

   
    def __str__(self) -> str:
        return self.metric            
    



class PsiMainNodes(models.Model):

    main_node = models.CharField(max_length=255, unique=True, default=None)
    description = models.CharField(max_length=255, unique=False, default=None)
    have_children = models.BooleanField(default=False) 
    have_children = models.BooleanField(default=False)
    in_use = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.main_node                 
    

class PsiMetrics(models.Model):
    metric = models.CharField(max_length=255, unique=False, default=None)
    parent = models.ForeignKey(PsiMainNodes, on_delete=models.CASCADE, default="")
    data_type = models.CharField(max_length=255, default=None)
    unit = models.CharField(max_length=255, default=None)
    in_use = models.BooleanField(default=False)
    haveChild = models.BooleanField(default=False)
    knowledge = models.CharField(max_length=255, default=None)

    def __str__(self) -> str:
        return self.metric    

class PsiCategories(models.Model):
    category = models.CharField(max_length=255, unique=True, default=None)
    description = models.CharField(max_length=255, unique=False, default=None)

    def __str__(self) -> str:
        return self.category   


class PsiUserAgents(models.Model):
    user_agent = models.CharField(max_length=255, unique=True, default="")
    in_use= models.BooleanField(default=False) 

    def __str__(self) -> str:
        return self.user_agent     


class CruxFetchResultsForUrl(models.Model):
    form_factor = models.CharField(max_length=255, default="")
    effective_conn_type = models.CharField(max_length=255, default="")
    url = models.CharField(max_length=255, default="")
    first_contentful_paint = models.CharField(max_length=255, default="")
    first_input_delay = models.CharField(max_length=255, default="")
    interaction_to_next_paint = models.CharField(max_length=255, default="")
    largest_contentful_paint = models.CharField(max_length=255, default="")
    cumulative_layout_shift = models.CharField(max_length=255, default="")
    experimental_time_to_first_byte = models.CharField(max_length=255, default="")
    collectionPeriod = models.CharField(max_length=255, default="")
    domain_id = models.IntegerField(blank=True)
    url_id = models.IntegerField(blank=True)
    extractionDate = models.DateTimeField(default=timezone.now)



    def __str__(self) -> str:
        return self.form_factor


class CruxFetchResultsForOrigin(models.Model):
    form_factor = models.CharField(max_length=255, default="")
    effective_conn_type = models.CharField(max_length=255, default="")
    origin = models.CharField(max_length=255, default="")
    first_contentful_paint = models.CharField(max_length=255, default="")
    first_input_delay = models.CharField(max_length=255, default="")
    interaction_to_next_paint = models.CharField(max_length=255, default="")
    largest_contentful_paint = models.CharField(max_length=255, default="")
    cumulative_layout_shift = models.CharField(max_length=255, default="")
    experimental_time_to_first_byte = models.CharField(max_length=255, default="")
    collectionPeriod = models.CharField(max_length=255, default="")
    domain_id = models.IntegerField(blank=True)
    #url_id = models.IntegerField(blank=True) 
    extractionDate = models.DateTimeField(default=timezone.now)
 


    def __str__(self) -> str:
        return self.form_factor
    


class PsiMainCoreWebVitalsMetrics(models.Model):
    """Main Core Web Vital Metrics + some most important and performance score"""
    url_id = models.IntegerField(default="")
    speed_index = models.FloatField(max_length=255, default="")
    first_contentful_paint = models.FloatField(max_length=255, default="")
    largest_contentful_paint = models.FloatField(max_length=255, default="")
    total_blocking_time = models.FloatField()
    cumulative_layout_shift = models.FloatField(max_length=255, default="")
    render_blocking_resources = models.FloatField(max_length=255, default="")
    server_response_time = models.FloatField(max_length=255, default="")
    server_network_latency = models.FloatField(max_length=255, default="")
    performance_score = models.IntegerField()
    extractionDate = models.DateTimeField(default=timezone.now)
    strategy = models.CharField(max_length=255, default="")
    final_snapshoot = models.TextField(default="")
    fetch_succesful = models.BooleanField(default=False)


    def __str__(self) -> str:
        return str(self.url_id)
    
class Snapshoots(models.Model):
    """Model to store snapshoots separately to release PaiMAinCOreWebVitals table"""
    url_id = models.IntegerField(default=None) 
    final_snapshoot = models.TextField(default=None)
    strategy = models.CharField(max_length = 255, default=None) 
    extractionDate = models.DateTimeField(default=timezone.now)

    def __str__(self) -> str:
        return str(self.id)  
    



class PsiMainCoreWebVitalsAverages(models.Model):
    """Model to store calculated averages for each metric PsiMainCoreWebVitalsMetrics has"""
    url_id = models.IntegerField(default="")
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE, default="")
    strategy_id = models.ForeignKey(PsiStrategy, on_delete=models.CASCADE, default="")
    speed_index_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    first_contenful_paint_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    largest_contentful_paint_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    total_blocking_time_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    cumulative_layout_shift_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    render_blocking_resources_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    server_response_time_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    server_network_latency_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    performance_score_average = models.DecimalField(max_digits=10, decimal_places=4, default=0)  # Set default to 0
    averagingDate = models.DateTimeField(default=timezone.now)

    def __str__(self) -> str:
        return str(self.url_id)
    

class PsiMainCoreWebVitalsStandardDeviation(models.Model):
    """Model to store standard deviation callculation for each metric PsiMainCoreWebVitalsMetrics model have """  
    url_id = models.IntegerField(default="")  
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE, default="")
    strategy_id = models.ForeignKey(PsiStrategy, on_delete=models.CASCADE, default="")
    speed_index_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    first_contenful_paint_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    largest_contentful_paint_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    total_blocking_time_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    cumulative_layout_shift_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    render_blocking_resources_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    server_response_time_std = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    server_network_latency_std = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    performance_score_std = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    stdingDate = models.DateTimeField(default=timezone.now)

    def __str__(self) -> str:
        return str(self.url_id)
    

class PsiMainCoreWebVitalsDifference(models.Model):
    '''Model to store the difference between two measurements for each numeric metric PsiMainCoreWebVitalsMetrics model have'''
    url_id = models.IntegerField(default="")
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE, default="")
    strategy_id = models.ForeignKey(PsiStrategy, on_delete=models.CASCADE, default="")
    speed_index_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    first_contenful_paint_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    largest_contentful_paint_diff= models.DecimalField(max_digits=10, decimal_places=4, default=0)
    total_blocking_time_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    cumulative_layout_shift_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    render_blocking_resources_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    server_response_time_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    server_network_latency_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    performance_score_diff = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    diffDate = models.DateTimeField(default=timezone.now)

    def __str__(self) -> str:
        return str(self.url_id)
    
class PsiMainCoreWebVitalsMediana(models.Model):
    """Model to store the mediana value """   
    url_id = models.IntegerField(default="")
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE, default="")
    strategy_id = models.ForeignKey(PsiStrategy, on_delete=models.CASCADE, default="")
    speed_index_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    first_contenful_paint_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    largest_contentful_paint_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    total_blocking_time_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    cumulative_layout_shift_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    render_blocking_resources_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    server_response_time_med = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    server_network_latency_med = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    performance_score_med = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    medDate = models.DateTimeField(default=timezone.now)

    def __str__(self) -> str:
        return str(self.url_id)    
    
class PsiMainCoreWebVitalsVarCoeff(models.Model):
    """Model to store the variance coefficient CV value """  
    url_id = models.IntegerField(default=None) 
    domain_id = models.ForeignKey(Domain, on_delete=models.CASCADE, default="")
    strategy_id = models.ForeignKey(PsiStrategy, on_delete=models.CASCADE, default="")
    speed_index_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    first_contentful_paint_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    largest_contentful_paint_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    total_blocking_time_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    cumulative_layout_shift_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    render_blocking_resources_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    server_response_time_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    server_network_latency_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    performance_score_coef = models.DecimalField(max_digits=10, decimal_places=4, default=0) 
    cfvDate = models.DateTimeField(default=timezone.now)

    def __str__(self) -> str:
        return str(self.url_id)       
    
    


class CruxFormFactor(models.Model):
    formFactor = models.CharField(max_length=255, unique=True, default="")
    factorName = models.CharField(max_length=255, unique=False, default=None)
    in_use= models.BooleanField(default=False) 

    def __str__(self) -> str:
        return self.formFactor   

class CruxIndentifier(models.Model):
    identifier = models.CharField(max_length=255, unique=True, default=None)
    ide_desc = models.CharField(max_length=255, unique=False, default=None)


    def __str__(self) -> str:
        return self.identifier    


class CruxHeaders(models.Model):
    header = models.CharField(max_length=255, unique=False, default="")
    header_desc = models.CharField(max_length=255, unique=False, default=None)


    def __str__(self) -> str:
        return self.header
    

class CruxNodeLevels(models.Model):
    node = models.CharField(max_length=255, unique=True, default="")    
    node_order = models.IntegerField()
    predecesor = models.CharField(max_length=255, unique=False, default=None)
    description = models.CharField(max_length=255, default="")

    def __str__(self) -> str:
        return self.node
    

class PsiFormFactor(models.Model):
    '''Identifies form factor options for Psi as they are differenct from Crux APi'''
    psi_form_factor = models.CharField(max_length = 255, unique=True, default="")
    psi_form_factor_name = models.CharField(max_length = 255, unique=False, default=None)
    in_use = models.BooleanField(default=False)
    
    def __str__(self) -> str:
        return self.psi_form_factor
    

#+++++++++++++++++++  History Crux Part ++++++++++++++++++++++++



class CruxJsonRecord(models.Model):
    '''Stores parsed crux records inside the database'''
    form_factor = models.CharField(max_length=50)  
    url = models.URLField(max_length=500) 
    identifier = models.ForeignKey(CruxIndentifier, on_delete=models.CASCADE, default=None)


    def __str__(self) -> str:
        return f"{self.form_factor} - {self.url}"

class CruxJsonMetrics(models.Model):
    '''Stores parsed json data into database table'''
    record_id = models.ForeignKey(CruxJsonRecord, on_delete=models.CASCADE)
    cumulative_layout_shift_histogram_densities = models.JSONField(null=True, blank=True)
    cumulative_layout_shift_percentiles_p75s = models.JSONField(null=True, blank=True)
    experimental_time_to_first_byte_histogram_densities = models.JSONField(null=True, blank=True)
    experimental_time_to_first_byte_percentiles_p75s = models.JSONField(null=True, blank=True)
    first_contentful_paint_histogram_densities = models.JSONField(null=True, blank=True)
    first_contentful_paint_percentiles_p75s = models.JSONField(null=True, blank=True)
    first_input_delay_histogram_densities = models.JSONField(null=True, blank=True)
    first_input_delay_percentiles_p75s = models.JSONField(null=True, blank=True)
    interaction_to_next_paint_histogram_densities = models.JSONField(null=True, blank=True)
    interaction_to_next_paint_percentiles_p75s = models.JSONField(null=True, blank=True)
    largest_contentful_paint_histogram_densities = models.JSONField(null=True, blank=True)
    largest_contentful_paint_percentiles_p75s = models.JSONField(null=True, blank=True)

    def __str__(self) -> str:
        return f"Metrics for record {self.record_id.id}"

class CruxCollectionPeriod(models.Model):
    '''Stores Collection period data in the database'''
    record_id = models.ForeignKey(CruxJsonRecord, on_delete=models.CASCADE)
    first_date_json = models.JSONField(null=True, blank=True)
    last_date_json = models.JSONField(null=True, blank=True)

    def __str__(self) -> str:
        return f"Collection period for record {self.record_id.id} from {self.first_date_json} to {self.last_date_json}"
    


class CruxJsonOriginRecord(models.Model):
    '''Stores parsed crux records inside the database'''
    form_factor = models.CharField(max_length=50)  
    url = models.URLField(max_length=500) 
    identifier = models.ForeignKey(CruxIndentifier, on_delete=models.CASCADE, default=None)


    def __str__(self) -> str:
        return f"{self.form_factor} - {self.url}"

class CruxJsonOriginMetrics(models.Model):
    '''Stores parsed json data into database table'''
    record_id = models.ForeignKey(CruxJsonOriginRecord, on_delete=models.CASCADE)
    cumulative_layout_shift_histogram_densities = models.JSONField(null=True, blank=True)
    cumulative_layout_shift_percentiles_p75s = models.JSONField(null=True, blank=True)
    experimental_time_to_first_byte_histogram_densities = models.JSONField(null=True, blank=True)
    experimental_time_to_first_byte_percentiles_p75s = models.JSONField(null=True, blank=True)
    first_contentful_paint_histogram_densities = models.JSONField(null=True, blank=True)
    first_contentful_paint_percentiles_p75s = models.JSONField(null=True, blank=True)
    first_input_delay_histogram_densities = models.JSONField(null=True, blank=True)
    first_input_delay_percentiles_p75s = models.JSONField(null=True, blank=True)
    interaction_to_next_paint_histogram_densities = models.JSONField(null=True, blank=True)
    interaction_to_next_paint_percentiles_p75s = models.JSONField(null=True, blank=True)
    largest_contentful_paint_histogram_densities = models.JSONField(null=True, blank=True)
    largest_contentful_paint_percentiles_p75s = models.JSONField(null=True, blank=True)

    def __str__(self) -> str:
        return f"Metrics for record {self.record_id.id}"

class CruxCollectionOriginPeriod(models.Model):
    '''Stores Collection period data in the database'''
    record_id = models.ForeignKey(CruxJsonOriginRecord, on_delete=models.CASCADE)
    first_date_json = models.JSONField(null=True, blank=True)
    last_date_json = models.JSONField(null=True, blank=True)

    def __str__(self) -> str:
        return f"Collection period for record {self.record_id.id} from {self.first_date_json} to {self.last_date_json}"    
    

class CruxDelta(models.Model):
    date = models.IntegerField()
    is_delta = models.BooleanField(default=True)

    def __str__(self) -> str:
        return f"Collection period for the last {self.date} weeks."
    
    






                                                 

    