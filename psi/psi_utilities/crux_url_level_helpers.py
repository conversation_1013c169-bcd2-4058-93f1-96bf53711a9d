from psi.psi_utilities.dashes.psi_dashes import hor_bar_plot



#---------This is the set of heleper functions for the crux_url_level view ---------#

def fetchMetricVarsFromResponse(response, outer_key, inner_key):
    '''This function takes in a response object and returns a dictionary of metrics variables
        as well as the corresponding data in a separate list
    '''
    try:
        metrics_vars = list(response.get(outer_key, {}).get(inner_key, {}).keys())
        histograms = response.get(outer_key, {}).get(inner_key, {})
    except KeyError as e:
        metrics_vars = {}
        histograms = {}  
    return metrics_vars, histograms    


def combineCheckAndFilter(keys_from_the_response, crm_from_db, the_hist_data, form_factor):
    ''''This function combines the variable keys from the response and
        the keys, ranges from the database and returns a list of metrics,
        taking into the the actual order in the database /Important for template rendering
        crm_from_dbis quary set explicitly converted to a list of tuples.
        This returns zipped data and zipped metrics.
    '''



    metrics_variables_list = []
    metrics_names_list = []
    ranges = []
    metrics_data = []
    zipped_metrics = []
    zipped_data = []



  
    for val in crm_from_db:

        for key in keys_from_the_response: 
  
            if val[0] == key:
                metrics_variables_list.append(val[0])
                metrics_names_list.append(val[1])
                ranges.append(val[2:])
                metrics_data.append(the_hist_data[val[0]])

     #Adding the ranges to the metrics data

    for i, element in enumerate(ranges):       
        metrics_data[i]['ranges'] = element

    zipped_metrics = list(zip(metrics_names_list, metrics_data))  

            
    zipped_data.append({'formFactor': form_factor, 'zipped_metrics': zipped_metrics})


    return zipped_data




def prepareDataAndOutputGraphs(zipped_data):
    '''This function takes the data and prepare it to be handled to the graphs for the consumsion in CRUX live part'''
    zipped_graphs = []
    for metric in zipped_data:
        for tpl in metric['zipped_metrics']:
                data =tpl[1]
                crux_live_hor_bar = hor_bar_plot(data_input=tpl[1], metric_name=tpl[0], ranges=tpl[1]['ranges'][0:], dinput='histogram', percentiles='percentiles', perkey="p75", orientation='v')  
                zipped_graphs.append({'metric': tpl[0], 'graph': crux_live_hor_bar}) 
    #print(zipped_data)          

    return zipped_graphs       


#---------This is the set of heleper functions for the crux_HISTORICAL_level view PART ---------#
#######TODO: At some point add here database connection to fetch the metrics names from the database
def mapHistoricalMetricsNames():
    '''This function has no input parameters and returns a dictionary of metrics names
        mapped to the corresponding metric variables
    '''

    metrics_map = {
        'largest_contentful_paint': 'Largest Contentful Paint',
        #'first_input_delay': 'First Input Delay',     
        'cumulative_layout_shift':'Cumulative Layout Shift',   
        'first_contentful_paint': 'First Contentful Paint',   
        'interaction_to_next_paint': 'Interaction to Next Paint',                     
        'experimental_time_to_first_byte':'Time to First Byte',

    }    

    
    return metrics_map

def mapHistoricalTribinMetricsNames():
    '''This function has no input parameters and returns a dictionary of metrics names
        mapped to the corresponding metric variables needed for the Tribin Graphs
    '''

    metrics_map = {
 
        'largest_contentful_paint_histogram_densities' : 'Largest Contentful Paint',
        #'first_input_delay_histogram_densities' : 'First Input Delay',
        'cumulative_layout_shift_histogram_densities': 'Cumulative Layout Shift',
        'first_contentful_paint_histogram_densities': 'First Contentful Paint',
        'interaction_to_next_paint_histogram_densities' : 'Interaction To The Next Paint',
        'experimental_time_to_first_byte_histogram_densities': 'Time To First Byte'

    }    

    
    return metrics_map

def mapHistoricalp75MetricsNames():
    '''This function has no input parameters and returns a dictionary of metrics names
        mapped to the corresponding metric variables needed for the p75 Graphs
    '''

    metrics_map = {
 
        'largest_contentful_paint_percentiles_p75s' : 'Largest Contentful Paint',
        #'first_input_delay_percentiles_p75s' : 'First Input Delay',
        'cumulative_layout_shift_percentiles_p75s': 'Cumulative Layout Shift',
        'first_contentful_paint_percentiles_p75s': 'First Contentful Paint',
        'interaction_to_next_paint_percentiles_p75s' : 'Interaction To The Next Paint',
        'experimental_time_to_first_byte_percentiles_p75s': 'Time To First Byte'

    }    

    
    return metrics_map







