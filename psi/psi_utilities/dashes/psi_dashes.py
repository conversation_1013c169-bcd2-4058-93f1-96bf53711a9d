#!usr/bin/env python3
from html.entities import html5
import dash
import plotly.graph_objects as go
from django_plotly_dash import DjangoDash
import plotly.express as px
import dash_bootstrap_components as dbc
import numpy as np
from dash import dcc
import pandas as pd




def hor_bar_plot(data_input, metric_name, ranges, dinput, percentiles, perkey, orientation):
    '''Displays the vertical bar chart similar to the one Google uses in Google page Speed insights'''
    densities = [entry.get('density', 0) for entry in data_input[dinput]]
    colors = ['green', 'orange', 'red']
    sum_of_densities = np.sum(densities)
    percentile_75 = sum_of_densities*0.75


    segments = [density for density in densities]
    fig = go.Figure()
    traces = []
    for segment, color, range in zip(segments, colors, ranges):

        traces.append(go.Bar(y=[0], x=[segment], orientation='h', name=str(range), marker=dict(color=color)))

    # Dodavanje svih tragova u figuru
    for trace in traces:
        fig.add_trace(trace)

    if percentile_75 <= densities[0]:
        color = "Green"      
    elif percentile_75 <= densities[0] + densities[1]:
        color = "Orange"
   
    elif percentile_75 <= densities[0] + densities[1] + densities[2]:
        color = "Red" 
   

    fig.add_shape(
        type="line",
        x0=percentile_75,
        y0=-5,
        x1=percentile_75,
        y1=5,
        line=dict(color=color, width=0.75)
    )      
    # Dodavanje anotacije za 75. procenat
    fig.add_annotation(
        x=percentile_75,
        y=1,
        text=f"{(data_input[percentiles][perkey])}",
        showarrow=True,
        font=dict(
            family="Courier New, monospace",
            size=18,
            color="#ffffff"
        ),
        align="center",
        arrowhead=2,
        arrowsize=0.5,
        arrowwidth=2,
        arrowcolor=color,
        ax=20,
        ay=-30,
        hovertext=f"{data_input[percentiles][perkey]}",
        bordercolor="red",
        borderwidth=0.5,
        borderpad=4,
        bgcolor=color,
        opacity=0.6
    )
    
    fig.update_xaxes(showgrid=False, showticklabels=False)
    fig.update_yaxes(showgrid=False, showticklabels=False)

    fig.update_layout(title="", barmode="stack", bargap=0.35,
                    xaxis_title='', yaxis_title='', plot_bgcolor='rgba(0,0,0,0)', legend_orientation = orientation) 

    graph_html = fig.to_html(full_html=False)

    return graph_html


def var_tribin_bar_chart(data, metric):
    '''Creates a Tribin chart to present metric distributions in timeline'''
    fig = go.Figure()
    
    # Adding traces for each category
    fig.add_trace(go.Bar(x=data['last_date'], y=data['good'], name='Good', marker_color='green'))
    fig.add_trace(go.Bar(x=data['last_date'], y=data['needs improvement'], name='Needs Improvement', marker_color='orange'))
    fig.add_trace(go.Bar(x=data['last_date'], y=data['poor'], name='Poor', marker_color='red'))

    #update Layout
    fig.update_layout(barmode='stack', bargap=0.35, title='', xaxis_title='', yaxis_title='Percentage', legend_orientation='h')

    tribin_graph = fig.to_html(full_html=False)

    return tribin_graph

def dbvar_tribin_bar_chart(data, metric):
    '''Creates a Tribin chart to present metric distributions in timeline'''
    fig = go.Figure()
    
    # Adding traces for each category
    fig.add_trace(go.Bar(x=data['last_date'], y=data['good'], name='Good', marker_color='green'))
    fig.add_trace(go.Bar(x=data['last_date'], y=data['needs_improvement'], name='Needs Improvement', marker_color='orange'))
    fig.add_trace(go.Bar(x=data['last_date'], y=data['poor'], name='Poor', marker_color='red'))

    #update Layout
    fig.update_layout(barmode='stack', bargap=0.35, title='', xaxis_title='', yaxis_title='Percentage', legend_orientation='h')

    tribin_graph = fig.to_html(full_html=False)

    return tribin_graph

def ver_p75_line_graph(data, metric, low_threshold, high_threshold):
    try:
        # Convert "p75" column to numeric
        data["p75"] = pd.to_numeric(data["p75"], errors='coerce')
        
        # Extracting data for the line chart
        dates = data["last_date"]
        inp_values = data["p75"]

        #print(f"Debug - Metric: {metric}, Raw Data: {inp_values}")
        #print(f"Debug - Metric: {metric}, Low Threshold: {low_threshold}, High Threshold: {high_threshold}")

        # Creating the figure
        fig = px.line(data, x=dates, y=inp_values, title="")
        fig.update_traces(mode='lines+markers')

        fig.update_layout(
            xaxis=dict(title='Date'),
            yaxis=dict(title=metric),
            showlegend=True,
            shapes=[
                dict(
                    type='rect',
                    xref='paper',
                    yref='y',
                    x0=0,
                    x1=1,
                    y0=0,
                    y1=low_threshold,
                    fillcolor='lightgreen',
                    opacity=0.5,
                    layer='below'
                ),
                dict(
                    type='rect',
                    xref='paper',
                    yref='y',
                    x0=0,
                    x1=1,
                    y0=low_threshold,
                    y1=high_threshold,
                    fillcolor='magenta',
                    opacity=0.5,
                    layer='below'
                )
            ]
        )

        # Convert the figure to an HTML element
        p75_graph = fig.to_html(full_html=False)
        return p75_graph

    except Exception as e:
        print(f"An error occurred: {e}")
        return None
    



def dbver_p75_line_graph(data, metric, low_threshold, high_threshold):
    try:
        # Convert "p75" column to numeric
        data["p75"] = pd.to_numeric(data["p75"], errors='coerce')
        
        # Extracting data for the line chart
        dates = data["last_date"]
        inp_values = data["p75"]

        # print(f"Debug - Metric: {metric}, Raw Data: {inp_values}")
        # print(f"Debug - Metric: {metric}, Low Threshold: {low_threshold}, High Threshold: {high_threshold}")

        # Creating the figure
        fig = px.line(data, x=dates, y=inp_values, title="")
        fig.update_traces(mode='lines+markers')

        fig.update_layout(
            xaxis=dict(title='Date'),
            yaxis=dict(title=metric),
            showlegend=True,
            shapes=[
                dict(
                    type='rect',
                    xref='paper',
                    yref='y',
                    x0=0,
                    x1=1,
                    y0=0,
                    y1=low_threshold,
                    fillcolor='lightgreen',  
                    opacity=0.5,
                    layer='below'
                ),
                dict(
                    type='rect',
                    xref='paper',
                    yref='y',
                    x0=0,
                    x1=1,
                    y0=low_threshold,
                    y1=high_threshold,
                    fillcolor='magenta',  
                    opacity=0.5,
                    layer='below'
                )
            ]
        )

        # Convert the figure to an HTML element
        p75_graph = fig.to_html(full_html=False)
        return p75_graph

    except Exception as e:
        print(f"An error occurred: {e}")
        return None
    



def plotPerformanceBar(dates_list, metric_list, area_color='rgba(0, 0, 255, 0.5)'):
    '''This function outputs the graph based upon the data fetched from the database'''

    # Create a DataFrame from the input data
    df = pd.DataFrame({'Date': dates_list, 'Performance': metric_list})

    # Create the area plot
    fig = px.area(df, x='Date', y='Performance')

    # Set the area color using the fill attribute
    fig.update_traces(fillcolor=area_color, selector=dict(type='scatter', mode='lines'))

    # Calculate the medium values for Performance
    medium_values = df.groupby('Date')['Performance'].median().reset_index()

    # Adding a line for the medium values
    fig.add_trace(px.line(medium_values, x='Date', y='Performance').data[0])

    # Adding annotation circles at data points with actual values
    for date, performance in zip(df['Date'], df['Performance']):
        fig.add_trace(go.Scatter(
            x=[date],
            y=[performance],
            mode='markers',
            marker=dict(
                size=10,
                color='orange', 
                symbol='circle',
                line=dict(color='black', width=2)
            ),
            showlegend=False
        ))

        # Adding annotations above the circles with actual values
        fig.add_annotation(
            x=date,
            y=performance + 2,  # The y-offset for the annotation
            text=str(performance),
            showarrow=False,
            font=dict(size=14),
            xanchor='center',
            yanchor='bottom'
        )

    # Update x-axes with rangeslider and rangeselector and setting up the range to look at last 10 entries for clarity
    last_10_dates = df['Date'].tail(10)
    fig.update_xaxes(
        rangeslider_visible=True,
        range=[last_10_dates.min(), last_10_dates.max()], 
        rangeselector=dict(
            buttons=list([
                dict(count=1, label="1m", step="month", stepmode="backward"),
                dict(count=6, label="6m", step="month", stepmode="backward"),
                dict(count=1, label="YTD", step="year", stepmode="todate"),
                dict(count=1, label="1y", step="year", stepmode="backward"),
                dict(step="all")
            ])
        )
    )

    return fig.to_html(full_html=False)


def plotPerformanceBarWithRound(dates_list, metric_list, rounding=None, ndigits=None, division_factor=None, area_color='rgba(0, 0, 255, 0.5)'):
    '''This function outputs the graph based upon the data fetched from the database'''

    # Create a DataFrame from the input data
    df = pd.DataFrame({'Date': dates_list, 'Performance': metric_list})

    # Perform division if division_factor is provided
    if division_factor is not None:
        df['Performance'] = df['Performance'] / division_factor

    # Round performance values if rounding is specified
    if rounding is not None:
        if ndigits is None:
            ndigits = 2  # Default to 2 digits after the decimal point if ndigits is not provided
        df['Performance'] = df['Performance'].apply(lambda x: round(x, ndigits) if x >= rounding else x)

    # Create the area plot
    fig = px.area(df, x='Date', y='Performance')

    # Set the area color using the fill attribute
    fig.update_traces(fillcolor=area_color, selector=dict(type='scatter', mode='lines'))

    # Calculate the medium values for Performance
    medium_values = df.groupby('Date')['Performance'].median().reset_index()

    # Adding a line for the medium values
    fig.add_trace(px.line(medium_values, x='Date', y='Performance').data[0])

    # Adding annotation circles at data points with actual values
    for date, performance in zip(df['Date'], df['Performance']):
        fig.add_trace(go.Scatter(
            x=[date],
            y=[performance],
            mode='markers',
            marker=dict(
                size=10,
                color='orange', 
                symbol='circle',
                line=dict(color='black', width=2)
            ),
            showlegend=False
        ))

        # Adding annotations above the circles with actual values
        fig.add_annotation(
            x=date,
            y=performance + 2,  # The y-offset for the annotation
            text=str(performance),
            showarrow=False,
            font=dict(size=14),
            xanchor='center',
            yanchor='bottom'
        )

    # Update x-axes with rangeslider and rangeselector and setting up the range to look at last 10 entries for clarity
    last_10_dates = df['Date'].tail(10)
    fig.update_xaxes(
        rangeslider_visible=True,
        range=[last_10_dates.min(), last_10_dates.max()], 
        rangeselector=dict(
            buttons=list([
                dict(count=1, label="1m", step="month", stepmode="backward"),
                dict(count=6, label="6m", step="month", stepmode="backward"),
                dict(count=1, label="YTD", step="year", stepmode="todate"),
                dict(count=1, label="1y", step="year", stepmode="backward"),
                dict(step="all")
            ])
        )
    )

    return fig.to_html(full_html=False)


