import requests
import json
from psi.models import PsiCruxMetric, CruxNodeLevels

#------The set of helper functions to be used for fetching the data from Crux API----------------

def return_response(endpoint, api_key, headers, formFactor, origin):
    url = endpoint+'?key='+str(api_key)

    headers = headers
    data = {
        'effectiveConnectionType':"4G",
        'formFactor': formFactor,
        'origin':origin,
        'metrics': []
    }


    response = requests.post(url=url, headers=headers, json=data)

    return response.json()


def return_history_response(endpoint, api_key, headers, formFactor, origin):
    '''Return response from the HIstory Crux API'''
    url = endpoint+'?key='+str(api_key)

    headers = headers
    data = {
        'formFactor': formFactor,
        'origin':origin,
        'metrics': []
    }


    history_response = requests.post(url=url, headers=headers, json=data)

    return history_response.json()

def return_history_response_for_url(endpoint, api_key, headers, formFactor, url):
    '''Return response from the HIstory Crux API'''
    url_endp = endpoint+'?key='+str(api_key)

    headers = headers
    data = {
        'formFactor': formFactor,
        'url':url,
        'metrics': []
    }


    history_response = requests.post(url=url_endp, headers=headers, json=data)

    return history_response.json()  

def return_response_for_url(endpoint, api_key, headers, formFactor, url):
    '''Return response from the HIstory Crux API'''
    url_endp = endpoint+'?key='+str(api_key)

    headers = headers
    data = {
        'formFactor': formFactor,
        'url':url,
        'metrics': []
    } 
    history_response = requests.post(url=url_endp, headers=headers, json=data)

    return history_response.json()      

def extractCruxDataForOrigin(response):
    '''Extracts the data from the response for a given origin'''

    try:
        metrics = response['record']['metrics']  # Metrics data from the response

    except KeyError:
        metrics = {}
  

    metrics_list, metrics_name_list = purify_list_from_orm()  # Assuming this function returns a list of metrics from the database
 

    metrics_input_list = []
    try:
        for metric_var in metrics_list:
            if metric_var in metrics.keys():
                 metrics_input_list.append(metrics[metric_var])
            else:
                metrics_input_list = []   
    except:
        metrics_input_list = []
       
    ranges = get_ranges_from_orm()  # Assuming this function returns the second list
 
    #Adding ranges to histogram but only those that are present in the request by checking the keys in the response dictionery
    for i, element in enumerate(ranges):      
        metrics_input_list[i]['ranges'] = element


    return metrics_input_list, metrics_list, metrics_name_list


def extractCruxDataForUrl(response):
    ''''Extracts the data from the response for a given URL'''

    try:
        metrics = response['record']['metrics']  # Metrics data from the response

    except KeyError:
        metrics = {}
  

    metrics_list, metrics_name_list = purify_list_from_orm_with_response_check(response=response)  # Assuming this function returns a list of metrics from the database
 
 
    metrics_input_list = []
    try:
        for metric_var in metrics_list:
            if metric_var in metrics.keys():
                 metrics_input_list.append(metrics[metric_var])
            else:
                metrics_input_list = []   
    except:
        metrics_input_list = []
       
    ranges = get_ranges_from_orm_with_response_check(response=response)  # This fnction returns returns the ranges
   
    #Adding ranges to histogram but only those that are present in the request by checking the keys in the response dictionery
    for i, element in enumerate(ranges):       
        metrics_input_list[i]['ranges'] = element
  

    return metrics_input_list, metrics_list, metrics_name_list

def extractCruxDataForUrlWithResponseCheck(response):
    ''''Extracts the data from the response for a given URL'''

    try:
        metrics = response['record']['metrics']  # Metrics data from the response
        metrics_from_response = list(metrics.keys())  #COnverting this to a list to be used for comparison

    except KeyError:
        metrics = {}
        metrics_from_response = []
    


    metrics_list, metrics_name_list = purify_list_from_orm_with_response_check(response=response)  # Assuming this function returns a list of metrics from the database
  
 
    metrics_input_list = []
    try:
        for metric_var in metrics_from_response:
            if metric_var in metrics.keys():
                 metrics_input_list.append(metrics[metric_var])
            else:
                metrics_input_list = []   
    except:
        metrics_input_list = []
       
    ranges = get_ranges_from_orm_with_response_check(response=response)  # Assuming this function returns the second list

    #Adding ranges to histogram but only those that are present in the request by checking the keys in the response dictionery
    for i, element in enumerate(ranges):       
        metrics_input_list[i]['ranges'] = element
  
    print(metrics_input_list)
    return metrics_input_list, metrics_list, metrics_name_list



    
 
            


def purify_list_from_orm():
    '''Purifies the ORM query set'''
    crmetrics = PsiCruxMetric.objects.all().values_list("metric", "crux_metric_name").order_by("id")  #Fetching metrics from the database

    metrics_list = list(map(lambda x: x[0], crmetrics))
    metric_name_list = list(map(lambda x: x[1], crmetrics))


    return metrics_list, metric_name_list

def purify_list_from_orm_with_response_check(response):
    '''Purifies the ORM query set and checks if the response is not empty'''
    #Fecthing the metrics variables from the response. This may have none values or uncompited values
    try:
        active_metrics_from_response = list(response['record']['metrics'].keys())
    except:
        active_metrics_from_response = []


    crmetrics = PsiCruxMetric.objects.all().values_list("metric", "crux_metric_name").order_by("id")  #Fetching metrics from the database

    metrics_list = list(map(lambda x: x[0], crmetrics))
    metric_name_list = list(map(lambda x: x[1], crmetrics))


    return metrics_list, metric_name_list    


def get_ranges_from_orm():
    '''Getting the ranges from the ORM'''

    name_and_ranges = PsiCruxMetric.objects.all().values_list("crux_metric_name", "good_values", "need_improvement", "poor").order_by("id")  #Fetching metrics from the database    
    #namesAndLists = list(map(lambda x: x[0:], name_and_ranges))
    namesAndLists = [[name] + list(ranges) for name, *ranges in name_and_ranges] 
    return namesAndLists

def get_ranges_from_orm_with_response_check(response):
    ''''Getting the ranges from the ORM and checking if the response is not empty'''
    #setting up the final range list
    filteredRangeList = []
    try:
        metrics = response['record']['metrics'] 
    except:
        metrics = []    
    #Fetching metrics from the database
    name_and_ranges = PsiCruxMetric.objects.all().values_list("metric","crux_metric_name", "good_values", "need_improvement", "poor").order_by("id")
 
    #Converting the query set to the list
    namesAndLists = [[name] + list(ranges) for name, *ranges in name_and_ranges]

    #Iterating over the list of tuples  and checking if the name of the metric is in the response if not redusing the list
    for active_metric in metrics:
        for name in namesAndLists:
            if active_metric == name[0]: # It's a list of the lists so we suscritbe to the first element
                filteredRangeList.append(name[1:])
  
    return filteredRangeList           
           

        
    


def map_metrics_with_response(response):
    """Maps the metric with the corresponding data in the request and return input list"""
    try:
        response_metrics = response['record']['metrics']  #Metrics data from the response
    except:
        response_metrics = []  #Sending empty list in to prevent error in the template
    for key in response_metrics.keys():
        for dbmetric in purify_list_from_orm():
            if key ==  dbmetric:
                #print(key,response_metrics[dbmetric])
                return key,response_metrics[dbmetric]


    
    
    #print(response_metrics)



def prepareLiveDataFromList(data_list):
    '''Prepares data to be consumed in Dash Bar'''
    #formFactor = data_list['form']
    #metrics = data_list['zipped_metrics'][0][0]
    #histogram_data = data_list['zipped_metrics'][0][1]
    
    return data_list


def get_condition_ranges():
    """Return prepared dictionery to be passed to the context"""
    node_ranges = CruxNodeLevels.objects.filter(predecesor="metrics")

    return node_ranges  



       
   




