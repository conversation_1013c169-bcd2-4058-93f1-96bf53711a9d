from typing import Any, Tu<PERSON>, Dict, List
import pandas as pd
import numpy as np
import pandas


#The set of tools that will be used for Crux API
#***********************************************
def short_name(metric: str) ->str:
    '''Return a short metric name. Abbreviations will be used in a legend'''
    abbr = ''.join([s[0].upper()
    for s in metric.splot('_') if s != 'experimental'])

    return abbr if abbr != 'ITNP' else 'INP'



def metric_name(rtesponse: dict) -> List[str]:
    ''''Takes the response as aninput and return a list of available metrics
    in the response.
    '''  
    try:  
        metrics = list(rtesponse['record']['metrics'].keys())
        metrics.sort()
    except:
        metrics = []    
    
    return metrics

def threshold_by_metric(response: dict) -> Dict[str, Tuple[float, float]]:
    '''The threshold by metric name

       Key in the returned dict is a metric name, e.g. 'first_contentful_paint'.
       Value is a tuple of the low threshold, which separates 'good' from
       'needs improvement', and the high threshold, which separates
       'needs improvement' from 'poor'.
    '''
    result = {}
    try:
        for metric, data in response.get('record', {}).get('metrics', {}).items():
            if "navigation_types" not in metric:
                histogram = data.get('histogramTimeseries', [])
                if len(histogram) > 1: 
                    result[metric] = (float(histogram[1].get('start', 0.0)), 
                                      float(histogram[1].get('end', 0.0)))
                else:
                    # Handle case where histogramTimeseries has less than 2 elements //Letting this pass for now
                    pass
        return result
    except KeyError as e:
        print(f"KeyError: {e}")
        return result
    except IndexError as e:
        print(f"IndexError: {e}")
        return result
    except Exception as e:
        print(f"Exception: {e}")
        return result



def dataframe_prep(metric, response) -> pandas.DataFrame:
    '''Prepares dataframe by extracting the info about p75s
       histogram density timeseries for a metric
     '''

    timestamp = lambda e : pandas.Timestamp(e['year'], e['month'], e['day'])

    cols = {
        'first_date': [timestamp(e['firstDate'])
                    for e in response['record']['collectionPeriods']],
        'last_date': [timestamp(e['lastDate'])
                    for e in response['record']['collectionPeriods']],
    }

    data = response['record']['metrics'][metric]   

    cols['p75'] = data['percentilesTimeseries']['p75s']
    cols['good'] = data['histogramTimeseries'][0]['densities']
    cols['needs improvement'] = data['histogramTimeseries'][1]['densities']
    cols['poor'] = data['histogramTimeseries'][2]['densities']
    
    return pandas.DataFrame(cols)    

def map_metric_names():
    '''This function maps metric name with the corresponding variable name
       It will be passed as a parameter to the plot function
    '''    
    metrics_map = {
        'largest_contentful_paint': 'Largest Contentful Paint',
        #'first_input_delay': 'First Input Delay',     
        'cumulative_layout_shift':'Cumulative Layout Shift',   
        'first_contentful_paint': 'First Contentful Paint',   
        'interaction_to_next_paint': 'Interaction to Next Paint',                     
        'experimental_time_to_first_byte':'Time to First Byte',

    }

    return metrics_map



#-------------This set of functions is used with Performance Section ------------------###


def prepareOrmDataForPerformance(query_set):
    '''This function takes as an argument queryset and return a list to feed a plotly graph'''
    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.performance_score)
        dates.append(item.extractionDate)
    return dates, metric_list    


def preparePageSpeedData(query_set):
    '''This function takes as an argument a queryset aned retur a lists of dates and page speed metrics to feed plotly graph'''
    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.speed_index)
        dates.append(item.extractionDate)
    return dates, metric_list   

def prepareLCPData(query_set):
    '''This Function prepares the LCP data and return a list of dates and LCP metrics to feed the plotly graph'''
    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.largest_contentful_paint)
        dates.append(item.extractionDate)
    return dates, metric_list      

def prepareFCPData(query_set):
    '''This function prepares the FCP data and return a list of dates and FCP metrics to feed the plotly graph'''
    
    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.first_contentful_paint)
        dates.append(item.extractionDate)
    return dates, metric_list  


def prepareTBTData(query_set):
    '''This function prepares the TBT data and return a list of dates and TBT metrics to feed the plotly graph'''


    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.total_blocking_time)
        dates.append(item.extractionDate)
    return dates, metric_list  

def prepareCLSData(query_set):
    '''This function prepares the   CLS data and return a list of dates and CLS metrics to feed the plotly graph'''

    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.cumulative_layout_shift)
        dates.append(item.extractionDate)
    return dates, metric_list     


def prepareSRTData(query_set):
    '''This function prepares the SRT data and return a list of dates and SRT metrics to feed the plotly graph'''

    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.server_response_time)
        dates.append(item.extractionDate)
    return dates, metric_list   


def prepareSNLData(query_set):
    '''This function prepares the Snl data and return a list of dates and SNL metrics to feed the plotly graph'''

    dates = []
    metric_list = []
    for item in query_set:
        metric_list.append(item.server_network_latency)
        dates.append(item.extractionDate)
    return dates, metric_list   


#---------------------------- This set of tools is used to calculate basic statistics  -----------------------------


def getFieldsNames(model_name, values):
    '''Makes the connection to the database and return column names in a list'''

    query_set = model_name.objects.all()
    col_names = list(query_set.values().first().keys())

    return col_names
    


def combineMediumValues():
    '''This function compares medium values for individual URL'''
    pass



#---------Convert sjon response to dataframe ------------------
def json_to_dataframe(data_json, collectionPeriodsData):
    '''Takes input data_json and collectionPerionds as a parameter 
       and returns a set od prepared dataframes
    .'''
    
    # Converting Queryset in dictionery
    input_json = data_json[0]

    # Timestamps creation using labda function
    timestamp = lambda e: pd.Timestamp(e['year'], e['month'], e['day'])

    # prepare dates usinf collectionPeriodsData
    first_dates = [timestamp(dt['first_date_json']) for dt in collectionPeriodsData]
    last_dates = [timestamp(dt['last_date_json']) for dt in collectionPeriodsData]
    # Initializing empty dictionery to hold the dataframes
    dataframes_dict = {}


    for key in input_json.keys():
        metric_data = input_json[key]
        

        cols = {
            'first_date': first_dates,
            'last_date': last_dates,
            'p75': metric_data['percentilesTimeseries']['p75s'],
            'good': metric_data['histogramTimeseries'][0]['densities'],
            'needs_improvement': metric_data['histogramTimeseries'][1]['densities'],
            'poor': metric_data['histogramTimeseries'][2]['densities'],
        }
        try:
        
            df = pd.DataFrame(cols)
            dataframes_dict[key] = df
        except Exception as e:
            df = None    

    return dataframes_dict

#Function to extract thresholds
def threshold_by_dbmetric(response: dict) -> Dict[str, Tuple[float, float]]:

    result = {}
    try:
        input_json = response[0]
    except Exception as e:
        input_json = {}    


    for key in input_json.keys():
        metric_data = input_json[key]['histogramTimeseries']
  
        result[key] = (float(metric_data[1].get('start', 0.0)), 
                                      float(metric_data[1].get('end', 0.0)))
    return result



