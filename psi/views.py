from django.shortcuts import render, redirect, get_object_or_404
from django.db.models import F, OuterRef, Subquery
from django.contrib.auth.decorators import login_required
from .models import Domain, PsiPageType, PsiUrl, CruxFormFactor, CruxFetchResultsForOrigin, \
    CruxJsonRecord, CruxFetchResultsForUrl, PsiApiKey, CruxHeaders, PsiEndPoint, PsiMainCoreWebVitalsMediana, \
        PsiMainCoreWebVitalsDifference, PsiMainCoreWebVitalsStandardDeviation, PsiMainCoreWebVitalsAverages, \
            PsiMainCoreWebVitalsVarCoeff, Snapshoots, CruxCollectionPeriod, CruxJsonOriginRecord, CruxJsonOriginMetrics, CruxCollectionOriginPeriod, CruxDelta
from psi.forms import CreateNewPsiDomainForm, CreateNewPsiPageTypeForm, CreateNewPsiUrlForm, CreateNewFormFactorFormDropdown, CreateNewFormDropdownForUrls, CreateNewFormDropdownForPsiWeeklyReport, CreateNewFormDropdownForDomains
from django.core.files.storage import FileSystemStorage
from psi.psi_utilities.dashes.psi_dashes import var_tribin_bar_chart, ver_p75_line_graph, hor_bar_plot, plotPerformanceBar, \
    plotPerformanceBarWithRound, dbvar_tribin_bar_chart, dbver_p75_line_graph
from psi.psi_utilities.utilities import return_response, extractCruxDataForOrigin, prepareLiveDataFromList, get_ranges_from_orm_with_response_check, return_history_response, return_history_response_for_url, return_response_for_url
from psi.psi_utilities.data_utils.df_utils import *
from psi.models import PsiCruxMetric, PsiMainCoreWebVitalsMetrics, Snapshoots
from psi.psi_utilities.crux_url_level_helpers import fetchMetricVarsFromResponse, combineCheckAndFilter, prepareDataAndOutputGraphs, \
    mapHistoricalMetricsNames, mapHistoricalTribinMetricsNames, mapHistoricalp75MetricsNames
import ast
from datetime import datetime,timedelta
from django.utils.timezone import make_aware
from django.utils import timezone

# Create your views here.


@login_required(login_url='./login')
def psi_domain_dash(request):
    psi_details = Domain.objects.all()

    print("Test")
    context = {
        'psi_details':psi_details,
    }
    
    return render(request,'psi/psi-domain-dash.html', context=context)



@login_required(login_url='./login')
def psi_admin(request):


    return render(request, 'psi/psi-admin.html', {})




@login_required(login_url='./login')
def add_new_domain(request):
    """Used to add a new domain in the database"""
    if request.method == "POST":
        form = CreateNewPsiDomainForm(request.POST, request.FILES)
        if form.is_valid():
            add_url = form.cleaned_data['url']
            add_domain = form.cleaned_data['domain']
            logo = form.cleaned_data['logo']

            # Check if a record with the same domain already exists
            if not Domain.objects.filter(domain=add_domain).exists():
                # Save the uploaded file
                fs = FileSystemStorage()
                name = fs.save(logo.name, logo)
                url = fs.url(name)

                # Create a new Domain instance
                domain = Domain.objects.create(url=add_url, domain=add_domain, logo=url)




                return redirect("psiadmin")  
            else:
                # Display a message to the user indicating that the domain already exists
                form.add_error('domain', 'This domain already exists.')

    else:
        form = CreateNewPsiDomainForm()

    context = {
        'form': form,
    }

    return render(request, 'psi/psi-add-new-domain.html', context=context)






@login_required(login_url='./login')
def add_new_ptype(request):
    """Used to add a new page type in the database"""
    if request.method == "POST":
        form = CreateNewPsiPageTypeForm(request.POST)
        if form.is_valid():
            add_page_type = form.cleaned_data['page_type']

            # Check if a record with the same page type already exists
            if not PsiPageType.objects.filter(type=add_page_type).exists():

                # Create a new Page type instance
                new_page_type = PsiPageType.objects.create(type=add_page_type)




                return redirect("psiadmin")  
            else:
                # Display a message to the user indicating that the domain already exists
                form.add_error('page_type', 'This page type already exists.')

    else:
        form = CreateNewPsiPageTypeForm()

    context = {
        'form': form,
    }

    

   
    return render(request, 'psi/psi-add-page-type.html', context=context)

@login_required(login_url='./login')
def add_new_url(request):
    """Used to add a new url in the database"""    
    if request.method == 'POST':
        form = CreateNewPsiUrlForm(request.POST)
        if form.is_valid():
            # Get the entered URL
            url = form.cleaned_data['url']

            # Check if the URL already exists in the database
            url_count = PsiUrl.objects.filter(url=url).count()
            if url_count > 0:
                # URL already exists, handle the duplicate URL case here (e.g., show an error message)
                error_message = "This URL already exists in the database."
                return render(request, 'psi/psi-add-new-url.html', {'form': form, 'error_message': error_message})

            # URL is unique, proceed to create a new PsiUrl instance
            domain = form.cleaned_data['domain']
            page_type = form.cleaned_data['type']

            psi_url = PsiUrl(
                url=url,
                domain=domain,
                type=page_type,
                is_active=True
            )
            psi_url.save()
            return redirect('psiadmin')

    else:
        form = CreateNewPsiUrlForm()

    context = {
        "form":form,
    }

    return render(request, 'psi/psi-add-new-url.html', context=context)




@login_required(login_url='./login')
def crux_domain_explore(request, id):
    """crUX report per domain"""
    domains = get_object_or_404(Domain, pk=id)
    formFactors = CruxFormFactor.objects.all().order_by("id")
    origin_data = CruxFetchResultsForOrigin.objects.filter(domain_id=id)
    psi_endpoint = PsiEndPoint.objects.filter(id=2)
    history_crux_endpoint = PsiEndPoint.objects.filter(id=3)
    #ranges = get_ranges_from_orm()

    selected_form_factor = request.GET.get('form_factor')

    if request.method == 'POST':
        form = CreateNewFormFactorFormDropdown(request.POST)
        if form.is_valid():
            selected_form_factor = form.cleaned_data['form_factor']
    else:
        form = CreateNewFormFactorFormDropdown(initial={'form_factor': selected_form_factor})

    try:
        api_keys = PsiApiKey.objects.filter(is_valid=True)
    except PsiApiKey.DoesNotExist:
        api_keys = None    
    url_data = CruxFetchResultsForUrl.objects.all()
    header = CruxHeaders.objects.values('header')
    #A set of empty lists to make sure they are declared and defined in the function space 

    zipped_data = []
    hzipped_data = []
    zipped_graphs = []
    zipped_tribin_graph = []
    p75_zipped_graph = []


    #cr_response for regular bullet graphs and history_response for tribin and p75th graphs - This should be refactored
    try:
        if selected_form_factor != None:
            cr_response = return_response(endpoint=str(psi_endpoint[0]), api_key=str(api_keys[0]), headers=dict(header[0]), formFactor=str(selected_form_factor), origin=str(domains.url))  

            history_response = return_history_response(endpoint=str(history_crux_endpoint[0]), api_key=str(api_keys[0]), headers=dict(header[0]), formFactor=str(selected_form_factor), origin=str(domains.url))   
        else:
            cr_response = return_response(endpoint=str(psi_endpoint[0]), api_key=str(api_keys[0]), headers=dict(header[0]), formFactor=str(selected_form_factor), origin=str(domains.url))
    except Exception as e:
           print(e)
    #Testing ranges from the response instead old function       
    ranges = get_ranges_from_orm_with_response_check(cr_response)         
    try:    
        metrics_data, metrics_list, metrics_name_list = extractCruxDataForOrigin(response=cr_response)
        zipped_metrics = list(zip(metrics_name_list, metrics_data))
        zipped_data.append({'formFactor': selected_form_factor, 'zipped_metrics': zipped_metrics})

    except Exception as e:
        metrics_data = []
        metrics_list = []
        metrics_name_list = []       
    for metric in zipped_data:
        for tpl in metric['zipped_metrics']:
            data = prepareLiveDataFromList(data_list=tpl[1])
            crux_live_hor_bar = hor_bar_plot(data_input=tpl[1], metric_name=tpl[0], ranges=tpl[1]['ranges'][1:], dinput='histogram', percentiles='percentiles', perkey="p75", orientation='v')  
            zipped_graphs.append({'metric': tpl[0], 'graph': crux_live_hor_bar})

    # Preparing the data for historical response Tribin graphs for 3 bins
    mapped_metrics = map_metric_names()

    for hmetric_name_var, metric_name in zip(metrics_list, list(mapped_metrics.values())):
        try:
            data = dataframe_prep(hmetric_name_var, history_response)
            tribin_chart = var_tribin_bar_chart(data, metric_name)
            zipped_tribin_graph.append({'metric': metric_name, 'graph': tribin_chart})
        except Exception as e:
            # Handle the exception - Can be rmoved later
            print(f"An error occurred while processing data for metric: {metric_name}")

    # Preparing the data for historical response p75s for each metric
    try:
        thresholds = threshold_by_metric(history_response)
        sorted_thresholds = {metric: thresholds[metric] for metric in mapped_metrics}

        for hmetric_name_var, metric_name, _, low_threshold, high_threshold in zip(
                        metrics_list, 
                        list(mapped_metrics.values()), 
                        sorted_thresholds.keys(), 
                        *zip(*sorted_thresholds.values())):    

            try:

                # Explicitly Convert thresholds to float to prevent integers to round numbers
                low_threshold = float(low_threshold)
                high_threshold = float(high_threshold)

                #PAssing history response to this func which should return dataframe in this case            
                data = dataframe_prep(hmetric_name_var, history_response)

                #Creating graphs and append them together with other data  - needed by the template
                p75_graph = ver_p75_line_graph(data, metric_name, low_threshold, high_threshold)
                p75_zipped_graph.append({'metric': metric_name, 'graph': p75_graph})

            except Exception as e:
                # Handle the exception, if required
                print(f"An error occurred while processing data for metric: {metric_name}")
                thresholds = {}
    except Exception as e:
        print(e)
                  
    context = {
        'domain': domains,
        'formFactors': formFactors,
        'metrics': metrics_list,
        "origin_data": origin_data,
        "url_data": url_data,
        "zipped_data": zipped_data,
        "zipped_graphs": zipped_graphs,
        'form': form,
        'metrics_name_list': metrics_name_list,
        'zipped_tribin_graph': zipped_tribin_graph,
        'hzipped_data': hzipped_data,
        'p75_zipped_graph':p75_zipped_graph,
    }

    return render(request, 'psi/crux-origin-explore.html', context=context)

########################Fetching the Origin data from database

@login_required(login_url='./login')
def crux_dbdomain_explore(request, id):
    '''Crux Report for the individual URL by domain id with the data fetched from the database'''

    #Initializing vars outside try_catch block to avoyd uninitalized objects after check
    zipped_data = []
    zipped_graphs = []
    zipped_tribin_graphs = []
    p75_zipped_graphs = []

    try:
        domain = get_object_or_404(Domain, pk=id)
        domain_data = Domain.objects.filter(
            domain=id,
            in_use = 1 
              )
        orm_metrics = PsiCruxMetric.objects.all().values_list(
            "metric",
            "crux_metric_name",
            "good_values",
            "need_improvement",
            "poor"
        )

    except Exception as e:
        print(f"An exception occured: {e}")
        domain = []
        domain_data = []
        orm_metrics = []   


    # Handle the POST  request from the factor dropdown    
    if request.method == "POST":
        form = CreateNewFormDropdownForDomains(request.POST, domain_id = id)

        if form.is_valid():
            selected_form_factor = form.cleaned_data.get("form_factor")

            #Fetch the data from the database for each url that is selected in respect to the choosen form factor
            crux_url_metrics_data = CruxFetchResultsForOrigin.objects.filter(domain_id=domain.id, form_factor=selected_form_factor).last()
            attributes = [
                'largest_contentful_paint', 
                #'first_input_delay', 
                'cumulative_layout_shift', 
                'first_contentful_paint', 
                'interaction_to_next_paint', 
                'experimental_time_to_first_byte'
                ]
            hist_data = []
            for attr in attributes:
                if attr is not None:
                    try:
                        hist_data.append(getattr(crux_url_metrics_data, attr))
                    except AttributeError:
                        hist_data.append(None)    
                else:
                    hist_data.append(None)        

            response_metrics_vars = [
                'Largest Contentful Paint',
                #'First Input Delay',
                'Cumulative Layout Shift',
                'First Contentful Paint',
                'Interaction to Next Paint',
                'Time to First Byte'
            ]
            histograms = {}
            try:
                for hist, attribute in zip(hist_data, attributes):
                    if hist is not None:
                        hist_dict = ast.literal_eval(hist)
                        histograms[attribute] = hist_dict
                    else:
                        pass    
            except:
                histograms = {}        



            prepacked_orm = [orm for orm in orm_metrics if any(var in orm for var in response_metrics_vars)] 


            #if no data is obtained from the database retunn empty list and none for zipped_graphs
            try:
                zipped_data = combineCheckAndFilter(
                keys_from_the_response= attributes, crm_from_db=prepacked_orm,
                    the_hist_data=histograms, form_factor=selected_form_factor
                )
                zipped_graphs = prepareDataAndOutputGraphs(zipped_data)   
            except:
                zipped_data = []
                zipped_graphs = None   

            #--------------------  Obtaining the data from models which returns json results as the response ---------------------------
            json_results = CruxJsonOriginRecord.objects.filter(
                identifier=1,
                url = (domain.url).rstrip('/'),
                form_factor=selected_form_factor
            ).annotate(
                largest_contentful_paint_histogram_densities=F('cruxjsonoriginmetrics__largest_contentful_paint_histogram_densities'),
                #first_input_delay_histogram_densities=F('cruxjsonoriginmetrics__first_input_delay_histogram_densities'),
                cumulative_layout_shift_histogram_densities=F('cruxjsonoriginmetrics__cumulative_layout_shift_histogram_densities'),
                first_contentful_paint_histogram_densities=F('cruxjsonoriginmetrics__first_contentful_paint_histogram_densities'),
                interaction_to_next_paint_histogram_densities=F('cruxjsonoriginmetrics__interaction_to_next_paint_histogram_densities'),
                experimental_time_to_first_byte_histogram_densities=F('cruxjsonoriginmetrics__experimental_time_to_first_byte_histogram_densities'),
            ).values(
                'largest_contentful_paint_histogram_densities',
                #'first_input_delay_histogram_densities',
                'cumulative_layout_shift_histogram_densities',
                'first_contentful_paint_histogram_densities',
                'interaction_to_next_paint_histogram_densities',
                'experimental_time_to_first_byte_histogram_densities',
            ).order_by('id') 


            json_p75_results = CruxJsonOriginRecord.objects.filter(
                identifier=1,
                url = (domain.url).rstrip('/'),
                form_factor=selected_form_factor
            ).annotate(
                largest_contentful_paint_percentiles_p75s=F('cruxjsonoriginmetrics__largest_contentful_paint_percentiles_p75s'),
                #first_input_delay_percentiles_p75s=F('cruxjsonoriginmetrics__first_input_delay_percentiles_p75s'),
                cumulative_layout_shift_percentiles_p75s=F('cruxjsonoriginmetrics__cumulative_layout_shift_percentiles_p75s'),
                first_contentful_paint_percentiles_p75s=F('cruxjsonoriginmetrics__first_contentful_paint_percentiles_p75s'),
                interaction_to_next_paint_percentiles_p75s=F('cruxjsonoriginmetrics__interaction_to_next_paint_percentiles_p75s'),
                experimental_time_to_first_byte_percentiles_p75s=F('cruxjsonoriginmetrics__experimental_time_to_first_byte_percentiles_p75s'),
            ).values(
                'largest_contentful_paint_percentiles_p75s',
               #'first_input_delay_percentiles_p75s',
                'cumulative_layout_shift_percentiles_p75s',
                'first_contentful_paint_percentiles_p75s',
                'interaction_to_next_paint_percentiles_p75s',
                'experimental_time_to_first_byte_percentiles_p75s',
            ).order_by('id')               
            

            record_ids = json_results.values_list('id', flat=True)

            #------------------- Obtaining the data from Collection Periods Model ----------------------------------
            collection_periods_results = CruxCollectionOriginPeriod.objects.filter(
                record_id__in = record_ids,
                ).values('first_date_json', 'last_date_json').distinct()

            # #Mapping metrics variable names to human readable names
            metric_names_mapping = mapHistoricalTribinMetricsNames()

            #Creating dataframes dict for Tribin 
            try:
                dataframes_dict = json_to_dataframe(json_results, collection_periods_results)
            except Exception as e:
                print(f"There's an error while creating a Dataframe: {str(e)}")
                dataframes_dict = {}

            # #Creating dictioneries for p75 graphs 
            try:
                p75_dataframes_dict = json_to_dataframe(json_p75_results, collection_periods_results)
            except Exception as e:
                print(f"There's an error while creating a Dataframe: {str(e)}")
                p75_dataframes_dict = {}                      

            #Populating Tribin graphs
            for hmetric_name_var, metric_name in metric_names_mapping.items():
                try:
                    
                    data = dataframes_dict.get(hmetric_name_var)
                    if data is not None:
                        
                        tribin_chart = dbvar_tribin_bar_chart(data, metric_name)

                        
                        zipped_tribin_graphs.append({'metric': metric_name, 'graph': tribin_chart})
                    else:
                        print(f"Nema podataka za metrički podatak: {metric_name}")
                except Exception as e:
                    print(f"Došlo je do greške pri obradi podataka za metrički podatak: {metric_name}. Greška: {str(e)}")


            # #Populating p75 graphs     
            # Getting thresholds and sort them to make sure they are in wright order
            thresholds = threshold_by_dbmetric(json_p75_results)
           
            try:
                p75sorted_thresholds = {metric: thresholds[metric] for metric in mapHistoricalp75MetricsNames()}
            except Exception as e:
                print(e)
                p75sorted_thresholds = {} 
            for hmetric_name_var, metric_name, _, low_threshold, high_threshold in zip(
                    mapHistoricalp75MetricsNames().keys(),
                    list(mapHistoricalp75MetricsNames().values()),
                    p75sorted_thresholds.keys(),
                    *zip(*p75sorted_thresholds.values())
            ):
                try:
                    data = p75_dataframes_dict.get(hmetric_name_var)
                    low_threshold = float(low_threshold)
                    high_threshold = float(high_threshold)
                  
                    p75_graph = dbver_p75_line_graph(data, metric_name, low_threshold, high_threshold)
                    p75_zipped_graphs.append({'metric': metric_name, 'graph': p75_graph})
                except Exception as e:
                    print(f"An error occurred while processing data for metric: {metric_name}")                    
  
                                 





        else:

            selected_form_factor = None
 
    else:
        form = CreateNewFormDropdownForDomains(initial={"url_form":None, "form_factor":None}, domain_id = id)            


    context = {
        "domain":domain,
        "domain_data":domain_data,
        "form":form,
        'zipped_data':zipped_data,
        'zipped_graphs':zipped_graphs,
        'zipped_tribin_graph':zipped_tribin_graphs,
        'p75_zipped_graph': p75_zipped_graphs,        
        
    }

    return render(request, 'psi/crux-origin-explore.html', context=context)

@login_required(login_url='./login')
def crux_url_explore(request, id):
    '''Crux Report for the individual URL by domain id'''

    # Initialize variables outside the try-except block
    zipped_data = []
    zipped_graphs = []
    zipped_tribin_graph = []
    p75_zipped_graph = []

    try:
        domains = get_object_or_404(Domain, pk=id)
        url_data = PsiUrl.objects.filter(domain=id)
        psi_endpoint = PsiEndPoint.objects.filter(id=2)
        history_crux_endpoint = PsiEndPoint.objects.filter(id=3)
        header = CruxHeaders.objects.values('header')
        api_keys = PsiApiKey.objects.filter(is_valid=True)
        orm_metrics = PsiCruxMetric.objects.all().values_list(
            "metric",
            "crux_metric_name",
            "good_values",
            "need_improvement",
            "poor"
        ).order_by("id")
    except PsiApiKey.DoesNotExist:
        api_keys = None
    except Exception as e:
        print(e)

    # Handling the POST request for the form factor dropdown to ensure it resets the form factor to None
    # in case the user selects the default option
    if request.method == 'POST':
        url_form = CreateNewFormDropdownForUrls(request.POST, domain_id=id)

        if url_form.is_valid():
            url_select_form = url_form.cleaned_data.get('url_form')
            selected_form_factor = url_form.cleaned_data.get('form_factor')

            # URL Level - LIVE DATA
            try:
                url_response = return_response_for_url(
                    endpoint=str(psi_endpoint[0]),
                    api_key=str(api_keys[0]),
                    headers=dict(header[0]),
                    formFactor=str(selected_form_factor),
                    url=str(url_select_form)
                )
            except Exception as e:
                url_response = None    

            print(url_response)
            response_metrics_vars, the_data = fetchMetricVarsFromResponse(
                url_response, outer_key="record", inner_key="metrics"
            )

            zipped_data = combineCheckAndFilter(
                response_metrics_vars, crm_from_db=list(orm_metrics),
                the_hist_data=the_data, form_factor=selected_form_factor
            )
            zipped_graphs = prepareDataAndOutputGraphs(zipped_data)

        # HISTORY RESPONSE - HISTORICAL DATA
            try:
                history_response = return_history_response_for_url(
                    endpoint=str(history_crux_endpoint[0]),
                    api_key=str(api_keys[0]),
                    headers=dict(header[0]),
                    formFactor=str(selected_form_factor),
                    url=str(url_select_form))
            except Exception as e:
                        history_response = None        

            metrics_list = []
            for hmetric_name_var, metric_name in mapHistoricalMetricsNames().items():
                try:
                    data = dataframe_prep(hmetric_name_var, history_response)
                    tribin_chart = var_tribin_bar_chart(data, metric_name)
                    zipped_tribin_graph.append({'metric': metric_name, 'graph': tribin_chart})
                except Exception as e:
                    print(f"An error occurred while processing data for metric: {metric_name}")

            thresholds = threshold_by_metric(history_response)
            try:
                sorted_thresholds = {metric: thresholds[metric] for metric in mapHistoricalMetricsNames()}
            except Exception as e:
                print(e)
                sorted_thresholds = {}

            for hmetric_name_var, metric_name, _, low_threshold, high_threshold in zip(
                    mapHistoricalMetricsNames().keys(),
                    list(mapHistoricalMetricsNames().values()),
                    sorted_thresholds.keys(),
                    *zip(*sorted_thresholds.values())
            ):
                try:
                    low_threshold = float(low_threshold)
                    high_threshold = float(high_threshold)
                    data = dataframe_prep(hmetric_name_var, history_response)
                    p75_graph = ver_p75_line_graph(data, metric_name, low_threshold, high_threshold)
                    p75_zipped_graph.append({'metric': metric_name, 'graph': p75_graph})
                except Exception as e:
                    print(f"An error occurred while processing data for metric: {metric_name}")

    else:
        url_form = CreateNewFormDropdownForUrls(initial={"url_form": None, "form_factor": None}, domain_id=id)

    context = {
        'domain': domains,
        'url_data': url_data,
        'url_form': url_form,
        'zipped_data': zipped_data,
        'zipped_graphs': zipped_graphs,
        'zipped_tribin_graph': zipped_tribin_graph,
        'p75_zipped_graph': p75_zipped_graph,
    }

    return render(request, 'psi/crux-url-explore.html', context=context)

#************************************************************************************
#THIS PART IS FETCHING THE CRUX DATA FROM THE DATABASE INSTEAD TO MAKE LIVE REQUESTS


@login_required(login_url='./login')
def cruxdb_url_explore(request, id):
    '''Crux Report for the individual URL by domain id with the data fetched from the database'''

    #Initializing vars outside try_catch block to avoyd uninitalized objects after check
    zipped_data = []
    zipped_graphs = []
    zipped_tribin_graphs = []
    p75_zipped_graphs = []

    try:
        domain = get_object_or_404(Domain, pk=id)
        url_data = PsiUrl.objects.filter(domain=id)
        orm_metrics = PsiCruxMetric.objects.all().values_list(
            "metric",
            "crux_metric_name",
            "good_values",
            "need_improvement",
            "poor"
        )

    except Exception as e:
        print(f"An exception occured: {e}")
        domain = []
        url_data = []
        orm_metrics = []   


    # Handle the POST  request from the factor dropdown    
    if request.method == "POST":
        url_form = CreateNewFormDropdownForUrls(request.POST, domain_id = id)

        if url_form.is_valid():
            url_select_form = url_form.cleaned_data.get("url_form")
            selected_form_factor = url_form.cleaned_data.get("form_factor")

            #Fetch the data from the database for each url that is selected in respect to the choosen form factor
            crux_url_metrics_data = CruxFetchResultsForUrl.objects.filter(domain_id=domain.id, url=url_select_form, form_factor=selected_form_factor).last()
            attributes = [
                'largest_contentful_paint', 
                #'first_input_delay', 
                'cumulative_layout_shift', 
                'first_contentful_paint', 
                'interaction_to_next_paint', 
                'experimental_time_to_first_byte'
                ]
            hist_data = []
            for attr in attributes:
                if attr is not None:
                    try:
                        hist_data.append(getattr(crux_url_metrics_data, attr))
                    except AttributeError:
                        hist_data.append(None)    
                else:
                    hist_data.append(None)        

            response_metrics_vars = [
                'Largest Contentful Paint',
                #'First Input Delay',
                'Cumulative Layout Shift',
                'First Contentful Paint',
                'Interaction to Next Paint',
                'Time to First Byte'
            ]
            histograms = {}
            try:
                for hist, attribute in zip(hist_data, attributes):
                    if hist is not None:
                        hist_dict = ast.literal_eval(hist)
                        histograms[attribute] = hist_dict
                    else:
                        pass    
            except:
                histograms = {}        



            prepacked_orm = [orm for orm in orm_metrics if any(var in orm for var in response_metrics_vars)] 


            #if no data is obtained from the database retunn empty list and none for zipped_graphs
            try:
                zipped_data = combineCheckAndFilter(
                keys_from_the_response= attributes, crm_from_db=prepacked_orm,
                    the_hist_data=histograms, form_factor=selected_form_factor
                )
                zipped_graphs = prepareDataAndOutputGraphs(zipped_data)   
            except:
                zipped_data = []
                zipped_graphs = None   

            #--------------------  Obtaining the data from models which returns json results as the response ---------------------------
            json_results = CruxJsonRecord.objects.filter(
                identifier_id = 2,
                url=url_select_form,
                form_factor=selected_form_factor
            ).annotate(
                largest_contentful_paint_histogram_densities=F('cruxjsonmetrics__largest_contentful_paint_histogram_densities'),
                #first_input_delay_histogram_densities=F('cruxjsonmetrics__first_input_delay_histogram_densities'),
                cumulative_layout_shift_histogram_densities=F('cruxjsonmetrics__cumulative_layout_shift_histogram_densities'),
                first_contentful_paint_histogram_densities=F('cruxjsonmetrics__first_contentful_paint_histogram_densities'),
                interaction_to_next_paint_histogram_densities=F('cruxjsonmetrics__interaction_to_next_paint_histogram_densities'),
                experimental_time_to_first_byte_histogram_densities=F('cruxjsonmetrics__experimental_time_to_first_byte_histogram_densities'),
            ).values(
                'largest_contentful_paint_histogram_densities',
                #'first_input_delay_histogram_densities',
                'cumulative_layout_shift_histogram_densities',
                'first_contentful_paint_histogram_densities',
                'interaction_to_next_paint_histogram_densities',
                'experimental_time_to_first_byte_histogram_densities',
            ).order_by('id') 


            json_p75_results = CruxJsonRecord.objects.filter(
                identifier_id = 2,
                url=url_select_form,
                form_factor=selected_form_factor
            ).annotate(
                largest_contentful_paint_percentiles_p75s=F('cruxjsonmetrics__largest_contentful_paint_percentiles_p75s'),
                #first_input_delay_percentiles_p75s=F('cruxjsonmetrics__first_input_delay_percentiles_p75s'),
                cumulative_layout_shift_percentiles_p75s=F('cruxjsonmetrics__cumulative_layout_shift_percentiles_p75s'),
                first_contentful_paint_percentiles_p75s=F('cruxjsonmetrics__first_contentful_paint_percentiles_p75s'),
                interaction_to_next_paint_percentiles_p75s=F('cruxjsonmetrics__interaction_to_next_paint_percentiles_p75s'),
                experimental_time_to_first_byte_percentiles_p75s=F('cruxjsonmetrics__experimental_time_to_first_byte_percentiles_p75s'),
            ).values(
                'largest_contentful_paint_percentiles_p75s',
                #'first_input_delay_percentiles_p75s',
                'cumulative_layout_shift_percentiles_p75s',
                'first_contentful_paint_percentiles_p75s',
                'interaction_to_next_paint_percentiles_p75s',
                'experimental_time_to_first_byte_percentiles_p75s',
            ).order_by('id')               
            
            record_ids = json_results.values_list('id', flat=True)

            #------------------- Obtaining the data from Collection Periods Model ----------------------------------
            collection_periods_results = CruxCollectionPeriod.objects.filter(
                record_id__in = record_ids).values(
                    'record_id',
                    'first_date_json',
                    'last_date_json'
                ).order_by('id')
            
            #Mapping metrics variable names to human readable names
            metric_names_mapping = mapHistoricalTribinMetricsNames()

            #Creating dataframes dict for Tribin 
            try:
                dataframes_dict = json_to_dataframe(json_results, collection_periods_results)
            except Exception as e:
                print(f"There's an error while creating a Dataframe: {str(e)}")
                dataframes_dict = {}

            #Creating dictioneries for p75 graphs 
            try:
                p75_dataframes_dict = json_to_dataframe(json_p75_results, collection_periods_results)
            except Exception as e:
                print(f"There's an error while creating a Dataframe: {str(e)}")
                p75_dataframes_dict = {}                      

            #Populating Tribin graphs
            for hmetric_name_var, metric_name in metric_names_mapping.items():
                try:
                    
                    data = dataframes_dict.get(hmetric_name_var)
                    if data is not None:
                        
                        tribin_chart = dbvar_tribin_bar_chart(data, metric_name)

                        
                        zipped_tribin_graphs.append({'metric': metric_name, 'graph': tribin_chart})
                    else:
                        print(f"Nema podataka za metrički podatak: {metric_name}")
                except Exception as e:
                    print(f"Došlo je do greške pri obradi podataka za metrički podatak: {metric_name}. Greška: {str(e)}")


            #Populating p75 graphs     
            # Getting thresholds and sort them to make sure they are in wright order
            thresholds = threshold_by_dbmetric(json_p75_results)
           
            try:
                p75sorted_thresholds = {metric: thresholds[metric] for metric in mapHistoricalp75MetricsNames()}
            except Exception as e:
                print(e)
                p75sorted_thresholds = {} 
            for hmetric_name_var, metric_name, _, low_threshold, high_threshold in zip(
                    mapHistoricalp75MetricsNames().keys(),
                    list(mapHistoricalp75MetricsNames().values()),
                    p75sorted_thresholds.keys(),
                    *zip(*p75sorted_thresholds.values())
            ):
                try:
                    data = p75_dataframes_dict.get(hmetric_name_var)
                    low_threshold = float(low_threshold)
                    high_threshold = float(high_threshold)
                  
                    p75_graph = dbver_p75_line_graph(data, metric_name, low_threshold, high_threshold)
                    p75_zipped_graphs.append({'metric': metric_name, 'graph': p75_graph})
                except Exception as e:
                    print(f"An error occurred while processing data for metric: {metric_name}")                    
  
                                 





        else:
            url_select_form = None
            selected_form_factor = None
 
    else:
        url_form = CreateNewFormDropdownForUrls(initial={"url_form":None, "form_factor":None}, domain_id = id)            


    context = {
        "domain":domain,
        "url_data":url_data,
        "url_form":url_form,
        'zipped_data':zipped_data,
        'zipped_graphs':zipped_graphs,
        'zipped_tribin_graph':zipped_tribin_graphs,
        'p75_zipped_graph': p75_zipped_graphs,        
        
    }

    return render(request, 'psi/cruxdb-url-explore.html', context=context)

#************************************************************************* 
                                 #PERFORMANCE PART

@login_required(login_url='./login')
def performance(request, id):
    """Page speed performance report per domain"""
    domains = get_object_or_404(Domain, pk=id)
    url_data = PsiUrl.objects.filter(domain=id)  #Getting the url data
    snapshoot = url_select_form = selected_form_factor = data = None #INitializing this to avoid error


    if request.method == 'POST':
        url_form = CreateNewFormDropdownForPsiWeeklyReport(request.POST, domain_id=id)

        if url_form.is_valid():
            url_select_form = url_form.cleaned_data.get('url_form')
            selected_form_factor = url_form.cleaned_data.get('form_factor')
            url_id = url_data.filter(url=url_select_form).values_list('id', flat=True).first()
            snapshoot = Snapshoots.objects.filter(url_id=url_id).filter(strategy=selected_form_factor).order_by('id').last()#Getting the final snapshoot encoded in base64
            data = PsiMainCoreWebVitalsMetrics.objects.filter(url_id=url_id).filter(strategy=selected_form_factor).order_by('id').last()#Getting the final snapshoot encoded in base64
        else:
            url_select_form = None
            selected_form_factor = None    
    else:
          url_form = CreateNewFormDropdownForPsiWeeklyReport(initial={"url_form": None, "form_factor": None}, domain_id=id)          

    context = {
        'domain':domains,
        'snapshoot':snapshoot,
        'url_data':url_data,
        'url_form':url_form,
        'url_select_form':url_select_form,
        'selected_form_factor':selected_form_factor,
        'data':data,


    }    

    return render(request, 'psi/performance.html', context=context)


@login_required
def monthly_performance_report(request, id):
    '''This view handles the monthly performance view for all url's registered per domain'''
    domains = get_object_or_404(Domain, pk=id)
    url_data = PsiUrl.objects.filter(domain=id)  #Getting the url data
    snapshoot = performance_plots = ps_plots = url_select_form = "no data to display"
    lcp_plot = fcp_plot = tbt_plot = cls_plot = srt_plot = snl_plot = "no data to display"  #Initializing these to NOne to avoid error in initialization
    avg_performance_value = 3.056
    selected_form_factor = None
    data = None
    average_data = None
    difference_data = None
    median_data = None
    stdev_data = None
    cv_difference_data = None




    if request.method == 'POST':
        url_form = CreateNewFormDropdownForPsiWeeklyReport(request.POST, domain_id=id)

        if url_form.is_valid():
            url_select_form = url_form.cleaned_data.get('url_form')
            selected_form_factor = url_form.cleaned_data.get('form_factor')           
            url_id = url_data.filter(url=url_select_form).values_list('id', flat=True).first()
            strategy_id = int(request.POST.get('form_factor'))
            snapshoot = Snapshoots.objects.filter(url_id=url_id, strategy=strategy_id).last()

            date_range_weeks = CruxDelta.objects.filter(is_delta=1).last()

            if date_range_weeks and date_range_weeks.date:
                weeks_delta = date_range_weeks.date  
                start_date = make_aware(datetime.now() - timedelta(weeks=weeks_delta))
            else:
                start_date = make_aware(datetime(2024, 12, 1))  


            data = PsiMainCoreWebVitalsMetrics.objects.filter(
                url_id=url_id,
                strategy=selected_form_factor,
                extractionDate__gt=start_date
            ).order_by('id')
            try:
                average_data = PsiMainCoreWebVitalsAverages.objects.filter(url_id=url_id).filter(strategy_id = request.POST.get('form_factor')).order_by('-id')[0] #Fetching last average
            except:
                average_data = None 
            try:       
                difference_data = PsiMainCoreWebVitalsDifference.objects.filter(url_id=url_id).filter(strategy_id = request.POST.get('form_factor')).order_by('-id')[0]  #Fetching last difference value
            except:
                difference_data = None
            try:        
                stdev_data = PsiMainCoreWebVitalsStandardDeviation.objects.filter(url_id=url_id).filter(strategy_id = request.POST.get('form_factor')).order_by('-id')[0]  #Fetching last standard deviation
            except:
                stdev_data = None 
            try:       
                median_data = PsiMainCoreWebVitalsMediana.objects.filter(url_id=url_id).filter(strategy_id = request.POST.get('form_factor')).order_by('-id')[0] #Fetching last median value
            except:
                median_data = None   
            try:
                cv_difference_data = PsiMainCoreWebVitalsVarCoeff.objects.filter(url_id=url_id).filter(strategy_id = request.POST.get('form_factor')).order_by('-id')[0] #Fetching last cvw value
            except:
                cv_difference_data = None    


            dates, metrics  = prepareOrmDataForPerformance(query_set=data)
            performance_plots= plotPerformanceBar(dates, metrics, area_color='#4AA36C')
            ps_dates, ps_metrics = preparePageSpeedData(query_set=data)
            ps_plots = plotPerformanceBarWithRound(ps_dates, ps_metrics, rounding=True, ndigits=2, division_factor=1000, area_color='#4AA36C')
            lcp_dates, lcp_metrics = prepareLCPData(query_set=data)
            lcp_plot = plotPerformanceBarWithRound(lcp_dates, lcp_metrics,rounding=False, ndigits=None, division_factor=1000, area_color='#4AA36C')
            fcp_dates, fcp_metrics = prepareFCPData(query_set=data)
            fcp_plot = plotPerformanceBarWithRound(fcp_dates, fcp_metrics, rounding=True, ndigits=2, division_factor=1000, area_color='#4AA36C')
            tbt_dates, tbt_metrics = prepareTBTData(query_set=data)
            tbt_plot = plotPerformanceBarWithRound(tbt_dates, tbt_metrics, rounding=True, ndigits=2, area_color='#4AA36C')
            cls_dates, cls_metrics = prepareCLSData(query_set=data)
            rounded_cls_metrics = list(map(lambda x: round(x, 4), cls_metrics))            
            cls_plot = plotPerformanceBarWithRound(cls_dates, rounded_cls_metrics )
            srt_dates, srt_metrics = prepareSRTData(query_set=data)
            srt_plot = plotPerformanceBar(srt_dates, srt_metrics, area_color='#4AA36C')
            snl_dates, snl_metrics = prepareSNLData(query_set=data)
            snl_plot = plotPerformanceBar(snl_dates, snl_metrics, area_color='#4AA36C')

            try:
                column_names = getFieldsNames(model_name= PsiMainCoreWebVitalsMetrics, values = None)
            except:
                column_names = []    
 

        else:
            url_select_form = None
            selected_form_factor = None    
    else:
          url_form = CreateNewFormDropdownForPsiWeeklyReport(initial={"url_form": None, "form_factor": None}, domain_id=id)    



    context = {
        'domain':domains,
        'url_data':url_data,
        'url_form':url_form,
        'snapshoot':snapshoot,
        'performance_plots':performance_plots,
        'ps_plots':ps_plots,
        'url_select_form':url_select_form,
        'lcp_plot':lcp_plot,
        'fcp_plot':fcp_plot,
        'tbt_plot':tbt_plot,
        'cls_plot':cls_plot,
        'srt_plot':srt_plot,
        'snl_plot':snl_plot,
        'avg_performance_value':avg_performance_value,
        'selected_form_factor':selected_form_factor,
        'url_select_form':url_select_form,
        'selected_form_factor':selected_form_factor,
        'data':data,
        'average':average_data,
        'difference': difference_data,
        'median':median_data,
        'sdeviation': stdev_data,
        'sdevdiff':cv_difference_data,

    }

    return render(request, 'psi/performance-report.html', context = context)



@login_required(login_url='./login')
def lighthouse(request, id):
    """LightHouse report per domain"""
    domains = get_object_or_404(Domain, pk=id)


    # Handling the POST request for the form factor dropdown to ensure it resets the form factor to None
    # in case the user selects the default option
    if request.method == 'POST':
        url_form = CreateNewFormDropdownForUrls(request.POST, domain_id=id)
        print(url_form)

        if url_form.is_valid():
            url_from_select_form = url_form.cleaned_data.get('url_form')
            selected_form_factor = url_form.cleaned_data.get('form_factor')
    else:
        url_form = CreateNewFormDropdownForUrls(initial={"url_form": None, "form_factor": None}, domain_id=id)  
        
              
    context = {
        'domain':domains,

    }   

    print(context) 

    return render(request, 'psi/lighthouse-explore.html', context=context)

def your_search_view(request):
    search_query = request.GET.get('search', '')
    url_data = CruxFetchResultsForUrl.objects.filter(your_search_field__icontains=search_query)
    return render(request, 'psi/crux-url-explore.html', {'url_data': url_data})