'Test harness code'

from django_plotly_dash import DjangoDash
from django.utils.module_loading import import_string

from .plotly_apps import multiple_callbacks, flexible_expanded_callbacks

ValidationError = import_string('django.core.exceptions.ValidationError')

def stateless_app_loader(app_name):

    # Load a stateless app
    return import_string("psi.scaffold." + app_name)

#demo_app = DjangoDash(name="name_of_demo_app")


# stateless_app_loader("respCodeData")
# stateless_app_loader("TableBelowGraph")

respCodeData = DjangoDash("respCodeData")
TableBelowGraph = DjangoDash("TableBelowGraph")
crux_live_hor_bar = DjangoDash("crux_live_hor_bar")
