from typing import Any
from django.contrib import admin
from .models import *
#admin.site.register(Application)


class ReadOnlyAdminMixin:

    def has_add_permission(self, request):
        return True

    def has_change_permision(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True

    def has_view_permission(self, request, obj=None):
        return True

@admin.register(PsiApplication)
class ProjectAdmin(ReadOnlyAdminMixin,admin.ModelAdmin):
    list_display = ("app_name", "id", "app_description", "app_is_active", "receive_alerts")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['app_name'].disabled = True
            form.base_fields['app_description'].disabled = True
            form.base_fields['app_is_active'].disabled = True
            form.base_fields['receive_alerts'].disabled = True
        return form  

@admin.register(Domain) 
class PsiDomain(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("url", "domain", "logo", "in_use")  
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['url'].disabled = True
            form.base_fields['domain'].disabled = True
            form.base_fields['logo'].disabled = True
            form.base_fields['in_use'].disabled = True
        return form    

@admin.register(PsiPageType)
class PsiPageTypeAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ('type',)
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['type'].disabled = True

        return form     



@admin.register(PsiUrl)
class PsiUrlAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("id","url", "type", "domain", "is_active", "description")
    search_fields = ["url"]
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['url'].disabled = True
            form.base_fields['type'].disabled = True
            form.base_fields['domain'].disabled = True
            form.base_fields['is_active'].disabled = True
            form.base_fields['description'].disabled = True

        return form   

@admin.register(PsiStrategy)
class PsiSourceAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("strategy_type","strategy_inuse")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['strategy_type'].disabled = True
            form.base_fields['strategy_inuse'].disabled = True

        return form   
    

@admin.register(PsiOption)
class PsiOptionAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("option_type",)
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['option_type'].disabled = True

        return form       
    
@admin.register(PsiConnectionType)
class PsiConnectionTypeAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("connection",)
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['connection'].disabled = True

        return form     

@admin.register(PsiEndPoint)
class PsiEndPointAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("end_point","ep_description", "ep_api_name", "ep_is_deprecated")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['end_point'].disabled = True
            form.base_fields['ep_description'].disabled = True
            form.base_fields['ep_api_name'].disabled = True
            form.base_fields['ep_is_deprecated'].disabled = True

        return form        
    
@admin.register(PsiApiKey)
class PsiApiKeyAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("api_key","is_valid")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['api_key'].disabled = True
            form.base_fields['is_valid'].disabled = True

        return form  
    
@admin.register(PsiCruxMetric)
class PsiCruxMetricAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("metric", "data_type", "unit", "in_use", "good_values", "poor", "unit", "abbreviation")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['metric'].disabled = True
            form.base_fields['in_use'].disabled = True
            form.base_fields['data_type'].disabled = True
            form.base_fields['unit'].disabled = True
            form.base_fields['good_values'].disabled = True 
            form.base_fields['need_improvement'].disabled = True 
            form.base_fields['poor'].disabled = True 
            form.base_fields['unit'].disabled = True    
            form.base_fields['abbreviation'].disabled = True                       

        return form   

@admin.register(PsiMetricWeight)
class PsiMetricWeightAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("metric", "weight")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['metric'].disabled = True
            form.base_fields['weight'].disabled = True

        return form          



@admin.register(PsiMetrics)
class PsiCruxMetricAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("metric", "data_type", "unit", "in_use", "knowledge", "parent")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['metric'].disabled = True
            form.base_fields['data_type'].disabled = True
            form.base_fields['unit'].disabled = True
            form.base_fields['in_use'].disabled = True                   
            form.base_fields['knowledge'].disabled = True       
            form.base_fields['parent'].disabled = True               

        return form   
    
    

@admin.register(PsiMainNodes)
class PsiMainNodesAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("main_node", "have_children")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['main_node'].disabled = True
            form.base_fields['have_children'].disabled = True
                  
        return form       
    
@admin.register(PsiCategories)
class PsiCategoriesAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("category", "description")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['category'].disabled = True
            form.base_fields['description'].disabled = True
                  
        return form        
    



@admin.register(PsiUserAgents)
class PsiUserAgentsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("user_agent", "in_use")
    search_fields = ("user_agent",)
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['user_agent'].disabled = True
            form.base_fields['in_use'].disabled = True
                  
        return form 
    
@admin.register(CruxFetchResultsForUrl)
class CruxFetchResultsForUrlAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("id", "form_factor","effective_conn_type", "url","domain_id","url_id", "extractionDate")
    list_display_links = ['url']
    search_fields = ("id", "form_factor","effective_conn_type", "url","domain_id","url_id", "extractionDate")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['form_factor'].disabled = True
            form.base_fields['effective_conn_type'].disabled = True
            form.base_fields['url'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['url_id'].disabled = True
            form.base_fields['percentiles'].disabled = True            
            form.base_fields['extractionDate'].disabled = True
                  
        return form     
    

@admin.register(CruxFetchResultsForOrigin)
class CruxFetchResultsForOriginAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("id", "form_factor","effective_conn_type", "origin","domain_id", "extractionDate")
    search_fields = ("id", "form_factor","effective_conn_type", "origin","domain_id", "extractionDate")
    list_display_links = ['origin']
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['form_factor'].disabled = True
            form.base_fields['effective_conn_type'].disabled = True
            form.base_fields['origin'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['percentiles'].disabled = True              
            form.base_fields['extractionDate'].disabled = True
                  
        return form        


@admin.register(PsiMainCoreWebVitalsMetrics)
class PsiMainCoreWebVitalsMetricsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("id", "url_id", "fetch_succesful", "speed_index","first_contentful_paint","largest_contentful_paint", "total_blocking_time", "cumulative_layout_shift", "render_blocking_resources", "server_response_time", "server_network_latency", "performance_score", "extractionDate")
    search_fields = ("id", "extractionDate")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['url_id'].disabled = True
            form.base_fields['fetch_succesful'].disabled = True
            form.base_fields['speed_index'].disabled = True
            form.base_fields['first_contentful_paint'].disabled = True
            form.base_fields['largest_contentful_paint'].disabled = True
            form.base_fields['total_blocking_time'].disabled = True
            form.base_fields['cumulative_layout_shift'].disabled = True
            form.base_fields['render_blocking_resources'].disabled = True
            form.base_fields['server_response_time'].disabled = True
            form.base_fields['server_network_latency'].disabled = True
            form.base_fields['performance_score'].disabled = True
            form.base_fields['extractionDate'].disabled = True
                  
        return form
    
@admin.register(Snapshoots)
class SnapshootsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("id", "url_id", "extractionDate") 
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj=None, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['url_id'].disabled = True
            form.base_fields['extractionDate'].disabled = True

        return form    




@admin.register(PsiMainCoreWebVitalsAverages)
class PsiMainCoreWebVitalsAveragesAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("id", "domain_id", "strategy_id", "speed_index_average","first_contenful_paint_average","largest_contentful_paint_average", "total_blocking_time_average", "cumulative_layout_shift_average", "render_blocking_resources_average", "server_response_time_average", "server_network_latency_average", "performance_score_average", "averagingDate")
    search_fields = ("id","averagingDate")
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['speed_index_average'].disabled = True
            form.base_fields['first_contenful_paint_average'].disabled = True
            form.base_fields['largest_contentful_paint_average'].disabled = True
            form.base_fields['total_blocking_time_average'].disabled = True
            form.base_fields['cumulative_layout_shift_average'].disabled = True
            form.base_fields['render_blocking_resources_average'].disabled = True
            form.base_fields['server_response_time_average'].disabled = True
            form.base_fields['server_network_latency_average'].disabled = True
            form.base_fields['server_network_latency'].disabled = True
            form.base_fields['performance_score_average'].disabled = True
            form.base_fields['averagingDate'].disabled = True
                  
        return form        


@admin.register(PsiMainCoreWebVitalsStandardDeviation)
class PsiMainCoreWebVitalsStandardDeviationAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "id",
        "domain_id",
        "strategy_id",
        "speed_index_std",
        "first_contenful_paint_std",
        "largest_contentful_paint_std",
        "total_blocking_time_std",
        "cumulative_layout_shift_std",
        "render_blocking_resources_std",
        "server_response_time_std",
        "server_network_latency_std",
        "performance_score_std",
        "stdingDate"
    )  
    search_fields = ("id", "stdinfDate")

    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['speed_index_std'].disabled = True
            form.base_fields['first_contenful_paint_std'].disabled = True
            form.base_fields['largest_contentful_paint_std'].disabled = True
            form.base_fields['total_blocking_time_std'].disabled = True
            form.base_fields['cumulative_layout_shift_std'].disabled = True
            form.base_fields['render_blocking_resources_std'].disabled = True
            form.base_fields['server_response_time_std'].disabled = True
            form.base_fields['server_network_latency_std'].disabled = True
            form.base_fields['server_network_latency_std'].disabled = True
            form.base_fields['performance_score_std'].disabled = True
            form.base_fields['stdingDate'].disabled = True
                  
        return form 
    

@admin.register(PsiMainCoreWebVitalsDifference)
class PsiMainCoreWebVitalsDifferenceAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "id",
        "domain_id",
        "strategy_id",
        "speed_index_diff",
        "first_contenful_paint_diff",
        "largest_contentful_paint_diff",
        "total_blocking_time_diff",
        "cumulative_layout_shift_diff",
        "render_blocking_resources_diff",
        "server_response_time_diff",
        "server_network_latency_diff",
        "performance_score_diff",
        "diffDate"
    )  
    search_fields = ("id", "diffDate")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['speed_index_diff'].disabled = True
            form.base_fields['first_contenful_paint_diff'].disabled = True
            form.base_fields['largest_contentful_paint_diff'].disabled = True
            form.base_fields['total_blocking_time_diff'].disabled = True
            form.base_fields['cumulative_layout_shift_diff'].disabled = True
            form.base_fields['render_blocking_resources_diff'].disabled = True
            form.base_fields['server_response_time_diff'].disabled = True
            form.base_fields['server_network_latency_diff'].disabled = True
            form.base_fields['server_network_latency_diff'].disabled = True
            form.base_fields['performance_score_diff'].disabled = True
            form.base_fields['diffDate'].disabled = True
                  
        return form     

@admin.register(PsiMainCoreWebVitalsMediana)
class PsiMainCoreWebVitalsMedianaAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "id",
        "domain_id",
        "strategy_id",
        "speed_index_med",
        "first_contenful_paint_med",
        "largest_contentful_paint_med",
        "total_blocking_time_med",
        "cumulative_layout_shift_med",
        "render_blocking_resources_med",
        "server_response_time_med",
        "server_network_latency_med",
        "performance_score_med",
        "medDate"
    )     

    search_fields = ("id","medDate")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['speed_index_med'].disabled = True
            form.base_fields['first_contenful_paint_med'].disabled = True
            form.base_fields['largest_contentful_paint_med'].disabled = True
            form.base_fields['total_blocking_time_med'].disabled = True
            form.base_fields['cumulative_layout_shift_med'].disabled = True
            form.base_fields['render_blocking_resources_med'].disabled = True
            form.base_fields['server_response_time_med'].disabled = True
            form.base_fields['server_network_latency_med'].disabled = True
            form.base_fields['server_network_latency_med'].disabled = True
            form.base_fields['performance_score_med'].disabled = True
            form.base_fields['medDate'].disabled = True
                  
        return form 
    
@admin.register(PsiMainCoreWebVitalsVarCoeff)
class PsiMainCoreWebVitalsVarCoeffAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "id",
        "domain_id",
        "strategy_id",
        "speed_index_coef",
        "first_contentful_paint_coef",
        "largest_contentful_paint_coef",
        "total_blocking_time_coef",
        "cumulative_layout_shift_coef",
        "render_blocking_resources_coef",
        "server_response_time_coef",
        "server_network_latency_coef",
        "performance_score_coef",
        "cfvDate"
    )     

    search_fields = ("id", "cvDate")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['id'].disabled = True
            form.base_fields['domain_id'].disabled = True
            form.base_fields['strategy_id'].disabled = True
            form.base_fields['speed_index_coef'].disabled = True
            form.base_fields['first_contenful_paint_coef'].disabled = True
            form.base_fields['largest_contentful_paint_coef'].disabled = True
            form.base_fields['total_blocking_time_coef'].disabled = True
            form.base_fields['cumulative_layout_shift_coef'].disabled = True
            form.base_fields['render_blocking_resources_coef'].disabled = True
            form.base_fields['server_response_time_coef'].disabled = True
            form.base_fields['server_network_latency_coef'].disabled = True
            form.base_fields['performance_score_coef'].disabled = True
            form.base_fields['cvfDate'].disabled = True
                  
        return form     
    


@admin.register(CruxFormFactor)
class CruxFetchResultsForUrlAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("formFactor",)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['formFactor'].disabled = True

                  
        return form       
    


   

@admin.register(CruxIndentifier)
class CruxFetchResultsForUrlAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("identifier", "ide_desc")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['identifier'].disabled = True
            form.base_fields['ide_desc'].disabled = True

                  
        return form       
    


@admin.register(CruxHeaders)
class CruxFetchResultsForUrlAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("header", "header_desc")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['header'].disabled = True
            form.base_fields['header_desc'].disabled = True

                  
        return form    
    



@admin.register(CruxNodeLevels)
class CruxNodeLevelsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("node", "node_order", "predecesor", "description")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser


        if not is_superuser:
            form.base_fields['node'].disabled = True
            form.base_fields['node_order'].disabled = True
            form.base_fields['predecesor'].disabled = True   
            form.base_fields['description'].disabled = True      

 
        return form    
    

@admin.register(PsiFormFactor)
class PsiFormFactorAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("psi_form_factor", "psi_form_factor_name", "in_use")    

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['psi_form_factor'].disabled = True
            form.base_fields['psi_form_factor_name'].disabled = True
            form.base_fields['in_use'].disabled = True

        return form
    
#Register url level
@admin.register(CruxJsonRecord)
class CruxJsonRecordAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("form_factor", "url", "identifier")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['form_factor'].disabled = True
            form.base_fields['url'].disabled = True
            form.base_fields['identifier'].disabled = True            

        return form

@admin.register(CruxJsonMetrics)
class CruxJsonMetricsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "cumulative_layout_shift_histogram_densities",
        "cumulative_layout_shift_percentiles_p75s",
        "experimental_time_to_first_byte_histogram_densities",
        "experimental_time_to_first_byte_percentiles_p75s",
        "first_contentful_paint_histogram_densities",
        "first_contentful_paint_percentiles_p75s",
        "first_input_delay_histogram_densities",
        "first_input_delay_percentiles_p75s",
        "interaction_to_next_paint_histogram_densities",
        "interaction_to_next_paint_percentiles_p75s",
        "largest_contentful_paint_histogram_densities",
        "largest_contentful_paint_percentiles_p75s",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['cumulative_layout_shift_histogram_densities'].disabled = True
            form.base_fields['cumulative_layout_shift_percentiles_p75s'].disabled = True
            form.base_fields['experimental_time_to_first_byte_histogram_densities'].disabled = True
            form.base_fields['experimental_time_to_first_byte_percentiles_p75s'].disabled = True
            form.base_fields['first_contentful_paint_histogram_densities'].disabled = True
            form.base_fields['first_contentful_paint_percentiles_p75s'].disabled = True
            form.base_fields['first_input_delay_histogram_densities'].disabled = True
            form.base_fields['first_input_delay_percentiles_p75s'].disabled = True
            form.base_fields['interaction_to_next_paint_histogram_densities'].disabled = True
            form.base_fields['interaction_to_next_paint_percentiles_p75s'].disabled = True
            form.base_fields['largest_contentful_paint_histogram_densities'].disabled = True
            form.base_fields['largest_contentful_paint_percentiles_p75s'].disabled = True

        return form

@admin.register(CruxCollectionPeriod)
class CruxCollectionPeriodAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("first_date_json", "last_date_json")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['first_date_json'].disabled = True
            form.base_fields['first_date_json'].disabled = True

        return form

#register Origin level

@admin.register(CruxJsonOriginRecord)
class CruxJsonOriginRecordAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("form_factor", "url", "identifier")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['form_factor'].disabled = True
            form.base_fields['url'].disabled = True
            form.base_fields['identifier'].disabled = True            

        return form

@admin.register(CruxJsonOriginMetrics)
class CruxJsonOriginMetricsAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = (
        "cumulative_layout_shift_histogram_densities",
        "cumulative_layout_shift_percentiles_p75s",
        "experimental_time_to_first_byte_histogram_densities",
        "experimental_time_to_first_byte_percentiles_p75s",
        "first_contentful_paint_histogram_densities",
        "first_contentful_paint_percentiles_p75s",
        "first_input_delay_histogram_densities",
        "first_input_delay_percentiles_p75s",
        "interaction_to_next_paint_histogram_densities",
        "interaction_to_next_paint_percentiles_p75s",
        "largest_contentful_paint_histogram_densities",
        "largest_contentful_paint_percentiles_p75s",
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['cumulative_layout_shift_histogram_densities'].disabled = True
            form.base_fields['cumulative_layout_shift_percentiles_p75s'].disabled = True
            form.base_fields['experimental_time_to_first_byte_histogram_densities'].disabled = True
            form.base_fields['experimental_time_to_first_byte_percentiles_p75s'].disabled = True
            form.base_fields['first_contentful_paint_histogram_densities'].disabled = True
            form.base_fields['first_contentful_paint_percentiles_p75s'].disabled = True
            form.base_fields['first_input_delay_histogram_densities'].disabled = True
            form.base_fields['first_input_delay_percentiles_p75s'].disabled = True
            form.base_fields['interaction_to_next_paint_histogram_densities'].disabled = True
            form.base_fields['interaction_to_next_paint_percentiles_p75s'].disabled = True
            form.base_fields['largest_contentful_paint_histogram_densities'].disabled = True
            form.base_fields['largest_contentful_paint_percentiles_p75s'].disabled = True

        return form

@admin.register(CruxCollectionOriginPeriod)
class CruxCollectionOriginPeriodAdmin(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("first_date_json", "last_date_json")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['first_date_json'].disabled = True
            form.base_fields['first_date_json'].disabled = True

        return form

@admin.register(CruxDelta)
class CruxDelta(ReadOnlyAdminMixin, admin.ModelAdmin):
    list_display = ("date", "is_delta")

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        is_superuser = request.user.is_superuser

        if not is_superuser:
            form.base_fields['date'].disabled = True
            form.base_fields['is_delta'].disabled = True

        return form