from django.core.management import BaseCommand
from django.utils import timezone
from psi.models import PsiApiKey, PsiEndPoint, PsiUrl, CruxFormFactor, PsiUserAgents, PsiMainCoreWebVitalsMetrics, PsiFormFactor, Snapshoots
from .psicrux.utils import fetch_random_user_agent, extract_page_speed_metrics, fetch_format
from .psicrux.clutilities import APIClient, APIURLBuilder
import json


currentDate = timezone.now()
print(currentDate)

class Command(BaseCommand):
     help = "Fetching the data using Crux API and populates the  PS database"

     def handle(self, *args, **options):
        '''Fetches the Google page speed data and insert it into database'''

        try:
            Snapshoots.objects.all().delete()
            print("Snapshoot table cleaned...")
        except Exception as e:
            print(e)        
        
        #Retrieving valid API key
        valid_api_key = PsiApiKey.objects.filter(is_valid=True).first()


        #Retrieving PSI ednoint
        psi_endpoint = PsiEndPoint.objects.filter(ep_api_name='PageSpeed').filter(ep_is_deprecated=False).first()

        #Creating API builder
        psi_url_builder = APIURLBuilder(base_url=psi_endpoint, api_key=valid_api_key)

        #Defining PSI API client by passing the psi_url builder instance
        psi_api_client = APIClient(url_builder=psi_url_builder)

        #Defining number of retries (3)
        max_retries = 3

        # Fetching url and domain ids from the database

        valid_url_data = PsiUrl.objects.all().filter(is_active=True).values_list()

        #Returning a tuple containg the URL itself and url id from the database
        psi_urls_ids = zip(fetch_format(valid_url_data, 0), fetch_format(valid_url_data, 1))


        #Fetching Form Factors (Desktop or phone) available in Django ORM

        valid_form_factor_qs = PsiFormFactor.objects.filter(in_use=True).values_list()
        form_factor_names = list(map(lambda x: x[1], valid_form_factor_qs))


        #Randomly select User-Agent that is going to be used in request header

        user_agent_string_list  = PsiUserAgents.objects.filter(in_use=True).values_list()
        random_user_agent = fetch_random_user_agent(user_agent_list=user_agent_string_list)[1]

        

        # Controll  iteratatiuon through each data point and make requests with a randomly selected user agent
        for page_id, url in psi_urls_ids:
            #Setting default fetch successfull to False
            for factor_name in valid_form_factor_qs:
                #Trying to make a resquest
                try:
                    response, status_code = psi_api_client.fetch_psi_data(url=url, user_agent=random_user_agent, strategy=factor_name[1], max_retries=max_retries, timeout=70, sleep=0.5)

                    fetch_successful = (status_code == 200)
                    
           
                    #If everything is OK
                    if status_code == 200:
                        (speed_index, 
                        first_contentful_paint, 
                        largest_contentful_paint, 
                        total_blocking_time, 
                        cumulative_layout_shift, 
                        render_blocking_resources, 
                        server_response_time, 
                        server_network_latency, 
                        performance_score, 
                        final_snapshoot) = extract_page_speed_metrics(response)   #Let this return dictionery


                        psi_isnstances = PsiMainCoreWebVitalsMetrics(

                            url_id = page_id,
                            speed_index = speed_index,
                            first_contentful_paint = first_contentful_paint,
                            largest_contentful_paint = largest_contentful_paint,
                            total_blocking_time = total_blocking_time,
                            cumulative_layout_shift = cumulative_layout_shift,
                            render_blocking_resources = render_blocking_resources,
                            server_response_time = server_response_time,
                            server_network_latency = server_network_latency,
                            performance_score = performance_score,
                            extractionDate = currentDate,
                            strategy = factor_name[1],
                            final_snapshoot = 0,
                            fetch_succesful =  fetch_successful
                        ) 
                        psi_isnstances.save()  

                        final_snapshoot  = extract_page_speed_metrics(response)[9]


                        #Saving snapshot instance  (Here we need only one snapshot, so we cleaned the table the table before new snapshot are added)

                        snapshoot_instance = Snapshoots(
                            url_id = page_id,
                            final_snapshoot = final_snapshoot,
                            strategy = factor_name[0],
                            extractionDate = currentDate
                        )

                        snapshoot_instance.save()

                    else:
                        print("Not valid response, inserting failed in the database")
                        fetch_successful = False
                        psi_isnstances = PsiMainCoreWebVitalsMetrics(

                            url_id = page_id,
                            speed_index = 0,
                            first_contentful_paint = 0,
                            largest_contentful_paint = 0,
                            total_blocking_time = 0,
                            cumulative_layout_shift = 0,
                            render_blocking_resources = 0,
                            server_response_time = 0,
                            server_network_latency = 0,
                            performance_score = 0,
                            extractionDate = currentDate,
                            strategy = factor_name[1],
                            final_snapshoot = 0,
                            fetch_succesful =  fetch_successful                       
                        )                     
                        psi_isnstances.save()


                except Exception as e:
                    print(f"Exception occured: {e}")    
                    fetch_successful = False        


                


        return None