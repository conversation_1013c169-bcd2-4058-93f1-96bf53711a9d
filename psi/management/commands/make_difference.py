from django.core.management.base import BaseCommand
from psi.models import PsiMainCoreWebVitalsMetrics, PsiUrl, PsiMainCoreWebVitalsDifference
from django.utils import timezone




current_date = timezone.now()

class Command(BaseCommand):
    help = "Calculates the Averages for the CWV metrics"

    # Function to handle differences
    def calculate_differences(self, current_vals, last_added_vals):
        '''Calculate the differences'''
        differences = {}

        try:
            for metric in current_vals:
                if metric in last_added_vals:
                    differences[metric] = last_added_vals[metric] - current_vals[metric] 
                else:
                    differences[metric] = None
            return differences   
        except Exception as e:
            print(e)

       

    def handle(self, *args, **options):
        '''Calculates the averages'''
        print("Updating the difference table")

        # Filtering url object that have active=True
        url_ids= PsiUrl.objects.filter(is_active=True)

        # Mapping the strategy string values to numeric values
        strategy_mapping = {'mobile':1, 'desktop':2}

        for strategy in ['mobile', 'desktop']:
            for url in url_ids:
                # Filter CWV metrics for the current URL and strategy
                cwv_metrics = PsiMainCoreWebVitalsMetrics.objects.filter(url_id=url.id, strategy=strategy).order_by('extractionDate')


                # If we have only one entry in the database, we can't compute the avg and standard dev nor difference as well
                if len(cwv_metrics) <= 1:
                    continue
              
                # Accessing domain_id directly from the url object
                domain_id = url.domain_id
              
                # Retrieve metrics from the previous week and current week to calculate differences 

                previous_week_metrics = cwv_metrics.order_by('-extractionDate')[:2]

                # Storing previous metrics in a diuctionery
                previous_metrics = {

                    'speed_index_diff': previous_week_metrics[0].speed_index,
                    'first_contenful_paint_diff': previous_week_metrics[0].first_contentful_paint,
                    'largest_contentful_paint_diff': previous_week_metrics[0].largest_contentful_paint,
                    'total_blocking_time_diff': previous_week_metrics[0].total_blocking_time,
                    'cumulative_layout_shift_diff': previous_week_metrics[0].cumulative_layout_shift,
                    'render_blocking_resources_diff': previous_week_metrics[0].render_blocking_resources,
                    'server_response_time_diff': previous_week_metrics[0].server_response_time,
                    'server_network_latency_diff': previous_week_metrics[0].server_network_latency,
                    'performance_score_diff': previous_week_metrics[0].performance_score


                }
                # Storing current metrics in a dictionery
                current_metrics = {
            
                    'speed_index_diff': previous_week_metrics[1].speed_index,
                    'first_contenful_paint_diff': previous_week_metrics[1].first_contentful_paint,
                    'largest_contentful_paint_diff': previous_week_metrics[1].largest_contentful_paint,
                    'total_blocking_time_diff': previous_week_metrics[1].total_blocking_time,
                    'cumulative_layout_shift_diff': previous_week_metrics[1].cumulative_layout_shift,
                    'render_blocking_resources_diff': previous_week_metrics[1].render_blocking_resources,
                    'server_response_time_diff': previous_week_metrics[1].server_response_time,
                    'server_network_latency_diff': previous_week_metrics[1].server_network_latency,
                    'performance_score_diff': previous_week_metrics[1].performance_score
                }

                difference_values = self.calculate_differences(current_metrics, previous_metrics)



                differences_model_instances = PsiMainCoreWebVitalsDifference(
                    speed_index_diff=difference_values['speed_index_diff'],
                    first_contenful_paint_diff=difference_values['first_contenful_paint_diff'],
                    largest_contentful_paint_diff=difference_values['largest_contentful_paint_diff'],
                    total_blocking_time_diff=difference_values['total_blocking_time_diff'],
                    cumulative_layout_shift_diff=difference_values['cumulative_layout_shift_diff'],
                    render_blocking_resources_diff=difference_values['render_blocking_resources_diff'],
                    server_response_time_diff=difference_values['server_response_time_diff'],
                    server_network_latency_diff=difference_values['server_network_latency_diff'],
                    performance_score_diff=difference_values['performance_score_diff'],
                    diffDate=current_date,
                    domain_id_id=domain_id,
                    strategy_id_id=strategy_mapping.get(strategy, None),  # Maping strategy to numeric value
                    url_id=url.id,                    
                )

                # Save the core web vitals metrics difference in the database
                differences_model_instances.save()








                
                

