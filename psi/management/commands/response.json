{"record": {"key": {"formFactor": "PHONE", "url": "https://www.goal.pl/"}, "metrics": {"navigation_types": {"fractions": {"back_forward_cache": 0.2053, "prerender": 0.0435, "navigate": 0.4508, "navigate_cache": 0.0691, "reload": 0.0477, "restore": 0, "back_forward": 0.1835}}, "cumulative_layout_shift": {"histogram": [{"start": "0.00", "end": "0.10", "density": 0.9305}, {"start": "0.10", "end": "0.25", "density": 0.0467}, {"start": "0.25", "density": 0.0228}], "percentiles": {"p75": "0.06"}}, "experimental_time_to_first_byte": {"histogram": [{"start": 0, "end": 800, "density": 0.8702}, {"start": 800, "end": 1800, "density": 0.0826}, {"start": 1800, "density": 0.0473}], "percentiles": {"p75": 420}}, "first_contentful_paint": {"histogram": [{"start": 0, "end": 1800, "density": 0.8969}, {"start": 1800, "end": 3000, "density": 0.0666}, {"start": 3000, "density": 0.0365}], "percentiles": {"p75": 1053}}, "first_input_delay": {"histogram": [{"start": 0, "end": 100, "density": 0.9628}, {"start": 100, "end": 300, "density": 0.0285}, {"start": 300, "density": 0.0087}], "percentiles": {"p75": 16}}, "interaction_to_next_paint": {"histogram": [{"start": 0, "end": 200, "density": 0.8933}, {"start": 200, "end": 500, "density": 0.0841}, {"start": 500, "density": 0.0226}], "percentiles": {"p75": 131}}, "largest_contentful_paint": {"histogram": [{"start": 0, "end": 2500, "density": 0.9301}, {"start": 2500, "end": 4000, "density": 0.0418}, {"start": 4000, "density": 0.0281}], "percentiles": {"p75": 1187}}}, "collectionPeriod": {"firstDate": {"year": 2024, "month": 5, "day": 16}, "lastDate": {"year": 2024, "month": 6, "day": 12}}}}