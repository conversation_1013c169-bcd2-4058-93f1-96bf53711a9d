from django.core.management.base import BaseCommand
from django.utils import timezone
from psi.models import CruxJsonRecord, CruxJsonMetrics, CruxCollectionPeriod, PsiApiKey, PsiUrl, CruxFormFactor, CruxHeaders, \
      PsiUserAgents, PsiEndPoint, CruxIndentifier, CruxFetchResultsForUrl, CruxFetchResultsForOrigin, Domain, CruxJsonOriginMetrics, CruxCollectionOriginPeriod, CruxJsonOriginRecord
from .psicrux.utils import fetch_random_user_agent, extract_crux_metrics
from psi.psi_utilities.utilities import return_history_response_for_url, return_history_response
import json
from .psicrux.clutilities import APIClient, APIURLBuilder
import os
import time
from datetime import date

class Command(BaseCommand):
    help = "Populating the crux database in bulk one time"


    def handle(self, *args, **options):
        """Fetching the data using the API one time"""
        
        #Connecting the Django ORM and retrieving the Crux API key
        valid_api_key = PsiApiKey.objects.filter(is_valid=True).first()

        #Contacting django ORM and retrieving the list of valid form faactors
        form_factors = CruxFormFactor.objects.filter(in_use=True).values_list()
        # Contacting Django ORM and retrieving a list of User-Agents (one has to be choosen randomly)
        user_agent_list = PsiUserAgents.objects.filter(in_use= True).values_list()
        random_user_agent = fetch_random_user_agent(user_agent_list=user_agent_list)[1] #First one belongs to the historical Crux API

        #Contacting Django ORM and retrieving a list of valid URL's that is going to be used in API call
        valid_url_list = PsiUrl.objects.filter(is_active=True).values_list()

        # Fetching Django orm Domain model to get a list of all domains in use
        valid_domain_list = Domain.objects.filter(in_use=True).values_list()

        #Contacting Django ORM and retrieving defined headers type dictionery
        headers_query = CruxHeaders.objects.all().values_list()[0]
        header_str = headers_query[1]
        header_dict=json.loads(header_str)

        #Update header_dict with random User Agent
        header_dict['User-agents'] = random_user_agent  

        #Fetching Crux Identifier from Django ORM
        identifier_qs = CruxIndentifier.objects.all()                   

        #Fetching ednpoint from the Dhjango ORM
        crux_end_point = PsiEndPoint.objects.filter(ep_api_name='CrUX History API').filter(ep_is_deprecated=False)[0]


        #Create API builder Object
        crux_api_builder = APIURLBuilder(base_url=crux_end_point, api_key=valid_api_key)        

        #Creating Crux Api Client
        crux_api_client = APIClient(url_builder=crux_api_builder, expected_status_code=[200, 404, 400])        
       

        #Clearing the dtabase before making update
        print("Clearing the database")
        CruxJsonOriginRecord.objects.all().delete()
        CruxJsonOriginMetrics.objects.all().delete()
        CruxCollectionOriginPeriod.objects.all().delete()
        CruxJsonRecord.objects.all().delete()
        CruxJsonMetrics.objects.all().delete()
        CruxCollectionPeriod.objects.all().delete()


        # Fetching url data and storing the data in the CruxFetchResultsForUrl  and  -  CruxFetchResultsForOrigin depending on the identifier

        for identificator in identifier_qs:
            for factor in form_factors:

                if identificator.identifier == "origin":

                    for domain in valid_domain_list:
                        try:
                            history_response = return_history_response(
                                endpoint=str(crux_end_point),
                                api_key=valid_api_key,
                                headers=header_dict,
                                formFactor=factor[2],
                                origin=domain[1],
                            )
                            try:
                                self.parse_response_and_save(history_response, factor[2], identificator)  
                                time.sleep(2) 
                            except Exception as e:
                                 print(e)                                                     
                        except Exception as e:
                                    print(e)
                                    print("The response returned empty list")
                                    history_response = None   


                                    
                elif identificator.identifier == "url":

                    for url in valid_url_list:
                        try:

                            history_url_response = return_history_response_for_url(
                                endpoint=str(crux_end_point),
                                api_key=valid_api_key,
                                headers=header_dict,
                                formFactor=factor[2],
                                url = url[1]
                            )    
                            self.parse_response_and_save(history_url_response, factor[2], identificator)  
                            time.sleep(2)               
                        except Exception as e:
                            print(e)
                            print("The response returned empty list")                            
                            history_url_response = None   





    def parse_response_and_save(self, response, form_factor, identificator):
        '''Parses the JSON obtained from the API and saves the data in the database''' 
        if response:
            try:
                record_node = response.get("record")
                if record_node:
                    try:
                        key_node = record_node.get("key")

                        if "origin" in key_node:
                            print("Processing the origin Record data")
                            origin_crux_record = CruxJsonOriginRecord.objects.create(
                                form_factor=form_factor,
                                url=key_node.get("origin"),
                                identifier=identificator
                            )
                            # Process the metrics level data
                            metrics_data = record_node.get("metrics", {})

                            CruxJsonOriginMetrics.objects.create(
                                record_id=origin_crux_record,
                                cumulative_layout_shift_histogram_densities=metrics_data.get("cumulative_layout_shift", {}),
                                cumulative_layout_shift_percentiles_p75s=metrics_data.get("cumulative_layout_shift", {}),
                                experimental_time_to_first_byte_histogram_densities=metrics_data.get("experimental_time_to_first_byte", {}),
                                experimental_time_to_first_byte_percentiles_p75s=metrics_data.get("experimental_time_to_first_byte", {}),
                                first_contentful_paint_histogram_densities=metrics_data.get("first_contentful_paint", {}),
                                first_contentful_paint_percentiles_p75s=metrics_data.get("first_contentful_paint", {}),
                                first_input_delay_histogram_densities=metrics_data.get("first_input_delay", {}),
                                first_input_delay_percentiles_p75s=metrics_data.get("first_input_delay", {}),
                                interaction_to_next_paint_histogram_densities=metrics_data.get("interaction_to_next_paint", {}),
                                interaction_to_next_paint_percentiles_p75s=metrics_data.get("interaction_to_next_paint", {}),
                                largest_contentful_paint_histogram_densities=metrics_data.get("largest_contentful_paint", {}),
                                largest_contentful_paint_percentiles_p75s=metrics_data.get("largest_contentful_paint", {}),                                  
                            ) 
                            
                            print("Processing the Collection Periods")
                            origin_collectionPeriods_data = record_node.get('collectionPeriods', [])
                            for period_data in origin_collectionPeriods_data:
                                CruxCollectionOriginPeriod.objects.create(
                                    record_id=origin_crux_record,
                                    first_date_json=period_data.get("firstDate", {}),
                                    last_date_json=period_data.get("lastDate", {})
                                ) 

                        elif "url" in key_node:
                            print("Processing the url Record data")
                            url_crux_record = CruxJsonRecord.objects.create(
                                form_factor=form_factor,
                                url=key_node.get("url"),
                                identifier=identificator
                            )  
                            # Process the metrics URL level data   
                            url_metrics_data = record_node.get("metrics", {})

                            CruxJsonMetrics.objects.create(
                                record_id=url_crux_record,
                                cumulative_layout_shift_histogram_densities=url_metrics_data.get("cumulative_layout_shift", {}),
                                cumulative_layout_shift_percentiles_p75s=url_metrics_data.get("cumulative_layout_shift", {}),
                                experimental_time_to_first_byte_histogram_densities=url_metrics_data.get("experimental_time_to_first_byte", {}),
                                experimental_time_to_first_byte_percentiles_p75s=url_metrics_data.get("experimental_time_to_first_byte", {}),
                                first_contentful_paint_histogram_densities=url_metrics_data.get("first_contentful_paint", {}),
                                first_contentful_paint_percentiles_p75s=url_metrics_data.get("first_contentful_paint", {}),
                                first_input_delay_histogram_densities=url_metrics_data.get("first_input_delay", {}),
                                first_input_delay_percentiles_p75s=url_metrics_data.get("first_input_delay", {}),
                                interaction_to_next_paint_histogram_densities=url_metrics_data.get("interaction_to_next_paint", {}),
                                interaction_to_next_paint_percentiles_p75s=url_metrics_data.get("interaction_to_next_paint", {}),
                                largest_contentful_paint_histogram_densities=url_metrics_data.get("largest_contentful_paint", {}),
                                largest_contentful_paint_percentiles_p75s=url_metrics_data.get("largest_contentful_paint", {}),                                  
                            )      

                            print("Processing the Collection Periods")
                            url_collectionPeriods_data = record_node.get("collectionPeriods", [])
                            for period_data in url_collectionPeriods_data:
                                CruxCollectionPeriod.objects.create(
                                    record_id=url_crux_record,
                                    first_date_json=period_data.get("firstDate", {}),
                                    last_date_json=period_data.get("lastDate", {})
                                )                       
                    except KeyError as e:
                        print(e)
            except KeyError as e:
                print(e)    
    
         