from django.core.management.base import BaseCommand
from psi.models import PsiMainCoreWebVitalsMetrics, PsiUrl,PsiMainCoreWebVitalsMediana
from django.utils import timezone
from statistics import median




current_date = timezone.now()

class Command(BaseCommand):
    help = "Calculating median values for the CWV metrics"

    def calculate_median(self, metrics):
        values = {
            'speed_index_med': [],
            'first_contenful_paint_med': [],
            'largest_contentful_paint_med': [],
            'total_blocking_time_med': [],
            'cumulative_layout_shift_med': [],
            'render_blocking_resources_med': [],
            'server_response_time_med': [],
            'server_network_latency_med': [],
            'performance_score_med': []
        }

        # Extract values for median calculation
        for metric in metrics:
            values['speed_index_med'].append(metric.speed_index)
            values['first_contenful_paint_med'].append(metric.first_contentful_paint)
            values['largest_contentful_paint_med'].append(metric.largest_contentful_paint)
            values['total_blocking_time_med'].append(metric.total_blocking_time)
            values['cumulative_layout_shift_med'].append(metric.cumulative_layout_shift)
            values['render_blocking_resources_med'].append(metric.render_blocking_resources)
            values['server_response_time_med'].append(metric.server_response_time)
            values['server_network_latency_med'].append(metric.server_network_latency)
            values['performance_score_med'].append(metric.performance_score)

        # Calculate median for each metric
        med_values = {k: median(v) for k, v in values.items()}
        return med_values

    def handle(self, *args, **options):
        print("Updating the median table")
        url_ids = PsiUrl.objects.filter(is_active=True)
        strategy_mapping = {'mobile': 1, 'desktop': 2}

        for strategy in ['mobile', 'desktop']:
            for url in url_ids:
                cwv_metrics = PsiMainCoreWebVitalsMetrics.objects.filter(url_id=url.id, strategy=strategy).order_by('extractionDate')

                # If we have only one entry in the database, we can't compute the median
                if len(cwv_metrics) <= 1:
                    continue

                domain_id = url.domain_id
                med_values = self.calculate_median(cwv_metrics)

                median_model_instances = PsiMainCoreWebVitalsMediana(
                    speed_index_med=med_values['speed_index_med'],
                    first_contenful_paint_med=med_values['first_contenful_paint_med'],
                    largest_contentful_paint_med=med_values['largest_contentful_paint_med'],
                    total_blocking_time_med=med_values['total_blocking_time_med'],
                    cumulative_layout_shift_med=med_values['cumulative_layout_shift_med'],
                    render_blocking_resources_med=med_values['render_blocking_resources_med'],
                    server_response_time_med=med_values['server_response_time_med'],
                    server_network_latency_med=med_values['server_network_latency_med'],
                    performance_score_med=med_values['performance_score_med'],
                    medDate=current_date,
                    domain_id_id=domain_id,
                    strategy_id_id=strategy_mapping.get(strategy, None),
                    url_id=url.id,
                )
                median_model_instances.save()








                
                

