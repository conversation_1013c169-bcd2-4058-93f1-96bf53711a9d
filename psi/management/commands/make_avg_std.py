from django.core.management.base import BaseCommand
from psi.models import (
    PsiMainCoreWebVitalsMetrics,
    PsiUrl,
    PsiMainCoreWebVitalsAverages,
    PsiMainCoreWebVitalsStandardDeviation,
    PsiFormFactor,
    PsiMainCoreWebVitalsVarCoeff,
)
from django.db.models import Avg, StdDev
from django.utils import timezone


class Command(BaseCommand):
    help = "Calculates the Averages for the CWV metrics"

    def handle(self, *args, **options):
        '''Calculates the averages'''
        print("Updating the average and StdDev tables")

        current_date = timezone.now()

        # Filtering url object that have active=True
        url_ids = PsiUrl.objects.filter(is_active=True)

        # Filtering Crux Form Factor ACA strategy
        strategy_qs = PsiFormFactor.objects.filter(in_use=True)

        # Mapping the strategy string values to numeric values
        strategy_mapping = {'mobile': 1, 'desktop': 2}

        for strategy in strategy_qs:
            for url in url_ids:
                domain_id = url.domain_id
                strategy_id = strategy.id

                # Filter CWV metrics for the current URL and strategy
                cwv_metrics = PsiMainCoreWebVitalsMetrics.objects.filter(url_id=url.id, strategy=strategy).order_by('extractionDate')

                # If we have only one entry in the database, we can't compute the avg and standard deviation
                if len(cwv_metrics) <= 1:
                    continue

                # Calculate averages for each metric
                avg_values = cwv_metrics.aggregate(
                    speed_index_average=Avg('speed_index'),
                    first_contentful_paint_average=Avg('first_contentful_paint'),
                    largest_contentful_paint_average=Avg('largest_contentful_paint'),
                    total_blocking_time_average=Avg('total_blocking_time'),
                    cumulative_layout_shift_average=Avg('cumulative_layout_shift'),
                    render_blocking_resources_average=Avg('render_blocking_resources'),
                    server_response_time_average=Avg('server_response_time'),
                    server_network_latency_average=Avg('server_network_latency'),
                    performance_score_average=Avg('performance_score')
                )

                std_values = cwv_metrics.aggregate(
                    speed_index_std=StdDev('speed_index'),
                    first_contentful_paint_std=StdDev('first_contentful_paint'),
                    largest_contentful_paint_std=StdDev('largest_contentful_paint'),
                    total_blocking_time_std=StdDev('total_blocking_time'),
                    cumulative_layout_shift_std=StdDev('cumulative_layout_shift'),
                    render_blocking_resources_std=StdDev('render_blocking_resources'),
                    server_response_time_std=StdDev('server_response_time'),
                    server_network_latency_std=StdDev('server_network_latency'),
                    performance_score_std=StdDev('performance_score')
                )

                # Trying to calculate the coefficient of variability CV
                coeff_values = {
                    'speed_index_coef': 0,
                    'first_contentful_paint_coef':0,
                    'largest_contentful_paint_coef':0,
                    'total_blocking_time_coef':0,
                    'cumulative_layout_shift_coef':0,
                    'render_blocking_resources_coef':0,
                    'server_response_time_coef':0,
                    'server_network_latency_coef':0,
                    'performance_score_coef':0,
                }

                for avg_key, std_key, coeff_key in zip(avg_values, std_values, coeff_values):
                        try:
                            coeff_values[coeff_key] = avg_values[avg_key] / std_values[std_key]
                        except:
                             coeff_values[coeff_key] = 0    

                
                try:
                    # Create an instance of PsiMainCoreWebVitalsVarCoeff and save it
                    coeff_values_model_instances = PsiMainCoreWebVitalsVarCoeff(
                        speed_index_coef=coeff_values.get('speed_index_coef', 0),
                        first_contentful_paint_coef=coeff_values.get('first_contentful_paint_coef', 0),
                        largest_contentful_paint_coef=coeff_values.get('largest_contentful_paint_coef', 0),
                        total_blocking_time_coef=coeff_values.get('total_blocking_time_coef', 0),
                        cumulative_layout_shift_coef=coeff_values.get('cumulative_layout_shift_coef', 0),
                        render_blocking_resources_coef=coeff_values.get('render_blocking_resources_coef', 0),
                        server_response_time_coef=coeff_values.get('server_response_time_coef', 0),
                        server_network_latency_coef=coeff_values.get('server_network_latency_coef', 0),
                        performance_score_coef=coeff_values.get('performance_score_coef', 0),
                        cfvDate=current_date,
                        domain_id_id=domain_id,
                        strategy_id_id=strategy_id,
                        url_id=url.id
                    )


                    coeff_values_model_instances.save()
                    
                except Exception as e:
                    print(f"An error occurred: {e}")

                # Create instances of PsiMainCoreWebVitalsAverages and PsiMainCoreWebVitalsStandardDeviation and save them
                average_model_instance = PsiMainCoreWebVitalsAverages(
                    speed_index_average=avg_values.get('speed_index_average', 0),
                    first_contenful_paint_average=avg_values.get('first_contentful_paint_average', 0),
                    largest_contentful_paint_average=avg_values.get('largest_contentful_paint_average', 0),
                    total_blocking_time_average=avg_values.get('total_blocking_time_average', 0),
                    cumulative_layout_shift_average=avg_values.get('cumulative_layout_shift_average', 0),
                    render_blocking_resources_average=avg_values.get('render_blocking_resources_average', 0),
                    server_response_time_average=avg_values.get('server_response_time_average', 0),
                    server_network_latency_average=avg_values.get('server_network_latency_average', 0),
                    performance_score_average=avg_values.get('performance_score_average', 0),
                    averagingDate=current_date,
                    domain_id_id=domain_id,
                    strategy_id_id=strategy_id,
                    url_id=url.id,
                )

                std_model_instance = PsiMainCoreWebVitalsStandardDeviation(
                    speed_index_std=std_values.get('speed_index_std', 0),
                    first_contenful_paint_std=std_values.get('first_contentful_paint_std', 0),
                    largest_contentful_paint_std=std_values.get('largest_contentful_paint_std', 0),
                    total_blocking_time_std=std_values.get('total_blocking_time_std', 0),
                    cumulative_layout_shift_std=std_values.get('cumulative_layout_shift_std', 0),
                    render_blocking_resources_std=std_values.get('render_blocking_resources_std', 0),
                    server_response_time_std=std_values.get('server_response_time_std', 0),
                    server_network_latency_std=std_values.get('server_network_latency_std', 0),
                    performance_score_std=std_values.get('performance_score_std', 0),
                    stdingDate=current_date,
                    domain_id_id=domain_id,
                    strategy_id_id=strategy_id,
                    url_id=url.id,
                )

                average_model_instance.save()
                std_model_instance.save()