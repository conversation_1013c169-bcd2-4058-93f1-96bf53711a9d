from django.core.management import BaseCommand
from psi.models import PsiApiKey, PsiUrl, CruxFormFactor, CruxHeaders, PsiUserAgents, PsiEndPoint, CruxIndentifier, CruxFetchResultsForUrl, CruxFetchResultsForOrigin, Domain
from django.utils import timezone
from .psicrux.utils import fetch_random_user_agent, extract_crux_metrics
from .psicrux.clutilities import APIClient, APIURLBuilder
import json


currentDate = timezone.now()
print(currentDate)

class Command(BaseCommand):

    help = "Fetching the data using Crux API and populates the Crux Origin and Url database"

    def handle(self, *args, **options):
        '''Fetches the CRUX data using CRUX API'''

        #Connecting the Django ORM and retrieving valid Crux API key
        valid_api_key = PsiApiKey.objects.filter(is_valid=True).first()

        #Contacting Django ORM and retrieving a list of valid formFactors (mobile Or Desktop) string 
        form_factor = CruxFormFactor.objects.filter(in_use=True).values_list()


        #Contacting Django ORM and retrieving User-Agent strings from the database (has to be random)
        user_agent_string_list  = PsiUserAgents.objects.filter(in_use=True).values_list()
        random_user_agent = fetch_random_user_agent(user_agent_list=user_agent_string_list)[1]

        #Contacting DJango ORM and retreving a list of valid URLs to use in API call
        valid_url_list = PsiUrl.objects.filter(is_active=True).values_list()

        # Fetching Django orm Domain model to get a list of all domains in use
        valid_domain_list = Domain.objects.filter(in_use=True).values_list()

        
        #Contacting Django ORM and retrieving defined headers type dictionery
        headers_query = CruxHeaders.objects.all().values_list()[0]
        header_str = headers_query[1]
        header_dict=json.loads(header_str)
        #Update header_dict with random User Agent
        header_dict['User-agents'] = random_user_agent



        #Fetching Crux Identifier from Django ORM
        identifier = CruxIndentifier.objects.all().values_list()



        #Fetching ednpoint from the Dhjango ORM
        crux_end_point = PsiEndPoint.objects.filter(ep_api_name='crux').filter(ep_is_deprecated=False)[0]
 

        #Create API builder Object
        crux_api_builder = APIURLBuilder(base_url=crux_end_point, api_key=valid_api_key)



        #Creating Crux Api Client
        crux_api_client = APIClient(url_builder=crux_api_builder, expected_status_code=[200, 404, 400])


        # Fetching url data and storing the data in the CruxFetchResultsForUrl  and  -  CruxFetchResultsForOrigin depending on the identifier
        url_insert_list = [] 
        origin_insert_list = []
        for identificator in identifier:
            for factor in form_factor:
                if identificator[1] == "origin":

                    for dom in valid_domain_list:
                        _crux_origin_response = crux_api_client.fetch_crux_data(url=dom[1], formFactor=factor[2],  metrics=[],  origin=identificator[1], effectiveConnectionType='4g', headers_dict=header_dict, sleeping=10)    
                        origin_insert_list = extract_crux_metrics(response=_crux_origin_response, origin=identificator[1], formFactor=factor[2], url=dom[1]) 
                        domain_id = dom[0]
                        origin_insert_list.extend([domain_id, currentDate])
                        crux_origin_instance = CruxFetchResultsForOrigin(
                            form_factor = origin_insert_list[0].lower(),
                            effective_conn_type = origin_insert_list[1],
                            origin = dom[1],
                            first_contentful_paint = origin_insert_list[3],
                            first_input_delay = origin_insert_list[4],
                            interaction_to_next_paint = origin_insert_list[5],
                            largest_contentful_paint = origin_insert_list[6],
                            cumulative_layout_shift = origin_insert_list[7],
                            experimental_time_to_first_byte = origin_insert_list[8],
                            collectionPeriod = currentDate,
                            domain_id = domain_id
                        )
                        crux_origin_instance.save()

        
                elif identificator[1] == "url":
                    print("Storing the data for the urlk")
                    for url in valid_url_list:
                        _crux_url_response = crux_api_client.fetch_crux_data(url=url[1], formFactor=factor[2],  metrics=[],  origin=identificator[1], effectiveConnectionType='4g', headers_dict=header_dict, sleeping=10) 
                        url_insert_list = extract_crux_metrics(response=_crux_url_response, origin=identificator[1], formFactor=factor[2], url=url[1])                                               
                        domain_id = url[3]
                        url_id = url[0]
                        url_insert_list.extend([domain_id, url_id, currentDate])
                        crux_url_instances = CruxFetchResultsForUrl(
                            form_factor = url_insert_list[0].lower(),
                            effective_conn_type = url_insert_list[1],
                            url = url_insert_list[2],
                            first_contentful_paint = url_insert_list[3],
                            first_input_delay = url_insert_list[4],
                            interaction_to_next_paint = url_insert_list[5],
                            largest_contentful_paint = url_insert_list[6],
                            cumulative_layout_shift = url_insert_list[7],
                            experimental_time_to_first_byte = url_insert_list[8],
                            collectionPeriod = currentDate,
                            domain_id = domain_id,
                            url_id = url_id,
                        )

                        crux_url_instances.save()                          

            

       