import random

def fetch_format(db_response, i):
    """Reusable function thet gets database response and format it in an python list"""
    data = list(map(lambda x: x[i], db_response))
    return data


def fetch_random_user_agent(user_agent_list):
    '''Receiving valid User-Agent list from the database and return random choice'''
    random_user_agent =  random.choice(user_agent_list)

    return random_user_agent

def fetch_format(db_response, i):
    """Reusable function thet gets database response and format it in an python list"""
    return list(map(lambda x: x[i], db_response))

def extract_crux_metrics(response, formFactor, origin, url):
    '''Check individual metrics from the response for cRux data'''

    try:
        formFactor = response['record']['key']["formFactor"]
    except:
        formFactor = formFactor    
    try:    
        effectiveConnectionType = response['record']['key']['effectiveConnectionType']
    except:
        effectiveConnectionType = "No Data"    
    try:    
        origin = response['record']['key'][origin]
    except:
        origin = url  
    try:     
        first_contentful_paint = response['record']['metrics']['first_contentful_paint']  
    except:
        first_contentful_paint = "No Data" 
    try:         
        first_input_delay = response['record']['metrics']['first_input_delay']
    except:
        first_input_delay = "No Data"   
    try:    
        interaction_to_next_paint = response['record']['metrics']['interaction_to_next_paint']  
    except:
        interaction_to_next_paint = "No Data" 
    try:         
        largest_contentful_paint = response['record']['metrics']['largest_contentful_paint'] 
    except:
        largest_contentful_paint = "No Data"   
    try:    
        cumulative_layout_shift = response['record']['metrics']['cumulative_layout_shift'] 
    except:
        cumulative_layout_shift = "No Data" 
    try:         
        experimental_time_to_first_byte = response['record']['metrics']['experimental_time_to_first_byte']    
    except:
        experimental_time_to_first_byte = "No Data" 
    try:            
        collectionPeriod = response['record']['collectionPeriod']    
    except:
        collectionPeriod = "No Data" 

    insert_list = [formFactor, effectiveConnectionType, origin, str(first_contentful_paint),str(first_input_delay), str(interaction_to_next_paint) ,
                    str(largest_contentful_paint),str(cumulative_layout_shift), str(experimental_time_to_first_byte), str(collectionPeriod)]
    return insert_list 




def extract_page_speed_metrics(response):
    '''Check individual metrics from the response for page speed data'''
    try:
        speed_index = response['lighthouseResult']['audits']['speed-index']['numericValue']
    except:
        speed_index = "No data"
    try:        
        first_contentful_paint = response['lighthouseResult']['audits']['first-contentful-paint']['numericValue']
    except:
        first_contentful_paint = "No data"
    try:        
        largest_contentful_paint = response['lighthouseResult']['audits']['largest-contentful-paint']['numericValue']
    except:
        largest_contentful_paint = "No data"   
    try:     
        total_blocking_time = response['lighthouseResult']['audits']['total-blocking-time']['numericValue']
    except:
        total_blocking_time = "No data"
    try:        
        cumulative_layout_shift = response['lighthouseResult']['audits']['cumulative-layout-shift']['numericValue']
    except:
        cumulative_layout_shift = "No data"
    try:        
        render_blocking_resources = response['lighthouseResult']['audits']['render-blocking-resources']['numericValue']
    except:
        render_blocking_resources = "No data"
    try:        
        server_response_time = response['lighthouseResult']['audits']['server-response-time']['numericValue']
    except:
        server_response_time = "No data"
    try:        
        server_network_latency = response['lighthouseResult']['audits']['network-server-latency']['numericValue']
    except:
        server_network_latency = "No data"
    try:        
        performance_score =  response['lighthouseResult']['categories']['performance']['score']*100
    except:
        performance_score = 0
    try:        
        final_snapshoot = response['lighthouseResult']['fullPageScreenshot']['screenshot']['data']
        
    except:
        final_snapshoot = "No data"
  

  
    return speed_index, first_contentful_paint, largest_contentful_paint, total_blocking_time, cumulative_layout_shift, render_blocking_resources, server_response_time, server_network_latency, performance_score, final_snapshoot