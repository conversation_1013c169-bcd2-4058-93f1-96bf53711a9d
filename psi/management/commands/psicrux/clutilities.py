import requests
import time

class APIURLBuilder:
    '''Builds the api url'''
    def __init__(self, base_url, api_key) -> str:
        self.base_url = base_url
        self.api_key = api_key

        self.parameters = {}

    def add_parameters(self, parameters):
        self.parameters.update(parameters)

    def build(self):
        url = f"{self.base_url}?key={self.api_key}"

        for key, value in self.parameters.items():
            url += f"&{key}={value}"

        return url    


class APIClient:
    def __init__(self, url_builder, expected_status_code = 200):
        self.url_builder = url_builder
        self.expected_status_code = expected_status_code
        self.failed_urls = []



    def fetch_crux_data(self,  url=None, origin = None, formFactor=None, effectiveConnectionType="4G", headers_dict = None, metrics=None, sleeping=None,  **kwargs):
        '''Fetches the Crux data using api key and defined URL parameters'''
        try:
            data = {
                "formFactor": formFactor.upper(),
                "effectiveConnectionType": effectiveConnectionType.upper(),
                "metrics": metrics or [],  # Use default [emtpy list] if metrics is None (retreiving everythiong)
            }

            # Check the value of origin and use it as the key accordingly
            #ToDo add url validator and iprove response options
            try:
                if origin == "url":
                    data["url"] = url
                elif origin == "origin":
                    data["origin"] = url  # Switch between url and origin
                else:
                    print("Invalid value for 'origin'. It must be 'url' or 'origin'.")
            except Exception as e:
                print(e)  

   
    
            #If everything is OK, building full API url
            full_api_url = self.url_builder.build()

            #Making the actual request and saving the response
            response = requests.post(full_api_url, headers=headers_dict, json=data, timeout=5)    

            time.sleep(sleeping)
            #Checking if the response is 404 as we know thta it may also return "no data in response" else returning json object
            if response.status_code in self.expected_status_code:
                response_data = response.json()

                return response_data
            else:
                print(f"CRUX API request failed with status code {response.status_code}")
                return None
        except requests.exceptions.RequestException as re:
   
            print(f"Error making a CRUX API request: {re}")
            return None
        

    def fetch_psi_data(self, url, strategy, user_agent, sleep, timeout=None, max_retries = 3):
        '''Method to fetch Google page speed data by making a request and preparing the response to be saved in a database'''
        try:
            params_dict = {"url": url, "strategy": strategy}
            self.url_builder.add_parameters(params_dict)

            #Buildng full API url
            full_api_url = self.url_builder.build()
            

            #Constructing header dictionery that will be used in request



            headers_dict = {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                  'User-Agent':user_agent 
            }


            for retry_count in range(max_retries):
                try:
                    response = requests.get(full_api_url, headers=headers_dict, timeout=timeout)
                    if response.status_code == 200:
                        print(f"PageSpeed API request returned status code {response.status_code}... extracting the data...")
                        response_data = response.json()
                        status_code = response.status_code
                        time.sleep(2) # Explicitly sleep for two seconds 
                        return response_data, status_code
                    else:
                        print(f"PageSpeed API request failed with status code {response.status_code} for URL: {url}")
                        self.failed_urls.append(url)
                        retry_count  += 1
                        timeout += 10
                        time.sleep(sleep)  # Explicitly sleep between two successful requests
                except requests.exceptions.Timeout:
                    print(f"Timeout error for URL: {url}. Retrying...")
                    continue
                except requests.exceptions.RequestException as e:
                    print(f"Error making a PageSpeed API request for URL: {url}: {e}")

        except requests.exceptions.RequestException as e:
            print(f"Error making a PAgeSPeed API request: {e}")


    def retry_failed_requests(self, strategy, user_agents):
        failed_urls_copy = self.failed_urls.copy()

        for url in set(failed_urls_copy):
            if url in set(self.failed_urls):
                print(f"Retrying failed request for URL: {url}")
                data, status_code = self.fetch_page_speed_data(url, strategy, user_agents)
                if status_code == 200:
                    self.failed_urls.remove(url)
                    print(f"Successfully retrieved data for URL: {url}")
                else:
                    print(f"Failed to retrieve data for URL: {url}")            

 




                                        


        










            

                                   
        