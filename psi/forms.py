from django import forms
from .models import Domain, PsiPageType, CruxFormFactor, PsiUrl, PsiFormFactor
from logana.processes.utilities import conn

    

class CreateNewPsiDomainForm(forms.Form):
    url = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': 'form-control rounded-10', 'placeholder': 'Enter URL...', 'id':'add-url', 'name': 'add-url'})
    )
    domain = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': "form-control rounded-10", 'id': "desc", 'rows': "10", 'placeholder': "Enter domain (example.com)...", 'name': 'Description'})
    )
        
    logo = forms.FileField()    


class CreateNewPsiPageTypeForm(forms.Form):
    page_type = forms.CharField(label='',widget=forms.TextInput(attrs={'class': 'form-control rounded-10', 'placeholder': 'Enter page type', 'id': 'page_type', 'name': 'page_type'}))



class CreateNewPsiUrlForm(forms.Form):
    url = forms.CharField(
        label='',
        widget=forms.TextInput(attrs={'class': 'form-control rounded-10', 'placeholder': 'Enter URL', 'id': 'add-url', 'name': 'add-url'})
    )
    domain = forms.ModelChoiceField(
        label='',
        queryset=Domain.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-sm rounded-10', 'id': 'domain', 'name': 'domain'})
    )
    type = forms.ModelChoiceField(
        label='',
        queryset=PsiPageType.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-sm rounded-10', 'id': 'page_type', 'name': 'page_type'})
    )

    def clean_type(self):
        type = self.cleaned_data.get('type')
        if not type:
            raise forms.ValidationError('Please select a page type.')
        return type


class CreateNewFormFactorFormDropdown(forms.Form):
    form_factor = forms.ModelChoiceField(

        queryset=CruxFormFactor.objects.all(),
        label='CHOOSE FORM FACTOR',
        widget=forms.Select(attrs={'class': 'form-control form-control-lg rounded-20 custom-form-factor btn btn-secondary my-1', 'id': 'formFactor', 'name': 'formFactor'})
    )

class CreateNewFormDropdownForUrls(forms.Form):
    def __init__(self, *args, **kwargs):
        domain_id = kwargs.pop('domain_id', None)
        super(CreateNewFormDropdownForUrls, self).__init__(*args, **kwargs)
        
        # Filtering Urls Based upon the domain id
        if domain_id is not None:
            self.fields['url_form'].queryset = PsiUrl.objects.filter(domain=domain_id)

    form_factor = forms.ModelChoiceField(
        label='CHOOSE FORM FACTOR',
        queryset=CruxFormFactor.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-lg rounded-20 custom-form-factor btn btn-secondary my-1', 'id': 'formFactor', 'name': 'formFactor'})
    )            

    url_form = forms.ModelChoiceField(
        label='CHOOSE URL FROM DROPDOWN LIST',
        queryset=PsiUrl.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-lg rounded-20 custom-form-factor btn btn-secondary my-1', 'id': 'urlName', 'name': 'urlName'})
    )

class CreateNewFormDropdownForDomains(forms.Form):
    def __init__(self, *args, **kwargs):
        domain_id = kwargs.pop('domain_id', None)
        super(CreateNewFormDropdownForDomains, self).__init__(*args, **kwargs)
        
        # Filtering Urls Based upon the domain id
        # if domain_id is not None:
        #     self.fields['url_form'].queryset = PsiUrl.objects.filter(domain=domain_id)

    form_factor = forms.ModelChoiceField(
        label='CHOOSE FORM FACTOR',
        queryset=CruxFormFactor.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-lg rounded-20 custom-form-factor btn btn-secondary my-1', 'id': 'formFactor', 'name': 'formFactor'})
    )            

 

class CreateNewFormDropdownForPsiWeeklyReport(forms.Form):
    def __init__(self, *args, **kwargs):
        domain_id = kwargs.pop('domain_id', None)
        super(CreateNewFormDropdownForPsiWeeklyReport, self).__init__(*args, **kwargs)
        
        # Filtering Urls Based upon the domain id
        if domain_id is not None:
            self.fields['url_form'].queryset = PsiUrl.objects.filter(domain=domain_id)

    form_factor = forms.ModelChoiceField(
        label='CHOOSE FORM FACTOR',
        queryset=PsiFormFactor.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-lg rounded-20 custom-form-factor btn btn-secondary my-1', 'id': 'formFactor', 'name': 'formFactor'})
    )            

    url_form = forms.ModelChoiceField(
        label='CHOOSE URL FROM DROPDOWN LIST',
        queryset=PsiFormFactor.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control form-control-lg rounded-20 custom-form-factor btn btn-secondary my-1', 'id': 'urlName', 'name': 'urlName'})
    )    